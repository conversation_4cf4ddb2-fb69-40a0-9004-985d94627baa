<?php
namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'sys']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->setJsonResponse();
        if($this->request->isPost()) {
            $this->moduleExistThenDie('sys');

            $res = [
                [
                    'name' => '数据字典管理',
                    'identity' => 'sys:dict',
                    'action' => [
                        ['name' => '数据字典管理', 'identity' => 'sys:dict:list', 'comment' => '']
                    ]
                ],
                [
                    'name' => '拓展字段管理',
                    'identity' => 'sys:extend',
                    'action' => [
                        ['name' => '拓展字段管理', 'identity' => 'sys:extend:list', 'comment' => '']
                    ]
                ],
                [
                    'name' => '组织管理',
                    'identity' => 'sys:group',
                    'action' => [
                        ['name' => '组织管理', 'identity' => 'sys:group:list', 'comment' => '']
                    ]
                ],
                [
                    'name' => '员工管理',
                    'identity' => 'sys:user',
                    'action' => [
                        ['name' => '员工一览', 'identity' => 'sys:user:list', 'comment' => ''],
                        ['name' => '添加员工', 'identity' => 'sys:user:create', 'comment' => ''],
                        ['name' => '编辑员工', 'identity' => 'sys:user:edit', 'comment' => ''],
                        ['name' => '删除员工', 'identity' => 'sys:user:delete', 'comment' => ''],
                    ]
                ],
                [
                    'name' => '岗位管理',
                    'identity' => 'sys:role',
                    'action' => [
                        ['name' => '角色一览', 'identity' => 'sys:role:list', 'comment' => ''],
                        ['name' => '添加角色', 'identity' => 'sys:role:create', 'comment' => ''],
                        ['name' => '编辑角色', 'identity' => 'sys:role:edit', 'comment' => ''],
                        ['name' => '删除角色', 'identity' => 'sys:role:delete', 'comment' => ''],
                        ['name' => '分配权限', 'identity' => 'sys:role:assign', 'comment' => '为角色分配具体的权限'],
                        ['name' => '查看权限', 'identity' => 'sys:res:sel', 'comment' => ''],
                    ]
                ],
                [
                    'name' => '权限管理',
                    'identity' => 'sys:res',
                    'action' => [
                        ['name' => '权限一览', 'identity' => 'sys:res:list', 'comment' => ''],
                        ['name' => '创建权限', 'identity' => 'sys:res:create', 'comment' => ''],
                        ['name' => '编辑权限', 'identity' => 'sys:res:edit', 'comment' => ''],
                        ['name' => '删除权限', 'identity' => 'sys:res:delete', 'comment' => ''],
                    ]
                ],
                [
                    'name' => '组织数据',
                    'identity' => 'sys:gconfig',
                    'action' => [
                        ['name' => '条目一览', 'identity' => 'sys:gconfig:list', 'comment' => ''],
                        ['name' => '编辑条目', 'identity' => 'sys:gconfig:edit', 'comment' => ''],
                        ['name' => '删除条目', 'identity' => 'sys:gconfig:delete', 'comment' => ''],
                    ]
                ]
            ];

            $ret = new JsonData();
            $this->db->begin();
            try {
                $this->makePackage('sys', '系统模块', '1.0', '提供用户、权限等基础功能');
                $module = $this->makeModule('sys', '系统模块');
                foreach ($res as $controller) {
                    $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                    foreach ($controller['action'] as $action) {
                        $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                    }
                }
                $this->db->commit();
            } catch (\Exception $e) {
                $this->db->rollback();
                Logger::error($e->getMessage(), $e->getTraceAsString());
                $ret->message = '发生错误';
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}