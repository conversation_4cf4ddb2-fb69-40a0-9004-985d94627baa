{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('css').addCss('static/pages/css/work/work-view.css') %}
{% do assets.collection('css').addCss('static/global/plugins/sweetalert/sweetalert.css') %}
{% do assets.collection('js').addJs('static/global/plugins/sweetalert/sweetalert.min.js') %}
<style media="print">
    @page {
        size: auto;
        margin: 0;
    }
</style>

<div class="page-content" id="app">
    <div class="portlet light">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-social-dribbble font-blue"></i>
                <span class="caption-subject font-blue bold uppercase" v-text="data.type_name"></span>
            </div>
        </div>
        <div class="portlet-body" >
            <div class="data-row">
                <div class="data-col">
                    <div class="data-title">提交部门：</div>
                    <div class="data-content" v-text="data.group"></div>
                </div>
                <div class="data-col">
                    <div class="data-title">业务单号：</div>
                    <div class="data-content" v-text="data.code"></div>
                </div>
                <div class="data-col">
                    <div class="data-title">业务类型：</div>
                    <div class="data-content" v-text="data.in_out_type_name"></div>
                </div>
                <div class="data-col" v-if="data.in_out_type == 1">
                    <div class="data-title">来源地：</div>
                    <div class="data-content"  v-text="data.src_name"></div>
                </div>
                <div class="data-col" v-if="data.in_out_type == 2">
                    <div class="data-title">目的地：</div>
                    <div class="data-content"  v-text="data.dest_name"></div>
                </div>
                <div class="data-col" v-if="data.plate">
                    <div class="data-title">车辆：</div>
                    <div class="data-content" v-text="data.plate"></div>
                </div>
                <div class="data-col" v-if="data.driver">
                    <div class="data-title">跟车人员：</div>
                    <div class="data-content"  v-text="data.driver"></div>
                </div>
                <div class="data-col" v-if="data.in_date">
                    <div class="data-title">入厂日期：</div>
                    <div class="data-content" v-text="data.in_date"></div>
                </div>
                <div class="data-col" v-if="data.out_date">
                    <div class="data-title">出厂日期：</div>
                    <div class="data-content" v-text="data.out_date"></div>
                </div>
            </div>
            <div class="data-row" v-for="(item,i) in data.form_list">
                <div v-if="item.type == 99" style="width: 100%;padding-right: 20px">
                    <div class="data-col">
                        <div class="data-title"><i class="fa fa-star"></i>${ item.name }</div>
                        <div class="data-content"></div>
                    </div>
                    <div v-for="(sum,j) in item.sum_list">
                        <div class="data-col">
                            <div class="data-title">${ sum.name }</div>
                            <div class="data-content">${ sum.value } ${ sum.unit }</div>
                        </div>
                    </div>
                    <div class="inner-row" v-for="(d,k) in item.data_list">
                        <div v-for="(v,l) in d" v-if="v.type != 100">
                            <div class="data-col">
                                <div class="data-title">${ v.name }</div>
                                <div class="data-content">${ v.value } ${ v.unit }</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="item.type != 999 && item.type != 99">
                    <div class="data-col">
                        <div class="data-title">${ item.name }</div>
                        <div class="data-content">${ item.value } ${ item.unit }</div>
                    </div>
                </div>
            </div>
            <div class="data-row" v-if="data.goods_list.length > 0">
                <div class="data-col">
                    <div class="data-title"><i class="fa fa-star"></i>货物明细</div>
                    <div class="data-content"></div>
                </div>
                <div class="inner-row" v-for="(d,k) in data.goods_list">
                    <div class="data-col">
                        <div class="data-title">厂区</div>
                        <div class="data-content" v-text="d.cc_name"></div>
                    </div>
                    <div class="data-col">
                        <div class="data-title">仓库</div>
                        <div class="data-content" v-text="d.ck_name"></div>
                    </div>
                    <div class="data-col">
                        <div class="data-title">货物</div>
                        <div class="data-content" v-text="d.goods_name"></div>
                    </div>
                    <div class="data-col">
                        <div class="data-title">数量</div>
                        <div class="data-content" v-text="d.cnt+'（'+d.unit+'）'"></div>
                    </div>
                </div>
            </div>
            <div class="data-row">
                <div class="data-col">
                    <div class="data-title">说明：</div>
                    <div class="data-content"  v-text="data.remarks"></div>
                </div>
            </div>
            <div class="work-line" style="margin-top: 20px">
                <div class="work-line-content">
                    <div v-for="(item,index) in data.flow_list" class="item">
                        <div class="item-content">
                            <div class="top">
                                <div v-if="item.status == 0" style="color: #A2A2A2;">
                                    <span>${item.name}</span>
                                    <span>${item.val}</span>
                                </div>
                                <div v-if="item.status != 0" style="color: #000000;">
                                    <span>${item.name}</span>
                                    <span>${item.val}</span>
                                </div>
                                <span style="font-size: 15px;color: #898989;margin-top: -3px">${item.time}</span>
                            </div>
                            <div>
                                <div v-if="item.text != ''" class="bottom-content">
                                    ${item.text}
                                </div>
                                <div v-if="item.send != ''" class="bottom-send">
                                    ${item.send}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="work-line-content">
                    <div v-for="(item,idx) in data.anchor_list">
                        <div style="display: flex;flex-direction: row;min-height: 60px;">
                            <div style="width: 35%;display: flex;flex-direction: row;">
                                <div style="height: 100%;position: relative">
                                    <div style="height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;">
                                    </div>
                                    <div v-if="idx == 0" style="height: 18px;position: absolute;top: 0;width: 12px;background-color: #FFF">
                                    </div>
                                    <div v-if="idx+1 == data.anchor_list.length" style="height: calc(100% - 22px);position: absolute;top: 22px;width: 12px;background-color: #FFF">
                                    </div>
                                    <div style="position: absolute;top: 15px;width: 15px;height: 15px;background-color: #aaaaaa;border-radius: 15px !important;">
                                    </div>
                                </div>
                                <div style="padding-top:5px;padding-left: 15px">
                                    <span style="font-weight: 400" v-text="item.name"></span>
                                    <div>
                                        <span style="font-size: 12px;color: #898989" v-if="item.type==1">会签</span>
                                        <span style="font-size: 12px;color: #898989" v-if="item.type==2">或签</span>
                                        <span style="font-size: 12px;color: #898989" v-if="item.type==3">终签</span>
                                    </div>
                                </div>
                            </div>
                            <div class="user-row">
                                <div v-for="(user,i) in item.list">
                                    <div v-if="item.type == 1" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #3598dc;color: #ffffff">${user.name}</div>
                                    <div v-if="item.type != 1" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #00adbc;color: #ffffff">${user.name}</div>
                                </div>
                            </div>
                        </div>
                        <div v-if="item.nlist.length > 0" style="display: flex;flex-direction: row;min-height: 60px;">
                            <div style="width: 35%;display: flex;flex-direction: row;">
                                <div style="height: 100%;position: relative">
                                    <div v-if="idx+1 != data.anchor_list.length" style="height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;">
                                    </div>
                                    <div v-if="idx+1 == data.anchor_list.length" style="height: 100%;width: 8px;">
                                    </div>
                                </div>
                                <div style="padding-top:5px;padding-left: 15px">
                                    <span style="font-weight: 400;color: #898989">抄送人</span>
                                </div>
                            </div>
                            <div class="user-row">
                                <div v-for="(user,i) in item.nlist" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #3598dc;color: #ffffff">${user.name}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            data : {{ data }},
        }
    });
    $(function () {
        window.print();
        // setTimeout(function () {
        //     window.close();
        // }, 200);
    })
</script>
<style>

    .work-line{
        width: 100%;
        display: flex;
        flex-direction: column;
        min-height: 50px;
    }
    .work-line-content{
        width: 100%;
        min-height: 50px;
        display: flex;
        flex-direction: column;
    }
    .work-line-content .item-content{
        width: auto;
        margin-left: 15px;
        color: #888888;
    }

    .work-line-content .item-content .top {
        height: 20px;
        line-height: 20px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 18px;
        margin-top: 2px;
        vertical-align: top;
    }

    .work-line-content .item-content .bottom {
        margin-left: -38px;
        border-left: 4px #D2D2D2 solid;
        padding-bottom: 10px;
        min-height: 55px;
    }

    .work-line-content .bottom-content{
        margin-left: 35px;
        margin-top:5px;
        min-height: 40px;
        background-color: #F2F2F2;
        border-radius: 5px !important;
        padding: 10px;
        color: #000000;
    }

    .work-line-content .bottom-send{
        margin-left: 35px;
        margin-top:5px;
        font-size: 14px;
    }

    .item{
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        margin-top: 4px;
    }
    .item .title{
        width: 45px;
        height: 45px;
        line-height: 45px;
        vertical-align: middle;
        background-color: #3296FB;
        border: 1px #E2E2E2 solid;
        border-radius: 5px !important;
        color: #FFFFFF;
        text-align: center;
        position: relative;
        font-size: 16px;
    }
    .item .title .border{
        position: absolute;
        top: 31px;
        left: 31px;
        width: 18px;
        height: 18px;
        line-height: 18px;
        border-radius: 9px !important;
        background-color: #FFFFFF;
        border: 0;
        text-align: center;
        padding-top: 1px;
    }

    .item .item-content{
        width: 100%;
        margin-left: 15px;
        color: #888888;
    }

    .item .item-content .top {
        height: 20px;
        line-height: 20px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 18px;
        margin-top: 2px;
        vertical-align: top;
    }

    .item .item-content .bottom {
        margin-left: -38px;
        border-left: 4px #D2D2D2 solid;
        padding-bottom: 10px;
        min-height: 55px;
    }

    .item .bottom-content{
        margin-left: 35px;
        margin-top:5px;
        min-height: 40px;
        background-color: #F2F2F2;
        border-radius: 5px !important;
        padding: 10px;
        color: #000000;
    }

    .item .bottom-send{
        margin-left: 35px;
        margin-top:5px;
        font-size: 14px;
    }

    .item .title-border{
        background-color: #FFFFFF;
        width: 45px;
        height: 52px;
        z-index: 999;
    }

    .data-row {
        display: flex;
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
    }

    .inner-row {
        padding: 10px;
        margin-bottom: 20px;
        width: 100%;
        background-color: #f5f5f5;
        border-radius: 10px !important;
    }

    .button-row{
        padding: 6px 0;
        display: flex;
        flex-direction: row;
        justify-content: right;
        border-bottom: 1px solid #eee;
    }
    .button-row>button:last-child {
        margin-left: 10px;
    }

    .data-row .data-col {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 5px;
        padding: 0 15px;
        width: 100%;
    }

    .data-col.time-line {
        width: 100%;
    }

    .data-col.pic {
        height: 95px;
        overflow: hidden;
    }

    .data-row.last {
        margin-bottom: 0;
    }

    .data-title {
        min-width: 80px;
    }

    .data-content {
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .weight-col {
        overflow-y: auto;
        height: 820px;
        margin-bottom: 10px;
    }

    .weight-col>div:last-child {
        margin-bottom: 0;
    }

    .user-row {
        width: 65%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-end;
        padding-right: 10px;
        padding-top: 5px;
    }
</style>