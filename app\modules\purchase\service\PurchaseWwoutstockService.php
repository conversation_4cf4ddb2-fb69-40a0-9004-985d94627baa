<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseWwoutstock;
use Envsan\Modules\Purchase\Model\PurchaseWwoutstockDetail;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class PurchaseWwoutstockService extends BaseService
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
            't1.code',
            't1.outstock_date',
            't2.order_code',
            't1.status',
            't1.status_name',
            't1.remarks',
            't1.ext_val',
            't3.name as supplier_name',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwoutstock', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseWwoutstock();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {

        return $this->executeInTransaction(function () use ($act, $row) {
            $type = $this->request->getPost('type', 'tstring');
            $order_id = $this->request->getPost('order_id', 'tstring');
            $outstock_date = $this->request->getPost('outstock_date', 'tstring');
            $files = $this->request->getPost('files');
            $remarks = $this->request->getPost('remarks', 'tstring');
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
            $detail_list = $this->request->getPost('detail_data');
            if (empty($order_id) || empty($outstock_date) || empty($detail_list))
                return $this->error(ErrorHelper::WRONG_INPUT);

            if ($type != 1 && $type != 2)
                return $this->error(ErrorHelper::WRONG_INPUT);

            foreach ($detail_list as &$detail_item) {
                if (!CheckUtil::isDecimal($detail_item['price'])) {
                    return $this->error('无效的未税单价');
                }

                if (!CheckUtil::isDecimal($detail_item['price_hs'])) {
                    return $this->error('无效的含税单价');
                }

                if (!CheckUtil::isDecimal($detail_item['quantity'])) {
                    return $this->error('无效的数量');
                }

                if (!CheckUtil::isDecimal($detail_item['pricing_quantity'])) {
                    return $this->error('无效的计价数量');
                }
                $detail_item['total_money'] = round(CvtUtil::emptyToDouble($detail_item['price']) * CvtUtil::emptyToDouble($detail_item['pricing_quantity']), 2);
                $detail_item['total_money_hs'] = round(CvtUtil::emptyToDouble($detail_item['price_hs']) * CvtUtil::emptyToDouble($detail_item['pricing_quantity']), 2);
            }

            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);

            $row->order_id = $order_id;
            $row->outstock_date = $outstock_date;
            $row->detail_data = CvtUtil::arrayToNull($detail_list);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->files = CvtUtil::arrayToNull($files);
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->status = $type == 1 ? 10 : 15;
            $row->status_name = Constant::$purchase_outstock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->code = $ss->useSequence(5);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            $row->save();
        });
    }

    public function selectById($id)
    {
        return PurchaseWwoutstock::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseWwoutstock::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        } else if ($row->status == 20) {
            return '已出库，不能删除';
        }

        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();

            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【外委出库删除】purchase_wwoutstock表更新失败");
            }

            $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseWwoutstockDetail';
            $phql .= ' SET del_flag = 1, update_date = ?2, update_by = ?3';
            $phql .= ' WHERE del_flag = 0 and outstock_id = ?1';
            $result = $this->modelsManager->executeQuery($phql, [
                1 => $row->id,
                2 => $now,
                3 => $user->id
            ]);
            if (!$result->success()) {
                throw new \Exception('【外委出库删除】purchase_wwoutstock_detail表更新失败');
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function cancel($uid)
    {
        return $this->executeInTransaction(function () use ($uid) {
            $row = $this->selectByUid($uid);
            if (!$row || $row->del_flag == 1) {
                return '';
            } else if ($row->status != 20) {
                return '未出库';
            }

            $now = DateUtil::now();
            $user = SessionData::user();

            $row->status = 10;
            $row->status_name = Constant::$purchase_outstock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->save();

            $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseWwoutstockDetail';
            $phql .= ' SET del_flag = 1, update_date = ?2, update_by = ?3';
            $phql .= ' WHERE del_flag = 0 and outstock_id = ?1';
            $result = $this->modelsManager->executeQuery($phql, [
                1 => $row->id,
                2 => $now,
                3 => $user->id
            ]);
            if (!$result->success()) {
                throw new \Exception('【外委取消出库】purchase_wwoutstock_detail表更新失败');
            }
        });

    }

    public function getBomList($order_id)
    {
        // 选择订单对应的采购审批
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t.code as plan_code,
                t10.quantity,
                t10.purchase_quantity,
                t10.goods_deputy_unit,   
                t10.goods_unit,
                t10.price,
                t10.price_hs, 
                t10.conversion_rate, 
                t10.goods_code,
                t10.goods_name,
                t10.goods_model,
                t4.code,
                t5.name as bom_name,
                1 as show_flag,
                t6.start_date,
                t6.end_date,
                
                if(t5.pid is null ,
                    round(ifnull(t6.plan_cnt, 0) - ifnull(t9.quantity, 0), 2),
                    round(ifnull(t7.finish_cnt,0) - ifnull(t9.quantity,0) ,2)
                ) as wait_out_cnt,
                ifnull(t9.quantity,0) as out_cnt,
                round(ifnull(t8.finish_cnt,0),2) as in_cnt
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't1.product_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't2.customer_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't1.notice_id = t4.id', 't4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom', 't.product_bom_id = t5.id', 't5')
            // 同一个计划的最大最小的排产日
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustPlan','t.id = t6.entrust_id','t6')
            // 获取前面bom的报工合格数量
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewBomFinishCnt','t.notice_detail_id = t1.id and t5.pid = t7.bom_id','t7')
            // 获取当前计划外委bom的报工合格数量
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewBomFinishCnt','t.notice_detail_id = t1.id and t.product_bom_id = t8.bom_id','t8')
            // 获取相同计划的外委出库量
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustOut','t.id = t9.entrust_id','t9')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrderDetail', 't.id = t10.apply_id', 't10')
            ->where('t1.del_flag = 0 and t1.status < 30 and t1.owner = ?1 and t.purchase_order_id = ?2', [1 => SessionData::ownerId(), 2 => $order_id])
            ->orderBy('t1.id asc');
        return $builder->getQuery()->execute();
    }

    public function getOrderData($order_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.order_code,
                s.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a.supplier_id = s.id', 's')
            ->where('a.del_flag = 0 and a.id = ?1', [1 => $order_id]);
        $rows = $builder->getQuery()->execute();
        return count($rows) > 0 ? $rows[0] : null;
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.code,
                a.outstock_date,
                a.status,
                a.status_name,
                a.remarks,
                a.ext_val,
                o.order_code,
                o.order_date,
                d.price,
                d.quantity,
                d.total_money,
                s.name as supplier_name,
                t2.code as product_code,
                t2.name as product_name,
                t3.name as customer_name,
                t4.code as notice_code,
                t5.name as bom_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwoutstock', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseWwoutstockDetail', 'a.id = d.outstock_id', 'd')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 'a.order_id = o.id', 'o')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'o.supplier_id = s.id', 's')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 'd.entrust_id = t.id','t')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t5.id','t5')
            ->where('a.del_flag = 0 and d.del_flag = 0 and a.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('a.id desc, d.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    /**
     * 审核通过
     * @return array
     */
    public function approval()
    {
        return $this->executeInTransaction(function () {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $ww_out_stock = $this->selectByUid($uid);

            if (empty($ww_out_stock)) {
                return $this->error('单据不存在');
            }
            $now = DateUtil::now();
            $user = SessionData::user();
            $ww_out_stock->comment = $comment;
            $ww_out_stock->approved_by = $user->id;
            $ww_out_stock->update_date = $now;
            $ww_out_stock->update_by = $user->id;
            $ww_out_stock->status = 20;
            $ww_out_stock->status_name = Constant::$purchase_outstock_status[$ww_out_stock->status];
            $ww_out_stock->save();

            // 提交的场合
            $detail_data = CvtUtil::emptyToArray($ww_out_stock->detail_data);
            foreach ($detail_data as $item) {
                $detail_row = new PurchaseWwoutstockDetail();
                $detail_row->uid = UUID::make();
                $detail_row->outstock_id = $ww_out_stock->id;
                $detail_row->entrust_id = $item['id'];
                $detail_row->price = $item['price'];
                $detail_row->price_hs = $item['price_hs'];
                $detail_row->quantity = $item['quantity'];
                $detail_row->deputy_unit = $item['goods_deputy_unit'];
                $detail_row->pricing_quantity = $item['pricing_quantity'];
                $detail_row->pricing_unit = $item['goods_unit'];
                $detail_row->total_money = $item['total_money'];
                $detail_row->total_money_hs = $item['total_money_hs'];
                $detail_row->update_date = $now;
                $detail_row->update_by = $user->id;
                $detail_row->del_flag = 0;
                $detail_row->owner = $user->owner;
                $detail_row->save();
            }
        });
    }

    /**
     * 审核驳回
     * @return array
     */
    public function reject()
    {
        return $this->executeInTransaction(function() {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $ww_out_stock = $this->selectByUid($uid);
            if (empty($ww_out_stock)) {
                return $this->error('单据不存在');
            }
            $now = DateUtil::now();
            $user = SessionData::user();
            $ww_out_stock->comment = $comment;
            $ww_out_stock->approved_by = $user->id;
            $ww_out_stock->update_date = $now;
            $ww_out_stock->update_by = $user->id;
            $ww_out_stock->status = 10;
            $ww_out_stock->status_name = '以驳回';
            $ww_out_stock->save();
        });
    }
}