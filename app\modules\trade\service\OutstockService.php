<?php
namespace Envsan\Modules\Trade\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesStockLogs;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Trade\Model\TradeOutstock;
use Envsan\Modules\Trade\Model\TradeOutstockDetail;
use Envsan\Modules\Trade\Util\Constant;
use Phalcon\Mvc\User\Component;

class OutstockService extends BaseService
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.outstock_date,
                t1.remarks,
                t1.ext_val
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOutstock', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.quantity,
                t1.remarks,
                t2.uid as outstock_uid,
                t2.code as outstock_code,
                t2.outstock_date,
                t2.ext_val as outstock_ext_val,
                t7.real_name as outstock_user,
                t3.ext_val as order_detail_ext_val,
                t3.remarks as order_detail_remarks,
                t4.code as order_code,
                t4.ext_val as order_ext_val,
                t4.remarks as order_remarks,
                t5.code as product_code,
                t5.name as product_name,
                t5.ext_val as product_ext_val,
                t6.name as customer_name
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOutstockDetail', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOutstock','t1.outstock_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrderDetail','t1.order_detail_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t3.order_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t4.customer_id = t6.id','t6')
            ->leftJoin('Envsan\Modules\Sys\Model\User','t2.create_by = t7.id','t7')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }


    public function summary()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t6.name as customer_name,
                t6.code as customer_code,
                t1.code as outstock_code,
                t1.outstock_date,
                t8.code as goods_code,
                t5.name as goods_name,
                t5.code as goods_model,
                t5.inventory_unit,
                sum(ifnull(t2.quantity, 0)) as shipped_quantity,
                sum(ifnull(t2.quantity, 0) * ifnull(t3.price_hs,0)) as total_amount
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOutstock', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOutstockDetail','t2.outstock_id = t1.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrderDetail','t2.order_detail_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t3.order_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t4.customer_id = t6.id','t6')
            ->leftJoin('Envsan\Modules\Sys\Model\User','t1.create_by = t7.id','t7')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods','t5.goods_id = t8.id','t8')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->groupBy('t1.id, t1.uid, t6.name, t6.code, t1.code, t1.outstock_date, t8.code, t5.name, t5.code, t5.inventory_unit')
            ->orderBy('t1.outstock_date desc');

        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectOrderAll()
    {
        $customer_id = $this->request->get('customer_id', 'tstring');
        $product_name = $this->request->get('product_name', 'tstring');
        $product_code = $this->request->get('product_code', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.product_id,
                t1.cnt as order_cnt,
                t3.code as order_code,
                t4.code as product_code,
                t4.name as product_name,
                round(ifnull(t2.cnt,0),4) as cnt
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrderDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewStock', 't1.product_id = t2.product_id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t1.order_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t4.id','t4')
            ->where('t1.del_flag = 0 and t3.status = 30 and t1.owner = ?1',[1=>SessionData::ownerId()])
            ->orderBy('t1.product_id,t1.id desc');
        if (empty($customer_id)){
            $builder->andWhere("t3.customer_id is null");
        } else {
            $builder->andWhere("t3.customer_id = ?2", [2 => $customer_id]);
        }
        if (!CheckUtil::is_empty($product_code)) {
            $builder->andWhere("t4.code like ?3", [3 => "%$product_code%"]);
        }
        if (!CheckUtil::is_empty($product_name)) {
            $builder->andWhere("t4.name like ?4", [4 => "%$product_name%"]);
        }
        return $builder;
    }

    /**
     * @param $customer_id
     * @param $ids
     * @return \Phalcon\Mvc\Model\Query\Builder
     */
    public function selectOrderShipmentDetails($customer_id, $ids)
    {
        $product_name = $this->request->get('product_name', 'tstring');
        $inventory_code = $this->request->get('inventory_code', 'tstring');
        $order_code = $this->request->get('order_code', 'tstring');
        $product_model = $this->request->get('product_model', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.product_id,
                t1.cnt as order_cnt,
                t1.price as unit_price,
                t1.price_hs as unit_price_hs,
                t1.price_hs as quote_price,
                round(ifnull(t1.tax_rate,0) * 100,2) as tax_rate,
                round(ifnull(t1.price,0) * ifnull(t1.tax_rate, 0) * ifnull(t1.cnt, 0) ,2) as tax_amount,
                round(ifnull(t1.price,0) * ifnull(t1.cnt, 0) ,2) as net_amount,
                round(ifnull(t1.price_hs,0) * ifnull(t1.cnt, 0) ,2) as total_amount,
                t3.code as order_code,
                t4.inventory_code,
                t4.name as product_name,
                t4.code as product_code,
                t4.inventory_unit,
                t4.goods_code,
                round(ifnull(t2.cnt,0),4) as stock_cnt,
                null as remarks,
                (t1.cnt - sum(ifnull(t5.quantity,0))) as pending_cnt,
                0 as sel
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrderDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewStock', 't1.product_id = t2.product_id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t1.order_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOutstockDetail',
                't1.id = t5.order_detail_id and t5.del_flag = 0 and exists (
                select 1 from Envsan\\Modules\\Trade\\Model\\TradeOutstock t6 
                where t5.outstock_id = t6.id and t6.status = ?3 and t6.del_flag = 0
            )', 't5')
            ->where('t1.del_flag = 0 and t3.status = 30 and t1.owner = ?1 and t3.customer_id = ?2',
                [
                    1=>SessionData::ownerId(),
                    2=>$customer_id,
                    3=>'20'
                ])
            ->groupBy('
                t1.id, t1.uid, t1.product_id, t1.cnt, t1.price, 
                t1.price_hs, t1.tax_rate, t3.code, t4.inventory_code,
                t4.name, t4.code, t4.inventory_unit, t4.goods_code, t2.cnt'
            )
            ->orderBy('t1.product_id,t1.id desc');

        if (!CheckUtil::is_empty($inventory_code)) {
            $builder->andWhere("t4.inventory_code like ?3", [3 => "%$inventory_code%"]);
        }
        if (!CheckUtil::is_empty($product_name)) {
            $builder->andWhere("t4.name like ?4", [4 => "%$product_name%"]);
        }
        if (!CheckUtil::is_empty($order_code)) {
            $builder->andWhere("t3.code like ?5", [5 => "%$order_code%"]);
        }

        if (!CheckUtil::is_empty($product_model)) {
            $builder->andWhere("t4.code like ?6", [6 => "%$product_model%"]);
        }

        if (!empty($ids)) {
            $builder->notInWhere('p1.id', explode(',', $ids));
        }
        return $builder;
    }




    public function selectById($id)
    {
        return TradeOutstock::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function selectByUid($uid)
    {
        return TradeOutstock::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }


    public function create()
    {
        $row = new TradeOutstock();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($act, $row) {
            // 类型：save or commit
            $type = $this->request->getPost('type', ['string', 'trim']);
            // 客户id
            $customer_id = $this->request->getPost('customer_id', 'tstring');
            // 发货时间
            $outstock_date = $this->request->getPost('outstock_date', 'tstring');
            // 备注
            $remarks = $this->request->getPost('remarks', 'tstring');
            // 附件
            $files = urldecode($this->request->getPost('files', ['string', 'trim']));
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
            $detail = str_replace('%2B', '+', urldecode($this->request->getPost('detail', ['string', 'trim'])));
            if (CheckUtil::is_empty($type) || empty($outstock_date) || empty($detail) || CheckUtil::is_empty($customer_id)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $files = CvtUtil::emptyToArray($files);
            $ext_data = CvtUtil::emptyToArray($ext_data);
            $detail = CvtUtil::emptyToArray($detail);
            $now = DateUtil::now();
            $user = SessionData::user();
            $row->customer_id = CvtUtil::blankToNull($customer_id);
            $row->outstock_date = CvtUtil::blankToNull($outstock_date);
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->detail_data = CvtUtil::arrayToNull($detail);;
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            if ($type == 1) {
                $row->status = 10;
            } else {
                $row->status = 20;
            }
            $row->status_name = Constant::$trade_outstock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $ss = new SequenceService();
                $row->code = $ss->useSequence(3);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            $row->save();

            // 如果是提交，生成出库详细
            if ($row->status == 20) {
                $mes_common = new \Envsan\Modules\Mes\Service\CommonService();
                $work_month = $mes_common->getWorkMonth($row->outstock_date);
                foreach ($detail as $item) {
                    $detail_row = new TradeOutstockDetail();
                    $detail_row->uid = UUID::make();
                    // 发货主表id
                    $detail_row->outstock_id = $row->id;
                    // 发货订单明细id
                    $detail_row->order_detail_id = $item['id'];
                    // 发货产品id
                    $detail_row->product_id = $item['product_id'];
                    // 发货数量
                    $detail_row->quantity = $item['quantity'];
                    // 备注
                    $detail_row->remarks = $item['remarks'];
                    $detail_row->update_date = $now;
                    $detail_row->update_by = $user->id;
                    $detail_row->del_flag = 0;
                    $detail_row->owner = $user->owner;
                    if (!$detail_row->save()) {
                        throw new \Exception("MesProduct表更新失败");
                    }
                    $in_logs = new MesStockLogs();
                    $in_logs->uid = UUID::make();
                    $in_logs->outstock_detail_id = $detail_row->id;
                    $in_logs->product_id = $detail_row->product_id;
                    $in_logs->work_month = $work_month;
                    $in_logs->work_date = $row->outstock_date;
                    $in_logs->cnt = $detail_row->quantity * -1;
                    $in_logs->staff_name = $user->real_name;
                    $in_logs->create_time = $now;
                    $in_logs->group_id = $user->group_id;
                    $in_logs->update_date = $now;
                    $in_logs->update_by = $user->id;
                    $in_logs->del_flag = 0;
                    $in_logs->owner = $user->owner;
                    $in_logs->save();
                }
            }
        });

    }

    public function delete(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 10){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->del_flag = 1;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function cancel()
    {
        return $this->executeInTransaction(function () {
            $uid = $this->request->getPost('uid', 'string');
            if (empty($uid)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $row = $this->selectByUid($uid);
            if (empty($row)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            if ($row->status != 20) {
                return $this->error('状态不正确') ;
            }
            $now = DateUtil::now();
            $user = SessionData::user();

            $outstock_rows = TradeOutstockDetail::find(['del_flag = 0 and outstock_id = ?1', 'bind' => [1 => $row->id]]);
            foreach ($outstock_rows as $outstock_row) {
                $outstock_row->del_flag = 1;
                $outstock_row->update_date = $now;
                $outstock_row->update_by = $user->id;
                $outstock_row->save();

                $log_row = MesStockLogs::findFirst(['del_flag = 0 and outstock_detail_id = ?1', 'bind' => [1 => $outstock_row->id]]);
                if (!empty($log_row)) {
                    $log_row->del_flag = 1;
                    $log_row->update_date = $now;
                    $log_row->update_by = $user->id;
                    $log_row->save();
                }
            }
            $row->status = 10;
            $row->status_name = Constant::$trade_outstock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->save();

            return;
        });

    }

}