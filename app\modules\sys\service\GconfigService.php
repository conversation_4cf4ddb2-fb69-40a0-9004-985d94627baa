<?php

namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\GroupConfig;
use Phalcon\Mvc\User\Component;

class GconfigService extends Component
{
    public function selectAll($col = '', $find = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\GroupConfig')
            ->orderBy('id desc')
            ->where('group_id=0 and visible=' . GroupConfig::DICT_VISIBLE_YES . ' and del_flag=0 and owner=' . SessionData::ownerId());

        return $builder;
    }

    public function selectAllByGroupIds($section, $group_ids)
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\GroupConfig')
            ->orderBy('id desc')
            ->where('del_flag=0 and visible=' . GroupConfig::DICT_VISIBLE_YES . ' and owner=' . SessionData::ownerId())
            ->andWhere('section=?1', [1 => $section])
            ->inWhere('group_id', $group_ids);

        return $builder->getQuery()->execute();
    }

    public function create()
    {
        $row = new GroupConfig();
        return $this->build('create', $row);
    }

    private function build($act, $row)
    {
        $type = $this->request->getPost('type', ['string', 'trim']);
        $section = $this->request->getPost('section', ['string', 'trim']);
        $comment = $this->request->getPost('comment', ['string', 'trim']);
        $visible = $this->request->getPost('visible', ['string', 'trim']);

        // 只对json类型的数据忽略
        // todo!!! 安全检查
        if ($type=='json')
            $val = $this->request->getPost('val', 'trim');
        else
            $val = $this->request->getPost('val', ['string', 'trim']);

        if (empty($type) || empty($section) || empty($val) || empty($comment))
            return ErrorHelper::WRONG_INPUT;

        $row->type = $type;
        $row->section = $section;
        $row->val = $val;
        $row->comment = $comment;
        $row->visible = intval($visible);

        $user = SessionData::user();
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;

        if ($act == 'create') {
            $row->owner = $user->owner;
            $row->group_id = 0;
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return GroupConfig::findFirst(['id=?1', 'bind' => [1 => $id]]);
    }

    public function deleteById($id)
    {
        if ($this->acl->isAdmin()) {
            $row = GroupConfig::findFirst(['id=?1', 'bind' => [1 => $id]]);
            if ($row != null && $row->delete())
                return ErrorHelper::NOERROR;
            return ErrorHelper::UNKOWN;
        }
        else {
            return '只有管理员可以删除';
        }
    }

    public function saveCustomizeData($group_id, $defConfig, $row, $val = '')
    {
        if (empty($val)) {
            // todo!! 应该根据类型进行不同的检查
            if ($defConfig->type == 'json')
                $val = $this->request->getPost('val', 'trim');
            else
                $val = $this->request->getPost('val', ['string', 'trim']);
        }

        if (empty($val))
            return ErrorHelper::WRONG_INPUT;

        if ($row != null) {
            $row->val = $val;
        }
        else {
            $row = new GroupConfig();
            $row->group_id = $group_id;
            $row->section = $defConfig->section;
            $row->type = $defConfig->type;
            $row->val = $val;
            $row->visible = $defConfig->visible;
            $row->owner = $defConfig->owner;
            $row->update_date = DateUtil::now();
            $row->update_by = SessionData::user()->id;
        }

        if ($row->save())
            return ErrorHelper::NOERROR;
        return ErrorHelper::UNKOWN;
    }

    public function saveDefault($row)
    {
        if ($row->type == 'json')
            $val = $this->request->getPost('val', 'trim');
        else
            $val = $this->request->getPost('val', ['string', 'trim']);

        $row->val = $val;
        if ($row->save())
            return ErrorHelper::NOERROR;
        return ErrorHelper::UNKOWN;
    }

    /**
     * 取得某个组织的设定
     * @param $section
     * @param $group_id
     * @return GroupConfig
     */
    public function get($section, $group_id)
    {
        return GroupConfig::findFirst([
            'section=?1 and group_id=?2',
            'bind' => [1 => $section, 2 => $group_id]
        ]);
    }

    /**
     * 逐级查找配置，该查找会执行很多次sql，建议对结果进行缓存处理
     * @param $section
     * @param $group_id
     * @return GroupConfig
     */
    public function getr($section, $group_id)
    {
        $gs = new GroupService();
        while(true){
            $row = $this->get($section, $group_id);
            if($row!=null)
                return $row;

            $group = $gs->selectParentById($group_id);
            if ($group!=null)
                $group_id = $group->id;
            else
                break;
        }
        return $this->get($section, 0);
    }

    /**
     * 设定一个基于组织的值，注意:默认的section必须由超级管理员预先创建好
     * @param $group_id
     * @param $section
     * @param $val
     * @return string
     */
    public function put($group_id, $section, $val)
    {
        $defConfig = GroupConfig::findFirst([
            'section=?1 and group_id=0',
            'bind' => [1 => $group_id]
        ]);

        if ($defConfig == null)
            return '默认的section不存在!';

        $row = $this->get($section, $group_id);
        return $this->saveCustomizeData($group_id, $defConfig, $row, $val);
    }

}