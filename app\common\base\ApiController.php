<?php
namespace Envsan\Common\Base;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Sys\Model\Owner;
use Envsan\Modules\Sys\Service\UserService;
use Phalcon\Crypt;

class ApiController extends \Envsan\Common\Base\ControllerBase
{
    protected function make4013()
    {
        $this->response->setStatusCode(401, '401.3');
    }

    protected function make4011()
    {
        $this->response->setStatusCode(401, '401.1');
    }

    /**
     * 用给定的加密信息，解密出对应的用户
     * @param $auth
     * @return null
     */
    private function decrytAuth($auth, $owner)
    {
        $crypt = new Crypt();
        $txt = $crypt->decryptBase64($auth, $this->config->api->authKey);
        if (!empty($txt)) {
            list($login_name, $password) = explode(',', $txt);
            if (!empty($login_name) && !empty($password)) {
                $us = new UserService();
                return $us->selectUser($login_name, $password, $owner->id);
            }
        }
        return null;
    }

        public function beforeExecuteRoute(\Phalcon\Mvc\Dispatcher $dispatcher)
    {
        $customFlag = $this->request->getHeader('X-Custom-Flag');
        // 如果标志为 noapi，调用父类的 beforeExecuteRoute
        if ($customFlag == 'noapi') {
            return parent::beforeExecuteRoute($dispatcher);
        }
        
        // 不使用cookie
        // ini_set('session.use_cookies', '0');

        // 允许跨域
        header('Access-Control-Allow-Origin:*');

        // 设置所有的输出json格式
        $this->setJsonResponse();

        // 允许客户端上传SID作为session id
        header('Access-Control-Allow-Headers:SID, AUTH, Content-Type, X-Custom-Flag');

        // http会发送options查询各种支持的协议，die即可
        if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'OPTIONS')
            die();

        // 处理json体请求
        $this->handleRequestPayload();

        $module = strtolower($this->router->getModuleName());
        $controller = strtolower($dispatcher->getControllerName());
        $action = strtolower($dispatcher->getActionName());
        $controllerClass = $dispatcher->getControllerClass();

        // class级别的noacl
        $collection = $this->annotations->get($controllerClass)->getClassAnnotations();
        if ($collection != false && $collection->has('noacl'))
            return true;

        $annotations = $this->annotations->getMethod($controllerClass, $dispatcher->getActiveMethod());
        if ($annotations->has('noacl'))
            return true;

        // 先判断是否有session，没有的话，尝试用http_auth的信息解密
        if (!$this->session->has('fuser')) {
            if (isset($_SERVER['HTTP_AUTH'])) {
                $auth = $_SERVER['HTTP_AUTH'];
                if (!empty($auth)) {
                    // todo!!
                    $owner = Owner::findFirst();
                    $user = $this->decrytAuth($auth, $owner);
                    if ($user != null)
                        $this->session->set('fuser', $user);
                }
            }
        }

        if ($this->session->has('user')) {
            if (SessionData::isSuper())
                return true;

            if ($annotations->has('super') || ($collection != false && $collection->has('super'))) {
                $this->make4013();
                return false;
            }

            if ($annotations->has('skipacl') || ($collection != false && $collection->has('skipacl')))
                return true;

            $acl_link = '';
            if ($annotations->has('acl')) {
                $acl = $annotations->get('acl');
                $arr = $acl->getArgument(0);
                if (!empty($arr))
                    $acl_link = $arr['link'];
            }

            if (!$this->acl->has("$module:$controller:$action")) {
                if ($acl_link != '' && $this->acl->has($acl_link))
                    return true;

                $this->make4013();
                return false;
            }

            return true;
        }

        $this->make4011();
        return false;
    }
}