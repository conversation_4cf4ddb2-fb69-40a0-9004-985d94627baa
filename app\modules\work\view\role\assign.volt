<div class="page-container">
    <div class="page-content-wrapper">
        <div class="page-content">
            <div  id="app" class="search-page">
                <div style="width: 100%;text-align: right;margin-bottom: 5px">
                    <button type="button" class="btn yellow" @click="confirm"><i class="fa fa-check"></i> 保存</button>
                </div>
                <div>
                    <table class="table table-bordered" style="margin-bottom: 0">
                        <thead>
                        <tr style="background-color: #3598DC;color: #FFFFFF">
                            <th>选择</th>
                            <th>业务名称</th>
                            <th>业务组织</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="item,idx in list" style="background-color: #F2F2F2">
                            <th>
                                <a @click="selItem(idx)" v-if="item.sel == 1">
                                    <i class="fa fa-check-square" style="color: green;font-size: 20px"></i>
                                </a>
                                <a @click="selItem(idx)" v-else>
                                    <i class="fa fa-check-square-o" style="color: #a2a2a2;font-size: 20px"></i>
                                </a>
                            </th>
                            <td v-text="item.name"></td>
                            <td v-text="item.group_name"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            list: {{ list }}
        },
        methods: {
            selItem(idx){
                let item = this.list[idx];
                if (item.sel == 1){
                    item.sel = 0;
                } else {
                    item.sel = 1;
                }
            },
            confirm(){
                var url= '{{ url('work/role/assign/'~uid) }}';
                var list = [];
                for(var item of this.list){
                    if (item.sel == 1){
                        list.push(item.id);
                    }
                }
                if(list.length == 0){
                    toastr.error('请选择业务');
                    return;
                }
                showSpin();
                $.post(url, {list:list}, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });
</script>
