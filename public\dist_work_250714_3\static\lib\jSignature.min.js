/*

jSignature v2 "2016-11-05T00:40" "commit ID d9ac2f271cfdf479d005a449193e09985043cafa"
Copyright (c) 2012 Willow Systems Corp http://willow-systems.com
Copyright (c) 2010 Brinley <PERSON> http://www.unbolt.net
MIT License <http://www.opensource.org/licenses/mit-license.php>


Simplify.js BSD 
(c) 2012, Vladimir <PERSON>nkin
mourner.github.com/simplify-js


base64 encoder
MIT, GPL
http://phpjs.org/functions/base64_encode
+   original by: <PERSON> (http://rumkin.com)
+   improved by: <PERSON><PERSON>
+   improved by: Thunder.m
+   improved by: <PERSON> (http://kevin.vanzonneveld.net)
+   bugfixed by: Pellentesque Malesuada
+   improved by: <PERSON> (http://kevin.vanzonneveld.net)
+   improved by: <PERSON><PERSON><PERSON> (http://kukawski.pl)


jSignature v2 jSignature's Undo Button and undo functionality plugin


jSignature v2 jSignature's custom "base30" format export and import plugins.


jSignature v2 SVG export plugin.

*/
(function(){function t(b){var a,c=b.css("color"),e;b=b[0];for(var h=!1;b&&!e&&!h;){try{a=$(b).css("background-color")}catch(l){a="transparent"}"transparent"!==a&&"rgba(0, 0, 0, 0)"!==a&&(e=a);h=b.body;b=b.parentNode}b=/rgb[a]*\((\d+),\s*(\d+),\s*(\d+)/;var h=/#([AaBbCcDdEeFf\d]{2})([AaBbCcDdEeFf\d]{2})([AaBbCcDdEeFf\d]{2})/,m;a=void 0;(a=c.match(b))?m={r:parseInt(a[1],10),g:parseInt(a[2],10),b:parseInt(a[3],10)}:(a=c.match(h))&&(m={r:parseInt(a[1],16),g:parseInt(a[2],16),b:parseInt(a[3],16)});var d;
e?(a=void 0,(a=e.match(b))?d={r:parseInt(a[1],10),g:parseInt(a[2],10),b:parseInt(a[3],10)}:(a=e.match(h))&&(d={r:parseInt(a[1],16),g:parseInt(a[2],16),b:parseInt(a[3],16)})):d=m?127<Math.max.apply(null,[m.r,m.g,m.b])?{r:0,g:0,b:0}:{r:255,g:255,b:255}:{r:255,g:255,b:255};a=function(a){return"rgb("+[a.r,a.g,a.b].join(", ")+")"};m&&d?(b=Math.max.apply(null,[m.r,m.g,m.b]),m=Math.max.apply(null,[d.r,d.g,d.b]),m=Math.round(m+-.75*(m-b)),m={r:m,g:m,b:m}):m?(m=Math.max.apply(null,[m.r,m.g,m.b]),b=1,127<m&&
(b=-1),m=Math.round(m+96*b),m={r:m,g:m,b:m}):m={r:191,g:191,b:191};return{color:c,"background-color":d?a(d):e,"decor-color":a(m)}}function k(b,a){this.x=b;this.y=a;this.reverse=function(){return new this.constructor(-1*this.x,-1*this.y)};this._length=null;this.getLength=function(){this._length||(this._length=Math.sqrt(Math.pow(this.x,2)+Math.pow(this.y,2)));return this._length};var c=function(a){return Math.round(a/Math.abs(a))};this.resizeTo=function(a){if(0===this.x&&0===this.y)this._length=0;else if(0===
this.x)this._length=a,this.y=a*c(this.y);else if(0===this.y)this._length=a,this.x=a*c(this.x);else{var b=Math.abs(this.y/this.x),e=Math.sqrt(Math.pow(a,2)/(1+Math.pow(b,2))),b=b*e;this._length=a;this.x=e*c(this.x);this.y=b*c(this.y)}return this};this.angleTo=function(a){var b=this.getLength()*a.getLength();return 0===b?0:Math.acos(Math.min(Math.max((this.x*a.x+this.y*a.y)/b,-1),1))/Math.PI}}function g(b,a){this.x=b;this.y=a;this.getVectorToCoordinates=function(a,b){return new k(a-this.x,b-this.y)};
this.getVectorFromCoordinates=function(a,b){return this.getVectorToCoordinates(a,b).reverse()};this.getVectorToPoint=function(a){return new k(a.x-this.x,a.y-this.y)};this.getVectorFromPoint=function(a){return this.getVectorToPoint(a).reverse()}}function n(b,a,c,e,h){this.data=b;this.context=a;if(b.length)for(var m=b.length,d,l,f=0;f<m;f++){d=b[f];l=d.x.length;c.call(a,d);for(var p=1;p<l;p++)e.call(a,d,p);h.call(a,d)}this.changed=function(){};this.startStrokeFn=c;this.addToStrokeFn=e;this.endStrokeFn=
h;this.inStroke=!1;this._stroke=this._lastPoint=null;this.startStroke=function(a){if(a&&"number"==typeof a.x&&"number"==typeof a.y){this._stroke={x:[a.x],y:[a.y]};this.data.push(this._stroke);this._lastPoint=a;this.inStroke=!0;var b=this._stroke,c=this.startStrokeFn,e=this.context;setTimeout(function(){c.call(e,b)},3);return a}return null};this.addToStroke=function(a){if(this.inStroke&&"number"===typeof a.x&&"number"===typeof a.y&&4<Math.abs(a.x-this._lastPoint.x)+Math.abs(a.y-this._lastPoint.y)){var b=
this._stroke.x.length;this._stroke.x.push(a.x);this._stroke.y.push(a.y);this._lastPoint=a;var c=this._stroke,e=this.addToStrokeFn,h=this.context;setTimeout(function(){e.call(h,c,b)},3);return a}return null};this.endStroke=function(){var a=this.inStroke;this.inStroke=!1;this._lastPoint=null;if(a){var b=this._stroke,c=this.endStrokeFn,e=this.context,h=this.changed;setTimeout(function(){c.call(e,b);h.call(e)},3);return!0}return null}}function r(b,a,c,e){if("ratio"===a||"%"===a.split("")[a.length-1])this.eventTokens[c+
".parentresized"]=e.subscribe(c+".parentresized",function(a,m,d,l){return function(){var h=m.width();if(h!==d){for(var l in a)a.hasOwnProperty(l)&&(e.unsubscribe(a[l]),delete a[l]);var f=b.settings;b.$parent.children().remove();for(l in b)b.hasOwnProperty(l)&&delete b[l];l=f.data;var h=1*h/d,u=[],A,E,g,k,r,n;E=0;for(g=l.length;E<g;E++){n=l[E];A={x:[],y:[]};k=0;for(r=n.x.length;k<r;k++)A.x.push(n.x[k]*h),A.y.push(n.y[k]*h);u.push(A)}f.data=u;m[c](f)}}}(this.eventTokens,this.$parent,this.$parent.width(),
1*this.canvas.width/this.canvas.height))}function x(b,a,c){var e=this.$parent=$(b);b=this.eventTokens={};this.events=new v(this);var h=$.fn.jSignature("globalEvents"),d={width:"ratio",height:"ratio",sizeRatio:4,color:"#000","background-color":"#fff","decor-color":"#eee",lineWidth:0,minFatFingerCompensation:-10,showUndoButton:!1,readOnly:!1,data:[],signatureLine:!1};$.extend(d,t(e));a&&$.extend(d,a);this.settings=d;for(var f in c)c.hasOwnProperty(f)&&c[f].call(this,f);this.events.publish("jSignature.initializing");
this.$controlbarUpper=$('<div style="padding:0 !important; margin:0 !important;width: 100% !important; height: 0 !important; -ms-touch-action: none; touch-action: none;margin-top:-1em !important; margin-bottom:1em !important;"></div>').appendTo(e);this.isCanvasEmulator=!1;a=this.canvas=this.initializeCanvas(d);c=$(a);this.$controlbarLower=$('<div style="padding:0 !important; margin:0 !important;width: 100% !important; height: 0 !important; -ms-touch-action: none; touch-action: none;margin-top:-1.5em !important; margin-bottom:1.5em !important; position: relative;"></div>').appendTo(e);
this.canvasContext=a.getContext("2d");c.data("jSignature.this",this);d.lineWidth=function(a,b){return a?a:Math.max(Math.round(b/400),2)}(d.lineWidth,a.width);this.lineCurveThreshold=3*d.lineWidth;d.cssclass&&""!=$.trim(d.cssclass)&&c.addClass(d.cssclass);this.fatFingerCompensation=0;e=function(a){var b,c,e=function(e){e=e.changedTouches&&0<e.changedTouches.length?e.changedTouches[0]:e;return new g(Math.round(e.pageX+b),Math.round(e.pageY+c)+a.fatFingerCompensation)},d=new w(750,function(){a.dataEngine.endStroke()});
this.drawEndHandler=function(b){if(!a.settings.readOnly){try{b.preventDefault()}catch(F){}d.clear();a.dataEngine.endStroke()}};this.drawStartHandler=function(h){if(!a.settings.readOnly){h.preventDefault();var m=$(a.canvas).offset();b=-1*m.left;c=-1*m.top;a.dataEngine.startStroke(e(h));d.kick()}};this.drawMoveHandler=function(b){a.settings.readOnly||(b.preventDefault(),a.dataEngine.inStroke&&(a.dataEngine.addToStroke(e(b)),d.kick()))};return this}.call({},this);(function(a,b,c){var e=this.canvas,h=
$(e);this.isCanvasEmulator?(h.bind("mousemove.jSignature",c),h.bind("mouseup.jSignature",a),h.bind("mousedown.jSignature",b)):(e.ontouchstart=function(h){e.onmousedown=e.onmouseup=e.onmousemove=void 0;this.fatFingerCompensation=d.minFatFingerCompensation&&-3*d.lineWidth>d.minFatFingerCompensation?-3*d.lineWidth:d.minFatFingerCompensation;b(h);e.ontouchend=a;e.ontouchstart=b;e.ontouchmove=c},e.onmousedown=function(d){e.ontouchstart=e.ontouchend=e.ontouchmove=void 0;b(d);e.onmousedown=b;e.onmouseup=
a;e.onmousemove=c},window.navigator.msPointerEnabled&&(e.onmspointerdown=b,e.onmspointerup=a,e.onmspointermove=c))}).call(this,e.drawEndHandler,e.drawStartHandler,e.drawMoveHandler);b["jSignature.windowmouseup"]=h.subscribe("jSignature.windowmouseup",e.drawEndHandler);this.events.publish("jSignature.attachingEventHandlers");r.call(this,this,d.width.toString(10),"jSignature",h);this.resetCanvas(d.data);this.events.publish("jSignature.initialized");return this}function y(b){if(b.getContext)return!1;
var a=b.ownerDocument.parentWindow,c=a.FlashCanvas?b.ownerDocument.parentWindow.FlashCanvas:"undefined"===typeof FlashCanvas?void 0:FlashCanvas;if(c){b=c.initElement(b);c=1;a&&a.screen&&a.screen.deviceXDPI&&a.screen.logicalXDPI&&(c=1*a.screen.deviceXDPI/a.screen.logicalXDPI);if(1!==c)try{$(b).children("object").get(0).resize(Math.ceil(b.width*c),Math.ceil(b.height*c)),b.getContext("2d").scale(c,c)}catch(e){}return!0}throw Error("Canvas element does not support 2d context. jSignature cannot proceed.");
}var w=function(b,a){var c;this.kick=function(){clearTimeout(c);c=setTimeout(a,b)};this.clear=function(){clearTimeout(c)};return this},v=function(b){this.topics={};this.context=b?b:this;this.publish=function(a,b,e,d){if(this.topics[a]){var c=this.topics[a],h=Array.prototype.slice.call(arguments,1),f=[],g=[],p,q,u,A;q=0;for(u=c.length;q<u;q++)A=c[q],p=A[0],A[1]&&(A[0]=function(){},f.push(q)),g.push(p);q=0;for(u=f.length;q<u;q++)c.splice(f[q],1);q=0;for(u=g.length;q<u;q++)g[q].apply(this.context,h)}};
this.subscribe=function(a,b,e){this.topics[a]?this.topics[a].push([b,e]):this.topics[a]=[[b,e]];return{topic:a,callback:b}};this.unsubscribe=function(a){if(this.topics[a.topic])for(var b=this.topics[a.topic],e=0,d=b.length;e<d;e++)b[e]&&b[e][0]===a.callback&&b.splice(e,1)}},z=function(b,a,c,e,d){b.beginPath();b.moveTo(a,c);b.lineTo(e,d);b.closePath();b.stroke()},D=function(b){var a=this.canvasContext,c=b.x[0];b=b.y[0];var e=this.settings.lineWidth,d=a.fillStyle;a.fillStyle=a.strokeStyle;a.fillRect(c+
e/-2,b+e/-2,e,e);a.fillStyle=d},f=function(b,a){var c=new g(b.x[a-1],b.y[a-1]),e=new g(b.x[a],b.y[a]),d=c.getVectorToPoint(e);if(1<a){var m=new g(b.x[a-2],b.y[a-2]),f=m.getVectorToPoint(c),l;if(f.getLength()>this.lineCurveThreshold){l=2<a?(new g(b.x[a-3],b.y[a-3])).getVectorToPoint(m):new k(0,0);var n=.35*f.getLength(),p=f.angleTo(l.reverse()),q=d.angleTo(f.reverse());l=(new k(l.x+f.x,l.y+f.y)).resizeTo(Math.max(.05,p)*n);var u=(new k(f.x+d.x,f.y+d.y)).reverse().resizeTo(Math.max(.05,q)*n),f=this.canvasContext,
n=m.x,q=m.y,p=c.x,A=c.y,r=m.x+l.x,m=m.y+l.y;l=c.x+u.x;u=c.y+u.y;f.beginPath();f.moveTo(n,q);f.bezierCurveTo(r,m,l,u,p,A);f.closePath();f.stroke()}}d.getLength()<=this.lineCurveThreshold&&z(this.canvasContext,c.x,c.y,e.x,e.y)},d=function(b){var a=b.x.length-1;if(0<a){var c=new g(b.x[a],b.y[a]),e=new g(b.x[a-1],b.y[a-1]),d=e.getVectorToPoint(c);if(d.getLength()>this.lineCurveThreshold)if(1<a){b=(new g(b.x[a-2],b.y[a-2])).getVectorToPoint(e);var f=(new k(b.x+d.x,b.y+d.y)).resizeTo(d.getLength()/2),d=
this.canvasContext;b=e.x;var a=e.y,E=c.x,l=c.y,n=e.x+f.x,e=e.y+f.y,f=c.x,c=c.y;d.beginPath();d.moveTo(b,a);d.bezierCurveTo(n,e,f,c,E,l);d.closePath();d.stroke()}else z(this.canvasContext,e.x,e.y,c.x,c.y)}};x.prototype.resetCanvas=function(b,a){var c=this.canvas,e=this.settings,h=this.canvasContext,m=this.isCanvasEmulator,g=c.width,l=c.height;a||h.clearRect(0,0,g+30,l+30);h.shadowColor=h.fillStyle=e["background-color"];m&&h.fillRect(0,0,g+30,l+30);h.lineWidth=Math.ceil(parseInt(e.lineWidth,10));h.lineCap=
h.lineJoin="round";if(e.signatureLine){if(null!=e["decor-color"]){h.strokeStyle=e["decor-color"];h.shadowOffsetX=0;h.shadowOffsetY=0;var k=Math.round(l/5);z(h,1.5*k,l-k,g-1.5*k,l-k)}m||(h.shadowColor=h.strokeStyle,h.shadowOffsetX=.5*h.lineWidth,h.shadowOffsetY=-.6*h.lineWidth,h.shadowBlur=0)}h.strokeStyle=e.color;b||(b=[]);h=this.dataEngine=new n(b,this,D,f,d);e.data=b;$(c).data("jSignature.data",b).data("jSignature.settings",e);h.changed=function(a,b,c){return function(){b.publish(c+".change");a.trigger("change")}}(this.$parent,
this.events,"jSignature");h.changed();return!0};x.prototype.initializeCanvas=function(b){var a=document.createElement("canvas"),c=$(a);b.width===b.height&&"ratio"===b.height&&(b.width="100%");c.css({margin:0,padding:0,border:"none",height:"ratio"!==b.height&&b.height?b.height.toString(10):1,width:"ratio"!==b.width&&b.width?b.width.toString(10):1,"-ms-touch-action":"none","touch-action":"none","background-color":b["background-color"]});c.appendTo(this.$parent);"ratio"===b.height?c.css("height",Math.round(c.width()/
b.sizeRatio)):"ratio"===b.width&&c.css("width",Math.round(c.height()*b.sizeRatio));c.addClass("jSignature");a.width=c.width();a.height=c.height();this.isCanvasEmulator=y(a);a.onselectstart=function(a){a&&a.preventDefault&&a.preventDefault();a&&a.stopPropagation&&a.stopPropagation();return!1};return a};(function(b){function a(a,b,c){var d=new Image,e=this;d.onload=function(){var a=e.getContext("2d"),b=a.shadowColor;a.shadowColor="transparent";a.drawImage(d,0,0,d.width<e.width?d.width:e.width,d.height<
e.height?d.height:e.height);a.shadowColor=b};d.src="data:"+b+","+a}function c(a,b){this.find("canvas.jSignature").add(this.filter("canvas.jSignature")).data("jSignature.this").resetCanvas(a,b);return this}function d(a,b){if(void 0===b&&"string"===typeof a&&"data:"===a.substr(0,5)&&(b=a.slice(5).split(",")[0],a=a.slice(6+b.length),b===a))return;var c=this.find("canvas.jSignature").add(this.filter("canvas.jSignature"));if(l.hasOwnProperty(b))0!==c.length&&l[b].call(c[0],a,b,function(a){return function(){return a.resetCanvas.apply(a,
arguments)}}(c.data("jSignature.this")));else throw Error("jSignature is unable to find import plugin with for format '"+String(b)+"'");return this}var h=new v;(function(a,b,c,d){var e,h=function(){a.publish(b+".parentresized")};c(d).bind("resize."+b,function(){e&&clearTimeout(e);e=setTimeout(h,500)}).bind("mouseup."+b,function(c){a.publish(b+".windowmouseup")})})(h,"jSignature",$,b);var f={},g={"default":function(a){return this.toDataURL()},"native":function(a){return a},image:function(a){a=this.toDataURL();
if("string"===typeof a&&4<a.length&&"data:"===a.slice(0,5)&&-1!==a.indexOf(",")){var b=a.indexOf(",");return[a.slice(5,b),a.substr(b+1)]}return[]}},l={"native":function(a,b,c){c(a)},image:a,"image/png;base64":a,"image/jpeg;base64":a,"image/jpg;base64":a},k=function(a){var b=!1;for(a=a.parentNode;a&&!b;)b=a.body,a=a.parentNode;return!b},p={"export":g,"import":l,instance:f},n={init:function(a){return this.each(function(){k(this)||new x(this,a,f)})},destroy:function(){return this.each(function(){if(!k(this)){var a=
$(this).find("canvas").data("jSignature.this");a&&(a.$controlbarLower.remove(),a.$controlbarUpper.remove(),$(a.canvas).remove())}})},getSettings:function(){return this.find("canvas.jSignature").add(this.filter("canvas.jSignature")).data("jSignature.this").settings},isModified:function(){return null!==this.find("canvas.jSignature").add(this.filter("canvas.jSignature")).data("jSignature.this").dataEngine._stroke},updateSetting:function(a,b,c){var d=this.find("canvas.jSignature").add(this.filter("canvas.jSignature")).data("jSignature.this");
d.settings[a]=b;d.resetCanvas(c?null:d.settings.data,!0);return d.settings[a]},clear:c,reset:c,addPlugin:function(a,b,c){p.hasOwnProperty(a)&&(p[a][b]=c);return this},listPlugins:function(a){var b=[];if(p.hasOwnProperty(a)){a=p[a];for(var c in a)a.hasOwnProperty(c)&&b.push(c)}return b},getData:function(a){var b=this.find("canvas.jSignature").add(this.filter("canvas.jSignature"));void 0===a&&(a="default");if(0!==b.length&&g.hasOwnProperty(a))return g[a].call(b.get(0),b.data("jSignature.data"),b.data("jSignature.settings"))},
importData:d,setData:d,globalEvents:function(){return h},disable:function(){this.find("input").attr("disabled",1);this.find("canvas.jSignature").addClass("disabled").data("jSignature.this").settings.readOnly=!0},enable:function(){this.find("input").removeAttr("disabled");this.find("canvas.jSignature").removeClass("disabled").data("jSignature.this").settings.readOnly=!1},events:function(){return this.find("canvas.jSignature").add(this.filter("canvas.jSignature")).data("jSignature.this").events}};$.fn.jSignature=
function(a){if(a&&"object"!==typeof a){if("string"===typeof a&&n[a])return n[a].apply(this,Array.prototype.slice.call(arguments,1));$.error("Method "+String(a)+" does not exist on jQuery.jSignature")}else return n.init.apply(this,arguments)}})(window)})();
(function(){function t(k,g,n){k=k.call(this);(function(g,k,n){g.events.subscribe(n+".change",function(){g.dataEngine.data.length?k.show():k.hide()})})(this,k,g);(function(g,k,n){var r=n+".undo";k.bind("click",function(){g.events.publish(r)});g.events.subscribe(r,function(){var k=g.dataEngine.data;k.length&&(k.pop(),g.resetCanvas(k))})})(this,k,this.events.topics.hasOwnProperty(g+".undo")?n:g)}$.fn.jSignature("addPlugin","instance","UndoButton",function(k){this.events.subscribe("jSignature.attachingEventHandlers",
function(){if(this.settings[k]){var g=this.settings[k];"function"!==typeof g&&(g=function(){var g=$('<input type="button" value="Undo last stroke" style="position:absolute;display:none;margin:0 !important;top:auto" />').appendTo(this.$controlbarLower),k=g.width();g.css("left",Math.round((this.canvas.width-k)/2));k!==g.width()&&g.width(k);return g});t.call(this,g,"jSignature",k)}})})})();
(function(){for(var t={},k={},g="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWX".split(""),n=g.length/2,r=n-1;-1<r;r--)t[g[r]]=g[r+n],k[g[r+n]]=g[r];var x=function(d){d=d.split("");for(var b=d.length,a=1;a<b;a++)d[a]=t[d[a]];return d.join("")},y=function(d){for(var b=[],a=0,c=1,e=d.length,h,f,g=0;g<e;g++)h=Math.round(d[g]),f=h-a,a=h,0>f&&0<c?(c=-1,b.push("Z")):0<f&&0>c&&(c=1,b.push("Y")),h=Math.abs(f),h>=n?b.push(x(h.toString(n))):b.push(h.toString(n));return b.join("")},w=function(d){var b=
[];d=d.split("");for(var a=d.length,c,e=1,f=[],m=0,g=0;g<a;g++)c=d[g],c in t||"Z"===c||"Y"===c?(0!==f.length&&(f=parseInt(f.join(""),n)*e+m,b.push(f),m=f),"Z"===c?(e=-1,f=[]):"Y"===c?(e=1,f=[]):f=[c]):f.push(k[c]);b.push(parseInt(f.join(""),n)*e+m);return b},v=function(d){for(var b=[],a=d.length,c,e=0;e<a;e++)c=d[e],b.push(y(c.x)),b.push(y(c.y));return b.join("_")},z=function(d){var b=[];d=d.split("_");for(var a=d.length/2,c=0;c<a;c++)b.push({x:w(d[2*c]),y:w(d[2*c+1])});return b},D=function(d){return["image/jsignature;base30",
v(d)]},f=function(d,b,a){"string"===typeof d&&("image/jsignature;base30"===d.substring(0,23).toLowerCase()&&(d=d.substring(24)),a(z(d)))};if(null==this.jQuery)throw Error("We need jQuery for some of the functionality. jQuery is not detected. Failing to initialize...");(function(d){d=d.fn.jSignature;d("addPlugin","export","base30",D);d("addPlugin","export","image/jsignature;base30",D);d("addPlugin","import","base30",f);d("addPlugin","import","image/jsignature;base30",f)})(this.jQuery);this.jSignatureDebug&&
(this.jSignatureDebug.base30={remapTailChars:x,compressstrokeleg:y,uncompressstrokeleg:w,compressstrokes:v,uncompressstrokes:z,charmap:t})}).call("undefined"!==typeof window?window:this);
(function(){function t(f,d){this.x=f;this.y=d;this.reverse=function(){return new this.constructor(-1*this.x,-1*this.y)};this._length=null;this.getLength=function(){this._length||(this._length=Math.sqrt(Math.pow(this.x,2)+Math.pow(this.y,2)));return this._length};var b=function(a){return Math.round(a/Math.abs(a))};this.resizeTo=function(a){if(0===this.x&&0===this.y)this._length=0;else if(0===this.x)this._length=a,this.y=a*b(this.y);else if(0===this.y)this._length=a,this.x=a*b(this.x);else{var c=Math.abs(this.y/
this.x),d=Math.sqrt(Math.pow(a,2)/(1+Math.pow(c,2))),c=c*d;this._length=a;this.x=d*b(this.x);this.y=c*b(this.y)}return this};this.angleTo=function(a){var b=this.getLength()*a.getLength();return 0===b?0:Math.acos(Math.min(Math.max((this.x*a.x+this.y*a.y)/b,-1),1))/Math.PI}}function k(f,d){this.x=f;this.y=d;this.getVectorToCoordinates=function(b,a){return new t(b-this.x,a-this.y)};this.getVectorFromCoordinates=function(b,a){return this.getVectorToCoordinates(b,a).reverse()};this.getVectorToPoint=function(b){return new t(b.x-
this.x,b.y-this.y)};this.getVectorFromPoint=function(b){return this.getVectorToPoint(b).reverse()}}function g(f,d){var b=Math.pow(10,d);return Math.round(f*b)/b}function n(f,d,b){d+=1;var a=new k(f.x[d-1],f.y[d-1]),c=new k(f.x[d],f.y[d]),c=a.getVectorToPoint(c),e=new k(f.x[d-2],f.y[d-2]),a=e.getVectorToPoint(a);return a.getLength()>b?(b=2<d?(new k(f.x[d-3],f.y[d-3])).getVectorToPoint(e):new t(0,0),f=.35*a.getLength(),e=a.angleTo(b.reverse()),d=c.angleTo(a.reverse()),b=(new t(b.x+a.x,b.y+a.y)).resizeTo(Math.max(.05,
e)*f),c=(new t(a.x+c.x,a.y+c.y)).reverse().resizeTo(Math.max(.05,d)*f),c=new t(a.x+c.x,a.y+c.y),["c",g(b.x,2),g(b.y,2),g(c.x,2),g(c.y,2),g(a.x,2),g(a.y,2)]):["l",g(a.x,2),g(a.y,2)]}function r(f,d){var b=f.x.length-1,a=new k(f.x[b],f.y[b]),c=new k(f.x[b-1],f.y[b-1]),a=c.getVectorToPoint(a);if(1<b&&a.getLength()>d){var b=(new k(f.x[b-2],f.y[b-2])).getVectorToPoint(c),c=a.angleTo(b.reverse()),e=.35*a.getLength(),b=(new t(b.x+a.x,b.y+a.y)).resizeTo(Math.max(.05,c)*e);return["c",g(b.x,2),g(b.y,2),g(a.x,
2),g(a.y,2),g(a.x,2),g(a.y,2)]}return["l",g(a.x,2),g(a.y,2)]}function x(f,d,b){d=["M",g(f.x[0]-d,2),g(f.y[0]-b,2)];b=1;for(var a=f.x.length-1;b<a;b++)d.push.apply(d,n(f,b,1));0<a?d.push.apply(d,r(f,b,1)):0===a&&d.push.apply(d,["l",1,1]);return d.join(" ")}function y(f){for(var d=[],b=[["fill",void 0,"none"],["stroke","color","#000000"],["stroke-width","lineWidth",2],["stroke-linecap",void 0,"round"],["stroke-linejoin",void 0,"round"]],a=b.length-1;0<=a;a--){var c=b[a][1],e=b[a][2];d.push(b[a][0]+
'="'+(c in f&&f[c]?f[c]:e)+'"')}return d.join(" ")}function w(f,d){var b=['<?xml version="1.0" encoding="UTF-8" standalone="no"?>','<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'],a,c=f.length,e,h=[],g=[],k=e=a=0,l=0,n=[];if(0!==c){for(a=0;a<c;a++){k=f[a];l=[];e={x:[],y:[]};var p,q;p=0;for(q=k.x.length;p<q;p++)l.push({x:k.x[p],y:k.y[p]});l=simplify(l,.7,!0);p=0;for(q=l.length;p<q;p++)e.x.push(l[p].x),e.y.push(l[p].y);n.push(e);h=h.concat(e.x);g=
g.concat(e.y)}c=Math.min.apply(null,h)-1;a=Math.max.apply(null,h)+1;h=Math.min.apply(null,g)-1;g=Math.max.apply(null,g)+1;k=0>c?0:c;l=0>h?0:h;a-=c;e=g-h}b.push('<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="'+a.toString()+'" height="'+e.toString()+'">');a=0;for(c=n.length;a<c;a++)e=n[a],b.push("<path "+y(d)+' d="'+x(e,k,l)+'"/>');b.push("</svg>");return b.join("")}function v(f,d){return["image/svg+xml",w(f,d)]}function z(f,d){return["image/svg+xml;base64",D(w(f,d))]}(function(f,d){"use strict";
(typeof exports!=d+""?exports:f).simplify=function(b,a,c){a=a!==d?a*a:1;if(!c){var e=b.length,f,g=b[0],k=[g];for(c=1;c<e;c++){f=b[c];var l=f.x-g.x,n=f.y-g.y;l*l+n*n>a&&(k.push(f),g=f)}b=(g!==f&&k.push(f),k)}f=b;c=f.length;var e=new (typeof Uint8Array!=d+""?Uint8Array:Array)(c),g=0,k=c-1,p,q,r=[],t=[],z=[];for(e[g]=e[k]=1;k;){n=0;for(l=g+1;l<k;l++){p=f[l];var B=f[g],x=f[k],v=B.x,w=B.y,B=x.x-v,C=x.y-w,y;if(0!==B||0!==C)y=((p.x-v)*B+(p.y-w)*C)/(B*B+C*C),1<y?(v=x.x,w=x.y):0<y&&(v+=B*y,w+=C*y);p=(B=p.x-
v,C=p.y-w,B*B+C*C);p>n&&(q=l,n=p)}n>a&&(e[q]=1,r.push(g),t.push(q),r.push(q),t.push(k));g=r.pop();k=t.pop()}for(l=0;l<c;l++)e[l]&&z.push(f[l]);return b=z,b}})(window);if("function"!==typeof D)var D=function(f){var d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".split(""),b,a,c,e,g=0,k=0,n=[];do b=f.charCodeAt(g++),a=f.charCodeAt(g++),c=f.charCodeAt(g++),e=b<<16|a<<8|c,b=e>>18&63,a=e>>12&63,c=e>>6&63,e&=63,n[k++]=d[b]+d[a]+d[c]+d[e];while(g<f.length);d=n.join("");f=f.length%3;
return(f?d.slice(0,f-3):d)+"===".slice(f||3)};if("undefined"===typeof $)throw Error("We need jQuery for some of the functionality. jQuery is not detected. Failing to initialize...");(function(f){f=f.fn.jSignature;f("addPlugin","export","svg",v);f("addPlugin","export","image/svg+xml",v);f("addPlugin","export","svgbase64",z);f("addPlugin","export","image/svg+xml;base64",z)})($)})();