<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\GoodsService;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Util\Constant;


/**
 * @name('物料')
 */
class GoodsController extends SuperController
{
    /**
     * @skipacl
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new GoodsService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setDetail($page->rows->toArray());
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'purchase:goodstype:list'})
     */
    public function createAction($uid = '')
    {
        $s = new GoodsService();
        $gs = new GoodsTypeService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }

        $row = $gs->selectByUid($uid);
        if ($row == null) {
            die(ErrorHelper::WRONG_ID);
        }
        $jrow = (new PurchaseGoods())->toArray();
        $jrow['type_name'] = $row->name;
        $jrow['re_code'] = $row->code;
        $jrow['code'] = $s->getGoodsCode($row->code);
        $jrow['edit_flag'] = 0;
        $jrow['type_id'] = $row->id;
        $jrow['exec_flag'] = 0;
        $jrow['input_list'] = Constant::$formula_input;
        $jrow['input_val'] = '';
        $jrow['formula_list'] = [];
        $jrow['formula_flag'] = 0;
        $jrow['quality_template_id'] = '';
        $jrow['quality_check_data'] = [];
        $this->view->edit_flag = 0;
        $common = new CommonService();
        $this->view->supplierList = $common->getSupplierList();
        $this->view->checkList = $s->getCheckList();;
        $this->view->jsonGoods = json_encode($jrow);
    }

    /**
     * @acl({'link':'purchase:goodstype:list'})
     */
    public function editAction($id = '')
    {
        $s = new GoodsService();

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($id));
            return json_encode($ret);
        }
        $row = $s->selectGoodsById($id);
        $jrow = $row->toArray();
        $jrow['edit_flag'] = 1;
        $jrow['exec_flag'] = $row->warning_flag;
        $jrow['input_list'] = Constant::$formula_input;
        $jrow['input_val'] = '';
        $jrow['formula_list'] = CvtUtil::emptyToArray($jrow['formula_list']);
        $jrow['formula_flag'] = 0;
        $jrow['quality_check_data'] = CvtUtil::emptyToArray($jrow['quality_check_data']);
        $common = new CommonService();
        $this->view->supplierList = $common->getSupplierList();
        $this->view->checkList = $s->getCheckList();;
        $this->view->jsonGoods = json_encode($jrow);
        $this->view->r_id = $row->id;
        $this->view->pick('goods/create');
    }

    /**
     * @acl({'link':'purchase:goodstype:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new GoodsService();
            $ret = new JsonData();
            if ($rs->deleteById($this->request->getPost('id', 'tstring')))
                $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function selAction(){
        $gs = new GoodsTypeService();
        $tree_list = $gs->selectTree();
        $uid = '';
        if (count($tree_list) > 0) {
            $uid = $tree_list[0]['uid'];
        }
        $jrow = [];
        $jrow['detail_data'] = [];
        $jrow['goods_list'] = [];
        $jrow['goods'] = '';
        $jrow['supplier'] = '';
        $jrow['type_uid'] = '';
        $jrow['type_pid'] = '';
        $jrow['select_idx'] = -1;
        $this->view->jsonTree = $tree_list;
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;

        $fs = new FileService();
        $this->view->download_url = $fs->getImagePath().$this->config->upyun->baseDir.$this->config->upyun->goods_template_dir;
    }

    /**
     * @skipacl
     */
    public function selgoodsAction($type = ''){
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new GoodsService();
            $builder = $s->selectGoodsByGoodsType();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

    /**
     * @skipacl
     */
    public function importAction()
    {
        $this->setJsonResponse();
        $s = new GoodsService();
        return json_encode($s->importExcel());
    }

    /**
     * @skipacl
     */
    public function testAction()
    {
        $this->setJsonResponse();
        $s = new GoodsService();
        return json_encode($s->updateSize());
    }
}