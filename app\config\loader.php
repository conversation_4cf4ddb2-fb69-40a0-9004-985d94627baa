<?php

use Phalcon\Loader;

$loader = new Loader();

/**
 * Register Namespaces
 */
$loader->registerNamespaces([
    'Envsan\Common\Service' => APP_PATH . '/common/service/',
    'Envsan\Common\Behavior' => APP_PATH . '/common/behavior/',
    'Envsan\Common\Model' => APP_PATH . '/common/model/',
    'Envsan\Common\Component' => APP_PATH . '/common/component/',
    'Envsan\Common\Controller' => APP_PATH . '/common/controller/',
    'Envsan\Common\Library' => APP_PATH . '/common/library/',
    'Envsan\Common\Base' => APP_PATH . '/common/base/',
    'Envsan\Common\Data' => APP_PATH . '/common/data/',
    'Envsan\Common\Util' => APP_PATH . '/common/util/',
    'Envsan\Common\Util\Excel' => APP_PATH . '/common/util/excel',
]);

/**
 * Register module classes
 */
$loader->registerClasses([
    'Envsan\Modules\Cli\Module' => APP_PATH . '/modules/cli/Module.php',
    'Envsan\Modules\Common\Module' => APP_PATH . '/modules/common/Module.php',
    'Envsan\Modules\Sys\Module' => APP_PATH . '/modules/sys/Module.php',
    'Envsan\Modules\Printing\Module' => APP_PATH . '/modules/printing/Module.php',
    'Envsan\Modules\Work\Module' => APP_PATH . '/modules/work/Module.php',
    'Envsan\Modules\Trade\Module' => APP_PATH . '/modules/trade/Module.php',
    'Envsan\Modules\Purchase\Module' => APP_PATH . '/modules/purchase/Module.php',
    'Envsan\Modules\Equ\Module' => APP_PATH . '/modules/equ/Module.php',
    'Envsan\Modules\Mes\Module' => APP_PATH . '/modules/mes/Module.php',
    'Envsan\Modules\Quality\Module' => APP_PATH . '/modules/quality/Module.php',
    'Envsan\Modules\Screen\Module' => APP_PATH . '/modules/screen/Module.php',
]);

$loader->register();
