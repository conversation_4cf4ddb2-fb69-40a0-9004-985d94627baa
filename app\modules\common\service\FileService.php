<?php

namespace Envsan\Modules\Common\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\UUID;
use Phalcon\Mvc\User\Component;
use Upyun\Upyun;

class FileService extends BaseService
{
    public function getImagePath() {
        return $this->config->upyun->host;
    }

    public function uploadImage($folder_name = '', $owner_id = '') {
        $ret = [];
        $ret['status'] = 'error';
        $ret['message'] = '';

        if (!$this->request->hasFiles()) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }

        $config = new \Upyun\Config($this->config->upyun->bucket, $this->config->upyun->user, $this->config->upyun->password);
        $upyun = new UpYun($config);
        foreach ($this->request->getUploadedFiles() as $file)
        {
            if ($file->getSize() > 10 * 1024 * 1024) {
                $ret['message'] = '上传失败，图片大小不能超过10M';
                return $ret;
            }

            $ext = pathinfo($file->getName(), PATHINFO_EXTENSION);
            if ($ext !== 'jpg' && $ext !== 'jpeg' && $ext !== 'png') {
                $ret['message'] = '上传失败，图片格式只支持jpg, jpeg, png';
                return $ret;
            }

            $path = $this->config->upyun->uploadDir.UUID::make().'.'.$ext;

            try {
                if ($file->moveTo($path)) {
                    $file_handle = fopen($path, 'r');

                    $user = SessionData::user();
                    if (empty($owner_id)) {
                        $owner_id = $user->owner;
                    }

                    $date = date('Y').'/'.date('m').'/'.date('d');
                    if (!empty($folder_name)) {
                        $folder_name = $folder_name.'/';
                    }

                    $filename = $this->config->upyun->baseDir.$owner_id.$folder_name.$date.'/'.time().'.'.$ext;
                    $upyun->write($filename, $file_handle);
                    if (is_resource($file_handle))
                        fclose($file_handle);
                    unlink($path); //删除临时文件

                    $ret['status'] = 'ok';
                    $ret['path'] = $this->getImagePath();
                    $ret['file_name'] = $filename;
                    return $ret;
                }
            } catch (\Exception $e) {
                Logger::error($e->getMessage(), $e->getTraceAsString());
            }
            break;
        }

        $ret['message'] = ErrorHelper::WRONG_INPUT;
        return $ret;
    }

    public function uploadBase64($folder_name, $base64, $ext)
    {
        try {
            $upyun_config = new \Upyun\Config($this->config->upyun->bucket, $this->config->upyun->user, $this->config->upyun->password);
            $upyun = new UpYun($upyun_config);
            $path = $this->config->upyun->uploadDir.UUID::make().'.'.$ext;
            // 统一去掉前缀
            $base64 = preg_replace('/^data:image\/\w+;base64,/', '', $base64);
            if (empty($base64)) {
                throw new \Exception("base64内容为空");
            }

            // 文件写入云服务器
            if (file_put_contents($path, base64_decode($base64)) === false) {
                throw new \Exception("文件写入失败");
            }

            $file_handle = fopen($path, 'r');
            if ($file_handle === false) {
                throw new \Exception("文件打开失败");
            }

            $date = date('Y').'/'.date('m').'/'.date('d');
            if (!empty($folder_name)) {
                $folder_name = $folder_name.'/';
            }
            $filename = $this->config->upyun->baseDir.SessionData::user()->owner.'/'.$folder_name.$date.'/'.time().'.'.$ext;
            $upyun->write($filename, $file_handle);
            if (is_resource($file_handle))
                fclose($file_handle);
            unlink($path); //删除临时文件
            return $this->success($filename);
        } catch (\Exception $e) {
            Logger::error($e->getMessage(), $e->getTraceAsString());
            return $this->error("上传失败");
        }
    }

    public function uploadFile($folder_name = '', $owner_id = '')
    {
        $ret = [];
        $ret['status'] = 'error';
        $ret['message'] = '';

        if (!$this->request->hasFiles()) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }

        $config = new \Upyun\Config($this->config->upyun->bucket, $this->config->upyun->user, $this->config->upyun->password);
        $upyun = new UpYun($config);
        foreach ($this->request->getUploadedFiles() as $file) {
            $file_name = $file->getName();
            $ext = pathinfo($file_name, PATHINFO_EXTENSION);

            if ($file->getSize() > 100 * 1024 * 1024) {
                $ret['message'] = '文件大小不能超过100M';
                return $ret;
            }

            $path = $this->config->upyun->uploadDir.UUID::make().'.'.$ext;
            try {
                if ($file->moveTo($path)) {
                    $file_handle = fopen($path, 'r');

                    $user = SessionData::user();
                    if (empty($owner_id)) {
                        $owner_id = $user->owner;
                    }

                    $date = date('Y').'/'.date('m').'/'.date('d');
                    if (!empty($folder_name)) {
                        $folder_name = $folder_name.'/';
                    }

                    $filename = $this->config->upyun->baseDir.$owner_id.'/'.$folder_name.$date.'/'.time().'.'.$ext;
                    $upyun->write($filename, $file_handle);
                    if (is_resource($file_handle))
                        fclose($file_handle);
                    unlink($path); //删除临时文件

                    $ret['status'] = 'ok';
                    $ret['file_url'] = $filename;
                    $ret['file_name'] = $file_name;
                    return $ret;
                }
            } catch (\Exception $e) {
                Logger::error($e->getMessage(), $e->getTraceAsString());
                $ret['message'] = 'PDF上传失败';
                return $ret;
            }
            break;
        }

        $ret['message'] = ErrorHelper::WRONG_INPUT;
        return $ret;
    }
}