<?php

namespace Envsan\Modules\Mes\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class MesProduct extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_type_code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $customer_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_detail_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $files;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;


    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $node_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $inventory_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $inventory_unit;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $purchase_unit;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $is_batch_managed;

    /**
     *
     * @var double
     * @Column(type="double", length=3, nullable=true)
     */
    public $tax_rate;


    /**
     *
     * @var double
     * @Column(type="double", length=10, nullable=true)
     */
    public $weight;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_product';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProduct[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProduct
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
