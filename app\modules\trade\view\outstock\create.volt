{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div class="page-content">
    <div class="row">
        <div id="app" class="col-sm-12">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">发货信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body">
                            <div id="form_data" class="row">
                                {% if dispatcher.getActionName() == 'edit' %}
                                    <div class="col-sm-2">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>发货单号</label>
                                            <div class="col-sm-8 ">
                                                <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}

                                {% endif %}
                                <div class="col-sm-2">
                                    <div class="form-group" style="margin-bottom: 10px;">
                                        <label class="col-sm-3 control-label" style="padding: 5px;"><span class="required">*</span>客户</label>
                                        <div class="col-sm-9">
                                            <select class="bs-select form-control" name="customer_id" v-model="customer_id" data-live-search="true" data-size="6" required style="height: 30px; padding: 4px 8px; font-size: 12px;">
                                                <option value="">请选择客户</option>
                                                {% for item in customerList %}
                                                    <option value="{{ item.id }}">{{ item.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <div class="form-group" style="margin-bottom: 10px;">
                                        <label class="col-sm-3 control-label" style="padding: 5px;"><span class="required">*</span>日期</label>
                                        <div class="col-sm-9">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker-ext" placeholder="发货日期" name="outstock_date" v-model="outstock_date" required style="height: 30px; padding: 4px 8px; font-size: 12px;"/>
                                                <span class="input-group-addon date-addon" style="padding: 4px 8px;"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-sm-2">
                                    <div class="form-group" style="margin-bottom: 10px;">
                                        <label class="col-sm-3 control-label" style="padding: 5px;">附件</label>
                                        <div class="col-sm-9">
                                            <div v-if="files.length > 0" v-for="item,index in files" style="display: flex; align-items: center; margin-bottom: 3px;">
                                                <a :href="base_path + item.url" target="_blank" style="flex: 1; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; max-width: 120px;">
                                                    <span v-text="item.url_name"></span>
                                                </a>
                                                <a href="javascript:;" title="删除" @click="delFile(index)" style="color: red; margin-left: 5px;">删除</a>
                                            </div>
                                            <button type="button" class="btn btn-outline blue" onclick="uploadPdf()" style="padding: 2px 6px;">
                                                <i class="fa fa-upload"></i> 上传
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group" style="margin-bottom: 10px;">
                                        <label class="col-sm-2 control-label" style="padding: 5px;">备注</label>
                                        <div class="col-sm-10">
                                            <textarea style="resize: none; height: 60px; width: 280px; padding: 4px 8px; font-size: 12px;"  placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" @click="submit" class="btn btn-primary">提交发货</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">到货明细</span>
                    </div>
                    <div class="actions">
                        <div class="actions" style="display: flex;align-items: center">
                            <button type="button" class="btn btn-outline yellow" onclick="choose()">
                                <span>选择</span>
                            </button>
                            <button style="margin-left: 10px" type="button" class="btn btn-outline red" @click="delApply">
                                <span>删除</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>
                                        <a @click="applySelect">
                                            <span v-if="apply_sel == 0"> 全选</span>
                                            <span v-else> 取消</span>
                                        </a>
                                    </th>
                                    <th>存货编码</th>
                                    <th>产品名称</th>
                                    <th>产品规格</th>
                                    <th>主计量</th>
                                    <th>订单数量</th>
                                    <th>报价</th>
                                    <th>无税单价</th>
                                    <th>无税金额</th>
                                    <th>含税单价</th>
                                    <th>税额</th>
                                    <th>税率（%）</th>
                                    <th>价税合计</th>
                                    <th>库存数量</th>
                                    <th>订单号</th>
                                    <th>发货数量</th>
                                    <th>发货备注</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="detail_data.length == 0">
                                    <td colspan="17" style="text-align: center;">没有数据</td>
                                </tr>
                                <tr v-else v-for="row, idx in detail_data">
                                    <td>
                                        <a @click="row.sel == 1 ? row.sel = 0 : row.sel = 1">
                                            <i v-if="row.sel == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                            <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                        </a>
                                    </td>
                                    <td>
                                        <span v-text="row.inventory_code"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.product_name"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.product_code"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.inventory_unit"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.order_cnt"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.quote_price"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.unit_price"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.net_amount"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.unit_price_hs"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.tax_amount"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.tax_rate"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.total_amount"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.stock_cnt"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.order_code"></span>
                                    </td>
                                    <td>
                                        <div class="input-group" style="width: 160px">
                                            <input type="number" class="form-control" :name="'quantity' + index" v-model="row.quantity" placeholder="发货数量" number="true"  maxlength="10" required>
                                            <span class="input-group-addon">${row.inventory_unit}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group" style="width: 160px">
                                            <textarea  class="form-control" style="resize: none;" placeholder="备注" v-model="row.remarks" maxlength="200" ></textarea>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <a href="javascript:;" onclick='addDetail("@row@")'>
        <i class="fa fa-arrow-right"></i>
    </a>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
<script>
    var $table = $('#table');


    $table.bootstrapTable();

    var actHtml = $('#act').html();

    function actionFormatter(v, row, idx) {
        return actHtml.replace(/@row@/g, encodeURI(JSON.stringify(row)));
    }

    function addDetail(row_str) {
        let row = JSON.parse(decodeURI(row_str));
        for(let item of app.detail_data){
            if (item.uid == row.uid){
                toastr.error('不能重复添加');
                return;
            }
        }
        row.quantity = '';
        row.remarks = '';
        app.detail_data.push(row);
    }

    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }},
            apply_sel: 0
        },
        methods: {
            save(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(1);
            },
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(2);
            },
            saveData(type){
                if (this.detail_data.length == 0){
                    toastr.error('请添加明细');
                    return;
                }

                if (!this.validateAllQuantities()) {
                    toastr.error('请添加有效的发货数量');
                    return;
                }
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('trade/outstock/edit/' ~ uid) }}';
                {% else %}
                var url= '{{ url('trade/outstock/create') }}';
                {% endif %}

                showSpin();
                $.post(url, {
                    type:type,
                    customer_id:app.customer_id,
                    outstock_date:app.outstock_date,
                    remarks:app.remarks,
                    files:encodeURI(JSON.stringify(app.files)),
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                    detail : encodeURI(JSON.stringify(app.detail_data)).replace(/\+/g,'%2B')
                }, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },
            delFile:function (index) {
                this.files.splice(index);
            },
            delList: function(idx) {
                this.detail_data.splice(idx, 1);
            },
            openProductSelector: function() {
               if (app.customer_id == '') {
                   toastr.error('必须选择客户后才能选择明细');
                   return;
               }
               ids = [];
               for (let item of app.detail_data) {
                   ids.push(item.id);
               }
               top.window.layer_result2 = '';
               top.layer.open({
                   title: '添加订单明细',
                   type: 2,
                   area: ['100%', '100%'],
                   content: '{{url("trade/outstock/detail/0/")}}' + app.customer_id + '/' + ids,
                   end: function() {
                       if (top.window.layer_result2 == 'ok') {
                           addDetailList(top.window.layer_data2);
                       }
                   }
               });
            },
            applySelect(){
               if (this.apply_sel == 0){
                   this.apply_sel = 1;
               } else {
                   this.apply_sel = 0;
               }
               for (let item of this.detail_data){
                   item.sel = this.apply_sel;
               }
            },
            delApply: function() {
                let detail_data = [], goods_obj = {};
                for (let item of this.detail_data) {
                    if (!goods_obj.hasOwnProperty(item.goods_id)) {
                        goods_obj[item.goods_id] = 0;
                    }

                    if (item.sel == 0) {
                        detail_data.push(item);
                        goods_obj[item.goods_id]++;
                    }
                }
                if (detail_data.length === this.detail_data.length) {
                    toastr.error('请选择删除记录！');
                    return;
                }
                this.detail_data = detail_data;
                app.$forceUpdate();
            },
            validateAllQuantities: function() {
                return this.detail_data.every(item => {
                    return item.quantity && 
                        !isNaN(parseFloat(item.quantity)) && 
                        parseFloat(item.quantity) > 0;
                });
            }
        }
    });

    initUpLoaderPdf('trade_outstock');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    function choose() {
        if (app.customer_id == '') {
            toastr.error('必须选择客户后才能选择明细');
            return;
        }
        ids = [];
        for (let item of app.detail_data) {
            ids.push(item.id);
        }
        top.window.layer_result2 = '';
        top.layer.open({
            title: '添加订单明细',
            type: 2,
            area: ['100%', '100%'],
            content: '{{url("trade/outstock/detail/0/")}}' + app.customer_id + '/' + ids,
            end: function() {
                if (top.window.layer_result2 == 'ok') {
                    addDetailList(top.window.layer_data2);
                }
            }
        });
    }

    function addDetailList(detail_data) {
        paramLoop: for (let i = 0; i < detail_data.length; i++) {
            let record = detail_data[i];
            for (let j = 0; j < app.detail_data.length; j++) {
                if (app.detail_data[j].id == record.id) {
                    continue paramLoop;
                }
            }
            app.detail_data.push(record);
            // app.calculateAmount(apply_row);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}