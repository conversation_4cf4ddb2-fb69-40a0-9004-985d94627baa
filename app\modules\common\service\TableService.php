<?php
namespace Envsan\Modules\Common\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Util\Constant;
use Envsan\Modules\Mes\Model\MesProductField;
use Envsan\Modules\Mes\Model\MesShipField;
use Envsan\Modules\Sys\Model\ExtendColumn;
use Envsan\Modules\Sys\Model\UserColumn;
use Envsan\Modules\Work\Model\WorkGoods;
use Phalcon\Mvc\User\Component;
use PHPExcel_Cell_DataType;
use PHPExcel_Style_Alignment;

class TableService extends Component
{
    public function getFieldData($page_id)
    {
        $page_id = CvtUtil::emptyToInt($page_id);
        $select_field_list = [];
        $uc_row = $this->selectUserColumn(SessionData::user()->id, $page_id);
        if (!empty($uc_row)) {
            $select_field_list = CvtUtil::emptyToArray($uc_row->column_data);
        }
        $pc_cols = [
            'base' => [
                'name' => '基本信息',
                'show' => 1,
                'list' => []
            ]
        ];
        if (array_key_exists($page_id,Constant::$page_extend_column)){
            foreach (Constant::$page_extend_column[$page_id]['header_data'] as $header_item){
                if ($header_item['show'] == 1){
                    if ($header_item['type'] >= 99){
                        if ($header_item['page_id'] == $page_id){
                            $header_item['id'] = 'base';
                        } else {
                            $pc_cols[$header_item['id']] = [
                                'name' => $header_item['name'],
                                'show' => 0,
                                'list' => []
                            ];
                        }
                        $condition = $header_item['condition'];
                        if ($header_item['type'] == 99){
                            $form_data = [];
                            $ext_row = $this->selectExtendColumn($header_item['page_id']);
                            if (!empty($ext_row)){
                                $form_data = CvtUtil::emptyToArray($ext_row->form_data);
                            }
                            foreach ($form_data as $form_item){
                                $pc_cols[$header_item['id']]['list'][] = $this->getColData($form_item['id'],$form_item['type'],$form_item['title'],$header_item['name'],$form_item['unit'],$condition);
                            }
                        } else if ($header_item['type'] == 100){
                            $form_data = $this->selectProductField();
                            $form_data = $form_data->toArray();
                            foreach ($form_data as $form_item){
                                $pc_cols[$header_item['id']]['list'][] =$this->getColData($form_item['uid'],$form_item['type'],$form_item['name'],$header_item['name'],CvtUtil::nullToBlank($form_item['unit']),$condition);
                            }
                        } else if ($header_item['type'] == 102) {
                            $form_data = $this->selectDict($header_item['page_id']);
                            $form_data = $form_data->toArray();
                            foreach ($form_data as $form_item){
                                $pc_cols[$header_item['id']]['list'][] =$this->getColData($form_item['code'],$header_item['data_type'],$form_item['name'],$header_item['name'],'',$condition);
                            }
                        }
                    } else {
                        $pc_cols['base']['list'][] = $header_item;
                    }
                }
            }
        }
        foreach ($pc_cols as $key => &$pc_item)
        {
            foreach ($pc_item['list'] as &$pc_col){
                $pc_col['is_hide'] = 0;
                $pc_col['checked'] = 0;
                $pc_col['is_sum'] = 0;
                foreach ($select_field_list as &$select_field_item) {
                    if ($select_field_item['id'] == $pc_col['id']) {
                        if (array_key_exists('is_sum',$select_field_item)){
                            $pc_col['is_sum'] = $select_field_item['is_sum'];
                        }
                        $select_field_item = $pc_col;
                        $pc_col['checked'] = 1;
                        break;
                    }
                }
            }
        }
        return [
            'field_list' => $pc_cols,
            'select_field_list' => $select_field_list
        ];
    }

    public function saveUserColumnData($page_id)
    {
        $fields= urldecode($this->request->getPost('fields', ['string', 'trim']));
        if (empty($fields)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $fields = CvtUtil::emptyToArray($fields);
        $user = SessionData::user();
        $uc_row = $this->selectUserColumn($user->id, $page_id);
        if ($uc_row == false) {
            $uc_row = new UserColumn();
            $uc_row->user_id = $user->id;
            $uc_row->page_id = $page_id;
            if ($page_id == 999){
                $uc_row->page_code = '审批报表';
            } else {
                $uc_row->page_code = Constant::$page_extend_column[$page_id]['page_name'];
            }

            $uc_row->del_flag = 0;
            $uc_row->owner = $user->owner;
        }
        $uc_row->column_data = CvtUtil::arrayToNull($fields);
        return $uc_row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function saveUserConditionData($page_id)
    {
        $condition= urldecode($this->request->getPost('condition', ['string', 'trim']));
        if (empty($condition)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $condition = CvtUtil::emptyToArray($condition);
        $user = SessionData::user();
        $uc_row = $this->selectUserColumn($user->id, $page_id);
        if (empty($uc_row)) {
            $uc_row = new UserColumn();
            $uc_row->user_id = $user->id;
            $uc_row->page_id = $page_id;
            if ($page_id == 999){
                $uc_row->page_code = '审批报表';
            } else {
                $uc_row->page_code = Constant::$page_extend_column[$page_id]['page_name'];
            }
            $uc_row->del_flag = 0;
            $uc_row->owner = $user->owner;
        }
        foreach ($condition as &$item){
            $item['val'] = '';
            $item['val_end'] = '';
        }
        $uc_row->condition_data = CvtUtil::arrayToNull($condition);
        return $uc_row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getConditionData($page_id){
        $uc_row = $this->selectUserColumn(SessionData::user()->id, $page_id);
        if (!empty($uc_row)) {
            return CvtUtil::emptyToArray($uc_row->condition_data);
        }
        return [];
    }

    private function selectUserColumn($user_id, $page_id)
    {
        return UserColumn::findFirst([
            'del_flag = 0 and user_id = ?1 and page_id = ?2',
            'bind' => [
                1 => $user_id,
                2 => $page_id
            ]
        ]);
    }

    public function selectExtendColumn($page_id){
        return ExtendColumn::findFirst([
            'del_flag = 0 and page_id = ?1',
            'bind' => [
                1 => $page_id
            ]
        ]);
    }

    private function selectProductField(){
        return MesProductField::find([
            'del_flag = 0 and owner = ?1',
            'bind' => [
                1 => SessionData::ownerId()
            ]
        ]);
    }

    public function selectDict($key){
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.code, a.name')
            ->addFrom('Envsan\Modules\Sys\Model\SysDictDetail', 'a')
            ->innerJoin('Envsan\Modules\Sys\Model\SysDict', 'a.dict_id = b.id and b.del_flag = 0', 'b')
            ->where('a.del_flag = 0 and b.code = ?1', [1 => $key])
            ->orderBy('a.sort');
        return $builder->getQuery()->execute();
    }

    private function getColData($id,$type,$title,$name,$unit,$condition){
        if (!empty($name)){
            $name = '('.$name.')';
        }
        return  [
            'id' => $id,
            'type' => $type,
            'name' => $title.$name,
            'unit' => $unit,
            'show' => 1,
            'condition' => empty($condition) ? '' : "json_extract($condition,'$.$id')"
        ];
    }

    public function getTableData($page_id,$page_rows,$builder){
        $field_list = [];
        $uc_row = $this->selectUserColumn(SessionData::user()->id, $page_id);
        if (!empty($uc_row)) {
            $field_list = CvtUtil::emptyToArray($uc_row->column_data);
        }
        $show_cnt = 0;
        $json_key_list = [];
        if (array_key_exists($page_id,Constant::$page_extend_column)){
            foreach (Constant::$page_extend_column[$page_id]['header_data'] as $header_item){
                if ($header_item['show'] == 0){
                    $field_list[] = $header_item;
                } else {
                    if ((empty($uc_row) || empty($uc_row->column_data)) && $header_item['type'] < 99){
                        $field_list[] = $header_item;
                        $show_cnt++;
                        if ($show_cnt > 8) {
                            break;
                        }
                    }
                }
                if ($header_item['type'] >= 99){
                    $json_key_list[] = $header_item['id'];
                }
            }
        }
        $header_data = [];
        $sum_data = [];
        $type_map = [];
        $data_values = [];
        foreach ($field_list as $field_item)
        {
            $header_data[$field_item['id']] = '';
            $type_map[$field_item['id']] = $field_item['type'];
            if ($field_item['type'] == 2){
                if (array_key_exists('is_sum',$field_item)){
                    if ($field_item['is_sum'] == 1){
                        $sum_data[] = $field_item;
                    }
                }
            } else if ($field_item['type'] == 9){
                if (array_key_exists('data_values',$field_item)){
                    $data_values[$field_item['id']] = $field_item['data_values'];
                }
            }
        }
        if (count($sum_data) > 0){
            $sum_data = $this->getSumData($sum_data,$builder);
        }
        $header_data = json_encode($header_data,JSON_UNESCAPED_UNICODE);
        if (!is_array($page_rows)) {
            $page_rows = $page_rows->toArray();
        }
        $data_list = [];
        foreach ($page_rows as $page_row){
            foreach ($json_key_list as $json_key){
                if (array_key_exists($json_key,$page_row)){
                    $ext_val = CvtUtil::emptyToArray($page_row[$json_key]);
                    foreach ($ext_val as $k => $v) {
                        $page_row[$k] = $v;
                    }
                }
            }
            $clone_header_data = json_decode($header_data,true);
            foreach ($clone_header_data as $h_key => $h_val){
                if (array_key_exists($h_key,$page_row)){
                    if ($type_map[$h_key] == 2) {
                        // 王让改成所有数字类型都省略小数点后的0，杨同意
                        $clone_header_data[$h_key] =CvtUtil::emptyToDouble($page_row[$h_key]);
                    } else if ($type_map[$h_key] == 9){
                        if (array_key_exists($h_key,$data_values)){
                            if ($page_row[$h_key] == null || $page_row[$h_key] == ''){
                                $clone_header_data[$h_key] =[
                                    'c' => '',
                                    'v' => ''
                                ];
                            } else {
                                $clone_header_data[$h_key] = $data_values[$h_key][$page_row[$h_key]];
                            }
                        } else {
                            $clone_header_data[$h_key] =[
                                'c' => '',
                                'v' => $page_row[$h_key]
                            ];
                        }
                    } else {
                        $clone_header_data[$h_key] = $page_row[$h_key];
                    }
                }
            }
            $data_list[] = $clone_header_data;
        }
        return [
            'data_header' => $field_list,
            'data_rows' => $data_list,
            'sum_data' => array_values($sum_data)
        ];
    }

    private function getSumData($sum_list,$builder){
        $columns = '';
        $sum_data = [];
        foreach ($sum_list as $sum_item){
            if (!empty($sum_item['condition'])){
                $condition = str_replace('&#34;', "\"", $sum_item['condition']);
                $condition = str_replace('&#39;', "'", $condition);
                $sum_data[$sum_item['id']] = [
                    'name' => $sum_item['name'],
                    'unit' => $sum_item['unit'],
                    'value' => ''
                ];
                $columns .= 'sum(' . $condition . ') as ' . $sum_item['id'] . ',';
            }
        }
        if (empty($columns)){
            return $sum_data;
        }
        $columns = rtrim($columns,',');
        $builder->columns($columns);
        $rows = $builder->getQuery()->execute()->toArray();
        if (count($rows) == 0){
            return $sum_data;
        }
        $row = $rows[0];
        foreach ($row as $key => $value){
            $sum_data[$key]['value'] = CvtUtil::emptyToDouble($value);
        }
        return $sum_data;
    }

    public function getFormData($page_id){
        $row = ExtendColumn::findFirst(['del_flag = 0 and page_id = ?1 and owner = ?2','bind'=>[1 => $page_id, 2 => SessionData::ownerId()]]);
        if (empty($row)){
            return [];
        }
        $form_list = CvtUtil::emptyToArray($row->form_data);
        $form_data = [];
        foreach ($form_list as $form_item){
            $form_data[$form_item['id']] = $form_item;
        }
        return $form_data;
    }

    public function margeFormData($page_id,$ext_data){
        $row = ExtendColumn::findFirst(['del_flag = 0 and page_id = ?1 and owner = ?2','bind'=>[1 => $page_id, 2 => SessionData::ownerId()]]);
        if (empty($row)){
            return [];
        }
        $form_list = CvtUtil::emptyToArray($row->form_data);
        $form_data = [];
        foreach ($form_list as $form_item) {
            if (array_key_exists($form_item['id'],$ext_data)) {
                $form_item['value'] = $ext_data[$form_item['id']]['value'];
                $form_item['values'] = $ext_data[$form_item['id']]['values'];
            }
            $form_data[$form_item['id']] = $form_item;
        }
        return $form_data;
    }

    public function getFormDataValue($form_data){
        $form_data_val = [];
        foreach ($form_data as $key => $form_item){
            if ($form_item['type'] == 4) {
                $val = '';
                foreach ($form_item['values'] as $value){
                  if (!empty($value)){
                      $val .= $value . ',';
                  }
                }
                $form_data_val[$form_item['id']] = rtrim($val, ',');
            } else if ($form_item['type'] == 2) {
                if (empty($form_item['value'])) {
                    $form_data_val[$form_item['id']] = '';
                } else {
                    $form_data_val[$form_item['id']] = doubleval($form_item['value']);
                }
            } else {
                $form_data_val[$form_item['id']] = $form_item['value'];
            }
        }
        return $form_data_val;
    }

    public function conditionExtend($builder){
        $conditions = $this->request->get('conditions', 'tstring');
        if (!empty($conditions)) {
            $conditions = rawurldecode($conditions);
            $conditions = str_replace('&#34;', "\"", $conditions);
            $conditions = str_replace('&#39;', "'", $conditions);
            $conditions = str_replace('＜', "<", $conditions);
            $conditions = CvtUtil::emptyToArray($conditions);
            foreach ($conditions as $idx => $condition)
            {
                $col_name = $condition['condition'];
                if ($condition['type'] == 6){
                    if (!empty($condition['val'])) {
                        $param_no = 200 + $idx;
                        $builder->andWhere($col_name .' >= ?'.$param_no, [$param_no => $condition['val']]);
                    }
                    if (!empty($condition['val_end'])) {
                        $param_no = 300 + $idx;
                        $builder->andWhere($col_name .' <= ?'.$param_no, [$param_no => $condition['val_end']]);
                    }
                } else {
                    if (!empty($condition['val'])) {
                        if ($condition['search_type'] == 'like') {
                            $condition['val'] = '%'.$condition['val'].'%';
                            $builder->andWhere($col_name.' like \''.$condition['val'].'\'');
                        } else if (CheckUtil::isDecimal($condition['val'])) {
                            $builder->andWhere($col_name.' '.$condition['search_type'].' '.$condition['val']);
                        } else {
                            $param_no = 100 + $idx;
                            $builder->andWhere($col_name.' '.$condition['search_type'].' ?'.$param_no, [$param_no => $condition['val']]);
                        }
                    }
                }
            }
        }
        return $builder;
    }

    public function exportExcel($page_id,$builder){
        $page_rows = $builder->getQuery()->execute();
        $ret = $this->getTableData($page_id, $page_rows, $builder);
        $file_title = Constant::$page_extend_column[$page_id]['page_name'];
        $objExcel = new \PHPExcel();
        //设置属性
        $objExcel->getProperties()->setCreator(SessionData::owner()->company);
        $objExcel->getProperties()->setLastModifiedBy(SessionData::owner()->company);
        $objExcel->getProperties()->setTitle($file_title);
        $objExcel->setActiveSheetIndex();
        $objActSheet = $objExcel->getActiveSheet();
        $objActSheet->setTitle($file_title);
        $data_header = $ret['data_header'];
        $column_data = [];
        foreach ($data_header as $header_item){
            if ($header_item['show'] == 1){
                $column_data[] = $header_item;
            }
        }
        $rows = $ret['data_rows'];
        if (count($column_data) == 0) {
            die('未配置该页面展示信息');
        } else if (count($column_data) > 52) {
            die('展示信息过多');
        }
        $letters = $this->getLetters(count($column_data));
        $last_no = $letters[count($letters) - 1];
        $objActSheet->mergeCells('A1'.':'.$last_no.'1');
        $objActSheet->setCellValue('A1', $file_title);
        $objActSheet->getStyle()->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objActSheet->getStyle()->getFont()->setBold(true);
        foreach ($letters as $idx => $letter)
        {
            $unit = '';
            if (!empty($column_data[$idx]['unit'])){
                $unit = '(' . $column_data[$idx]['unit'] . ')';
            }
            $objActSheet->setCellValue($letter.'2', $column_data[$idx]['name'].$unit);
            $objActSheet->getColumnDimension($letter)->setWidth(20);
        }
        $objExcel->getActiveSheet()->getStyle('a1:'.$last_no.'2')->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
        $objExcel->getActiveSheet()->getStyle('a1:'.$last_no.'2')->getFont()->setBold(true);
        $val = '';
        $row_no = 3;
        foreach($rows as $row) {
            $row_no_start = $row_no;
            foreach ($letters as $idx => $letter)
            {
                $cell_no = $letter.$row_no;
                if (array_key_exists($column_data[$idx]['id'],$row)){
                    if ($column_data[$idx]['type'] == 2) {
                        $objActSheet->setCellValue($cell_no, $row[$column_data[$idx]['id']]);
                    } else {
                        $objExcel->getActiveSheet()->setCellValueExplicit($cell_no, $row[$column_data[$idx]['id']], PHPExcel_Cell_DataType::TYPE_STRING);
                        $objExcel->getActiveSheet()->getStyle($cell_no)->getNumberFormat()->setFormatCode("@");
                    }
                } else {
                    $objActSheet->setCellValue($cell_no, '');
                }
            }
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getAlignment()->setWrapText(true);
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
            $row_no++;
        }
        $objActSheet->getStyle('A1:'.$last_no.($row_no - 1))->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
        //清除缓冲区,避免乱码
        ob_end_clean();
        // 设置页方向和规模
        $objExcel->getActiveSheet()->getPageSetup()->setOrientation(\PHPExcel_Worksheet_PageSetup::ORIENTATION_PORTRAIT);
        $objExcel->getActiveSheet()->getPageSetup()->setPaperSize(\PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
        $objExcel->setActiveSheetIndex(0);

        $timestamp = time();
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$file_title.'_'.$timestamp.'.xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objExcel, 'Excel2007');
        $objWriter->save('php://output');
        exit;
    }

    public function getLetters($col_cnt)
    {
        $first_code = ord('A');
        $letters = [];
        $col_cnt_2 = 0;
        if ($col_cnt > 26) {
            $col_cnt_2 = $col_cnt - 26;
            $col_cnt = 26;
        }
        for ($i = 0; $i < $col_cnt; $i++)
        {
            array_push($letters, chr($first_code + $i));
        }
        if ($col_cnt_2 > 0) {
            for ($i = 0; $i < $col_cnt_2; $i++)
            {
                array_push($letters, 'A'.chr($first_code + $i));
            }
        }
        return $letters;
    }
}