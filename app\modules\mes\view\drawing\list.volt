{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered" style="margin-bottom: 0;padding-bottom: 0">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-6 col-lg-6">
                        <h3 class="page-title">图纸管理</h3>
                    </div>
                    <div class="col-md-6 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        {{ partial('table') }}
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {

        },
        mounted:function (){
            let btn_list = [];
            btn_list.push({ type: 1, name: '版本管理' });
            btn_list.push({ type: 2, name: '暂停使用', fn: (data) => { return data.status == 20 } });
            btn_list.push({ type: 3, name: '恢复使用', fn: (data) => { return data.status == 30 } });
            btn_list.push({ type: 4, name: '发布版本', fn: (data) => { return data.status == 40 } });
            app_ext_table.init({{ page_id }}, btn_list, '{{ url('mes/drawing/list/json') }}');
        },
        methods: {
            search: function() {
                app_ext_table.search();
            },
            reset: function() {
                app_ext_table.reset();
            },
            excel:function (){
                app_ext_table.excel("{{ url('mes/drawing/export') }}");
            },
            getParams: function() {
                return {}
            },
            dataView:function (type,row){
                if (type == 1) {
                    version(row.uid);
                } else if (type == 2 || type == 3) {
                    changeStatus(row.uid, type);
                } else if (type == 4) {
                    release(row.uid);
                }
            }
        }
    });

    function version(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '版本管理',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('mes/drawing/version/') }}' + uid,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function changeStatus(uid, type) {
        if (type == 2) {
            var confirm_msg = '确认暂停使用吗?';
        } else {
            var confirm_msg = '确认恢复使用吗?';
        }
        var dlg = top.layer.confirm(confirm_msg, function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('mes/drawing/status')}}", {uid: uid, status_type: type}, function(rs) {
                closeSpin();
                if (rs.status == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
                else {
                    toastr.error('操作失败!');
                }
            })
        });
    }

    function release(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '发布版本',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('mes/drawing/release/') }}' + uid,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }
</script>