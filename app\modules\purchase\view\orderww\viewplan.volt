{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}

<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-goods" style="padding-bottom: 5px;padding-top: 5px;margin-bottom: 10px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-basket-loaded font-yellow"></i>
                        <span class="caption-subject font-yellow bold">外委计划信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="padding-top: 10px;padding-bottom: 10px;">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">外委计划单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="entrust_code" v-model="entrust_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">生产批次号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="notice_code" v-model="notice_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品名称</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_name" v-model="product_name" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品规格型号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_code" v-model="product_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">外委工序</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="bom_name" v-model="bom_name" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" class="form-control" v-model="remarks" maxlength="200" rows="3" readonly></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-sm-12">
            <div class="portlet light portlet-detail" style="margin-bottom: 0;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-handbag font-blue"></i>
                        <span class="caption-subject font-blue bold">外委计划明细</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-bordered table-big" >
                                <thead>
                                <tr>
                                    <th>物料编码</th>
                                    <th>物料名称</th>
                                    <th>规格型号</th>
                                    <th>外委数量</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="ww_list.length == 0">
                                    <td colspan="18" style="text-align: center;">没有找到匹配的记录</td>
                                </tr>
                                <tr v-else v-for="row, index in ww_list">
                                    <td v-text="row.goods_code"></td>
                                    <td v-text="row.goods_name"></td>
                                    <td v-text="row.goods_model"></td>
                                    <td v-text="row.goods_quantity"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

{{ partial('check') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }}
        },
    });

    function initSize() {
        let portlet_height = $(window).height() - 35 - 27 - 8;

        $(".portlet-detail .zh-table-box-content").height(portlet_height
            - $(".portlet-detail .portlet-title").outerHeight(true));

    }

    $(function () {
        initSize();
    });
</script>
<style>
    .zh-table-box table thead {
        z-index: 3;
    }
</style>