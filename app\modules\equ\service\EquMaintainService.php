<?php
namespace Envsan\Modules\Equ\Service;

use Dompdf\Dompdf;
use Dompdf\Options;
use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Equ\Model\EquCheckForm;
use Envsan\Modules\Equ\Model\EquItem;
use Envsan\Modules\Equ\Model\EquItemMaintainLogs;
use Envsan\Modules\Equ\Util\Constant;
use Envsan\Modules\Sys\Model\User;
use Phalcon\Mvc\User\Component;

class EquMaintainService extends Component
{
    public function searchAll()
    {
        $code = $this->request->get('code', 'tstring');
        $date_begin = $this->request->get('date_begin', 'tstring');
        $date_end = $this->request->get('date_end', 'tstring');
        $date_begin_b = $this->request->get('date_begin_b', 'tstring');
        $date_end_b = $this->request->get('date_end_b', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.status,
                a.maintain_date,
                a.plan_date,
                a.remarks,
                a.maintain_by,
                e.code,
                c.name as check_name,
                u.real_name as crete_user
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItemMaintainLogs', 'a')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItem', 'a.item_id = e.id','e')
            ->leftJoin('Envsan\Modules\Equ\Model\EquCheckForm', 'a.form_id = c.id','c')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 'a.maintain_by = u.id','u')
            ->where('a.del_flag = 0 and a.owner = ?1',[1 => SessionData::ownerId()])
            ->orderBy('a.plan_date desc');

        if (!CheckUtil::is_empty($code)) {
            $builder->andWhere("e.code like ?2", [2 => "%$code%"]);
        }
        if (!CheckUtil::is_empty($date_begin)) {
            $builder->andWhere("a.plan_date >= ?3", [3 => $date_begin]);
        }
        if (!CheckUtil::is_empty($date_end)) {
            $builder->andWhere("a.plan_date <= ?4", [4 => $date_end]);
        }
        if (!CheckUtil::is_empty($date_begin_b)) {
            $builder->andWhere("a.maintain_date >= ?5", [5 => $date_begin_b]);
        }
        if (!CheckUtil::is_empty($date_end_b)) {
            $builder->andWhere("a.maintain_date <= ?6", [6 => $date_end_b]);
        }
        return $builder;
    }

    public function setDetail($rows)
    {
        foreach ($rows as &$temp)
        {
            $temp['status_name'] = Constant::$maintain_logs_status[$temp['status']];
        }
        return $rows;
    }
    public function create()
    {
        $row = new EquItemMaintainLogs();
        return $this->build('create', $row);
    }
    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $plan_date = trim($this->request->getPost('plan_date', 'string'));
        $item_id = trim($this->request->getPost('item_id', 'string'));
        $form_id = trim($this->request->getPost('form_id', 'string'));
        $form = EquCheckForm::findFirst(['id=?1', 'bind'=>[1=>$form_id]]);

        if (empty($plan_date) || empty($item_id) || empty($form_id))
            return ErrorHelper::WRONG_INPUT;

        $user = SessionData::user();
        $now = DateUtil::now();
        $row->plan_date = $plan_date;
        $row->status = 10;
        $row->item_id = $item_id;
        $row->form_id = $form_id;
        $row->form_data = $form->form_data;
        $row->update_date = $now;
        $row->update_by = $user->id;

        if ($act == 'create') {
            $row->create_by = $user->id;
            $row->create_date = $now;
            $row->group_id = $user->group_id;
            $row->owner = $user->owner;
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return EquItemMaintainLogs::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function delete(){
        $id = $this->request->getPost('id');
        if (empty($id)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectById($id);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function exportListExcel()
    {
        $file_title = '保养计划';
        $objExcel = new \PHPExcel();
        //设置属性
        $objExcel->getProperties()->setCreator(SessionData::owner()->company);
        $objExcel->getProperties()->setLastModifiedBy(SessionData::owner()->company);
        $objExcel->getProperties()->setTitle($file_title);
        $objExcel->setActiveSheetIndex();
        $objActSheet = $objExcel->getActiveSheet();
        $objActSheet->setTitle($file_title);

        $headers = [
            ['name' => '设备', 'col' => 'code', 'width' => 8],
            ['name' => '计划日期', 'col' => 'plan_date', 'width' => 12],
            ['name' => '模板', 'col' => 'check_name', 'width' => 33],
            ['name' => '保养日期', 'col' => 'maintain_date', 'width' => 12],
            ['name' => '保养人', 'col' => 'crete_user', 'width' => 10],
            ['name' => '状态', 'col' => 'status_name', 'width' => 10],
            ['name' => '备注', 'col' => 'remarks', 'width' => 35],
        ];

        $ts = new TableService();
        $letters = $ts->getLetters(count($headers));
        $last_no = $letters[count($letters) - 1];
        foreach ($letters as $idx => $letter)
        {
            $objActSheet->setCellValue($letter.'1', $headers[$idx]['name']);
            $objActSheet->getColumnDimension($letter)->setWidth($headers[$idx]['width']);
        }

        $objActSheet->getStyle('A1:'.$last_no.'1')->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
        $objActSheet->getStyle('A1:'.$last_no.'1')->getFont()->setBold(true);

        $rows = $this->searchAll()->getQuery()->execute()->toArray();
        $rows = json_decode(json_encode($this->setDetail($rows)));
        $val = '';
        $row_no = 2;
        foreach($rows as $row)
        {
            foreach ($letters as $idx => $letter)
            {
                $cell_no = $letter.$row_no;
                $col = $headers[$idx]['col'];
                $val_str = '$row->'.$col;
                eval("\$val = \"$val_str\";");

                $objActSheet->setCellValue($cell_no, $val);
            }
            $row_no++;
        }

        $objActSheet->getStyle('A2:'.$last_no.($row_no - 1))->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);

        //清除缓冲区,避免乱码
        ob_end_clean();

        // 设置页方向和规模
        $objActSheet->getPageSetup()->setOrientation(\PHPExcel_Worksheet_PageSetup::ORIENTATION_PORTRAIT);
        $objActSheet->getPageSetup()->setPaperSize(\PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
        $objActSheet->setSelectedCell();
        $objExcel->setActiveSheetIndex();

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$file_title.'.xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objExcel, 'Excel2007');
        $objWriter->save('php://output');
        exit;
    }

    public function exportExcel($id)
    {
        $row = $this->selectById($id);
        if (empty($row) || empty($row->form_data))
            die(ErrorHelper::WRONG_ID);

        $equ_code = '';
        $equ_row = EquItem::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $row->item_id]]);
        if (!empty($equ_row)) {
            $equ_code = $equ_row->code;
        }

        $maintain_name = '';
        if (!empty($row->maintain_by)) {
            $maintain_user = User::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $row->maintain_by]]);
            if (!empty($maintain_user)) {
                $maintain_name = $maintain_user->real_name;
            }
        }

        $report_date = empty($row->maintain_date) ? $row->plan_date : $row->maintain_date;
        $file_title = '保养数据_'.date('Ymd', strtotime($report_date));

        try {
            $objExcel = new \PHPExcel();
            //设置属性
            $objExcel->getProperties()->setCreator(SessionData::owner()->company);
            $objExcel->getProperties()->setLastModifiedBy(SessionData::owner()->company);
            $objExcel->getProperties()->setTitle($file_title);
            $objExcel->setActiveSheetIndex();
            $objActSheet = $objExcel->getActiveSheet();
            $objActSheet->setTitle($file_title);

            $objActSheet->getColumnDimension()->setWidth(20);
            $objActSheet->getColumnDimension('B')->setWidth(30);
            $objActSheet->getColumnDimension('C')->setWidth(60);

            $a1 = '设备：'.$equ_code.'　　　　';
            $a1 .= '计划日期：'.CvtUtil::nullToBlank($row->plan_date).'　　　　';
            $a1 .= '实际日期：'.CvtUtil::nullToBlank($row->maintain_date).'　　　　';
            $a1 .= '保养人：'.CvtUtil::nullToBlank($maintain_name);
            $objActSheet->mergeCells('A1:E1');
            $objActSheet->setCellValue('A1', $a1);

            $objActSheet->mergeCells('A2:E2');
            $objActSheet->setCellValue('A2', '备注：'.CvtUtil::nullToBlank($row->remarks));

            $row_no = 3;
            $list = CvtUtil::emptyToArray($row->form_data);
            foreach ($list as $item)
            {
                $objExcel->getActiveSheet()->setCellValue('A'.$row_no, $item['name']);
                $objExcel->getActiveSheet()->setCellValue('B'.$row_no, $item['label1_value']);
                $objExcel->getActiveSheet()->setCellValue('C'.$row_no, $item['label2_value']);
                $objExcel->getActiveSheet()->setCellValue('D'.$row_no, $item['label3_value']);
                $objExcel->getActiveSheet()->setCellValue('E'.$row_no, $item['value']);
                $row_no++;
            }

            $objActSheet->getStyle('A1:E'.($row_no - 1))->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);

            // 清除缓冲区,避免乱码
            ob_end_clean();

            // 设置页方向和规模
            $objActSheet->getPageSetup()->setOrientation(\PHPExcel_Worksheet_PageSetup::ORIENTATION_PORTRAIT);
            $objActSheet->getPageSetup()->setPaperSize(\PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
            $objActSheet->setSelectedCell();
            $objExcel->setActiveSheetIndex();

            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="'.$file_title.'.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter = \PHPExcel_IOFactory::createWriter($objExcel, 'Excel2007');
            $objWriter->save('php://output');
            exit;
        } catch (\Exception $e) {
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
    }
}