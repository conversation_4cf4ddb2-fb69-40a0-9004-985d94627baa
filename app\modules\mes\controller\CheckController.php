<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Service\CheckService;
use Envsan\Modules\Mes\Service\NoticeService;

/**
 * @name("生产检验")
 */
class CheckController extends SuperController
{
    private $page_id = 8;

    /**
     * @name("列表")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new CheckService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @skipacl
     * @name("查看")
     */
    function viewAction($uid)
    {
        $s = new CheckService();
        $rows = $s->view($uid);
        if (count($rows) == 0)
            die(ErrorHelper::WRONG_ID);
        $jrow = $rows[0];
        $jrow['check_data'] = CvtUtil::emptyToArray($jrow['check_data']);
        $this->view->jsonData = json_encode($jrow,JSON_UNESCAPED_UNICODE);
    }

    /**
     * @skipacl
     * @name("导出")
     */
    public function exportAction()
    {
        $s = new CheckService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }
}