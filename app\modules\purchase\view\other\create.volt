{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div style="display: flex;">
        <div style="width: 45%;">
            <div class="portlet light portlet-goods" style="margin-bottom: 0;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">物资信息</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="tree-box">
                                <div id="tree"></div>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="search-page">
                                <div class="row table-search-bar">
                                    <div class="col-sm-6">
                                        <div class="input-group">
                                            <span class="input-group-addon">物料</span>
                                            <input type="text" class="form-control" name="goods" v-model="goods">
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="input-group">
                                            <span class="input-group-addon">供应商</span>
                                            <input type="text" class="form-control" name="supplier" v-model="supplier">
                                        </div>
                                    </div>
                                </div>
                                <div class="zh-table-box">
                                    <div class="zh-table-box-content">
                                        <table class="table table-bordered table-big">
                                            <thead>
                                            <tr>
                                                <th>编码/名称</th>
                                                <th style="width: 190px;">规格/型号/供应商</th>
                                                <th>库存数量</th>
                                                <th>选择</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr v-if="goods_list.length == 0">
                                                <td colspan="4" style="text-align: center;">没有数据</td>
                                            </tr>
                                            <tr v-for="row, index in goods_list" v-if="row.show == 1">
                                                <td>
                                                    <div v-text="row.code"></div>
                                                    <div v-text="row.name"></div>
                                                </td>
                                                <td>
                                                    <div v-text="row.spec" style="word-break: break-all;"></div>
                                                    <div v-text="row.model"></div>
                                                    <div v-text="row.supplier_name"></div>
                                                </td>
                                                <td><span v-text="row.stock_cnt + ' ' + row.unit"></span></td>
                                                <td>
                                                    <a href="javascript:;" @click='addList(row)'>
                                                        <i class="fa fa-arrow-right"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="flex: 1;padding-left: 20px;">
            <div class="portlet light portlet-detail" style="margin-bottom: 0;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <div class="caption-subject font-green bold" style="white-space: nowrap;">
                            <span>入库信息</span>
                            <span v-if="code" v-text="'(' + code + ')'"></span>
                        </div>
                    </div>
                    <div class="actions" style="display: flex;align-items: center;justify-content: flex-end">
                        <button type="button" class="btn blue" @click="preSubmit(1, $event)" style="margin-right: 20px;"><i class="fa fa-save"></i> 保存</button>
                        <button type="button" class="btn green" @click="preSubmit(2, $event)"><i class="fa fa-check"></i> 提交</button>
                    </div>
                </div>
                <div class="portlet-body" style="overflow-y: auto;padding-right: 15px;">
                    <form id="form" class="form-horizontal" autocomplete="off">
                        <div class="table-search-bar row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required">*</span>入库日期</label>
                                    <div class="col-sm-8">
                                        <div class="input-group date dtpicker">
                                            <input type="text" class="form-control" name="other_date" v-model="other_date" required>
                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{ partial('form') }}
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">附件</label>
                                    <div class="col-sm-8" style="display: flex;flex-direction: column">
                                        <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                            <div>
                                                <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                    <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                </a>
                                            </div>
                                            <div>
                                                <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                    删除
                                                </a>
                                            </div>
                                        </div>
                                        <div class="btn btn-outline blue" style="width: 80px;" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">备注</label>
                                    <div class="col-sm-8">
                                        <textarea class="form-control" name="remark" v-model="remarks" rows="3" maxlength="200" style="resize: none;"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>编码/名称</th>
                                    <th>规格/型号</th>
                                    <th style="width: 200px;">数量/单价</th>
                                    <th>总价</th>
                                    <th style="width: 210px;">供应商</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="detail_data.length == 0">
                                    <td colspan="7" style="text-align: center;">没有数据</td>
                                </tr>
                                <tr v-for="row, index in detail_data">
                                    <td>
                                        <div v-text="row.code"></div>
                                        <div v-text="row.name"></div>
                                    </td>
                                    <td>
                                        <div v-text="row.spec"></div>
                                        <div v-text="row.model"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" style="margin-bottom: 5px;">
                                            <input type="number" class="form-control" :name="'quantity_' + index" v-model="row.quantity" @keyup="sumMoney" placeholder="数量" maxlength="10">
                                            <span class="input-group-addon" v-text="row.unit"></span>
                                        </div>
                                        <div class="input-group">
                                            <input type="number" class="form-control" :name="'price_' + index" v-model="row.price" @keyup="sumMoney" placeholder="单价" maxlength="10">
                                            <span class="input-group-addon" v-text="'元/' + row.unit"></span>
                                        </div>
                                    </td>
                                    <td v-text="row.total_money"></td>
                                    <td>
                                        <select class="bs-select-detail form-control" :class="'bs-select-' + index" :name="'supplier_id@' + index" v-model="row.supplier_id" data-size="8" data-live-search="true">
                                            <option value="">请选择</option>
                                            <option v-for="item in supplier_list" :value="item.id" v-text="item.name"></option>
                                        </select>
                                    </td>
                                    <td>
                                        <a href="javascript:;" @click='delList(index)' style="color: red;font-size: 20px">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonOther }},
        methods: {
            preSubmit(type, e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                if (this.detail_data.length == 0){
                    toastr.error('请添加明细');
                    return;
                }

                for (let i = 0; i < this.detail_data.length; i++) {
                    let row = this.detail_data[i];
                    if (!isDecimal(row.quantity) || parseFloat(row.quantity) <= 0) {
                        toastr.error('请输入有效的数量，大于0');
                        return;
                    }

                    if (!isDecimal(row.price)) {
                        toastr.error('请输入有效的单价');
                        return;
                    }
                }

                let param = {
                    type: type,
                    other_date: app.other_date,
                    remarks: app.remarks,
                    files: encodeURI(JSON.stringify(app.files)),
                    ext_data: encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                    detail: encodeURI(JSON.stringify(app.detail_data)).replace(/\+/g,'%2B')
                };

                if (type == 2) {
                    let dlg = top.layer.confirm('确认提交吗？', function() {
                        top.layer.close(dlg);
                        app.doSubmit(param);
                    });
                } else {
                    this.doSubmit(param);
                }
            },
            doSubmit(param) {
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('purchase/other/edit/' ~ uid) }}';
                {% else %}
                var url= '{{ url('purchase/other/create') }}';
                {% endif %}

                showSpin();
                $.post(url, param, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },
            delFile:function (index) {
                this.files.splice(index);
            },
            addList(goods) {
                for(let item of this.detail_data) {
                    if (item.id == goods.id){
                        toastr.error('已存在，不能重复添加');
                        return;
                    }
                }

                let supplier_id = '';
                if (goods.supplier_name) {
                    for (let item of this.supplier_list) {
                        if (item.name == goods.supplier_name){
                            supplier_id = item.id;
                            break;
                        }
                    }
                }

                this.detail_data.push({...goods, quantity: '', price: '', total_money: '', supplier_id: supplier_id});
                this.$nextTick(function() {
                    $(".bs-select-" + (this.detail_data.length - 1)).selectpicker(bsSelectOption);
                });
            },
            delList: function(idx) {
                this.detail_data.splice(idx, 1);
                this.$nextTick(function() {
                    $(".bs-select-detail").selectpicker('refresh');
                });
            },
            goodsShow() {
                for (let item of this.goods_list) {
                    item.show = 1;
                    if (this.supplier != '') {
                        if (item.supplier_name.indexOf(this.supplier) == -1){
                            item.show = 0;
                        }
                    }

                    if (this.goods != '') {
                        let name = item.code + item.name + (item.spec || '') + (item.model || '');
                        let conditions = this.goods.split(' ');
                        let show_flag = 1;
                        for (let condition of conditions) {
                            if (name.indexOf(condition) < 0) {
                                show_flag = 0;
                                break;
                            }
                        }
                        item.show = show_flag;
                    }
                }
            },
            sumMoney() {
                let total_money = '';
                for (let item of this.detail_data) {
                    if (!isDecimal(item.quantity) || !isDecimal(item.price)) {
                        total_money = '';
                    } else {
                        total_money = Number((Number(item.quantity) * Number(item.price)).toFixed(2));
                    }
                    this.$set(item, 'total_money', total_money);
                }
            }
        },
        watch:{
            type_uid: function(val) {
                showSpin();
                $.post('{{ url('purchase/goodstype/change/') }}' + val, function(rs) {
                    closeSpin();
                    app.supplier = '';
                    app.goods = '';
                    app.goods_list = rs.data;
                });
            },
            supplier: function() {
                this.goodsShow();
            },
            goods: function() {
                this.goodsShow();
            }
        }
    });

    initUpLoaderPdf('purchase_other');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    function initSize() {
        let h = $(window).height() - 35 - 27;

        let goods_body_height = h - 8 - $(".alert").outerHeight(true)
            - $(".portlet-goods .portlet-title").outerHeight(true);

        $(".portlet-goods .tree-box").height(goods_body_height);

        $(".portlet-goods .zh-table-box-content").height(goods_body_height
            - $(".portlet-goods .table-search-bar").outerHeight(true));

        let $portlet_body = $(".portlet-detail .portlet-body");
        $portlet_body.height(h - 8 - $(".portlet-detail .portlet-title").outerHeight(true));

        $(".portlet-detail .zh-table-box-content").height($portlet_body.height()
            - $(".portlet-detail .table-search-bar").outerHeight(true));
    }

    var $tree = $('#tree');

    $(function () {
        $tree.jstree({
            "core": {"data": {{ jsonTree | json_encode}}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            app.type_uid = data.node.original.uid;
        });

        initSize();
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: true,
        autoclose: true,
        format: 'yyyy-mm-dd',
        pickerPosition: 'bottom-left'
    }).on('changeDate', function () {
        let obj = $(this).find('input');
        app[$(obj).attr('name')] = $(obj).val();
    });

    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select-detail').selectpicker(bsSelectOption);
</script>
<style>
    .tree-box {
        overflow-y: auto;
    }

    .table-search-bar {
        margin-bottom: 20px;
    }

    .zh-table-box table thead {
        z-index: 3;
    }
</style>