<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Service\NoticeService;
use Envsan\Modules\Mes\Service\ProduceService;

/**
 * @name("生产")
 */
class ProduceController extends SuperController
{
    private $page_id = 9;
    private $other_page_id = 10;

    /**
     * @name("履历")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ProduceService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("其他工作")
     */
    public function otherAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ProduceService();
            $builder = $s->selectOtherAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->other_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->other_page_id;
    }


    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new ProduceService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new ProduceService();
        $builder = $s->selectOtherAll();
        $table = new TableService();
        $table->exportExcel($this->other_page_id,$builder);
    }
}