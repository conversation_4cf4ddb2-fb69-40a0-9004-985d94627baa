<?php
namespace Envsan\Modules\Mes\Api;

use Envsan\Common\Data\JsonData;
use Envsan\Modules\Mes\Service\PlanService;
use Envsan\Modules\Purchase\Service\OrderWwService;

/**
 * @noacl
 */
class PlanController extends SuperController
{
    /**
     * @name("初始化")
     */
    public function initAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $ret->handleResult($s->getInitData());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * @name("查看")
     */
    public function viewAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $rtn = $s->getPlanListAll();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * @name("一览")
     */
    public function listAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $builder = $s->selectAll();
            $ret->message = '';
            $ret->data = $builder->getQuery()->execute();
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * @name("数据")
     */
    public function dataAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $rtn = $s->getPlanData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function saveAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $ret->handleResult($s->savePlan());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function save2Action(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $rtn = $s->savePlan2();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function moveAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $rtn = $s->saveMove();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function deleteAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $rtn = $s->deleteById();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }


    public function tempentrustAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderWwService();
            $ret = new JsonData();
            $ret->handleResult($s->createWwPlan());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }
    public function delete2Action(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PlanService();
            $ret = new JsonData();
            $rtn = $s->deleteById2();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }
}