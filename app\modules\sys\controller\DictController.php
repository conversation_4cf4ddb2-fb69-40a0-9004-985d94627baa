<?php
namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Modules\Sys\Model\SysDict;
use Envsan\Modules\Sys\Service\DictService;

/**
 * @name("数据字典")
 */
class DictController extends SuperController
{
    /**
     * @name("管理")
     */
    public function listAction()
    {
        $this->view->dictList = json_encode(ConstantUtil::$dict_types);
    }

    /**
     * @acl({'link':'sys:dict:list'})
     */
    function editAction($dict_type)
    {
        $s = new DictService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($dict_type);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = [];
        $jrow['select_idx'] = -1;
        $jrow['detail_list'] = $s->selectDetailAll($dict_type);
        $this->view->jsonDict = json_encode($jrow);
        $this->view->dict_type = $dict_type;
    }
}