<?php
namespace Envsan\Common\Component;

class ErrorHelper
{
    public static function format($ormObj)
    {
        $ret = 'dberror:';
        foreach ($ormObj->getMessages() as $message) {
            $ret.=$message->getMessage().',';
        }
        return $ret;
    }

    const WRONG_ID          = '数据ID不正确';
    const WRONG_OWNER       = 'OWNER不正确';
    const WRONG_INPUT       = '输入不正确';
    const WRONG_STATUS       = '数据状态不正确';
    const UNKOWN            = '未知错误';
    const DATA_EXIST        = '当前有数据绑定到该记录，无法删除!';
    const ROW_EXIST         = '该记录已存在!';
    const ROW_NOTEXIST      = '该记录不存在!';
    const NOERROR           = '';
}