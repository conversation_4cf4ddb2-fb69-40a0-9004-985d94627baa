<?php
namespace Envsan\Modules\Work\Api;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\LoginService;

/**
 * @noacl
 */
class LoginController extends SuperController
{
    public function codeAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new LoginService();
            $ret = new JsonData();
            $ret->message = $s->getMobileCode();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function loginAction()
    {
        if ($this->request->isPost()) {
            $s = new LoginService();
            return json_encode($s->login(), JSON_UNESCAPED_UNICODE);
        }
    }

    public function logoutAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new LoginService();
            return json_encode($s->logout());
        }
    }
}
