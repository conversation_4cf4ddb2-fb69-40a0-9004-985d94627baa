{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" >
            <div class="search-bar bordered">
                <form class="form-horizontal" autocomplete="off">
                    <div class="row">
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group">
                                <label class="col-md-3 control-label">产品名称</label>
                                <div class="col-md-9">
                                    <input type="text" class="form-control" name="product_name" v-model="product_name"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-lg-3">
                            <div class="form-group">
                                <label class="col-md-3 control-label">规格型号</label>
                                <div class="col-md-9">
                                    <input type="text" class="form-control" name="product_model" v-model="product_model"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 to-right">
                            <button type="button" class="btn green" @click="search">
                                <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                            </button>
                            <button type="button" class="btn blue" @click="reset">
                                <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                            </button>
                            <button type="button" class="btn yellow" @click="toggleMaterialsPanel">
                                <i class="fa fa-list"></i>&nbsp;
                                <span v-if="materialsLoading">加载中...</span>
                                <span v-else>查看原材料</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="row row-summary">
                <div class="col-md-12">
                    <div class="summary-info" style="display: inline-flex; margin-right: 20px;">
                        <span class="label label-primary" style="margin-right: 14px; font-size: 16px; padding: 8px 12px;">
                            <i class="fa fa-file-text-o"></i> 订单总数: <span id="order_count" style="font-size: 18px; font-weight: bold;">${order_count}</span>
                        </span>
                        <span class="label label-success" style="margin-right: 14px; font-size: 16px; padding: 8px 12px;">
                            <i class="fa fa-database"></i> 库存数量: <span id="stock_count" style="font-size: 18px; font-weight: bold;">${stock_count}</span>
                        </span>
                        <span class="label label-warning" style="margin-right: 14px; font-size: 16px; padding: 8px 12px;">
                            <i class="fa fa-calendar-check-o"></i> 排产数量: <span id="max_plan_cnt" style="font-size: 18px; font-weight: bold;">${max_plan_cnt}</span>
                        </span>
                        <span class="label label-danger" style="margin-right: 14px; font-size: 16px; padding: 8px 12px;">
                            <i class="fa fa-industry"></i> 生产计划数量: <span id="total_notice_quantity" style="font-size: 18px; font-weight: bold;">${total_notice_quantity}</span>
                        </span>

                        <span class="label label-success" style="margin-right: 14px; font-size: 16px; padding: 8px 12px;">
                            <i class="fa fa-check"></i> 完成数量: <span id="max_finish_cnt" style="font-size: 18px; font-weight: bold;">${max_finish_cnt}</span>
                        </span>

                        <span class="label label-danger" style="margin-right: 14px; font-size: 16px; padding: 8px 12px;">
                            <i class="fa fa-exclamation-triangle"></i> 错误数量: <span id="total_error_cnt" style="font-size: 18px; font-weight: bold;">${total_error_cnt}</span>
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 产品统计数据区域 -->
            
            
            <!-- 原材料信息面板 -->
            <div class="row" id="materials-panel" style="display: none;">
                <div class="col-md-12">
                    <div class="portlet light bordered">
                        <div class="portlet-title">
                            <div class="caption">
                                <i class="fa fa-list"></i>
                                <span class="caption-subject font-blue">原材料清单</span>
                            </div>
                            <div class="actions">
                                <button type="button" class="btn btn-circle btn-icon-only btn-default" @click="hideMaterialsPanel">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div id="materials-table-container"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('mes/notice/orderdetail/json/' ~ product_id) }}"
                   data-page-size="100"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-checkbox="true"></th>
                    <th data-field="product_name">产品名称</th>
                    <th data-field="product_code">规格型号</th>
                    <th data-field="cnt">订单数量</th>
                    <th data-field="inventory_unit">数量单位</th>
                    <th data-field="order_code">订单号</th>
                    <th data-field="sign_date">订单日期</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            product_name: '',
            product_model: '',
            order_count: 0,
            stock_count: 0,
            materials: [], // 原材料数据
            materialsLoading: false, // 是否正在加载
            // 产品统计数据
            max_plan_cnt: 0,
            total_plan_cnt: 0,
            max_finish_cnt: 0,
            total_error_cnt: 0,
            total_notice_detail_cnt: 0,
            total_notice_quantity: 0
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            // 切换原材料面板
            toggleMaterialsPanel: function() {
                var $panel = $('#materials-panel');
                if ($panel.is(':visible')) {
                    $panel.hide();
                } else {
                    // 有缓存就直接显示，没有就去后台查询
                    if (this.materials.length > 0) {
                        this.showMaterialsPanel();
                    } else {
                        this.loadMaterials();
                    }
                }
            },
            // 加载原材料数据
            loadMaterials: function() {
                var self = this;
                
                if (self.materialsLoading) return; // 防止重复请求
                
                self.materialsLoading = true;
                
                $.ajax({
                    url: '{{ url("mes/notice/materialsdetail/json") }}/' + '{{ product_id }}',
                    method: 'GET',
                    success: function(data) {
                        self.materials = data || [];
                        self.materialsLoading = false;
                        self.showMaterialsPanel();
                    },
                    error: function() {
                        self.materialsLoading = false;
                        alert('加载原材料数据失败');
                    }
                });
            },
            // 显示原材料面板
            showMaterialsPanel: function() {
                if (this.materials.length > 0) {
                    this.renderMaterialsPanel();
                    $('#materials-panel').show();
                } else {
                    alert('该产品暂无原材料信息');
                }
            },
            // 渲染原材料面板
            renderMaterialsPanel: function() {
                var self = this;
                
                setTimeout(function() {
                    var $container = $('#materials-table-container');
                    
                    if (!self.materials || self.materials.length === 0) {
                        $container.html('<div class="alert alert-info text-center">该产品暂无原材料信息</div>');
                        return;
                    }
                    
                    $container.html('<table id="materials-main-table" class="table table-striped table-condensed">' +
                        '<thead class="bg-blue">' +
                        '<tr>' +
                        '<th>原材料名称</th>' +
                        '<th>规格型号</th>' +
                        '<th>需求数量</th>' +
                        '<th>库存数量</th>' +
                        '<th>单位</th>' +
                        '<th>备注</th>' +
                        '</tr>' +
                        '</thead>' +
                        '</table>');
                    
                    $('#materials-main-table').bootstrapTable({
                        data: self.materials,
                        sidePagination: 'client',
                        pagination: false,
                        search: false,
                        showHeader: true,
                        showColumns: false,
                        showRefresh: false,
                        showToggle: false,
                        columns: [
                            {
                                field: 'material_name',
                                title: '原材料名称',
                                sortable: true
                            },
                            {
                                field: 'material_model',
                                title: '规格型号',
                                sortable: true
                            },
                            {
                                field: 'required_qty',
                                title: '需求数量',
                                sortable: true,
                                formatter: function(value, row, index) {
                                    var unitRequiredQty = value || 0;
                                    var totalRequiredQty = unitRequiredQty * app.order_count;
                                    
                                    if (app.order_count > 0) {
                                        return '<span class="text-primary font-weight-bold">' + totalRequiredQty + '</span>' +
                                               '<br><small class="text-muted">(' + unitRequiredQty + ' × ' + app.order_count + ')</small>';
                                    } else {
                                        return '<span class="text-muted">' + unitRequiredQty + '</span>';
                                    }
                                }
                            },
                            {
                                field: 'stock_qty',
                                title: '库存数量',
                                sortable: true,
                                formatter: function(value, row, index) {
                                    var stockQty = value || 0;
                                    var unitRequiredQty = row.required_qty || 0;
                                    var totalRequiredQty = unitRequiredQty * app.order_count;
                                    
                                    var className = stockQty >= totalRequiredQty ? 'text-success' : 'text-danger';
                                    return '<span class="' + className + ' font-weight-bold">' + stockQty + '</span>';
                                }
                            },
                            {
                                field: 'unit',
                                title: '单位',
                                sortable: true
                            },
                            {
                                field: 'remarks',
                                title: '备注'
                            }
                        ]
                    });
                }, 100);
            },
            // 刷新原材料表格
            refreshMaterialsTable: function() {
                if ($('#materials-main-table').length > 0) {
                    // 销毁并重新创建表格，以便重新执行formatter函数
                    $('#materials-main-table').bootstrapTable('destroy');
                    this.renderMaterialsPanel();
                }
            },
            // 隐藏原材料面板
            hideMaterialsPanel: function() {
                $('#materials-panel').hide();
            }
        }
    });
    $table.bootstrapTable();
    
    // 监听表格加载完成事件
    $table.on('load-success.bs.table', function (e, data) {
        updateSelectedSummary();
        
        // 计算所有行的库存总数
        updateStockSummary(data);
        
        // 更新产品统计数据
        if (data && data.product_stats && data.product_stats.length > 0) {
            var stats = data.product_stats[0];
            
            // 添加新的统计数据到Vue数据中
            app.$set(app, 'max_plan_cnt', stats.max_plan_cnt || 0);
            app.$set(app, 'total_plan_cnt', stats.total_plan_cnt || 0);
            app.$set(app, 'max_finish_cnt', stats.max_finish_cnt || 0);
            app.$set(app, 'total_error_cnt', stats.total_error_cnt || 0);
            app.$set(app, 'total_notice_detail_cnt', stats.total_notice_detail_cnt || 0);
            app.$set(app, 'total_notice_quantity', stats.total_notice_quantity || 0);
        }
    });
    
    // 监听表格选择事件，计算选中行的合计
    $table.on('check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table', function () {
          updateSelectedSummary();
          // 如果原材料面板是显示状态，刷新原材料表格
          if ($('#materials-panel').is(':visible') && app.materials.length > 0) {
              app.refreshMaterialsTable();
          }
      });
    
    // 计算选中行的合计数据
    function updateSelectedSummary() {
        var selections = $table.bootstrapTable('getSelections');
        var totalOrderCount = 0;
        
        selections.forEach(function(row) {
            totalOrderCount += parseInt(row.cnt) || 0;
        });
        
        // 更新Vue数据
        app.order_count = totalOrderCount;
    }
    
    // 计算所有行的库存总数
    function updateStockSummary(data) {
        var totalStockCount = 0;
        
        if (data && data.rows) {
            data.rows.forEach(function(row) {
                totalStockCount += parseInt(row.stock_cnt) || 0;
            });
        }
        
        // 更新Vue数据
        app.stock_count = totalStockCount;
    }

    function codeFormatter(v, row, index) {
        return row.apply_code ?? v;
    }

    function dateFormatter(v, row, index) {
        return row.apply_date ?? v;
    }

    function add() {
        var datas = $table.bootstrapTable('getSelections');
        if (datas.length == 0) {
            alertWarning('请选择添加明细');
            return;
        }

        if (datas.length > 1) {
            alertWarning('只能添加一条明细');
            return;
        }

        top.window.layer_data2 = datas;
        top.window.layer_result2 = 'ok';
        top.layer.close(top.layer.getFrameIndex(window.name));
    }

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>

<style>
    .detail-view {
        padding: 15px;
        background-color: #f9f9f9;
        border-left: 3px solid #3598dc;
    }
    
    .detail-view h5 {
        margin-top: 0;
        color: #3598dc;
        font-weight: bold;
    }
    
    .detail-view .table {
        margin-bottom: 0;
        font-size: 12px;
    }
    
    .detail-view .table th {
        background-color: #e7ecf1;
        font-weight: bold;
        text-align: center;
    }
    
    .detail-view .table td {
        vertical-align: middle;
    }
    
    .text-success {
        color: #27ae60 !important;
        font-weight: bold;
    }
    
    .text-danger {
        color: #e74c3c !important;
        font-weight: bold;
    }
    
    .loading-placeholder {
        padding: 20px;
        color: #666;
    }
    
    .loading-placeholder .fa-spinner {
        margin-right: 5px;
    }
</style>