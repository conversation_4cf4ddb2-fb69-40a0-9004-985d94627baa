<?php
return [
    [
        'name' => '拒收单',
        'identity' => 'purchase:arrivalrejection',
        'action' => [
            ['name' => '拒收单列表', 'identity' => 'purchase:arrivalrejection:list', 'comment' => ''],
        ]
    ],
    [
        'name' => '不良品处理',
        'identity' => 'purchase:defecthandling',
        'action' => [
            ['name' => '不良品处理列表', 'identity' => 'purchase:defecthandling:list', 'comment' => ''],
            ['name' => '不良品处理新增', 'identity' => 'purchase:defecthandling:create', 'comment' => ''],
            ['name' => '不良品处理审核', 'identity' => 'purchase:defecthandling:approval', 'comment' => ''],
        ]
    ],
    [
        'name' => '物料类型',
        'identity' => 'purchase:goodstype',
        'action' => [
            ['name' => '物料类型物料列表', 'identity' => 'purchase:goodstype:list', 'comment' => ''],
            ['name' => '物料类型新增', 'identity' => 'purchase:goodstype:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '采购质检',
        'identity' => 'purchase:inspection',
        'action' => [
            ['name' => '采购质检报检单列表', 'identity' => 'purchase:inspection:list', 'comment' => ''],
        ]
    ],
    [
        'name' => '采购入库',
        'identity' => 'purchase:instock',
        'action' => [
            ['name' => '采购入库列表', 'identity' => 'purchase:instock:list', 'comment' => ''],
            ['name' => '采购入库查询', 'identity' => 'purchase:instock:search', 'comment' => ''],
            ['name' => '采购入库新增', 'identity' => 'purchase:instock:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '盘点',
        'identity' => 'purchase:inventory',
        'action' => [
            ['name' => '盘点列表', 'identity' => 'purchase:inventory:list', 'comment' => ''],
            ['name' => '盘点查询', 'identity' => 'purchase:inventory:search', 'comment' => ''],
            ['name' => '盘点新增', 'identity' => 'purchase:inventory:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '开票管理',
        'identity' => 'purchase:invoice',
        'action' => [
            ['name' => '开票管理列表', 'identity' => 'purchase:invoice:list', 'comment' => ''],
            ['name' => '开票管理新增', 'identity' => 'purchase:invoice:create', 'comment' => ''],
            ['name' => '开票管理查询', 'identity' => 'purchase:invoice:search', 'comment' => ''],
        ]
    ],
    [
        'name' => '采购订单',
        'identity' => 'purchase:order',
        'action' => [
            ['name' => '采购订单列表', 'identity' => 'purchase:order:list', 'comment' => ''],
            ['name' => '采购订单待采购查询', 'identity' => 'purchase:order:wait', 'comment' => ''],
            ['name' => '采购订单查询', 'identity' => 'purchase:order:search', 'comment' => ''],
            ['name' => '采购订单创建', 'identity' => 'purchase:order:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '外委加工',
        'identity' => 'purchase:orderww',
        'action' => [
            ['name' => '外委加工计划', 'identity' => 'purchase:orderww:plan', 'comment' => ''],
            ['name' => '外委加工列表', 'identity' => 'purchase:orderww:list', 'comment' => ''],
            ['name' => '外委加工查询', 'identity' => 'purchase:orderww:search', 'comment' => ''],
            ['name' => '外委加工创建', 'identity' => 'purchase:orderww:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '其他入库',
        'identity' => 'purchase:other',
        'action' => [
            ['name' => '其他入库列表', 'identity' => 'purchase:other:list', 'comment' => ''],
            ['name' => '其他入库查询', 'identity' => 'purchase:other:search', 'comment' => ''],
            ['name' => '其他入库新增', 'identity' => 'purchase:other:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '领料出库',
        'identity' => 'purchase:outstock',
        'action' => [
            ['name' => '领料出库列表', 'identity' => 'purchase:outstock:list', 'comment' => ''],
            ['name' => '领料出库mes列表', 'identity' => 'purchase:outstock:meslist', 'comment' => ''],
            ['name' => '领料出库查询', 'identity' => 'purchase:outstock:search', 'comment' => ''],
        ]
    ],
    [
        'name' => '付款管理',
        'identity' => 'purchase:pay',
        'action' => [
            ['name' => '付款管理列表', 'identity' => 'purchase:pay:list', 'comment' => ''],
            ['name' => '付款管理新增', 'identity' => 'purchase:pay:create', 'comment' => ''],
            ['name' => '付款管理查询', 'identity' => 'purchase:pay:search', 'comment' => ''],
        ]
    ],
    [
        'name' => '采购用料计划',
        'identity' => 'purchase:plan',
        'action' => [
            ['name' => '采购用料计划列表', 'identity' => 'purchase:plan:list', 'comment' => ''],
        ]
    ],
    [
        'name' => '采购到货',
        'identity' => 'purchase:receipt',
        'action' => [
            ['name' => '采购到货列表', 'identity' => 'purchase:receipt:list', 'comment' => ''],
            ['name' => '采购到货新增', 'identity' => 'purchase:receipt:create', 'comment' => ''],
            ['name' => '采购到货审核', 'identity' => 'purchase:receipt:approval', 'comment' => ''],
        ]
    ],
    [
        'name' => '采购请求',
        'identity' => 'purchase:request',
        'action' => [
            ['name' => '采购请求列表', 'identity' => 'purchase:request:list', 'comment' => ''],
            ['name' => '采购请求查询', 'identity' => 'purchase:request:search', 'comment' => ''],
        ]
    ],
    [
        'name' => '库存',
        'identity' => 'purchase:stock',
        'action' => [
            ['name' => '库存查询列表', 'identity' => 'purchase:stock:list', 'comment' => ''],
            ['name' => '库存履历查询', 'identity' => 'purchase:stock:search', 'comment' => ''],
        ]
    ],
    [
        'name' => '供应商',
        'identity' => 'purchase:supplier',
        'action' => [
            ['name' => '供应商列表', 'identity' => 'purchase:supplier:list', 'comment' => ''],
            ['name' => '供应商新增', 'identity' => 'purchase:supplier:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '外委入库',
        'identity' => 'purchase:wwinstock',
        'action' => [
            ['name' => '外委入库管理', 'identity' => 'purchase:wwinstock:list', 'comment' => ''],
            ['name' => '外委入库查询', 'identity' => 'purchase:wwinstock:search', 'comment' => ''],
            ['name' => '外委入库新增', 'identity' => 'purchase:wwinstock:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '外委出库',
        'identity' => 'purchase:wwoutstock',
        'action' => [
            ['name' => '外委出库管理', 'identity' => 'purchase:wwoutstock:list', 'comment' => ''],
            ['name' => '外委出库查询', 'identity' => 'purchase:wwoutstock:search', 'comment' => ''],
            ['name' => '外委出库新增', 'identity' => 'purchase:wwoutstock:create', 'comment' => ''],
            ['name' => '外委出库审核', 'identity' => 'purchase:wwoutstock:approval', 'comment' => ''],
        ]
    ],
    [
        'name' => '委外工序价格',
        'identity' => 'purchase:wwprocess',
        'action' => [
            ['name' => '委外工序价格管理', 'identity' => 'purchase:wwprocess:list', 'comment' => ''],
            ['name' => '委外工序价格新增', 'identity' => 'purchase:wwprocess:create', 'comment' => ''],
        ]
    ],
    [
        'name' => '外委到货单',
        'identity' => 'purchase:wwreceipt',
        'action' => [
            ['name' => '外委到货单管理', 'identity' => 'purchase:wwreceipt:list', 'comment' => ''],
            ['name' => '外委到货单新增', 'identity' => 'purchase:wwreceipt:create', 'comment' => ''],
            ['name' => '外委到货单审核', 'identity' => 'purchase:wwreceipt:approval', 'comment' => ''],
        ]
    ],
];
