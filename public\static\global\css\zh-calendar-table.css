.calendar-box {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.label-time {
    width: 50px;
    border-radius: 2px !important;
    background-color: #f2f2f2;
    color: #333;
    padding: 5px;
}

.label-time.first {
    background-color: #f2e1baa6;
    border: 1px solid #dc6713;
    color: #dc6713;
}
.label-time.last {
    background-color: rgba(186, 217, 242, 0.65);
    border: 1px solid #136adc;
    color: #136adc;
}

.table-box {
    flex: 1;
    height: 1px;
}

.table-box-content {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.table-box table {
    width: max-content !important;
    max-width: max-content !important;
    border: 0 !important;
}

.table-box th, .table-box td {
    text-align: center;
    vertical-align: middle !important;
}

.table-box table thead th {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #3598DC !important;
    color: #ffffff;
}

.table-box table tbody td {
    background-color: #ffffff;
}

.table-box table thead th:first-child {
    left: 0;
    z-index: 3;
}

.table-box table tr > :first-child {
    position: sticky;
    left: 0;
    border-left: 0 !important;
    background-color: #c49f47;
    color: #ffffff;
}

.calendar-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
}

.biz-left {
    display: flex;
    align-items: center;
}

.biz-right {
    display: flex;
    align-items: center;
}

.biz-right .page-list {
    min-width: 60px;
}

.col-btn-search {
    margin-left: 5px;
    cursor: pointer;
}

.col-btn-search i {
    opacity: 0.5;
    transition: all 0.5s;
}

.col-btn-search:hover i {
    opacity: 1;
}