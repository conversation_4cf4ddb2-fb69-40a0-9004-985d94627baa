<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesViewPlanWarning extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $notice_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $warning_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $error_rate;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $finish_rate;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $plan_rate;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_view_plan_warning';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewPlanWarning[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewPlanWarning
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
