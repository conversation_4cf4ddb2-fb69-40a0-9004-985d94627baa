<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesOtherLogs;
use Envsan\Modules\Mes\Model\MesProduceLogs;
use Envsan\Modules\Mes\Model\MesReport;
use Envsan\Modules\Mes\Model\MesReportDetail;
use Envsan\Modules\Mes\Model\MesStockLogs;
use Envsan\Modules\Mes\Util\Constant;
use Phalcon\Mvc\User\Component;

class ReportService extends BaseService
{
    public function selectAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.report_month,
                t1.report_date,
                t1.user_cnt,
                t1.user_cost,
                t1.status,
                t1.status_name,
                t1.remarks,
                t1.ext_val,
                t1.review_time,
                t2.real_name as review_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesReport', 't1')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.review_user_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.report_date desc,t1.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectStatAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.report_date,
                t1.staff_name,
                t1.cost,
                t1.day_money,
                t1.jj_money,
                t1.other_money,
                t1.day_bz_money,
                t1.sum_money
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesReportDetail', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.report_date desc,t1.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function getReportData($report_date)
    {
        $rtn = new \stdClass();
        if (empty($report_date)) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $check_row = MesReport::findFirst(['del_flag = 0 and report_date = ?1 and status = ?2', 'bind' => [1 => $report_date, '20']]);
        if (!empty($check_row)) {
            $rtn->message = '生产日期日报数据已存在';
            return $rtn;
        }
        $common = new CommonService();
        $work_date = $common->getWorkDate();
        if ($work_date < $report_date) {
            $rtn->message = '不能审核未来日期';
            return $rtn;
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.real_name,
                t1.cost
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User', 't1')
            ->where('t1.del_flag = 0 and t1.account_status = 0 and t1.role_id = 2')
            ->orderBy('t1.id');
        $user_list = $builder->getQuery()->execute()->toArray();
        $user_obj = [];
        // 取得角色是工人：所有工人
        foreach ($user_list as $user) {
            $user['produce_list'] = [];
            $user['other_list'] = [];
            $user['day_money'] = 0;
            $user['day_bz_money'] = 0;
            $user['jj_money'] = 0;
            $user['other_money'] = 0;
            $user['sum_money'] = 0;
            $user['cost'] = CvtUtil::emptyToDouble($user['cost']);
            $user_obj['_' . $user['id']] = $user;
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.bom_id,
                t1.notice_detail_id,
                t1.shift_type,
                t1.work_type,
                t1.one_cost,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name,
                t5.name as bom_name,
                ifnull(t5.produce_cnt,\'\') as scxjz,
                t5.data_sort,
                t5.nid,
                round(t1.cnt,4) as cnt,
                round(t1.hour,4) as hour,
                ifnull(t1.error_cnt,0) as error_cnt,
                t1.error_type,
                t1.error_remarks,
                t1.staff_id,
                t1.staff_name,
                t6.wages_ratio
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduceLogs', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1.notice_detail_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't2.notice_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't2.product_id = t4.id', 't4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom', 't1.bom_id = t5.id', 't5')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItem', 't1.equ_id = t6.id', 't6')
            ->where('t1.del_flag = 0 and t1.work_date = ?1 and t1.staff_id is not null', [1 => $report_date])
            ->orderBy('t1.staff_id,t1.shift_type,t1.id');
        $report_list = $builder->getQuery()->execute()->toArray();
        foreach ($report_list as $report_item) {
            $key = '_' . $report_item['staff_id'];
            // 生产报工的工人能看到自己的日报
            if (array_key_exists($key, $user_obj)) {
                // 设备工资系数
                $wages_ratio = empty($report_item['wages_ratio']) ? 1 : CvtUtil::emptyToDouble($report_item['wages_ratio']);
                $report_item['wages_ratio'] = $wages_ratio;
                $report_item['jzz'] = '';
                $report_item['scx'] = '';
                $sort = CvtUtil::emptyToDouble($report_item['data_sort']);
                if ($sort < 10) {
                    $sort = '0' . $sort;
                }
                $report_item['bom_code'] = $report_item['notice_code'] . $sort;
                // 产品生产性基准
                if (!empty($report_item['scxjz'])) {
                    // 实际生产/生产基准
                    $report_item['jzz'] = round(CvtUtil::emptyToInt($report_item['cnt']) / CvtUtil::emptyToInt($report_item['scxjz']), 2);
                    // 标准生产率:上面的结果在除以小时数
                    $report_item['scx'] = round(($report_item['jzz'] / CvtUtil::emptyToDouble($report_item['hour'])) * 100, 2);
                }
                $user_obj[$key]['produce_list'][] = $report_item;
                // 产品的工资类型：1-日工，2-计件
                if ($report_item['work_type'] == 1) {
                    // 工作时长 * 工人小时工资 * 设备的工资系数
                    $day_money = CvtUtil::emptyToDouble($report_item['hour']) * $user_obj[$key]['cost'] * $wages_ratio;
                    // 工人每天的工资
                    $user_obj[$key]['day_money'] += $day_money;
                } else {
                    // 这个是计件的工资
                    // 单件的工资 * 生产的数量 * 设备的工资系数
                    // TODO 这里的数量是生产数量（包含不合格的产品）
                    $jj_money = CvtUtil::emptyToDouble($report_item['one_cost']) * CvtUtil::emptyToInt($report_item['cnt']) * $wages_ratio;
                    $user_obj[$key]['jj_money'] += $jj_money;
                }
                $day_bz_money = 0;
                // shift_type: 1-早班，2-晚班
                if ($report_item['shift_type'] == 2) {
                    $day_bz_money = CvtUtil::emptyToDouble($report_item['hour']) * 0.5;
                }
                $user_obj[$key]['day_bz_money'] += $day_bz_money;
            }
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.shift_type,
                round(t1.hour,4) as hour,
                t1.work_type,
                t1.produce_type,
                t1.remarks,
                t1.staff_id,
                t1.staff_name,
                1 as wages_ratio
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesOtherLogs', 't1')
            ->where('t1.del_flag = 0 and t1.work_date = ?1', [1 => $report_date])
            ->orderBy('t1.id');
        $other_list = $builder->getQuery()->execute()->toArray();
        foreach ($other_list as $other_item) {
            $key = '_' . $other_item['staff_id'];
            if (array_key_exists($key, $user_obj)) {
                $wages_ratio = empty($other_item['wages_ratio']) ? 1 : CvtUtil::emptyToDouble($other_item['wages_ratio']);
                $user_obj[$key]['other_list'][] = $other_item;
                $other_money = CvtUtil::emptyToDouble($other_item['hour']) * $user_obj[$key]['cost'] * $wages_ratio;
                $day_bz_money = 0;
                if ($other_item['shift_type'] == 2) {
                    $day_bz_money = CvtUtil::emptyToDouble($other_item['hour']) * 0.5;
                }
                $user_obj[$key]['day_bz_money'] += $day_bz_money;
                $user_obj[$key]['other_money'] += $other_money;
            }
        }
        $user_list = [];
        foreach ($user_obj as $key => $user) {
            if (count($user['produce_list']) > 0 || count($user['other_list']) > 0) {
                $user['sum_money'] = $user['day_money'] + $user['day_bz_money'] + $user['jj_money'] + $user['other_money'];
                $user_list[] = $user;
            }
        }
        $rtn->message = '';
        $rtn->data = $user_list;
        return $rtn;
    }

    public function selectById($id)
    {
        return MesReport::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return MesReport::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function selectDetailByUid($uid)
    {
        return MesReportDetail::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function create()
    {
        $row = new MesReport();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($act, $row) {
            $type = $this->request->getPost('type', 'tstring');
            $report_date = $this->request->getPost('report_date', 'tstring');
            $remarks = $this->request->getPost('remarks', 'tstring');
            $files = urldecode($this->request->getPost('files', 'tstring'));
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
            $report_data = str_replace('%2B', '+', urldecode($this->request->getPost('report_data', 'tstring')));
            if (empty($type) || empty($report_date)) {
                return $this->error(ErrorHelper::WRONG_INPUT) ;
            }
            $common = new CommonService();
            if ($act == 'update') {
                $check_row = MesReport::findFirst(['del_flag = 0 and report_date = ?1 and status = ?2', 'bind' => [1 => $report_date, 2 => '20']]);
                if (!empty($check_row)) {
                    return $this->error('生产日期日报数据已存在');
                }
                $work_date = $common->getWorkDate();
                if ($work_date < $report_date) {
                    return  $this->error('不能审核未来日期');
                }
            }

            $files = CvtUtil::emptyToArray($files);
            $report_data = CvtUtil::emptyToArray($report_data);
            $ext_data = CvtUtil::emptyToArray($ext_data);

            $user_cost = 0;
            foreach ($report_data as $report_item) {
                $user_cost += CvtUtil::emptyToDouble($report_item['sum_money']);
            }
            $now = DateUtil::now();
            $user = SessionData::user();
            $row->report_date = $report_date;
            $row->report_month = $common->getWorkMonth($row->report_date);
            $row->user_cnt = count($report_data);
            $row->user_cost = round($user_cost, 2);
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->report_data = CvtUtil::arrayToNull($report_data);
            if ($type == 1) {
                $row->status = 10;
            } else {
                $row->review_user_id = $user->id;
                $row->review_time = $now;
                $row->status = 20;
            }
            $row->status_name = Constant::$report_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            $row->save();
            if ($type == 2) {
                foreach ($report_data as $report_item) {
                    $detail = new MesReportDetail();
                    $detail->uid = UUID::make();
                    $detail->report_id = $row->id;
                    $detail->report_date = $row->report_date;
                    $detail->report_month = $row->report_month;
                    $detail->staff_id = $report_item['id'];
                    $detail->staff_name = $report_item['real_name'];
                    $detail->cost = CvtUtil::emptyToDouble($report_item['cost']);
                    $detail->day_money = CvtUtil::emptyToDouble($report_item['day_money']);
                    $detail->jj_money = CvtUtil::emptyToDouble($report_item['jj_money']);
                    $detail->other_money = CvtUtil::emptyToDouble($report_item['other_money']);
                    $detail->day_bz_money = CvtUtil::emptyToDouble($report_item['day_bz_money']);
                    $detail->sum_money = CvtUtil::emptyToDouble($report_item['sum_money']);
                    $detail->produce_list = CvtUtil::arrayToNull($report_item['produce_list']);
                    $detail->other_list = CvtUtil::arrayToNull($report_item['other_list']);
                    $detail->update_date = $now;
                    $detail->update_by = $user->id;
                    $detail->del_flag = 0;
                    $detail->owner = $user->owner;
                    $detail->save();
                    // 做一些优化
                    $result = $this->optimizedProductionUpdate($report_item, $detail , $user,$now);
                    if (is_array($result)) {
                        // 如果返回数组，检查success字段
                        if (isset($result['success']) && $result['success'] === false) {
                            return $result;
                        }
                    }
                    $other_ids = [];
                    foreach ($report_item['other_list'] as $other) {
                        $other_ids[] = $other['id'];
                    }

                    $logs_map = [];
                    if (!empty($other_ids)) {
                        $logs = MesOtherLogs::find([
                            'conditions' => 'del_flag = 0 AND id IN ({ids:array})',
                            'bind' => ['ids' => $other_ids]
                        ]);
                        foreach ($logs as $log) {
                            $logs_map[$log->id] = $log;
                        }
                    }
                    foreach ($report_item['other_list'] as $other) {
                        if (array_key_exists($other['id'], $logs_map)) {
                            $log->shift_type = $other['shift_type'];
                            $log->hour = CvtUtil::emptyToDouble($other['hour']);
                            $log->wages_ratio = CvtUtil::emptyToDouble($other['wages_ratio']);
                            $log->staff_cost = $detail->cost;
                            $log->update_date = $now;
                            $log->update_by = $user->id;
                            $log->save();
                        }
                    }
                }
            }
        });

    }

    public function deleteByUid()
    {
        $row = $this->selectByUid($this->request->getPost('uid', 'string'));
        if (empty($row) || $row->del_flag == 1) {
            return '';
        } else if ($row->status != 10) {
            return '生产计划已提交，不能删除';
        }

        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()){
                throw new \Exception('【生产计划管理-删除】mes_notice表更新失败');
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }
// 优化后的代码
    public function optimizedProductionUpdate($report_item, $detail, $user, $now) {

        if (empty($report_item['produce_list'])) {
            return true;
        }

        // 1. 批量收集所有需要的ID和数据
        $produce_ids = [];
        $notice_detail_ids = [];
        $bom_combinations = [];

        foreach ($report_item['produce_list'] as $produce) {
            $produce_ids[] = $produce['id'];
            $notice_detail_ids[] = $produce['notice_detail_id'];
            $bom_combinations[] = [
                'notice_detail_id' => $produce['notice_detail_id'],
                'bom_id' => $produce['bom_id']
            ];
        }

        // 去重
        $produce_ids = array_unique($produce_ids);
        $notice_detail_ids = array_unique($notice_detail_ids);

        // 2. 批量查询生产日志
        $logs_map = [];
        if (!empty($produce_ids)) {
            $logs = MesProduceLogs::find([
                'conditions' => 'del_flag = 0 AND id IN ({ids:array})',
                'bind' => ['ids' => $produce_ids]
            ]);
            foreach ($logs as $log) {
                $logs_map[$log->id] = $log;
            }
        }

        // 3. 批量查询通知单详情
        $notice_details_map = [];
        if (!empty($notice_detail_ids)) {
            $notice_details = MesNoticeDetail::find([
                'conditions' => 'del_flag = 0 AND id IN ({ids:array})',
                'bind' => ['ids' => $notice_detail_ids]
            ]);
            foreach ($notice_details as $detail_item) {
                $notice_details_map[$detail_item->id] = $detail_item;
            }
        }

        // 4. 批量查询已生产数量汇总
        $total_counts_map = [];
        if (!empty($bom_combinations)) {
            // 去重bom组合
            $unique_combinations = [];
            foreach ($bom_combinations as $combo) {
                $key = $combo['notice_detail_id'] . '_' . $combo['bom_id'];
                $unique_combinations[$key] = $combo;
            }

            // 构建IN查询条件
            $notice_detail_ids_for_query = [];
            $bom_ids_for_query = [];
            foreach ($unique_combinations as $combo) {
                $notice_detail_ids_for_query[] = $combo['notice_detail_id'];
                $bom_ids_for_query[] = $combo['bom_id'];
            }

            // 使用ORM批量查询
            $results = $this->modelsManager->createBuilder()
                ->columns('notice_detail_id, bom_id, SUM(cnt) as total_cnt')
                ->addFrom('Envsan\Modules\Mes\Model\MesProduceLogs', 't1')
                ->where('t1.del_flag = 0')
                ->andWhere('t1.notice_detail_id IN ({notice_ids:array})', [
                    'notice_ids' => array_unique($notice_detail_ids_for_query)
                ])
                ->andWhere('t1.bom_id IN ({bom_ids:array})', [
                    'bom_ids' => array_unique($bom_ids_for_query)
                ])
                ->groupBy('notice_detail_id, bom_id')
                ->getQuery()
                ->execute()
                ->toArray();

            // 只保留我们需要的组合结果
            foreach ($results as $result) {
                $key = $result['notice_detail_id'] . '_' . $result['bom_id'];
                if (isset($unique_combinations[$key])) {
                    $total_counts_map[$key] = $result['total_cnt'];
                }
            }
        }

        // 5. 批量查询库存日志
        $stock_logs_map = [];
        if (!empty($produce_ids)) {
            $stock_logs = MesStockLogs::find([
                'conditions' => 'del_flag = 0 AND produce_logs_id IN ({ids:array})',
                'bind' => ['ids' => $produce_ids]
            ]);
            foreach ($stock_logs as $stock_log) {
                $stock_logs_map[$stock_log->produce_logs_id] = $stock_log;
            }
        }

        // 6. 使用批量查询的结果进行业务处理
        foreach ($report_item['produce_list'] as $produce) {
            // 从缓存中获取数据，避免重复查询
            $log = isset($logs_map[$produce['id']]) ? $logs_map[$produce['id']] : null;
            if (empty($log)) {
                continue;
            }

            // 从缓存中获取通知单详情
            $notice_detail_row = isset($notice_details_map[$produce['notice_detail_id']])
                ? $notice_details_map[$produce['notice_detail_id']] : null;
            if (empty($notice_detail_row)) {
                continue;
            }

            // 验证生产数量
            $produce_cnt = CvtUtil::emptyToInt($produce['cnt']);
            $total_key = $produce['notice_detail_id'] . '_' . $produce['bom_id'];
            $current_total = isset($total_counts_map[$total_key]) ? $total_counts_map[$total_key] : 0;

            // 需要减去当前记录的原有数量，因为汇总包含了当前记录
            $existing_total = $current_total - $log->cnt;

            // 数量验证逻辑保持不变
            if (!empty($current_total)) {
                if ($existing_total + $produce_cnt > CvtUtil::emptyToInt($notice_detail_row->quantity)) {
                    return $this->error('合格数量不能大于生产计划的剩余数量');
                }
            } else {
                if ($produce_cnt > CvtUtil::emptyToInt($notice_detail_row->quantity)) {
                    return $this->error('合格数量不能大于生产计划数量');
                }
            }

            // 更新生产日志
            $log->shift_type = $produce['shift_type'];
            $log->work_type = $produce['work_type'];
            $log->one_cost = CvtUtil::emptyToDouble($produce['one_cost']);
            $log->cnt = $produce_cnt;
            $log->hour = CvtUtil::emptyToDouble($produce['hour']);
            $log->error_cnt = CvtUtil::emptyToDouble($produce['error_cnt']);
            $log->wages_ratio = CvtUtil::emptyToDouble($produce['wages_ratio']);
            $log->staff_cost = $detail->cost;
            $log->update_date = $now;
            $log->update_by = $user->id;
            $log->save();

            // 更新库存日志（如果存在nid且有库存记录）
            if (!empty($produce['nid'])) {
                $stock_log = isset($stock_logs_map[$log->id]) ? $stock_logs_map[$log->id] : null;
                if (!empty($stock_log)) {
                    $stock_log->cnt = $log->cnt;
                    $stock_log->update_date = $now;
                    $stock_log->update_by = $user->id;
                    $stock_log->save();
                }
            }
        }
    }
}