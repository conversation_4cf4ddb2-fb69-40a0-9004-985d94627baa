<?php
namespace Envsan\Common\Util;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Phalcon\Mvc\Model\Behavior\SoftDelete;
use Envsan\Modules\Sys\Model\Group;

class ModelUtil
{
    public static function build($params)
    {
        if($params==null)
            $params = array();

        $super = SessionData::isSuper();
        if($params==null) {
            $params = 'del_flag=0';
            if(!$super)
                $params .= ' and owner=' . SessionData::ownerId();
        }
        else if( is_array($params) ) {
            if (!isset($params[0]))
                $params[0] = '';

            if ($params[0] != '')
                $params[0] .= ' and ';

            $params[0] .= 'del_flag=0';
            if(!$super)
                $params[0] .= ' and owner=' . SessionData::ownerId();
        }
        else if( is_string($params) ) {
            if ($params != '')
                $params .= ' and ';
            $params .= 'del_flag=0';
            if(!$super)
                $params .= ' and owner=' . SessionData::ownerId();
        }

        return $params;
    }

    public static function softDelete($obj)
    {
        $obj->addBehavior(new SoftDelete(
            array(
                'field' => 'del_flag',
                'value' => '1'
            )
        ));
    }

    public static function checkOwner($owner)
    {
        if(SessionData::user()->owner!=$owner) {
            if(!SessionData::isSuper())
                die('OWNER不一致');
        }
    }

    /**
     * 对于list之类的数据进行组织的限定
     * @param $builder
     */
    public static function limitGroup($expr, $builder)
    {
        $ids = SessionData::groupIds();
        if ($ids==null)
            $ids=[0];
        $builder->inWhere($expr, $ids);
    }

    /**
     * 对于list之类的数据进行组织的限定
     * @param $builder
     */
    public static function limitCustomer($expr, $builder)
    {
        if (SessionData::isAdmin()){
            return;
        }
        $user = SessionData::user();
        if ($user->type == 1){
            $builder->andWhere("JSON_CONTAINS($expr,:user:)",['user'=>$user->id]);
        } else {
            $ids = SessionData::groupIds();
            if ($ids==null)
                $ids=[0];
            $group_expr = str_replace('market_user_id', 'group_id', $expr);
            $builder->inWhere($group_expr, $ids);
        }
    }

    /**
     * 页面组织筛选条件下拉列表
     */
    public static function getUserGroups(){
        $ids = SessionData::groupIds();
        if (empty($ids)) {
            $ids = [0];
        }
        $ids = array_values($ids);
        $groups = Group::find([
            'id IN ({id:array})',
            'bind' => [
                'id' => $ids
            ]
        ]);
        return $groups->toArray();
    }

    /**
     * owner限定
     * @param $builder
     */
    public static function limitOwner($expr, $builder)
    {
        $builder->andWhere("$expr=".SessionData::ownerId());
    }
}