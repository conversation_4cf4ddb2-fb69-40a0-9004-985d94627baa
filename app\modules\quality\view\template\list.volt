{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">检验模板管理</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="name" v-model="name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8 col-lg-9 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('quality/template/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                    <tr>
                        <th data-field="type" data-formatter="typeFormatter">模板类型</th>
                        <th data-field="name">名称</th>
                        <th data-field="remarks">备注</th>
                        <th data-formatter="actionFormatter">操作</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn blue dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu pull-right" role="menu">
            <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-pencil"></i> 编辑</a></li>
            <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-times"></i> 删除</a></li>
        </ul>
    </div>
</div>

<script>
    var types = {{ types | json_encode }};
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            name: '',
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.name = this.name;
                return p;
            },
            reset: function() {
                this.name = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable();
    function actionFormatter(v, row, idx) {
        return $('#act').html().replace(/@id@/g, row.uid);
    }
    
    function typeFormatter(v) {
        return types[v];
    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新增',
            type: 2,
            resize: false,
            area: ['100%', '100%'],
            content: '{{ url('quality/template/create') }}' ,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(id) {
        top.window.layer_result = '';
        top.layer.open({
            title: '编辑',
            type: 2,
            resize: false,
            area: ['100%', '100%'],
            content: '{{ url('quality/template/edit/') }}' + id,
            end: function() {
                $table.bootstrapTable('refresh');
            }
        });
    }

    function del(id) {
        var dlg = top.layer.confirm('确认删除吗？', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('quality/template/delete') }}", {uid: id}, function (rs) {
                closeSpin();
                if (rs.status == 'ok') {
                    toastr.success('操作成功！');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }
</script>