{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div class="row row-summary">
            <div class="col-md-12" style="text-align: right">
                <button type="button" class="btn blue btn-outline btn-circle" style="margin-right: 15px;" onclick="add()">
                    <i class="fa fa-plus"></i>&nbsp;<span>添加</span>
                </button>
            </div>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-url="{{ url('purchase/request/addnotice/json/' ~ notice_id  ~ '/' ~ product_id ~ '/' ~ ids) }}"
                   data-page-size="100"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-checkbox="true"></th>
                    <th data-field="code">物料编码</th>
                    <th data-field="name">物料名称</th>
                    <th data-field="model">规格型号</th>
                    <th data-field="stock_cnt">库存数量</th>
                    <th data-field="notice_quantity">生产需求总数量</th>
                    <th data-field="deputy_unit">库存单位</th>
                    <th data-field="supplier_name">供应商</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
        },
    });
    $table.bootstrapTable();

    function add() {
        var datas = $table.bootstrapTable('getSelections');
        if (datas.length == 0) {
            alertWarning('请选择添加明细');
            return;
        }

        top.window.layer_data2 = datas;
        top.window.layer_result2 = 'ok';
        top.layer.close(top.layer.getFrameIndex(window.name));
    }    

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>