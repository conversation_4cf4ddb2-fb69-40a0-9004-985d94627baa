{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vue.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/Sortable.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vuedraggable.umd.min.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-4">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">模板信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 72vh;overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>模板名称</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="name" v-model="name" required maxlength="50"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>模板类型</label>
                                        <div class="col-sm-8">
                                            <select class="bs-select form-control" name="type" v-model="type" required>
                                                <option value="">请选择</option>
                                                {% for key,val in types %}
                                                    <option value="{{ key }}">{{ val }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-8">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">检验项目</span>
                    </div>
                    <div class="actions" style="display: flex;">
                        <div style="width: 180px">
                            <select class="bs-select form-control" name="select_type" v-model="select_type">
                                <option value="">请选择</option>
                                {% for key,val in inputTypes %}
                                    <option value="{{ key }}">{{ val }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="button" class="btn yellow" @click="addItem">
                            <span>添加项目</span>
                        </button>
                    </div>
                </div>
                <div class="portlet-body" style="height: 80vh;overflow: auto">
                    <div style="display: flex;">
                        <div class="form-header-item" style="width: 5%">排序</div>
                        <div class="form-header-item" style="width: 10%">类型</div>
                        <div class="form-header-item" style="width: 8%">必须输入</div>
                        <div class="form-header-item" style="width: 8%">单位</div>
                        <div class="form-header-item" style="width: 8%">实测数量</div>
                        <div class="form-header-item" style="width: 18%">标题</div>
                        <div class="form-header-item" style="width: 18%">说明</div>
                        <div class="form-header-item" style="width: 20%">选项/公式</div>
                        <div class="form-header-item" style="width: 5%">操作</div>
                    </div>
                    <draggable tag="div" :list="form_data" group="form_data" handle=".handle">
                        <div
                                style="display: flex;"
                                v-for="item,idx in form_data"
                                :key="idx"
                        >
                            <div class="form-data-item" style="width: 5%">
                                <i class="fa fa-align-justify handle" style="margin-right: 10px"></i>
                            </div>
                            <div class="form-data-item" style="width: 10%" v-text="item.name"></div>
                            <div class="form-data-item" style="width: 8%">
                                <a @click="item.required == 0 ? item.required = 1 : item.required = 0">
                                    <i v-if="item.required == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                    <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                </a>
                            </div>
                            <div class="form-data-item" style="width: 8%">
                                <input v-if="item.type == 2 || item.type == 7 || item.type == 8" style="width:100%" type="text" class="form-control" placeholder="单位" :name="'unit_' + idx" v-model="item.unit">
                            </div>
                            <div class="form-data-item" style="width: 8%">
                                <input v-if="item.type == 2 || item.type == 6 || item.type == 7 || item.type == 8" style="width:100%" type="number" class="form-control" placeholder="实测数量" :name="'input_cnt_' + idx" v-model="item.input_cnt">
                            </div>
                            <div class="form-data-item" style="width: 18%">
                                <input style="width:100%" type="text" class="form-control" placeholder="请输入标题" :name="'title_' + idx" v-model="item.title">
                            </div>
                            <div class="form-data-item" style="width: 18%">
                                <input style="width:100%" type="text" class="form-control" placeholder="请输入说明" :name="'explain_' + idx" v-model="item.explain">
                            </div>
                            <div class="form-data-item" style="width: 20%">
                                <div v-if="item.type == 3 || item.type == 4" style="display: flex;flex-wrap: wrap">
                                    <div v-for="t,i in item.list" style="padding: 2px">
                                        <div style="width: 200px" class="input-group" style="padding: 3px;">
                                            <input placeholder="请输入选项名称" maxlength="100" type="text" class="form-control" v-model="item.list[i]" >
                                            <span class="input-group-addon">
                                                <a href="javascript:;" @click="item.list.splice(i,1)" style="color: red;font-size: 20px">
                                                    <i class="fa fa-times"></i>
                                                </a>
                                            </span>
                                        </div>
                                    </div>
                                    <a href="javascript:;" @click="item.list.push('')" style="color:#2b80e8 ;font-size: 20px">
                                        <i class="fa fa-plus-square"></i>
                                    </a>
                                </div>
                                <div v-if="item.type == 6">
                                    <div v-for="t,i in item.list" style="padding: 2px">
                                        <div style="width: 200px" class="input-group" style="padding: 3px;">
                                            <input type="text"  maxlength="50" class="form-control" v-model="item.list[i]" >
                                            <span class="input-group-addon" v-text="i==0 ? '合 格 项 名 称':'不合格项名称'"></span>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="item.type == 7">
                                    <span v-text="item.formula_val"></span>
                                    <a @click="openFormula(item)">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="form-data-item" style="width: 5%">
                                <a href="javascript:;" @click="form_data.splice(idx,1)" style="color: red;font-size: 20px">
                                    <i class="fa fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </draggable>
                </div>
            </div>
        </div>
    </div>
    <div class="input-layer bg-gray" style="display: none">
        <div class="portlet light">
            <div class="portlet-body">
                <div class="form-group">
                    <label>请输入计算公式</label>
                    <div style="margin-bottom: 10px;">
                        <input type="text" class="form-control" name="formula_val" v-model="formula_item.formula_val" readonly>
                    </div>
                    <div style="display: flex;flex-direction: row;flex-wrap: wrap;align-items: flex-start;">
                        <div class="input-group" style="padding: 3px;">
                            <input style="width: 120px" type="text" class="form-control" name="real_val" v-model="real_val" placeholder="实测值名称">
                            <span class="input-group-btn">
                                <button @click="addReal" type="button" class="btn blue btn-outline">实测值</button>
                            </span>
                        </div>
                    </div>
                    <div style="display: flex;flex-direction: row;flex-wrap: wrap;align-items: flex-start;">
                        <div v-for="input_item,input_idx in input_list" style="padding: 3px">
                            <button @click="keyInput(input_item)" type="button" class="btn blue btn-outline" v-text="input_item"></button>
                        </div>
                        <div style="padding: 3px;">
                            <button @click="delInput" type="button" class="btn red btn-outline">删除</button>
                        </div>
                        <div style="padding: 3px;">
                            <button @click="saveInput" type="button" class="btn green btn-outline">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
<script>
    var inputLayerIndex;
    var dataTemplate = {{ dataTemplate | json_encode }};
    var inputTypes = {{ inputTypes | json_encode }};
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        components: {
            draggable: window.vuedraggable
        },
        mounted: function() {

        },
        methods: {
            saveInput(){
                if (app.formula_item.formula_list.length > 0){
                    let val = '';
                    for(let item of app.formula_item.formula_list) {
                        if (item.t == 2 || item.t == 3) {
                            val += '1';
                        } else {
                            val += item.v;
                        }
                    }
                    try {
                        let res = eval(val);
                        toastr.success(res);
                        for(let item of app.form_data){
                            if (item.id == app.formula_item.id){
                                item.formula_list = JSON.parse(JSON.stringify(app.formula_item.formula_list));
                                item.formula_val = JSON.parse(JSON.stringify(app.formula_item.formula_val));
                                break;
                            }
                        }
                        layer.close(inputLayerIndex);
                    } catch (e){
                        toastr.error('计算公式不正确');
                        return;
                    }
                }
            },
            addStandard(){
                if (app.standard_val == ''){
                    return;
                }
                app.formula_item.formula_list.push({t:2,l:app.standard_val,v:''});
                app.standard_val = '';
                app.setFormulaVal();
            },
            addReal(){
                if (app.real_val == ''){
                    return;
                }
                for(let item of app.formula_item.formula_list) {
                    if (item.t == 3){
                        toastr.error('只能添加一个实际测值');
                        return;
                    }
                }
                app.formula_item.formula_list.push({t:3,l:app.real_val,v:''});
                app.real_val = '';
                app.setFormulaVal();
            },
            keyInput(val){
                app.formula_item.formula_list.push({t:1,l:val,v:val});
                app.setFormulaVal();
            },
            delInput(){
                if (app.formula_item.formula_list.length == 0){
                    return;
                }
                app.formula_item.formula_list.splice(app.formula_item.formula_list.length-1,1);
                app.setFormulaVal();
            },
            setFormulaVal(){
                let val = '';
                for(let item of app.formula_item.formula_list){
                    val += item.l;
                }
                app.formula_item.formula_val = val;
            },
            openFormula(item){
                app.real_val = '';
                app.formula_item = JSON.parse(JSON.stringify(item));
                inputLayerIndex = layer.open({
                    type: 1,
                    title: item.name,
                    area: ['40em', '30em'],
                    content: $(".input-layer"),
                    success: function(){
                        console.log('success');
                    },
                    end: function(){
                        console.log('end');
                    }
                });
            },
            addItem(){
                if (this.select_type == ''){
                    toastr.error('请选择项目类型');
                    return;
                }
                let data = JSON.parse(JSON.stringify(dataTemplate));
                data.id = getUuid2();
                data.type = this.select_type;
                data.name = inputTypes[this.select_type];
                if (data.type == 3 || data.type == 4){
                    data.list.push('');
                } else if (data.type == 6){
                    data.list.push('合格');
                    data.list.push('不合格');
                }
                this.form_data.push(data);
            },
            save(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                for(let form_item of app.form_data){
                    if (form_item.title == ''){
                        toastr.error('请输入标题');
                        return;
                    }
                    if (form_item.type == 3 || form_item.type == 4 || form_item.type == 6){
                        if (form_item.list.length == 0){
                            toastr.error('请添加选项');
                            return;
                        }
                        for(let t of form_item.list){
                            if (t == ''){
                                toastr.error('请输入选项名称');
                                return;
                            }
                        }
                    } else if (form_item.type == 7){
                        if (form_item.formula_list.length == 0){
                            toastr.error('请编辑计算公式');
                            return;
                        }
                    }
                }
                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('quality/template/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('quality/template/create') }}';
                {% endif %}
                showSpin();
                $.post(url, {
                    uid:app.uid,
                    name:app.name,
                    type:app.type,
                    remarks:app.remarks,
                    form_data:encodeURI(JSON.stringify(app.form_data)).replace(/\+/g,'%2B')
                }, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })

            }
        }
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>
<style>
    .form-header-item{
        color: #fff;
        background-color: #2b80e8;
        text-align: center;
        padding: 8px;
    }
    .form-data-item{
        text-align: center;
        padding: 8px;
        border-bottom: 1px #d2d2d2 solid;
        line-height: 35px;
    }
</style>