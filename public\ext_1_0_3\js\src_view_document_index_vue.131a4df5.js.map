{"version": 3, "file": "js/src_view_document_index_vue.131a4df5.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAyZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpsBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACTA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;AC9BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AGAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/document/index.vue", "webpack://sfp_ext/src/view/document/nested.vue", "webpack://sfp_ext/./src/view/document/index.vue", "webpack://sfp_ext/./src/view/document/nested.vue", "webpack://sfp_ext/./node_modules/core-js/modules/es.iterator.some.js", "webpack://sfp_ext/./src/view/document/index.vue?f9b3", "webpack://sfp_ext/./src/view/document/nested.vue?043a", "webpack://sfp_ext/./src/view/document/index.vue?82b5", "webpack://sfp_ext/./src/view/document/nested.vue?5c49", "webpack://sfp_ext/./src/view/document/index.vue?4e79", "webpack://sfp_ext/./src/view/document/index.vue?4b36", "webpack://sfp_ext/./src/view/document/index.vue?c383", "webpack://sfp_ext/./src/view/document/index.vue?d7aa", "webpack://sfp_ext/./src/view/document/nested.vue?f33e", "webpack://sfp_ext/./src/view/document/nested.vue?20d8", "webpack://sfp_ext/./src/view/document/nested.vue?3ded", "webpack://sfp_ext/./src/view/document/nested.vue?c7a0"], "sourcesContent": ["<template>\r\n    <div style=\"display: flex;flex-direction: row\">\r\n        <div style=\"width: 75%;background-color: #f2f2f2;min-height: 100vh;display: flex;flex-direction: row;justify-content: center\">\r\n            <div class=\"container-print-page\">\r\n                <div class=\"container1-page\" :style=\"{\r\n                    width:ele_data.width+'px',\r\n                    minHeight:ele_data.height+'px',\r\n                    borderWidth: ele_data.border[0]+'px '+ele_data.border[1]+'px '+ele_data.border[2]+'px '+ele_data.border[3]+'px',\r\n                    borderStyle : 'solid',\r\n                    borderColor : '#000',\r\n                    margin : ele_data.margin[0]+'px '+ele_data.margin[1]+'px '+ele_data.margin[2]+'px '+ele_data.margin[3]+'px'}\">\r\n                    <nested-draggable :tasks=\"ele_data.tasks\" :taskid=\"ele_data.id\" :select=\"select_element_id\" :styleProps=\"{width:ele_data.width+'px',minHeight:ele_data.height+'px'}\" :pid=\"''\"/>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div style=\"width: 25%\">\r\n            <div style=\"position: absolute;top: 0;right: 25vw\">\r\n                <div>\r\n                    <el-button type=\"warning\" plain @click=\"printPreview\">打印预览</el-button>\r\n                </div>\r\n                <div style=\"margin-top: 5px\">\r\n                    <el-button type=\"primary\" plain @click=\"save\">保存模板</el-button>\r\n                </div>\r\n            </div>\r\n            <div style=\"padding: 15px\">\r\n                <draggable\r\n                        class=\"list-group\"\r\n                        :list=\"controls\"\r\n                        :clone=\"cloneItem\"\r\n                        :group=\"{ name: 'element_gl', pull: 'clone', put: false }\"\r\n                >\r\n                    <div\r\n                            class=\"list-group-item\"\r\n                            v-for=\"element in controls\"\r\n                            :key=\"element.name\"\r\n                    >\r\n                        <el-input v-if=\"element.type == 'flex' || element.type == 'table'\" type=\"number\" v-model=\"element.cnt\">\r\n                            <template slot=\"prepend\">{{ element.name }}({{ element.type }})</template>\r\n                            <template slot=\"append\">{{ element.unit }}</template>\r\n                        </el-input>\r\n                        <div v-else>\r\n                            <el-button>{{ element.name }}({{ element.type }})</el-button>\r\n                        </div>\r\n                    </div>\r\n                </draggable>\r\n                <el-descriptions v-if=\"select_element_id == ''\" class=\"margin-top\" title=\"页面属性\" :column=\"1\" border>\r\n                    <el-descriptions-item label=\"页面宽度\" label-style=\"width:100px\">\r\n                        <el-input type=\"number\" v-model=\"ele_data.width\">\r\n                            <template slot=\"append\">PX</template>\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"页面高度\" label-style=\"width:100px\">\r\n                        <el-input type=\"number\" v-model=\"ele_data.height\">\r\n                            <template slot=\"append\">PX</template>\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"边框\">\r\n                        <div style=\"display: flex;flex-direction: row;\">\r\n                            <el-input type=\"number\" v-model=\"ele_data.border[0]\">\r\n                                <template slot=\"prepend\">上</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"ele_data.border[1]\">\r\n                                <template slot=\"prepend\">右</template>\r\n                            </el-input>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;margin-top: 5px\">\r\n                            <el-input type=\"number\" v-model=\"ele_data.border[2]\">\r\n                                <template slot=\"prepend\">下</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"ele_data.border[3]\">\r\n                                <template slot=\"prepend\">左</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"外间距\">\r\n                        <div style=\"display: flex;flex-direction: row;\">\r\n                            <el-input type=\"number\" v-model=\"ele_data.margin[0]\">\r\n                                <template slot=\"prepend\">上</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"ele_data.margin[1]\">\r\n                                <template slot=\"prepend\">右</template>\r\n                            </el-input>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;margin-top: 5px\">\r\n                            <el-input type=\"number\" v-model=\"ele_data.margin[2]\">\r\n                                <template slot=\"prepend\">下</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"ele_data.margin[3]\">\r\n                                <template slot=\"prepend\">左</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"按明细条数分页\" label-style=\"width:100px\">\r\n                        <el-select v-model=\"ele_data.page_detail_id\" filterable placeholder=\"请选择分页明细\">\r\n                            <el-option\r\n                                    v-for=\"(item,key) in bind_data\"\r\n                                    v-if = \"item.type == 99\"\r\n                                    :key=\"key\"\r\n                                    :label=\"item.name\"\r\n                                    :value=\"key\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"明细分页条数\" label-style=\"width:100px\">\r\n                        <el-input type=\"number\" v-model=\"ele_data.page_count\">\r\n                            <template slot=\"append\">条</template>\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"明细合计项目\" label-style=\"width:100px\">\r\n                        <el-select v-model=\"select_sum_id\" filterable placeholder=\"请选择分页明细\">\r\n                            <template v-for=\"(item,key) in bind_data\"\r\n                                        v-if = \"item.type == 99\">\r\n                                <el-option\r\n                                        v-for=\"(d_item,d_key) in bind_data[key]['data']\"\r\n                                        :key=\"d_key\"\r\n                                        :label=\"d_item.name +'('+item.name+')'\"\r\n                                        :value=\"key + '|' + d_key\">\r\n                                </el-option>\r\n                            </template>\r\n                        </el-select>\r\n                        <el-button type=\"primary\" size=\"small\" @click=\"sumAdd\" plain>添加</el-button>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"\">\r\n                        <div style=\"display: flex;\">\r\n                            <div style=\"width: 30%\">\r\n                                <span>合计项目</span>\r\n                            </div>\r\n                            <div style=\"width: 30%;text-align: center\">\r\n                                <span>是否</span><br>\r\n                                <span>分页合计</span>\r\n                            </div>\r\n                            <div style=\"width: 30%;text-align: center\">\r\n                                <span>是否</span><br>\r\n                                <span>总合计</span>\r\n                            </div>\r\n                            <div style=\"width: 10%\">\r\n                                <span>操作</span>\r\n                            </div>\r\n                        </div>\r\n                        <div v-for=\"(item,idx) in ele_data.sum_data\" :key=\"idx\" style=\"display: flex;\">\r\n                            <div style=\"width: 30%\">\r\n                                <span v-text=\"item.name\"></span>\r\n                            </div>\r\n                            <div style=\"width: 30%\">\r\n                                <el-radio v-model=\"item.page_flag\" label=\"否\">否</el-radio>\r\n                                <el-radio v-model=\"item.page_flag\" label=\"是\">是</el-radio>\r\n                            </div>\r\n                            <div style=\"width: 30%\">\r\n                                <el-radio v-model=\"item.sum_flag\" label=\"否\">否</el-radio>\r\n                                <el-radio v-model=\"item.sum_flag\" label=\"是\">是</el-radio>\r\n                            </div>\r\n                            <div style=\"width: 10%\">\r\n                                <i class=\"fa fa-times close\" style=\"color: red\" @click=\" ele_data.sum_data.splice(idx,1)\"></i>\r\n                            </div>\r\n                        </div>\r\n                    </el-descriptions-item>\r\n                </el-descriptions>\r\n                <el-descriptions v-else class=\"margin-top\" :title=\"'组件属性('+select_element.type+')'\" :column=\"1\" border style=\"height: 68vh;overflow-y: auto\">\r\n                    <template slot=\"extra\">\r\n                        <el-button type=\"default\" size=\"small\" @click=\"select_element_id=''\">关闭</el-button>\r\n                    </template>\r\n                    <el-descriptions-item v-if=\"select_element.type == 'table'\" label=\"表格数据源\" label-style=\"width:100px\">\r\n                        <el-select v-model=\"select_element.detail_id\" @change=\"bindTableData\" filterable placeholder=\"请选择绑定项目\">\r\n                            <el-option\r\n                                    v-for=\"(item,key) in bind_data\"\r\n                                    v-if = \"item.type == 99\"\r\n                                    :key=\"key\"\r\n                                    :label=\"item.name\"\r\n                                    :value=\"key\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item v-if=\"select_element.type == 'table'\"  label=\"是否显示表头\" label-style=\"width:100px\">\r\n                        <el-radio v-model=\"select_element.show_header\" label=\"1\">显示</el-radio>\r\n                        <el-radio v-model=\"select_element.show_header\" label=\"0\">不显示</el-radio>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item v-if=\"select_element.type == 'column'\"  label=\"列头名称\" label-style=\"width:100px\">\r\n                        <el-input\r\n                                type=\"textarea\"\r\n                                :rows=\"1\"\r\n                                placeholder=\"表头名称\"\r\n                                v-model=\"select_element.header_text\">\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item v-if=\"select_element.type == 'column'\"  label=\"是否序号列\" label-style=\"width:100px\">\r\n                        <el-radio v-model=\"select_element.is_number\" label=\"1\">是</el-radio>\r\n                        <el-radio v-model=\"select_element.is_number\" label=\"0\">否</el-radio>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item v-if=\"select_element.type == 'column' && select_element.is_number != 1 && select_element.detail_id != ''\"  label=\"绑定数据\" label-style=\"width:100px\">\r\n                        <div>\r\n                            <el-radio v-model=\"input_type\" label=\"1\">文本输入</el-radio>\r\n                            <el-radio v-model=\"input_type\" label=\"2\">绑定数据</el-radio>\r\n                            <el-button type=\"primary\" size=\"small\" @click=\"textAdd\" plain>添加</el-button>\r\n                        </div>\r\n                        <draggable tag=\"ul\" :list=\"select_element.texts\" group=\"bind_data\" class=\"list-group\" handle=\".handle\">\r\n                            <li\r\n                                    class=\"list-group-item\"\r\n                                    v-for=\"(element, idx) in select_element.texts\"\r\n                                    :key=\"idx\"\r\n                            >\r\n                                <div style=\"display: flex\">\r\n                                    <i class=\"fa fa-align-justify handle\" style=\"margin-right: 10px\"></i>\r\n                                    <el-input\r\n                                            style=\"width: 80%\"\r\n                                            v-if=\"element.type == 1\"\r\n                                            type=\"textarea\"\r\n                                            :rows=\"1\"\r\n                                            placeholder=\"文本内容\"\r\n                                            v-model=\"element.text\">\r\n                                    </el-input>\r\n                                    <el-select v-else v-model=\"element.data_id\" @change=\"bindDetailDataChange(element)\" filterable placeholder=\"请选择绑定项目\">\r\n                                        <el-option\r\n                                                v-for=\"(item,key) in bind_data[select_element.detail_id]['data']\"\r\n                                                v-if = \"item.type == 1\"\r\n                                                :key=\"key\"\r\n                                                :label=\"item.name\"\r\n                                                :value=\"key\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                    <el-input type=\"text\" v-model=\"element.spacing\" placeholder=\"间距\" style=\"width: 80px\"></el-input>\r\n                                    <i class=\"fa fa-times close\" style=\"color: red\" @click=\"select_element.texts.splice(idx,1)\"></i>\r\n                                </div>\r\n                            </li>\r\n                        </draggable>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item v-if=\"select_element.tasks.length == 0 && select_element.type == 'flex'\" label=\"文本内容\" label-style=\"width:100px\">\r\n                        <div>\r\n                            <el-radio v-model=\"input_type\" label=\"1\">文本输入</el-radio>\r\n                            <el-radio v-model=\"input_type\" label=\"2\">绑定数据</el-radio>\r\n                            <el-button type=\"primary\" size=\"small\" @click=\"textAdd\" plain>添加</el-button>\r\n                        </div>\r\n                        <draggable tag=\"ul\" :list=\"select_element.texts\" group=\"bind_data\" class=\"list-group\" handle=\".handle\">\r\n                            <li\r\n                                    class=\"list-group-item\"\r\n                                    v-for=\"(element, idx) in select_element.texts\"\r\n                                    :key=\"idx\"\r\n                            >\r\n                                <div style=\"display: flex\">\r\n                                    <i class=\"fa fa-align-justify handle\" style=\"margin-right: 10px\"></i>\r\n                                    <el-input\r\n                                            style=\"width: 80%\"\r\n                                            v-if=\"element.type == 1\"\r\n                                            type=\"textarea\"\r\n                                            :rows=\"1\"\r\n                                            placeholder=\"文本内容\"\r\n                                            v-model=\"element.text\">\r\n                                    </el-input>\r\n                                    <el-select v-else v-model=\"element.data_id\" @change=\"bindDataChange(element)\" filterable placeholder=\"请选择绑定项目\">\r\n                                        <el-option\r\n                                                v-for=\"(item,key) in bind_data\"\r\n                                                v-if = \"item.type == 1\"\r\n                                                :key=\"key\"\r\n                                                :label=\"item.name\"\r\n                                                :value=\"key\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                    <el-input type=\"text\" v-model=\"element.spacing\" placeholder=\"间距\" style=\"width: 80px\">\r\n                                    </el-input>\r\n                                    <i class=\"fa fa-times close\" style=\"color: red\" @click=\"select_element.texts.splice(idx,1)\"></i>\r\n                                </div>\r\n                            </li>\r\n                        </draggable>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item v-if=\"select_element.type == 'image'\" label=\"图片\" label-style=\"width:100px\">\r\n                        <el-upload\r\n                                class=\"avatar-uploader\"\r\n                                :action=\"file_upload_server\"\r\n                                :show-file-list=\"false\"\r\n                                :on-success=\"handleAvatarSuccess\">\r\n                            <img v-if=\"select_element.path != ''\" :src=\"select_element.path\" class=\"avatar\">\r\n                            <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n                        </el-upload>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item v-if=\"select_element.type == 'barcode'\" label=\"二维码\" label-style=\"width:100px\">\r\n                        <el-select v-model=\"select_element.data_id\" @change=\"bindDataChange(element)\" filterable placeholder=\"请选择二维码\">\r\n                            <el-option\r\n                                    v-for=\"(item,key) in bind_data\"\r\n                                    v-if = \"item.type == 2\"\r\n                                    :key=\"key\"\r\n                                    :label=\"item.name\"\r\n                                    :value=\"key\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"宽度\" label-style=\"width:100px\">\r\n                        <el-input type=\"number\" v-model=\"select_element.width\">\r\n                            <template slot=\"append\">PX</template>\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"高度\" label-style=\"width:100px\">\r\n                        <el-input type=\"number\" v-model=\"select_element.height\">\r\n                            <template slot=\"append\">PX</template>\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"字体大小\" label-style=\"width:100px\">\r\n                        <el-input type=\"number\" v-model=\"select_element.fontSize\">\r\n                            <template slot=\"append\">PX</template>\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"字体加粗\" label-style=\"width:100px\">\r\n                        <el-radio-group v-model=\"select_element.fontWeight\">\r\n                            <el-radio-button label=\"bold\"></el-radio-button>\r\n                            <el-radio-button label=\"normal\"></el-radio-button>\r\n                        </el-radio-group>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"布局方式\" label-style=\"width:100px\">\r\n                        <el-radio-group v-model=\"select_element.flexDirection\" @change=\"changeDirection\">\r\n                            <el-radio-button label=\"row\"></el-radio-button>\r\n                            <el-radio-button label=\"column\"></el-radio-button>\r\n                        </el-radio-group>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"对齐方式\" label-style=\"width:100px\">\r\n                        <el-radio-group v-model=\"select_element.justifyContent\">\r\n                            <el-radio-button label=\"flex-start\"></el-radio-button>\r\n                            <el-radio-button label=\"flex-end\"></el-radio-button>\r\n                            <el-radio-button label=\"center\"></el-radio-button>\r\n                            <el-radio-button label=\"space-between\"></el-radio-button>\r\n                            <el-radio-button label=\"space-around\"></el-radio-button>\r\n                        </el-radio-group>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"边框\">\r\n                        <div style=\"display: flex;flex-direction: row;\">\r\n                            <el-input type=\"number\" v-model=\"select_element.border[0]\">\r\n                                <template slot=\"prepend\">上</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"select_element.border[1]\">\r\n                                <template slot=\"prepend\">右</template>\r\n                            </el-input>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;margin-top: 5px\">\r\n                            <el-input type=\"number\" v-model=\"select_element.border[2]\">\r\n                                <template slot=\"prepend\">下</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"select_element.border[3]\">\r\n                                <template slot=\"prepend\">左</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"外间距\">\r\n                        <div style=\"display: flex;flex-direction: row;\">\r\n                            <el-input type=\"number\" v-model=\"select_element.margin[0]\">\r\n                                <template slot=\"prepend\">上</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"select_element.margin[1]\">\r\n                                <template slot=\"prepend\">右</template>\r\n                            </el-input>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;margin-top: 5px\">\r\n                            <el-input type=\"number\" v-model=\"select_element.margin[2]\">\r\n                                <template slot=\"prepend\">下</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"select_element.margin[3]\">\r\n                                <template slot=\"prepend\">左</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"内间距\">\r\n                        <div style=\"display: flex;flex-direction: row;\">\r\n                            <el-input type=\"number\" v-model=\"select_element.padding[0]\">\r\n                                <template slot=\"prepend\">上</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"select_element.padding[1]\">\r\n                                <template slot=\"prepend\">右</template>\r\n                            </el-input>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;margin-top: 5px\">\r\n                            <el-input type=\"number\" v-model=\"select_element.padding[2]\">\r\n                                <template slot=\"prepend\">下</template>\r\n                            </el-input>\r\n                            <el-input type=\"number\" v-model=\"select_element.padding[3]\">\r\n                                <template slot=\"prepend\">左</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"背景颜色\" label-style=\"width:100px\">\r\n                        <div style=\"display: flex;flex-direction: row;\">\r\n                            <el-color-picker v-model=\"select_element.backgroundColor\"></el-color-picker>\r\n                            <el-select v-if=\"select_element.type == 'flex'\" v-model=\"select_element.bg_id\" filterable placeholder=\"请选择绑定项目颜色\">\r\n                                <el-option\r\n                                        v-for=\"(item,key) in bind_data\"\r\n                                        v-if = \"item.type == 3\"\r\n                                        :key=\"key\"\r\n                                        :label=\"item.name\"\r\n                                        :value=\"key\">\r\n                                </el-option>\r\n                            </el-select>\r\n                            <el-select v-if=\"select_element.type == 'column' && select_element.detail_id != ''\" v-model=\"select_element.bg_id\" filterable placeholder=\"请选择绑定颜色\">\r\n                                <el-option\r\n                                        v-for=\"(item,key) in bind_data[select_element.detail_id]['data']\"\r\n                                        v-if = \"item.type == 3\"\r\n                                        :key=\"key\"\r\n                                        :label=\"item.name\"\r\n                                        :value=\"key\">\r\n                                </el-option>\r\n                            </el-select>\r\n                        </div>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"边框圆角\" label-style=\"width:100px\">\r\n                        <el-input type=\"number\" v-model=\"select_element.borderRadius\">\r\n                            <template slot=\"append\">PX</template>\r\n                        </el-input>\r\n                    </el-descriptions-item>\r\n                </el-descriptions>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import Config from \"../../config\";\r\n    import draggable from \"vuedraggable\";\r\n    import nestedDraggable from \"./nested\";\r\n    export default {\r\n        name: \"document\",\r\n        display: \"document\",\r\n        order: 15,\r\n        components: {\r\n            nestedDraggable,draggable\r\n        },\r\n        data() {\r\n            return {\r\n                qrText: 'https://example.com',\r\n                file_upload_server: Config.host + 'api/printing/common/upload',\r\n                uid : '',\r\n                controls:[\r\n                    {\r\n                        id: this.getUuid(),\r\n                        pid:'',\r\n                        detail_id:'',\r\n                        bg_id:'',\r\n                        show_header:'0',\r\n                        is_number:'0',\r\n                        header_text:'',\r\n                        type:'flex',\r\n                        name:'单元项目',\r\n                        unit:'个',\r\n                        cnt:1,\r\n                        width: 200,\r\n                        height: 30,\r\n                        fontSize: 14,\r\n                        fontWeight: 'normal',\r\n                        textAlign: 'center',\r\n                        flexDirection: 'row',\r\n                        justifyContent: 'flex-start',\r\n                        border: [0,0,0,0],\r\n                        margin: [0,0,0,0],\r\n                        padding: [0,0,0,0],\r\n                        backgroundColor:'',\r\n                        borderRadius: 0,\r\n                        texts:[],\r\n                        tasks:[]\r\n                    },\r\n                    {\r\n                        id: this.getUuid(),\r\n                        pid:'',\r\n                        detail_id:'',\r\n                        bg_id:'',\r\n                        show_header:'1',\r\n                        is_number:'0',\r\n                        header_text:'',\r\n                        type:'table',\r\n                        name:'明细表格',\r\n                        cnt:2,\r\n                        unit:'列',\r\n                        width: 200,\r\n                        height: 30,\r\n                        fontSize: 14,\r\n                        fontWeight: 'normal',\r\n                        textAlign: 'center',\r\n                        flexDirection: 'row',\r\n                        justifyContent: 'flex-start',\r\n                        border: [0,0,0,0],\r\n                        margin: [0,0,0,0],\r\n                        padding: [0,0,0,0],\r\n                        backgroundColor:'',\r\n                        borderRadius: 0,\r\n                        texts:[],\r\n                        tasks:[]\r\n                    },\r\n                    {\r\n                        id: this.getUuid(),\r\n                        pid:'',\r\n                        detail_id:'',\r\n                        bg_id:'',\r\n                        show_header:'0',\r\n                        is_number:'0',\r\n                        header_text:'',\r\n                        type:'image',\r\n                        path:'',\r\n                        name:'图片',\r\n                        unit:'个',\r\n                        cnt:1,\r\n                        width: 200,\r\n                        height: 150,\r\n                        fontSize: 14,\r\n                        fontWeight: 'normal',\r\n                        textAlign: 'center',\r\n                        flexDirection: 'row',\r\n                        justifyContent: 'flex-start',\r\n                        border: [0,0,0,0],\r\n                        margin: [0,0,0,0],\r\n                        padding: [0,0,0,0],\r\n                        backgroundColor:'#ffffff',\r\n                        borderRadius: 0,\r\n                        texts:[],\r\n                        tasks:[]\r\n                    },\r\n                    {\r\n                        id: this.getUuid(),\r\n                        pid:'',\r\n                        detail_id:'',\r\n                        bg_id:'',\r\n                        show_header:'0',\r\n                        is_number:'0',\r\n                        header_text:'',\r\n                        type:'barcode',\r\n                        name:'二维码',\r\n                        data_id:'',\r\n                        unit:'个',\r\n                        cnt:1,\r\n                        width: 200,\r\n                        height: 150,\r\n                        fontSize: 14,\r\n                        fontWeight: 'normal',\r\n                        textAlign: 'center',\r\n                        flexDirection: 'row',\r\n                        justifyContent: 'flex-start',\r\n                        border: [0,0,0,0],\r\n                        margin: [0,0,0,0],\r\n                        padding: [0,0,0,0],\r\n                        backgroundColor:'#ffffff',\r\n                        borderRadius: 0,\r\n                        texts:[],\r\n                        tasks:[]\r\n                    }\r\n                ],\r\n                ele_data: {\r\n                    id: 'page',\r\n                    name:'',\r\n                    width : 750,\r\n                    height : 50,\r\n                    page_detail_id:'',\r\n                    page_count:'',\r\n                    sum_data:[],\r\n                    border: [0,0,0,0],\r\n                    margin: [20,20,20,20],\r\n                    tasks:[]\r\n                },\r\n                select_element_id : '',\r\n                select_element : {\r\n                    border: [0,0,0,0],\r\n                    margin: [0,0,0,0],\r\n                    padding: [0,0,0,0],\r\n                    texts:[],\r\n                },\r\n                bind_data:{},\r\n                input_type : '1',\r\n                new_task_id : '',\r\n                select_sum_id:'',\r\n            };\r\n        },\r\n        created(){\r\n            this.uid = this.$route.query.uid || '';\r\n            this.init(this.uid);\r\n            this.$hub.$on('update:tasks', (value,taskid,pid) => {\r\n                let new_value = [];\r\n                for (let val of value){\r\n                    if (val !== undefined){\r\n                        new_value.push(val);\r\n                    }\r\n                }\r\n                this.getElement(this.ele_data,taskid,(ele)=>{\r\n                    let width = ele.width;\r\n                    ele.tasks = new_value;\r\n                    ele.pid = pid;\r\n                    let new_width = 0;\r\n                    if (ele.flexDirection == 'row'){\r\n                        new_width = Math.round(width / ele.tasks.length);\r\n                    } else {\r\n                        new_width = width;\r\n                    }\r\n                    for (let task of ele.tasks){\r\n                        task.width = new_width;\r\n                        if (task.id == this.new_task_id){\r\n                            if (ele.type == 'table'){\r\n                                task.type = 'column';\r\n                                task.cnt = 1;\r\n                            }\r\n                            task.padding = [5,0,0,0];\r\n                            if (task.cnt > 1){\r\n                                let control_type = task.type;\r\n                                if (task.type == 'table'){\r\n                                    control_type = 'column'\r\n                                }\r\n                                let w = Math.round((task.width )/ task.cnt);\r\n                                for(let i = 0 ; i < task.cnt ; i++){\r\n                                    task.tasks.push(\r\n                                        { ...task, id:this.getUuid(),type:control_type,width:w,texts:[],tasks:[], border: [0,0,0,0],\r\n                                            margin: [0,0,0,0],\r\n                                            padding: [5,0,0,0]}\r\n                                    )\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    this.new_task_id = '';\r\n                })\r\n            });\r\n            this.$hub.$on('delete:tasks', (task_id) => {\r\n                this.select_element_id = '';\r\n                this.deleteElement(this.ele_data,task_id);\r\n            });\r\n            this.$hub.$on('select:tasks', (task_id) => {\r\n                this.taskClick(task_id);\r\n            });\r\n        },\r\n        methods: {\r\n            init(uid){\r\n                this.$http.post('printing/template/init', {uid:uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let data = rs.data;\r\n                        if (data.form_data != null) {\r\n                            this.ele_data = data.form_data;\r\n                        }\r\n                        this.bind_data = data.bind_data;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            save(){\r\n                this.$http.post('printing/template/save', {\r\n                    uid:this.uid,\r\n                    form_data :encodeURI(JSON.stringify(this.ele_data)).replace(/\\+/g,'%2B')\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.$message.success('保存成功');\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            changeDirection(){\r\n                if (this.select_element.flexDirection == 'row'){\r\n                    this.select_element.width / this.select_element.tasks.length;\r\n                    let w = Math.round((this.select_element.width) / this.select_element.tasks.length);\r\n                    for(let task of this.select_element.tasks){\r\n                        task.width = w;\r\n                    }\r\n                } else {\r\n                    for(let task of this.select_element.tasks){\r\n                        task.width = Math.round(this.select_element.width);\r\n                    }\r\n                }\r\n            },\r\n            taskClick(task_id){\r\n                this.getElement(this.ele_data,task_id,(ele)=>{\r\n                    this.select_element_id = ele.id;\r\n                    this.select_element = ele;\r\n                })\r\n            },\r\n            bindDataChange(ele){\r\n                ele.text = this.bind_data[ele.data_id].name;\r\n            },\r\n            bindDetailDataChange(ele){\r\n                ele.text = this.bind_data[this.select_element.detail_id]['data'][ele.data_id].name;\r\n            },\r\n            bindTableData(){\r\n                for(let item of this.select_element.tasks){\r\n                    item.detail_id = this.select_element.detail_id;\r\n                }\r\n            },\r\n            textAdd(){\r\n                this.select_element.texts.push({\r\n                    data_id:'',\r\n                    type:this.input_type,\r\n                    text:'',\r\n                    spacing: ''\r\n                })\r\n            },\r\n            getElement(ele_data,task_id,callback){\r\n                if (task_id == ele_data.id){\r\n                    callback(ele_data);\r\n                    return;\r\n                }\r\n                for (let item of ele_data.tasks) {\r\n                    this.getElement(item,task_id,callback);\r\n                }\r\n            },\r\n            deleteElement(ele_data,task_id,idx=null,tasks=null){\r\n                if (task_id == ele_data.id){\r\n                    if (idx !== null && tasks !== null){\r\n                        tasks.splice(idx,1);\r\n                    }\r\n                    return;\r\n                }\r\n                for (let i = 0; i < ele_data.tasks.length ; i++) {\r\n                    this.deleteElement(ele_data.tasks[i],task_id,i,ele_data.tasks);\r\n                }\r\n            },\r\n            printLog(){\r\n                console.log(this.ele_data);\r\n            },\r\n            cloneItem(item) {\r\n                this.new_task_id = this.getUuid();\r\n                return { ...item, id:this.new_task_id,texts:[],tasks:[], border: [0,0,0,0],\r\n                    margin: [0,0,0,0],\r\n                    padding: [5,0,0,0]}; // 确保新id\r\n            },\r\n            getUuid(){\r\n                var s = [];\r\n                var hexDigits = \"0123456789abcdef\";\r\n                var firstDigits = \"abcdefghij\";\r\n                s[0] = firstDigits.substr(Math.floor(Math.random() * 0x9), 1);\r\n                for (var i = 1; i < 20; i++) {\r\n                    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);\r\n                }\r\n                return s.join(\"\");\r\n            },\r\n            printPreview(){\r\n                const iframe  = document.createElement(\"iframe\");\r\n                const f  = document.body.appendChild(iframe);\r\n                iframe.id = \"myIframe\";\r\n                iframe.setAttribute(\r\n                    \"style\",\r\n                    \"position:absolute;width:0;height:0;top:-10px;left:-10px;\"\r\n                );\r\n                const w = f.contentWindow || f.contentDocument;\r\n                // eslint-disable-next-line prefer-const\r\n                const doc = f.contentDocument || f.contentWindow.document;\r\n                doc.open();\r\n                doc.write($('.container-print-page').html());\r\n                doc.close();\r\n                w.print();\r\n            },\r\n            sumAdd(){\r\n                if (this.select_sum_id == ''){\r\n                    return;\r\n                }\r\n                let keys = this.select_sum_id.split('|');\r\n                if (this.ele_data.sum_data.some(item => item.item_id == keys[1] && item.detail_id == keys[0])){\r\n                    this.select_sum_id = '';\r\n                    return;\r\n                }\r\n                this.ele_data.sum_data.push({\r\n                    detail_id : keys[0],\r\n                    item_id : keys[1],\r\n                    name : this.bind_data[keys[0]]['data'][keys[1]]['name'] + '('+this.bind_data[keys[0]]['name']+ ')',\r\n                    page_flag : '否',\r\n                    sum_flag : '否'\r\n                });\r\n                this.select_sum_id = '';\r\n            },\r\n            handleAvatarSuccess(rs, file) {\r\n                if (rs.status == 'ok') {\r\n                    this.$message.success('保存成功');\r\n                    this.select_element.path = rs.path + rs.file_name;\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style scoped>\r\n    .container-print-page{\r\n        background-color: #fff;\r\n        text-align: center;\r\n        margin-top: 10px;\r\n        padding: 5px;\r\n    }\r\n    .container1-page{\r\n        background-color: #fff;\r\n    }\r\n\r\n    .dragArea {\r\n        min-height: 50px;\r\n        outline: 1px dashed;\r\n        padding-bottom: 10px;\r\n    }\r\n\r\n    .avatar-uploader .el-upload {\r\n        border: 1px dashed #d9d9d9;\r\n        border-radius: 6px;\r\n        cursor: pointer;\r\n        position: relative;\r\n        overflow: hidden;\r\n    }\r\n    .avatar-uploader .el-upload:hover {\r\n        border-color: #409EFF;\r\n    }\r\n    .avatar-uploader-icon {\r\n        font-size: 28px;\r\n        color: #8c939d;\r\n        width: 178px;\r\n        height: 178px;\r\n        line-height: 178px;\r\n        text-align: center;\r\n    }\r\n    .avatar {\r\n        width: 178px;\r\n        height: 178px;\r\n        display: block;\r\n    }\r\n</style>\r\n", "<template>\r\n    <draggable\r\n            class=\"dragArea\"\r\n            v-model=\"safeList\"\r\n            group=\"element_gl\"\r\n            :style=\"styleProps\"\r\n    >\r\n        <div v-for=\"item in safeList\" :key=\"item.id\" class=\"columu-item\"\r\n             @click.stop=\"taskClick(item.id)\"\r\n             :style=\"{\r\n                    display: 'flex',\r\n                    backgroundColor: select == item.id ? '#FFEBE1' : item.backgroundColor,\r\n                    width: item.width + 'px',\r\n                    minHeight: item.height + 'px',\r\n                    fontSize: item.fontSize + 'px',\r\n                    fontWeight: item.fontWeight,\r\n                    textAlign: item.textAlign,\r\n                    flexDirection: item.flexDirection,\r\n                    justifyContent: item.justifyContent,\r\n                    borderWidth: item.border[0]+'px '+item.border[1]+'px '+item.border[2]+'px '+item.border[3]+'px',\r\n                    borderStyle: 'solid',\r\n                    borderColor: '#000',\r\n                    borderRadius: item.borderRadius + 'px',\r\n                    margin: item.margin[0]+'px '+item.margin[1]+'px '+item.margin[2]+'px '+item.margin[3]+'px',\r\n                    padding: item.padding[0]+'px '+item.padding[1]+'px '+item.padding[2]+'px '+item.padding[3]+'px'\r\n                }\"\r\n        >\r\n            <div style=\"position: absolute;top: -12px;right: -5px;z-index: 999\">\r\n                <a @click.stop=\"deleteTask(item.id)\"><i class=\"fa fa-close\" style=\"color: red;cursor: pointer;\" ></i></a>\r\n            </div>\r\n            <div>\r\n                <span v-for=\"(text,text_idx) in item.texts\" :key=\"text_idx\" v-text=\"text.text\" :style=\"{marginRight: text.spacing && text.spacing != '' ? text.spacing +'px':0}\"></span>\r\n            </div>\r\n            <template v-if=\"item.texts.length == 0 && item.type != 'column' && item.type != 'image' && item.type != 'barcode'\">\r\n                <nested-draggable :tasks=\"item.tasks\" :taskid=\"item.id\" :pid=\"taskid\" :select=\"select\" :styleProps=\"{\r\n                       display: 'flex',\r\n                       width: item.width + 'px',\r\n                       flexDirection: item.flexDirection,\r\n                       justifyContent: item.justifyContent,\r\n                }\" />\r\n            </template>\r\n            <template v-if=\"item.type == 'image'\">\r\n                <img v-if=\"item.path != ''\" :src=\"item.path\" alt=\"\" :style=\"{\r\n                    width: item.width + 'px',\r\n                    height: item.height + 'px'\r\n                }\"/>\r\n            </template>\r\n            <template v-if=\"item.type == 'barcode'\">\r\n                <img src=\"data:image/png;base64,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\" alt=\"\" :style=\"{\r\n                    width: item.width + 'px',\r\n                    height: item.height + 'px'\r\n                }\"/>\r\n            </template>\r\n        </div>\r\n    </draggable>\r\n</template>\r\n\r\n<script>\r\n    import draggable from 'vuedraggable';\r\n\r\n    export default {\r\n        name: \"nested-draggable\",\r\n        props: {\r\n            styleProps: {\r\n                required: true,\r\n                type: Object\r\n            },\r\n            select: {\r\n                required: true,\r\n                type: String\r\n            },\r\n            taskid: {\r\n                required: true,\r\n                type: String\r\n            },\r\n            pid: {\r\n                required: true,\r\n                type: String\r\n            },\r\n            tasks: {\r\n                required: true,\r\n                type: Array,\r\n                default: () => []\r\n            }\r\n        },\r\n        components: { draggable },\r\n        computed: {\r\n            safeList: {\r\n                get() {\r\n                    return this.tasks;\r\n                },\r\n                set(value) {\r\n                    this.$hub.$emit('update:tasks', value,this.taskid,this.pid)\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            deleteTask(id){\r\n                this.$hub.$emit('delete:tasks',id)\r\n            },\r\n            taskClick(id){\r\n                this.$hub.$emit('select:tasks',id);\r\n            }\r\n        }\r\n\r\n    };\r\n</script>\r\n\r\n<style scoped>\r\n    .dragArea {\r\n        min-height: 10px;\r\n    }\r\n    .columu-item {\r\n        position: relative;\r\n        cursor: pointer;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"width\":\"75%\",\"background-color\":\"#f2f2f2\",\"min-height\":\"100vh\",\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\"}},[_c('div',{staticClass:\"container-print-page\"},[_c('div',{staticClass:\"container1-page\",style:({\n                width:_vm.ele_data.width+'px',\n                minHeight:_vm.ele_data.height+'px',\n                borderWidth: _vm.ele_data.border[0]+'px '+_vm.ele_data.border[1]+'px '+_vm.ele_data.border[2]+'px '+_vm.ele_data.border[3]+'px',\n                borderStyle : 'solid',\n                borderColor : '#000',\n                margin : _vm.ele_data.margin[0]+'px '+_vm.ele_data.margin[1]+'px '+_vm.ele_data.margin[2]+'px '+_vm.ele_data.margin[3]+'px'})},[_c('nested-draggable',{attrs:{\"tasks\":_vm.ele_data.tasks,\"taskid\":_vm.ele_data.id,\"select\":_vm.select_element_id,\"styleProps\":{width:_vm.ele_data.width+'px',minHeight:_vm.ele_data.height+'px'},\"pid\":''}})],1)])]),_c('div',{staticStyle:{\"width\":\"25%\"}},[_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"0\",\"right\":\"25vw\"}},[_c('div',[_c('el-button',{attrs:{\"type\":\"warning\",\"plain\":\"\"},on:{\"click\":_vm.printPreview}},[_vm._v(\"打印预览\")])],1),_c('div',{staticStyle:{\"margin-top\":\"5px\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":_vm.save}},[_vm._v(\"保存模板\")])],1)]),_c('div',{staticStyle:{\"padding\":\"15px\"}},[_c('draggable',{staticClass:\"list-group\",attrs:{\"list\":_vm.controls,\"clone\":_vm.cloneItem,\"group\":{ name: 'element_gl', pull: 'clone', put: false }}},_vm._l((_vm.controls),function(element){return _c('div',{key:element.name,staticClass:\"list-group-item\"},[(element.type == 'flex' || element.type == 'table')?_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(element.cnt),callback:function ($$v) {_vm.$set(element, \"cnt\", $$v)},expression:\"element.cnt\"}},[_c('template',{slot:\"prepend\"},[_vm._v(_vm._s(element.name)+\"(\"+_vm._s(element.type)+\")\")]),_c('template',{slot:\"append\"},[_vm._v(_vm._s(element.unit))])],2):_c('div',[_c('el-button',[_vm._v(_vm._s(element.name)+\"(\"+_vm._s(element.type)+\")\")])],1)],1)}),0),(_vm.select_element_id == '')?_c('el-descriptions',{staticClass:\"margin-top\",attrs:{\"title\":\"页面属性\",\"column\":1,\"border\":\"\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"页面宽度\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.width),callback:function ($$v) {_vm.$set(_vm.ele_data, \"width\", $$v)},expression:\"ele_data.width\"}},[_c('template',{slot:\"append\"},[_vm._v(\"PX\")])],2)],1),_c('el-descriptions-item',{attrs:{\"label\":\"页面高度\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.height),callback:function ($$v) {_vm.$set(_vm.ele_data, \"height\", $$v)},expression:\"ele_data.height\"}},[_c('template',{slot:\"append\"},[_vm._v(\"PX\")])],2)],1),_c('el-descriptions-item',{attrs:{\"label\":\"边框\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.border[0]),callback:function ($$v) {_vm.$set(_vm.ele_data.border, 0, $$v)},expression:\"ele_data.border[0]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"上\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.border[1]),callback:function ($$v) {_vm.$set(_vm.ele_data.border, 1, $$v)},expression:\"ele_data.border[1]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"右\")])],2)],1),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"margin-top\":\"5px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.border[2]),callback:function ($$v) {_vm.$set(_vm.ele_data.border, 2, $$v)},expression:\"ele_data.border[2]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"下\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.border[3]),callback:function ($$v) {_vm.$set(_vm.ele_data.border, 3, $$v)},expression:\"ele_data.border[3]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"左\")])],2)],1)]),_c('el-descriptions-item',{attrs:{\"label\":\"外间距\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.margin[0]),callback:function ($$v) {_vm.$set(_vm.ele_data.margin, 0, $$v)},expression:\"ele_data.margin[0]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"上\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.margin[1]),callback:function ($$v) {_vm.$set(_vm.ele_data.margin, 1, $$v)},expression:\"ele_data.margin[1]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"右\")])],2)],1),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"margin-top\":\"5px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.margin[2]),callback:function ($$v) {_vm.$set(_vm.ele_data.margin, 2, $$v)},expression:\"ele_data.margin[2]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"下\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.margin[3]),callback:function ($$v) {_vm.$set(_vm.ele_data.margin, 3, $$v)},expression:\"ele_data.margin[3]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"左\")])],2)],1)]),_c('el-descriptions-item',{attrs:{\"label\":\"按明细条数分页\",\"label-style\":\"width:100px\"}},[_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择分页明细\"},model:{value:(_vm.ele_data.page_detail_id),callback:function ($$v) {_vm.$set(_vm.ele_data, \"page_detail_id\", $$v)},expression:\"ele_data.page_detail_id\"}},_vm._l((_vm.bind_data),function(item,key){return (item.type == 99)?_c('el-option',{key:key,attrs:{\"label\":item.name,\"value\":key}}):_vm._e()}),1)],1),_c('el-descriptions-item',{attrs:{\"label\":\"明细分页条数\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.ele_data.page_count),callback:function ($$v) {_vm.$set(_vm.ele_data, \"page_count\", $$v)},expression:\"ele_data.page_count\"}},[_c('template',{slot:\"append\"},[_vm._v(\"条\")])],2)],1),_c('el-descriptions-item',{attrs:{\"label\":\"明细合计项目\",\"label-style\":\"width:100px\"}},[_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择分页明细\"},model:{value:(_vm.select_sum_id),callback:function ($$v) {_vm.select_sum_id=$$v},expression:\"select_sum_id\"}},[_vm._l((_vm.bind_data),function(item,key){return (item.type == 99)?_vm._l((_vm.bind_data[key]['data']),function(d_item,d_key){return _c('el-option',{key:d_key,attrs:{\"label\":d_item.name +'('+item.name+')',\"value\":key + '|' + d_key}})}):_vm._e()})],2),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"plain\":\"\"},on:{\"click\":_vm.sumAdd}},[_vm._v(\"添加\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',{staticStyle:{\"width\":\"30%\"}},[_c('span',[_vm._v(\"合计项目\")])]),_c('div',{staticStyle:{\"width\":\"30%\",\"text-align\":\"center\"}},[_c('span',[_vm._v(\"是否\")]),_c('br'),_c('span',[_vm._v(\"分页合计\")])]),_c('div',{staticStyle:{\"width\":\"30%\",\"text-align\":\"center\"}},[_c('span',[_vm._v(\"是否\")]),_c('br'),_c('span',[_vm._v(\"总合计\")])]),_c('div',{staticStyle:{\"width\":\"10%\"}},[_c('span',[_vm._v(\"操作\")])])]),_vm._l((_vm.ele_data.sum_data),function(item,idx){return _c('div',{key:idx,staticStyle:{\"display\":\"flex\"}},[_c('div',{staticStyle:{\"width\":\"30%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(item.name)}})]),_c('div',{staticStyle:{\"width\":\"30%\"}},[_c('el-radio',{attrs:{\"label\":\"否\"},model:{value:(item.page_flag),callback:function ($$v) {_vm.$set(item, \"page_flag\", $$v)},expression:\"item.page_flag\"}},[_vm._v(\"否\")]),_c('el-radio',{attrs:{\"label\":\"是\"},model:{value:(item.page_flag),callback:function ($$v) {_vm.$set(item, \"page_flag\", $$v)},expression:\"item.page_flag\"}},[_vm._v(\"是\")])],1),_c('div',{staticStyle:{\"width\":\"30%\"}},[_c('el-radio',{attrs:{\"label\":\"否\"},model:{value:(item.sum_flag),callback:function ($$v) {_vm.$set(item, \"sum_flag\", $$v)},expression:\"item.sum_flag\"}},[_vm._v(\"否\")]),_c('el-radio',{attrs:{\"label\":\"是\"},model:{value:(item.sum_flag),callback:function ($$v) {_vm.$set(item, \"sum_flag\", $$v)},expression:\"item.sum_flag\"}},[_vm._v(\"是\")])],1),_c('div',{staticStyle:{\"width\":\"10%\"}},[_c('i',{staticClass:\"fa fa-times close\",staticStyle:{\"color\":\"red\"},on:{\"click\":function($event){return _vm.ele_data.sum_data.splice(idx,1)}}})])])})],2)],1):_c('el-descriptions',{staticClass:\"margin-top\",staticStyle:{\"height\":\"68vh\",\"overflow-y\":\"auto\"},attrs:{\"title\":'组件属性('+_vm.select_element.type+')',\"column\":1,\"border\":\"\"}},[_c('template',{slot:\"extra\"},[_c('el-button',{attrs:{\"type\":\"default\",\"size\":\"small\"},on:{\"click\":function($event){_vm.select_element_id=''}}},[_vm._v(\"关闭\")])],1),(_vm.select_element.type == 'table')?_c('el-descriptions-item',{attrs:{\"label\":\"表格数据源\",\"label-style\":\"width:100px\"}},[_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择绑定项目\"},on:{\"change\":_vm.bindTableData},model:{value:(_vm.select_element.detail_id),callback:function ($$v) {_vm.$set(_vm.select_element, \"detail_id\", $$v)},expression:\"select_element.detail_id\"}},_vm._l((_vm.bind_data),function(item,key){return (item.type == 99)?_c('el-option',{key:key,attrs:{\"label\":item.name,\"value\":key}}):_vm._e()}),1)],1):_vm._e(),(_vm.select_element.type == 'table')?_c('el-descriptions-item',{attrs:{\"label\":\"是否显示表头\",\"label-style\":\"width:100px\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.select_element.show_header),callback:function ($$v) {_vm.$set(_vm.select_element, \"show_header\", $$v)},expression:\"select_element.show_header\"}},[_vm._v(\"显示\")]),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(_vm.select_element.show_header),callback:function ($$v) {_vm.$set(_vm.select_element, \"show_header\", $$v)},expression:\"select_element.show_header\"}},[_vm._v(\"不显示\")])],1):_vm._e(),(_vm.select_element.type == 'column')?_c('el-descriptions-item',{attrs:{\"label\":\"列头名称\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":1,\"placeholder\":\"表头名称\"},model:{value:(_vm.select_element.header_text),callback:function ($$v) {_vm.$set(_vm.select_element, \"header_text\", $$v)},expression:\"select_element.header_text\"}})],1):_vm._e(),(_vm.select_element.type == 'column')?_c('el-descriptions-item',{attrs:{\"label\":\"是否序号列\",\"label-style\":\"width:100px\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.select_element.is_number),callback:function ($$v) {_vm.$set(_vm.select_element, \"is_number\", $$v)},expression:\"select_element.is_number\"}},[_vm._v(\"是\")]),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(_vm.select_element.is_number),callback:function ($$v) {_vm.$set(_vm.select_element, \"is_number\", $$v)},expression:\"select_element.is_number\"}},[_vm._v(\"否\")])],1):_vm._e(),(_vm.select_element.type == 'column' && _vm.select_element.is_number != 1 && _vm.select_element.detail_id != '')?_c('el-descriptions-item',{attrs:{\"label\":\"绑定数据\",\"label-style\":\"width:100px\"}},[_c('div',[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.input_type),callback:function ($$v) {_vm.input_type=$$v},expression:\"input_type\"}},[_vm._v(\"文本输入\")]),_c('el-radio',{attrs:{\"label\":\"2\"},model:{value:(_vm.input_type),callback:function ($$v) {_vm.input_type=$$v},expression:\"input_type\"}},[_vm._v(\"绑定数据\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"plain\":\"\"},on:{\"click\":_vm.textAdd}},[_vm._v(\"添加\")])],1),_c('draggable',{staticClass:\"list-group\",attrs:{\"tag\":\"ul\",\"list\":_vm.select_element.texts,\"group\":\"bind_data\",\"handle\":\".handle\"}},_vm._l((_vm.select_element.texts),function(element,idx){return _c('li',{key:idx,staticClass:\"list-group-item\"},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('i',{staticClass:\"fa fa-align-justify handle\",staticStyle:{\"margin-right\":\"10px\"}}),(element.type == 1)?_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"type\":\"textarea\",\"rows\":1,\"placeholder\":\"文本内容\"},model:{value:(element.text),callback:function ($$v) {_vm.$set(element, \"text\", $$v)},expression:\"element.text\"}}):_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择绑定项目\"},on:{\"change\":function($event){return _vm.bindDetailDataChange(element)}},model:{value:(element.data_id),callback:function ($$v) {_vm.$set(element, \"data_id\", $$v)},expression:\"element.data_id\"}},_vm._l((_vm.bind_data[_vm.select_element.detail_id]['data']),function(item,key){return (item.type == 1)?_c('el-option',{key:key,attrs:{\"label\":item.name,\"value\":key}}):_vm._e()}),1),_c('el-input',{staticStyle:{\"width\":\"80px\"},attrs:{\"type\":\"text\",\"placeholder\":\"间距\"},model:{value:(element.spacing),callback:function ($$v) {_vm.$set(element, \"spacing\", $$v)},expression:\"element.spacing\"}}),_c('i',{staticClass:\"fa fa-times close\",staticStyle:{\"color\":\"red\"},on:{\"click\":function($event){return _vm.select_element.texts.splice(idx,1)}}})],1)])}),0)],1):_vm._e(),(_vm.select_element.tasks.length == 0 && _vm.select_element.type == 'flex')?_c('el-descriptions-item',{attrs:{\"label\":\"文本内容\",\"label-style\":\"width:100px\"}},[_c('div',[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.input_type),callback:function ($$v) {_vm.input_type=$$v},expression:\"input_type\"}},[_vm._v(\"文本输入\")]),_c('el-radio',{attrs:{\"label\":\"2\"},model:{value:(_vm.input_type),callback:function ($$v) {_vm.input_type=$$v},expression:\"input_type\"}},[_vm._v(\"绑定数据\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"plain\":\"\"},on:{\"click\":_vm.textAdd}},[_vm._v(\"添加\")])],1),_c('draggable',{staticClass:\"list-group\",attrs:{\"tag\":\"ul\",\"list\":_vm.select_element.texts,\"group\":\"bind_data\",\"handle\":\".handle\"}},_vm._l((_vm.select_element.texts),function(element,idx){return _c('li',{key:idx,staticClass:\"list-group-item\"},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('i',{staticClass:\"fa fa-align-justify handle\",staticStyle:{\"margin-right\":\"10px\"}}),(element.type == 1)?_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"type\":\"textarea\",\"rows\":1,\"placeholder\":\"文本内容\"},model:{value:(element.text),callback:function ($$v) {_vm.$set(element, \"text\", $$v)},expression:\"element.text\"}}):_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择绑定项目\"},on:{\"change\":function($event){return _vm.bindDataChange(element)}},model:{value:(element.data_id),callback:function ($$v) {_vm.$set(element, \"data_id\", $$v)},expression:\"element.data_id\"}},_vm._l((_vm.bind_data),function(item,key){return (item.type == 1)?_c('el-option',{key:key,attrs:{\"label\":item.name,\"value\":key}}):_vm._e()}),1),_c('el-input',{staticStyle:{\"width\":\"80px\"},attrs:{\"type\":\"text\",\"placeholder\":\"间距\"},model:{value:(element.spacing),callback:function ($$v) {_vm.$set(element, \"spacing\", $$v)},expression:\"element.spacing\"}}),_c('i',{staticClass:\"fa fa-times close\",staticStyle:{\"color\":\"red\"},on:{\"click\":function($event){return _vm.select_element.texts.splice(idx,1)}}})],1)])}),0)],1):_vm._e(),(_vm.select_element.type == 'image')?_c('el-descriptions-item',{attrs:{\"label\":\"图片\",\"label-style\":\"width:100px\"}},[_c('el-upload',{staticClass:\"avatar-uploader\",attrs:{\"action\":_vm.file_upload_server,\"show-file-list\":false,\"on-success\":_vm.handleAvatarSuccess}},[(_vm.select_element.path != '')?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.select_element.path}}):_c('i',{staticClass:\"el-icon-plus avatar-uploader-icon\"})])],1):_vm._e(),(_vm.select_element.type == 'barcode')?_c('el-descriptions-item',{attrs:{\"label\":\"二维码\",\"label-style\":\"width:100px\"}},[_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择二维码\"},on:{\"change\":function($event){return _vm.bindDataChange(_vm.element)}},model:{value:(_vm.select_element.data_id),callback:function ($$v) {_vm.$set(_vm.select_element, \"data_id\", $$v)},expression:\"select_element.data_id\"}},_vm._l((_vm.bind_data),function(item,key){return (item.type == 2)?_c('el-option',{key:key,attrs:{\"label\":item.name,\"value\":key}}):_vm._e()}),1)],1):_vm._e(),_c('el-descriptions-item',{attrs:{\"label\":\"宽度\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.width),callback:function ($$v) {_vm.$set(_vm.select_element, \"width\", $$v)},expression:\"select_element.width\"}},[_c('template',{slot:\"append\"},[_vm._v(\"PX\")])],2)],1),_c('el-descriptions-item',{attrs:{\"label\":\"高度\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.height),callback:function ($$v) {_vm.$set(_vm.select_element, \"height\", $$v)},expression:\"select_element.height\"}},[_c('template',{slot:\"append\"},[_vm._v(\"PX\")])],2)],1),_c('el-descriptions-item',{attrs:{\"label\":\"字体大小\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.fontSize),callback:function ($$v) {_vm.$set(_vm.select_element, \"fontSize\", $$v)},expression:\"select_element.fontSize\"}},[_c('template',{slot:\"append\"},[_vm._v(\"PX\")])],2)],1),_c('el-descriptions-item',{attrs:{\"label\":\"字体加粗\",\"label-style\":\"width:100px\"}},[_c('el-radio-group',{model:{value:(_vm.select_element.fontWeight),callback:function ($$v) {_vm.$set(_vm.select_element, \"fontWeight\", $$v)},expression:\"select_element.fontWeight\"}},[_c('el-radio-button',{attrs:{\"label\":\"bold\"}}),_c('el-radio-button',{attrs:{\"label\":\"normal\"}})],1)],1),_c('el-descriptions-item',{attrs:{\"label\":\"布局方式\",\"label-style\":\"width:100px\"}},[_c('el-radio-group',{on:{\"change\":_vm.changeDirection},model:{value:(_vm.select_element.flexDirection),callback:function ($$v) {_vm.$set(_vm.select_element, \"flexDirection\", $$v)},expression:\"select_element.flexDirection\"}},[_c('el-radio-button',{attrs:{\"label\":\"row\"}}),_c('el-radio-button',{attrs:{\"label\":\"column\"}})],1)],1),_c('el-descriptions-item',{attrs:{\"label\":\"对齐方式\",\"label-style\":\"width:100px\"}},[_c('el-radio-group',{model:{value:(_vm.select_element.justifyContent),callback:function ($$v) {_vm.$set(_vm.select_element, \"justifyContent\", $$v)},expression:\"select_element.justifyContent\"}},[_c('el-radio-button',{attrs:{\"label\":\"flex-start\"}}),_c('el-radio-button',{attrs:{\"label\":\"flex-end\"}}),_c('el-radio-button',{attrs:{\"label\":\"center\"}}),_c('el-radio-button',{attrs:{\"label\":\"space-between\"}}),_c('el-radio-button',{attrs:{\"label\":\"space-around\"}})],1)],1),_c('el-descriptions-item',{attrs:{\"label\":\"边框\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.border[0]),callback:function ($$v) {_vm.$set(_vm.select_element.border, 0, $$v)},expression:\"select_element.border[0]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"上\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.border[1]),callback:function ($$v) {_vm.$set(_vm.select_element.border, 1, $$v)},expression:\"select_element.border[1]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"右\")])],2)],1),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"margin-top\":\"5px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.border[2]),callback:function ($$v) {_vm.$set(_vm.select_element.border, 2, $$v)},expression:\"select_element.border[2]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"下\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.border[3]),callback:function ($$v) {_vm.$set(_vm.select_element.border, 3, $$v)},expression:\"select_element.border[3]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"左\")])],2)],1)]),_c('el-descriptions-item',{attrs:{\"label\":\"外间距\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.margin[0]),callback:function ($$v) {_vm.$set(_vm.select_element.margin, 0, $$v)},expression:\"select_element.margin[0]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"上\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.margin[1]),callback:function ($$v) {_vm.$set(_vm.select_element.margin, 1, $$v)},expression:\"select_element.margin[1]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"右\")])],2)],1),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"margin-top\":\"5px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.margin[2]),callback:function ($$v) {_vm.$set(_vm.select_element.margin, 2, $$v)},expression:\"select_element.margin[2]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"下\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.margin[3]),callback:function ($$v) {_vm.$set(_vm.select_element.margin, 3, $$v)},expression:\"select_element.margin[3]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"左\")])],2)],1)]),_c('el-descriptions-item',{attrs:{\"label\":\"内间距\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.padding[0]),callback:function ($$v) {_vm.$set(_vm.select_element.padding, 0, $$v)},expression:\"select_element.padding[0]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"上\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.padding[1]),callback:function ($$v) {_vm.$set(_vm.select_element.padding, 1, $$v)},expression:\"select_element.padding[1]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"右\")])],2)],1),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"margin-top\":\"5px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.padding[2]),callback:function ($$v) {_vm.$set(_vm.select_element.padding, 2, $$v)},expression:\"select_element.padding[2]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"下\")])],2),_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.padding[3]),callback:function ($$v) {_vm.$set(_vm.select_element.padding, 3, $$v)},expression:\"select_element.padding[3]\"}},[_c('template',{slot:\"prepend\"},[_vm._v(\"左\")])],2)],1)]),_c('el-descriptions-item',{attrs:{\"label\":\"背景颜色\",\"label-style\":\"width:100px\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('el-color-picker',{model:{value:(_vm.select_element.backgroundColor),callback:function ($$v) {_vm.$set(_vm.select_element, \"backgroundColor\", $$v)},expression:\"select_element.backgroundColor\"}}),(_vm.select_element.type == 'flex')?_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择绑定项目颜色\"},model:{value:(_vm.select_element.bg_id),callback:function ($$v) {_vm.$set(_vm.select_element, \"bg_id\", $$v)},expression:\"select_element.bg_id\"}},_vm._l((_vm.bind_data),function(item,key){return (item.type == 3)?_c('el-option',{key:key,attrs:{\"label\":item.name,\"value\":key}}):_vm._e()}),1):_vm._e(),(_vm.select_element.type == 'column' && _vm.select_element.detail_id != '')?_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择绑定颜色\"},model:{value:(_vm.select_element.bg_id),callback:function ($$v) {_vm.$set(_vm.select_element, \"bg_id\", $$v)},expression:\"select_element.bg_id\"}},_vm._l((_vm.bind_data[_vm.select_element.detail_id]['data']),function(item,key){return (item.type == 3)?_c('el-option',{key:key,attrs:{\"label\":item.name,\"value\":key}}):_vm._e()}),1):_vm._e()],1)]),_c('el-descriptions-item',{attrs:{\"label\":\"边框圆角\",\"label-style\":\"width:100px\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.select_element.borderRadius),callback:function ($$v) {_vm.$set(_vm.select_element, \"borderRadius\", $$v)},expression:\"select_element.borderRadius\"}},[_c('template',{slot:\"append\"},[_vm._v(\"PX\")])],2)],1)],2)],1)])])\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('draggable',{staticClass:\"dragArea\",style:(_vm.styleProps),attrs:{\"group\":\"element_gl\"},model:{value:(_vm.safeList),callback:function ($$v) {_vm.safeList=$$v},expression:\"safeList\"}},_vm._l((_vm.safeList),function(item){return _c('div',{key:item.id,staticClass:\"columu-item\",style:({\n                display: 'flex',\n                backgroundColor: _vm.select == item.id ? '#FFEBE1' : item.backgroundColor,\n                width: item.width + 'px',\n                minHeight: item.height + 'px',\n                fontSize: item.fontSize + 'px',\n                fontWeight: item.fontWeight,\n                textAlign: item.textAlign,\n                flexDirection: item.flexDirection,\n                justifyContent: item.justifyContent,\n                borderWidth: item.border[0]+'px '+item.border[1]+'px '+item.border[2]+'px '+item.border[3]+'px',\n                borderStyle: 'solid',\n                borderColor: '#000',\n                borderRadius: item.borderRadius + 'px',\n                margin: item.margin[0]+'px '+item.margin[1]+'px '+item.margin[2]+'px '+item.margin[3]+'px',\n                padding: item.padding[0]+'px '+item.padding[1]+'px '+item.padding[2]+'px '+item.padding[3]+'px'\n            }),on:{\"click\":function($event){$event.stopPropagation();return _vm.taskClick(item.id)}}},[_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"-12px\",\"right\":\"-5px\",\"z-index\":\"999\"}},[_c('a',{on:{\"click\":function($event){$event.stopPropagation();return _vm.deleteTask(item.id)}}},[_c('i',{staticClass:\"fa fa-close\",staticStyle:{\"color\":\"red\",\"cursor\":\"pointer\"}})])]),_c('div',_vm._l((item.texts),function(text,text_idx){return _c('span',{key:text_idx,style:({marginRight: text.spacing && text.spacing != '' ? text.spacing +'px':0}),domProps:{\"textContent\":_vm._s(text.text)}})}),0),(item.texts.length == 0 && item.type != 'column' && item.type != 'image' && item.type != 'barcode')?[_c('nested-draggable',{attrs:{\"tasks\":item.tasks,\"taskid\":item.id,\"pid\":_vm.taskid,\"select\":_vm.select,\"styleProps\":{\n                   display: 'flex',\n                   width: item.width + 'px',\n                   flexDirection: item.flexDirection,\n                   justifyContent: item.justifyContent,\n            }}})]:_vm._e(),(item.type == 'image')?[(item.path != '')?_c('img',{style:({\n                width: item.width + 'px',\n                height: item.height + 'px'\n            }),attrs:{\"src\":item.path,\"alt\":\"\"}}):_vm._e()]:_vm._e(),(item.type == 'barcode')?[_c('img',{style:({\n                width: item.width + 'px',\n                height: item.height + 'px'\n            }),attrs:{\"src\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAAAXNSR0IArs4c6QAADf9JREFUeF7tndly5DgQA+3//2hvtGNj98ViMjoJUrIxrxzUgQJYUvv6/Pj4+Pr4pf++vs629vn5eZTZdP+n+9tB7muCZ1UU7DItECr9tIDS/Z/uj/hfcV6DrGDxIsZpAdUgfrg1iOfwMkINEiR3U+gaJEh0DRIkd1PoGiRIdA0SJHdT6BokSHQNEiR3U+ihQdIvebZHEqCt38YnPPVv66f49pz6e3r9L35qkIFKrAAITwJ9usCeXn8NAgolgZMACF+DEAPZ85n5dIN0g7ytQhIYXSBvJ14EpPq7QbpBlNRIYDWIoteD0wOy8QlPDDxdYE+vvxukG4Q8OjynC6AGUfR6cHpANj7hiYGnC+zp9esNYgVgBUL50wOi/Kf7++31pfmtQYhh+QhG4cnAJHCLf3p96fprEGK4Bom+g9AFIMfzseICUV8HuXuDRJAdgO2f6qP4Fk/92/gWT/XR+Yr8NQixPDgnAVNoO0CLf3p96fr7iEUM9xGrj1ijn0lP31CkT5uf8JSfzrtBxr+Ugvi3/NF8VuTvIxax3EesSwZI4CsEKsbTl3Q7IEP+DPZ0fZSferACt3iqj85X5O8GIZbFOQmUBihSf0MpP8Wn+ii+xVN9dL4ifw1CLItzKyCRugZ5vVzDLw6k+fRTLKtA+SkXDdCWNyOAUQ6qj+JbvO1/Rf5uEDsF8RJPA7SlkYApPtVH8S2e6qPzFflrEGJZnFsBidR9xOojFr+E0g1iBUj4GmT8a5+JH+KXzmn+M/m7QYhlcU4DoAGK1N0g3SB+g5CASaAkcIpPeMpP8Qmfzk/xbf22v5n8f3qDzBB08lMeEkC6fpu/Bgn/gRhLsMWTQGx8wlP+GmTMEPE7w183CKlwcG4HQHgqbWbAZgPa/NSfrZ/qW5G/BiGWa5BLBkjgKwQqxtOvpJ8ekBUA4Ukc1D/h0/kpvq3f9jeTvxuEWO4G6Qa5YuDpN0C6fhuf8OTdmRuw7yDXDMzwpzYIDTB9Tg2mBUjxqT7ih+ITns6pPspv8VRf+pzqf+WvQQZTIAKtgEgAFJ/wdJ7uL10/9Ufn1H8NAgwSgSQAwtMAKT7h6Zzqo/wWT/Wlz6n+GqQGGTJQg/QRaygQumGsgOiGpPiEp/N0f+n6qT86p/67QbpBukFIA+b3YpFD0+d0A9gbzMYnPPFj66f4VB/lt3iqL31O9XeD0O0B34xpBUQCoPiEp3MSCOW3eKovfU71o0HSBabjpwdMBP/1/On57og//DrIjgKSOf66QE/3n5ztrtg1yIDp0wJ7ev5dIk7mqUFqkEsGrEGTwt0VuwapQWqQgQZqkBqkBqlBfmagn0Ld++977HqMGuXpBukG6QYZbZAvehO7g41DNdAGobRp6mx9VD+dp/uj/Hc4/6xB3h9DWkA1yPuzWYWsQQSTNYgg7yHQGkQMqgYR5D0EWoOIQdUggryHQGsQMagaRJD3EGgNIgZVgwjyHgKtQcSgahBB3kOgyiD2Y0grMJs/PSPqj+q3eOrv7vGpPupvxXkNsoLFixg04BrEfatLcHT/ha5BgizXIO5vFBJ/wdHVIDvIpQF3g3SDDHVIAiIRk8AInz6n/qh+i6f+7h6f6qP+Vpz3EWsFi30H+ZEBEri9IIKj6yPWDnLTAiGBUY+2vnR8qo/yrzjvBlnBYjfI790g5jcr2hvMatPeMOn6T9eXzm/j0/xpPpTf4l/1qb8PQgUQAfacCKL46fpP15fOb+Pb+VB+mi/ha5DDf+fdCoTwMwIYxVghMKoxmX9F/d0gZoKATQuUSk/nt/Gpfitwi+8G6QYZanSFwMgE3SCGoZvf0NSavWFJoKfz2/6ofuqf8lt8N0g3SDcIuLTvIHSNiXO64Sg03YCET+e38al+6p/yW/z3Bhn92h+bgPBEkD0nAil+un6qL52f+qf6CE/n1J/NvyJ+DTKYIhFMAqBzEkA6v62P8HRO/RE/O+LXIDXIJQNWoDsEnPwUrI9Y9IJ2+CWeblgSoD2vQfoOoj7FSQuwBhn/RCLxT/zNXAB9xOojVh+xRhrop1jX7NANRDcYndMNls5v6yM8nVN/xM+O+N0g3SDdIHfdIPaGSN8g6RuO6k+f2/4s3vZH+Sn+jP6ObpCZAqnJ5Md8NIB0/ab3Gaztz+JnajTzpfgz86tBxCPWDME0pJPnVuAWb3un/BR/Zn41SA3y9jsICXRGgCTibhDDEGDtAC0+2NqS0LY/i7dNUH6KP2PgbpBukG6Qfor1MwN0g9ANRXi6wU6f2/4s3vZP+Sn+zPy6QbpBukFGGhj9XqwVDqQYyZcwyk03CN1QFm/rIzzVT/h0f3fP/6pv+BOFtgHC07kdMMW3ArB4Wx/hLX/p/qj+0/lrkK/s369IC5QEls5v41P9NQgwdPcBpAdI8Ulglj/Kb+NT/afzd4N0gww1elqgp/PXIDVIDUJPMf0U65oheoRI33AUnx5RqH7CU34b/+75u0G6QbpBaIOYnyg8fQPYG47w1B+dp29Yym/7o/rT8U/3971BahAaw/vnJLD3I88h0wJOx6cud+SvQWgK4rwGGZNn+alBQJxEEA2A8MIb31DKb+MT3vZH9afjn+6vj1jwkk4DonMSGOHteVrA6fjU/478fcSiKYjzGqSPWEc/JqQbhARKeOGNPmJNkEfzoRB2fjP5u0FoCuJ8ZgAiPELTAkrHpwZ35I8axDZoBbaDwFGPlJ/6S+NpPlQf4W39FJ/OKT/h4y/pVAA1kB4Q1ZfOT/EtP4S/e/9UH53b/msQYJgEbAdE8WnAFk/1U3zC2/opPp1TfsLXIDXIkIEaJPytJuRQcnh6QFRfOj/Ft/wQ/u79U310bvvvBukG6QYhDSS/WdE6nG5YG5/w6fwUn25Ai797/1QfnRN/hO8GodtD/o1CGpAVuMWTQCg+4W3/FJ/OKT/hvw0y+olCSnCaQKpvhoDR/7H9UX6q/3R+qj99Tv1b/ghfg4Q3CAmIBkQCofh0TvkJnz6n/ql+i69BapC0xlV8K3CLr0FqECXgNNgK3OJrkBokrXEV3wrc4muQGkQJOA22Arf4GqQGSWtcxbcCt/gapAZRAk6DrcAt/tsgo6+kEwG2AMJTfjqnjwEJb8+pP1ufjU942/9pvOW3BglPkARoB2jjEz5MTzy85bcGCY+IBGgHaOMTPkxPPLzltwYJj4gEaAdo4xM+TE88vOW3BgmPiARoB2jjEz5MTzy85bcGCY+IBGgHaOMTPkxPPLzltwYJj4gEaAdo4xM+TE88vOW3BgmPiARoB2jjEz5MTzy85VcbJN5hOAEJhAgmvC3f5k/jbX/EH9Wfzl+DwE8M0oBowHaANn8ab/sj/qj+dP4apAYZauy0QE/nr0FqkBoE1pD6Xiy74k7j7YonvO2PblDKn8bb/mz96fzdIN0g3SDdINcM2BuM8PaGS28Aqp/y2/7unr8bpBukG4Q2yOj3Ytkb4jSebsD0DZaOT/xSfsKn+TtdH/X/vUFqkGuaSCBEMAnAxrf5CU/12f4In66P4tcg8hGLCCYBkAApPp1TfsJTfRTf4tP1UfwapAZR7yA1yIzFbvx/7A1GeGrdCoji0znlJzz1T/EtPl0fxe8G6QbpBumnWNcM2BuQbqB0fJuf8HYDWHy6PorfDdIN0g1iNgjdADMOTP6f9A1t4xOeuLH8n85P/VF9p/vHDWILJILs+WmCiR+qj/qn+IQ/nd/Wd7r/GoTWq3wEOy3Q0/lrEGIgfE4CSN9AFJ/qI3ooPuFP57f1ne6/G6QbRL2kkwHonAxcgxCDYQFTejtAwlP+0wKx+ak/4sfmp/hUXzdI2IB2QKcFYvOTAIkfm5/iU301SA3SRyzSwMm/k04OphuEbgjCU36KT3jKb+NT/t9+TvxS/zP8D38ehAqYSUBFjs5tfsJTbbY/ym/jU/2//Zz4pf5n+K9BBizOEJg0OA34r5/XIF9fQw2QgHcQWIOcs+mO+XaDdIOcU7jMXIN0g0gJ/W54DVKD/G6Fy+5qkBpESuh3w2uQsEFOv+TTgKm+0/Kn+qk+29+O/H/6JZ0GlB4Axaf6SIDpc6qf8tv+duSvQQZTTA+A4lsBkUDtOdVP8W1/O/LXIDUI6fjyfIdAzdeZqLEZg9YgNQjpqAa5YoBuiBkHvs3+67so+5Ju6ItjaT5UgNXPjvzdIN0gpONukG6QnxlI31AU396wbyt/Ekj1Uxjb34783SBig5wecFqAFJ8Eavmh/Pac6n/Fr0FqkLd1RgKrQeD3Rr3N/L9AO4DTeOqf6iM8nacFSvWn81P/dE71d4M8/BfDkQDSAiWBpfNT/3RO9dcgNQhpaHhOAqtB+ogVFZgK/nrBvPl8bH8WTwbvBukGURojgaUNqoqf+EJ0DVKDKI3VIPCtHordBWC6oWiAVIKNf3c89W/5o/h0TvwRfkX96usgVGD6nAi0BNn4d8fTfCx/FJ/OiT/Cr6i/BhmwTAOiAdwdv0NglGN0TvxRbJoP4fU7yEyC5P8hAi1BNv7d8TQbyx/Fp3Pij/Ar6u8G6Qa5ZGCFwEjE3SCGIcDSDWMHbOPfHU+jsfxRfDon/gi/ov5ukG6QbpCRBsyfPyAHp8/phrE3iI1/dzzNx/JH8emc+CP8ivqHG4QKuPv5CoLu3GNaQOn4xK3NT/FnzmuQGZZu+n+sgOgCSccnWm1+ij9zXoPMsHTT/2MFVIPwYGsQ5ui2/6MGyY+mBslzHMtQg8So/S9wDZLnOJahBolRW4Pkqc1nqEE2cDz6Okg+fTYDvYRms+ej1yAbOK5B8iSnMtQgKWb/j/sP/WtS8n9WZucAAAAASUVORK5CYII=\",\"alt\":\"\"}})]:_vm._e()],2)}),0)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar iterate = require('../internals/iterate');\nvar aCallable = require('../internals/a-callable');\nvar anObject = require('../internals/an-object');\nvar getIteratorDirect = require('../internals/get-iterator-direct');\nvar iteratorClose = require('../internals/iterator-close');\nvar iteratorHelperWithoutClosingOnEarlyError = require('../internals/iterator-helper-without-closing-on-early-error');\n\nvar someWithoutClosingOnEarlyError = iteratorHelperWithoutClosingOnEarlyError('some', TypeError);\n\n// `Iterator.prototype.some` method\n// https://tc39.es/ecma262/#sec-iterator.prototype.some\n$({ target: 'Iterator', proto: true, real: true, forced: someWithoutClosingOnEarlyError }, {\n  some: function some(predicate) {\n    anObject(this);\n    try {\n      aCallable(predicate);\n    } catch (error) {\n      iteratorClose(this, 'throw', error);\n    }\n\n    if (someWithoutClosingOnEarlyError) return call(someWithoutClosingOnEarlyError, this, predicate);\n\n    var record = getIteratorDirect(this);\n    var counter = 0;\n    return iterate(record, function (value, stop) {\n      if (predicate(value, counter++)) return stop();\n    }, { IS_RECORD: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.container-print-page[data-v-d835a582]{\\n    background-color: #fff;\\n    text-align: center;\\n    margin-top: 10px;\\n    padding: 5px;\\n}\\n.container1-page[data-v-d835a582]{\\n    background-color: #fff;\\n}\\n.dragArea[data-v-d835a582] {\\n    min-height: 50px;\\n    outline: 1px dashed;\\n    padding-bottom: 10px;\\n}\\n.avatar-uploader .el-upload[data-v-d835a582] {\\n    border: 1px dashed #d9d9d9;\\n    border-radius: 6px;\\n    cursor: pointer;\\n    position: relative;\\n    overflow: hidden;\\n}\\n.avatar-uploader .el-upload[data-v-d835a582]:hover {\\n    border-color: #409EFF;\\n}\\n.avatar-uploader-icon[data-v-d835a582] {\\n    font-size: 28px;\\n    color: #8c939d;\\n    width: 178px;\\n    height: 178px;\\n    line-height: 178px;\\n    text-align: center;\\n}\\n.avatar[data-v-d835a582] {\\n    width: 178px;\\n    height: 178px;\\n    display: block;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.dragArea[data-v-ec39e04c] {\\n    min-height: 10px;\\n}\\n.columu-item[data-v-ec39e04c] {\\n    position: relative;\\n    cursor: pointer;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=d835a582&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"6b1a7890\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=d835a582&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=d835a582&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./nested.vue?vue&type=style&index=0&id=ec39e04c&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"64d7b73a\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./nested.vue?vue&type=style&index=0&id=ec39e04c&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./nested.vue?vue&type=style&index=0&id=ec39e04c&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=d835a582&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=d835a582&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d835a582\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('d835a582')) {\n      api.createRecord('d835a582', component.options)\n    } else {\n      api.reload('d835a582', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=d835a582&scoped=true\", function () {\n      api.rerender('d835a582', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/document/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=d835a582&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=d835a582&scoped=true\"", "import { render, staticRenderFns } from \"./nested.vue?vue&type=template&id=ec39e04c&scoped=true\"\nimport script from \"./nested.vue?vue&type=script&lang=js\"\nexport * from \"./nested.vue?vue&type=script&lang=js\"\nimport style0 from \"./nested.vue?vue&type=style&index=0&id=ec39e04c&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ec39e04c\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('ec39e04c')) {\n      api.createRecord('ec39e04c', component.options)\n    } else {\n      api.reload('ec39e04c', component.options)\n    }\n    module.hot.accept(\"./nested.vue?vue&type=template&id=ec39e04c&scoped=true\", function () {\n      api.rerender('ec39e04c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/document/nested.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./nested.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./nested.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./nested.vue?vue&type=style&index=0&id=ec39e04c&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./nested.vue?vue&type=template&id=ec39e04c&scoped=true\""], "names": [], "sourceRoot": ""}