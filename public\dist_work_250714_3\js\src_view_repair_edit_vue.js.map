{"version": 3, "file": "js/src_view_repair_edit_vue.js", "mappings": ";;;;;;;;;;;AAyEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://rrts-manager/src/view/repair/edit.vue", "webpack://rrts-manager/./src/view/repair/edit.vue", "webpack://rrts-manager/./src/view/repair/edit.vue?a1f7", "webpack://rrts-manager/./src/view/repair/edit.vue?26fa", "webpack://rrts-manager/./src/view/repair/edit.vue?1ee9"], "sourcesContent": ["<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"录入修理费用\" is_back=\"1\"></m-header>\r\n        <m-body>\r\n            <div v-if=\"loading\" style=\"height: 100%;display: flex;align-items: center;justify-content: center;\">\r\n                <van-loading size=\"36px\" text-size=\"16px\" vertical>加载中...</van-loading>\r\n            </div>\r\n            <div v-else style=\"height: 100%;display: flex;flex-direction: column;\">\r\n                <van-form style=\"flex: 1;overflow-y: auto;\">\r\n                    <van-field\r\n                        label=\"设备\"\r\n                        type=\"text\"\r\n                        v-model=\"equ_code\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        v-model=\"begin_describe\"\r\n                        label=\"故障现象\"\r\n                        type=\"textarea\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"计划解除时间\"\r\n                        type=\"text\"\r\n                        v-model=\"repair_dt\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        v-model=\"repair_describe\"\r\n                        label=\"解除计划\"\r\n                        type=\"textarea\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"修理费用金额\"\r\n                        type=\"number\"\r\n                        v-model=\"repair_money\"\r\n                        placeholder=\"请输入修理费用金额\"\r\n                        maxlength=\"10\"\r\n                        input-align=\"right\"\r\n                        required\r\n                    >\r\n                        <template #right-icon>\r\n                            <span>元</span>\r\n                        </template>\r\n                    </van-field>\r\n\r\n                    <van-field\r\n                        v-model=\"repair_money_describe\"\r\n                        rows=\"2\"\r\n                        autosize\r\n                        label=\"修理费用描述\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入修理费用描述\"\r\n                        input-align=\"right\"\r\n                        show-word-limit\r\n                        required\r\n                    />\r\n                </van-form>\r\n                <div>\r\n                    <van-button type=\"warning\" icon=\"success\" block size=\"large\" @click=\"doSubmit\">提交</van-button>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { Dialog } from 'vant';\r\n\r\n    export default {\r\n        name: \"repairEdit\",\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                uid: '',\r\n                equ_code: '',\r\n                begin_describe: '',\r\n                repair_dt: '',\r\n                repair_describe: '',\r\n                repair_money: '',\r\n                repair_money_describe: '',\r\n            }\r\n        },\r\n        created() {\r\n            this.uid = this.$route.params.uid || '';\r\n\r\n            this.init();\r\n        },\r\n        methods: {\r\n            init() {\r\n                this.loading = true;\r\n                this.$http.post_only('work/fault/repairmoneyinit', {uid: this.uid}).then((rs) => {\r\n                    this.loading = false;\r\n                    if (rs.status == 'ok') {\r\n                        let data = rs.data;\r\n                        this.equ_code = data.equ_code;\r\n                        this.begin_describe = data.begin_describe;\r\n                        this.repair_dt = data.repair_dt;\r\n                        this.repair_describe = data.repair_describe;\r\n                        this.repair_money = data.repair_money;\r\n                        this.repair_money_describe = data.repair_money_describe;\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '提示',\r\n                            message: rs.message,\r\n                            confirmButtonText: '返回上一页'\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            doSubmit() {\r\n                if (!this.repair_money) {\r\n                    this.$toast.fail('请输入修理费用金额');\r\n                    return;\r\n                }\r\n\r\n                if (!this.repair_money_describe) {\r\n                    this.$toast.fail('请输入修理费用描述');\r\n                    return;\r\n                }\r\n\r\n                Dialog.confirm({\r\n                    title: '提交',\r\n                    message: '确定提交吗？',\r\n                }).then(() => {\r\n                    this.$http.post('work/fault/repairmoney', {\r\n                        uid: this.uid,\r\n                        repair_money: this.repair_money,\r\n                        repair_money_describe: this.repair_money_describe\r\n                    }).then((rs) => {\r\n                        if (rs.status === 'ok') {\r\n                            this.$toast.success('提交成功！');\r\n                            this.$router.go(-1);\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                        }\r\n                    }).catch((e) => {\r\n                        this.$toast.fail('提交失败');\r\n                    });\r\n                }).catch(() => {});\r\n            },\r\n        }\r\n    }\r\n</script>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"录入修理费用\",\"is_back\":\"1\"}}),_c('m-body',[(_vm.loading)?_c('div',{staticStyle:{\"height\":\"100%\",\"display\":\"flex\",\"align-items\":\"center\",\"justify-content\":\"center\"}},[_c('van-loading',{attrs:{\"size\":\"36px\",\"text-size\":\"16px\",\"vertical\":\"\"}},[_vm._v(\"加载中...\")])],1):_c('div',{staticStyle:{\"height\":\"100%\",\"display\":\"flex\",\"flex-direction\":\"column\"}},[_c('van-form',{staticStyle:{\"flex\":\"1\",\"overflow-y\":\"auto\"}},[_c('van-field',{attrs:{\"label\":\"设备\",\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.equ_code),callback:function ($$v) {_vm.equ_code=$$v},expression:\"equ_code\"}}),_c('van-field',{attrs:{\"label\":\"故障现象\",\"type\":\"textarea\",\"readonly\":\"\"},model:{value:(_vm.begin_describe),callback:function ($$v) {_vm.begin_describe=$$v},expression:\"begin_describe\"}}),_c('van-field',{attrs:{\"label\":\"计划解除时间\",\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.repair_dt),callback:function ($$v) {_vm.repair_dt=$$v},expression:\"repair_dt\"}}),_c('van-field',{attrs:{\"label\":\"解除计划\",\"type\":\"textarea\",\"readonly\":\"\"},model:{value:(_vm.repair_describe),callback:function ($$v) {_vm.repair_describe=$$v},expression:\"repair_describe\"}}),_c('van-field',{attrs:{\"label\":\"修理费用金额\",\"type\":\"number\",\"placeholder\":\"请输入修理费用金额\",\"maxlength\":\"10\",\"input-align\":\"right\",\"required\":\"\"},scopedSlots:_vm._u([{key:\"right-icon\",fn:function(){return [_c('span',[_vm._v(\"元\")])]},proxy:true}]),model:{value:(_vm.repair_money),callback:function ($$v) {_vm.repair_money=$$v},expression:\"repair_money\"}}),_c('van-field',{attrs:{\"rows\":\"2\",\"autosize\":\"\",\"label\":\"修理费用描述\",\"type\":\"textarea\",\"maxlength\":\"200\",\"placeholder\":\"请输入修理费用描述\",\"input-align\":\"right\",\"show-word-limit\":\"\",\"required\":\"\"},model:{value:(_vm.repair_money_describe),callback:function ($$v) {_vm.repair_money_describe=$$v},expression:\"repair_money_describe\"}})],1),_c('div',[_c('van-button',{attrs:{\"type\":\"warning\",\"icon\":\"success\",\"block\":\"\",\"size\":\"large\"},on:{\"click\":_vm.doSubmit}},[_vm._v(\"提交\")])],1)],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./edit.vue?vue&type=template&id=2ada848a\"\nimport script from \"./edit.vue?vue&type=script&lang=js\"\nexport * from \"./edit.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2ada848a')) {\n      api.createRecord('2ada848a', component.options)\n    } else {\n      api.reload('2ada848a', component.options)\n    }\n    module.hot.accept(\"./edit.vue?vue&type=template&id=2ada848a\", function () {\n      api.rerender('2ada848a', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/repair/edit.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit.vue?vue&type=template&id=2ada848a\""], "names": [], "sourceRoot": ""}