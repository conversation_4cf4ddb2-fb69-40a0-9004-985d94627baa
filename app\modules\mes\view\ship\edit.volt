{{ assets.outputJs('validate') }}
<div class="page-content">
    <div id="app">
        <div class="row" style="padding-left: 15px">
            <div class="col-sm-4" style="padding-left: 0">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">工艺项目</span>
                        </div>
                    </div>
                    <div class="portlet-body" style="min-height: 773px;">
                        <form  id="form" class="form-horizontal" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>工艺字段</label>
                                        <div v-show="add_flag == 1" class="col-sm-8">
                                            <select name="select_id" class="form-control bs-select" v-model="select_id" required>
                                                <option value="">请选择</option>
                                                <option v-for="(item,idx) in input_types" :value="item.uid">${item.name}</option>
                                            </select>
                                        </div>
                                        <div v-show="add_flag == 2" class="col-sm-8">
                                            <input name="data.title" class="form-control" v-model="data.title" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required"> * </span>必须输入</label>
                                        <div class="col-sm-8">
                                            <select name="data.required" class="form-control bs-select" v-model="data.required" required>
                                                <option value="0">否</option>
                                                <option value="1">是</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="data.type == 1 || data.type == 2 || data.type == 5" class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required"> * </span>最大输入</label>
                                        <div class="col-sm-8">
                                            <input type="number" maxlength="3" name="data.max" class="form-control" v-model="data.max" required/>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="data.type == 2" class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">数字单位</label>
                                        <div class="col-sm-8">
                                            <input type="text" maxlength="40" name="data.unit" class="form-control" v-model="data.unit"/>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="data.type == 3 || data.type == 4" class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required"> * </span>选项</label>
                                        <div class="col-sm-8">
                                            <div style="display: flex">
                                                <input type="text" maxlength="20" v-model="select_value" class="form-control"/>
                                                <button type="button"  class="btn btn-outline yellow" @click="addSelect"><i class="fa fa-plus"></i> 添加</button>
                                            </div>
                                            <div>
                                                <table class="factory-table">
                                                    <tbody>
                                                    <tr v-for="item,idx in data.list">
                                                        <td v-text="item"></td>
                                                        <td>
                                                            <a href="javascript:;" @click="delSelect(idx)" style="color: red;font-size: 20px">
                                                                <i class="fa fa-times"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">说明</label>
                                        <div class="col-sm-8">
                                            <textarea class="form-control" name="data.explain" v-model="data.explain" maxlength="200" style="resize: none;"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-actions" style="text-align: right">
                                <button type="button" class="btn default" @click="cleanForm">取消</button>
                                <button v-if="add_flag == 1" type="button" class="btn blue" @click="addForm"><i class="fa fa-plus"></i> 添加</button>
                                <button v-else type="button" class="btn yellow" @click="editFormSave"><i class="fa fa-check"></i> 保存</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-sm-8" style="padding-left: 0">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">工艺信息</span>
                        </div>
                        <div class="actions">
                            <div class="input-group input-icon" style="display: flex;flex-direction: row ;margin-left: 20px;">
                                <div style="width: 150px">
                                    <select class="bs-select form-control" name="plan_type" v-model="plan_type">
                                        {% for key,val in planTypes %}
                                            <option value="{{key }}">{{ val }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <input style="width: 200px" type="text" maxlength="20" class="form-control" name="name" v-model="name" placeholder="表单名称">
                                <span class="input-group-btn">
                                     <button class="btn yellow" @click="saveFormData" style="width: 80px"> <i class="fa fa-check"></i> 保存</button>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body" style="height: 773px;overflow: auto">
                        <table class="factory-table" style="margin-bottom: 0;">
                            <thead>
                            <tr style="background-color: #3598DC;color: #FFFFFF">
                                <th>序号</th>
                                <th>排序</th>
                                <th>类型</th>
                                <th>标题</th>
                                <th>必须输入</th>
                                <th>最大输入</th>
                                <th>数字单位</th>
                                <th>选项</th>
                                <th>说明</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <template v-for="item,idx in form_data">
                                <tr>
                                    <td v-text="idx+1"></td>
                                    <td>
                                        <a href="javascript:;" @click="formSort(1,idx)">
                                            <i class="fa fa-long-arrow-up"></i>
                                        </a>
                                        <a href="javascript:;" @click="formSort(2,idx)">
                                            <i class="fa fa-long-arrow-down"></i>
                                        </a>
                                    </td>
                                    <td v-text="item.name"></td>
                                    <td v-text="item.title"></td>
                                    <td>
                                        <span v-if="item.required == 1">是</span>
                                        <span v-else>否</span>
                                    </td>
                                    <td v-text="item.max"></td>
                                    <td v-text="item.unit"></td>
                                    <td>
                                        <div v-for="t,i in item.list">
                                            <span v-text="t"></span>
                                        </div>
                                    </td>
                                    <td v-text="item.explain"></td>
                                    <td>
                                        <a href="javascript:;" @click="editForm(idx)" style="color: blue;font-size: 16px" >
                                            <i class="fa fa-edit"></i>
                                        </a>
                                        <a href="javascript:;" @click="delForm(idx)" style="color: red;font-size: 20px">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </td>
                                </tr>
                            </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var defaultData = {{ defaultData | json_encode }};
    var app = new Vue({
        el: '#app',
        data: {{ jsonForm }},
        methods: {
            addSelect(){
                if (this.select_value == ''){
                    return;
                }
                for(let item of this.data.list){
                    if (this.select_value == item){
                        toastr.error('选项值重复!');
                        return;
                    }
                }
                this.data.list.push(this.select_value);
                this.select_value = '';
            },
            delSelect(idx){
                this.data.list.splice(idx,1);
            },
            addForm (e){
                if( !$('#form').validate().form() )
                    return;
                for(let item of this.form_data){
                    if (item.id == this.data.id){
                        toastr.error('不能重复添加!');
                        return;
                    }
                }
                if (this.data.type == 3 || this.data.type == 4){
                    if (this.data.list.length == 0){
                        toastr.error('选项不能为空!');
                        return;
                    }
                }

                this.dataClean();
                this.form_data.push(JSON.parse(JSON.stringify(this.data)));
                this.cleanForm();
            },
            getServiceItem(id){
                for(let item of this.input_types){
                    if (id == item.uid){
                        return item;
                    }
                }
                return null;
            },
            editForm(idx){
                this.add_flag = 2;
                this.data = JSON.parse(JSON.stringify(this.form_data[idx]));
                app.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
            },
            editFormSave(){
                if( !$('#form').validate().form() )
                    return;
                if (this.data.id == ''){
                    toastr.error('ID不存在!');
                    return;
                }
                for(let item of this.form_data){
                    if (item.id == this.data.id  && item.id != this.data.id){
                        toastr.error('不能重复添加!');
                        return;
                    }
                }
                if (this.data.type == 3 || this.data.type == 4){
                    if (this.data.list.length == 0){
                        toastr.error('选项不能为空!');
                        return;
                    }
                }
                for(let i = 0; i < this.form_data.length; i++){
                    if (this.form_data[i].id == this.data.id){
                        this.dataClean();
                        this.form_data[i] =  JSON.parse(JSON.stringify(this.data));
                        break;
                    }
                }
                this.cleanForm();
            },
            delForm(idx){
                let item = this.form_data[idx];
                this.form_data.splice(idx,1);
            },
            cleanForm(){
                this.add_flag = 1;
                this.select_id = '';
                this.data = {...defaultData};
                this.data.list = [];
                app.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
            },
            formSort (sort,idx) {
                let item = this.form_data[idx];
                if (sort == 1){
                    //up
                    if (idx == 0){
                        return;
                    }
                    this.form_data.splice(idx,1);
                    this.form_data.splice(idx-1,0,item);
                } else {
                    //down
                    if(idx == this.form_data.length-1) {
                        return;
                    }
                    this.form_data.splice(idx,1);
                    this.form_data.splice(idx+1,0,item);
                }
            },
            saveFormData(){
                if (this.name == ''){
                    toastr.error('请添写表单名称');
                    return;
                }
                var url= '{{ url('mes/ship/edit/')~uid}}';
                showSpin();
                $.post(url, {
                    name : this.name,
                    plan_type : this.plan_type,
                    data: encodeURI(JSON.stringify(this.form_data))
                }, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        toastr.success('保存成功！');
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            },
            dataClean(){
                if (!(this.data.type == 1 || this.data.type == 2 || this.data.type == 5)){
                    this.data.max = '';
                }
                if (!(this.data.type == 2)){
                    this.data.unit = '';
                }
                if (!(this.data.type == 3 || this.data.type == 4)){
                    this.data.list = [];
                }
            }
        },
        watch : {
            select_id : function (val){
                if (val == ''){
                    this.data.id = '';
                    this.data.type = '';
                    this.data.name = '';
                    this.data.title = '';
                    this.data.unit = '';
                    this.data.list = [];
                    this.data.explain = '';
                    return;
                }
                let item = this.getServiceItem(val);
                this.data.id = item.uid;
                this.data.type = item.type;
                this.data.name = item.type_name;
                this.data.title = item.name;
                this.data.unit = item.unit;
                this.data.list = item.list;
                this.data.explain = item.explain;
            }
        }
    });

    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);
</script>

<style>
    .factory-table {
        margin-bottom: 0;
        width: 100%;
        margin-top: 10px
    }

    .factory-table > thead > tr > th, .factory-table > tbody > tr > td {
        border: 1px solid #F2F2F2;
        height: 35px;
        padding-left: 5px ;
        background-color: #ffffff;
    }

    .factory-table > thead > tr > th {
        font-weight: normal;
        background-color: #3598DC;
    }

    .detail-table {
        margin-bottom: 0;
        width: 100%;
        margin-top: 10px
    }

    .detail-table > thead > tr > th, .detail-table > tbody > tr > td {
        border: 1px solid #F2F2F2;
        height: 35px;
        padding-left: 5px ;
        background-color: #ffffff;
    }

    .detail-table > thead > tr > th {
        font-weight: normal;
        background-color: #B2B2B2;
    }
</style>