<?php

return [
    'header' => '生产中心',
    'id' => 'd-mes',
    'icon' => 'icon-pie-chart',
    'module' => 'mes',
    'active' => false,
    'items' => [
        ['name' => '生产通知管理','controller'=>'notice',  'icon' => 'fa fa-fw fa-puzzle-piece', 'items' => [
            ['name' => '生产通知管理', 'controller' => 'notice', 'url' => 'mes/notice/list', 'identity' => 'mes:notice:list', 'icon' => 'fa fa-fw fa-briefcase'],
        ]],
        ['name' => '排产管理','controller'=>'plan',  'icon' => 'fa fa-fw fa-puzzle-piece', 'items' => [
            ['name' => '排产管理', 'controller' => 'plan', 'url' => 'mes/plan/list', 'identity' => 'mes:plan:list', 'icon' => 'fa fa-fw fa-briefcase'],
            ['name' => '排产查询', 'controller' => 'notice', 'url' => 'mes/plan/search', 'identity' => 'mes:plan:search', 'icon' => 'fa fa-fw fa-briefcase'],
            ['name' => '设备排产情况统计', 'controller' => 'notice', 'url' => 'common/ext/planview', 'identity' => 'mes:plan:view', 'icon' => 'fa fa-fw fa-briefcase']
        ]],
        ['name' => '生产管理','controller'=>'notice',  'icon' => 'fa fa-fw fa-puzzle-piece', 'items' => [
            ['name' => '生产完成情况统计', 'controller' => 'notice', 'url' => 'mes/notice/search', 'identity' => 'mes:notice:search', 'icon' => 'fa fa-fw fa-briefcase'],
            ['name' => '生产履历查询', 'controller' => 'produce', 'url' => 'mes/produce/list', 'identity' => 'mes:produce:list', 'icon' => 'fa fa-fw fa-briefcase'],
            ['name' => '其他工作查询', 'controller' => 'produce', 'url' => 'mes/produce/other', 'identity' => 'mes:produce:other', 'icon' => 'fa fa-fw fa-briefcase'],
            ['name' => '工时日报表', 'controller' => 'dailyreport', 'url' => 'mes/dailyreport/search', 'identity' => 'mes:dailyreport:search', 'icon' => 'fa fa-fw fa-briefcase']
        ]],
        ['name' => '生产日报','controller'=>'notice',  'icon' => 'fa fa-fw fa-puzzle-piece', 'items' => [
            ['name' => '生产日报管理', 'controller' => 'produce', 'url' => 'mes/report/list', 'identity' => 'mes:report:list', 'icon' => 'fa fa-fw fa-briefcase'],
            ['name' => '工资统计', 'controller' => 'produce', 'url' => 'mes/report/stat', 'identity' => 'mes:report:stat', 'icon' => 'fa fa-fw fa-briefcase'],
        ]],
        ['name' => '质量管理','controller'=>'check',  'icon' => 'fa fa-fw fa-puzzle-piece', 'items' => [
            ['name' => '质验数据查询', 'controller' => 'check', 'url' => 'mes/check/list', 'identity' => 'mes:check:list', 'icon' => 'fa fa-fw fa-briefcase'],
        ]],
    ]
];