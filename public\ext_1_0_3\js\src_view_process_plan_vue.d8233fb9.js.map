{"version": 3, "file": "js/src_view_process_plan_vue.d8233fb9.js", "mappings": ";;;;;;;;;;;;AA8MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AC5eA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/process/plan.vue", "webpack://sfp_ext/./src/view/process/plan.vue", "webpack://sfp_ext/./src/view/process/plan.vue?9ff0", "webpack://sfp_ext/./src/view/process/plan.vue?6ca7", "webpack://sfp_ext/./src/view/process/plan.vue?898f", "webpack://sfp_ext/./src/view/process/plan.vue?e71f", "webpack://sfp_ext/./src/view/process/plan.vue?bd7a", "webpack://sfp_ext/./src/view/process/plan.vue?cd26"], "sourcesContent": ["<template>\r\n    <div class=\"form-group form-group-lg panel panel-default\">\r\n        <div class=\"panel-heading\">\r\n            <h3 class=\"panel-title\">机加工班次排产计划</h3>\r\n        </div>\r\n        <div class=\"panel-body\">\r\n            <div style=\"display: flex;flex-direction: row;width: 100%;justify-content: flex-start\">\r\n                <div style=\"width: 400px;margin-right: 10px;padding: 10px;background-color: #F2F2F2;height: 90vh;overflow-y: auto;padding-bottom: 50px\">\r\n                    <div style=\"padding-bottom: 10px;width: 100%;display: flex;flex-direction: row;justify-content: space-between\">\r\n                        <el-popconfirm\r\n                                confirm-button-text='确认'\r\n                                confirm-button-type = 'danger'\r\n                                cancel-button-text = '取消'\r\n                                cancel-button-type = 'default'\r\n                                icon=\"el-icon-info\"\r\n                                icon-color=\"red\"\r\n                                title=\"确认要清空吗？\"\r\n                                @confirm = \"clearAll\"\r\n                        >\r\n                            <el-button type=\"danger\" slot=\"reference\">一键清空</el-button>\r\n                        </el-popconfirm>\r\n<!--                        <el-popconfirm-->\r\n<!--                                confirm-button-text='确认'-->\r\n<!--                                confirm-button-type = 'success'-->\r\n<!--                                cancel-button-text='取消'-->\r\n<!--                                cancel-button-type = 'default'-->\r\n<!--                                icon=\"el-icon-success\"-->\r\n<!--                                icon-color=\"green\"-->\r\n<!--                                title=\"确认要自动排产吗？\"-->\r\n<!--                                @confirm = \"planAuto\"-->\r\n<!--                        >-->\r\n<!--                            <el-button type=\"success\" slot=\"reference\">一键排产</el-button>-->\r\n<!--                        </el-popconfirm>-->\r\n                        <el-input v-model=\"sxdm\" placeholder=\"请输入属性代码\"></el-input>\r\n                    </div>\r\n                    <div v-if=\"batch_sel_idx == -1\">\r\n                        <div v-for=\"(batch,batch_idx) in batch_list\" v-show=\"batch.show_flag == 1\" :key=\"batch_idx\" style=\"margin-bottom: 10px;border: 1px solid #E2E2E2;padding: 5px;border-radius: 5px;background-color: #FFF\">\r\n                            <el-descriptions class=\"margin-top\" :title=\"batch.sxdm\" :column=\"2\" size=\"mini\" >\r\n                                <template slot=\"extra\">\r\n                                    <el-button type=\"primary\" size=\"small\" @click=\"selBatch(batch_idx)\">选择</el-button>\r\n                                </template>\r\n                                <el-descriptions-item label=\"计划入库\">{{batch.plan_cnt}} 支</el-descriptions-item>\r\n                                <el-descriptions-item label=\"入库完成\">{{batch.instock_cnt}} 支</el-descriptions-item>\r\n                                <el-descriptions-item label=\"厂家\">{{batch.customer_name}}</el-descriptions-item>\r\n                                <el-descriptions-item label=\"材质\">{{batch.material_name}}</el-descriptions-item>\r\n                                <el-descriptions-item label=\"规格\">{{batch.spec}}</el-descriptions-item>\r\n                                <el-descriptions-item label=\"待产辊数\">{{batch.data_cnt}} 支</el-descriptions-item>\r\n                                <el-descriptions-item label=\"排产辊数\" v-if=\"batch.task_plan_cnt > 0\" content-style=\"color:red\">{{batch.task_plan_cnt}} 支</el-descriptions-item>\r\n                            </el-descriptions>\r\n                        </div>\r\n                    </div>\r\n                    <div v-else>\r\n                        <div style=\"margin-bottom: 10px;border: 1px solid #E2E2E2;padding: 5px;border-radius: 5px;background-color: #FFF\">\r\n                            <el-descriptions class=\"margin-top\" :title=\"batch_sel_item.sxdm\" :column=\"2\" size=\"mini\" >\r\n                                <template slot=\"extra\">\r\n                                    <el-button type=\"info\" plain size=\"small\" @click=\"backBatch\">返回</el-button>\r\n                                </template>\r\n                                <el-descriptions-item label=\"计划入库\">{{batch_sel_item.plan_cnt}} 支</el-descriptions-item>\r\n                                <el-descriptions-item label=\"入库完成\">{{batch_sel_item.instock_cnt}} 支</el-descriptions-item>\r\n                                <el-descriptions-item label=\"客户\">{{batch_sel_item.customer_name}}</el-descriptions-item>\r\n                                <el-descriptions-item label=\"材质\">{{batch_sel_item.material_name}}</el-descriptions-item>\r\n                                <el-descriptions-item label=\"规格\">{{batch_sel_item.spec}}</el-descriptions-item>\r\n                                <el-descriptions-item label=\"待产辊数\">{{batch_sel_item.data_cnt}} 支</el-descriptions-item>\r\n                                <el-descriptions-item v-if=\"batch_sel_item.task_plan_cnt > 0\" label=\"排产辊数\" content-style=\"color:red\">{{batch_sel_item.task_plan_cnt}} 支</el-descriptions-item>\r\n                            </el-descriptions>\r\n                        </div>\r\n                        <div style=\"width: 100%;padding-top:10px\" v-for=\"(data,data_idx) in data_list\" :key=\"data_idx\">\r\n                            <div style=\"background-color: #FFF;padding: 10px 10px 10px 10px\">\r\n                                <div style=\"width: 260px;padding: 0;display: flex;flex-direction: row;font-weight: 600\">\r\n                                    <div style=\"width:60%;line-height: 24px\">\r\n                                        <span v-text=\"data.ship_name\" ></span>\r\n                                    </div>\r\n                                    <div style=\"width:20%;line-height: 24px\">\r\n                                        {{data.g_cnt + ' 支'}}\r\n                                    </div>\r\n                                    <div style=\"width:20%;line-height: 24px\" v-if=\"(data.ship_type == 1 || data.ship_type == 9) && data.g_cnt > 0\">\r\n                                        <a @click=\"plusCount(data)\"><i style=\"font-size: 24px\" class=\"el-icon-circle-plus-outline\"></i></a>\r\n                                        <a @click=\"minusCount(data)\"><i style=\"font-size: 24px\" class=\"el-icon-remove-outline\"></i></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div v-if=\"(data.ship_type == 1 || data.ship_type == 9) && data.g_cnt > 0\">\r\n                                    <draggable handle=\".handle\" class=\"list-group\" tag=\"div\" v-model=\"data.list\" v-bind=\"dragOptions\" :move=\"onMove\" @start=\"isDragging=true\" @end=\"isDragging=false\"\r\n                                               :name=\"'data_id_'+data.ship_id\" style=\"margin-bottom: 0 !important;min-height: 40px\"\r\n                                    >\r\n                                        <div v-for=\"(element,idx) in data.list\" :key=\"idx\" class=\"list-group-item\" style=\"width: 260px;padding: 0;display: flex;flex-direction: row;height: 40px\">\r\n                                            <div style=\"width:10%;line-height: 40px;text-align: center\">\r\n                                                <i class=\"fa fa-hand-o-up handle\"></i>\r\n                                            </div>\r\n                                            <div style=\"width:55%;line-height: 40px\">\r\n                                                <span v-text=\"element.ship_name\" ></span>\r\n                                            </div>\r\n                                            <div style=\"width:20%;line-height: 40px\">\r\n                                                {{element.p_cnt + ' 支'}}\r\n                                            </div>\r\n                                            <div style=\"width:15%;line-height: 40px\">\r\n                                                <span v-text=\"element.ship_hour\" style=\"color: green\"></span> H\r\n                                            </div>\r\n                                        </div>\r\n                                    </draggable>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div style=\"display: flex;flex-direction: row;width: 100%;height: 90vh;overflow: auto;justify-content: flex-start;padding-bottom: 500px\">\r\n                    <div style=\"width: 150px;margin-top: 20px;border-top: 1px solid #E2E2E2;border-left: 1px solid #E2E2E2\">\r\n                        <div v-for=\"(task,task_idx) in task_list\" :key=\"task_idx\">\r\n                            <div style=\"margin-top: 10px;background-color: #FFDBCA;width: 100%;height: 40px;display: flex;flex-direction: row\">\r\n                                <div style=\"width: 60px;padding-left: 5px\">\r\n                                    <div>\r\n                                        <span v-text=\"task.equ_code\" style=\"font-size: 13px\"></span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <el-dropdown trigger=\"click\"  @command=\"staffSelect(task_idx,arguments[0])\">\r\n                                        <span class=\"el-dropdown-link\" v-text=\"task.staff_id == null?'无':task.name\">\r\n                                        </span>\r\n                                            <el-dropdown-menu slot=\"dropdown\">\r\n                                                <el-dropdown-item command=\"-1\"> 取消选择 </el-dropdown-item>\r\n                                                <el-dropdown-item v-for=\"(staff,staff_idx) in staff_list\" :key=\"staff_idx\" :command=\"staff_idx\">\r\n                                                    {{staff.name}} <span v-if=\"staff.rest_flag == 1\" style=\"color: red\">(休)</span></el-dropdown-item>\r\n                                            </el-dropdown-menu>\r\n                                        </el-dropdown>\r\n                                    </div>\r\n                                </div>\r\n                                <div style=\"font-size: 16px;font-weight: 600;vertical-align: center;line-height: 40px;width: 40px;\">\r\n                                    <span v-text=\"task.cnt\"></span> 支\r\n                                </div>\r\n                                <div style=\"font-size: 16px;font-weight: 600;vertical-align: center;line-height: 40px\">\r\n                                    <div v-if=\"task.rest_flag == 1\">\r\n                                        <span style=\"color: red\">休</span>\r\n                                    </div>\r\n                                    <div v-else>\r\n                                        <span  v-text=\"task.hour\"></span> h\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div style=\"min-width: 1200px;min-height: 800px;position: relative;\">\r\n                        <div style=\"position: absolute;top: 0;left: 0\">\r\n                            <div style=\"display: flex;flex-direction: row;padding-left: 10px;padding-bottom: 5px;color: #000;height: 20px\">\r\n                                <div v-for=\"hour in hour_list\" :key=\"hour\" style=\"margin-left: 80px;width: 20px;text-align: center\">\r\n                                    <span v-text=\"hour+'H'\"></span>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"display: flex;flex-direction: row;border-left: 1px solid #E2E2E2\">\r\n                                <div v-for=\"hour in hour_list\" :key=\"hour\" style=\"min-height: 800px;width: 100px;border-right: 1px solid #E2E2E2;border-top: 1px solid #E2E2E2\">\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"z-index: 999;padding-top: 20px\">\r\n                            <div v-for=\"(task,task_idx) in task_list\" :key=\"task_idx\">\r\n                                <div style=\"margin-top: 10px;\">\r\n                                    <draggable handle=\".handle\" tag=\"div\" v-model=\"task.list\" v-bind=\"dragOptions\" :move=\"onMove\" @change=\"taskChange(task_idx,arguments[0])\"\r\n                                               class=\"list-group\"  style=\"position: relative;margin-bottom: 0 !important;height: 40px;background-color: #F2F2F2\"\r\n                                    >\r\n                                        <template v-for=\"(element,idx) in task.list\">\r\n                                            <div v-if=\"element.type == 0\" class=\"list-group-item \"  :key=\"task.staff_id+''+idx\" :style=\"{'left':idx*25+'px'}\" style=\" position:absolute; top:0;height: 40px;width:25px;padding:0;border:0;background:transparent\">\r\n                                            </div>\r\n                                            <div v-else class=\"list-group-item \" :key=\"task.equ_id+''+idx\" :style=\"{'left':idx*25+'px','width':element.cnt*25+'px !important','borderColor':element.sel == 1 ? 'red' : ''}\" style=\"position:absolute; top:0;height: 40px;display: flex;flex-direction: row;padding:0;justify-content: space-between\">\r\n                                                <div style=\"width: 15px\">\r\n                                                    <i class=\"fa fa-hand-o-up handle\"></i>\r\n                                                    <el-tooltip placement=\"bottom\" effect=\"light\">\r\n                                                        <div slot=\"content\">\r\n                                                            {{ element.sxdm}}<br/>\r\n                                                            {{element.customer_name}}<br/>\r\n                                                            {{ element.spec}}<br/>\r\n                                                            {{element.ship_name}}<br/>\r\n                                                            {{element.p_cnt}} 支<br/>\r\n                                                            {{element.ship_hour*element.p_cnt}} h<br/>\r\n                                                            <div style=\"width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-top: 5px\">\r\n                                                                <i @click=\"selectTask(task_idx,idx)\" class=\"fa fa-check\" style=\"color: green;cursor: pointer;font-size: 18px\"></i>\r\n                                                                <a @click=\"deleteTask(task_idx,idx)\"><i class=\"fa fa-close\" style=\"color: red;cursor: pointer;font-size: 18px\" ></i></a>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <i class=\"fa fa-search\"></i>\r\n                                                    </el-tooltip>\r\n                                                </div>\r\n                                                <div style=\"display: flex;flex-direction: row;width: 100%\">\r\n                                                    <div>\r\n                                                        <div>\r\n                                                            <span v-text=\"element.sxdm\" ></span>\r\n                                                        </div>\r\n                                                        <div>\r\n                                                            <span>{{ element.ship_name}}</span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div style=\"line-height:40px;padding: 0 10px 0 10px; white-space: nowrap;text-overflow: ellipsis\">\r\n                                                        <span>{{ element.p_cnt}}</span> 支\r\n                                                        <span>{{ element.p_cnt*element.ship_hour}}</span> H\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </template>\r\n                                    </draggable>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport draggable from \"vuedraggable\";\r\nexport default {\r\n    name: \"plan\",\r\n    components: {\r\n        draggable\r\n    },\r\n    data() {\r\n        return {\r\n            uid:'',\r\n            sxdm:'',\r\n            batch_sel_idx:-1,\r\n            batch_sel_item: {},\r\n            batch_list:[],\r\n            data_list:[],\r\n            task_list:[],\r\n            staff_list:[],\r\n            hour_list:[1,2,3,4,5,6,7,8,9,10,11,12],\r\n            editable: true,\r\n            isDragging: false,\r\n            delayedDragging: false\r\n        };\r\n    },\r\n    created() {\r\n        let uid = this.$route.query.uid || '';\r\n        this.initData(uid);\r\n    },\r\n    methods: {\r\n        initData(uid){\r\n            this.$http.post('mes/process/plan', {uid:uid}).then((rs) => {\r\n                if (rs.status == 'ok') {\r\n                    this.batch_sel_idx = -1;\r\n                    this.batch_sel_item = {};\r\n                    this.data_list = [];\r\n                    this.uid = rs.data.uid;\r\n                    this.batch_list = rs.data.batch_list;\r\n                    this.task_list = rs.data.task_list;\r\n                    this.staff_list = rs.data.staff_list;\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('未知错误');\r\n            });\r\n        },\r\n        selBatch(idx){\r\n            let item = this.batch_list[idx];\r\n            this.$http.post('mes/process/datalist', {uid:this.uid,product_id:item.product_id}).then((rs) => {\r\n                if (rs.status == 'ok') {\r\n                    this.batch_sel_idx = idx;\r\n                    this.batch_sel_item = item;\r\n                    this.data_list = rs.data.data_list;\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('未知错误');\r\n            });\r\n        },\r\n        backBatch(){\r\n            this.batch_sel_idx = -1;\r\n            this.batch_sel_item = {};\r\n            this.data_list = [];\r\n        },\r\n        onMove({ relatedContext, draggedContext }) {\r\n            const relatedElement = relatedContext.element;\r\n            const draggedElement = draggedContext.element;\r\n            if (relatedElement){\r\n                if (relatedElement.type == 1 || relatedElement.type == 2){\r\n                    return false;\r\n                }\r\n                if (draggedElement.type == 0){\r\n                    return false;\r\n                }\r\n                if(!this.task_list[relatedElement.task_idx].ship_ids.includes(draggedElement.ship_id)){\r\n                    return false;\r\n                }\r\n            } else {\r\n                return false;\r\n            }\r\n            return  true;\r\n        },\r\n        taskChange(task_idx,elem){\r\n            let task = this.task_list[task_idx];\r\n            let item = null;\r\n            if (elem['added']){\r\n                item = elem.added;\r\n                let type = task.list[item.newIndex].type;\r\n                task.list[item.newIndex].task_idx = task_idx;\r\n                task.list[item.newIndex].type = 2;\r\n                task.list.splice(item.newIndex+1, 1);\r\n                this.saveData(task_idx,item.newIndex,task.id,type,item.element);\r\n            } else if (elem['moved']){\r\n                item = elem.moved;\r\n                this.saveData(task_idx,item.newIndex,task.id,3,item.element);\r\n            }  else if (elem['removed']){\r\n                item = elem.removed;\r\n                task.list.splice(item.oldIndex, 0,{type:0,task_idx:task_idx,cnt:1});\r\n            }\r\n            this.batch_sel_item.task_plan_cnt = this.getPlanConut(item.element.product_id);\r\n        },\r\n        saveData(task_idx,idx,task_id,type,obj){\r\n            if (type == 1){\r\n                this.$http.post('mes/process/save', {uid:this.uid,idx:idx,task_id:task_id,...obj}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let data = this.task_list[task_idx].list[idx];\r\n                        let hour = parseFloat(this.task_list[task_idx].hour);\r\n                        let cnt =  parseFloat(this.task_list[task_idx].cnt);\r\n                        this.task_list[task_idx].hour = hour + parseFloat(obj.ship_hour*obj.p_cnt);\r\n                        this.task_list[task_idx].cnt = cnt + parseFloat(obj.p_cnt);\r\n                        data.cnt = rs.time_cnt;\r\n                        data.id = rs.plan_id;\r\n                        data.team_task_detail_id = task_id;\r\n                        this.selBatch(this.batch_sel_idx);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                        this.initData(this.uid);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            } else {\r\n                this.$http.post('mes/process/change', {uid:this.uid,idx:idx,task_id:task_id,plan_id:obj.id}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let data = this.task_list[task_idx].list[idx];\r\n                        let hour = parseFloat(this.task_list[task_idx].hour);\r\n                        let cnt =  parseFloat(this.task_list[task_idx].cnt);\r\n                        this.task_list[task_idx].hour = hour + parseFloat(obj.ship_hour*obj.p_cnt);\r\n                        this.task_list[task_idx].cnt = cnt + parseFloat(obj.p_cnt);\r\n                        for(let item of this.task_list){\r\n                            if (item.id == obj.team_task_detail_id){\r\n                                item.hour -= parseFloat(obj.ship_hour*obj.p_cnt);\r\n                                item.cnt -= parseFloat(obj.p_cnt);\r\n                                break;\r\n                            }\r\n                        }\r\n                        data.cnt = rs.time_cnt;\r\n                        data.team_task_detail_id = task_id;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            }\r\n        },\r\n        deleteTask(task_idx,list_idx){\r\n            let data = this.task_list[task_idx].list[list_idx];\r\n            this.$http.post('mes/process/remove', {uid:this.uid,plan_id:data.id}).then((rs) => {\r\n                if (rs.status == 'ok') {\r\n                    this.task_list[task_idx].hour -= parseFloat(data.ship_hour*data.p_cnt);\r\n                    this.task_list[task_idx].cnt -= parseFloat(data.p_cnt);\r\n                    this.task_list[task_idx].list.splice(list_idx, 1,{type:0,task_idx:task_idx,cnt:1});\r\n                    if (this.batch_sel_idx != -1){\r\n                        this.selBatch(this.batch_sel_idx);\r\n                    }\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('未知错误');\r\n            });\r\n        },\r\n        selectTask(task_idx,list_idx){\r\n            let product_id = this.task_list[task_idx].list[list_idx].product_id;\r\n            let sel = 1;\r\n            if ( this.task_list[task_idx].list[list_idx].sel === 1){\r\n                sel = 0;\r\n            }\r\n            for(let item of this.task_list){\r\n                for(let k of item.list){\r\n                    if (k.type > 0){\r\n                        if (k.product_id == product_id){\r\n                            k.sel = sel;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        clearAll(){\r\n            this.$http.post('mes/process/clear', {uid:this.uid}).then((rs) => {\r\n                if (rs.status == 'ok') {\r\n                    this.$message.success('操作成功！');\r\n                    this.initData(this.uid);\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('未知错误');\r\n            });\r\n        },\r\n        planAuto(){\r\n\r\n        },\r\n        staffSelect(task_idx,staff_idx){\r\n            let task = this.task_list[task_idx];\r\n            let staff_id = null;\r\n            let staff = null;\r\n            if (staff_idx > -1){\r\n                staff = this.staff_list[staff_idx];\r\n                staff_id = staff.id;\r\n            }\r\n            this.$http.post('mes/process/setstaff', {uid:this.uid,task_id:task.id,staff_id:staff_id}).then((rs) => {\r\n                if (rs.status == 'ok') {\r\n                    this.$message.success('操作成功！');\r\n                    if (staff == null){\r\n                        this.task_list[task_idx].staff_id = null;\r\n                        this.task_list[task_idx].name = null;\r\n                        this.task_list[task_idx].skill_level = 0;\r\n                        this.task_list[task_idx].rest_flag = 0;\r\n                    } else {\r\n                        this.task_list[task_idx].staff_id = staff.id;\r\n                        this.task_list[task_idx].name = staff.name;\r\n                        this.task_list[task_idx].skill_level = staff.skill_level;\r\n                        this.task_list[task_idx].rest_flag = staff.rest_flag;\r\n                    }\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('未知错误');\r\n            });\r\n        },\r\n        plusCount(data){\r\n            if (data.list[0].p_cnt >= data.list[0].g_cnt){\r\n                return;\r\n            }\r\n            data.list[0].p_cnt ++;\r\n        },\r\n        minusCount(data){\r\n            if (data.list[0].p_cnt <= 1){\r\n                return;\r\n            }\r\n            data.list[0].p_cnt --;\r\n        },\r\n        getPlanConut(product_id){\r\n            let cnt = 0;\r\n            for(let item of this.task_list){\r\n                for(let k of item.list){\r\n                    if (k.type > 0){\r\n                        if (k.product_id == product_id){\r\n                            if (k.p_cnt > cnt){\r\n                                cnt = k.p_cnt;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            return cnt;\r\n        }\r\n    },\r\n    computed: {\r\n        dragOptions() {\r\n            return {\r\n                animation: 0,\r\n                group: \"description\",\r\n                disabled: !this.editable,\r\n                ghostClass: \"ghost\"\r\n            };\r\n        }\r\n    },\r\n    watch: {\r\n        isDragging(newValue) {\r\n            if (newValue) {\r\n                this.delayedDragging = true;\r\n                return;\r\n            }\r\n            this.$nextTick(() => {\r\n                this.delayedDragging = false;\r\n            });\r\n        },\r\n        sxdm(v){\r\n            if (v == ''){\r\n                for(let item of this.batch_list){\r\n                    item.show_flag = 1;\r\n                }\r\n                return;\r\n            }\r\n            for(let item of this.batch_list){\r\n                if (item.sxdm.indexOf(v) > -1){\r\n                    item.show_flag = 1;\r\n                } else {\r\n                    item.show_flag = 0;\r\n                }\r\n            }\r\n        }\r\n   }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.flip-list-move {\r\n  transition: transform 0.5s;\r\n}\r\n\r\n.no-move {\r\n  transition: transform 0s;\r\n}\r\n\r\n.ghost {\r\n  opacity: 0.5;\r\n  background: #c8ebfb;\r\n}\r\n\r\n.list-group {\r\n  min-height: 20px;\r\n}\r\n\r\n.list-group-item i {\r\n  cursor: pointer;\r\n}\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"form-group form-group-lg panel panel-default\"},[_vm._m(0),_c('div',{staticClass:\"panel-body\"},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"width\":\"100%\",\"justify-content\":\"flex-start\"}},[_c('div',{staticStyle:{\"width\":\"400px\",\"margin-right\":\"10px\",\"padding\":\"10px\",\"background-color\":\"#F2F2F2\",\"height\":\"90vh\",\"overflow-y\":\"auto\",\"padding-bottom\":\"50px\"}},[_c('div',{staticStyle:{\"padding-bottom\":\"10px\",\"width\":\"100%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"space-between\"}},[_c('el-popconfirm',{attrs:{\"confirm-button-text\":\"确认\",\"confirm-button-type\":\"danger\",\"cancel-button-text\":\"取消\",\"cancel-button-type\":\"default\",\"icon\":\"el-icon-info\",\"icon-color\":\"red\",\"title\":\"确认要清空吗？\"},on:{\"confirm\":_vm.clearAll}},[_c('el-button',{attrs:{\"slot\":\"reference\",\"type\":\"danger\"},slot:\"reference\"},[_vm._v(\"一键清空\")])],1),_c('el-input',{attrs:{\"placeholder\":\"请输入属性代码\"},model:{value:(_vm.sxdm),callback:function ($$v) {_vm.sxdm=$$v},expression:\"sxdm\"}})],1),(_vm.batch_sel_idx == -1)?_c('div',_vm._l((_vm.batch_list),function(batch,batch_idx){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(batch.show_flag == 1),expression:\"batch.show_flag == 1\"}],key:batch_idx,staticStyle:{\"margin-bottom\":\"10px\",\"border\":\"1px solid #E2E2E2\",\"padding\":\"5px\",\"border-radius\":\"5px\",\"background-color\":\"#FFF\"}},[_c('el-descriptions',{staticClass:\"margin-top\",attrs:{\"title\":batch.sxdm,\"column\":2,\"size\":\"mini\"}},[_c('template',{slot:\"extra\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.selBatch(batch_idx)}}},[_vm._v(\"选择\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"计划入库\"}},[_vm._v(_vm._s(batch.plan_cnt)+\" 支\")]),_c('el-descriptions-item',{attrs:{\"label\":\"入库完成\"}},[_vm._v(_vm._s(batch.instock_cnt)+\" 支\")]),_c('el-descriptions-item',{attrs:{\"label\":\"厂家\"}},[_vm._v(_vm._s(batch.customer_name))]),_c('el-descriptions-item',{attrs:{\"label\":\"材质\"}},[_vm._v(_vm._s(batch.material_name))]),_c('el-descriptions-item',{attrs:{\"label\":\"规格\"}},[_vm._v(_vm._s(batch.spec))]),_c('el-descriptions-item',{attrs:{\"label\":\"待产辊数\"}},[_vm._v(_vm._s(batch.data_cnt)+\" 支\")]),(batch.task_plan_cnt > 0)?_c('el-descriptions-item',{attrs:{\"label\":\"排产辊数\",\"content-style\":\"color:red\"}},[_vm._v(_vm._s(batch.task_plan_cnt)+\" 支\")]):_vm._e()],2)],1)}),0):_c('div',[_c('div',{staticStyle:{\"margin-bottom\":\"10px\",\"border\":\"1px solid #E2E2E2\",\"padding\":\"5px\",\"border-radius\":\"5px\",\"background-color\":\"#FFF\"}},[_c('el-descriptions',{staticClass:\"margin-top\",attrs:{\"title\":_vm.batch_sel_item.sxdm,\"column\":2,\"size\":\"mini\"}},[_c('template',{slot:\"extra\"},[_c('el-button',{attrs:{\"type\":\"info\",\"plain\":\"\",\"size\":\"small\"},on:{\"click\":_vm.backBatch}},[_vm._v(\"返回\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"计划入库\"}},[_vm._v(_vm._s(_vm.batch_sel_item.plan_cnt)+\" 支\")]),_c('el-descriptions-item',{attrs:{\"label\":\"入库完成\"}},[_vm._v(_vm._s(_vm.batch_sel_item.instock_cnt)+\" 支\")]),_c('el-descriptions-item',{attrs:{\"label\":\"客户\"}},[_vm._v(_vm._s(_vm.batch_sel_item.customer_name))]),_c('el-descriptions-item',{attrs:{\"label\":\"材质\"}},[_vm._v(_vm._s(_vm.batch_sel_item.material_name))]),_c('el-descriptions-item',{attrs:{\"label\":\"规格\"}},[_vm._v(_vm._s(_vm.batch_sel_item.spec))]),_c('el-descriptions-item',{attrs:{\"label\":\"待产辊数\"}},[_vm._v(_vm._s(_vm.batch_sel_item.data_cnt)+\" 支\")]),(_vm.batch_sel_item.task_plan_cnt > 0)?_c('el-descriptions-item',{attrs:{\"label\":\"排产辊数\",\"content-style\":\"color:red\"}},[_vm._v(_vm._s(_vm.batch_sel_item.task_plan_cnt)+\" 支\")]):_vm._e()],2)],1),_vm._l((_vm.data_list),function(data,data_idx){return _c('div',{key:data_idx,staticStyle:{\"width\":\"100%\",\"padding-top\":\"10px\"}},[_c('div',{staticStyle:{\"background-color\":\"#FFF\",\"padding\":\"10px 10px 10px 10px\"}},[_c('div',{staticStyle:{\"width\":\"260px\",\"padding\":\"0\",\"display\":\"flex\",\"flex-direction\":\"row\",\"font-weight\":\"600\"}},[_c('div',{staticStyle:{\"width\":\"60%\",\"line-height\":\"24px\"}},[_c('span',{domProps:{\"textContent\":_vm._s(data.ship_name)}})]),_c('div',{staticStyle:{\"width\":\"20%\",\"line-height\":\"24px\"}},[_vm._v(\" \"+_vm._s(data.g_cnt + ' 支')+\" \")]),((data.ship_type == 1 || data.ship_type == 9) && data.g_cnt > 0)?_c('div',{staticStyle:{\"width\":\"20%\",\"line-height\":\"24px\"}},[_c('a',{on:{\"click\":function($event){return _vm.plusCount(data)}}},[_c('i',{staticClass:\"el-icon-circle-plus-outline\",staticStyle:{\"font-size\":\"24px\"}})]),_c('a',{on:{\"click\":function($event){return _vm.minusCount(data)}}},[_c('i',{staticClass:\"el-icon-remove-outline\",staticStyle:{\"font-size\":\"24px\"}})])]):_vm._e()]),((data.ship_type == 1 || data.ship_type == 9) && data.g_cnt > 0)?_c('div',[_c('draggable',_vm._b({staticClass:\"list-group\",staticStyle:{\"margin-bottom\":\"0 !important\",\"min-height\":\"40px\"},attrs:{\"handle\":\".handle\",\"tag\":\"div\",\"move\":_vm.onMove,\"name\":'data_id_'+data.ship_id},on:{\"start\":function($event){_vm.isDragging=true},\"end\":function($event){_vm.isDragging=false}},model:{value:(data.list),callback:function ($$v) {_vm.$set(data, \"list\", $$v)},expression:\"data.list\"}},'draggable',_vm.dragOptions,false),_vm._l((data.list),function(element,idx){return _c('div',{key:idx,staticClass:\"list-group-item\",staticStyle:{\"width\":\"260px\",\"padding\":\"0\",\"display\":\"flex\",\"flex-direction\":\"row\",\"height\":\"40px\"}},[_c('div',{staticStyle:{\"width\":\"10%\",\"line-height\":\"40px\",\"text-align\":\"center\"}},[_c('i',{staticClass:\"fa fa-hand-o-up handle\"})]),_c('div',{staticStyle:{\"width\":\"55%\",\"line-height\":\"40px\"}},[_c('span',{domProps:{\"textContent\":_vm._s(element.ship_name)}})]),_c('div',{staticStyle:{\"width\":\"20%\",\"line-height\":\"40px\"}},[_vm._v(\" \"+_vm._s(element.p_cnt + ' 支')+\" \")]),_c('div',{staticStyle:{\"width\":\"15%\",\"line-height\":\"40px\"}},[_c('span',{staticStyle:{\"color\":\"green\"},domProps:{\"textContent\":_vm._s(element.ship_hour)}}),_vm._v(\" H \")])])}),0)],1):_vm._e()])])})],2)]),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"width\":\"100%\",\"height\":\"90vh\",\"overflow\":\"auto\",\"justify-content\":\"flex-start\",\"padding-bottom\":\"500px\"}},[_c('div',{staticStyle:{\"width\":\"150px\",\"margin-top\":\"20px\",\"border-top\":\"1px solid #E2E2E2\",\"border-left\":\"1px solid #E2E2E2\"}},_vm._l((_vm.task_list),function(task,task_idx){return _c('div',{key:task_idx},[_c('div',{staticStyle:{\"margin-top\":\"10px\",\"background-color\":\"#FFDBCA\",\"width\":\"100%\",\"height\":\"40px\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"width\":\"60px\",\"padding-left\":\"5px\"}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"13px\"},domProps:{\"textContent\":_vm._s(task.equ_code)}})]),_c('div',[_c('el-dropdown',{attrs:{\"trigger\":\"click\"},on:{\"command\":function($event){return _vm.staffSelect(task_idx,arguments[0])}}},[_c('span',{staticClass:\"el-dropdown-link\",domProps:{\"textContent\":_vm._s(task.staff_id == null?'无':task.name)}}),_c('el-dropdown-menu',{attrs:{\"slot\":\"dropdown\"},slot:\"dropdown\"},[_c('el-dropdown-item',{attrs:{\"command\":\"-1\"}},[_vm._v(\" 取消选择 \")]),_vm._l((_vm.staff_list),function(staff,staff_idx){return _c('el-dropdown-item',{key:staff_idx,attrs:{\"command\":staff_idx}},[_vm._v(\" \"+_vm._s(staff.name)+\" \"),(staff.rest_flag == 1)?_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(\"(休)\")]):_vm._e()])})],2)],1)],1)]),_c('div',{staticStyle:{\"font-size\":\"16px\",\"font-weight\":\"600\",\"vertical-align\":\"center\",\"line-height\":\"40px\",\"width\":\"40px\"}},[_c('span',{domProps:{\"textContent\":_vm._s(task.cnt)}}),_vm._v(\" 支 \")]),_c('div',{staticStyle:{\"font-size\":\"16px\",\"font-weight\":\"600\",\"vertical-align\":\"center\",\"line-height\":\"40px\"}},[(task.rest_flag == 1)?_c('div',[_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(\"休\")])]):_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(task.hour)}}),_vm._v(\" h \")])])])])}),0),_c('div',{staticStyle:{\"min-width\":\"1200px\",\"min-height\":\"800px\",\"position\":\"relative\"}},[_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"0\",\"left\":\"0\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"padding-left\":\"10px\",\"padding-bottom\":\"5px\",\"color\":\"#000\",\"height\":\"20px\"}},_vm._l((_vm.hour_list),function(hour){return _c('div',{key:hour,staticStyle:{\"margin-left\":\"80px\",\"width\":\"20px\",\"text-align\":\"center\"}},[_c('span',{domProps:{\"textContent\":_vm._s(hour+'H')}})])}),0),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"border-left\":\"1px solid #E2E2E2\"}},_vm._l((_vm.hour_list),function(hour){return _c('div',{key:hour,staticStyle:{\"min-height\":\"800px\",\"width\":\"100px\",\"border-right\":\"1px solid #E2E2E2\",\"border-top\":\"1px solid #E2E2E2\"}})}),0)]),_c('div',{staticStyle:{\"z-index\":\"999\",\"padding-top\":\"20px\"}},_vm._l((_vm.task_list),function(task,task_idx){return _c('div',{key:task_idx},[_c('div',{staticStyle:{\"margin-top\":\"10px\"}},[_c('draggable',_vm._b({staticClass:\"list-group\",staticStyle:{\"position\":\"relative\",\"margin-bottom\":\"0 !important\",\"height\":\"40px\",\"background-color\":\"#F2F2F2\"},attrs:{\"handle\":\".handle\",\"tag\":\"div\",\"move\":_vm.onMove},on:{\"change\":function($event){return _vm.taskChange(task_idx,arguments[0])}},model:{value:(task.list),callback:function ($$v) {_vm.$set(task, \"list\", $$v)},expression:\"task.list\"}},'draggable',_vm.dragOptions,false),[_vm._l((task.list),function(element,idx){return [(element.type == 0)?_c('div',{key:task.staff_id+''+idx,staticClass:\"list-group-item\",staticStyle:{\"position\":\"absolute\",\"top\":\"0\",\"height\":\"40px\",\"width\":\"25px\",\"padding\":\"0\",\"border\":\"0\",\"background\":\"transparent\"},style:({'left':idx*25+'px'})}):_c('div',{key:task.equ_id+''+idx,staticClass:\"list-group-item\",staticStyle:{\"position\":\"absolute\",\"top\":\"0\",\"height\":\"40px\",\"display\":\"flex\",\"flex-direction\":\"row\",\"padding\":\"0\",\"justify-content\":\"space-between\"},style:({'left':idx*25+'px','width':element.cnt*25+'px !important','borderColor':element.sel == 1 ? 'red' : ''})},[_c('div',{staticStyle:{\"width\":\"15px\"}},[_c('i',{staticClass:\"fa fa-hand-o-up handle\"}),_c('el-tooltip',{attrs:{\"placement\":\"bottom\",\"effect\":\"light\"}},[_c('div',{attrs:{\"slot\":\"content\"},slot:\"content\"},[_vm._v(\" \"+_vm._s(element.sxdm)),_c('br'),_vm._v(\" \"+_vm._s(element.customer_name)),_c('br'),_vm._v(\" \"+_vm._s(element.spec)),_c('br'),_vm._v(\" \"+_vm._s(element.ship_name)),_c('br'),_vm._v(\" \"+_vm._s(element.p_cnt)+\" 支\"),_c('br'),_vm._v(\" \"+_vm._s(element.ship_hour*element.p_cnt)+\" h\"),_c('br'),_c('div',{staticStyle:{\"width\":\"100%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"space-between\",\"margin-top\":\"5px\"}},[_c('i',{staticClass:\"fa fa-check\",staticStyle:{\"color\":\"green\",\"cursor\":\"pointer\",\"font-size\":\"18px\"},on:{\"click\":function($event){return _vm.selectTask(task_idx,idx)}}}),_c('a',{on:{\"click\":function($event){return _vm.deleteTask(task_idx,idx)}}},[_c('i',{staticClass:\"fa fa-close\",staticStyle:{\"color\":\"red\",\"cursor\":\"pointer\",\"font-size\":\"18px\"}})])])]),_c('i',{staticClass:\"fa fa-search\"})])],1),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"width\":\"100%\"}},[_c('div',[_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(element.sxdm)}})]),_c('div',[_c('span',[_vm._v(_vm._s(element.ship_name))])])]),_c('div',{staticStyle:{\"line-height\":\"40px\",\"padding\":\"0 10px 0 10px\",\"white-space\":\"nowrap\",\"text-overflow\":\"ellipsis\"}},[_c('span',[_vm._v(_vm._s(element.p_cnt))]),_vm._v(\" 支 \"),_c('span',[_vm._v(_vm._s(element.p_cnt*element.ship_hour))]),_vm._v(\" H \")])])])]})],2)],1)])}),0)])])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"panel-heading\"},[_c('h3',{staticClass:\"panel-title\"},[_vm._v(\"机加工班次排产计划\")])])\n}]\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.flip-list-move[data-v-b94eec2c] {\\r\\n  -webkit-transition: -webkit-transform 0.5s;\\r\\n  transition: -webkit-transform 0.5s;\\r\\n  transition: transform 0.5s;\\r\\n  transition: transform 0.5s, -webkit-transform 0.5s;\\n}\\n.no-move[data-v-b94eec2c] {\\r\\n  -webkit-transition: -webkit-transform 0s;\\r\\n  transition: -webkit-transform 0s;\\r\\n  transition: transform 0s;\\r\\n  transition: transform 0s, -webkit-transform 0s;\\n}\\n.ghost[data-v-b94eec2c] {\\r\\n  opacity: 0.5;\\r\\n  background: #c8ebfb;\\n}\\n.list-group[data-v-b94eec2c] {\\r\\n  min-height: 20px;\\n}\\n.list-group-item i[data-v-b94eec2c] {\\r\\n  cursor: pointer;\\n}\\r\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"2d6a1fba\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./plan.vue?vue&type=template&id=b94eec2c&scoped=true\"\nimport script from \"./plan.vue?vue&type=script&lang=js\"\nexport * from \"./plan.vue?vue&type=script&lang=js\"\nimport style0 from \"./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b94eec2c\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('b94eec2c')) {\n      api.createRecord('b94eec2c', component.options)\n    } else {\n      api.reload('b94eec2c', component.options)\n    }\n    module.hot.accept(\"./plan.vue?vue&type=template&id=b94eec2c&scoped=true\", function () {\n      api.rerender('b94eec2c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/process/plan.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=template&id=b94eec2c&scoped=true\""], "names": [], "sourceRoot": ""}