<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseOther;
use Envsan\Modules\Purchase\Model\PurchaseOtherDetail;
use Envsan\Modules\Purchase\Model\PurchaseStockLogs;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class OtherService extends Component
{
    private $page_id = 27;

    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.other_date,
                t1.total_quantity,
                t1.total_money,
                t1.total_money_hs,
                t1.remarks,
                t1.ext_val
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOther', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.other_date,
                t1.remarks,
                t1.ext_val,
                t99.goods_code,
                t99.goods_name,
                t99.goods_spec,
                t99.goods_model,
                t99.goods_unit,
                t99.price,
                t99.quantity,
                t99.total_money,
                t99.tax_rate,
                t99.total_money_hs,
                t2.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOther', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOtherDetail', 't1.id = t99.other_id', 't99')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't99.supplier_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t99.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectById($id)
    {
        return PurchaseOther::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseOther::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function create()
    {
        $row = new PurchaseOther();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        $type = $this->request->getPost('type', 'tstring');
        $other_date = $this->request->getPost('other_date', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $files = urldecode($this->request->getPost('files', 'tstring'));
        $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
        $detail = str_replace('%2B', '+', urldecode($this->request->getPost('detail', 'tstring')));
        if (empty($type) || empty($other_date) || empty($detail)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $this->db->begin();
        try {
            $table = new TableService();
            $files = CvtUtil::emptyToArray($files);
            $ext_data = CvtUtil::emptyToArray($ext_data);
            $detail = CvtUtil::emptyToArray($detail);
            $now = DateUtil::now();
            $user = SessionData::user();

            $row->other_date = $other_date;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->detail_data = CvtUtil::arrayToNull($detail);
            $row->ext_val = CvtUtil::arrayToNull($table->getFormDataValue($ext_data));
            $row->status = $type == 1 ? 10 : 20;
            $row->status_name = Constant::$purchase_other_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->code = $ss->useSequence(15);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("purchase_other表更新失败");
            }

            if ($row->status == 20) {
                foreach ($detail as $item)
                {
                    $goods_row = PurchaseGoods::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $item['id']]]);
                    if (empty($goods_row)) {
                        throw new \Exception("商品不存在");
                    }
                    $tax_rate = CvtUtil::emptyToDouble($goods_row->tax_rate);

                    $money = round(CvtUtil::emptyToDouble($item['price']) * CvtUtil::emptyToDouble($item['quantity']), 2);
                    $detail_row = new PurchaseOtherDetail();
                    $detail_row->uid = UUID::make();
                    $detail_row->other_id = $row->id;
                    $detail_row->goods_id = $item['id'];
                    $detail_row->goods_code = $item['code'];
                    $detail_row->goods_name = $item['name'];
                    $detail_row->goods_spec = $item['spec'];
                    $detail_row->goods_model = $item['model'];
                    $detail_row->goods_unit = $item['unit'];
                    $detail_row->price = $item['price'];
                    $detail_row->quantity = $item['quantity'];
                    $detail_row->total_money = $money;
                    $detail_row->tax_rate = CvtUtil::zeroToNull($tax_rate);
                    $detail_row->total_money_hs = round($money * (1 + $tax_rate), 2);
                    $detail_row->supplier_id = CvtUtil::blankToNull($item['supplier_id']);
                    $detail_row->update_date = $now;
                    $detail_row->update_by = $user->id;
                    $detail_row->del_flag = 0;
                    $detail_row->owner = $user->owner;
                    if (!$detail_row->save()) {
                        throw new \Exception("purchase_other_detail表更新失败");
                    }

                    $logs = new PurchaseStockLogs();
                    $logs->batch_no = $row->code;
                    $logs->data_type = 3;
                    $logs->data_id = $row->id;
                    $logs->sign = 1;
                    $logs->goods_id = $detail_row->goods_id;
                    $logs->goods_name = $detail_row->goods_name;
                    $logs->goods_unit = $detail_row->goods_unit;
                    $logs->quantity = $detail_row->quantity;
                    $logs->money = $detail_row->total_money;
                    $logs->update_date = $now;
                    $logs->update_user = $user->real_name;
                    $logs->del_flag = 0;
                    $logs->owner = $user->owner;
                    if (!$logs->save()) {
                        throw new \Exception("PurchaseStockLogs表更新失败");
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function delete(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 10){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->del_flag = 1;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function cancel(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 20){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
             $other_rows = PurchaseOtherDetail::find(['del_flag = 0 and other_id = ?1','bind'=>[1=>$row->id]]);
             if (!$other_rows->delete()){
                 throw new \Exception("PurchaseOtherDetail表更新失败");
             }
             $log_rows = PurchaseStockLogs::find(['del_flag = 0 and data_type = 3 and data_id = ?1','bind'=>[1=>$row->id]]);
             if (!$log_rows->delete()){
                throw new \Exception("PurchaseOtherDetail表更新失败");
             }
             $row->status = 10;
             $row->status_name = Constant::$purchase_other_status[$row->status];
             $row->update_date = $now;
             $row->update_by = $user->id;
             if (!$row->save()){
                 throw new \Exception("PurchaseOther表更新失败");
             }
             $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function getDetailList($other_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.goods_code,
                a.goods_name,
                a.goods_spec,
                a.goods_model,
                a.goods_unit,
                a.price,
                a.quantity,
                a.total_money,
                a.tax_rate,
                a.total_money_hs,
                s.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOtherDetail', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a.supplier_id = s.id', 's')
            ->where('a.del_flag = 0 and a.other_id = ?1', [1 => $other_id])
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }
}