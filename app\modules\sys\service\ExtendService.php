<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\ExtendColumn;
use Phalcon\Mvc\User\Component;

class ExtendService extends Component
{
    public function selectAll(){
        $name = $this->request->get('name', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.page_id,
                t1.page_name
            ')
            ->addFrom('Envsan\Modules\Sys\Model\ExtendColumn', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("t1.name like ?2", [2 => "%$name%"]);
        }
        return $builder;
    }

    public function create()
    {
        $page_id = trim($this->request->getPost('page_id', 'string'));
        if (empty($page_id))
            return ErrorHelper::WRONG_INPUT;
        $user = SessionData::user();
        $check_row = ExtendColumn::findFirst(['del_flag = 0 and page_id = ?1 and owner = ?2', 'bind' => [1 => $page_id , 2 => $user->owner]]);
        if (!empty($check_row)){
            return '重复添加';
        }
        $row = new ExtendColumn();
        $row->uid = UUID::make();
        $row->page_id = $page_id;
        $row->page_name = ConstantUtil::$page_extend_column[$page_id];
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        $row->group_id = $user->group_id;
        $row->del_flag = 0;
        $row->owner = $user->owner;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function update($row){
        $data = urldecode($this->request->getPost('data', 'string'));
        if (empty($data)){
            return ErrorHelper::WRONG_INPUT;
        }
        $data = json_decode($data,true);
        $user = SessionData::user();
        $row->form_data = json_encode($data,JSON_UNESCAPED_UNICODE);
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function delete(){
        $uid = $this->request->getPost('uid');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function selectById($id)
    {
        return ExtendColumn::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return ExtendColumn::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }


}