<?php
namespace Envsan\Modules\Work\Api;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function execAction()
    {
        $this->setJsonResponse();
        $this->moduleExistThenDie('workapi');

        $res = [
            [
                'name' => '生产管理',
                'identity' => 'work:produce',
                'action' => [
                    ['name' => '扫一扫', 'identity' => 'work:produce:view', 'comment' => ''],
                    ['name' => '扫码报工', 'identity' => 'work:produce:report', 'comment' => ''],
                    ['name' => '其他报工', 'identity' => 'work:produce:other', 'comment' => ''],
                    ['name' => '工时统计', 'identity' => 'work:produce:list', 'comment' => ''],
                ]
            ],
            [
                'name' => '质量管理',
                'identity' => 'work:quality',
                'action' => [
                    ['name' => '生产质检', 'identity' => 'work:quality:produce', 'comment' => ''],
                    ['name' => '原材料检验', 'identity' => 'work:check:list', 'comment' => ''],
                    ['name' => '质检履历', 'identity' => 'work:quality:list', 'comment' => ''],
                ]
            ],
            [
                'name' => '设备管理',
                'identity' => 'work:fault',
                'action' => [
                    ['name' => '设备故障管理', 'identity' => 'work:fault:list', 'comment' => ''],
                    ['name' => '外协修理管理', 'identity' => 'work:fault:repair', 'comment' => ''],
                ]
            ],
        ];

        $ret = new JsonData();
        $this->db->begin();
        try {
            $this->makePackage('workapi', '移动端', '1.0', '移动办公');
            $module = $this->makeModule('workapi', '移动端');
            foreach ($res as $controller) {
                $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                foreach ($controller['action'] as $action) {
                    $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage(), $e->getTraceAsString());
            $ret->message = '发生错误';
        }
        $ret->emptyIsOk();
        return json_encode($ret);
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}