<?php
namespace Envsan\Modules\Printing\Api;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Printing\Model\PrintingTemplate;
use Envsan\Modules\Printing\Service\PrintService;
use Envsan\Modules\Printing\Service\TemplateService;
use Envsan\Modules\Printing\Util\Constant;

/**
 * @noacl
 */
class TemplateController extends SuperController
{

    public function initAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new TemplateService();
            $ret = new JsonData();
            $rtn = $s->getInitData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function saveAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new TemplateService();
            $ret = new JsonData();
            $rtn = $s->saveData();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function printAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PrintService();
            $ret = new JsonData();
            $rtn = $s->getPrintData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
                $ret->form_data = $rtn->form_data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }
}