{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-6">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">开票信息</span>
                    </div>
                </div>
                <div class="portlet-body" style="min-height: 700px">
                    <div class="search-page">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">编码/名称：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="goods" v-model="goods">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 15px">
                            <thead>
                            <tr>
                                <th>编码</th>
                                <th>名称</th>
                                <th>规格</th>
                                <th>型号</th>
                                <th>数量</th>
                                <th>单价</th>
                                <th>总价</th>
                                <th>入库时间</th>
                                <th>入库单号</th>
                                <th>开票单号</th>
                                <th>选择</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="row, index in instock_list" v-if="row.show == 1">
                                <td><span v-text="row.goods_code"></span></td>
                                <td><span v-text="row.goods_name"></span></td>
                                <td><span v-text="row.goods_spec"></span></td>
                                <td><span v-text="row.goods_model"></span></td>
                                <td><span v-text="row.quantity + '('+row.goods_unit+')' "></span></td>
                                <td><span v-text="row.price + '(元/'+row.goods_unit+')' "></span></td>
                                <td><span v-text="row.total_money + '(元)' "></span></td>
                                <td><span v-text="row.instock_date"></span></td>
                                <td><span v-text="row.instock_code"></span></td>
                                <td><span v-text="row.invoice_no"></span></td>
                                <td>
                                    <a href="javascript:;" @click='addList(index)'>
                                        <i class="fa fa-arrow-right"></i>
                                    </a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="row" v-if="instock_list.length == 0" style="text-align: center;margin-top: 15px">
                            <span>没有数据</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold">付款信息{% if dispatcher.getActionName() == 'edit' %}(<span v-text="pay_no"></span>){% endif %}</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="min-height: 625px">
                            <div id="form_data" class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>采购订单</label>
                                        <div class="col-sm-8">
                                            <select class="bs-select form-control" name="order_id" v-model="order_id" data-live-search="true" data-size="8" required>
                                                <option value="">请选择采购订单</option>
                                                {% for item in order_list %}
                                                    <option value="{{ item.id }}">{{ item.order_code }} / {{ item.supplier_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>付款日期</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" placeholder="请输入付款日期" name="pay_date" v-model="pay_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>付款金额</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="pay_money" v-model="pay_money" readonly/>
                                                <span class="input-group-addon">元</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                        <thead>
                                        <tr>
                                            <th>编码</th>
                                            <th>名称</th>
                                            <th>规格</th>
                                            <th>型号</th>
                                            <th>数量</th>
                                            <th>单价</th>
                                            <th>总价</th>
                                            <th>入库时间</th>
                                            <th>入库单号</th>
                                            <th>开票单号</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="row, index in detail_list">
                                            <td><span v-text="row.goods_code"></span></td>
                                            <td><span v-text="row.goods_name"></span></td>
                                            <td><span v-text="row.goods_spec"></span></td>
                                            <td><span v-text="row.goods_model"></span></td>
                                            <td><span v-text="row.quantity + '('+row.goods_unit+')' "></span></td>
                                            <td><span v-text="row.price + '(元/'+row.goods_unit+')' "></span></td>
                                            <td><span v-text="row.total_money + '(元)' "></span></td>
                                            <td><span v-text="row.instock_date"></span></td>
                                            <td><span v-text="row.instock_code"></span></td>
                                            <td><span v-text="row.invoice_no"></span></td>
                                            <td>
                                                <a href="javascript:;" @click='delList(index)' style="color: red;font-size: 20px">
                                                    <i class="fa fa-times"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div class="row"v-if="detail_list.length == 0" style="text-align: center;margin-top: 15px">
                                        <span>没有数据</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" @click="submit" class="btn btn-primary">提交付款</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{{ partial('check') }}
{{ partial('uploader_file') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonPurchasePay }},
        methods: {
            save(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(1);
            },
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(2);
            },
            saveData(type){
                let detail_ids = [];
                for (let row of this.detail_list) {
                    detail_ids.push(row.instock_detail_id);
                }

                if (detail_ids.length == 0){
                    toastr.error('请添加付款明细');
                    return;
                }

                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('purchase/pay/edit/' ~ uid) }}';
                {% else %}
                var url= '{{ url('purchase/pay/create') }}';
                {% endif %}

                showSpin();
                $.post(url, {
                    type: type,
                    pay_date: app.pay_date,
                    order_id: app.order_id,
                    remarks: app.remarks,
                    files: encodeURI(JSON.stringify(app.files)),
                    ext_data: encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                    detail_ids: detail_ids.toString()
                }, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },
            delFile:function (index) {
                this.files.splice(index);
            },
            addList(idx){
                let goods = this.instock_list[idx];
                for(let item of this.detail_list) {
                    if (item.instock_detail_id == goods.instock_detail_id){
                        toastr.error('已存在，不能重复添加');
                        return;
                    }
                }
                this.detail_list.push({...goods});
                this.sumTotalMoney();
            },
            delList: function(idx) {
                this.detail_list.splice(idx, 1);
                this.sumTotalMoney();
            },
            goodsShow(){
                for(let item of this.instock_list){
                    item.show = 1;
                    if (this.goods != ''){
                        let name = item.goods_code + item.goods_name;
                        if (name.indexOf(this.goods) == -1){
                            item.show = 0;
                        }
                    }
                }
            },
            sumTotalMoney() {
                let total_money = 0;
                for (let row of this.detail_list) {
                    total_money += Number(row.total_money);
                }
                this.pay_money = Number(total_money.toFixed(2));
            }
        },
        watch: {
            order_id: function (val) {
                if (val == '') {
                    app.instock_list = [];
                    return;
                }
                showSpin();
                var url= '{{ url('purchase/pay/order/') }}' + val;
                $.post(url, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        app.instock_list = rs.list;
                    }
                    else {
                        app.instock_list = [];
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },
            goods: function(val) {
                this.goodsShow();
            }
        }
    });

    initUpLoaderPdf('purchase_pay');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key: getUuid(),
                url_name: rs.file_name,
                url: rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        let obj = $(this).find('input');
        app[$(obj).attr('name')] = $(obj).val();
    });
</script>
{{ partial('form_script') }}