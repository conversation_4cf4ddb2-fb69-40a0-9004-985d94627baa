<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-social-dribbble font-blue"></i>
                <span class="caption-subject font-blue bold uppercase">点检数据</span>
            </div>
        </div>
        <div class="portlet-body" style="overflow-y: auto">
            <div class="check-box">
                <div v-for="item,idx in log.form_data" style="display: flex;flex-direction: row;padding: 10px" :style="{backgroundColor : idx % 2 == 1 ? '#F2F2F2':'#FFFFFF'}">
                    <div style="width: 15%;color: #898989;font-weight: 600">
                        <span v-text="item.name"></span>
                    </div>
                    <div style="width: 15%;color: #898989;font-weight: 600">
                        <span v-text="item.label1_value"></span>
                    </div>
                    <div style="width: 20%;color: #898989;font-weight: 600">
                        <span v-text="item.label2_value"></span>
                    </div>
                    <div style="width: 20%;color: #898989;font-weight: 600">
                        <span v-text="item.label3_value"></span>
                    </div>
                    <div style="width: 10%;margin-left: 50px" >
                        <span v-text="item.value"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            log: {{ jsonLog }}
        },
        methods: {
        }
    });

    function initSize() {
        let h = $(window).height() - 30 - 27 - $(".portlet-title").outerHeight(true) - 8;
        $(".portlet-body").height(h);
    }

    $(function() {
        initSize();
    })
</script>