<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\JpushUtil;
use Envsan\Common\Util\UUID;
use Envsan\Common\Util\WeixinUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Service\OrderService as PurchaseOrderService;
use Envsan\Modules\Purchase\Service\OrderWwService;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Work\Model\WorkData;
use Envsan\Modules\Work\Model\WorkDataForm;
use Envsan\Modules\Work\Model\WorkDataRead;
use Envsan\Modules\Work\Model\WorkDesign;
use Envsan\Modules\Work\Model\WorkForm;
use Envsan\Modules\Work\Util\Constant;
use Envsan\Modules\Work\Util\FormConstant;
use Phalcon\Mvc\User\Component;

class DataCommonService extends Component
{
    public function pushFlowList($data_id,$value,$remarks,$files){
        try{
            $user = SessionData::user();
            $now = DateUtil::now();
            $work_row = WorkData::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$data_id]]);
            if (empty($work_row)){
                throw new \Exception(ErrorHelper::WRONG_INPUT);
            }
            $flow_list = json_decode($work_row->flow_list,true);
            $flow_item = json_decode(json_encode(Constant::$data_template),true);
            $flow_item['id'] = $user->id;
            $flow_item['type'] = 4;
            $flow_item['val'] = '('.$value.')';
            $flow_item['name'] = $user->real_name;
            $flow_item['text'] = CvtUtil::nullToBlank($remarks);
            $flow_item['time'] = $now;
            $flow_item['files'] = $files;
            array_push($flow_list,$flow_item);
            $work_row->flow_list = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
            $work_row->update_date = DateUtil::now();
            $work_row->update_by = $user->id;
            if (!$work_row->save()){
                throw new \Exception( '更新失败');
            }
        } catch (\Exception $e) {
            Logger::error($e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    public function businessPass($data_row,$tran_flag = true){
        Logger::error('businessPass');
        $review_type = $data_row->review_type;
        $form_data_type = $data_row->form_data_type;
        $form_data_id = $data_row->form_data_id;
        if ($review_type == 1){
            if (empty($form_data_id) || empty($form_data_type)){
                return;
            }
            $work_data_row = WorkData::findFirst(['del_flag = 0 and status = 20 and handle_status = 1 and (review_type = 0 or review_type is null) and form_data_type = ?1 and form_data_id = ?2 '
                ,'bind'=>[1=>$form_data_type , 2=>$form_data_id]]);
            if (!empty($work_data_row)){
                $work_data_row->handle_status = 3;
                if (!$work_data_row->save()) {
                    throw new \Exception("WorkData更新失败");
                }
            }
        }
        try{
            if (!empty($data_row->form_id)){
                $this->saveFormData($data_row);
            } else {
                switch ($form_data_type)
                {
                    case 1:
                        $ps = new \Envsan\Modules\Trade\Service\OrderService();
                        $ps->passSave($form_data_id);
                        break;
                    case 2:
                        $ps = new \Envsan\Modules\Mes\Service\ProductService();
                        $ps->passSave($form_data_id);
                        break;
                    case 3:
                        $ps = new \Envsan\Modules\Mes\Service\DrawingService();
                        $ps->passSave($form_data_id);
                        break;
                    case 4:
                        $ps = new \Envsan\Modules\Mes\Service\NoticeService();
                        $ps->passSave($form_data_id);
                        break;
                    case 5:
                        $ps = new OrderWwService();
                        $ps->passSave($form_data_id);
                        break;
                    case 6:
                        $ps = new PurchaseOrderService();
                        $ps->passSave($form_data_id);
                        break;
                    default:
                        break;
                }
            }
        } catch (\Exception $e) {
            Logger::error($e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    public function businessReject($data_row,$remarks){
        $form_data_id = $data_row->form_data_id;
        $form_data_type = $data_row->form_data_type;
        $review_type = $data_row->review_type;
        if (empty($form_data_id) || empty($form_data_type)){
            return;
        }
        try{
            switch ($form_data_type)
            {
                case 1:
                    $s = new \Envsan\Modules\Trade\Service\OrderService();
                    $s->rejectSave($form_data_id,$remarks);
                    break;
                case 2:
                    $s = new \Envsan\Modules\Mes\Service\ProductService();
                    $s->rejectSave($form_data_id,$remarks);
                    break;
                case 3:
                    $s = new \Envsan\Modules\Mes\Service\DrawingService();
                    $s->rejectSave($form_data_id, $remarks);
                    break;
                case 4:
                    $s = new \Envsan\Modules\Mes\Service\NoticeService();
                    $s->rejectSave($form_data_id, $remarks);
                    break;
                case 5:
                    $es = new OrderWwService();
                    $es->rejectSave($form_data_id, $remarks);
                    break;
                case 6:
                    $es = new PurchaseOrderService();
                    $es->rejectSave($form_data_id, $remarks);
                    break;
                default:
                    break;
            }
        } catch (\Exception $e) {
            Logger::error($e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    public function pushMessage($users, $uid, $type_name, $src, $user_name, $remark, $time = '',$code = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('id,real_name,managewx_open_id')
            ->addFrom('Envsan\Modules\Sys\Model\User')
            ->where('del_flag = 0 and managewx_open_id is not null and owner = ?1', [1 => SessionData::ownerId()])
            ->inWhere('id', $users);
        $users = $builder->getQuery()->execute();

        if (empty($time)) {
            $time = DateUtil::now();
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('g.name')
            ->addFrom('Envsan\Modules\Sys\Model\User', 'a')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'a.group_id = g.id', 'g')
            ->where('a.del_flag = 0 and a.real_name = ?1', [1 => $user_name]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0) {
            $row = $rows[0];
            if (!empty($row->name)) {
                $user_name .= '('.$row->name.')';
            }
        }

        $wx_util = new WeixinUtil();
        foreach ($users as $user)
        {
            $wx_util->sendReviewMsg($user->managewx_open_id, 'work_'.$uid.'_'.$src, $type_name, $user_name, $remark, $time,$code);
        }
    }

    public function pushAppMessage($users, $uid , $src, $type_name, $code ,$user_id , $remark = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('id,real_name,app_registration_id')
            ->addFrom('Envsan\Modules\Sys\Model\User')
            ->where('del_flag = 0 and app_registration_id is not null and owner = ?1', [1 => SessionData::ownerId()])
            ->inWhere('id', $users);
        $users = $builder->getQuery()->execute();
        $reg_ids = [];
        foreach ($users as $user){
            $reg_ids[] = $user->app_registration_id;
        }
        $content = '';
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.real_name,g.name as group_name')
            ->addFrom('Envsan\Modules\Sys\Model\User', 'a')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'a.group_id = g.id', 'g')
            ->where('a.del_flag = 0 and a.id = ?1', [1 => $user_id]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0) {
            $row = $rows[0];
            if (!empty($row->real_name)) {
                $content .= $row->real_name . '('.$row->group_name.')';
            }
        }
        $content .= $code;
        if (!empty($remark)){
            $content .= ','.$remark;
        }
        if (count($reg_ids) > 0){
            $wx_util = new JpushUtil();
            $wx_util->push($uid,$src,$type_name,$content,$reg_ids);
        }
    }

    public function submitDesign($doc_id,$doc_type,$group_id,$review_remarks = '',$flow_id='', $user_id = ''){
        $rs = new ReviewDataService();
        if(!empty($flow_id)){
            $design_row = WorkDesign::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $flow_id]]);
        } else {
            $design_row = null;
            $this->getFlow($group_id,$doc_type,$design_row);
        }
        if (empty($design_row)){
            throw new \Exception('没有配置审批流程,请联系相关负责人添加流程');
        }
        $rtn = new \stdClass();
        switch ($doc_type)
        {
            case 1:
                $rtn = $rs->orderData($doc_id);
                break;
            case 2:
                $rtn = $rs->productData($doc_id);
                break;
            case 3:
                $rtn = $rs->drawingData($doc_id);
                break;
            case 4:
                $rtn = $rs->noticeData($doc_id);
                break;
            case 5:
                $rtn = $rs->purchaseWwData($doc_id);
                break;
            case 6:
                $rtn = $rs->purchaseData($doc_id);
                break;
            default:
                break;
        }
        $data_list = $this->setRowsData($doc_type,$rtn->data,CvtUtil::emptyToArray($design_row->abstrakt_keys));
        $this->saveWorkData($design_row,$data_list,$doc_id,$doc_type,$review_remarks,$user_id);
    }

    public function submitCancelFlow($doc_id,$doc_type,$group_id,$review_remarks = '',$user_id = ''){
        $data_row = WorkData::findFirst(['del_flag = 0 and status = 20 and handle_status = 1 and (review_type = 0 or review_type is null) and form_data_type = ?1 and form_data_id = ?2 ','bind'=>[1=>$doc_type,2=>$doc_id]]);
        if (empty($data_row)){
            throw new \Exception( '审批通过数据不存在!');
        }
        $design_row = null;
        $this->getFlow($group_id,$doc_type,$design_row);
        if (empty($design_row)){
            throw new \Exception('没有配置审批流程,请联系相关负责人添加流程');
        }
        $data_list = [];
        $data_list['form_data'] = CvtUtil::emptyToArray($data_row->form_data);
        $data_list['abstrakt_data'] = CvtUtil::emptyToArray($data_row->abstrakt_data);
        $this->saveWorkData($design_row,$data_list,$doc_id,$doc_type,$review_remarks,$user_id,1);
    }

    public function cancelFlow($doc_id,$doc_type,$review_remarks = ''){
        $data_row = WorkData::findFirst(['del_flag = 0 and status = 20 and handle_status = 1 and (review_type = 0 or review_type is null) and form_data_type = ?1 and form_data_id = ?2 ','bind'=>[1=>$doc_type,2=>$doc_id]]);
        if (!empty($data_row)){
            $user = SessionData::user();
            $now = DateUtil::now();
            $flow_list = json_decode($data_row->flow_list,true);
            $flow_item = json_decode(json_encode(Constant::$data_template),true);
            $flow_item['id'] = $user->id;
            $flow_item['type'] = 5;
            $flow_item['status'] = 2;
            $flow_item['name'] = $user->real_name;
            $flow_item['val'] = '(退回)';
            $flow_item['text'] = CvtUtil::nullToBlank($review_remarks);
            $flow_item['time'] = $now;
            $flow_item['files'] = [];
            array_push($flow_list,$flow_item);
            $data_row->pressing_flag = 0;
            $data_row->anchor_users = null;
            $data_row->handle_status = 2;
            $data_row->flow_list = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
            $data_row->update_date = DateUtil::now();
            $data_row->update_by = $user->id;
            if (!$data_row->save()){
                throw new \Exception( 'WorkData表更新失败');
            }
        }
    }

    private function saveWorkData($design_row, $data_list, $doc_id, $doc_type,$review_remarks = '', $user_id = '',$review_type = 0){
        $user = SessionData::user();
        if (!empty($user_id)){
            $user_row = User::findFirst(['id = ?1','bind'=>[1=>$user_id]]);
            if (!empty($user_row)){
                $user = $user_row;
            }
        }
        $flow_data = json_decode($design_row->flow_data,true);
        $flow_list = json_decode($design_row->flow_list,true);
        $hidden = CvtUtil::emptyToArray($design_row->hidden_data);
        $hidden_data = [];
        foreach ($hidden as $hidden_key => $hidden_item){
            if (array_key_exists('keys',$hidden[$hidden_key])){
                if (count($hidden[$hidden_key]['keys']) > 0){
                    $hidden_data[$hidden_key] = $hidden[$hidden_key]['keys'];
                }
            }
        }
        $now = DateUtil::now();
        $row_flow_list = [];
        $flow_item = json_decode(json_encode(Constant::$data_template),true);
        $flow_item['id'] = $user->id;
        $flow_item['name'] = $user->real_name;
        $flow_item['val'] = '(发起申请)';
        $flow_item['time'] = $now;
        $flow_item['text'] = $review_remarks;
        $row = new WorkData();
        $ss = new SequenceService();
        $row->uid = UUID::make();
        $row->code =  $ss->useSequence(0, $user->owner);
        $row->type_id = $design_row->id;
        $row->type_name = $design_row->name;
        array_push($row_flow_list,$flow_item);
        $push_users = array();
        $user_ids = '|' . $user->id . '|';
        foreach ($flow_data as $u){
            if ($u['id'] != ''){
                if (strpos($user_ids, '|' . $u['id'] . '|') ===  false){
                    $user_ids .= $u['id'] . '|';
                }
            }
        }
        foreach ($flow_list as &$item){
            if (!array_key_exists('stock', $item)) {
                $item['stock'] = 0;
            }
            $item['list'] = [];
            $item['nlist'] = [];
            foreach ($flow_data as $u){
                if ($item['id'] == $u['anchor']){
                    if ($u['notify'] == 0){
                        array_push( $item['list'],[
                            'id' => $u['id'],
                            'name' => $u['name'],
                            'status' => 0
                        ]);
                    } else {
                        array_push( $item['nlist'],[
                            'id' => $u['id'],
                            'name' => $u['name']
                        ]);
                    }
                }
            }
        }
        $first_anchor = $flow_list[0];
        $anchor_users = '|';
        foreach ($first_anchor['list'] as $anchor){
            if ($anchor['id'] != ''){
                $anchor_users .= $anchor['id'] . '|';
                array_push($push_users,$anchor['id']);
            }
        }

        $row->status = 15;
        $row->pressing_flag = 0;
        $row->handle_status = 0;
        $row->read_flag = 0;
        $row->anchor_data = json_encode($first_anchor,JSON_UNESCAPED_UNICODE);
        $row->anchor_users = $anchor_users;
        $row->flow_list = json_encode($row_flow_list,JSON_UNESCAPED_UNICODE);
        $row->flow_data = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
        $row->flow_users = $user_ids;
        $row->form_data_id = $doc_id;
        $row->form_data_type = $doc_type;
        $row->form_data = json_encode($data_list['form_data'],JSON_UNESCAPED_UNICODE);
        $row->abstrakt_data = json_encode($data_list['abstrakt_data'],JSON_UNESCAPED_UNICODE);
        $row->files = '[]';
        $row->review_type = $review_type;
        $row->remarks = $review_remarks;
        $row->hidden_data = json_encode($hidden_data,JSON_UNESCAPED_UNICODE);
        $row->create_date = $now;
        $row->create_by = $user->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->del_flag = 0;
        $row->create_group_id = $user->group_id;
        $row->group_id = $design_row->group_id;
        $row->owner = $design_row->owner;
        if (!$row->save()){
            throw new \Exception( 'WorkData表更新失败');
        }
        $row->pid = $row->id;
        $row->main_id = $row->id;
        if (!$row->save()){
            throw new \Exception( 'WorkData表更新失败');
        }
        if (count($push_users) > 0){
            $type_name = $row->type_name;
            $this->pushAppMessage($push_users,$row->uid,2,$type_name,$row->code,$row->create_by,$review_remarks);
        }

        if ($design_row->notify_flow_flag == 1){
            $this->autoPass($row);
        }
    }

    public function autoPass($work_row){
        $user = SessionData::user();
        $now = DateUtil::now();
        if ($work_row->status >= 20){
            throw new \Exception( '该审批流已结束');
        }
        $anchor_data = json_decode($work_row->anchor_data,true);
        $push_read_users = array();
        $anchor_users = '|';
        foreach ($anchor_data['nlist'] as $n){
            $read_row = new WorkDataRead();
            $read_row->data_id = $work_row->id;
            $read_row->user_id = $n['id'];
            $read_row->read_flag = 0;
            $read_row->update_date = $now;
            $read_row->update_by = $user->id;
            $read_row->del_flag = 0;
            $read_row->owner = $user->owner;
            if (!$read_row->save()){
                throw new \Exception( 'WorkDataRead,抄送表更新失败');
            }
            array_push($push_read_users,$n['id']);
        }
        $work_row->status = 20;
        $work_row->handle_status = 1;
        if (!empty($work_row->form_data_type)){
            $work_row->read_flag = 1;
        }
        $work_row->anchor_data = null;
        $work_row->anchor_users = $anchor_users;
        $work_row->update_date = DateUtil::now();
        $work_row->update_by = $user->id;
        if (!$work_row->save()){
            throw new \Exception( '更新失败');
        }
        $this->businessPass($work_row);
        if (count($push_read_users) > 0){
            $this->pushAppMessage($push_read_users,$work_row->uid,3,$work_row->type_name,$work_row->code,$work_row->create_by,null);
        }
    }

    public function getFlow($group_id,$doc_type,&$design_row){
        $design_row = WorkDesign::findFirst(['del_flag = 0 and status = 1 and group_id =?1 and doc_type =?2','bind'=>[1=>$group_id , 2=>$doc_type]]);
        if (empty($design_row)){
            $group = Group::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$group_id]]);
            if (!empty($group->pid)){
                $this->getFlow($group->pid,$doc_type,$design_row);
            }
        }
    }
    
    public function setRowsData($doc_type, $rtn_data,$abstrakt_keys = [])
    {
        $abstrakt_data = [];
        $form_data = [];
        $sum_list = [];
        $data_list = [];
        foreach (FormConstant::$form_data_list[$doc_type] as $key => $item){
            if (array_key_exists($key, $rtn_data)){
                $f_data = FormConstant::$form_data_list[$doc_type][$key];
                $f_data['value'] = $rtn_data[$key];
                $form_data[] = $f_data;
                if (in_array($key.'_1',$abstrakt_keys)){
                    $abstrakt_data[] = [
                        'name' => $f_data['name'],
                        'value' => $f_data['value']
                    ];
                }
            }
        }
        if (array_key_exists('ext_data',$rtn_data)){
            $ext_data = CvtUtil::emptyToArray($rtn_data['ext_data']);
            foreach ($ext_data as $id => $ext_val){
                $e_data = json_decode(json_encode(Constant::$review_field_template),true);
                $e_data['id'] = $id;
                $e_data['name'] = $ext_val['title'];
                $e_data['value'] = $ext_val['value'];
                $e_data['unit'] = $ext_val['unit'];
                $form_data[] = $e_data;
            }
        }
        if (array_key_exists('files',$rtn_data)){
            $data = [
                'id' => 'files',
                'type' => '88',
                'name' => '附件',
                'sum_list' => [],
                'data_list' => CvtUtil::emptyToArray($rtn_data['files'])
            ];
            $form_data[] = $data;
        }
        if (array_key_exists('details',$rtn_data)){
            $details = $rtn_data['details'];
            foreach ($details as $detail){
                $detail = json_decode(json_encode($detail),true);
                $detail_list = [];
                foreach (FormConstant::$detail_data_list[$doc_type] as $key => $item){
                    if (array_key_exists($key, $detail)){
                        $d_data = FormConstant::$detail_data_list[$doc_type][$key];
                        $d_data['value'] = $detail[$key];
                        $detail_list[] = $d_data;
                    }
                }
                if (array_key_exists('ext_data',$detail)){
                    $ext_data = CvtUtil::emptyToArray($detail['ext_data']);
                    foreach ($ext_data as $id => $ext_val){
                        $e_data = json_decode(json_encode(Constant::$review_field_template),true);
                        $e_data['id'] = $id;
                        $e_data['name'] = $ext_val['title'];
                        $e_data['value'] = $ext_val['value'];
                        $e_data['unit'] = $ext_val['unit'];
                        $detail_list[] = $e_data;
                    }
                    if (array_key_exists('files',$detail)){
                        $data = [
                            'id' => 'files',
                            'type' => '88',
                            'name' => '附件',
                            'sum_list' => [],
                            'data_list' => CvtUtil::emptyToArray($detail['files'])
                        ];
                        $detail_list[] = $data;
                    }
                }
                $data_list[] = $detail_list;
            }
            $data = [
                'id' => 'details',
                'type' => '99',
                'name' => '评审明细',
                'sum_list' => $sum_list,
                'data_list' => $data_list
            ];
            $form_data[] = $data;
        }
        if (count($data_list) > 0){
            $data_item = $data_list[0];
            foreach ($data_item as $detail_item){
                if (in_array($detail_item['id'].'_2',$abstrakt_keys)){
                    $abstrakt_data[] = [
                        'name' => $detail_item['name'],
                        'value' => $detail_item['value']
                    ];
                }
            }
        }
        return [
            'form_data' => $form_data,
            'abstrakt_data' => $abstrakt_data
        ];
    }

    public function getFlowList($work_data){
        $ws = new WorkService();
        $user = SessionData::user();
        $pass_list = json_decode($work_data->flow_list,true);
        $flow_data = json_decode($work_data->flow_data,true);
        $data = $ws->getWorkRow($work_data);

        $data->form_list = [];
        if (!empty($work_data->form_data)){
            $data->form_list = json_decode($work_data->form_data,true);
        }
        $data->goods_list = [];
        if (!empty($work_data->goods_data)){
            $data->goods_list = json_decode($work_data->goods_data,true);
        }

        foreach ($pass_list as &$p){
            $p['icon'] = $ws->getIconName($p['name']);
            $p['time'] = date('m-d H:i', strtotime($p['time']));
            $send = '';
            if (isset($p['send'])){
                foreach ($p['send'] as $item){
                    $send .= '@'.$item['name'].' ';
                }
            }
            $p['send'] = $send;
        }
        $anchor_list = [];
        if ($work_data->status == 10){
            $data->auth = 0;
        } else if ($work_data->status < 20){
            $data->auth = 1;
            $anchor_data = json_decode($work_data->anchor_data,true);
            $data->anchor = $anchor_data['name'];
            foreach ($anchor_data['list'] as $u){
                if ($u['status'] == 0){
                    if ($u['id'] == $user->id){
                        $data->auth = 2;
                    }
                }
            }
            $push_flag = false;
            foreach ($flow_data as $f){
                if ($f['id'] == $anchor_data['id']){
                    $push_flag = true;
                }
                if ($push_flag){
                    array_push($anchor_list,$f);
                }
            }
        } else {
            $data->anchor = '已完成';
        }
        $data->flow_list = $pass_list;
        $data->anchor_list = $anchor_list;
        return $data;
    }

    private function saveFormData($work_row){
        $ext_data = CvtUtil::emptyToArray($work_row->form_data);
        $data_list = [];
        foreach ($ext_data as $ext_item){
            if ($ext_item['type'] == 99){
                $data_list = $ext_item['data_list'];
                break;
            }
        }
        $form_list = [];
        if (count($data_list) == 0){
            $form_data_val = array();
            foreach ($ext_data as $form_item){
                if ($form_item['type'] < 99){
                    if ($form_item['type'] == 2) {
                        if (empty($form_item['value'])) {
                            $form_data_val[$form_item['id']] = '';
                        } else {
                            $form_data_val[$form_item['id']] = doubleval($form_item['value']);
                        }
                    } else {
                        $form_data_val[$form_item['id']] = $form_item['value'];
                    }
                }
            }
            $form_list[] = json_encode($form_data_val,JSON_UNESCAPED_UNICODE);
        } else {
            foreach ($data_list as $data_item){
                $form_data_val = array();
                foreach ($ext_data as $form_item){
                    if ($form_item['type'] < 99){
                        if ($form_item['type'] == 2) {
                            if (empty($form_item['value'])) {
                                $form_data_val[$form_item['id']] = '';
                            } else {
                                $form_data_val[$form_item['id']] = doubleval($form_item['value']);
                            }
                        } else {
                            $form_data_val[$form_item['id']] = $form_item['value'];
                        }
                    }
                }
                foreach ($data_item as $data_val){
                    if ($data_val['type'] == 2) {
                        if (empty($data_val['value'])) {
                            $form_data_val[$data_val['id']] = '';
                        } else {
                            $form_data_val[$data_val['id']] = doubleval($data_val['value']);
                        }
                    } else {
                        $form_data_val[$data_val['id']] = $data_val['value'];
                    }
                }
                $form_list[] = json_encode($form_data_val,JSON_UNESCAPED_UNICODE);
            }
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        foreach ($form_list as $save_item){
            $form = new WorkDataForm();
            $form->data_id = $work_row->id;
            $form->form_id = $work_row->form_id;
            $form->json_data = $save_item;
            $form->update_date = $now;
            $form->update_by = $user->id;
            $form->del_flag = 0;
            $form->owner = $user->owner;
            if (!$form->save()) {
                throw new \Exception("WorkDataForm表更新失败");
            }
        }
    }
}