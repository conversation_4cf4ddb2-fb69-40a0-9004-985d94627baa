<?php

namespace Envsan\Common\Model;

class OperationHistory extends BaseModel
{

    /**
     *
     * @var string
     * @Primary
     * @Identity
     * @Column(type="string", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $event_name;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $table_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $record_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $operation_type;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $old_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $new_data;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $operator_user;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $ip_address;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'operation_history';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return OperationHistory[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return OperationHistory
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
