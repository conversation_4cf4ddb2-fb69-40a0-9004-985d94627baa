<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Company;
use Phalcon\Mvc\User\Component;
use Upyun\Upyun;

class CommonService extends Component
{
    function uploadImage($folder_name) {
        $ret = array();
        $ret['status'] = 'error';
        $ret['message'] = '';
        if ($this->request->hasFiles() == true) {
            $config = new \Upyun\Config($this->config->upyun->bucket, $this->config->upyun->user, $this->config->upyun->password);
            $upyun = new UpYun($config);
            foreach ($this->request->getUploadedFiles() as $file) {
                if ($file->getSize() > 5 * 1024 * 1024) {
                    break;
                }

                $ext = pathinfo($file->getName(), PATHINFO_EXTENSION);
                if ($ext !== 'jpg' && $ext !== 'jpeg' && $ext !== 'png') {
                    break;
                }

                $path = $this->config->upyun->uploadDir.UUID::make().'.'.$ext;

                try {
                    if ($file->moveTo($path)) {
                        $file_handle = fopen($path, 'r');
                        $filename = time().'.'.$ext;
                        $company = Company::findFirst('group_id = '.SessionData::groupId());
                        $id = $company->id;
                        $filename = '/image/stamp/' . $id . '/' . $filename;
                        $upyun->write($filename, $file_handle);
                        if (is_resource($file_handle))
                            fclose($file_handle);
                        unlink($path); //删除临时文件

                        $ret['status'] = 'ok';
                        $ret['path'] = $this->getImagePath();
                        $ret['file_name'] = $filename;
                    }
                } catch (\Exception $e) {
                    Logger::error($e->getMessage(), $e->getTraceAsString());
                }
                break;
            }
        }
        return $ret;
    }

    function getImagePath() {
        return $this->config->upyun->host;
    }

    public function getCjList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.id,a.uid,a.short_name as name')
            ->addFrom('Envsan\Modules\Sys\Model\Group', 'a')
            ->where('a.del_flag = 0 and type = ?1 and owner = ?2',[1=>'生产车间',2=>SessionData::ownerId()])
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }
}