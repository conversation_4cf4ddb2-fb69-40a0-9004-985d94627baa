<script>
    class ButtonNode extends HtmlNode {
        setHtml(rootEl) {
            const { properties } = this.props.model;
            const el = document.createElement('div');
            el.className = 'uml-wrapper';
            const html = `
                 <div style="width: 200px;height: 120px;border: 2px #0052D9 solid;border-radius: 5px;background-color: #FFF">
                    <div style="display: flex;flex-direction: row;background-color: #f2f2f2;padding: 10px;border-bottom: 1px #E2E2E2 solid;justify-content: space-between;border-radius: 5px 5px 0px 0px;">
                        <div>
                            <span style="font-size: 16px;">${properties.type_name ? properties.type_name:''}</span>
                        </div>
                    </div>
                    <div style="padding-top: 5px">
                        <div style="padding: 5px 10px 5px 10px">
                            工艺名称：${properties.name ? properties.name:''}
                        </div>
                    <div style="padding: 5px 10px 5px 10px">
                            构成数量：${properties.cnt ? properties.cnt:''}
                        </div>
                    </div>
                </div>
                `;
            el.innerHTML = html;
            rootEl.innerHTML = '';
            rootEl.appendChild(el);
        }
    }

    class ButtonNodeModel extends HtmlNodeModel {
        setAttributes() {
            this.width = 200;
            this.height = 120;
            this.text.editable = false;
        }
        createId() {
            return getUuid2();
        }
    }

    function shipCard(type){
        return {
            type: type,
            view: ButtonNode,
            model: ButtonNodeModel
        }
    }
</script>