<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\CommonService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseSupplier;
use Envsan\Modules\Purchase\Service\SupplierService;


/**
 * @name('供应商')
 */
class SupplierController extends SuperController
{
    private $page_id = 23;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new SupplierService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $rs = new SupplierService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = (new PurchaseSupplier())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $this->view->jsonSupplier = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
    }

    /**
     * @acl({'link':'purchase:supplier:create'})
     */
    function editAction($uid)
    {
        $rs = new SupplierService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $this->view->jsonSupplier = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
        $this->view->pick('supplier/create');
    }

    /**
     * @acl({'link':'purchase:supplier:create'})
     */
    public function deleteAction(){
        $s = new SupplierService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new SupplierService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }
}