{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered" style="margin-bottom: 0;padding-bottom: 0">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-6 col-lg-6">
                        <h3 class="page-title">生产通知管理</h3>
                    </div>
                    <div class="col-md-6 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        {% if acl.has('mes:notice:create') %}
                            <button type="button" class="btn yellow" onclick="create()">
                                <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                            </button>
                        {% endif %}
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        {{ partial('table') }}
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {},
        mounted:function (){
            let btn_list = [];
            {% if acl.has('mes:notice:create') %}
            btn_list.push({type:1,name:'编辑',fn:(data)=>{return data.status == 10}});
            btn_list.push({type:2,name:'删除',fn:(data)=>{return data.status == 10}});
            btn_list.push({type:5,name:'变更',fn:(data)=>{return data.status == 30}});
            btn_list.push({type:6,name:'停产',fn:(data)=>{return data.status == 30}});
            btn_list.push({type:7,name:'恢复生产',fn:(data)=>{return data.status == 40}});
            {% endif %}
            btn_list.push({type:3,name:'查看详情',fn:(data)=>{return (data.status == 30 || data.status == 40)}});
            btn_list.push({type:4,name:'查看审批'});
            
            app_ext_table.init({{ page_id }},btn_list,'{{ url('mes/notice/list/json') }}');
        },
        methods: {
            search: function() {
                app_ext_table.search();
            },
            reset: function() {
                app_ext_table.reset();
            },
            excel:function (){
                app_ext_table.excel("{{ url('mes/notice/export') }}");
            },
            getParams: function() {
                return {}
            },
            dataView:function (type,row){
                if (type == 1){
                    edit('edit', row.uid);
                } else if (type == 2){
                    del(row.uid);
                } else if (type == 3){
                    view(row.uid);
                } else if (type == 4) {
                    flow(4,row.id);
                } else if (type == 5) {
                    edit('change', row.uid);
                } else if (type == 6) {
                    stop(row.uid);
                } else if (type == 7) {
                    restart(row.uid);
                }
            }
        }
    });

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title:'新增',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('mes/notice/create') }}',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function edit(type, uid) {
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('mes/notice/edit/') }}'+ type + '/' + uid,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function view(uid) {
        top.layer.open({
            title:'查看',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('mes/notice/view/') }}' + uid
        });
    }

    function del(uid) {
        var dlg = top.layer.confirm('确认删除吗?', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('mes/notice/delete') }}", {uid: uid}, function(rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
                else {
                    toastr.error(rs.message);
                }
            })
        });
    }

    function stop(uid) {
        var dlg = top.layer.confirm('确认停产吗?', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('mes/notice/stop') }}", {uid: uid}, function(rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
                else {
                    toastr.error(rs.message);
                }
            })
        });
    }

    function restart(uid) {
        var dlg = top.layer.confirm('确认恢复生产吗?', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('mes/notice/restart') }}", {uid: uid}, function(rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
                else {
                    toastr.error(rs.message);
                }
            })
        });
    }

    function flow(type, id) {
        top.layer.open({
            title: '审批流程',
            type: 2,
            area: ['100%', '100%'],
            content: "{{ url('work/flow/view/') }}" + type + '/' + id
        });
    }
</script>