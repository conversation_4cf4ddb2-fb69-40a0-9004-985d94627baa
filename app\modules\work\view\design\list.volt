{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/responsiveTabs/css/main.css') %}
{% do assets.collection('css').addCss('static/global/css/order.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">业务流程管理</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">审批类型</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="type_name" v-model="type_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">业务类型</label>
                            <div class="col-md-9">
                                <select name="in_out_type" v-model="in_out_type" class="form-control bs-select" data-live-search="true" data-size="8">
                                    <option value="">全部</option>
                                    {% for key, value in docTypes %}
                                        <option value="{{ key }}">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">使用组织</label>
                            <div class="col-md-9">
                                <select name="group_id" v-model="group_id" class="form-control bs-select" data-live-search="true" data-size="8">
                                    <option value="">全部</option>
                                    {% for row in groupList %}
                                        <option value="{{ row.id }}">{{ row.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3 to-right">
                        <button type="button" class="btn green" onclick="change()">
                            <i class="fa fa-exchange"></i>&nbsp;<span>更换审批人</span>
                        </button>
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   data-mobile-responsive="true"
                   data-query-params="app.params"
                   data-pagination="true"
                   data-url="{{ url('work/design/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server"
                   class="table table-striped table-condensed">
                <thead class="bg-blue">
                <tr>
                    <th data-field="name">审批类型</th>
                    <th data-field="doc_type" data-formatter="docFormatter">业务类型</th>
                    <th data-field="group_name">使用组织</th>
                    <th data-field="status" data-formatter="statusFormatter">启用状态</th>
                    <th data-field="real_name">更新人</th>
                    <th data-field="update_date">更新时间</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a href="javascript:" onclick="save('@id@')"><i class="fa fa-pencil"></i> 配置流程</a></li>
            <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-pencil"></i> 编辑流程</a></li>
            <li><a href="javascript:" onclick="view('@id@')"><i class="fa fa-eye"></i> 查看流程</a></li>
            <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-times"></i> 删除</a></li>
        </ul>
    </div>
</div>
<script>
    var docTypes = {{ docTypes | json_encode}};
    var app = new Vue({
        el: '#app',
        data: {
            type_name: '',
            group_id: '',
            in_out_type: '',
        },
        methods: {
            search: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params : function (p) {
                p.type_name = this.type_name;
                p.group_id = this.group_id;
                p.in_out_type = this.in_out_type;
                return p;
            },
            reset: function () {
                this.type_name = '';
                this.group_id = '';
                this.in_out_type = '';
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    var $table = $('#table');

    $table.bootstrapTable();
    var actHtml = $('#act').html();
    function actionFormatter(v, row, idx) {
        return actHtml.replace(/@id@/g, row.id);
    }

    function docFormatter(v) {
        if(v == '' || v == null){
            return '-';
        } else {
            return docTypes[v];
        }
    }

    function statusFormatter(val) {
        if (val == 1){
            return '<span class="label label-success">启用</span>';
        } else {
            return '<span class="label label-danger">未启用</span>';
        }
    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新建',
            type: 2,
            area: ['100%', '100%'],
            content: "{{ url('work/design/create') }}",
            end: function () {
                $table.bootstrapTable('refresh');
            }
        });
    }

    function change(){
        top.window.layer_result='';
        top.layer.open({
            title:'更换审批人',
            type: 2,
            area: ['40em', '50em'],
            content: '{{ url('work/design/change') }}',
            end:function(){}
        });
    }

    function edit(id) {
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('work/design/create/') }}' + id,
            end:function(){
                $table.bootstrapTable('refresh');
            }
        });
    }
    
    function save(id) {
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['60em', '50em'],
            content: '{{ url('work/design/bind/') }}' + id,
            end:function(){
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function view(id) {
        window.open('{{ url('work/design/view/') }}' + id)
    }

    function del(id) {
        var dlg = top.layer.confirm('确认删除吗?', function () {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('work/design/delete') }}", { id: id }, function (rs) {
                closeSpin();
                if (rs.status == 'ok') {
                    toastr.success('操作成功!');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败!');
                }
            })
        });
    }

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>