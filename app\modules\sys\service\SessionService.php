<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Util\SessionUtil;
use Phalcon\Mvc\User\Component;

class SessionService extends Component
{
    public function getOnlineUser($cur, $limit)
    {
        $redis = $this->redis;
        $redis->select($this->config->redis->sessionIdx);
        $redis->setOption(\Redis::OPT_SCAN, \Redis::SCAN_RETRY);

        $arr = array();
        $it = null;
        while ($arr_keys = $redis->scan($it, '', $limit)) {
            try {
                foreach ($arr_keys as $str_key) {
                    $data = $redis->get($str_key);
                    if( !empty($data) ){
                        $sessionObj = SessionUtil::unserialize($data);
                        if( !empty($sessionObj) ) {
                            $user = $sessionObj['user'];
                            if ($user != null) {
                                $d = new \stdClass();
                                $d->id = str_replace('_PHCRsession_', '', $str_key);
                                $d->real_name = $user->real_name;
                                $d->login_name = $user->login_name;
                                $d->empno = $user->empno;
                                $d->status = $this->session->status();
                                array_push($arr, $d);
                            }
                        }
                    }
                }
            }
            catch (\Exception $e){
                Logger::error($e->getMessage(), $e->getTraceAsString());
            }

            break;
        }

        $obj = new \stdClass();
        $obj->rows = $arr;
        $obj->total = count($arr);
        return $obj;
    }

    public function offline($sessionId)
    {
        $redis = $this->redis;
        $redis->select($this->config->redis->sessionIdx);
        if($this->session->getId()!=$sessionId)
            return $redis->delete('_PHCRsession_'.$sessionId);
        return 0;
    }
}