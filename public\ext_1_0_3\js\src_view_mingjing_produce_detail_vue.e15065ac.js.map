{"version": 3, "file": "js/src_view_mingjing_produce_detail_vue.e15065ac.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwSA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC/2BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/mingjing/produce_detail.vue", "webpack://sfp_ext/./src/view/mingjing/produce_detail.vue", "webpack://sfp_ext/./node_modules/core-js/modules/es.array.find.js", "webpack://sfp_ext/./node_modules/core-js/modules/es.iterator.find.js", "webpack://sfp_ext/./src/view/mingjing/produce_detail.vue?1ab4", "webpack://sfp_ext/./src/view/mingjing/produce_detail.vue?c594", "webpack://sfp_ext/./src/view/mingjing/produce_detail.vue?7fc9", "webpack://sfp_ext/./src/view/mingjing/produce_detail.vue?9296", "webpack://sfp_ext/./src/view/mingjing/produce_detail.vue?7c00", "webpack://sfp_ext/./src/view/mingjing/produce_detail.vue?c0d8"], "sourcesContent": ["<template>\r\n    <div class=\"container\">\r\n        <div v-loading=\"loading\" element-loading-text=\"加载中...\">\r\n            <!-- 批次和产品选择 -->\r\n            <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n                <div slot=\"header\" class=\"clearfix\">\r\n                    <span style=\"font-weight: bold; font-size: 18px;\">产品检验</span>\r\n                </div>\r\n                \r\n                <el-form label-width=\"100px\">\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"12\">\r\n                            <el-form-item label=\"生产批次\" required>\r\n                                <el-select\r\n                                    v-model=\"selectedBatch\"\r\n                                    placeholder=\"请选择生产批次\"\r\n                                    filterable\r\n                                    @change=\"onBatchChange\"\r\n                                    style=\"width: 100%;\"\r\n                                >\r\n                                    <el-option\r\n                                        v-for=\"batch in batchOptions\"\r\n                                        :key=\"batch.id\"\r\n                                        :label=\"batch.code\"\r\n                                        :value=\"batch.id\"\r\n                                    >\r\n                                        <span style=\"float: left\">{{ batch.code }}</span>\r\n                                    </el-option>\r\n                                </el-select>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"12\">\r\n                            <el-form-item label=\"产品名称\" required>\r\n                                <el-select\r\n                                    v-model=\"selectedProduct\"\r\n                                    placeholder=\"请选择产品\"\r\n                                    filterable\r\n                                    :disabled=\"!selectedBatch || productOptions.length === 0\"\r\n                                    @change=\"onProductChange\"\r\n                                    style=\"width: 100%;\"\r\n                                >\r\n                                    <el-option\r\n                                        v-for=\"product in productOptions\"\r\n                                        :key=\"product.id\"\r\n                                        :label=\"product.name + ' - ' + product.code\"\r\n                                        :value=\"product.id\"\r\n                                    >\r\n                                        <span style=\"float: left\">{{ product.name }}</span>\r\n                                        <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ product.code }}</span>\r\n                                    </el-option>\r\n                                </el-select>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-form>\r\n            </el-card>\r\n\r\n            <!-- 以下内容只有在选择了批次和产品后才显示 -->\r\n            <div v-if=\"selectedBatch && selectedProduct && !loading\">\r\n                <!-- 基本信息 -->\r\n                <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n                    <div slot=\"header\" class=\"clearfix\">\r\n                        <span style=\"font-weight: bold; font-size: 18px;\">产品信息</span>\r\n                    </div>\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <div class=\"info-item\">\r\n                                <span class=\"label\">生产批次号:</span>\r\n                                <span class=\"value\">{{ data.notice_code }}</span>\r\n                            </div>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <div class=\"info-item\">\r\n                                <span class=\"label\">产品名称:</span>\r\n                                <span class=\"value\">{{ data.product_name }}</span>\r\n                            </div>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <div class=\"info-item\">\r\n                                <span class=\"label\">规格型号:</span>\r\n                                <span class=\"value\">{{ data.product_code }}</span>\r\n                            </div>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <div class=\"info-item\">\r\n                                <span class=\"label\">质检工艺:</span>\r\n                                <span class=\"value\">{{ data.bom_name }}</span>\r\n                            </div>\r\n                        </el-col>\r\n                    </el-row>\r\n                    <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n                        <el-col :span=\"12\">\r\n                            <div class=\"info-item\">\r\n                                <span class=\"label\">质检项目:</span>\r\n                                <span class=\"value\">{{ data.quality_template_name }}</span>\r\n                            </div>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-card>\r\n\r\n                <!-- 质检工艺选择 -->\r\n                <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n                    <div slot=\"header\">\r\n                        <span style=\"font-weight: bold;\">质检设置</span>\r\n                    </div>\r\n                    <el-form label-width=\"100px\">\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"质检工艺\" required>\r\n                                    <el-select \r\n                                        v-model=\"selectedQualityId\" \r\n                                        placeholder=\"选择质检工艺\"\r\n                                        style=\"width: 100%\"\r\n                                        @change=\"onQualityChange\"\r\n                                    >\r\n                                        <el-option\r\n                                            v-for=\"item in quality_list\"\r\n                                            :key=\"item.id\"\r\n                                            :label=\"item.quality_template_name\"\r\n                                            :value=\"item.id\"\r\n                                        >\r\n                                            <span>{{ item.bom_name }} - {{ item.quality_template_name }}</span>\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"生产工人\" required>\r\n                                    <el-select \r\n                                        v-model=\"production_workers\" \r\n                                        multiple\r\n                                        placeholder=\"选择生产工人\"\r\n                                        style=\"width: 100%\"\r\n                                    >\r\n                                        <el-option\r\n                                            v-for=\"worker in worker_list\"\r\n                                            :key=\"worker.id\"\r\n                                            :label=\"worker.name\"\r\n                                            :value=\"worker.id\"\r\n                                        />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"测量工具\" required>\r\n                                    <el-select \r\n                                        v-model=\"check_tools\" \r\n                                        multiple\r\n                                        placeholder=\"选择测量工具\"\r\n                                        style=\"width: 100%\"\r\n                                    >\r\n                                        <el-option\r\n                                            v-for=\"tool in tool_list\"\r\n                                            :key=\"tool.id\"\r\n                                            :label=\"tool.name\"\r\n                                            :value=\"tool.id\"\r\n                                        />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row v-if=\"drawing_data.length > 0\">\r\n                            <el-col :span=\"24\">\r\n                                <el-form-item label=\"图纸查看\">\r\n                                    <el-button type=\"text\" @click=\"previewImg\" icon=\"el-icon-view\">\r\n                                        查看图纸 ({{ drawing_data.length }}张)\r\n                                    </el-button>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                    </el-form>\r\n                </el-card>\r\n\r\n                <!-- 质检项目 -->\r\n                <el-card shadow=\"never\" style=\"margin-bottom: 20px;\" v-if=\"check_data.length > 0\">\r\n                    <div slot=\"header\">\r\n                        <span style=\"font-weight: bold;\">质检项目</span>\r\n                    </div>\r\n                    <el-form label-width=\"120px\">\r\n                        <quality-field \r\n                            v-for=\"(item, idx) in check_data\" \r\n                            :key=\"idx\"\r\n                            :data=\"item\" \r\n                            @change=\"checkResult\"\r\n                        />\r\n                    </el-form>\r\n                </el-card>\r\n\r\n                <!-- 质检结果 -->\r\n                <el-card shadow=\"never\" style=\"margin-bottom: 20px;\" v-if=\"data.bom_id != ''\">\r\n                    <div slot=\"header\">\r\n                        <span style=\"font-weight: bold;\">质检结果</span>\r\n                    </div>\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"8\">\r\n                            <div class=\"info-item\">\r\n                                <span class=\"label\">质检结果:</span>\r\n                                <el-tag v-if=\"error_result == 0\" type=\"success\" size=\"medium\">OK</el-tag>\r\n                                <el-tag v-else type=\"danger\" size=\"medium\">NG</el-tag>\r\n                            </div>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-card>\r\n\r\n                <!-- 不合格信息 -->\r\n                <el-card shadow=\"never\" style=\"margin-bottom: 20px;\" v-if=\"error_result == 1 && data.bom_id != ''\">\r\n                    <div slot=\"header\">\r\n                        <span style=\"font-weight: bold; color: #F56C6C;\">不合格信息</span>\r\n                    </div>\r\n                    <el-form label-width=\"120px\">\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"8\">\r\n                                <el-form-item label=\"不合格数量\" required>\r\n                                    <el-input-number\r\n                                        v-model=\"error_cnt\"\r\n                                        :min=\"0\"\r\n                                        placeholder=\"请输入不合格数量\"\r\n                                        style=\"width: 100%\"\r\n                                    />\r\n                                    <span style=\"margin-left: 10px; color: #909399;\">件</span>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"8\">\r\n                                <el-form-item label=\"不合格类型\" required>\r\n                                                                          <el-select \r\n                                          v-model=\"error_type\"\r\n                                          placeholder=\"选择不合格类型\"\r\n                                          style=\"width: 100%\"\r\n                                      >\r\n                                          <el-option\r\n                                              v-for=\"type in error_types\"\r\n                                              :key=\"type.name\"\r\n                                              :label=\"type.name\"\r\n                                              :value=\"type.name\"\r\n                                          />\r\n                                      </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row>\r\n                            <el-col :span=\"16\">\r\n                                <el-form-item label=\"不合格原因\">\r\n                                    <el-input\r\n                                        v-model=\"error_remarks\"\r\n                                        type=\"textarea\"\r\n                                        :rows=\"3\"\r\n                                        placeholder=\"请输入不合格原因\"\r\n                                        style=\"width: 100%\"\r\n                                    />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                    </el-form>\r\n                </el-card>\r\n\r\n\r\n                <!-- 操作按钮 -->\r\n                <div class=\"footer-actions\">\r\n                    <el-button @click=\"resetForm\" size=\"large\">重新选择</el-button>\r\n                    <el-button \r\n                        type=\"primary\" \r\n                        size=\"large\"\r\n                        @click=\"onSubmit\"\r\n                        :loading=\"submitting\"\r\n                    >\r\n                        提交检验结果\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 未选择时的提示 -->\r\n            <div v-if=\"!selectedBatch || !selectedProduct\" style=\"text-align: center; padding: 80px 20px;\">\r\n                <i class=\"el-icon-s-check\" style=\"font-size: 100px; color: #C0C4CC;\"></i>\r\n                <div style=\"font-size: 18px; color: #909399; margin-top: 20px;\">\r\n                    请先选择生产批次和产品\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 图片预览对话框 -->\r\n        <el-dialog title=\"图纸预览\" :visible.sync=\"imagePreviewVisible\" width=\"80%\">\r\n            <div style=\"text-align: center;\">\r\n                <img \r\n                    v-for=\"(img, index) in previewImages\" \r\n                    :key=\"index\"\r\n                    :src=\"img\"\r\n                    style=\"max-width: 100%; margin-bottom: 20px; border: 1px solid #ddd;\"\r\n                />\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport qs from 'qs'; \r\nimport QualityField from '../../components/QualityField.vue';\r\n\r\nexport default {\r\n    name: \"ProduceDetail\",\r\n    components: {\r\n        QualityField\r\n    },\r\n    data() {\r\n        return {\r\n            loading: false,\r\n            submitting: false,\r\n            uploadHover: false,\r\n            data: {},\r\n            check_data: [],\r\n            drawing_data: [],\r\n            quality_list: [],\r\n            error_result: 0,\r\n            error_cnt: 0,\r\n            error_types: [],\r\n            error_type: '',\r\n            error_remarks: '',\r\n            selectedQualityId: '',\r\n            worker_list: [],\r\n            production_workers: [],\r\n            tool_list: [],\r\n            check_tools: [],\r\n            files: [],\r\n            imagePreviewVisible: false,\r\n            previewImages: [],\r\n            // 新增的选择相关数据\r\n            batchOptions: [],\r\n            batchLoading: false,\r\n            selectedBatch: '',\r\n            productOptions: [],\r\n            selectedProduct: ''\r\n        }\r\n    },\r\n    mounted() {\r\n        // this.init();  // 恢复原来的 init 方法\r\n        // 初始加载所有批次数据用于前端筛选\r\n        this.loadAllBatches();\r\n        \r\n        // 如果路由传递了参数，直接设置选中的批次和产品\r\n        // const { batch_id, product_id } = this.$route.params;\r\n        // if (batch_id && product_id) {\r\n        //     this.selectedBatch = batch_id;\r\n        //     this.selectedProduct = product_id;\r\n        //     this.onBatchChange(batch_id);\r\n        //     this.onProductChange(product_id);\r\n        // }\r\n    },\r\n    methods: {\r\n        init() {\r\n            // 恢复原来的 init 逻辑，加载基础数据\r\n            this.$http.post('/work/quality/init', qs.stringify({}), {\r\n                headers: {\r\n                    'Content-Type': 'application/x-www-form-urlencoded'\r\n                }\r\n            }).then((rs) => {\r\n                if (rs.status === 'ok') {\r\n                    this.error_types = rs.data.error_types || [];\r\n                    this.worker_list = rs.data.worker_list || [];\r\n                    this.tool_list = rs.data.tool_list || [];\r\n                    this.quality_list = rs.data.quality_list || [];\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('获取基础数据失败');\r\n            });\r\n        },\r\n        \r\n        initWithCode(code) {\r\n            // 以前的 init 方法的完整逻辑，传递 code 参数\r\n            this.$http.post('/work/quality/init', qs.stringify({\r\n                code: code\r\n            }), {\r\n                headers: {\r\n                    'Content-Type': 'application/x-www-form-urlencoded'\r\n                }\r\n            }).then((rs) => {\r\n                if (rs.status === 'ok') {\r\n                    // 更新所有数据\r\n                    if (rs.data.data) {\r\n                        // 合并基本信息，保留已设置的值\r\n                        this.data = { ...this.data, ...rs.data.data };\r\n                    }\r\n                    if (rs.data.error_types) {\r\n                        this.error_types = rs.data.error_types;\r\n                    }\r\n                    if (rs.data.worker_list) {\r\n                        this.worker_list = rs.data.worker_list;\r\n                    }\r\n                    if (rs.data.tool_list) {\r\n                        this.tool_list = rs.data.tool_list;\r\n                    }\r\n                    if (rs.data.quality_list) {\r\n                        this.quality_list = rs.data.quality_list;\r\n                    }\r\n                    if (rs.data.check_data) {\r\n                        this.check_data = rs.data.check_data;\r\n                    }\r\n                    if (rs.data.drawing_data) {\r\n                        this.drawing_data = rs.data.drawing_data;\r\n                    }\r\n                    \r\n                    // 如果有默认的质检工艺，自动选中\r\n                    if (this.quality_list.length > 0 && !this.selectedQualityId) {\r\n                        this.selectedQualityId = this.quality_list[0].id;\r\n                        this.onQualityChange(this.selectedQualityId);\r\n                    }\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('获取质检数据失败');\r\n            });\r\n        },\r\n        onQualityChange(qualityId) {\r\n            const selectedQuality = this.quality_list.find(item => item.id === qualityId);\r\n            if (!selectedQuality) return;\r\n            \r\n            if (this.data.bom_id == selectedQuality.product_bom_id && \r\n                this.data.quality_template_id == selectedQuality.id) {\r\n                return;\r\n            }\r\n            \r\n            this.data.bom_id = selectedQuality.product_bom_id;\r\n            this.data.bom_name = selectedQuality.bom_name;\r\n            this.data.quality_template_id = selectedQuality.id;\r\n            this.data.quality_template_name = selectedQuality.quality_template_name;\r\n            this.check_data = JSON.parse(JSON.stringify(selectedQuality.form_data));\r\n            this.drawing_data = selectedQuality.drawing_data || [];\r\n            this.error_result = 0;\r\n        },\r\n        previewImg() {\r\n            this.previewImages = [];\r\n            for (let i = 0; i < this.drawing_data.length; i++) {\r\n                this.previewImages.push(this.drawing_data[i].url);\r\n            }\r\n            this.imagePreviewVisible = true;\r\n        },\r\n        checkResult(callback) {\r\n            this.error_result = 0;\r\n            for (let check_item of this.check_data) {\r\n                check_item.result = 0;\r\n                if (check_item.type == 6) {\r\n                    for (let i = 0; i < check_item.values.length; i++) {\r\n                        check_item.results[i] = 0;\r\n                        if (check_item.values[i] == 1) {\r\n                            if (check_item.result == 0) {\r\n                                check_item.result = 1;\r\n                            }\r\n                            check_item.results[i] = 1;\r\n                        }\r\n                    }\r\n                } else if (check_item.type == 7) {\r\n                    check_item.result = 0;\r\n                    for (let i = 0; i < check_item.values.length; i++) {\r\n                        check_item.results[i] = 0;\r\n                        if (check_item.values[i] != '') {\r\n                            // 验证输入是否为有效数字\r\n                            const inputValue = parseFloat(check_item.values[i]);\r\n                            if (isNaN(inputValue)) {\r\n                                // 输入的不是有效数字，标记为错误\r\n                                check_item.results[i] = 1;\r\n                                if (check_item.result == 0) {\r\n                                    check_item.result = 1;\r\n                                }\r\n                                continue;\r\n                            }\r\n                            \r\n                            let val = '';\r\n                            for (let item of check_item.formula_list) {\r\n                                if (item.t == 3) {\r\n                                    val += check_item.values[i];\r\n                                } else {\r\n                                    val += item.v;\r\n                                }\r\n                            }\r\n                            try {\r\n                                let res = eval(val);\r\n                                res = Number(res.toFixed(4));\r\n                                if (isNaN(res) || !(res >= parseFloat(check_item.standard_minus) && res <= parseFloat(check_item.standard_plus))) {\r\n                                    check_item.results[i] = 1;\r\n                                }\r\n                            } catch (e) {\r\n                                check_item.results[i] = 1;\r\n                            }\r\n                            if (check_item.result == 0) {\r\n                                check_item.result = check_item.results[i];\r\n                            }\r\n                        }\r\n                    }\r\n                } else if (check_item.type == 8) {\r\n                    check_item.result = 0;\r\n                    let min = parseFloat(check_item.standard_val) - parseFloat(check_item.standard_minus);\r\n                    let max = parseFloat(check_item.standard_val) + parseFloat(check_item.standard_plus);\r\n                    for (let i = 0; i < check_item.values.length; i++) {\r\n                        check_item.results[i] = 0;\r\n                        if (check_item.values[i] != '') {\r\n                            let res = parseFloat(check_item.values[i]);\r\n                            // 验证输入是否为有效数字\r\n                            if (isNaN(res) || !(res >= min && res <= max)) {\r\n                                check_item.results[i] = 1;\r\n                            }\r\n                            if (check_item.result == 0) {\r\n                                check_item.result = check_item.results[i];\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (this.error_result == 0) {\r\n                    this.error_result = check_item.result;\r\n                }\r\n            }\r\n            \r\n            // 如果检验结果为OK，清空不合格信息\r\n            if (this.error_result == 0) {\r\n                this.error_cnt = 0;\r\n                this.error_type = '';\r\n                this.error_remarks = '';\r\n            }\r\n            \r\n            if (callback) callback();\r\n        },\r\n        onSubmit() {\r\n            // 表单验证\r\n            for (let item of this.check_data) {\r\n                if (item.required == 1) {\r\n                    if (item.type == 1 || item.type == 3 || item.type == 5) {\r\n                        if (item.value == '') {\r\n                            this.$message.error('请选择' + item.title);\r\n                            return;\r\n                        }\r\n                        if (item.values.length == 0) {\r\n                            this.$message.error('请选择' + item.title);\r\n                            return;\r\n                        }\r\n                    } else if (item.type == 4) {\r\n                        if (item.values.length == 0) {\r\n                            this.$message.error('请选择' + item.title);\r\n                            return;\r\n                        }\r\n                    } else if (item.type == 2 || item.type == 7 || item.type == 8) {\r\n                        for (let value of item.values) {\r\n                            if (value == '') {\r\n                                this.$message.error('请输入' + item.title);\r\n                                return;\r\n                            }\r\n                            // 验证输入是否为有效数字\r\n                            const numValue = parseFloat(value);\r\n                            if (isNaN(numValue)) {\r\n                                this.$message.error(item.title + ' 必须输入有效的数字');\r\n                                return;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            \r\n            // 验证生产工人和测量工具必填\r\n            if (!this.production_workers || this.production_workers.length === 0) {\r\n                this.$message.error('请选择生产工人');\r\n                return;\r\n            }\r\n            \r\n            if (!this.check_tools || this.check_tools.length === 0) {\r\n                this.$message.error('请选择测量工具');\r\n                return;\r\n            }\r\n            \r\n            if (this.error_result == 1) {\r\n                if (this.error_cnt === '' || this.error_cnt === 0) {\r\n                    this.$message.error('请输入不合格数量');\r\n                    return;\r\n                }\r\n                if (this.error_type == '') {\r\n                    this.$message.error('请选择不合格类型');\r\n                    return;\r\n                }\r\n            }\r\n\r\n            // 准备数据，数组需要特殊处理\r\n            let postData = qs.stringify({\r\n                notice_detail_id: this.data.id,\r\n                bom_id: this.data.bom_id,\r\n                quality_template_id: this.data.quality_template_id,\r\n                error_result: this.error_result,\r\n                error_cnt: this.error_cnt,\r\n                error_type: this.error_type,\r\n                error_remarks: this.error_remarks,\r\n                check_data: encodeURI(JSON.stringify(this.check_data)),\r\n                production_workers: this.production_workers, // qs会自动处理数组\r\n                check_tools: this.check_tools, // qs会自动处理数组\r\n                files: [] // 暂时传空数组，如果有文件上传需要单独处理\r\n            });\r\n            this.submitting = true;\r\n            \r\n            this.$http.post('work/quality/save', postData, {\r\n                headers: {\r\n                    'Content-Type': 'application/x-www-form-urlencoded'\r\n                }\r\n            }).then((rs) => {\r\n                this.submitting = false;\r\n                if (rs.status == 'ok') {\r\n                    this.$message.success('提交成功');\r\n                    // 关闭 layer 弹窗\r\n                    if (window.top && window.top.layer) {\r\n                        window.top.layer.closeAll();\r\n                    } else {\r\n                        // 如果不是在 layer 中打开，则使用路由返回\r\n                        this.$router.back();\r\n                    }\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.submitting = false;\r\n                this.$message.error('网络异常');\r\n            });\r\n            \r\n            // this.submitting = true;\r\n            // this.upload(this.files, [], 0, (upload_rs) => {\r\n            //     if (upload_rs.status == 'ok') {\r\n            //         let data = {\r\n            //             notice_detail_id: this.data.id,\r\n            //             bom_id: this.data.bom_id,\r\n            //             quality_template_id: this.data.quality_template_id,\r\n            //             error_result: this.error_result,\r\n            //             error_cnt: this.error_cnt,\r\n            //             error_type: this.error_type, // 如果是数组直接传数组\r\n            //             error_remarks: this.error_remarks,\r\n            //             check_data: encodeURI(JSON.stringify(this.check_data)), // 直接传对象/数组，不用 encodeURI/JSON.stringify\r\n            //             production_workers: this.production_workers, // 直接传数组\r\n            //             check_tools: this.check_tools, // 直接传数组\r\n            //             files: upload_rs.list // 直接传图片路径数组\r\n            //         };\r\n                    \r\n            //         this.$http.post('work/quality/save', data).then((rs) => {\r\n            //             this.submitting = false;\r\n            //             if (rs.status == 'ok') {\r\n            //                 this.$message.success('提交成功');\r\n            //                 this.$router.back();\r\n            //             } else {\r\n            //                 this.$message.error(rs.message);\r\n            //             }\r\n            //         }).catch(() => {\r\n            //             this.submitting = false;\r\n            //             this.$message.error('网络异常');\r\n            //         });\r\n            //     } else {\r\n            //         this.submitting = false;\r\n            //         this.$message.error('文件上传失败！');\r\n            //     }\r\n            // });\r\n        },\r\n        \r\n        takePhoto() {\r\n            this.$refs.fileInput.click();\r\n        },\r\n        \r\n        async handleFileChange(e) {\r\n            const selectedFiles = Array.from(e.target.files);\r\n            if (!selectedFiles) return;\r\n            \r\n            for (const file of selectedFiles) {\r\n                if (!file.type.startsWith('image/')) {\r\n                    this.$message.error('仅支持图片格式');\r\n                    continue;\r\n                }\r\n                if (file.size > 10 * 1024 * 1024) {\r\n                    this.$message.error('文件大小不能超过10MB');\r\n                    continue;\r\n                }\r\n                const preview = await this.readFileAsDataURL(file);\r\n                this.files.push(preview);\r\n            }\r\n        },\r\n        \r\n        readFileAsDataURL(file) {\r\n            return new Promise((resolve, reject) => {\r\n                const reader = new FileReader();\r\n                reader.onload = () => resolve(reader.result);\r\n                reader.onerror = reject;\r\n                reader.readAsDataURL(file);\r\n            });\r\n        },\r\n        \r\n        delPhoto(i) {\r\n            this.files.splice(i, 1);\r\n        },\r\n        \r\n        upload(files, new_files, i, cb) {\r\n            if (files.length == i) {\r\n                cb({\r\n                    status: 'ok',\r\n                    list: new_files\r\n                });\r\n                return;\r\n            }\r\n            \r\n            // 添加延迟确保文件名唯一性\r\n            setTimeout(() => {\r\n                this.fileUpload(files[i], (data) => {\r\n                    if (data.status == 'ok') {\r\n                        new_files.push(data.path);\r\n                        i++;\r\n                        this.upload(files, new_files, i, cb);\r\n                    } else {\r\n                        cb(data);\r\n                    }\r\n                });\r\n            }, i * 100); // 每个文件间隔100ms上传\r\n        },\r\n        \r\n        fileUpload(base64Data, cb) {\r\n            // 使用FormData格式发送上传数据\r\n            const formData = new FormData();\r\n            formData.append('img_base64', base64Data);\r\n            formData.append('folder_name', 'produce'); // 使用produce作为文件夹名\r\n            \r\n            // 添加时间戳和随机数确保文件名唯一性\r\n            const timestamp = Date.now();\r\n            const random = Math.floor(Math.random() * 10000);\r\n            formData.append('file_suffix', `_${timestamp}_${random}`);\r\n            \r\n            this.$http.post('work/common/upload', formData).then((rs) => {\r\n                if (rs.status == 'ok') {\r\n                    cb({\r\n                        status: 'ok',\r\n                        path: rs.data\r\n                    });\r\n                } else {\r\n                    cb({\r\n                        status: 'error',\r\n                        message: rs.message\r\n                    });\r\n                }\r\n            }).catch((error) => {\r\n                cb({\r\n                    status: 'error',\r\n                    message: '上传失败'\r\n                });\r\n            });\r\n        },\r\n\r\n        loadAllBatches() {\r\n            this.batchLoading = true;\r\n            this.$http.post('work/quality/batch', qs.stringify({ \r\n                keyword: '',\r\n                limit: 1000  // 加载更多数据用于前端筛选\r\n            }), {\r\n                headers: {\r\n                    'Content-Type': 'application/x-www-form-urlencoded'\r\n                }\r\n            }).then((rs) => {\r\n                this.batchLoading = false;\r\n                if (rs.status === 'ok') {\r\n                    this.batchOptions = rs.data;\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                    this.batchOptions = [];\r\n                }\r\n            }).catch(() => {\r\n                this.batchLoading = false;\r\n                this.$message.error('获取批次列表失败');\r\n                this.batchOptions = [];\r\n            });\r\n        },\r\n\r\n        onBatchChange(batchId) {\r\n            this.selectedProduct = '';\r\n            this.productOptions = [];\r\n            \r\n            // 批次改变时，清空所有质检相关数据\r\n            this.data = {};\r\n            this.check_data = [];\r\n            this.drawing_data = [];\r\n            this.quality_list = [];\r\n            this.error_result = 0;\r\n            this.error_cnt = 0;\r\n            this.error_type = '';\r\n            this.error_remarks = '';\r\n            this.production_workers = [];\r\n            this.check_tools = [];\r\n            this.selectedQualityId = '';\r\n            this.files = []; // 清空照片\r\n            \r\n            if (!batchId) return;\r\n            this.$http.post('work/quality/products', qs.stringify({\r\n                batch_id: batchId\r\n            }), {\r\n                headers: {\r\n                    'Content-Type': 'application/x-www-form-urlencoded'\r\n                }\r\n            }).then((rs) => {\r\n                if (rs.status === 'ok') {\r\n                    this.productOptions = rs.data;\r\n                    if (rs.data.length === 0) {\r\n                        this.$message.warning('该批次下没有可检验的产品');\r\n                    }\r\n                } else {\r\n                    this.$message.error(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('获取产品列表失败');\r\n            });\r\n        },\r\n\r\n        onProductChange(productId) {\r\n            if (productId) {\r\n                const product = this.productOptions.find(p => p.id === productId);\r\n                const batch = this.batchOptions.find(b => b.id === this.selectedBatch);\r\n                \r\n                if (product && batch) {\r\n                                    // 先清空旧的数据，避免数据混淆\r\n                this.check_data = [];\r\n                this.drawing_data = [];\r\n                this.quality_list = [];\r\n                this.error_result = 0;\r\n                this.error_cnt = 0;\r\n                this.error_type = '';\r\n                this.error_remarks = '';\r\n                    this.production_workers = [];\r\n                    this.check_tools = [];\r\n                    this.selectedQualityId = '';\r\n                    this.files = []; // 清空照片\r\n                    \r\n                    // 设置基本信息\r\n                    this.data = {\r\n                        id: productId,\r\n                        notice_code: batch.code,\r\n                        product_name: product.name,\r\n                        product_code: product.code,\r\n                        bom_id: product.bom_id || '',\r\n                        bom_name: product.bom_name || '',\r\n                        quality_template_id: product.quality_template_id || '',\r\n                        quality_template_name: product.quality_template_name || ''\r\n                    };\r\n                    \r\n                    // 使用原来的 init 逻辑，传递 notice_code\r\n                    this.initWithCode(product.uid);\r\n                }\r\n            } else {\r\n                this.resetProductData();\r\n            }\r\n        },\r\n         \r\n        resetProductData() {\r\n            this.data = {};\r\n            this.check_data = [];\r\n            this.drawing_data = [];\r\n            this.quality_list = [];\r\n            this.error_result = 0;\r\n            this.error_cnt = 0;\r\n            this.error_type = '';\r\n            this.error_remarks = '';\r\n            this.production_workers = [];\r\n            this.check_tools = [];\r\n            this.selectedQualityId = '';\r\n            this.files = []; // 清空照片\r\n        },\r\n\r\n        resetForm() {\r\n            this.selectedBatch = '';\r\n            this.selectedProduct = '';\r\n            this.productOptions = [];\r\n            this.data = {};\r\n            this.check_data = [];\r\n            this.drawing_data = [];\r\n            this.quality_list = [];\r\n            this.error_result = 0;\r\n            this.error_cnt = 0;\r\n            this.error_type = '';\r\n            this.error_remarks = '';\r\n            this.production_workers = [];\r\n            this.check_tools = [];\r\n            this.selectedQualityId = '';\r\n            this.files = [];\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n    padding: 20px;\r\n    background-color: #f5f7fa;\r\n    min-height: 100vh;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n    display: table;\r\n    content: \"\";\r\n}\r\n.clearfix:after {\r\n    clear: both;\r\n}\r\n\r\n.info-item {\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.label {\r\n    font-weight: bold;\r\n    color: #606266;\r\n    margin-right: 10px;\r\n}\r\n\r\n.value {\r\n    color: #303133;\r\n}\r\n\r\n.footer-actions {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.footer-actions .el-button {\r\n    margin: 0 10px;\r\n    padding: 12px 30px;\r\n}\r\n</style> ", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"container\"},[_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"element-loading-text\":\"加载中...\"}},[_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\",\"font-size\":\"18px\"}},[_vm._v(\"产品检验\")])]),_c('el-form',{attrs:{\"label-width\":\"100px\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"生产批次\",\"required\":\"\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择生产批次\",\"filterable\":\"\"},on:{\"change\":_vm.onBatchChange},model:{value:(_vm.selectedBatch),callback:function ($$v) {_vm.selectedBatch=$$v},expression:\"selectedBatch\"}},_vm._l((_vm.batchOptions),function(batch){return _c('el-option',{key:batch.id,attrs:{\"label\":batch.code,\"value\":batch.id}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(batch.code))])])}),1)],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"产品名称\",\"required\":\"\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择产品\",\"filterable\":\"\",\"disabled\":!_vm.selectedBatch || _vm.productOptions.length === 0},on:{\"change\":_vm.onProductChange},model:{value:(_vm.selectedProduct),callback:function ($$v) {_vm.selectedProduct=$$v},expression:\"selectedProduct\"}},_vm._l((_vm.productOptions),function(product){return _c('el-option',{key:product.id,attrs:{\"label\":product.name + ' - ' + product.code,\"value\":product.id}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(product.name))]),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(_vm._s(product.code))])])}),1)],1)],1)],1)],1)],1),(_vm.selectedBatch && _vm.selectedProduct && !_vm.loading)?_c('div',[_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\",\"font-size\":\"18px\"}},[_vm._v(\"产品信息\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"生产批次号:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.notice_code))])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"产品名称:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.product_name))])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"规格型号:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.product_code))])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"质检工艺:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.bom_name))])])])],1),_c('el-row',{staticStyle:{\"margin-top\":\"15px\"},attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"质检项目:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.quality_template_name))])])])],1)],1),_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\"}},[_vm._v(\"质检设置\")])]),_c('el-form',{attrs:{\"label-width\":\"100px\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"质检工艺\",\"required\":\"\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"选择质检工艺\"},on:{\"change\":_vm.onQualityChange},model:{value:(_vm.selectedQualityId),callback:function ($$v) {_vm.selectedQualityId=$$v},expression:\"selectedQualityId\"}},_vm._l((_vm.quality_list),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.quality_template_name,\"value\":item.id}},[_c('span',[_vm._v(_vm._s(item.bom_name)+\" - \"+_vm._s(item.quality_template_name))])])}),1)],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"生产工人\",\"required\":\"\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"multiple\":\"\",\"placeholder\":\"选择生产工人\"},model:{value:(_vm.production_workers),callback:function ($$v) {_vm.production_workers=$$v},expression:\"production_workers\"}},_vm._l((_vm.worker_list),function(worker){return _c('el-option',{key:worker.id,attrs:{\"label\":worker.name,\"value\":worker.id}})}),1)],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"测量工具\",\"required\":\"\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"multiple\":\"\",\"placeholder\":\"选择测量工具\"},model:{value:(_vm.check_tools),callback:function ($$v) {_vm.check_tools=$$v},expression:\"check_tools\"}},_vm._l((_vm.tool_list),function(tool){return _c('el-option',{key:tool.id,attrs:{\"label\":tool.name,\"value\":tool.id}})}),1)],1)],1)],1),(_vm.drawing_data.length > 0)?_c('el-row',[_c('el-col',{attrs:{\"span\":24}},[_c('el-form-item',{attrs:{\"label\":\"图纸查看\"}},[_c('el-button',{attrs:{\"type\":\"text\",\"icon\":\"el-icon-view\"},on:{\"click\":_vm.previewImg}},[_vm._v(\" 查看图纸 (\"+_vm._s(_vm.drawing_data.length)+\"张) \")])],1)],1)],1):_vm._e()],1)],1),(_vm.check_data.length > 0)?_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\"}},[_vm._v(\"质检项目\")])]),_c('el-form',{attrs:{\"label-width\":\"120px\"}},_vm._l((_vm.check_data),function(item,idx){return _c('quality-field',{key:idx,attrs:{\"data\":item},on:{\"change\":_vm.checkResult}})}),1)],1):_vm._e(),(_vm.data.bom_id != '')?_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\"}},[_vm._v(\"质检结果\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"质检结果:\")]),(_vm.error_result == 0)?_c('el-tag',{attrs:{\"type\":\"success\",\"size\":\"medium\"}},[_vm._v(\"OK\")]):_c('el-tag',{attrs:{\"type\":\"danger\",\"size\":\"medium\"}},[_vm._v(\"NG\")])],1)])],1)],1):_vm._e(),(_vm.error_result == 1 && _vm.data.bom_id != '')?_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\",\"color\":\"#F56C6C\"}},[_vm._v(\"不合格信息\")])]),_c('el-form',{attrs:{\"label-width\":\"120px\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('el-form-item',{attrs:{\"label\":\"不合格数量\",\"required\":\"\"}},[_c('el-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":0,\"placeholder\":\"请输入不合格数量\"},model:{value:(_vm.error_cnt),callback:function ($$v) {_vm.error_cnt=$$v},expression:\"error_cnt\"}}),_c('span',{staticStyle:{\"margin-left\":\"10px\",\"color\":\"#909399\"}},[_vm._v(\"件\")])],1)],1),_c('el-col',{attrs:{\"span\":8}},[_c('el-form-item',{attrs:{\"label\":\"不合格类型\",\"required\":\"\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"选择不合格类型\"},model:{value:(_vm.error_type),callback:function ($$v) {_vm.error_type=$$v},expression:\"error_type\"}},_vm._l((_vm.error_types),function(type){return _c('el-option',{key:type.name,attrs:{\"label\":type.name,\"value\":type.name}})}),1)],1)],1)],1),_c('el-row',[_c('el-col',{attrs:{\"span\":16}},[_c('el-form-item',{attrs:{\"label\":\"不合格原因\"}},[_c('el-input',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入不合格原因\"},model:{value:(_vm.error_remarks),callback:function ($$v) {_vm.error_remarks=$$v},expression:\"error_remarks\"}})],1)],1)],1)],1)],1):_vm._e(),_c('div',{staticClass:\"footer-actions\"},[_c('el-button',{attrs:{\"size\":\"large\"},on:{\"click\":_vm.resetForm}},[_vm._v(\"重新选择\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"loading\":_vm.submitting},on:{\"click\":_vm.onSubmit}},[_vm._v(\" 提交检验结果 \")])],1)],1):_vm._e(),(!_vm.selectedBatch || !_vm.selectedProduct)?_c('div',{staticStyle:{\"text-align\":\"center\",\"padding\":\"80px 20px\"}},[_c('i',{staticClass:\"el-icon-s-check\",staticStyle:{\"font-size\":\"100px\",\"color\":\"#C0C4CC\"}}),_c('div',{staticStyle:{\"font-size\":\"18px\",\"color\":\"#909399\",\"margin-top\":\"20px\"}},[_vm._v(\" 请先选择生产批次和产品 \")])]):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"图纸预览\",\"visible\":_vm.imagePreviewVisible,\"width\":\"80%\"},on:{\"update:visible\":function($event){_vm.imagePreviewVisible=$event}}},[_c('div',{staticStyle:{\"text-align\":\"center\"}},_vm._l((_vm.previewImages),function(img,index){return _c('img',{key:index,staticStyle:{\"max-width\":\"100%\",\"margin-bottom\":\"20px\",\"border\":\"1px solid #ddd\"},attrs:{\"src\":img}})}),0)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "'use strict';\nvar $ = require('../internals/export');\nvar $find = require('../internals/array-iteration').find;\nvar addToUnscopables = require('../internals/add-to-unscopables');\n\nvar FIND = 'find';\nvar SKIPS_HOLES = true;\n\n// Shouldn't skip holes\n// eslint-disable-next-line es/no-array-prototype-find -- testing\nif (FIND in []) Array(1)[FIND](function () { SKIPS_HOLES = false; });\n\n// `Array.prototype.find` method\n// https://tc39.es/ecma262/#sec-array.prototype.find\n$({ target: 'Array', proto: true, forced: SKIPS_HOLES }, {\n  find: function find(callbackfn /* , that = undefined */) {\n    return $find(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables(FIND);\n", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar iterate = require('../internals/iterate');\nvar aCallable = require('../internals/a-callable');\nvar anObject = require('../internals/an-object');\nvar getIteratorDirect = require('../internals/get-iterator-direct');\nvar iteratorClose = require('../internals/iterator-close');\nvar iteratorHelperWithoutClosingOnEarlyError = require('../internals/iterator-helper-without-closing-on-early-error');\n\nvar findWithoutClosingOnEarlyError = iteratorHelperWithoutClosingOnEarlyError('find', TypeError);\n\n// `Iterator.prototype.find` method\n// https://tc39.es/ecma262/#sec-iterator.prototype.find\n$({ target: 'Iterator', proto: true, real: true, forced: findWithoutClosingOnEarlyError }, {\n  find: function find(predicate) {\n    anObject(this);\n    try {\n      aCallable(predicate);\n    } catch (error) {\n      iteratorClose(this, 'throw', error);\n    }\n\n    if (findWithoutClosingOnEarlyError) return call(findWithoutClosingOnEarlyError, this, predicate);\n\n    var record = getIteratorDirect(this);\n    var counter = 0;\n    return iterate(record, function (value, stop) {\n      if (predicate(value, counter++)) return stop(value);\n    }, { IS_RECORD: true, INTERRUPTED: true }).result;\n  }\n});\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.container[data-v-240baac1] {\\r\\n    padding: 20px;\\r\\n    background-color: #f5f7fa;\\r\\n    min-height: 100vh;\\n}\\n.clearfix[data-v-240baac1]:before,\\r\\n.clearfix[data-v-240baac1]:after {\\r\\n    display: table;\\r\\n    content: \\\"\\\";\\n}\\n.clearfix[data-v-240baac1]:after {\\r\\n    clear: both;\\n}\\n.info-item[data-v-240baac1] {\\r\\n    margin-bottom: 10px;\\n}\\n.label[data-v-240baac1] {\\r\\n    font-weight: bold;\\r\\n    color: #606266;\\r\\n    margin-right: 10px;\\n}\\n.value[data-v-240baac1] {\\r\\n    color: #303133;\\n}\\n.footer-actions[data-v-240baac1] {\\r\\n    text-align: center;\\r\\n    padding: 20px 0;\\r\\n    background: white;\\r\\n    border-radius: 8px;\\r\\n    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\\r\\n            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\\n}\\n.footer-actions .el-button[data-v-240baac1] {\\r\\n    margin: 0 10px;\\r\\n    padding: 12px 30px;\\n}\\r\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"f1ed9268\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./produce_detail.vue?vue&type=template&id=240baac1&scoped=true\"\nimport script from \"./produce_detail.vue?vue&type=script&lang=js\"\nexport * from \"./produce_detail.vue?vue&type=script&lang=js\"\nimport style0 from \"./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"240baac1\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('240baac1')) {\n      api.createRecord('240baac1', component.options)\n    } else {\n      api.reload('240baac1', component.options)\n    }\n    module.hot.accept(\"./produce_detail.vue?vue&type=template&id=240baac1&scoped=true\", function () {\n      api.rerender('240baac1', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/mingjing/produce_detail.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=template&id=240baac1&scoped=true\""], "names": [], "sourceRoot": ""}