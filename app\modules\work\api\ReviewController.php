<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Modules\Work\Service\ReviewService;

class ReviewController extends SuperController
{
    public function listAction($type = '')
    {
        $as = new ReviewService();
        $builder = $as->selectAll();
        $page = $this->getPagination($builder);
        $page->rows = $as->setDetail($page->rows);
        if (empty($type)){
            $page->cnt_data = $as->getCntData();
        }
        return json_encode($page);
    }

    public function searchAction()
    {
        $as = new ReviewService();
        $builder = $as->searchAll();
        $page = $this->getPagination($builder);
        $page->rows = $as->setDetail($page->rows);
        return json_encode($page);
    }
}
