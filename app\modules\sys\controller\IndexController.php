<?php

namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Sys\Model\Package;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Sys\Service\HomeService;
use Envsan\Modules\Sys\Service\MenuService;
use Phalcon\Mvc\View;

/**
 * @skipacl
 */
class IndexController extends SuperController
{
    public function indexAction()
    {
        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);

        $user = SessionData::user();
        if ($user != null) {
            $this->view->user = $user;
        }

        $ms = new MenuService();
        $this->view->menu = $ms->buileMenu2();

        $this->view->super = SessionData::isSuper();
        $this->view->owner = SessionData::owner();
        $this->view->sid = $this->session->getId();
        $gs = new GroupService();
        $this->view->group = $gs->selectById($user->group_id);//UPDATE RC-LQ-968 20240220 BY WXX
        $this->view->home_page = $this->session->get('home_page');
        $s = new HomeService();
        $this->view->data = $s->getReviewData();
        $pwd_flag = false;
        if ($user->password == '123456'){
            $pwd_flag = true;
        }
        $this->view->pwd_flag = $pwd_flag;
    }

    public function refreshAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new HomeService();
            return json_encode($s->getReviewData());
        }
    }

    public function emptyAction()
    {

    }

    public function menuAction($id = '')
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();

            if (empty($id)) {
                return json_encode([]);
            }

            $ms = new MenuService();
            return json_encode($ms->buileMenu($id));
        }
    }


    public function packageAction($type='')
    {
        if($type=='json') {
            $this->setJsonResponse();
            $rows = Package::find();
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
    }
}