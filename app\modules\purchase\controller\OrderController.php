<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Printing\Service\TemplateService;
use Envsan\Modules\Purchase\Model\PurchaseOrder;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\OrderService;
use Envsan\Modules\Purchase\Service\PurchaseRequestService;
use Envsan\Modules\Purchase\Service\SupplierService;

/**
 * @name('采购订单')
 */
class OrderController extends SuperController
{
    private $page_id = 24;
    private $search_page_id = 29;


    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList($this->page_id));
    }


    /**
     * @name('待采购查询')
     */
    public function waitAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderService();
            $builder = $s->selectWaitAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }


    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }


    /**
     * @name('创建')
     */
    public function createAction()
    {
        $rs = new OrderService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->create());
            return json_encode($ret);
        }

        $table = new TableService();
        $common = new CommonService();
        $prs = new PurchaseRequestService();

        $jrow = (new PurchaseOrder())->toArray();
        $jrow['order_date'] = DateUtil::today();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['files'] = [];
        $this->view->jsonOrder = json_encode($jrow);

        $this->view->supplierList = $common->getSupplierList();
        $this->view->requestList = $prs->getRequestAll();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
        $this->view->page_name = '';
    }

    /**
     * @acl({'link':'purchase:order:create'})
     */
    function editAction($uid)
    {
        $rs = new OrderService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if ($row->status > 20){
            die(ErrorHelper::WRONG_ID);
        }
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->update($row));
            return json_encode($ret);
        }

        $common = new CommonService();
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $ss = new SupplierService();
        $supplier_row = $ss->selectById($row->supplier_id);

        $jrow = $row->toArray();
        $jrow['supplier_name'] = empty($supplier_row) ? '' : $supplier_row->name;
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $this->view->jsonOrder = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->supplierList = $common->getSupplierList();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
        $this->view->page_name = '';
        $this->view->pick('order/create');
    }

    /**
     * @acl({'link':'purchase:order:create'})
     */
    function detailAction($uid = '')
    {
        $rs = new OrderService();
        if (!empty($uid)) {
            $row = $rs->selectByUid($uid);
            if (empty($row) || $row->del_flag == 1) {
                die(ErrorHelper::ROW_NOTEXIST);
            } else if ($row->status != PurchaseOrder::STATUS_START) {
                die(ErrorHelper::WRONG_STATUS);
            }
        } else {
            $row = new PurchaseOrder();
        }

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->detail($row));
            return json_encode($ret);
        }

        $gs = new GoodsTypeService();
        $common = new CommonService();
        $prs = new PurchaseRequestService();

        $detail_list = CvtUtil::emptyToArray($row->detail_data);
        $this->view->detail = json_encode($detail_list);

        $this->view->uid = $uid;
        $this->view->order_date = empty($row->order_date) ? DateUtil::today() : $row->order_date;
        $this->view->supplier_id = CvtUtil::nullToBlank($row->supplier_id);
        $this->view->order_code = $row->order_code;
        $this->view->request_id = $row->request_id;
        $this->view->reject_status = $row->reject_status;
        $this->view->reject_remarks = $row->reject_remarks;
        $this->view->jsonTree = $gs->selectTree();
        $this->view->apply_list = json_encode(!empty($row->id) ? $rs->getApplyList($row->id)['apply'] : []);
        $this->view->supplierList = $common->getSupplierList();
        $this->view->requestList = $prs->getRequestAll();

    }

    /**
     * @acl({'link':'purchase:order:create'})
     */
    public function addapplyAction($type,$request_id, $ids = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderService();
            $builder = $s->selectWaitAll($request_id, $ids);
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->ids = $ids;
        $this->view->request_id = $request_id;
    }

    /**
     * @acl({'link':'purchase:order:create'})
     */
    public function addapplySaveAction()
    {
        $s = new OrderService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->addApply();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:order:create'})
     */
    public function delapplyAction()
    {
        $s = new OrderService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delApply();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function applyAction($uid)
    {
        $rs = new OrderService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            return json_encode($rs->getApplyList($row->id));
        }
    }

    /**
     * @acl({'link':'purchase:order:create'})
     */
    public function deleteAction(){
        $s = new OrderService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->delete());
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:order:list'})
     */
    public function cancelAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderService();
            $ret = new JsonData();
            $ret->handleResult($s->cancelReview());
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:order:create'})
     */
    public function finishAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderService();
            $ret = new JsonData();
            $ret->handleResult($s->finish());
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $rs = new OrderService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        $jrow = $row->toArray();
        $oss_util = new FileService();
        $ss = new SupplierService();
        $supplier_row = $ss->selectById($row->supplier_id);
        $jrow['supplier_name'] = '';
        if (!empty($supplier_row)){
            $jrow['supplier_name'] = $supplier_row->name;
        }

        $prs = new PurchaseRequestService();
        $request_row = $prs->selectById($row->request_id);
        $jrow['request_code'] = '';
        if (!empty($supplier_row)){
            $jrow['request_code'] = $request_row->apply_code;
        }

        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = CvtUtil::emptyToArray($row->detail_data);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
        $this->view->jsonOrder = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new OrderService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new OrderService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }
}