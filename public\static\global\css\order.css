.summary-box {
    display: flex;
    flex-direction: column;
    border: 1px solid #A2A2A2;
    font-size: 16px;
    font-family: "Source Han Sans CN", sans-serif;
    color: #4B4F51;
    margin: 15px 0 20px;
}

.summary-box .summary-box-row {
    display: flex;
    flex-direction: row;
    padding: 0 40px;
}

.summary-box .summary-box-row.row-first {
    border-bottom: 1px dotted #B2B2B2;
}

.summary-tab {
    position: relative;
    margin: 15px 40px;
    padding: 5px 0;
}

.summary-box .summary-box-row .summary-tab:first-child {
    margin-left: 0;
}

.summary-tab .title {
    cursor: pointer;
}

.summary-tab .cnt {
    height: 20px;
    min-width: 20px;
    text-align: center;
    line-height: 20px;
    position: absolute;
    font-size: 12px;
    background: #1c84c6;
    border-radius: 12px;
    color: #FFFFFF;
    top: -5px;
    right: -25px;
}

.summary-box .summary-box-row .summary-tab:hover {
    border-bottom: 1px solid #4B4F51;
    font-weight: 500;
}

.summary-box .summary-box-row .summary-tab.active {
    border-bottom: 1px solid #1c84c6;
    font-weight: 500;
    color: #1c84c6;
}

.summary-box-row .summary-col {
    margin: 15px 10px;
}

.summary-box .summary-box-row .summary-col:first-child {
    margin-left: 0;
}

.summary-box-row .summary-col .summary-num {
    color: #18a689;
}

.order-btn-group {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.order-btn-group .btn {
    min-width: 124px;
    margin-bottom: 5px;
}

.order-btn-group .btn.last {
    margin-bottom: 0;
}

.order-header-bar {
    font-size: 16px;
    color: #75757b;
    letter-spacing: 1px;
}

.order-header-bar .order-header-main,
.order-header-bar .order-header-sub {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 27px;
}

.order-header-bar .order-header-main {
    border: 1px solid #e5e5e5;
    background: #f5f5f5;
}

.order-header-bar .order-header-sub {
    background: #f5f5f5;
    border-left: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
    padding-left: 27px;
    position: relative;
}

.order-header-bar .order-header-sub:before {
    content: '';
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 8px solid #f5f5f5;
    position: absolute;
    top: -8px;
    left: 104px;
    z-index: 1;
}

.order-header-bar .order-header-sub:after {
    content: '';
    width: 0;
    height: 0;
    border-left: 6px solid #f5f5f5;
    border-right: 6px solid #f5f5f5;
    border-bottom: 8px solid #e5e5e5;
    position: absolute;
    top: -9px;
    left: 103px;
}

.order-header-bar .oh-item {
    margin-right: 40px;
}

.order-type {
    background: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    margin-right: 27px;
}

.order-type.blue {
    color: #1c84c6;
}
.order-type.green {
    color: #19B393;
}
.order-type.yellow {
    color: #f8ac59;
}

.order-head {
    font-size: 16px;
}

.order-body {
    width: 100%;
}

.order-body table tr td {
    border: 1px solid #e5e5e5;
    text-align: center;
    vertical-align: middle !important;
}

.tab-content {
    display: none;
}

.order-body a, .order-body a:hover, .order-body a:focus {
    text-decoration-line: underline;
}

.row-btn {
    display: flex;
    justify-content: center;
    margin-bottom: 8px;
}

.row-btn.last {
    margin-bottom: 0;
}

.row-btn > button:first-child {
    margin-right: 15px;
}

.general-panel {
    background-color: #F0F0F0;
    font-size: 16px;
    color: #4D4E51;
}

.general-panel .gp-header, .general-panel .gp-body, .general-panel .gp-row {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.general-panel .gp-header {
    border-bottom: 1px dotted #ADADAD;
    padding: 13px 80px;
}

.general-panel .gp-header .gp-item {
    margin-right: 60px;
}

.general-panel .gp-body {
    padding: 6px 0;
}

.general-panel .gp-body .gp-area {
    width: 50%;
    padding: 7px 80px;
}

.general-panel .gp-body .gp-area:first-child {
    border-right: 1px dotted #ADADAD;
}

.general-panel .gp-row {
    padding: 5px 0;
}

.general-panel .gp-row .gp-item {
    width: 50%;
}

.btn-reject {
    margin-left: 10px;
    text-decoration: underline;
    color: #ed5565;
    cursor: pointer;
}

.btn-reject:hover, .btn-reject:active {
    color: #b8424e;
}