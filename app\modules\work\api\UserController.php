<?php
namespace Envsan\Modules\Work\Api;

use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\UserService;
use Envsan\Modules\Sys\Service\UserService as SysUserService;

class UserController extends SuperController
{
    public function indexAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new UserService();
            return json_encode($s->getUser());
        }
    }

    public function changeAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new SysUserService();
            $ret = new JsonData();
            $ret->message = $rs->changepwd();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function signAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new UserService();
            $ret = new JsonData();
            $rtn = $rs->signSave();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}