{"version": 3, "file": "js/src_view_document_print_vue.5863ae66.js", "mappings": ";;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACnLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/document/print.vue", "webpack://sfp_ext/./src/view/document/print.vue", "webpack://sfp_ext/./src/view/document/print.vue?a256", "webpack://sfp_ext/./src/view/document/print.vue?8ffb", "webpack://sfp_ext/./src/view/document/print.vue?8854", "webpack://sfp_ext/./src/view/document/print.vue?70e0", "webpack://sfp_ext/./src/view/document/print.vue?d34d", "webpack://sfp_ext/./src/view/document/print.vue?b404"], "sourcesContent": ["<template>\r\n    <div class=\"container-print-page\">\r\n        <div class=\"container1-page\" v-for=\"(data,data_idx) in data_list\" :key=\"data_idx\" :style=\"{\r\n                    width:ele_data.width+'px',\r\n                    minHeight:ele_data.height+'px',\r\n                    borderWidth: ele_data.border[0]+'px '+ele_data.border[1]+'px '+ele_data.border[2]+'px '+ele_data.border[3]+'px',\r\n                    borderStyle : 'solid',\r\n                    borderColor : '#000',\r\n                    margin : ele_data.margin[0]+'px '+ele_data.margin[1]+'px '+ele_data.margin[2]+'px '+ele_data.margin[3]+'px'}\">\r\n            <print-item :tasks=\"ele_data.tasks\" :styleProps=\"{width:ele_data.width+'px'}\" :data=\"data\"/>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import printItem from \"./print_item\";\r\n    export default {\r\n        name: \"documentPrint\",\r\n        display: \"documentPrint\",\r\n        order: 15,\r\n        components: {\r\n            printItem\r\n        },\r\n        data() {\r\n            return {\r\n                uid : '',\r\n                doc_id: '',\r\n                ele_data: {\r\n                    id: 'page',\r\n                    name:'',\r\n                    width : 960,\r\n                    height : 50,\r\n                    border: [0,0,0,0],\r\n                    margin: [0,0,0,0],\r\n                    tasks:[]\r\n                },\r\n                data_list:[]\r\n            };\r\n        },\r\n        created(){\r\n            this.uid = this.$route.query.uid || '';\r\n            this.doc_id = this.$route.query.doc_id || '';\r\n            this.init(this.uid, this.doc_id);\r\n        },\r\n        methods: {\r\n            init(uid,doc_id){\r\n                this.$http.post('printing/template/print', {uid:uid,doc_id:doc_id}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let page_detail_id = rs.form_data.page_detail_id || '';\r\n                        let page_count =  rs.form_data.page_count || 0;\r\n                        let sum_data = rs.form_data.sum_data || [];\r\n                        let page_sum_data = {};\r\n                        let all_sum_data = {};\r\n                        for(let item of sum_data){\r\n                            if (item.page_flag == '是'){\r\n                                page_sum_data[item.detail_id + '|' + item.item_id] = 0;\r\n                            }\r\n                            if (item.sum_flag == '是'){\r\n                                all_sum_data[item.detail_id + '|' + item.item_id] = 0;\r\n                            }\r\n                        }\r\n                        let data_list = [];\r\n                        if (page_detail_id != '' && page_count > 0){\r\n                            if (rs.data[page_detail_id]){\r\n                                let detail_list = [];\r\n                                let page_num = 1;\r\n                                for (let i = 0; i < rs.data[page_detail_id].length; i++){\r\n                                    if (i == page_num*page_count){\r\n                                        let new_data = JSON.parse(JSON.stringify(rs.data));\r\n                                        new_data[page_detail_id] = JSON.parse(JSON.stringify(detail_list));\r\n                                        data_list.push(new_data);\r\n                                        detail_list = [];\r\n                                        page_num++;\r\n                                    }\r\n                                    if (i < page_num*page_count){\r\n                                       detail_list.push(rs.data[page_detail_id][i]);\r\n                                    }\r\n                                }\r\n                                if (detail_list.length > 0){\r\n                                    let new_data = JSON.parse(JSON.stringify(rs.data));\r\n                                    new_data[page_detail_id] = JSON.parse(JSON.stringify(detail_list));\r\n                                    data_list.push(new_data);\r\n                                }\r\n                            }\r\n                        } else {\r\n                            data_list.push(rs.data);\r\n                        }\r\n                        if (sum_data.length > 0){\r\n                            let all_detail_ids = [];\r\n                            for(let data of data_list){\r\n                                for (let key in page_sum_data){\r\n                                    page_sum_data[key] = 0;\r\n                                }\r\n                                let page_flag = false;\r\n                                let detail_id = '';\r\n                                for (let key in page_sum_data){\r\n                                    let keys = key.split('|');\r\n                                    if(data[keys[0]]){\r\n                                        detail_id = keys[0];\r\n                                        for (let d of data[keys[0]]){\r\n                                            if (d[keys[1]]){\r\n                                                try {\r\n                                                    page_sum_data[key] += parseFloat(d[keys[1]]);\r\n                                                } catch (error){\r\n                                                    page_sum_data[key] = 0;\r\n                                                }\r\n                                                page_flag = true;\r\n                                            }\r\n                                        }\r\n                                    }\r\n                                }\r\n                                for (let key in all_sum_data){\r\n                                    let keys = key.split('|');\r\n                                    if(data[keys[0]]){\r\n                                        if (!all_detail_ids.includes(keys[0])){\r\n                                            all_detail_ids.push(keys[0]);\r\n                                        }\r\n                                        for (let d of data[keys[0]]){\r\n                                            if (d[keys[1]]){\r\n                                                try {\r\n                                                    all_sum_data[key] += parseFloat(d[keys[1]]);\r\n                                                } catch (error){\r\n                                                    all_sum_data[key] = 0;\r\n                                                }\r\n\r\n                                            }\r\n                                        }\r\n                                    }\r\n                                }\r\n                                if (page_flag && data_list.length > 1){\r\n                                    let row = JSON.parse(JSON.stringify(data[detail_id][0]));\r\n                                    for (let row_id in row){\r\n                                        row[row_id] = '';\r\n                                        for (let key in page_sum_data){\r\n                                            let keys = key.split('|');\r\n                                            if(keys[0] == detail_id && keys[1] == row_id){\r\n                                                row[row_id] = '合计:' + Number(page_sum_data[key].toFixed(4));\r\n                                            }\r\n                                        }\r\n                                    }\r\n                                    data[detail_id].push(row);\r\n                                }\r\n                            }\r\n                            for (let all_detail_id of all_detail_ids){\r\n                                let last_data = data_list[data_list.length-1];\r\n                                let row = JSON.parse(JSON.stringify(last_data[all_detail_id][0]));\r\n                                for (let row_id in row){\r\n                                    row[row_id] = '';\r\n                                    for (let key in all_sum_data){\r\n                                        let keys = key.split('|');\r\n                                        if(keys[0] == all_detail_id && keys[1] == row_id){\r\n                                            row[row_id] = '总计:' + Number(all_sum_data[key].toFixed(4));\r\n                                        }\r\n                                    }\r\n                                }\r\n                                last_data[all_detail_id].push(row);\r\n                            }\r\n                        }\r\n                        this.data_list = data_list;\r\n                        this.ele_data = rs.form_data;\r\n\r\n                        // 等待 DOM 更新后再打印\r\n                        this.$nextTick(() => {\r\n                            setTimeout(() => {\r\n                                try {\r\n                                    window.print();\r\n                                } catch (error) {\r\n                                    this.$message.error('打印失败：' + error.message);\r\n                                }\r\n                            }, 500); // 增加延迟确保渲染完成\r\n                        });\r\n                    } else {\r\n                        this.$message.error(rs.message || '获取打印数据失败');\r\n                    }\r\n                }).catch((error) => {\r\n                    this.$message.error('获取打印数据失败：' + (error.message || '未知错误'));\r\n                });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style scoped>\r\n    .container-print-page{\r\n        background-color: #fff;\r\n        text-align: center;\r\n        margin-top: 10px;\r\n        padding: 0;\r\n    }\r\n    .container1-page{\r\n        background-color: #fff;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"container-print-page\"},_vm._l((_vm.data_list),function(data,data_idx){return _c('div',{key:data_idx,staticClass:\"container1-page\",style:({\n                width:_vm.ele_data.width+'px',\n                minHeight:_vm.ele_data.height+'px',\n                borderWidth: _vm.ele_data.border[0]+'px '+_vm.ele_data.border[1]+'px '+_vm.ele_data.border[2]+'px '+_vm.ele_data.border[3]+'px',\n                borderStyle : 'solid',\n                borderColor : '#000',\n                margin : _vm.ele_data.margin[0]+'px '+_vm.ele_data.margin[1]+'px '+_vm.ele_data.margin[2]+'px '+_vm.ele_data.margin[3]+'px'})},[_c('print-item',{attrs:{\"tasks\":_vm.ele_data.tasks,\"styleProps\":{width:_vm.ele_data.width+'px'},\"data\":data}})],1)}),0)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.container-print-page[data-v-58eb519a]{\\n    background-color: #fff;\\n    text-align: center;\\n    margin-top: 10px;\\n    padding: 0;\\n}\\n.container1-page[data-v-58eb519a]{\\n    background-color: #fff;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./print.vue?vue&type=style&index=0&id=58eb519a&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"7985a824\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./print.vue?vue&type=style&index=0&id=58eb519a&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./print.vue?vue&type=style&index=0&id=58eb519a&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./print.vue?vue&type=template&id=58eb519a&scoped=true\"\nimport script from \"./print.vue?vue&type=script&lang=js\"\nexport * from \"./print.vue?vue&type=script&lang=js\"\nimport style0 from \"./print.vue?vue&type=style&index=0&id=58eb519a&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"58eb519a\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('58eb519a')) {\n      api.createRecord('58eb519a', component.options)\n    } else {\n      api.reload('58eb519a', component.options)\n    }\n    module.hot.accept(\"./print.vue?vue&type=template&id=58eb519a&scoped=true\", function () {\n      api.rerender('58eb519a', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/document/print.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./print.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./print.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./print.vue?vue&type=style&index=0&id=58eb519a&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./print.vue?vue&type=template&id=58eb519a&scoped=true\""], "names": [], "sourceRoot": ""}