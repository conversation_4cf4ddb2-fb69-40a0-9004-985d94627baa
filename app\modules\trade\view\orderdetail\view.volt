<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">产品名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" v-model="name" required readonly maxlength="100"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">订单数量</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" number="true" name="cnt" v-model="cnt" readonly required maxlength="8">
                                        <span class="input-group-addon">个</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">单价</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" number="true" name="price" v-model="price" readonly required maxlength="8">
                                        <span class="input-group-addon">元/个</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">交付日期</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control date dtpicker" placeholder="请输入交付日期" name="deliver_date" v-model="deliver_date" readonly required/>
                                        <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ partial('view') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" readonly maxlength="200" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
    });
</script>