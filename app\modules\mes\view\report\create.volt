{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content" style="padding-top: 5px !important; padding-bottom: 5px !important;">
    <div class="row" style="display: flex; flex-direction: column;">
        <div style="padding-left: 5px; padding-right: 5px;">
            <div class="portlet light" style="margin-bottom: 0; padding-top: 5px; padding-bottom: 5px;">
                <div class="portlet-title" style="margin-bottom: 5px;">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">生产情况</span>
                    </div>
                    <button type="button" class="btn green" @click="save"><i class="fa fa-save"></i> 保存</button>
                            <button type="button" class="btn yellow" @click="commit"><i class="fa fa-check"></i> 提交</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
                <div class="portlet-body form" >
                    <form id="form" class="form-horizontal">
                        <div class="form-body"  style="overflow-y: auto;overflow-x: hidden;padding-top: 5px; padding-bottom: 5px; ">
                            <div id="form_data" class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>生产日期</label>
                                        <div class="col-sm-8">
                                            {% if dispatcher.getActionName() == 'edit' %}
                                                <input type="text" class="form-control" name="report_date" v-model="report_date" readonly/>
                                            {% else %}
                                                <div class="input-group">
                                                    <input type="text" class="form-control date dtpicker" placeholder="请输入生产日期" name="report_date" v-model="report_date" required/>
                                                    <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div style="padding-top: 10px;  padding-left: 5px; padding-right: 5px;">
            <div class="portlet light" style="margin-bottom: 0" style="padding-left: 5px; padding-right: 5px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">生产日报</span>
                    </div>
                    <div class="actions">
                    </div>
                </div>
                <div class="portlet-body" style="overflow-y: auto">
                    <div v-for="work_item,work_idx in report_data" style="margin-bottom: 15px">
                        <div style="width: 100%;background-color: #F2F2F2;display: flex;flex-direction: row;padding: 8px;border: 1px solid #E2E2E2">
                            <div>
                                <span v-text="work_item.real_name" style="font-weight: bold"></span>
                            </div>
                            <div style="display: flex;color:#0080FF;padding-left: 10px;font-weight: bold">
                                <div style="margin-right: 10px">
                                    时薪：<span v-text="work_item.cost + ' 元/H'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    日工：<span v-text="work_item.day_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    计件：<span v-text="work_item.jj_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    其他工作：<span v-text="work_item.other_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    补助：<span v-text="work_item.day_bz_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px;color: red">
                                    总工资：<span v-text="work_item.sum_money + ' 元'"></span>
                                </div>
                            </div>
                        </div>
                        <div style="border: 1px solid #E2E2E2">
                            <div v-if="work_item.produce_list.length > 0">
                                <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                    <thead>
                                    <tr>
                                        <th>班次</th>
                                        <th>批次号</th>
                                        <th>产品名称</th>
                                        <th>工艺名称</th>
                                        <th>基准</th>
                                        <th>生产性</th>
                                        <th>合格率</th>
                                        <th>工资方式</th>
                                        <th>合格数量</th>
                                        <th>加工费(元/件)</th>
                                        <th>工时(H)</th>
                                        <th>工资系数</th>
                                        <th>不良数</th>
                                        <th>不良原因</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="d_item, d_index in work_item.produce_list">
                                        <td>
                                            <div style="width: 80px">
                                                <select class="bs-select form-control" :name="'shift_type' + d_index" v-model="d_item.shift_type" @change="produceCalc(work_idx)">
                                                    <option value="1">白班</option>
                                                    <option value="2">夜班</option>
                                                </select>
                                            </div>
                                        </td>
                                        <td>
                                           <span v-text='d_item.notice_code'></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.product_code'></span>
                                            <br>
                                            <span v-text='d_item.product_name'></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.bom_code'></span>
                                            <br>
                                            <span v-text='d_item.bom_name'></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.scxjz != ''" v-text="d_item.scxjz + '(件/H)'"></span>
                                            <br>
                                            <span v-if="d_item.jzz != ''" v-text="d_item.jzz + '(H)'"></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.scx != ''" v-text="d_item.scx + '%'"></span>
                                        </td>
                                        <td>
                                            <span v-text="getQualifiedRate(d_item)"></span>
                                        </td>
                                        <td>
                                            <div style="width: 80px">
                                                <select class="bs-select form-control" :name="'work_type' + d_index" v-model="d_item.work_type" @change="produceCalc(work_idx)">
                                                    <option value="1">日工</option>
                                                    <option value="2">计件</option>
                                                </select>
                                            </div>
                                        </td>
                                        <td>
                                            <input style="width: 100px" type="number" step="1" min="0" class="form-control" @change="produceCalc(work_idx)" placeholder="合格数量" :name="'cnt_' + d_index" v-model="d_item.cnt">
                                        </td>
                                        <td>
                                            <input style="width: 100px" type="number" class="form-control" @change="produceCalc(work_idx)" placeholder="加工费" :name="'one_cost_' + d_index" v-model="d_item.one_cost">
                                        </td>
                                        <td>
                                            <input style="width: 100px" type="number" class="form-control" @change="produceCalc(work_idx)" placeholder="工时" :name="'hour_' + d_index" v-model="d_item.hour">
                                        </td>
                                        <td>
                                            <input style="width: 100px" type="number" class="form-control" @change="produceCalc(work_idx)" placeholder="工资系数" :name="'wages_ratio_' + d_index" v-model="d_item.wages_ratio">
                                        </td>
                                        <td>
                                            <input style="width: 100px" type="number" step="1" min="0" class="form-control" placeholder="不良数" :name="'error_cnt_' + d_index" v-model="d_item.error_cnt">
                                        </td>
                                        <td>
                                            <span v-text='d_item.error_type'></span>
                                            <br>
                                            <span v-text='d_item.error_remarks'></span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div v-if="work_item.other_list.length > 0">
                                <div style="display: flex;width: 100%;padding: 5px;flex-wrap: wrap">
                                    <div style="width: 50%;padding-right: 5px" v-for="o_item, o_index in work_item.other_list">
                                        <div style="width: 100%;display: flex;background-color: #F2F2F2;padding: 5px;border: 1px solid #e2e2e2;border-radius: 5px">
                                            <div style="width: 15%;line-height: 36px">
                                                <span v-text="o_item.produce_type"></span>
                                            </div>
                                            <div style="width: 20%;line-height: 36px">
                                                <span v-text="o_item.remarks"></span>
                                            </div>
                                            <div style="width: 15%;line-height: 36px">
                                                <div style="width: 70px">
                                                    <select class="bs-select form-control" :name="'oshift_type' + o_index" v-model="o_item.shift_type" @change="produceCalc(work_idx)">
                                                        <option value="1">白班</option>
                                                        <option value="2">夜班</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div style="width: 25%">
                                                <div class="input-group" style="width: 140px;">
                                                    <span class="input-group-addon">工时(H)</span>
                                                    <input type="number" class="form-control" @change="produceCalc(work_idx)" placeholder="工时" :name="'ohour_' + o_index" v-model="o_item.hour">
                                                </div>
                                            </div>
                                            <div style="width: 25%">
                                                <div class="input-group" style="width: 140px;">
                                                    <span class="input-group-addon">工资系数</span>
                                                    <input style="width: 70px" type="number" @change="produceCalc(work_idx)" class="form-control" placeholder="工资系数" :name="'owages_ratio_' + o_index" v-model="o_item.wages_ratio">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{ partial('check') }}
{{ partial('uploader_file') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {
            save(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                if( !this.validateReportData() )
                    return;
                this.summit(1);
            },
            commit(e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                if( !this.validateReportData() )
                    return;
                var dlg = top.layer.confirm('确认提交吗?', () => {
                    top.layer.close(dlg);
                    this.summit(2);
                });
            },
            summit(type){
                showSpin();
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('mes/report/edit/' ~ uid) }}';
                {% else %}
                var url= '{{ url('mes/report/create') }}';
                {% endif %}
                $.post(url, {
                    type : type,
                    report_date : app.report_date,
                    remarks : app.remarks,
                    files : encodeURI(JSON.stringify(app.files)),
                    ext_data : encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                    report_data : encodeURI(JSON.stringify(app.report_data)).replace(/\+/g,'%2B')
                }, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error(rs.message);
                    }
                })
            },
            delFile: function(index) {
                this.files.splice(index);
            },
            produceCalc(work_idx){
                let work_data = app.report_data[work_idx];
                let day_money = 0;
                let day_hour = 0;
                let other_day_hour = 0;
                let day_bz_money = 0;
                let jj_money = 0;
                let other_money = 0;
                let sum_money = 0;
                try {
                    for(let produce_item of work_data.produce_list){
                        if (produce_item.scxjz != ''){
                            produce_item.jzz = this.toRound(this.numFormart(produce_item.cnt) / this.numFormart(produce_item.scxjz),2);
                            produce_item.scx = this.toRound((this.numFormart(produce_item.jzz) / this.numFormart(produce_item.hour))*100,2);
                        }
                        if (produce_item.work_type == 1){
                            day_hour +=  this.numFormart(produce_item.hour);
                            day_money += this.numFormart(produce_item.hour) * this.numFormart(work_data.cost) *  this.numFormart(produce_item.wages_ratio);
                        } else {
                            jj_money += this.numFormart(produce_item.one_cost) * this.numFormart(produce_item.cnt) *  this.numFormart(produce_item.wages_ratio);
                        }
                        if (produce_item.shift_type == 2){
                            day_bz_money += this.numFormart(produce_item.hour) * 0.5;
                        }
                    }
                    for(let other_item of work_data.other_list){
                        other_day_hour += this.numFormart(other_item.hour)
                        other_money +=  this.numFormart(other_item.hour) * this.numFormart(work_data.cost) *  this.numFormart(other_item.wages_ratio);
                        if (other_item.shift_type == 2){
                            day_bz_money += this.numFormart(other_item.hour) * 0.5;
                        }
                    }
                    work_data.day_money = this.toRound(day_money,2);
                    work_data.day_hour = this.toRound(day_hour,2);
                    work_data.other_day_hour = this.toRound(other_day_hour,2);
                    work_data.day_bz_money = this.toRound(day_bz_money,2);
                    work_data.jj_money = this.toRound(jj_money,2);
                    work_data.other_money = this.toRound(other_money,2);
                    work_data.sum_money = this.toRound( work_data.day_money + work_data.day_bz_money + work_data.jj_money + work_data.other_money,2);
                } catch (e){
                    console.error(e);
                }
            },
            numFormart(v){
                if (v == null || v == ''){
                    return 0;
                }
                try {
                    return parseFloat(v);
                } catch (e){
                    return 0;
                }
            },
            toRound(v,b){
               return  Number(v.toFixed(b));
            },
            getQualifiedRate(item){
                // 计算合格率：合格数量 / (合格数量 + 不合格数量)
                let qualifiedCount = this.numFormart(item.cnt);
                let errorCount = this.numFormart(item.error_cnt);
                let totalCount = qualifiedCount + errorCount;
                
                if (totalCount <= 0) {
                    return '0%';
                }
                
                let rate = (qualifiedCount / totalCount) * 100;
                return this.toRound(rate, 2) + '%';
            },
            validateReportData(){
                // 验证报工数据
                for(let work_idx = 0; work_idx < this.report_data.length; work_idx++){
                    let work_item = this.report_data[work_idx];
                    
                    // 验证生产记录
                    for(let d_index = 0; d_index < work_item.produce_list.length; d_index++){
                        let d_item = work_item.produce_list[d_index];
                        let staff_name = work_item.real_name;
                        let row_info = `第${d_index + 1}行`;
                        
                        // 1. 合格数量必须输入，且为非负整数（可以为0）
                        if(d_item.cnt === '' || d_item.cnt === null || d_item.cnt === undefined){
                            toastr.error(`${row_info}：合格数量不能为空`);
                            return false;
                        }
                        if(!this.isNonNegativeNumber(d_item.cnt)){
                            toastr.error(`${row_info}：合格数量必须为有效数值`);
                            return false;
                        }
                        
                        // 2. 不良数必须为非负整数（可以为0）
                        if(d_item.error_cnt !== null && d_item.error_cnt !== '' && d_item.error_cnt !== undefined){
                            if(!this.isNonNegativeNumber(d_item.error_cnt)){
                                toastr.error(`${row_info}：不良数必须为有效数值`);
                                return false;
                            }
                        }
                        
                        // 3. 验证其他数值字段（可以为小数）
                        if(d_item.one_cost !== null && d_item.one_cost !== '' && d_item.one_cost !== undefined){
                            if(!this.isValidNumber(d_item.one_cost)){
                                toastr.error(`${row_info}：加工费必须为有效数值`);
                                return false;
                            }
                        }
                        
                        if(d_item.hour !== null && d_item.hour !== '' && d_item.hour !== undefined){
                            if(!this.isValidNumber(d_item.hour) || parseFloat(d_item.hour) <= 0){
                                toastr.error(`${row_info}：工时必须为正数`);
                                return false;
                            }
                        }
                        
                        if(d_item.wages_ratio !== null && d_item.wages_ratio !== '' && d_item.wages_ratio !== undefined){
                            if(!this.isValidNumber(d_item.wages_ratio)){
                                toastr.error(`${row_info}：工资系数必须为有效数值`);
                                return false;
                            }
                        }
                    }
                }
                return true;
            },
            // 检查是否为非负整数（包括0）
            isNonNegativeNumber(value){
                // 允许非负数（包括小数）
                return /^\d+(\.\d+)?$/.test(value) && parseFloat(value) >= 0;
            },
            // 检查是否为有效数值
            isValidNumber(value){
                return !isNaN(value) && isFinite(value) && value !== '';
            }
        },
        watch: {
            report_date: function(val) {
                if (val == ''){
                    app.report_data = [];
                    return;
                }
                showSpin();
                $.post('{{ url('mes/report/date') }}', { report_date: val }, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        app.report_data = rs.data;
                    } else {
                        app.report_data = [];
                        toastr.error(rs.message);
                    }
                });
            }
        }
    });

    initUpLoaderPdf('report');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}
<style>
    .tb-left {
        position: sticky;
        left: 0;
        z-index: 1;
    }

    .left-1 {
        border-left: 0 !important;
    }
    .left-2 {
        left: 220px;
    }
    .left-3 {
        left: 380px;
    }
    .left-4 {
        left: 480px;
    }
    .left-5 {
        left: 580px;
    }

    thead .tb-left {
        background-color: #3598DC !important;
    }

    .zh-table-box th, .zh-table-box td {
        text-align: left;
    }

    .zh-table-box table tbody .tb-left {
        border: 0 !important;
        padding: 0 !important;
    }

    .zh-table-box table tbody td {
        background-color: #ffffff;
    }

    .zh-table-box table tbody .tb-left > div {
        padding: 8px;
        border-right: 1px solid #e7ecf1;
        border-bottom: 1px solid #e7ecf1;
        line-height: 36px;
        height: 54px;
    }

    .zh-table-box table tbody .left-1 > div {
        border-left: 1px solid #e7ecf1;
    }

    .btn-del {
        width: 18px;
        height: 18px;
        font-size: 13px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 100%;
        margin-left: 5px;
        background-color: #e7505a;
        color: #FFFFFF;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .btn-del:hover {
        background-color: #e12330;
    }

    .btn-del:active {
        background-color: #c51b26;
    }
</style>