<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
class PurchaseViewWworderInstock extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $order_detail_id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $quantity;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_view_wworder_instock';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewWworderInstock[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewWworderInstock
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
