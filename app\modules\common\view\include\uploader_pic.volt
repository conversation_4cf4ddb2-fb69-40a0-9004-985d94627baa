<div id="btn_select_pic" style="display: none;">选择文件</div>
<script>
    function initPicUpLoader(folder_name) {
        let uploader_self = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/lib/uploader/uploader.swf') }}',
            server: '{{ url('common/file/upload/') }}' + folder_name,
            dnd: '#dnd_' + folder_name,
            pick: '#btn_select_pic',
            accept: {
                title: 'Images',
                extensions: 'jpg,jpeg,png',
                mimeTypes: 'image/jpg,image/jpeg,image/png'
            },
            fileSingleSizeLimit: 10 * 1024 * 1024,
            compress: false,
            resize: false,
            duplicate: true
        });

        uploader_self.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败');
            }
        });

        uploader_self.on('fileQueued', function(file) {
            showSpin();
            uploader_self.options.formData = {img_type: folder_name};
            uploader_self.upload();
        });

        uploader_self.on('uploadSuccess', function(file, rs) {
            closeSpin();
            if (rs.status == 'ok') {
                uploadPicSuccess(rs);
            } else {
                toastr.error('上传失败' + rs.message);
            }
        });

        uploader_self.on('uploadError', function(file, rs) {
            closeSpin();
            toastr.error('上传失败');
        });
    }

    function uploadPic() {
        $("#btn_select_pic").find("label").click();
    }
</script>