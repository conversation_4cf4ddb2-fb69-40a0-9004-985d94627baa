<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                <thead>
                <tr>
                    <th>入出库日期</th>
                    <th>入出库数量</th>
                    <th>操作人</th>
                    <th>操作时间</th>
                    <th>备注</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="row, index in list">
                    <td>
                        <span v-text="row.work_date"></span>
                    </td>
                    <td>
                        <span v-text="row.cnt"></span>
                    </td>
                    <td>
                        <span v-text="row.staff_name"></span>
                    </td>
                    <td>
                        <span v-text="row.create_time"></span>
                    </td>
                    <td>
                        <span v-text="row.remarks"></span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            list:{{ list }}
        },
        methods: {

        }
    });
</script>