<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Res;
use Phalcon\Mvc\User\Component;
use Envsan\Common\Data\SessionData;

class ResService extends Component
{
    public function selectTree($all=true)
    {
        // 一次性查询所有资源
        $allResources = Res::find([
            'del_flag = 0',
            'order' => 'id ASC'
        ]);

        // 将所有资源转换为数组格式
        $resourceArray = [];
        $resourceMap = []; // 用于快速查找

        foreach ($allResources as $resource) {
            // 构建节点对象
            $node = [
                'id' => $resource->id,
                'uid' => $resource->uid,
                'type' => $resource->type,
                'pid' => $resource->pid,
                'state' => ['opened' => true],
                'children' => []
            ];

            // 设置节点文本
            if (!$all) {
                $node['text'] = $resource->name;
            } else {
                $node['text'] = $resource->name . ' (' . $resource->identity . ')';
                if (!empty($resource->comment)) {
                    $node['text'] .= " ({$resource->comment})";
                }
            }

            // 添加权限标记，用于后续过滤
            if ($resource->type === 'action') {
                $node['has_permission'] = SessionData::isSuper() ||
                    SessionData::isAdmin() ||
                    $this->acl->has($resource->identity);
            } else {
                $node['has_permission'] = true;
            }

            $resourceArray[$resource->id] = $node;

            // 建立父子关系映射
            if (!isset($resourceMap[$resource->pid])) {
                $resourceMap[$resource->pid] = [];
            }
            $resourceMap[$resource->pid][] = $resource->id;
        }

        // 构建树结构
        $tree = [];

        // 获取顶级模块
        $rootIds = isset($resourceMap[0]) ? $resourceMap[0] : [];
        foreach ($rootIds as $id) {
            if ($resourceArray[$id]['type'] == 'module') {
                // 递归构建子树
                $this->buildTreeArray($resourceArray, $resourceMap, $id);

                // 只添加模块类型的根节点
                $tree[] = $resourceArray[$id];
            }
        }

        // 如果不是超级管理员或管理员，过滤没有权限的节点和空节点
        if (!SessionData::isSuper() && !SessionData::isAdmin()) {
            $tree = $this->filterTreeArray($tree);
        }

        return json_encode($tree, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 递归构建树数组
     * @param array &$resourceArray 所有资源的数组
     * @param array $resourceMap 父子关系映射
     * @param int $parentId 父节点ID
     */
    private function buildTreeArray(&$resourceArray, $resourceMap, $parentId)
    {
        // 如果没有子节点，直接返回
        if (!isset($resourceMap[$parentId])) {
            return;
        }

        // 遍历所有子节点
        foreach ($resourceMap[$parentId] as $childId) {
            // 递归构建子节点的子树
            $this->buildTreeArray($resourceArray, $resourceMap, $childId);

            // 将子节点添加到父节点的children中
            $resourceArray[$parentId]['children'][] = &$resourceArray[$childId];
        }
    }

    /**
     * 过滤树数组，移除没有权限的节点和空节点
     * @param array $nodes 节点数组
     * @return array 过滤后的节点数组
     */
    private function filterTreeArray($nodes)
    {
        $result = [];

        foreach ($nodes as $node) {
            // 过滤子节点
            if (!empty($node['children'])) {
                $node['children'] = $this->filterTreeArray($node['children']);
            }

            // 根据节点类型和权限决定是否保留
            if ($node['type'] === 'module') {
                // 模块节点：只保留有子节点的模块
                if (!empty($node['children'])) {
                    $result[] = $node;
                }
            } else if ($node['type'] === 'controller') {
                // 控制器节点：只保留有子节点的控制器
                if (!empty($node['children'])) {
                    $result[] = $node;
                }
            } else if ($node['type'] === 'action') {
                // 动作节点：只保留有权限的动作
                if ($node['has_permission']) {
                    $result[] = $node;
                }
            } else {
                // 其他类型节点：保留
                $result[] = $node;
            }
        }

        return $result;
    }

    public function selectAll($find)
    {
        if(empty($find)) {
            return Res::find(['order' => 'action']);
        }
        else{
            return Res::find([
                'name like ?1',
                'bind'=>[1=>"%$find%"],
                'order' => 'action'
            ]);
        }
    }

    public function create($type)
    {
        $row = new Res();
        return $this->build('create', $type, $row);
    }

    public function update($row)
    {
        return $this->build('update', $row->type, $row);
    }

    private function build($act, $type, $row)
    {
        $name = trim($this->request->getPost('name', 'string'));
        $identity = trim($this->request->getPost('identity', 'string'));

        if(empty($identity) || empty($name))
            return ErrorHelper::WRONG_INPUT;

        $row->name = $name;
        $row->identity = $identity;
        $row->comment = trim($this->request->getPost('comment', 'string'));
        $row->update_date = DateUtil::now();

        if($act=='create') {
            $row->uid = UUID::make();
            $row->pid = intval($this->request->getPost('pid', 'string'));
            $row->type = $type;
        }

        if ($row->save())
            return '';
        return '保存失败，请确认标识是否唯一';
    }

    public function selectById($id)
    {
        return Res::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function selectModule($module)
    {
        return Res::findFirst(["module=?1 and category=''", 'bind'=>[1=>$module]]);
    }

    public function deleteById($id)
    {
        $row = Res::findFirst(['id=?1', 'bind'=>[1=>$id]]);
        if($row!=null)
            return $row->delete();
        return false;
    }
}