<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseWarehouse;
use Envsan\Common\Service\BaseService;

class PurchaseWarehouseService extends BaseService
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWarehouse', 't1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseWarehouse();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($act, $row) {
            $code = $this->request->getPost('code', 'tstring');
            $name = $this->request->getPost('name', 'tstring');
            $manager_id = $this->request->getPost('manager_id', 'tstring');
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));

            if (empty($name))
                return $this->error(ErrorHelper::WRONG_INPUT);

            if ($this->isRepeat($name, $row->id)) {
                return $this->error('名称重复');
            }

            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);

            $row->name = $name;
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;

            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            $row->save();
        });

    }

    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWarehouse')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return PurchaseWarehouse::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseWarehouse::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        $row->save();
    }
}