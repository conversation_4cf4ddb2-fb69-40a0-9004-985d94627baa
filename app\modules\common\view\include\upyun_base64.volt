<script>
    function upyunUploadBase64(folder_name, base64Data) {
        let matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,/);
        if (!matches) {
            toastr.error('文件编码无效');
            return;
        }
        let mime_type = matches[1];
        let ext = '';
        if (mime_type.indexOf('image') >= 0) {
            ext = 'jpg';
        } else if (mime_type.indexOf('pdf') >= 0) {
            ext = 'pdf';
        } else if (mime_type.indexOf('excel') >= 0) {
            ext = 'xls';
        } else if (mime_type.indexOf('sheet') >= 0) {
            ext = 'xlsx';
        } else {
            toastr.error('文件格式无效');
            return;
        }
        let now = new Date();
        let date = now.toGMTString();
        let year = now.getFullYear();
        let month = ('0' + (now.getMonth() + 1)).slice(-2);
        let day = ('0' + now.getDate()).slice(-2);
        let remote_path = '{{ upyun.baseDir }}{{ owner.id }}/' + folder_name + '/' + year + '/' + month + '/' + day + '/';
        let file_name = remote_path + now.getTime() + '.' + ext;
        let opts = {
            'save-key': file_name,
            'bucket': '{{ upyun.bucket }}',
            'expiration': Math.round(now.getTime() / 1000) + 3600,
            'date': date,
            'content-type': mime_type
        };
        let policy = btoa(JSON.stringify(opts));
        let data = [ 'POST', '/{{ upyun.bucket }}', date, policy ].join('&');
        return new Promise((resolve, reject) => {
            $.post('{{ url('common/file/sign') }}', {data: data}, function(rs) {
                let blob = base64ToBlob(base64Data, mime_type);
                let formData = new FormData();
                formData.append('authorization', 'UPYUN {{ upyun.user }}:' + rs.data);
                formData.append('policy', policy);
                formData.append("file", blob, 'image.jpg');
                $.ajax({
                    url: 'https://v0.api.upyun.com/{{ upyun.bucket }}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: (res) => {
                        try {
                            let obj = JSON.parse(res);
                            console.log(obj);
                            if (obj.message === 'ok') {
                                resolve(obj.url);
                            } else {
                                reject(obj.message);
                            }
                        } catch (e) {
                            reject(e);
                        }
                    },
                    error: (e) => {
                        reject(e);
                    }
                });
            });
        });
    }

    function base64ToBlob(base64Data, mimeType) {
        const byteString = atob(base64Data.split(',')[1]);
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }
        return new Blob([ab], { type: mimeType });
    }
</script>