{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>委外商名称</label>
                                <div class="col-sm-8">
                                    <select
                                      id="supplier_info"
                                      class="form-control"
                                      v-model="supplier_info"
                                      @change="changeSupplier"
                                    >
                                      <option value="">请选择委外商</option>
                                      <option v-for="row in supplier_list" :value="row.id + '-' + row.name">
                                          ${ row.name }
                                      </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>委外商编码</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="supplier_code" v-model="supplier_code" maxlength="50" required readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>价格标识</label>
                                <div class="col-sm-8">
                                    <select
                                      id="price_flag_info"
                                      class="form-control"
                                      v-model="price_flag_info"
                                      required
                                    >
                                      <option value="">请选择价格标识</option>
                                      <option value="1">含税</option>
                                      <option value="2">未税</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>物料编码</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" @blur="handleGoodsCodeSearch" class="form-control" name="goods_code" v-model="goods_code" maxlength="50" required>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn blue" @click="showGoodsSelector">
                                                <i class="fa fa-search"></i> 选择
                                            </button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>物料名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="goods_name" v-model="goods_name" maxlength="50" required readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">物料代码</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="good_inventory_code" v-model="good_inventory_code" maxlength="50" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>规格型号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="goods_model" v-model="goods_model" maxlength="50" required readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>工序名称</label>
                                <div class="col-sm-8">
                                    <select
                                      id="ship_type_info"
                                      class="form-control"
                                      v-model="ship_type_info"
                                    >
                                      <option value="">请选择工序</option>
                                      <option v-for="row in ship_type_list" :value="row.id + '-' + row.name">
                                          ${ row.name }
                                      </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>计量单位</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="measurement_unit" v-model="measurement_unit" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>计价单位</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="price_unit" v-model="price_unit" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>换算率</label>
                                <div class="col-sm-8">
                                    <input type="number" number="true" class="form-control" name="conversion_rate" v-model="conversion_rate" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>加工费单价</label>
                                <div class="col-sm-8">
                                    <input type="number" number="true" class="form-control" name="unit_price" v-model="unit_price" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>币种</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="currency" v-model="currency" maxlength="50" required readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>税率</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" number="true" name="tax_rate" v-model="tax_rate" required maxlength="8">
                                        <span class="input-group-addon">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>生效日期</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control date dtpicker" placeholder="请输入生效日期" name="effective_date" v-model="effective_date" required/>
                                        <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">失效日期</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control date dtpicker" placeholder="请输入失效日期" name="expiry_date" v-model="expiry_date"/>
                                        <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="remarks" v-model="remarks" maxlength="50">
                                </div>
                            </div>
                        </div>
                        {{ partial('form') }}
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
    <!-- 添加弹出窗口的HTML结构 -->
    <div id="custom-popup" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title">双击选择产品档案</h4>
                </div>
                <div class="modal-body">
                    <div>
                        <div id="tree"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" @click="handleClose">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonPurchaseWwProcess }},
            price_flag_info: '',
            supplier_info: '',
            ship_type_info: '',
            currency: '人民币'
        },
        created() {
            // 确保在Vue实例创建后设置这些值
            if (this.supplier_id && this.supplier_name) {
                this.supplier_info = `${this.supplier_id}-${this.supplier_name}`;
            }
            
            if (this.ship_type_id && this.ship_type_name) {
                this.ship_type_info = `${this.ship_type_id}-${this.ship_type_name}`;
            }

            if (this.price_flag) {
                this.price_flag_info = this.price_flag
            }

            if (this.tax_rate) {
                this.tax_rate = this.tax_rate * 100
            }
        },
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                // 自定义验证
                if (!this.validateForm()) {
                    return;
                }

                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('purchase/wwprocess/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('purchase/wwprocess/create') }}';
                {% endif %}

                let param = JSON.parse(JSON.stringify(this.$data));
                param.ext_data = encodeURI(JSON.stringify(this.ext_data)).replace(/\+/g, '%2B');

                showSpin();
                $.post(url, param, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            // 供应商变更取得供应商其他信息
            changeSupplier() {
                if (!this.supplier_info) {
                    app.supplier_code = '';
                    return;
                }
                showSpin();
                let url = '{{ url('purchase/wwprocess/changesupplier/') }}';
                $.post(url, {
                    supplier_id: this.supplier_info.split("-")[0]
                },function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        app.supplier_code = rs.data.code
                    }
                    else {
                        app.supplier_code = '';
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            // 取得物料的信息
            handleGoodsCodeSearch() {
                // 如果为空则不查询
                if (!this.goods_code) {
                    this.clearGoods();
                    return;
                }  
                showSpin();
                // 调用 API 进行后台检索
                let url = '{{ url('purchase/wwprocess/goodsinfo/') }}';
                $.post(url, {
                    goods_code: this.goods_code
                },function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        // 物料名称
                        app.goods_name = rs.data.name
                        // 物料代码
                        app.good_inventory_code = rs.data.inventory_code
                        // 规格型号
                        app.goods_model = rs.data.model
                    }
                    else {
                        app.clearGoods();
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            clearGoods() {
                this.goods_name = '';
                this.good_inventory_code = '';
                this.goods_model = '';
            },
            showPopup: function() {
                $('#custom-popup').modal('show');
            },
            handleClose() {
                $('#custom-popup').modal('hide');
            },
            // 显示物料选择器
            showGoodsSelector() {
                top.window.layer_result2 = '';
                    top.layer.open({
                        title: '选择物料',
                        type: 2,
                        area: ['90%', '90%'],
                        content: '{{ url('purchase/wwprocess/addgoods') }}?mode=single',
                        end: function() {
                            if (top.window.layer_result2 == 'ok') {
                                app.addDetailList(top.window.layer_data2);
                            }
                        }
                });
            },
            addDetailList(apply_list) {
                paramLoop: for (let i = 0; i < apply_list.length; i++) {
                    apply_row = apply_list[i];
                    app.goods_code = apply_row.code;
                    app.goods_name = apply_row.name;
                    app.goods_model = apply_row.model;
                    app.good_inventory_code = apply_row.inventory_code;
                }
            },
            validateForm() {
                // 税率验证
                if (this.tax_rate && !this.isNumberBetween0And100(this.tax_rate)) {
                    toastr.error('无效的税率，应为0-100之间的数字，最多保留2位小数');
                    return false;
                }
                
                // 换算率验证
                if (parseFloat(this.conversion_rate) < 0 || !this.isValidDecimal(this.conversion_rate, 6, 4)) {
                    toastr.error('换算率格式错误，应为非负数，最多6位整数和4位小数');
                    return false;
                }
                
                // 加工费单价验证
                if (parseFloat(this.unit_price) < 0 || !this.isValidDecimal(this.unit_price, 8, 4)) {
                    toastr.error('加工费格式错误，应为非负数，最多8位整数和4位小数');
                    return false;
                }
                
                // 日期验证
                if (!this.effective_date || !this.isValidDate(this.effective_date) || (this.expiry_date && !this.isValidDate(this.expiry_date))) {
                    toastr.error('请选择有效的日期');
                    return false;
                }
                
                // 日期大小关系验证
                if (this.expiry_date && (new Date(this.effective_date) > new Date(this.expiry_date))) {
                    toastr.error('生效日期不能晚于失效日期');
                    return false;
                }
                
                if (this.expiry_date) {
                    // 失效日期必须大于今天
                    const today = new Date();
                    today.setHours(0, 0, 0, 0); // 设置为当天的00:00:00
                    const expiryDate = new Date(this.expiry_date);
                    
                    if (expiryDate <= today) {
                        toastr.error('失效日期不能早于今天');
                        return false;
                    }
                }
                
                return true;
            },
            
            // 辅助方法：验证整数是否在0-100之间
            isNumberBetween0And100(value) {
                // 首先转换为数字
                const num = parseFloat(value);
                
                // 检查是否为有效数字且在0-100之间
                if (isNaN(num) || num < 0 || num > 100) {
                    return false;
                }
                
                // 检查小数位数是否不超过2位
                const decimalStr = value.toString().split('.')[1] || '';
                return decimalStr.length <= 2;
            },
            
            // 辅助方法：验证小数格式
            isValidDecimal(value, integerDigits, decimalDigits) {
                // 正则表达式验证小数格式
                const pattern = new RegExp(`^\\d{1,${integerDigits}}(\\.\\d{1,${decimalDigits}})?$`);
                return pattern.test(value);
            },
            
            // 辅助方法：验证日期格式
            isValidDate(dateString) {
                // 检查格式是否为yyyy-mm-dd
                if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                    return false;
                }
                
                // 检查日期是否有效
                const date = new Date(dateString);
                return date instanceof Date && !isNaN(date);
            },
        }
    });
    
    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

</script>
{{ partial('form_script') }}