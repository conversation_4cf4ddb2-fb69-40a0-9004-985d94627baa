<?php
namespace Envsan\Modules\Printing\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\DomainUtil;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Purchase\Service\InstockService;
use Envsan\Modules\Purchase\Service\OrderService;
use Envsan\Modules\Purchase\Service\OtherService;
use Envsan\Modules\Purchase\Service\OutstockService;
use Phalcon\Mvc\User\Component;

class PrintService extends Component
{
    public function getPrintData()
    {
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid');
        $doc_id = $this->request->getPost('doc_id');
        if (empty($uid) || empty($doc_id)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $ts = new TemplateService();
        $template_row = $ts->selectByUid($uid);
        if (empty($template_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        switch ($template_row->page_id)
        {
            case 6:
                $rtn = $this->noticeData($doc_id);
                break;
            case 24:
                $rtn = $this->purchaseData($doc_id);
                break;
            case 25:
                $rtn = $this->instockData($doc_id);
                break;
            case 26:
                $rtn = $this->outstockData($doc_id);
                break;
            case 27:
                $rtn = $this->otherData($doc_id);
                break;
            default:
                $rtn->message = ErrorHelper::WRONG_INPUT;
                return $rtn;
        }
        $rtn->form_data = CvtUtil::emptyToArray($template_row->form_data);
        return $rtn;
    }

    public function noticeData($uid){
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.remarks,
                t1.quantity,
                t1.product_id,
                t4.code,
                t2.code as product_code,
                t2.name as product_name,
                t3.name as customer_name,
                t4.plan_begin_date,
                t4.plan_end_date
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        $row['print_user'] = SessionData::user()->real_name;
        $row['print_date'] = DateUtil::today();
        $bom_rows = MesProductBom::find(['del_flag = 0 and product_id = ?1','bind'=>[1 => $row['product_id']],'order'=>'data_sort']);
        $bom_names = '';
        foreach ($bom_rows as $bom_row){
            $bom_names .= $bom_row->name . '→';
        }
        $row['bom_names'] = rtrim($bom_names,'→');
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function purchaseData($uid)
    {
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.order_code,
                t1.order_date,
                t1.total_money,
                t1.total_money_hs,
                t2.name as supplier_name,
                t3.real_name as create_user,
                t4.real_name as review_user,
                t1.remarks,
                t5.name as group_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.review_by = t4.id', 't4')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.group_id = t5.id', 't5')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid])
            ->orderBy('t1.id desc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        $row['print_user'] = SessionData::user()->real_name;
        Logger::error(SessionData::user()->real_name);
        $row['print_time'] = date('Y年m月d日 H:i');
        $ps = new OrderService();
        $row['detail'] = $ps->getDetailList($row['id']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function instockData($uid)
    {
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.code,
                t1.instock_date,
                t1.remarks,
                t2.order_code,
                t3.name as supplier_name,
                u.real_name as create_user
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInstock', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = u.id', 'u')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid])
            ->orderBy('t1.id desc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        $row['print_user'] = SessionData::user()->real_name;
        $row['print_time'] = date('Y年m月d日 H:i');
        $ts = new InstockService();
        $row['detail'] = $ts->getInstockDetailList($row['id']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function otherData($uid)
    {
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.code,
                t1.other_date,
                t1.remarks,
                u.real_name as create_user
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOther', 't1')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = u.id', 'u')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid])
            ->orderBy('t1.id desc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        $row['print_user'] = SessionData::user()->real_name;
        $row['print_time'] = date('Y年m月d日 H:i');
        $ts = new OtherService();
        $row['detail'] = $ts->getDetailList($row['id']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function outstockData($uid){
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.outstock_date,
                t1.outstock_user,
                t1.remarks,
                t1.ext_val,
                t3.name as bom_name,
                t4.code as product_code,
                t4.name as product_name,
                t6.code as order_code,
                t7.name as customer_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOutstock', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesOrderBom', 't1.order_bom_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom', 't2.product_bom_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't3.product_id = t4.id', 't4')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrderDetail', 't2.order_detail_id = t5.id', 't5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder', 't5.order_id = t6.id', 't6')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't6.customer_id = t7.id', 't7')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid])
            ->orderBy('t1.id desc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        $ps = new OutstockService();
        $row['detail'] = $ps->getDetail($row['id']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }
}