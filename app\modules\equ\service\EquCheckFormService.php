<?php
namespace Envsan\Modules\Equ\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Equ\Model\EquCheckForm;
use Envsan\Modules\Equ\Util\Constant;
use Phalcon\Mvc\User\Component;

class EquCheckFormService extends Component
{
    public function selectAll()
    {
        $type = $this->request->get('type', 'tstring');
        $name = $this->request->get('name', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.name,
                a.type,
                a.code,
                a.remarks,
                a.update_date,
                a.update_by
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquCheckForm', 'a')
            ->where('a.del_flag = 0 and a.owner = '.SessionData::ownerId())
            ->orderBy('a.id desc');

        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("a.name like ?2", [2 => "%$name%"]);
        }
        if (!CheckUtil::is_empty($type)) {
            $builder->andWhere("a.type = ?3", [3 => $type]);
        }
        return $builder;
    }

    public function setDetail($rows)
    {
        foreach ($rows as &$temp)
        {
            $temp['type_name'] = Constant::$check_form_type[$temp['type']];
        }
        return $rows;
    }

    public function create()
    {
        $row = new EquCheckForm();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = trim($this->request->getPost('name', 'string'));
        $code = trim($this->request->getPost('code', 'string'));
        $type = trim($this->request->getPost('type', 'string'));
        $remarks = trim($this->request->getPost('remarks', 'string'));

        if (empty($name))
            return ErrorHelper::WRONG_INPUT;

        $user = SessionData::user();
        $row->name = $name;
        $row->code = $code;
        $row->type = $type;
        $row->remarks = $remarks;

        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        $row->group_id = $user->group_id;

        if ($act == 'create') {
            $row->owner = $user->owner;
            $row->uid = UUID::make();
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function save($row){
        $data = urldecode($this->request->getPost('data', 'string'));
        $name = $this->request->getPost('name', 'string');
        $remarks = $this->request->getPost('remarks', 'string');
        $type = $this->request->getPost('type', 'string');
        $code = $this->request->getPost('code', 'string');
        if (empty($data)){
            return ErrorHelper::WRONG_INPUT;
        }
        $data = json_decode($data,true);


        $user = SessionData::user();
        $row->name = $name;
        $row->remarks = $remarks;
        $row->type = $type;
        $row->code = $code;
        $row->form_data = json_encode($data,JSON_UNESCAPED_UNICODE);
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return EquCheckForm::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return EquCheckForm::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return true;
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save();
    }

    public function getList($type = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.name,
                a.code
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquCheckForm', 'a')
            ->where('a.del_flag = 0 and a.owner = '.SessionData::ownerId())
            ->orderBy('a.id');

        if (!CheckUtil::is_empty($type)) {
            $builder->andWhere("a.type = ?1", [1 => $type]);
        }
        return $builder->getQuery()->execute();
    }
}