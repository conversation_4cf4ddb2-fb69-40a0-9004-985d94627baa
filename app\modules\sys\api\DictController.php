<?php

namespace Envsan\Modules\Sys\Api;

use Envsan\Common\Base\ApiController;
use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Service\DictService;

/**
 * @skipacl
 */
class DictController extends ApiController
{
    public function viewAction()
    {
        $ret = new JsonData();
        $key = $this->request->get('key', ['string', 'trim']);
        if (!empty($key)) {
            $s = new DictService();
            $ret->data = $s->get($key);
        }
        else {
            $ret->message = ErrorHelper::WRONG_INPUT;
        }

        $ret->emptyIsOk();
        return json_encode($ret);
    }
}