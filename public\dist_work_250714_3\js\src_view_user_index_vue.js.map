{"version": 3, "file": "js/src_view_user_index_vue.js", "mappings": ";;;;;;;;;;;;;AAqDA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AChJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://rrts-manager/src/view/user/index.vue", "webpack://rrts-manager/./src/view/user/index.vue", "webpack://rrts-manager/./src/view/user/index.vue?5328", "webpack://rrts-manager/./src/view/user/index.vue?403a", "webpack://rrts-manager/./src/view/user/index.vue?6112", "webpack://rrts-manager/./src/view/user/index.vue?e9a8", "webpack://rrts-manager/./src/view/user/index.vue?734e", "webpack://rrts-manager/./src/view/user/index.vue?0d9e"], "sourcesContent": ["<template>\r\n    <div>\r\n        <m-header name=\"\" is_back=\"1\"></m-header>\r\n        <m-title name=\"个人信息\"></m-title>\r\n        <van-cell-group>\r\n            <van-cell title=\"登录名\" :value=\"user.login_name\"/>\r\n            <van-cell title=\"姓名\" :value=\"user.real_name\"/>\r\n            <van-cell title=\"电话\" :value=\"user.mobile\"/>\r\n            <van-cell title=\"部门\" :value=\"user.group_name\"/>\r\n            <van-cell title=\"岗位\" :value=\"user.role_name\"/>\r\n            <van-cell is-link @click=\"redirectTo('sign')\">\r\n                <template #title>\r\n                    <div class=\"list-cell-title\">\r\n                        <span class=\"custom-title\">签名</span>\r\n                    </div>\r\n                </template>\r\n            </van-cell>\r\n            <van-cell is-link @click=\"showPwd\">\r\n                <template #title>\r\n                    <div class=\"list-cell-title\">\r\n                        <span class=\"custom-title\">修改密码</span>\r\n                    </div>\r\n                </template>\r\n            </van-cell>\r\n        </van-cell-group>\r\n        <div v-if=\"sign != ''\" style=\"background-color: #fff;padding: 16px;border-bottom: 1px solid #f2f2f2\">\r\n            <van-image :src=\"sign\" width=\"100wh\"></van-image>\r\n        </div>\r\n        <div class=\"footer\">\r\n            <van-button style=\"width: 200px;font-size: 18px\" type=\"danger\" block @click=\"logout\">退出登录</van-button>\r\n        </div>\r\n\r\n        <van-popup\r\n                v-model=\"show_flag\"\r\n                :style=\"{ height: '260px' ,width: '80%' }\"\r\n        >\r\n            <div style=\"padding: 10px;font-size: 16px;text-align: center\">修改密码</div>\r\n            <div>\r\n                <van-cell-group>\r\n                    <van-field v-model=\"pwd\" placeholder=\"请输入原密码\" type=\"password\" label=\"原密码\" />\r\n                    <van-field v-model=\"new_pwd\" placeholder=\"请输入新密码\" type=\"password\" label=\"新密码\" />\r\n                    <van-field v-model=\"new_pwd_confirm\"  placeholder=\"请确认新密码\" type=\"password\" label=\"确认新密码\" />\r\n                </van-cell-group>\r\n            </div>\r\n            <div style=\"display: flex;justify-content: space-around;margin-top: 20px\">\r\n                <van-button style=\"width: 100px\" @click=\"submitChange()\" type=\"info\">提交</van-button>\r\n                <van-button style=\"width: 100px\" @click=\"show_flag=false\" type=\"default\">关闭</van-button>\r\n            </div>\r\n        </van-popup>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: \"index\",\r\n\r\n        data() {\r\n            return {\r\n                user: {},\r\n                sign: '',\r\n                show_flag:false,\r\n                pwd: '',\r\n                new_pwd: '',\r\n                new_pwd_confirm: '',\r\n            };\r\n        },\r\n\r\n        mounted() {\r\n            this.init();\r\n        },\r\n\r\n        methods: {\r\n            init() {\r\n                let that = this;\r\n                this.$http.post('work/user/index').then((rs) => {\r\n                    that.user = rs;\r\n                    if (rs.sign != '' && rs.sign != null) {\r\n                        that.sign = rs.sign;\r\n                    }\r\n                }).catch(() => {\r\n                    that.$router.replace({name: 'error'});\r\n                });\r\n            },\r\n            logout() {\r\n                let that = this;\r\n                this.$http.post('work/login/logout').then((rs) => {\r\n                    if (rs.status === 'ok') {\r\n                        this.$store.commit('CHECK_AUTH', false);\r\n                        that.$router.replace({name: 'login',params: {registration: rs.app_registration_id}});\r\n                        if (window.ReactNativeWebView){\r\n                            window.ReactNativeWebView.postMessage(JSON.stringify({\r\n                                type:'logout'\r\n                            }));\r\n                        }\r\n                    } else {\r\n                        that.$toast.fail('退出失败');\r\n                    }\r\n                }).catch(() => {\r\n                    that.$router.replace({name: 'error'});\r\n                });\r\n            },\r\n            redirectTo(name) {\r\n                this.$router.push({name: name, params: {}});\r\n            },\r\n            showPwd(){\r\n                this.pwd = '';\r\n                this.new_pwd = '';\r\n                this.new_pwd_confirm = '';\r\n                this.show_flag = true;\r\n            },\r\n            submitChange(){\r\n                if (this.pwd == '') {\r\n                    this.$toast.fail('请输入原密码');\r\n                    return;\r\n                }\r\n                if (this.new_pwd == '') {\r\n                    this.$toast.fail('请输入新密码');\r\n                    return;\r\n                }\r\n                if (this.new_pwd_confirm == '') {\r\n                    this.$toast.fail('请输入确认新密码');\r\n                    return;\r\n                }\r\n                if (this.new_pwd != this.new_pwd_confirm) {\r\n                    this.$toast.fail('新密码不一致');\r\n                    return;\r\n                }\r\n                if (this.new_pwd.length < 6){\r\n                    this.$toast.fail('密码不能小于6位');\r\n                    return;\r\n                }\r\n                this.$http.post('work/user/change', {old_pwd: this.pwd, pwd1: this.new_pwd, pwd2: this.new_pwd_confirm}).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.$toast.success('密码修改成功!');\r\n                        this.show_flag = false;\r\n                    } else {\r\n                        this.$toast.fail(rs.message);\r\n                        return;\r\n                    }\r\n                }).catch(() => {\r\n                    this.$router.replace({name: 'error'});\r\n                });\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    .footer {\r\n        margin-top: 50px;\r\n        padding: 16px;\r\n        text-align: center;\r\n        display: flex;\r\n        justify-content: center;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('m-header',{attrs:{\"name\":\"\",\"is_back\":\"1\"}}),_c('m-title',{attrs:{\"name\":\"个人信息\"}}),_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"登录名\",\"value\":_vm.user.login_name}}),_c('van-cell',{attrs:{\"title\":\"姓名\",\"value\":_vm.user.real_name}}),_c('van-cell',{attrs:{\"title\":\"电话\",\"value\":_vm.user.mobile}}),_c('van-cell',{attrs:{\"title\":\"部门\",\"value\":_vm.user.group_name}}),_c('van-cell',{attrs:{\"title\":\"岗位\",\"value\":_vm.user.role_name}}),_c('van-cell',{attrs:{\"is-link\":\"\"},on:{\"click\":function($event){return _vm.redirectTo('sign')}},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"list-cell-title\"},[_c('span',{staticClass:\"custom-title\"},[_vm._v(\"签名\")])])]},proxy:true}])}),_c('van-cell',{attrs:{\"is-link\":\"\"},on:{\"click\":_vm.showPwd},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"list-cell-title\"},[_c('span',{staticClass:\"custom-title\"},[_vm._v(\"修改密码\")])])]},proxy:true}])})],1),(_vm.sign != '')?_c('div',{staticStyle:{\"background-color\":\"#fff\",\"padding\":\"16px\",\"border-bottom\":\"1px solid #f2f2f2\"}},[_c('van-image',{attrs:{\"src\":_vm.sign,\"width\":\"100wh\"}})],1):_vm._e(),_c('div',{staticClass:\"footer\"},[_c('van-button',{staticStyle:{\"width\":\"200px\",\"font-size\":\"18px\"},attrs:{\"type\":\"danger\",\"block\":\"\"},on:{\"click\":_vm.logout}},[_vm._v(\"退出登录\")])],1),_c('van-popup',{style:({ height: '260px' ,width: '80%' }),model:{value:(_vm.show_flag),callback:function ($$v) {_vm.show_flag=$$v},expression:\"show_flag\"}},[_c('div',{staticStyle:{\"padding\":\"10px\",\"font-size\":\"16px\",\"text-align\":\"center\"}},[_vm._v(\"修改密码\")]),_c('div',[_c('van-cell-group',[_c('van-field',{attrs:{\"placeholder\":\"请输入原密码\",\"type\":\"password\",\"label\":\"原密码\"},model:{value:(_vm.pwd),callback:function ($$v) {_vm.pwd=$$v},expression:\"pwd\"}}),_c('van-field',{attrs:{\"placeholder\":\"请输入新密码\",\"type\":\"password\",\"label\":\"新密码\"},model:{value:(_vm.new_pwd),callback:function ($$v) {_vm.new_pwd=$$v},expression:\"new_pwd\"}}),_c('van-field',{attrs:{\"placeholder\":\"请确认新密码\",\"type\":\"password\",\"label\":\"确认新密码\"},model:{value:(_vm.new_pwd_confirm),callback:function ($$v) {_vm.new_pwd_confirm=$$v},expression:\"new_pwd_confirm\"}})],1)],1),_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-around\",\"margin-top\":\"20px\"}},[_c('van-button',{staticStyle:{\"width\":\"100px\"},attrs:{\"type\":\"info\"},on:{\"click\":function($event){return _vm.submitChange()}}},[_vm._v(\"提交\")]),_c('van-button',{staticStyle:{\"width\":\"100px\"},attrs:{\"type\":\"default\"},on:{\"click\":function($event){_vm.show_flag=false}}},[_vm._v(\"关闭\")])],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.footer[data-v-2648786f] {\\n    margin-top: 50px;\\n    padding: 16px;\\n    text-align: center;\\n    display: flex;\\n    justify-content: center;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2648786f&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"446f8093\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2648786f&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2648786f&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2648786f&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2648786f&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2648786f\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2648786f')) {\n      api.createRecord('2648786f', component.options)\n    } else {\n      api.reload('2648786f', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=2648786f&scoped=true\", function () {\n      api.rerender('2648786f', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/user/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2648786f&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=2648786f&scoped=true\""], "names": [], "sourceRoot": ""}