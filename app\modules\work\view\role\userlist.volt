{% do assets.collection('css').addCss('static/pages/css/search.css') %}
<div class="page-content">
    <h3 class="page-title">发起权限设定</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">姓名</label>
                            <div class="col-md-9">
                                <input name="name" type="text" class="form-control" v-model="name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">部门</label>
                            <div class="col-md-9">
                                <select name="group_id" v-model="group_id" class="form-control bs-select" data-live-search="true" data-size="8">
                                    <option value="">全部</option>
                                    {% for row in groupList %}
                                        <option value="{{ row.id }}">{{row.name}}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('work/role/userlist/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="real_name">姓名</th>
                    <th data-field="group_name">组织</th>
                    <th data-field="role_name">权限</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
<div id="act" style="display: none;">
    <div class="btn-group">
        <div class="btn-group">
            <button type="button" class="btn btn-primary" onclick="setting('@id@')">设定</button>
        </div>
    </div>
</div>
<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            name: '',
            group_id: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.name = this.name;
                p.group_id = this.group_id;
                return p;
            },
            reset: function() {
                this.name = '';
                this.group_id = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });


    $table.bootstrapTable();
    var actHtml = $('#act').html();

    function actionFormatter(v, row, idx) {
        return actHtml.replace(/@id@/g, row.uid);
    }

    function setting(uid) {
        top.window.layer_result='';
        top.layer.open({
            title : '设定权限',
            type : 2,
            area : makeArea('40em', '80%'),
            content : '{{ url('work/role/user') }}/'+uid,
            end : function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }
</script>
