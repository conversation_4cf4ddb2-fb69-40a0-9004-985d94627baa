<?php
namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

use Envsan\Common\Util\ModelUtil;

class Role extends BaseModel
{
    const SCOPE_ROLE = 0;
    const SCOPE_USER = 1;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=4, nullable=false)
     */
    public $scope;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $identity;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $home_page_id;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $auth_data_ids;//ADD RC-LQ-968 20240220 BY WXX

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $auth_data_names;//ADD RC-LQ-968 20240220 BY WXX

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $create_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=false)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_role';
    }

    public static function find($params = null)
    {
        return parent::find(ModelUtil::build($params));
    }

    public static function findFirst($params = null)
    {
        return parent::findFirst(ModelUtil::build($params));
    }

    public static function findFirstDirect($params = null)
    {
        return parent::findFirst($params);
    }

    public function initialize()
    {
        $this->hasMany('id', 'Envsan\Modules\Sys\Model\User', 'role_id', array('alias' => 'User'));
        $this->hasMany('id', 'Envsan\Modules\Sys\Model\RoleRes', 'role_id', array('alias' => 'RoleRes'));
        $this->belongsTo('group_id', 'Envsan\Modules\Sys\Model\Group', 'id', array('alias' => 'Group'));

        ModelUtil::softDelete($this);
    }

    public function beforeDelete()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }

    public function beforeSave()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }
}
