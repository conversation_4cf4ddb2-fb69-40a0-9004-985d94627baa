{% do assets.collection('css').addCss('static/pages/css/search.css') %}
<?php
use Env<PERSON>\Modules\Sys\Model\Role;
?>
<div class="page-content">
    <h3 class="page-title">岗位管理</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">名称</label>
                            <div class="col-md-9">
                                <input name="name" type="text" class="form-control" v-model="name"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-offset-8 col-md-4 col-lg-offset-9 col-lg-3" style="text-align: right;">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('sys/role/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="name">名称</th>
                    <th data-field="group_name">部门</th>
                    <th data-field="home_page_id" data-formatter="pageFormatter">首页</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
<div id="act" style="display: none;">
    <div class="btn-group">
        <div class="btn-group">
            <button type="button" class="btn btn-primary" onclick="viewres('@id@')">查看权限</button>
            <button type="button" class="btn btn-primary" onclick="manage('@id@')">分配权限</button>
        </div>

        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                操作 <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-pencil"></i> 编辑</a></li>
                <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-times"></i> 删除</a></li>
            </ul>
        </div>
    </div>
</div>
<script>
    var pageList = {{ jsonPageList }};
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            name: ''
        },
        methods: {
            search: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function (p) {
                p.name = this.name;
                return p;
            },
            reset: function () {
                this.name = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });


    $table.bootstrapTable();
    var actHtml = $('#act').html();

    function actionFormatter(v, row, idx) {
        return actHtml.replace(/@id@/g, row.uid);
    }

    function pageFormatter(v) {
       if (v == '' || v == null){
           return '';
       } else {
           for (var j = 0 ; j <pageList.length;j++){
               if (pageList[j].id == v){
                   return pageList[j].name;
               }
           }
       }
       return '';
    }

    function create() {
        top.window.layer_result='';
        top.layer.open({
            title : '新建',
            type : 2,
            area : makeArea('40em', '60%'),
            content: '{{ url('sys/role/create') }}',
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(uid) {
        top.window.layer_result='';
        top.layer.open({
            title : '编辑',
            type : 2,
            area : makeArea('40em', '60%'),
            content : '{{ url('sys/role/edit/') }}' + uid,
            end : function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function del(uid){
        var dlg = top.layer.confirm('确认删除吗?', function(){
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('sys/role/delete') }}", {uid:uid}, function (rs) {
                closeSpin(null);
                if(rs.status=='ok'){
                    toastr.success('操作成功!');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败!'+rs.message);
                }
            })
        });
    }

    function manage(uid) {
        top.window.layer_result='';
        top.layer.open({
            title : '分配权限',
            type : 2,
            area : makeArea('40em', '80%'),
            content : '{{ url('sys/res/sel') }}/'+uid+'/edit',
            end : function(){
                if(top.window.layer_result=='ok'){
                    var ids = top.window.layer_data;
                    showSpin();
                    $.post("{{ url('sys/role/assign') }}", {uid:uid, ids:ids.toString()}, function (rs) {
                        closeSpin(null);
                        if(rs.status=='ok'){
                            toastr.success('操作成功!');
                            $table.bootstrapTable('refresh');
                        }
                        else {
                            toastr.error('操作失败!'+rs.message);
                        }
                    })
                }
            }
        });
    }

    function viewres(uid) {
        top.layer.open({
            title : '查看权限',
            type : 2,
            area : makeArea('40em', '80%'),
            content : '{{ url('sys/res/sel') }}/'+uid+'/view'
        });
    }

    function scope(uid) {
        top.layer.open({
            title : '数据范围',
            type : 2,
            area : makeArea('40em', '90%'),
            content : '{{ url('sys/role/scope') }}/'+uid
        });
    }
</script>
