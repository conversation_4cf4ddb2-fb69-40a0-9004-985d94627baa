<?php
namespace Envsan\Modules\{{Module}}\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\{{Module}}\Model\{{Model}};
use Envsan\Modules\{{Module}}\Service\{{Model}}Service;

class {{ModelWithoutModule}}Controller extends SuperController
{
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new {{Model}}Service();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link': '{{module}}:{{model}}:list'})
     */
    public function createAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new {{Model}}Service();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $jrow = (new {{Model}}())->toArray();
        $this->view->json{{Model}} = json_encode($jrow);
    }

    /**
     * @acl({'link': '{{module}}:{{model}}:list'})
     */
    public function editAction($uid = '')
    {
        $s = new {{Model}}Service();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $jrow = $row->toArray();
        $this->view->json{{Model}} = json_encode($jrow);

        $this->view->uid = $row->uid;
        $this->view->pick('{{model}}/create');
    }

    /**
     * @acl({'link': '{{module}}:{{model}}:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new {{Model}}Service();
            $ret = new JsonData();
            if ($rs->deleteByUid($this->request->getPost('uid', 'tstring')))
                $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}