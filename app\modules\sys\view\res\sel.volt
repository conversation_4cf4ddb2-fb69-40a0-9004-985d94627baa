<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}


{#
    <!-- Default box -->#}{#
    <div class="box" id="app">#}{#
    <div id="app" class="page-content">
        <div class="portlet-body">
            <div id="tree">213</div>
        </div>
        {% if action!='view' %}
            <div class="form-actions right">
                <div class="btn-bar">
                    <button class="btn btn-primary" @click="confirm"><i class="fa fa-check"></i> 确认</button>
                </div>
            </div>
        {% endif %}
    </div>#}

<section class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <div class="form-body">
                <div id="tree"></div>
            </div>
            {% if action!='view' %}
                <div class="form-actions right" id="app">
                    <button type="button" class="btn green" @click="confirm"><i class="fa fa-check"></i> 确认</button>
                </div>
            {% endif %}
        </div>
    </div>



<script>
    {% if action!='view' %}
    var app = new Vue({
        el:'#app',
        methods:{
            _confirm : function (ids) {
                parent.window.layer_result = 'ok';
                parent.window.layer_data = ids;
                parent.layer.close(parent.layer.getFrameIndex(window.name));
            },
            confirm : function () {
                var ids=$tree.jstree('get_bottom_selected');
                if(ids==null || ids.length<=0)
                    layer.confirm('您尚未选择任何权限，这可能导致创建一个空白角色或者现有角色的所有权限都被删除，确认继续吗?', this._confirm(ids));
                else
                    this._confirm(ids);
            }
        }
    });
    {% endif %}

    var $tree = $('#tree');
    $(document).ready(function () {
        $tree.jstree({
            'plugins':['wholerow', 'checkbox'],
            'core': {'data': {{ jsonTree }}}
        }).on('loaded.jstree', function() {
            {% if nodes is not empty %}
                $tree.jstree('select_node', {{ nodes }});
            {% endif %}
        });
    });
</script>
