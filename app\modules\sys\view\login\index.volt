<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ owner.short_name }}</title>
    <link rel="shortcut icon" href="{{ static_url('favicon.ico') }}" />
    <script>if (window.top !== window.self) {window.top.location = window.location;}</script>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/toastr/toastr.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/font-awesome-4.7.0/css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/simple-line-icons/simple-line-icons.min.css') }}"/>
    <link rel="stylesheet" href="{{ static_url('static/global/css/components.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/layouts/layout/css/layout.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/layouts/layout/css/themes/darkblue.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/pages/css/login.css') }}">
</head>
<body>
<div class="login-main">
    <div class="login-box login-img-box">
        <img src="{{ static_url('static/pages/img/login/platform/img.png') }}" style="height: 100%;">
        <div class="login-content">
            <div class="login-header" style="display: flex;">
                <div style="display: flex;flex-direction: column;align-items: center;">
                    <img src="{{ static_url('static/pages/img/login/platform/logo.png') }}" style="width: 80px;">
                    <div class="login-title logo-text" style="font-size: 26px;margin-top: 5px;">新钢液压</div>
                </div>
            </div>
            <div class="login-footer">
                <div class="login-title">
                    XIN GANG YE YA<span style="margin: 0 10px;">·</span>SMART FACTORY SYSTEM
                </div>
            </div>
        </div>
    </div>
    <div class="login-box login-form-box">
        <div class="decorate"></div>
        <div id="app" class="login-content">
            <div class="login-title">用户登录</div>
            <form class="login-form" autocomplete="off">
                <div class="form-row">
                    <div class="form-img">
                        <img src="{{ static_url('static/pages/img/login/platform/icon_user.png') }}">
                    </div>
                    <input type="text" id="txt_name" placeholder="请输入账号" name="name" v-model="name" maxlength="30">
                </div>
                <div class="form-row">
                    <div class="form-img">
                        <img src="{{ static_url('static/pages/img/login/platform/icon_pwd.png') }}">
                    </div>
                    <input type="password" placeholder="请输入密码" name="pwd" v-model="pwd" maxlength="30">
                </div>
                <div class="btn-row">
                    <button type="button" class="btn btn-login" @click="login">登录</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% include "include/loader.volt" %}

<script src="{{ static_url('static/global/plugins/jquery/jquery-2.2.3.min.js') }}"></script>
<script src="{{ static_url('static/global/plugins/bootstrap/js/bootstrap.min.js') }}"></script>
<script src="{{ static_url('static/global/plugins/toastr/toastr.min.js') }}"></script>
<script src="{{ static_url('static/global/plugins/vue/vue.js') }}"></script>
<script src="{{ static_url('static/global/plugins/jquery.blockui.min.js') }}"></script>
<script src="{{ static_url('static/global/plugins/backstretch/jquery.backstretch.min.js') }}"></script>
<script src="{{ static_url('static/global/js/app.js') }}"></script>
<script src="{{ static_url('static/global/js/custom.js') }}"></script>

{{ assets.outputJs('validate') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            name: '',
            pwd: '',
            csrf: '{{ security.getToken() }}'
        },
        methods: {
            login: function(e) {
                e.preventDefault();

                if (!this.name) {
                    toastr.error("请输入用户名");
                    return;
                }
                if (!this.pwd) {
                    toastr.error("请输入密码");
                    return;
                }

//                    showSpinCustom();
                showSpin();
                $.post("{{ url('sys/login/index') }}", app.$data, function (rs) {
//                        closeSpinCustom();
                    closeSpin();
                    if (rs.status == 'ok') {
                        toastr.success('成功登录，正在跳转!');
                        setTimeout(function () {
                            location.href = "{{ url('sys') }}";
                        }, 500);
                    }
                    else {
                        toastr.error(rs.message);
                        app.csrf = rs.csrf; //刷新csrf的token
                    }
                }).error(function(rs) {
                    closeSpin();
                    console.log(rs);
                });
            }
        }
    });

    $('.login-form input').keypress(function(e) {
        if (e.which == 13) {
            app.login(e);
        }
    });

    function updateSize() {
        $(".login-panel-white").width(460 * $(".login-content").height() * 0.95 / 699);
    }

    $(function () {
        updateSize();
    });

    $(window).resize(function () {
        updateSize();
    });
</script>
</body>
</html>
