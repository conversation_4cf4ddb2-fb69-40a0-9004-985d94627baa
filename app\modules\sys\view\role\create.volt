{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    名称<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="name" class="form-control" v-model="name" required/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    部门<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="group" v-model="group_name" required readonly>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default btn-flat" @click="selectGroup"><i class="fa fa-search"></i></button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    限制<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="scope" v-model="scope">  选中时数据权限为用户所属组织数据，否则岗位所属部门数据
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" V-if="scope == 0">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">
                                    数据权限
                                </label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <textarea rows="2" class="form-control" maxlength="1000" name="auth_data_names" v-model="auth_data_names" readonly></textarea>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default btn-flat" @click="selectAuth"><i class="fa fa-search"></i></button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    首页页面<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <select id="customer" class="form-control" name="home_page_id" v-model="home_page_id">
                                        <option value="0">请选择</option>
                                        {% for c in pageList %}
                                            <option value="{{c['id']}}">{{ c['name'] }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonRole }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName()=='edit' %}
                var url= '{{ url('sys/role/edit/'~role.uid) }}';
                {% else %}
                var url= '{{ url('sys/role/create') }}';
                {% endif %}

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            },
            selectGroup : function () {
                top.window.group_layer_result=null;
                top.layer.open({
                    title:'选择组织',
                    type: 2,
                    area: ['35em', '60%'],
                    content: '{{ url('sys/group/sel') }}',
                    end:function(){
                        if(top.window.group_layer_result!=null){
                            var rs = top.window.group_layer_result;
                            app.group_name = rs.name;
                            app.group_uid = rs.uid;
                        }
                    }
                });
            },
            //ADD RC-LQ-968 20240220 BY WXX
            selectAuth:function () {
                top.window.layer_result = null;
                top.layer.open({
                    title: '数据权限',
                    type: 2,
                    area: ['50em', '80%'],
                    content: '{{ url('sys/user/sel-multiple/') }}'+app.auth_data_ids,
                    end: function() {
                        if (top.window.layer_result == 'ok') {
                            app.auth_data_names = top.window.result_names;
                            app.auth_data_ids = top.window.result_ids;
                        }
                    }
                });
            }
        }
    })
</script>
