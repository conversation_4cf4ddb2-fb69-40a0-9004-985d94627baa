{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{{ assets.outputJs('validate') }}




<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">采购入库</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 72vh;overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                {% if dispatcher.getActionName() == 'edit' %}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>入库单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>到货单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="receipt_code" v-model="receipt_code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>采购到货单</label>
                                            <div class="col-sm-8">
                                                <select class="bs-select form-control" name="receipt_id" v-model="receipt_id"  data-live-search="true" data-size="8" required>
                                                    <option value="">请选择采购到货单</option>
                                                    {% for item in receiptList %}
                                                        <option value="{{ item.id }}">{{ item.receipt_code }} / {{ item.supplier_name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>入库日期</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker-ext" placeholder="请输入入库日期" name="instock_date" v-model="instock_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>采购订单</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>供应商</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right" style="padding-bottom: 5px;">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" @click="submit" class="btn btn-primary">提交入库</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">采购入库明细</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                <thead style="position: sticky; top: 0; background-color: #f9f9f9; z-index: 10;">
                                <tr>
                                    <th style="border-top: none;">编码/名称</th>
                                    <th style="border-top: none;">规格型号</th>
                                    <th style="width: 190px; border-top: none;">数量</th>
                                    <th style="border-top: none;">采购数量</th>
                                    <th style="border-top: none;">无税单价</th>
                                    <th style="border-top: none;">含税单价</th>
                                    <th style="border-top: none;">无税总价</th>
                                    <th style="border-top: none;">含税总价</th>
                                    <th style="border-top: none;">是否质检</th>
                                    <th style="border-top: none;">质检状态</th>
                                    <th style="border-top: none;">不良品处置</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <tr v-if="detail_data.length == 0">
                                        <td colspan="7" style="text-align: center;">请选择采购到货单</td>
                                    </tr>
                                    <tr v-for="row, index in detail_data">
                                        <td>
                                            <span v-text="row.goods_code"></span>
                                            <br>
                                            <span v-text="row.goods_name"></span>
                                        </td>
                                        <td>
                                            <span v-text="row.goods_model"></span>
                                        </td>
                                        
                                        <td>
                                            <div class="input-group">
                                                <input type="number" class="form-control" :name="'order_quantity' + index" v-model="row.order_quantity" placeholder="数量" number="true" readonly>
                                                <span class="input-group-addon" v-text="row.goods_deputy_unit"></span>
                                            </div>
                                        </td>
                                        <td v-text="(row.order_purchase_quantity || 0) + '(' + row.goods_unit + ')'"></td>
                                        <td >
                                            <div class="input-group">
                                                <input type="number" class="form-control" :name="'price' + index" v-model="row.price" placeholder="未税单价"  @keyup="sumMoney" @input="onPriceChange(row)" number="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" class="form-control" :name="'price_hs' + index" v-model="row.price_hs" placeholder="含税单价"  @keyup="sumMoney" @input="onPriceHsChange(row)"  number="true">
                                            </div>
                                        </td>
                                        <td v-text="row.total_amount"></td>
                                        <td v-text="row.total_amount_hs"></td>
                                        <td>${ row.check_flag === '0' ? '否' : '是' }</td>
                                        <td v-if="row.check_user_id"> ${ row.check_result_flag === '1' ? 'NG' : 'OK' }</td>
                                        <td v-else></td>
                                        <td v-text="row.handling_method_name"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="dnd_purchase_instock" style="display: none"></div>
</div>

{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('uploader_pic') }}
{{ partial('common_utils') }}
<script>
    var detail_index = -1;
    var app = new Vue({
        el: '#app',
        data: {{ jsonInstock }},
        created() {
            if (this.receipt_id) {
                this.receipt_id = '';
            }
        },
        methods: {
            onPriceChange(row) {
                if (row.price !== '' && row.price !== null) {
                    const priceValue = Number(row.price) || 0;
                    const rateValue = Number(row.tax_rate) || 0;
                    row.price_hs = (priceValue * (1 + rateValue)).toFixed(2);
                }
            },
            onPriceHsChange(row) {
                if (row.price_hs !== '' && row.price_hs !== null) {
                    const priceHsValue = Number(row.price_hs) || 0;
                    const rateValue = Number(row.tax_rate) || 0;
                    if (rateValue === 0) {
                        this.price = priceHsValue.toFixed(2);
                    } else {
                        row.price = (priceHsValue / (1 + rateValue)).toFixed(2);
                    }
                }
            },
            sumMoney() {
                for (let item of this.detail_data) {
                    let total_money = '';        // 不含税总价
                    let total_money_hs = '';     // 含税总价
                    
                    // 获取并验证数量、单价
                    const quantity = item.order_purchase_quantity;
                    const price = item.price;           // 不含税单价
                    const tax_rate = item.tax_rate;     // 税率（如：17表示17%）
                    
                    // 检查必要字段是否有效
                    if (isDecimal(quantity) && isDecimal(price)) {
                        const qty = Number(quantity);
                        const unitPrice = Number(price);
                        
                        // 计算不含税总价
                        total_money = Number((qty * unitPrice).toFixed(4));
                        
                        // 处理税率：空值当作0处理
                        let taxRateValue = 0;
                        if (isDecimal(tax_rate)) {
                            taxRateValue = Number(tax_rate);
                        }
                        // 如果tax_rate为空、null、undefined、''等，taxRateValue保持为0
                        
                        // 计算含税总价
                        total_money_hs = Number((qty * unitPrice * (1 + taxRateValue)).toFixed(4));
                    }
                    // 如果数量或单价无效，则保持空字符串
                    
                    // 设置计算结果
                    this.$set(item, 'total_amount', total_money);
                    this.$set(item, 'total_amount_hs', total_money_hs);
                }
            },
            save(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(1);
            },
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(2);
            },
            saveData(type){
                if (this.detail_data.length == 0){
                    toastr.error('请添加入库明细');
                    return;
                }

                for (let i = 0; i < this.detail_data.length; i++) {
                    let row = this.detail_data[i];
                    if (!isDecimal(row.order_quantity) || row.order_quantity <= 0) {
                        toastr.error('请输入有效的数量，大于0');
                        return;
                    }

                    if (!isDecimal(row.price)) {
                        toastr.error('请输入有效的未税单价');
                        return;
                    }

                    if (!isDecimal(row.price_hs)) {
                        toastr.error('请输入有效的含税单价');
                        return;
                    }
                }

                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('purchase/instock/edit/' ~ uid) }}';
                {% else %}
                var url= '{{ url('purchase/instock/create') }}';
                {% endif %}

                commonAjaxRequest(url, {
                   type:type,
                   instock_date:app.instock_date,
                   receipt_id:app.receipt_id,
                   supplier_id:app.supplier_id,
                   order_id:app.order_id,
                   remarks:app.remarks,
                   files:encodeURI(JSON.stringify(app.files)),
                   ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                   detail : encodeURI(JSON.stringify(app.detail_data)).replace(/\+/g,'%2B')
                }, function(rs) {
                    // 成功回调
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                });

            },
            delFile:function (index) {
                this.files.splice(index);
            },
            uploadImg(index) {
                detail_index = index;
                uploadPic();
            },
            delImg(index, img_idx, e) {
                e.preventDefault();
                this.detail_data[index].images.splice(img_idx, 1);
            },
            clearData() {
                this.supplier_name = '';
                this.order_code = '';
                this.receipt_code = '';
                this.order_id = '';
                this.supplier_id = '';
            }
        },
        watch:{
            receipt_id:function (val) {
                if (val == ''){
                    this.detail_data = [];
                    this.clearData();
                    return;
                }
                var url= '{{ url('purchase/instock/receipt/') }}'+ val;
                commonAjaxRequest(url, {
                   type:type,
                   instock_date:app.instock_date,
                   receipt_id:app.receipt_id,
                   supplier_id:app.supplier_id,
                   order_id:app.order_id,
                   remarks:app.remarks,
                   files:encodeURI(JSON.stringify(app.files)),
                   ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                   detail : encodeURI(JSON.stringify(app.detail_data)).replace(/\+/g,'%2B')
                }, function(rs) {
                    // 直接使用返回的数据，后台已经过滤了合格的物料
                    app.detail_data = rs.data.rows;
                    if (rs.data.headerInfo) {
                        app.supplier_name = rs.data.headerInfo.supplier_name;
                        app.order_code = rs.data.headerInfo.order_code;
                        app.receipt_code = rs.data.headerInfo.receipt_code;
                        app.order_id = rs.data.headerInfo.order_id;
                        app.supplier_id = rs.data.headerInfo.supplier_id;
                    } else {
                        app.clearData();
                    }
                }, function(){
                    app.detail_data = [];
                });
            }
        }
    });

    initUpLoaderPdf('purchase_instock');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key: getUuid(),
                url_name: rs.file_name,
                url: rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    initPicUpLoader('purchase_instock');
    function uploadPicSuccess(rs) {
        app.detail_data[detail_index].images.push(rs.file_name);
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}
<style>
    .lightbox-a {
        position: relative;
    }

    .btn-img-del {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 15px;
        height: 15px;
        border-radius: 100%;
        background-color: #e43a45;
        color: #FFFFFF;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .btn-img-del:hover {
        background-color: #cf1c28;
    }

    .btn-img-del i {
        font-size: 10px;
    }
</style>