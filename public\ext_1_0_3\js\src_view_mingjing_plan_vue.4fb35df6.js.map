{"version": 3, "file": "js/src_view_mingjing_plan_vue.4fb35df6.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAmZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AChzBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/mingjing/plan.vue", "webpack://sfp_ext/./src/view/mingjing/plan.vue", "webpack://sfp_ext/./node_modules/core-js/modules/es.iterator.find.js", "webpack://sfp_ext/./src/view/mingjing/plan.vue?1a42", "webpack://sfp_ext/./src/view/mingjing/plan.vue?d96b", "webpack://sfp_ext/./src/view/mingjing/plan.vue?63de", "webpack://sfp_ext/./src/view/mingjing/plan.vue?a1b9", "webpack://sfp_ext/./src/view/mingjing/plan.vue?56f0", "webpack://sfp_ext/./src/view/mingjing/plan.vue?9cc7"], "sourcesContent": ["<template>\r\n    <div class=\"form-group form-group-lg panel panel-default\">\r\n        <div class=\"panel-heading\">\r\n            <h3 class=\"panel-title\">排产计划</h3>\r\n        </div>\r\n        <div>\r\n            <el-descriptions class=\"margin-top\" :column=\"4\" border>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                       批次号\r\n                    </template>\r\n                    <span v-text=\"row.code\"></span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                        客户\r\n                    </template>\r\n                    <span v-text=\"row.customer_name\"></span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                        产品编号\r\n                    </template>\r\n                    <span v-text=\"row.product_code\"></span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                      型号规格\r\n                    </template>\r\n                    <span v-text=\"row.product_name\"></span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                        生产数量\r\n                    </template>\r\n                    <span v-text=\"row.quantity\"></span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                        计划开始日\r\n                    </template>\r\n                    <span v-text=\"row.plan_begin_date\"></span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                        计划完成日\r\n                    </template>\r\n                    <span v-text=\"row.plan_end_date\"></span>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template slot=\"label\">\r\n                        备注\r\n                    </template>\r\n                    <span v-text=\"row.remarks\"></span>\r\n                </el-descriptions-item>\r\n            </el-descriptions>\r\n        </div>\r\n        <div style=\"display: flex;\">\r\n            <div style=\"width: 17vw;border-right: 1px solid #f2f2f2;height: 83vh;overflow-y: auto;padding: 0 5px\">\r\n                <div v-for=\"(bom_item , bom_idx) in bom_list\" :key=\"bom_idx\" :style=\"{border:sel_bom_item.id ==  bom_item.id ?  ' 1px solid #208FFF' :  ' 1px solid #e2e2e2',margin:'10px 5px'}\">\r\n                    <div :style=\"{padding:'5px',backgroundColor:sel_bom_item.id ==  bom_item.id ? '#ECF5FF' : '#f2f2f2'}\" >\r\n                        <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;\">\r\n                            <div style=\"line-height: 30px;\">\r\n                                <span v-text=\"bom_item.bom_code\"></span> &nbsp;\r\n                                <span v-text=\"bom_item.name\"></span> &nbsp;\r\n                                <el-tag  v-if=\"bom_item.entrust_id != null\" size=\"small\" type=\"warning\" effect=\"dark\">委外</el-tag>\r\n                            </div>\r\n                            <el-button :type=\"sel_bom_item.id == bom_item.id ? 'primary' : 'default'\" @click=\"selPlanData(bom_item)\" size=\"mini\">排产</el-button>\r\n                        </div>\r\n                        <div v-if=\"bom_item.entrust_id == null\" style=\"text-align: right;\">\r\n                            <el-button type=\"warning\" @click=\"showTempEntrust(bom_item)\" size=\"mini\">临时外委</el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div style=\"min-height: 50px\">\r\n                        <div v-if=\"bom_item.warning_flag == 1\" style=\"display: flex;justify-content: flex-end\">\r\n                            <div style=\"padding: 2px;background-color: red;width: 80px;color: white;text-align: center;\">排产预警</div>\r\n                        </div>\r\n                        <div style=\"display: flex\">\r\n                            <div style=\"display: flex;width: 50%;padding: 5px\">\r\n                                <div style=\"width: 40%\">\r\n                                    <span>开始：</span>\r\n                                </div>\r\n                                <div>\r\n                                    <span v-text=\"bom_item.start_date\"></span>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"display: flex;width: 50%;padding: 5px\">\r\n                                <div style=\"width: 40%\">\r\n                                    <span>完成：</span>\r\n                                </div>\r\n                                <div>\r\n                                    <span v-text=\"bom_item.end_date\"></span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"display: flex\">\r\n                            <div style=\"display: flex;width: 50%;padding: 5px\">\r\n                                <div style=\"width: 60%\">\r\n                                    <span>完成数量：</span>\r\n                                </div>\r\n                                <div>\r\n                                    <span v-text=\"bom_item.finish_cnt\"></span>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"display: flex;width: 50%;padding: 5px\">\r\n                                <div style=\"width: 60%\">\r\n                                    <span>不良数量：</span>\r\n                                </div>\r\n                                <div>\r\n                                    <span v-text=\"bom_item.error_cnt\"></span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"display: flex\">\r\n                            <div style=\"display: flex;width: 50%;padding: 5px\">\r\n                                <div style=\"width: 60%\">\r\n                                    <span>排产数量：</span>\r\n                                </div>\r\n                                <div>\r\n                                    <span v-text=\"bom_item.plan_cnt\"></span>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"display: flex;width: 50%;padding: 5px\">\r\n                                <div style=\"width: 60%\">\r\n                                    <span>待排数量：</span>\r\n                                </div>\r\n                                <div>\r\n                                    <span v-text=\"bom_item.un_plan_cnt\"></span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div style=\"width: 83vw\">\r\n                <div style=\"display: flex;padding: 5px\">\r\n                    <div style=\"width: 230px;line-height: 30px\">\r\n                        按设备使用优选级查找可排产设备:\r\n                    </div>\r\n                    <el-radio-group v-model=\"equ_level\" size=\"mini\" @change=\"changeEquLevel\">\r\n                        <el-radio-button :label=\"1\">优先级1</el-radio-button>\r\n                        <el-radio-button :label=\"2\">优先级2</el-radio-button>\r\n                        <el-radio-button :label=\"3\">优先级3</el-radio-button>\r\n                    </el-radio-group>\r\n                </div>\r\n                <div style=\"display: flex;flex-direction: row\">\r\n                    <div>\r\n                        <div class=\"plan-header\" style=\"width: 120px\">\r\n                        </div>\r\n                        <div v-for=\"(equ_item,equ_idx) in equ_list\" :key=\"equ_idx\">\r\n                            <div class=\"plan-item\" style=\"background-color: #f2f2f2;font-size: 12px;font-weight: 600;padding: 3px;width: 120px\">\r\n                                <div><span v-text=\"equ_item.code\"></span></div>\r\n                                <div><span v-text=\"equ_item.name\"></span></div>\r\n                                <div><span v-text=\"equ_item.type_name\"></span></div>\r\n                                <div>\r\n                                    <div v-if=\"equ_item.status_name == ''\"></div>\r\n                                    <el-tag v-else-if=\"equ_item.status_name == '' || equ_item.status_name == '在用'\" size=\"small\" type=\"success\" effect=\"dark\">{{equ_item.status_name}}</el-tag>\r\n                                    <el-tag v-else size=\"small\" type=\"danger\" effect=\"dark\">{{equ_item.status_name}}</el-tag>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div style=\"flex: 1;overflow-x: auto\">\r\n                        <div style=\"display: flex;flex-direction: row\">\r\n                            <div v-for=\"(day_item,day_idx) in day_list\" :key=\"day_idx\" class=\"plan-header\">\r\n                                <span v-text=\"day_item.date_show + ' ' + day_item.week\"></span>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row\" v-for=\"(equ_item,equ_idx) in equ_list\" :key=\"equ_idx\">\r\n                            <div class=\"plan-item\" v-for=\"(day_item,day_idx) in equ_item.day_list\" :key=\"day_idx\">\r\n                                <template v-if=\"sel_bom_item.plan_type == 1\">\r\n                                    <div style=\"display: flex;justify-content: space-between;background-color: #E2E2E2;padding: 2px\">\r\n                                        <div>\r\n                                            <span v-text=\"day_item.hour + '  (H)'\"></span>\r\n                                        </div>\r\n                                        <div>\r\n                                            <a @click=\"addPlan(equ_item,day_item)\"><i style=\"font-size: 20px\" class=\"el-icon-circle-plus-outline\"></i></a>\r\n                                        </div>\r\n                                    </div>\r\n                                    <draggable tag=\"div\" :list=\"day_item.list\" group=\"plan_gl\" handle=\".handle\" @change=\"planChange(equ_idx,day_idx,arguments[0])\" style=\"min-height: 80px\">\r\n                                        <div v-for=\"(plan_item,plan_idx) in day_item.list\" :key=\"plan_idx\" :style=\"{display:'flex',lineHeight:'20px',backgroundColor: row.id == plan_item.notice_detail_id && sel_bom_item.id == plan_item.bom_id ? '#ECF5FF' :'#fff',justifyContent:'space-between',padding:' 0 2px',marginBottom:'1px'}\">\r\n                                            <div><i class=\"fa fa-align-justify handle\" style=\"margin-right: 10px\"></i></div>\r\n                                            <el-tooltip placement=\"top\" effect=\"light\">\r\n                                                <div><span v-text=\"plan_item.bom_code\"></span>(<span v-text=\"plan_item.plan_cnt\"></span>)</div>\r\n                                                <div slot=\"content\" style=\"width: 200px\">\r\n                                                    <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2;text-align: center;margin-bottom: 2px\">\r\n                                                        <div style=\"width: 100%\"><span v-text=\"plan_item.customer_name\"></span></div>\r\n                                                    </div>\r\n                                                    <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2;margin-bottom: 2px\">\r\n                                                        <div style=\"width: 50%\"><span v-text=\"plan_item.bom_code\"></span></div>\r\n                                                        <div style=\"width: 50%\"><span v-text=\"plan_item.name\"></span></div>\r\n                                                    </div>\r\n                                                    <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2;margin-bottom: 2px\">\r\n                                                        <div style=\"width: 50%\"><span v-text=\"plan_item.product_code\"></span></div>\r\n                                                        <div style=\"width: 50%\"><span v-text=\"plan_item.product_name\"></span></div>\r\n                                                    </div>\r\n                                                    <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2\">\r\n                                                        <div style=\"width: 50%\"><span v-text=\"plan_item.plan_cnt + '(件)'\"></span></div>\r\n                                                        <div style=\"width: 50%\"><span v-text=\"plan_item.plan_hour+ '(H)'\"></span></div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </el-tooltip>\r\n                                            <div><a @click=\"deletePlan(plan_item.id)\"><i class=\"fa fa-close\" style=\"color: red\"></i></a></div>\r\n                                        </div>\r\n                                    </draggable>\r\n                                </template>\r\n                                <template v-else>\r\n                                    <div class=\"sub-item\"\r\n                                         :style=\"{opacity:\r\n                                         ((start_idx.x == plan_idx && start_idx.y >= day_idx && end_idx.x == plan_idx && end_idx.y <= day_idx) ||\r\n                                         (start_idx.x == plan_idx && start_idx.y <= day_idx && end_idx.x == plan_idx && end_idx.y >= day_idx)) && plan_item.id == ''\r\n                                         ? 0 : 1}\"\r\n                                         @mouseover=\"mouseOver(plan_item.col_key)\" @mousedown=\"mouseDown(arguments[0],plan_item.col_key)\"\r\n                                         v-for=\"(plan_item,plan_idx) in day_item.list\" :key=\"plan_idx\">\r\n                                         <div v-if=\"plan_item.id != ''\" :style=\"{display:'flex',lineHeight:'24px',backgroundColor: row.id == plan_item.notice_detail_id && sel_bom_item.id == plan_item.bom_id  ? '#ECF5FF' :'#fff',justifyContent:'space-between'}\">\r\n                                             <el-tooltip placement=\"top\" effect=\"light\">\r\n                                                 <div style=\"padding-left: 4px\"><span v-text=\"plan_item.bom_code\"></span></div>\r\n                                                 <div slot=\"content\" style=\"width: 200px\">\r\n                                                     <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2;text-align: center;margin-bottom: 2px\">\r\n                                                         <div style=\"width: 100%\"><span v-text=\"plan_item.customer_name\"></span></div>\r\n                                                     </div>\r\n                                                     <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2;margin-bottom: 2px\">\r\n                                                         <div style=\"width: 50%\"><span v-text=\"plan_item.bom_code\"></span></div>\r\n                                                         <div style=\"width: 50%\"><span v-text=\"plan_item.name\"></span></div>\r\n                                                     </div>\r\n                                                     <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2;margin-bottom: 2px\">\r\n                                                         <div style=\"width: 50%\"><span v-text=\"plan_item.product_code\"></span></div>\r\n                                                         <div style=\"width: 50%\"><span v-text=\"plan_item.product_name\"></span></div>\r\n                                                     </div>\r\n                                                     <div style=\"display: flex;padding: 2px;border-bottom: 1px solid #D2D2D2\">\r\n                                                         <div style=\"width: 50%\"><span v-text=\"plan_item.plan_cnt + '(件)'\"></span></div>\r\n                                                         <div style=\"width: 50%\"><span v-text=\"plan_item.plan_hour+ '(H)'\"></span></div>\r\n                                                     </div>\r\n                                                 </div>\r\n                                             </el-tooltip>\r\n                                             <div style=\"padding-right: 4px\"><a @click=\"deletePlan2(plan_item.id)\"><i class=\"fa fa-close\" style=\"color: red\"></i></a></div>\r\n                                         </div>\r\n                                    </div>\r\n                                </template>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <!-- 临时外委弹窗 -->\r\n        <el-dialog width=\"600px\" title=\"临时外委\" :visible.sync=\"temp_entrust_visible\">\r\n            <el-form ref=\"entrustForm\" :model=\"temp_entrust_form\" :rules=\"temp_entrust_rules\" label-width=\"120px\">\r\n                <el-form-item label=\"生产批次\">\r\n                    <el-input v-model=\"row.code\" readonly></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"产品名称\">\r\n                    <el-input v-model=\"row.product_name\" readonly></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"BOM名称\">\r\n                    <el-input v-model=\"temp_entrust_form.bom_name\" readonly></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"外委数量\" prop=\"entrust_quantity\">\r\n                    <el-input type=\"number\" v-model=\"temp_entrust_form.entrust_quantity\" readonly>\r\n                        <template slot=\"suffix\">\r\n                            <span>(件)</span>\r\n                        </template>\r\n                    </el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\">\r\n                    <el-input \r\n                        type=\"textarea\" \r\n                        v-model=\"temp_entrust_form.remarks\" \r\n                        placeholder=\"请输入备注信息\"\r\n                        :rows=\"3\">\r\n                    </el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"temp_entrust_visible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveTempEntrust\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <el-dialog width=\"500px\" title=\"排产\" :visible.sync=\"plan_visible\">\r\n            <el-form ref=\"form\" :model=\"plan_form\" :rules=\"plan_rules\" label-width=\"100px\">\r\n                <el-form-item label=\"设备编号\">\r\n                    <el-input type=\"text\" v-model=\"plan_form.equ_code\" readonly></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"设备名称\">\r\n                    <el-input type=\"text\" v-model=\"plan_form.equ_code\" readonly></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"排产日期\" >\r\n                    <el-input type=\"text\" v-model=\"plan_form.plan_date\" readonly></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"待排产数量\">\r\n                    <el-input type=\"number\"  v-model=\"plan_form.quantity\" >\r\n                        <template slot=\"suffix\">\r\n                            <span>(件)</span>\r\n                        </template>\r\n                    </el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"生产性基准\">\r\n                    <el-input type=\"number\"  v-model=\"plan_form.produce_cnt\" >\r\n                        <template slot=\"suffix\">\r\n                            <span>(件/小时)</span>\r\n                        </template>\r\n                    </el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"排产数量\" prop=\"plan_cnt\">\r\n                    <el-input type=\"number\"  v-model=\"plan_form.plan_cnt\" :readonly=\"plan_form.key_type == 2 ? true : false\" placeholder=\"请输入排产数量\">\r\n                        <template slot=\"suffix\">\r\n                            <span>(件)</span>\r\n                        </template>\r\n                    </el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"排产时长\" prop=\"plan_hour\">\r\n                    <el-input type=\"number\"  v-model=\"plan_form.plan_hour\" :readonly=\"plan_form.key_type == 1 ? true : false\" placeholder=\"请输入时长\">\r\n                        <template slot=\"suffix\">\r\n                            <span>(小时)</span>\r\n                        </template>\r\n                    </el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"生产工人\" prop=\"worker_id\">\r\n                    <el-select v-model=\"plan_form.worker_id\" placeholder=\"请选择生产工人\" style=\"width: 100%\" >\r\n                        <el-option\r\n                            :key=\"''\"\r\n                            label=\"请选择\"\r\n                            value=\"\">\r\n                        </el-option>\r\n                        <el-option\r\n                            v-for=\"worker in worker_list\"\r\n                            :key=\"worker.id\"\r\n                            :label=\"worker.name\"\r\n                            :value=\"worker.id\">\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"plan_visible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"savePlan\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <div style=\"position: absolute;top:0;right: 0\">\r\n            <el-button type=\"primary\" @click=\"showPlanList\" plain>排产列表</el-button>\r\n        </div>\r\n        <el-drawer\r\n                title=\"排产列表\"\r\n                :visible.sync=\"list_show\"\r\n                direction=\"rtl\">\r\n            <el-table\r\n                    :data=\"notice_list\"\r\n                    style=\"width: 100%;\">\r\n                <el-table-column\r\n                        prop=\"code\"\r\n                        label=\"批次号\">\r\n                    <template slot-scope=\"scope\">\r\n                        <div>\r\n                            <span v-text=\"scope.row.code\"></span>\r\n                        </div>\r\n                        <div v-if=\"scope.row.warning_flag == 1\" style=\"padding: 2px;background-color: red;width: 80px;color: white;text-align: center;\">排产预警</div>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column\r\n                        label=\"客户/规格型号\">\r\n                    <template slot-scope=\"scope\">\r\n                        <div>\r\n                            <div>\r\n                                <span v-text=\"scope.row.customer_name\"></span>\r\n                            </div>\r\n                            <div>\r\n                                <span v-text=\"scope.row.product_name\"></span>\r\n                            </div>\r\n                        </div>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column\r\n                        prop=\"quantity\"\r\n                        label=\"数量\">\r\n                </el-table-column>\r\n                <el-table-column\r\n                        label=\"排产日期\">\r\n                    <template slot-scope=\"scope\">\r\n                        <div>\r\n                            <div>\r\n                                <span v-text=\"scope.row.plan_begin_date\"></span>\r\n                            </div>\r\n                            <div>\r\n                                <span v-text=\"scope.row.plan_end_date\"></span>\r\n                            </div>\r\n                        </div>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column\r\n                        label=\"排产\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button type=\"primary\" @click=\"changePlan(scope.row.uid)\" plain>排产</el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n\r\n\r\n        </el-drawer>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import draggable from \"vuedraggable\";\r\n    export default {\r\n        name: \"plan\",\r\n        components: {\r\n            draggable\r\n        },\r\n        data() {\r\n            return {\r\n                notice_detail_uid : '',\r\n                equ_level:1,\r\n                row:{},\r\n                bom_list:[],\r\n                day_list:[],\r\n                equ_list:[],\r\n                sel_bom_item:{\r\n                    id:''\r\n                },\r\n                plan_visible:false,\r\n                plan_form:{\r\n                    equ_id:'',\r\n                    equ_code : '',\r\n                    equ_name : '',\r\n                    key_type : 0,\r\n                    plan_date:'',\r\n                    plan_cnt:'',\r\n                    quantity:'',\r\n                    produce_cnt:'',\r\n                    plan_hour:'',\r\n                    worker_id:''\r\n                },\r\n                plan_rules: {\r\n                    plan_cnt: [\r\n                        { required: true, message: '请输入排产数量', trigger: 'blur' },\r\n                        { type: 'number', min: 1, message: '排产数量必须大于0', trigger: 'blur', transform: value => Number(value) }\r\n                    ],\r\n                    plan_hour: [\r\n                        { required: true, message: '请输入排产时长', trigger: 'blur' },\r\n                        { type: 'number', min: 0.01, message: '排产时长必须大于0', trigger: 'blur', transform: value => Number(value) }\r\n                    ],\r\n                    worker_id: [\r\n                        { required: true, message: '请选择生产工人', trigger: 'blur' }\r\n                    ]\r\n                },\r\n                keyPressFlag : false,\r\n                mouseCol : '',\r\n                start_idx : {x : -1,y : -1},\r\n                end_idx : {x : -1,y : -1},\r\n                list_show:false,\r\n                notice_list:[],\r\n                worker_list:[],\r\n                // 临时外委相关\r\n                temp_entrust_visible: false,\r\n                temp_entrust_form: {\r\n                    bom_id: '',\r\n                    bom_code: '',\r\n                    bom_name: '',\r\n                    entrust_quantity: '',\r\n                    remarks: ''\r\n                },\r\n                temp_entrust_rules: {\r\n                    entrust_quantity: [\r\n                        { required: true, message: '请输入外委数量', trigger: 'blur' },\r\n                        { type: 'number', min: 1, message: '外委数量必须大于0', trigger: 'blur', transform: value => Number(value) }\r\n                    ],\r\n                }\r\n            };\r\n        },\r\n        created() {\r\n            this.notice_detail_uid = this.$route.query.uid || '';\r\n            this.initData(this.notice_detail_uid);\r\n        },\r\n        mounted() {\r\n            window.addEventListener('mouseup', this.savePlanData);\r\n        },\r\n        beforeUnmount() {\r\n            window.removeEventListener('mouseup', this.savePlanData);\r\n        },\r\n        watch: {\r\n            'plan_form.plan_cnt'(newVal) {\r\n                // 防止循环触发\r\n                if (this.plan_form.key_type === 2) return;\r\n\r\n                if (newVal === '' || newVal === null || newVal === undefined) {\r\n                    this.plan_form.key_type = 0;\r\n                    this.plan_form.plan_hour = '';\r\n                    return;\r\n                }\r\n\r\n                const cnt = Number(newVal);\r\n                const produceCnt = Number(this.plan_form.produce_cnt);\r\n\r\n                if (cnt > 0 && produceCnt > 0) {\r\n                    this.plan_form.key_type = 1;\r\n                    this.plan_form.plan_hour = (cnt / produceCnt).toFixed(2);\r\n                }\r\n            },\r\n            'plan_form.plan_hour'(newVal) {\r\n                // 防止循环触发\r\n                if (this.plan_form.key_type === 1) return;\r\n\r\n                if (newVal === '' || newVal === null || newVal === undefined) {\r\n                    this.plan_form.key_type = 0;\r\n                    this.plan_form.plan_cnt = '';\r\n                    return;\r\n                }\r\n\r\n                const hour = Number(newVal);\r\n                const produceCnt = Number(this.plan_form.produce_cnt);\r\n\r\n                if (hour > 0 && produceCnt > 0) {\r\n                    this.plan_form.key_type = 2;\r\n                    this.plan_form.plan_cnt = (hour * produceCnt).toFixed(2);\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            mouseOver(col_key){\r\n                this.mouseCol = col_key;\r\n                if (this.keyPressFlag){\r\n                    let over_data = this.keyToIndex(col_key);\r\n                    if (over_data.x !=  this.end_idx.x){\r\n                        this.cancelSelect();\r\n                        return;\r\n                    }\r\n                    this.end_idx = over_data;\r\n                }\r\n            },\r\n            mouseDown(e,col_key){\r\n                e.preventDefault();\r\n                if (e.button == 0){\r\n                    this.keyPressFlag = true;\r\n                    if (this.mouseCol != ''){\r\n                        let idn = this.keyToIndex(this.mouseCol);\r\n                        this.start_idx = idn;\r\n                        this.end_idx = idn;\r\n                    } else {\r\n                        this.mouseCol = col_key;\r\n                    }\r\n                }\r\n            },\r\n            keyToIndex(col_key){\r\n                let idxs = col_key.split('_');\r\n                return {\r\n                    y:parseInt(idxs[1]),\r\n                    x:parseInt(idxs[2])\r\n                };\r\n            },\r\n\r\n            initData(uid){\r\n                if (uid == ''){\r\n                    this.$message.error('参数错误');\r\n                    return;\r\n                }\r\n                this.$http.post('mes/plan/init', {uid:uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let data = rs.data;\r\n                        this.row = data.row;\r\n                        this.bom_list = data.bom_list;\r\n                        this.day_list = data.day_list;\r\n                        this.list_show = false;\r\n                        this.sel_bom_item = {id:''};\r\n                        this.equ_list = [];\r\n                        this.worker_list = data.worker_list;\r\n                        this.equ_level = 1;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            selPlanData(bom_item){\r\n                this.sel_bom_item = bom_item;\r\n                this.equ_level = 1;\r\n                this.getPlanData();\r\n            },\r\n            getPlanData(){\r\n                this.$http.post('mes/plan/data', {\r\n                    notice_detail_uid:this.notice_detail_uid,\r\n                    bom_uid:this.sel_bom_item.uid,\r\n                    level:this.equ_level,\r\n                    entrust_id:this.sel_bom_item.entrust_id,\r\n                    plan_type:this.sel_bom_item.plan_type\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let data = rs.data;\r\n                        this.equ_list = data.equ_list;\r\n                        this.bom_list = data.bom_list;\r\n                        \r\n                        // 重新设置 sel_bom_item 为更新后的数据\r\n                        const currentBomId = this.sel_bom_item.id;\r\n                        const updatedBomItem = this.bom_list.find(item => item.id === currentBomId);\r\n                        if (updatedBomItem) {\r\n                            this.sel_bom_item = updatedBomItem;\r\n                        }\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            changeEquLevel(){\r\n                if (this.sel_bom_item.id == ''){\r\n                    return;\r\n                }\r\n                this.getPlanData();\r\n            },\r\n            addPlan(equ_item,day_item){\r\n                if (this.sel_bom_item.id == ''){\r\n                    return;\r\n                }\r\n                this.plan_form = {\r\n                    equ_id : equ_item.equ_id,\r\n                    equ_code : equ_item.code,\r\n                    equ_name : equ_item.name,\r\n                    plan_date : day_item.date,\r\n                    plan_cnt:'',\r\n                    quantity:this.sel_bom_item.un_plan_cnt,\r\n                    produce_cnt:this.sel_bom_item.produce_cnt,\r\n                    plan_hour:'',\r\n                    worker_id:''\r\n                }\r\n                this.plan_visible = true;\r\n            },\r\n            savePlan(){\r\n                this.$refs.form.validate((valid) => {\r\n                    if (valid) {\r\n                        this.$http.post('mes/plan/save', {...this.plan_form,notice_detail_uid:this.notice_detail_uid,bom_uid:this.sel_bom_item.uid}).then((rs) => {\r\n                            if (rs.status == 'ok') {\r\n                               this.getPlanData();\r\n                               this.plan_visible = false;\r\n                            } else {\r\n                                this.$message.error(rs.message);\r\n                            }\r\n                        }).catch(() => {\r\n                            this.$message.error('未知错误');\r\n                        });\r\n                    } else {\r\n                        this.$message.error('请完善表单信息');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            planChange(equ_idx,day_idx,elem){\r\n                if (elem['added']){\r\n                    let item = elem.added;\r\n                    this.saveMove(equ_idx,day_idx,item.element);\r\n                } else if (elem['moved']){\r\n                    let item = elem.moved;\r\n                    this.saveMove(equ_idx,day_idx,item.element);\r\n                }\r\n            },\r\n            saveMove(equ_idx,day_idx,item){\r\n                let equ_item = this.equ_list[equ_idx];\r\n                let day_item = equ_item['day_list'][day_idx];\r\n                let ids = [];\r\n                for(let i of day_item['list']){\r\n                    ids.push(i.id);\r\n                }\r\n                this.$http.post('mes/plan/move', {equ_id:equ_item.equ_id,plan_date:day_item.date,plan_id:item.id,ids:ids}).then((rs) => {\r\n                    if (rs.status != 'ok'){\r\n                        this.$message.error(rs.message);\r\n                    } else {\r\n                        this.getPlanData();\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            deletePlan(plan_id){\r\n                this.$confirm('确认是否要删除吗?', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.$http.post('mes/plan/delete', {plan_id:plan_id}).then((rs) => {\r\n                        if (rs.status != 'ok'){\r\n                            this.$message.error(rs.message);\r\n                        } else {\r\n                            this.getPlanData();\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$message.error('未知错误');\r\n                    });\r\n                }).catch(() => {});\r\n            },\r\n            deletePlan2(plan_id){\r\n                this.$confirm('确认是否要删除吗?', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.$http.post('mes/plan/delete2', {plan_id:plan_id}).then((rs) => {\r\n                        if (rs.status != 'ok'){\r\n                            this.$message.error(rs.message);\r\n                        } else {\r\n                            this.getPlanData();\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$message.error('未知错误');\r\n                    });\r\n                }).catch(() => {});\r\n            },\r\n            savePlanData(e) {\r\n                if (e.button == 0){\r\n                    if (this.start_idx.x == -1 || this.start_idx.y == -1 || this.end_idx.x == -1 || this.end_idx.y == -1){\r\n                        return;\r\n                    }\r\n                    if (this.mouseCol == ''){\r\n                        return;\r\n                    }\r\n                    let idxs = this.mouseCol.split('_');\r\n                    let equ_idx = parseInt(idxs[3]);\r\n                    let start_idx = this.start_idx.y > this.end_idx.y ? this.end_idx.y : this.start_idx.y;\r\n                    let end_idx = this.start_idx.y < this.end_idx.y ? this.end_idx.y : this.start_idx.y;\r\n                    let row_idx = this.start_idx.x;\r\n                    let equ_item = this.equ_list[equ_idx];\r\n                    let plan_list = [];\r\n                    for(let i = start_idx; i <= end_idx; i++){\r\n                        let plan_item = equ_item['day_list'][i]['list'][row_idx];\r\n                        if (plan_item.id != ''){\r\n                            this.cancelSelect();\r\n                            this.$message.error('该时间段已有排产');\r\n                            return;\r\n                        }\r\n                        plan_list.push(equ_item['day_list'][i]['date']);\r\n                    }\r\n                    if (plan_list.length == 0){\r\n                        this.$message.error('请选择时间段');\r\n                        return;\r\n                    }\r\n                    this.$http.post('mes/plan/save2', {\r\n                        notice_detail_uid: this.notice_detail_uid,\r\n                        bom_uid: this.sel_bom_item.uid,\r\n                        entrust_id: this.sel_bom_item.entrust_id,\r\n                        equ_id : equ_item.equ_id,\r\n                        row_idx: row_idx,\r\n                        plan_list: plan_list\r\n                    }).then((rs) => {\r\n                        if (rs.status == 'ok') {\r\n                            this.getPlanData();\r\n                            this.plan_visible = false;\r\n                        } else {\r\n                            this.$message.error(rs.message);\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$message.error('未知错误');\r\n                    });\r\n                }\r\n            },\r\n            cancelSelect(){\r\n                this.keyPressFlag = false;\r\n                this.start_idx = {x : -1,y : -1};\r\n                this.end_idx = {x : -1,y : -1};\r\n                this.mouseCol = '';\r\n            },\r\n            showPlanList(){\r\n                this.$http.post('mes/plan/list', {id:0}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.notice_list = rs.data;\r\n                        this.list_show = true;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            changePlan(uid) {\r\n                this.notice_detail_uid = uid;\r\n                this.initData(uid);\r\n            },\r\n            // 临时外委相关方法\r\n            showTempEntrust(bom_item) {\r\n                this.temp_entrust_form = {\r\n                    bom_id: bom_item.id,\r\n                    bom_code: bom_item.bom_code,\r\n                    bom_name: bom_item.name,\r\n                    entrust_quantity: (Number(bom_item.un_plan_cnt || 0) + Number(bom_item.plan_cnt || 0)) || '',\r\n                    remarks: ''\r\n                };\r\n                this.temp_entrust_visible = true;\r\n            },\r\n            saveTempEntrust() {\r\n                this.$refs.entrustForm.validate((valid) => {\r\n                    if (valid) {\r\n                        const formData = {\r\n                            ...this.temp_entrust_form,\r\n                            notice_detail_uid: this.notice_detail_uid,\r\n                            product_id: this.row.product_id,\r\n                            notice_id: this.row.notice_id\r\n                        };\r\n                        \r\n                        this.$http.post('mes/plan/tempentrust', formData).then((rs) => {\r\n                            if (rs.status == 'ok') {\r\n                                this.$message.success('临时外委创建成功');\r\n                                this.temp_entrust_visible = false;\r\n                                // 刷新数据\r\n                                this.initData(this.notice_detail_uid);\r\n                            } else {\r\n                                this.$message.error(rs.message);\r\n                            }\r\n                        }).catch(() => {\r\n                            this.$message.error('未知错误');\r\n                        });\r\n                    } else {\r\n                        this.$message.error('请完善表单信息');\r\n                        return false;\r\n                    }\r\n                });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n\r\n<style scoped>\r\n    .plan-header{\r\n        width: 160px;\r\n        height: 40px;\r\n        line-height: 40px;\r\n        background-color: #F2F2F2;\r\n        border-width:  0 2px 2px 0;\r\n        border-color: #FFF;\r\n        border-style: solid;\r\n        text-align: center;\r\n        flex-shrink: 0;\r\n    }\r\n\r\n    .plan-item{\r\n        width: 160px;\r\n        height: 120px;\r\n        background-color: #F2F2F2;\r\n        border-width:  0 2px 2px 0;\r\n        border-color: #FFF;\r\n        border-style: solid;\r\n        flex-shrink: 0;\r\n        position: relative;\r\n    }\r\n    .plan-item::selection {\r\n        background: rgba(255,255,255,0);\r\n    }\r\n\r\n    .sub-item{\r\n        width: 160px;\r\n        height: 24px;\r\n        background-color: #E2E2E2;\r\n        border: 1px  solid #fff;\r\n        flex-shrink: 0;\r\n    }\r\n    .sub-item::selection {\r\n        background: rgba(255,255,255,0);\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"form-group form-group-lg panel panel-default\"},[_vm._m(0),_c('div',[_c('el-descriptions',{staticClass:\"margin-top\",attrs:{\"column\":4,\"border\":\"\"}},[_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 批次号 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.code)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 客户 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.customer_name)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 产品编号 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.product_code)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 型号规格 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.product_name)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 生产数量 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.quantity)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 计划开始日 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.plan_begin_date)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 计划完成日 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.plan_end_date)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 备注 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.row.remarks)}})],2)],1)],1),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',{staticStyle:{\"width\":\"17vw\",\"border-right\":\"1px solid #f2f2f2\",\"height\":\"83vh\",\"overflow-y\":\"auto\",\"padding\":\"0 5px\"}},_vm._l((_vm.bom_list),function(bom_item,bom_idx){return _c('div',{key:bom_idx,style:({border:_vm.sel_bom_item.id ==  bom_item.id ?  ' 1px solid #208FFF' :  ' 1px solid #e2e2e2',margin:'10px 5px'})},[_c('div',{style:({padding:'5px',backgroundColor:_vm.sel_bom_item.id ==  bom_item.id ? '#ECF5FF' : '#f2f2f2'})},[_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-between\",\"align-items\":\"center\",\"margin-bottom\":\"5px\"}},[_c('div',{staticStyle:{\"line-height\":\"30px\"}},[_c('span',{domProps:{\"textContent\":_vm._s(bom_item.bom_code)}}),_vm._v(\"   \"),_c('span',{domProps:{\"textContent\":_vm._s(bom_item.name)}}),_vm._v(\"   \"),(bom_item.entrust_id != null)?_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"warning\",\"effect\":\"dark\"}},[_vm._v(\"委外\")]):_vm._e()],1),_c('el-button',{attrs:{\"type\":_vm.sel_bom_item.id == bom_item.id ? 'primary' : 'default',\"size\":\"mini\"},on:{\"click\":function($event){return _vm.selPlanData(bom_item)}}},[_vm._v(\"排产\")])],1),(bom_item.entrust_id == null)?_c('div',{staticStyle:{\"text-align\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.showTempEntrust(bom_item)}}},[_vm._v(\"临时外委\")])],1):_vm._e()]),_c('div',{staticStyle:{\"min-height\":\"50px\"}},[(bom_item.warning_flag == 1)?_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"flex-end\"}},[_c('div',{staticStyle:{\"padding\":\"2px\",\"background-color\":\"red\",\"width\":\"80px\",\"color\":\"white\",\"text-align\":\"center\"}},[_vm._v(\"排产预警\")])]):_vm._e(),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"50%\",\"padding\":\"5px\"}},[_vm._m(1,true),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(bom_item.start_date)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"50%\",\"padding\":\"5px\"}},[_vm._m(2,true),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(bom_item.end_date)}})])])]),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"50%\",\"padding\":\"5px\"}},[_vm._m(3,true),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(bom_item.finish_cnt)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"50%\",\"padding\":\"5px\"}},[_vm._m(4,true),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(bom_item.error_cnt)}})])])]),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"50%\",\"padding\":\"5px\"}},[_vm._m(5,true),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(bom_item.plan_cnt)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"50%\",\"padding\":\"5px\"}},[_vm._m(6,true),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(bom_item.un_plan_cnt)}})])])])])])}),0),_c('div',{staticStyle:{\"width\":\"83vw\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"5px\"}},[_c('div',{staticStyle:{\"width\":\"230px\",\"line-height\":\"30px\"}},[_vm._v(\" 按设备使用优选级查找可排产设备: \")]),_c('el-radio-group',{attrs:{\"size\":\"mini\"},on:{\"change\":_vm.changeEquLevel},model:{value:(_vm.equ_level),callback:function ($$v) {_vm.equ_level=$$v},expression:\"equ_level\"}},[_c('el-radio-button',{attrs:{\"label\":1}},[_vm._v(\"优先级1\")]),_c('el-radio-button',{attrs:{\"label\":2}},[_vm._v(\"优先级2\")]),_c('el-radio-button',{attrs:{\"label\":3}},[_vm._v(\"优先级3\")])],1)],1),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',[_c('div',{staticClass:\"plan-header\",staticStyle:{\"width\":\"120px\"}}),_vm._l((_vm.equ_list),function(equ_item,equ_idx){return _c('div',{key:equ_idx},[_c('div',{staticClass:\"plan-item\",staticStyle:{\"background-color\":\"#f2f2f2\",\"font-size\":\"12px\",\"font-weight\":\"600\",\"padding\":\"3px\",\"width\":\"120px\"}},[_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(equ_item.code)}})]),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(equ_item.name)}})]),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(equ_item.type_name)}})]),_c('div',[(equ_item.status_name == '')?_c('div'):(equ_item.status_name == '' || equ_item.status_name == '在用')?_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"success\",\"effect\":\"dark\"}},[_vm._v(_vm._s(equ_item.status_name))]):_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"danger\",\"effect\":\"dark\"}},[_vm._v(_vm._s(equ_item.status_name))])],1)])])})],2),_c('div',{staticStyle:{\"flex\":\"1\",\"overflow-x\":\"auto\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},_vm._l((_vm.day_list),function(day_item,day_idx){return _c('div',{key:day_idx,staticClass:\"plan-header\"},[_c('span',{domProps:{\"textContent\":_vm._s(day_item.date_show + ' ' + day_item.week)}})])}),0),_vm._l((_vm.equ_list),function(equ_item,equ_idx){return _c('div',{key:equ_idx,staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},_vm._l((equ_item.day_list),function(day_item,day_idx){return _c('div',{key:day_idx,staticClass:\"plan-item\"},[(_vm.sel_bom_item.plan_type == 1)?[_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-between\",\"background-color\":\"#E2E2E2\",\"padding\":\"2px\"}},[_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(day_item.hour + '  (H)')}})]),_c('div',[_c('a',{on:{\"click\":function($event){return _vm.addPlan(equ_item,day_item)}}},[_c('i',{staticClass:\"el-icon-circle-plus-outline\",staticStyle:{\"font-size\":\"20px\"}})])])]),_c('draggable',{staticStyle:{\"min-height\":\"80px\"},attrs:{\"tag\":\"div\",\"list\":day_item.list,\"group\":\"plan_gl\",\"handle\":\".handle\"},on:{\"change\":function($event){return _vm.planChange(equ_idx,day_idx,arguments[0])}}},_vm._l((day_item.list),function(plan_item,plan_idx){return _c('div',{key:plan_idx,style:({display:'flex',lineHeight:'20px',backgroundColor: _vm.row.id == plan_item.notice_detail_id && _vm.sel_bom_item.id == plan_item.bom_id ? '#ECF5FF' :'#fff',justifyContent:'space-between',padding:' 0 2px',marginBottom:'1px'})},[_c('div',[_c('i',{staticClass:\"fa fa-align-justify handle\",staticStyle:{\"margin-right\":\"10px\"}})]),_c('el-tooltip',{attrs:{\"placement\":\"top\",\"effect\":\"light\"}},[_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.bom_code)}}),_vm._v(\"(\"),_c('span',{domProps:{\"textContent\":_vm._s(plan_item.plan_cnt)}}),_vm._v(\")\")]),_c('div',{staticStyle:{\"width\":\"200px\"},attrs:{\"slot\":\"content\"},slot:\"content\"},[_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\",\"text-align\":\"center\",\"margin-bottom\":\"2px\"}},[_c('div',{staticStyle:{\"width\":\"100%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.customer_name)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\",\"margin-bottom\":\"2px\"}},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.bom_code)}})]),_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.name)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\",\"margin-bottom\":\"2px\"}},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.product_code)}})]),_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.product_name)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\"}},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.plan_cnt + '(件)')}})]),_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.plan_hour+ '(H)')}})])])])]),_c('div',[_c('a',{on:{\"click\":function($event){return _vm.deletePlan(plan_item.id)}}},[_c('i',{staticClass:\"fa fa-close\",staticStyle:{\"color\":\"red\"}})])])],1)}),0)]:_vm._l((day_item.list),function(plan_item,plan_idx){return _c('div',{key:plan_idx,staticClass:\"sub-item\",style:({opacity:\n                                     ((_vm.start_idx.x == plan_idx && _vm.start_idx.y >= day_idx && _vm.end_idx.x == plan_idx && _vm.end_idx.y <= day_idx) ||\n                                     (_vm.start_idx.x == plan_idx && _vm.start_idx.y <= day_idx && _vm.end_idx.x == plan_idx && _vm.end_idx.y >= day_idx)) && plan_item.id == ''\n                                     ? 0 : 1}),on:{\"mouseover\":function($event){return _vm.mouseOver(plan_item.col_key)},\"mousedown\":function($event){return _vm.mouseDown(arguments[0],plan_item.col_key)}}},[(plan_item.id != '')?_c('div',{style:({display:'flex',lineHeight:'24px',backgroundColor: _vm.row.id == plan_item.notice_detail_id && _vm.sel_bom_item.id == plan_item.bom_id  ? '#ECF5FF' :'#fff',justifyContent:'space-between'})},[_c('el-tooltip',{attrs:{\"placement\":\"top\",\"effect\":\"light\"}},[_c('div',{staticStyle:{\"padding-left\":\"4px\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.bom_code)}})]),_c('div',{staticStyle:{\"width\":\"200px\"},attrs:{\"slot\":\"content\"},slot:\"content\"},[_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\",\"text-align\":\"center\",\"margin-bottom\":\"2px\"}},[_c('div',{staticStyle:{\"width\":\"100%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.customer_name)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\",\"margin-bottom\":\"2px\"}},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.bom_code)}})]),_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.name)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\",\"margin-bottom\":\"2px\"}},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.product_code)}})]),_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.product_name)}})])]),_c('div',{staticStyle:{\"display\":\"flex\",\"padding\":\"2px\",\"border-bottom\":\"1px solid #D2D2D2\"}},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.plan_cnt + '(件)')}})]),_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{domProps:{\"textContent\":_vm._s(plan_item.plan_hour+ '(H)')}})])])])]),_c('div',{staticStyle:{\"padding-right\":\"4px\"}},[_c('a',{on:{\"click\":function($event){return _vm.deletePlan2(plan_item.id)}}},[_c('i',{staticClass:\"fa fa-close\",staticStyle:{\"color\":\"red\"}})])])],1):_vm._e()])})],2)}),0)})],2)])])]),_c('el-dialog',{attrs:{\"width\":\"600px\",\"title\":\"临时外委\",\"visible\":_vm.temp_entrust_visible},on:{\"update:visible\":function($event){_vm.temp_entrust_visible=$event}}},[_c('el-form',{ref:\"entrustForm\",attrs:{\"model\":_vm.temp_entrust_form,\"rules\":_vm.temp_entrust_rules,\"label-width\":\"120px\"}},[_c('el-form-item',{attrs:{\"label\":\"生产批次\"}},[_c('el-input',{attrs:{\"readonly\":\"\"},model:{value:(_vm.row.code),callback:function ($$v) {_vm.$set(_vm.row, \"code\", $$v)},expression:\"row.code\"}})],1),_c('el-form-item',{attrs:{\"label\":\"产品名称\"}},[_c('el-input',{attrs:{\"readonly\":\"\"},model:{value:(_vm.row.product_name),callback:function ($$v) {_vm.$set(_vm.row, \"product_name\", $$v)},expression:\"row.product_name\"}})],1),_c('el-form-item',{attrs:{\"label\":\"BOM名称\"}},[_c('el-input',{attrs:{\"readonly\":\"\"},model:{value:(_vm.temp_entrust_form.bom_name),callback:function ($$v) {_vm.$set(_vm.temp_entrust_form, \"bom_name\", $$v)},expression:\"temp_entrust_form.bom_name\"}})],1),_c('el-form-item',{attrs:{\"label\":\"外委数量\",\"prop\":\"entrust_quantity\"}},[_c('el-input',{attrs:{\"type\":\"number\",\"readonly\":\"\"},model:{value:(_vm.temp_entrust_form.entrust_quantity),callback:function ($$v) {_vm.$set(_vm.temp_entrust_form, \"entrust_quantity\", $$v)},expression:\"temp_entrust_form.entrust_quantity\"}},[_c('template',{slot:\"suffix\"},[_c('span',[_vm._v(\"(件)\")])])],2)],1),_c('el-form-item',{attrs:{\"label\":\"备注\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"placeholder\":\"请输入备注信息\",\"rows\":3},model:{value:(_vm.temp_entrust_form.remarks),callback:function ($$v) {_vm.$set(_vm.temp_entrust_form, \"remarks\", $$v)},expression:\"temp_entrust_form.remarks\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.temp_entrust_visible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.saveTempEntrust}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"width\":\"500px\",\"title\":\"排产\",\"visible\":_vm.plan_visible},on:{\"update:visible\":function($event){_vm.plan_visible=$event}}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.plan_form,\"rules\":_vm.plan_rules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"设备编号\"}},[_c('el-input',{attrs:{\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.plan_form.equ_code),callback:function ($$v) {_vm.$set(_vm.plan_form, \"equ_code\", $$v)},expression:\"plan_form.equ_code\"}})],1),_c('el-form-item',{attrs:{\"label\":\"设备名称\"}},[_c('el-input',{attrs:{\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.plan_form.equ_code),callback:function ($$v) {_vm.$set(_vm.plan_form, \"equ_code\", $$v)},expression:\"plan_form.equ_code\"}})],1),_c('el-form-item',{attrs:{\"label\":\"排产日期\"}},[_c('el-input',{attrs:{\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.plan_form.plan_date),callback:function ($$v) {_vm.$set(_vm.plan_form, \"plan_date\", $$v)},expression:\"plan_form.plan_date\"}})],1),_c('el-form-item',{attrs:{\"label\":\"待排产数量\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.plan_form.quantity),callback:function ($$v) {_vm.$set(_vm.plan_form, \"quantity\", $$v)},expression:\"plan_form.quantity\"}},[_c('template',{slot:\"suffix\"},[_c('span',[_vm._v(\"(件)\")])])],2)],1),_c('el-form-item',{attrs:{\"label\":\"生产性基准\"}},[_c('el-input',{attrs:{\"type\":\"number\"},model:{value:(_vm.plan_form.produce_cnt),callback:function ($$v) {_vm.$set(_vm.plan_form, \"produce_cnt\", $$v)},expression:\"plan_form.produce_cnt\"}},[_c('template',{slot:\"suffix\"},[_c('span',[_vm._v(\"(件/小时)\")])])],2)],1),_c('el-form-item',{attrs:{\"label\":\"排产数量\",\"prop\":\"plan_cnt\"}},[_c('el-input',{attrs:{\"type\":\"number\",\"readonly\":_vm.plan_form.key_type == 2 ? true : false,\"placeholder\":\"请输入排产数量\"},model:{value:(_vm.plan_form.plan_cnt),callback:function ($$v) {_vm.$set(_vm.plan_form, \"plan_cnt\", $$v)},expression:\"plan_form.plan_cnt\"}},[_c('template',{slot:\"suffix\"},[_c('span',[_vm._v(\"(件)\")])])],2)],1),_c('el-form-item',{attrs:{\"label\":\"排产时长\",\"prop\":\"plan_hour\"}},[_c('el-input',{attrs:{\"type\":\"number\",\"readonly\":_vm.plan_form.key_type == 1 ? true : false,\"placeholder\":\"请输入时长\"},model:{value:(_vm.plan_form.plan_hour),callback:function ($$v) {_vm.$set(_vm.plan_form, \"plan_hour\", $$v)},expression:\"plan_form.plan_hour\"}},[_c('template',{slot:\"suffix\"},[_c('span',[_vm._v(\"(小时)\")])])],2)],1),_c('el-form-item',{attrs:{\"label\":\"生产工人\",\"prop\":\"worker_id\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择生产工人\"},model:{value:(_vm.plan_form.worker_id),callback:function ($$v) {_vm.$set(_vm.plan_form, \"worker_id\", $$v)},expression:\"plan_form.worker_id\"}},[_c('el-option',{key:'',attrs:{\"label\":\"请选择\",\"value\":\"\"}}),_vm._l((_vm.worker_list),function(worker){return _c('el-option',{key:worker.id,attrs:{\"label\":worker.name,\"value\":worker.id}})})],2)],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.plan_visible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.savePlan}},[_vm._v(\"确 定\")])],1)],1),_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"0\",\"right\":\"0\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":_vm.showPlanList}},[_vm._v(\"排产列表\")])],1),_c('el-drawer',{attrs:{\"title\":\"排产列表\",\"visible\":_vm.list_show,\"direction\":\"rtl\"},on:{\"update:visible\":function($event){_vm.list_show=$event}}},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.notice_list}},[_c('el-table-column',{attrs:{\"prop\":\"code\",\"label\":\"批次号\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(scope.row.code)}})]),(scope.row.warning_flag == 1)?_c('div',{staticStyle:{\"padding\":\"2px\",\"background-color\":\"red\",\"width\":\"80px\",\"color\":\"white\",\"text-align\":\"center\"}},[_vm._v(\"排产预警\")]):_vm._e()]}}])}),_c('el-table-column',{attrs:{\"label\":\"客户/规格型号\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(scope.row.customer_name)}})]),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(scope.row.product_name)}})])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"quantity\",\"label\":\"数量\"}}),_c('el-table-column',{attrs:{\"label\":\"排产日期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(scope.row.plan_begin_date)}})]),_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(scope.row.plan_end_date)}})])])]}}])}),_c('el-table-column',{attrs:{\"label\":\"排产\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.changePlan(scope.row.uid)}}},[_vm._v(\"排产\")])]}}])})],1)],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"panel-heading\"},[_c('h3',{staticClass:\"panel-title\"},[_vm._v(\"排产计划\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"width\":\"40%\"}},[_c('span',[_vm._v(\"开始：\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"width\":\"40%\"}},[_c('span',[_vm._v(\"完成：\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"width\":\"60%\"}},[_c('span',[_vm._v(\"完成数量：\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"width\":\"60%\"}},[_c('span',[_vm._v(\"不良数量：\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"width\":\"60%\"}},[_c('span',[_vm._v(\"排产数量：\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"width\":\"60%\"}},[_c('span',[_vm._v(\"待排数量：\")])])\n}]\nrender._withStripped = true\nexport { render, staticRenderFns }", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar iterate = require('../internals/iterate');\nvar aCallable = require('../internals/a-callable');\nvar anObject = require('../internals/an-object');\nvar getIteratorDirect = require('../internals/get-iterator-direct');\nvar iteratorClose = require('../internals/iterator-close');\nvar iteratorHelperWithoutClosingOnEarlyError = require('../internals/iterator-helper-without-closing-on-early-error');\n\nvar findWithoutClosingOnEarlyError = iteratorHelperWithoutClosingOnEarlyError('find', TypeError);\n\n// `Iterator.prototype.find` method\n// https://tc39.es/ecma262/#sec-iterator.prototype.find\n$({ target: 'Iterator', proto: true, real: true, forced: findWithoutClosingOnEarlyError }, {\n  find: function find(predicate) {\n    anObject(this);\n    try {\n      aCallable(predicate);\n    } catch (error) {\n      iteratorClose(this, 'throw', error);\n    }\n\n    if (findWithoutClosingOnEarlyError) return call(findWithoutClosingOnEarlyError, this, predicate);\n\n    var record = getIteratorDirect(this);\n    var counter = 0;\n    return iterate(record, function (value, stop) {\n      if (predicate(value, counter++)) return stop(value);\n    }, { IS_RECORD: true, INTERRUPTED: true }).result;\n  }\n});\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.plan-header[data-v-25ad301a]{\\n    width: 160px;\\n    height: 40px;\\n    line-height: 40px;\\n    background-color: #F2F2F2;\\n    border-width:  0 2px 2px 0;\\n    border-color: #FFF;\\n    border-style: solid;\\n    text-align: center;\\n    -ms-flex-negative: 0;\\n        flex-shrink: 0;\\n}\\n.plan-item[data-v-25ad301a]{\\n    width: 160px;\\n    height: 120px;\\n    background-color: #F2F2F2;\\n    border-width:  0 2px 2px 0;\\n    border-color: #FFF;\\n    border-style: solid;\\n    -ms-flex-negative: 0;\\n        flex-shrink: 0;\\n    position: relative;\\n}\\n.plan-item[data-v-25ad301a]::-moz-selection {\\n    background: rgba(255,255,255,0);\\n}\\n.plan-item[data-v-25ad301a]::selection {\\n    background: rgba(255,255,255,0);\\n}\\n.sub-item[data-v-25ad301a]{\\n    width: 160px;\\n    height: 24px;\\n    background-color: #E2E2E2;\\n    border: 1px  solid #fff;\\n    -ms-flex-negative: 0;\\n        flex-shrink: 0;\\n}\\n.sub-item[data-v-25ad301a]::-moz-selection {\\n    background: rgba(255,255,255,0);\\n}\\n.sub-item[data-v-25ad301a]::selection {\\n    background: rgba(255,255,255,0);\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=25ad301a&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"e0747106\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=25ad301a&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=25ad301a&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./plan.vue?vue&type=template&id=25ad301a&scoped=true\"\nimport script from \"./plan.vue?vue&type=script&lang=js\"\nexport * from \"./plan.vue?vue&type=script&lang=js\"\nimport style0 from \"./plan.vue?vue&type=style&index=0&id=25ad301a&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"25ad301a\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('25ad301a')) {\n      api.createRecord('25ad301a', component.options)\n    } else {\n      api.reload('25ad301a', component.options)\n    }\n    module.hot.accept(\"./plan.vue?vue&type=template&id=25ad301a&scoped=true\", function () {\n      api.rerender('25ad301a', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/mingjing/plan.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=25ad301a&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=template&id=25ad301a&scoped=true\""], "names": [], "sourceRoot": ""}