<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInventory;
use Envsan\Modules\Purchase\Model\PurchaseInventoryDetail;
use Envsan\Modules\Purchase\Model\PurchaseStockLogs;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class InventoryService extends Component
{
    private $page_id = 28;

    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.inventory_date,
                t1.remarks,
                t1.ext_val
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInventory', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.inventory_date,
                t1.remarks,
                t1.ext_val,
                t99.goods_code,
                t99.goods_name,
                t99.goods_spec,
                t99.goods_model,
                t99.goods_unit,
                t99.quantity_before,
                t99.quantity,
                t99.quantity_after
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInventory', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInventoryDetail', 't1.id = t99.inventory_id', 't99')
            ->where('t1.del_flag = 0 and t99.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectById($id)
    {
        return PurchaseInventory::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseInventory::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function create()
    {
        $row = new PurchaseInventory();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        $type = urldecode($this->request->getPost('type', ['string', 'trim']));
        $inventory_date = $this->request->getPost('inventory_date', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $files = urldecode($this->request->getPost('files', ['string', 'trim']));
        $ext_data = str_replace('%2B','+',urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
        $detail = str_replace('%2B','+',urldecode($this->request->getPost('detail', ['string', 'trim'])));
        if (empty($type) || empty($inventory_date) || empty($detail)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $files = CvtUtil::emptyToArray($files);
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $detail = CvtUtil::emptyToArray($detail);
        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
            $row->inventory_date = $inventory_date;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->detail_data =  CvtUtil::arrayToNull($detail);;
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data),JSON_UNESCAPED_UNICODE);
            if ($type == 1){
                $row->status = 10;
            } else {
                $row->status = 20;
            }
            $row->status_name = Constant::$purchase_inventory_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $ss = new SequenceService();
                $row->code = $ss->useSequence(16);
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("MesProduct表更新失败");
            }
            if ($row->status == 20){
                foreach ($detail as $item){
                    $detail_row = new PurchaseInventoryDetail();
                    $detail_row->uid = UUID::make();
                    $detail_row->inventory_id = $row->id;
                    $detail_row->goods_id = $item['id'];
                    $detail_row->goods_code = $item['code'];
                    $detail_row->goods_name = $item['name'];
                    $detail_row->goods_spec = $item['spec'];
                    $detail_row->goods_model = $item['model'];
                    $detail_row->goods_unit = $item['unit'];
                    $detail_row->quantity_before = $item['quantity_before'];
                    $detail_row->quantity = $item['quantity'];
                    $detail_row->quantity_after =  $item['quantity_after'];
                    $detail_row->update_date = $now;
                    $detail_row->update_by = $user->id;
                    $detail_row->del_flag = 0;
                    $detail_row->owner = $user->owner;
                    if (!$detail_row->save()) {
                        throw new \Exception("MesProduct表更新失败");
                    }
                    $logs = new PurchaseStockLogs();
                    $logs->batch_no = $row->code;
                    $logs->data_type = 4;
                    $logs->data_id = $row->id;
                    if ($detail_row->quantity > 0){
                        $logs->sign = 1;
                    } else {
                        $logs->sign = 2;
                    }
                    $logs->goods_id = $detail_row->goods_id;
                    $logs->goods_name = $detail_row->goods_name;
                    $logs->goods_unit = $detail_row->goods_unit;
                    $logs->quantity = $detail_row->quantity;
                    $logs->update_date = $now;
                    $logs->update_user = $user->real_name;
                    $logs->del_flag = 0;
                    $logs->owner = $user->owner;
                    if (!$logs->save()) {
                        throw new \Exception("PurchaseStockLogs表更新失败");
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function delete(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 10){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->del_flag = 1;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function cancel(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 20){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
             $inventory_rows = PurchaseInventoryDetail::find(['del_flag = 0 and inventory_id = ?1','bind'=>[1=>$row->id]]);
             if (!$inventory_rows->delete()){
                 throw new \Exception("PurchaseInventoryDetail表更新失败");
             }
             $log_rows = PurchaseStockLogs::find(['del_flag = 0 and data_type = 4 and data_id = ?1','bind'=>[1=>$row->id]]);
             if (!$log_rows->delete()){
                throw new \Exception("PurchaseInventoryDetail表更新失败");
             }
             $row->status = 10;
             $row->status_name = Constant::$purchase_inventory_status[$row->status];
             $row->update_date = $now;
             $row->update_by = $user->id;
             if (!$row->save()){
                 throw new \Exception("PurchaseInventory表更新失败");
             }
             $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function getDetail($inventory_id){
        return PurchaseInventoryDetail::find(['del_flag = 0 and inventory_id = ?1','bind'=>[1=>$inventory_id]]);
    }
}