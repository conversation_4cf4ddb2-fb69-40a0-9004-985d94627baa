<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'purchase']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->moduleExistThenDie('purchase');
        $res = [
            [
                'name' => '供应商管理',
                'identity' => 'purchase:supplier',
                'action' => [
                    ['name' => '供应商管理', 'identity' => 'purchase:supplier:list', 'comment' => ''],
                    ['name' => '创建供应商', 'identity' => 'purchase:supplier:create', 'comment' => '']
                ]
            ],
            [
                'name' => '物资管理',
                'identity' => 'purchase:goods',
                'action' => [
                    ['name' => '物资管理', 'identity' => 'purchase:goods:list', 'comment' => ''],
                ]
            ],
            [
                'name' => '物资类型管理',
                'identity' => 'purchase:goodstype',
                'action' => [
                    ['name' => '物资类型管理', 'identity' => 'purchase:goodstype:list', 'comment' => ''],
                    ['name' => '创建物资类型', 'identity' => 'purchase:goodstype:create', 'comment' => '']
                ]
            ],
            [
                'name' => '采购订单',
                'identity' => 'purchase:order',
                'action' => [
                    ['name' => '待采购查询', 'identity' => 'purchase:order:wait', 'comment' => ''],
                    ['name' => '采购订单管理', 'identity' => 'purchase:order:list', 'comment' => ''],
                    ['name' => '创建采购订单', 'identity' => 'purchase:order:create', 'comment' => ''],
                    ['name' => '采购订单查询', 'identity' => 'purchase:order:search', 'comment' => '']
                ]
            ],
            [
                'name' => '外委管理',
                'identity' => 'purchase:orderww',
                'action' => [
                    ['name' => '外委计划', 'identity' => 'purchase:orderww:plan', 'comment' => ''],
                    ['name' => '外委加工单管理', 'identity' => 'purchase:orderww:list', 'comment' => ''],
                    ['name' => '创建外委加工单', 'identity' => 'purchase:orderww:create', 'comment' => ''],
                    ['name' => '外委加工单查询', 'identity' => 'purchase:orderww:search', 'comment' => '']
                ]
            ],
            [
                'name' => '采购入库',
                'identity' => 'purchase:instock',
                'action' => [
                    ['name' => '采购入库管理', 'identity' => 'purchase:instock:list', 'comment' => ''],
                    ['name' => '创建采购入库', 'identity' => 'purchase:instock:create', 'comment' => ''],
                    ['name' => '采购入库查询', 'identity' => 'purchase:instock:search', 'comment' => '']
                ]
            ],
            [
                'name' => '其他入库',
                'identity' => 'purchase:other',
                'action' => [
                    ['name' => '其他入库管理', 'identity' => 'purchase:other:list', 'comment' => ''],
                    ['name' => '创建其他入库', 'identity' => 'purchase:other:create', 'comment' => ''],
                    ['name' => '其他入库查询', 'identity' => 'purchase:other:search', 'comment' => '']
                ]
            ],
            [
                'name' => '领料出库',
                'identity' => 'purchase:outstock',
                'action' => [
                    ['name' => '领料出库管理', 'identity' => 'purchase:outstock:list', 'comment' => ''],
                    ['name' => '创建领料出库', 'identity' => 'purchase:outstock:create', 'comment' => ''],
                    ['name' => '领料出库查询', 'identity' => 'purchase:outstock:search', 'comment' => '']
                ]
            ],
            [
                'name' => '付款管理',
                'identity' => 'purchase:pay',
                'action' => [
                    ['name' => '付款管理', 'identity' => 'purchase:pay:list', 'comment' => ''],
                    ['name' => '创建付款', 'identity' => 'purchase:pay:create', 'comment' => ''],
                    ['name' => '付款查询', 'identity' => 'purchase:pay:search', 'comment' => '']
                ]
            ],
            [
                'name' => '采购用料计划',
                'identity' => 'purchase:plan',
                'action' => [
                    ['name' => '采购用料计划', 'identity' => 'purchase:plan:list', 'comment' => '']
                ]
            ],
            [
                'name' => '采购申请',
                'identity' => 'purchase:request',
                'action' => [
                    ['name' => '采购申请管理', 'identity' => 'purchase:request:list', 'comment' => ''],
                    ['name' => '创建采购申请', 'identity' => 'purchase:request:create', 'comment' => ''],
                    ['name' => '采购申请查询', 'identity' => 'purchase:request:search', 'comment' => '']
                ]
            ],
            [
                'name' => '盘点管理',
                'identity' => 'purchase:inventory',
                'action' => [
                    ['name' => '盘点管理', 'identity' => 'purchase:inventory:list', 'comment' => ''],
                    ['name' => '创建盘点', 'identity' => 'purchase:inventory:create', 'comment' => ''],
                    ['name' => '盘点查询', 'identity' => 'purchase:inventory:search', 'comment' => '']
                ]
            ],
            [
                'name' => '开票管理',
                'identity' => 'purchase:invoice',
                'action' => [
                    ['name' => '开票管理', 'identity' => 'purchase:invoice:list', 'comment' => ''],
                    ['name' => '开票查询', 'identity' => 'purchase:invoice:search', 'comment' => '']
                ]
            ],
            [
                'name' => '库存查询',
                'identity' => 'purchase:stock',
                'action' => [
                    ['name' => '实时库存查询', 'identity' => 'purchase:stock:list', 'comment' => ''],
                    ['name' => '库存履历查询', 'identity' => 'purchase:stock:search', 'comment' => '']
                ]
            ],
            [
                'name' => '外委入库管理',
                'identity' => 'purchase:wwinstock',
                'action' => [
                    ['name' => '外委入库管理', 'identity' => 'purchase:wwinstock:list', 'comment' => ''],
                    ['name' => '创建外委入库', 'identity' => 'purchase:wwinstock:create', 'comment' => ''],
                    ['name' => '外委入库查询', 'identity' => 'purchase:wwinstock:search', 'comment' => '']
                ]
            ],
            [
                'name' => '外委出库管理',
                'identity' => 'purchase:wwoutstock',
                'action' => [
                    ['name' => '外委出库管理', 'identity' => 'purchase:wwoutstock:list', 'comment' => ''],
                    ['name' => '创建外委出库', 'identity' => 'purchase:wwoutstock:create', 'comment' => ''],
                    ['name' => '外委出库查询', 'identity' => 'purchase:wwoutstock:search', 'comment' => '']
                ]
            ]
        ];

        $ret = new JsonData();
        $this->db->begin();
        try {
            $this->makePackage('purchase', '采购模块', '1.0', '提供采购模块功能');
            $module = $this->makeModule('purchase', '采购模块');
            foreach ($res as $controller) {
                $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                foreach ($controller['action'] as $action) {
                    $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage(), $e->getTraceAsString());
            $ret->message = '发生错误';
        }
        $ret->emptyIsOk();
        die(json_encode($ret,JSON_UNESCAPED_UNICODE));
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}