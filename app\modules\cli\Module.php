<?php

namespace Envsan\Modules\Cli;

use Phalcon\Config;
use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\ModuleDefinitionInterface;

class Module implements ModuleDefinitionInterface
{
    /**
     * Registers an autoloader related to the module
     *
     * @param DiInterface $di
     */
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'Envsan\Modules\Cli\Tasks' => __DIR__ . '/tasks/',
            'Envsan\Modules\Cli\Util' => __DIR__ . '/util/',
            'Envsan\Modules\Cli\Service' => __DIR__ . '/service/',
            'Envsan\Modules\Sys\Model' => APP_PATH . '/modules/sys/model/',
            'Envsan\Modules\Sys\Service' => APP_PATH . '/modules/sys/service/',
        ]);

        $loader->register();
    }

    /**
     * Registers services related to the module
     *
     * @param DiInterface $di
     */
    public function registerServices(DiInterface $di)
    {
        if (file_exists(__DIR__ . '/config/config.php')) {
            $override = new Config(include __DIR__ . '/config/config.php');;
            $config = $di->get('config');

            if ($config instanceof Config) {
                $config->merge($override);
            }
            else {
                $config = $override;
            }
        }

        $di->setShared('log', function () use ($di) {
            $config = $di->get('config');
            $logger = new \Phalcon\Logger\Adapter\File($config->application->logDir . 'cli-' . date("Ymd") . '.log');
            return $logger;
        });
    }
}
