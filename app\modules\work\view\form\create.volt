{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    表单名称<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="name" class="form-control" v-model="name" required/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonForm }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName()=='edit' %}
                var url= '{{ url('work/form/edit/'~form.uid) }}';
                {% else %}
                var url= '{{ url('work/form/create') }}';
                {% endif %}

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });
</script>
