<?php
namespace Envsan\Modules\Quality\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Quality\Model\QualityTemplate;
use Envsan\Modules\Quality\Service\TemplateService;
use Envsan\Modules\Quality\Util\Constant;

/**
 * @name('质量检验模版')
 */
class TemplateController extends SuperController
{
    /**
     * @name('管理')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new TemplateService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->types = Constant::$template_types;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new TemplateService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new QualityTemplate())->toArray();
        $jrow['form_data'] = [];
        $jrow['select_type'] = '';
        $jrow['input_list'] = Constant::$formula_input;
        $jrow['real_val'] = '';
        $jrow['formula_val'] = '';
        $jrow['formula_item'] = Constant::$data_template;
        $this->view->types = Constant::$template_types;
        $this->view->dataTemplate = Constant::$data_template;
        $this->view->inputTypes = Constant::$input_types;
        $this->view->jsonData = json_encode($jrow);
    }

    /**
     * @acl({'link':'quality:template:create'})
     */
    public function editAction($uid)
    {
        $s = new TemplateService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
        $jrow['select_type'] = '';
        $jrow['input_list'] = Constant::$formula_input;
        $jrow['real_val'] = '';
        $jrow['standard_val'] = '';
        $jrow['formula_val'] = '';
        $jrow['formula_item'] = Constant::$data_template;
        $this->view->types = Constant::$template_types;
        $this->view->dataTemplate = Constant::$data_template;
        $this->view->inputTypes = Constant::$input_types;
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->pick('template/create');
    }

    /**
     * @acl({'link':'quality:template:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new TemplateService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}