<div class="col-sm-{{ extDataViewCnt }}"  v-for="ext_item,ext_key in {{extDataViewName}}">
    <div class="form-group">
        <label class="col-sm-4 control-label">
            <span v-text="ext_item.title"></span>
        </label>
        <div class="col-sm-8">
            <input v-if="ext_item.type == 1 || ext_item.type == 3 || ext_item.type == 6 || ext_item.type == 7 || ext_item.type == 8" type="text" class="form-control" :name="ext_key" v-model="ext_item.value" readonly>
            <div v-if="ext_item.type == 2" class="input-group">
                <input type="number" class="form-control"  :name="ext_key" v-model="ext_item.value" readonly>
                <span class="input-group-addon" v-text="ext_item.unit"></span>
            </div>
            <textarea v-if="ext_item.type == 5" style="resize: none;" class="form-control" rows="3" :name="ext_key" v-model="ext_item.value" readonly></textarea>
            <div v-if="ext_item.type == 4" class="form-control" style="background-color: #EEF1F5">
                <template v-for="value,idx in ext_item.values">
                    <span v-text="value"></span>
                    <span v-if="ext_item.values.length -1 > idx">,</span>
                </template>
            </div>
        </div>
    </div>
</div>