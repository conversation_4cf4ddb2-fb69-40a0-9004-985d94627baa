<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\OssUtil;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Model\MesShipField;
use Envsan\Modules\Mes\Model\MesShipType;
use Envsan\Modules\Mes\Service\ShipService;
use Envsan\Modules\Mes\Util\Constant;
use Envsan\Modules\Sys\Service\CommonService;
use Envsan\Modules\Sys\Service\DictService;
use Envsan\Modules\Sys\Service\GroupService;

/**
 * @name('工艺')
 */
class ShipController extends SuperController
{

    /**
     * @name('标准字段管理')
     */
    public function fieldlistAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ShipService();
            $builder = $s->selectAllField();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

    /**
     * @name('类型管理列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ShipService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->planTypes = Constant::$ship_plan_types;
    }

    /**
     * @acl({'link':'mes:ship:list'})
     */
    public function createAction()
    {
        $rs = new ShipService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new MesShipType())->toArray();
        $this->view->planTypes = Constant::$ship_plan_types;
        $this->view->jsonForm = json_encode($jrow);
    }

    /**
     * @acl({'link':'mes:ship:list'})
     */
    function editAction($uid)
    {
        $rs = new ShipService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
        $jrow['data'] = ConstantUtil::$form_data_template;
        $jrow['select_value'] = '';
        $jrow['select_id'] = '';
        $jrow['add_flag'] = 1;
        $jrow['input_types'] = $rs->selectFieldList();
        $this->view->planTypes = Constant::$ship_plan_types;
        $this->view->uid = $uid;
        $this->view->jsonForm = json_encode($jrow);
        $this->view->defaultData = ConstantUtil::$form_data_template;
    }

    /**
     * @acl({'link':'mes:ship:list'})
     */
    public function deleteAction(){
        $s = new ShipService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'mes:ship:fieldlist'})
     */
    public function fieldcreateAction()
    {
        $s = new ShipService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->createField();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new MesShipField())->toArray();
        $jrow['type'] = '';
        $jrow['select_value'] = '';
        $jrow['list'] = [];
        $this->view->input_types = ConstantUtil::$input_types;
        $this->view->jsonField = json_encode($jrow);
    }

    /**
     * @acl({'link':'mes:ship:fieldlist'})
     */
    public function fieldeditAction($id)
    {
        $s = new ShipService();
        $row = $s->selectFieldById($id);
        if (empty($row)){
            die(ErrorHelper::WRONG_INPUT);
        }
        $s = new ShipService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->updateField($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        $jrow['select_value'] = '';
        $jrow['list'] = CvtUtil::emptyToArray($jrow['list']);
        $this->view->input_types = ConstantUtil::$input_types;
        $this->view->jsonField = json_encode($jrow);
        $this->view->field_id = $jrow['id'];
        $this->view->pick('ship/fieldcreate');
    }

    /**
     * @acl({'link':'mes:ship:fieldlist'})
     */
    public function fielddeleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new ShipService();
            $ret = new JsonData();
            $ret->message = $rs->deleteFieldById();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }


}