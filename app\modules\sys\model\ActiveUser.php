<?php

namespace Envsan\Modules\Sys\Model;
use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class ActiveUser extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=false)
     */
    public $session_id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $user_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $login_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $ip;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_active_user';
    }

    public static function find($params = null)
    {
        return parent::find($params);
    }

    public static function findFirst($params = null)
    {
        return parent::findFirst($params);
    }

}
