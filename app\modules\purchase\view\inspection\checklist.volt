{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered" style="margin-bottom: 0;padding-bottom: 0">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-6 col-lg-6">
                        <h3 class="page-title">来料检验单</h3>
                    </div>
                    <div class="col-md-6 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        {{ partial('table') }}
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            name: ''
        },
        mounted: function() {
            let btn_list = [];
            btn_list.push({ type: 1, name: '查看详情' });
            app_ext_table.init({{ page_id }}, btn_list, '{{ url('purchase/inspection/checklist/json') }}');
        },
        methods: {
            search: function() {
                app_ext_table.search();
            },
            reset: function() {
                app_ext_table.reset();
            },
            excel: function() {
                app_ext_table.excel("{{ url('purchase/inspection/export') }}");
            },
            getParams: function() {
                return {}
            },
            dataView: function(type, row) {
                if (type == 1) {
                    view(row.uid);
                }
            }
        }
    });

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新增',
            type: 2,
            area: ['70em', '90%'],
            content: '{{ url('purchase/inspection/create') }}',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function edit(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '编辑',
            type: 2,
            area: ['70em', '90%'],
            content: '{{ url('purchase/inspection/edit/') }}' + uid,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function view(uid) {
        top.layer.open({
            title: '查看',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('purchase/inspection/check/') }}' + uid,
        });
    }

    function del(uid) {
        let dlg = top.layer.confirm('确认删除吗?', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('purchase/inspection/delete') }}", {uid: uid}, function(rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
                else {
                    toastr.error('操作失败!');
                }
            })
        });
    }
</script>