{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">产品名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="product_name" v-model="product_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">产品规格</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="product_model" v-model="product_model"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">工序名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="bom_name" v-model="bom_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">委外计划号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="ww_plan_code" v-model="ww_plan_code"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">生产批次</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="notice_code" v-model="notice_code"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="row row-summary">
            <div class="col-md-12" style="text-align: right">
                <button type="button" class="btn blue btn-outline btn-circle" style="margin-right: 15px;" onclick="add()">
                    <i class="fa fa-plus"></i>&nbsp;<span>添加</span>
                </button>
            </div>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('purchase/orderww/addplan/json/' ~ ids) }}"
                   data-page-size="100"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-checkbox="true"></th>
                    <th data-field="goods_code">物料编码</th>
                    <th data-field="product_name">产品名称</th>
                    <th data-field="product_model">产品规格</th>
                    <th data-field="bom_name">委外工序</th>
                    <th data-field="quantity">计划外委数量</th>
                    <th data-field="wait_out_cnt">待出库数量</th>
                    <th data-field="out_cnt">出库数量</th>
                    <th data-field="in_cnt">入库数量</th>
                    <th data-field="ww_plan_code">委外计划号</th>
                    <th data-field="notice_code">生产批次</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            product_name: '',
            product_model: '',
            bom_name: '',
            ww_plan_code: '',
            notice_code: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });
    $table.bootstrapTable();
    function codeFormatter(v, row, index) {
        return row.apply_code ?? v;
    }

    function dateFormatter(v, row, index) {
        return row.apply_date ?? v;
    }

    function add() {
        var datas = $table.bootstrapTable('getSelections');
        if (datas.length == 0) {
            alertWarning('请选择添加明细');
            return;
        }
        top.window.layer_data2 = datas;
        top.window.layer_result2 = 'ok';
        top.layer.close(top.layer.getFrameIndex(window.name));
    }

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>