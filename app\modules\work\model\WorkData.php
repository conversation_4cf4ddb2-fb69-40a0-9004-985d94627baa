<?php

namespace Envsan\Modules\Work\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class WorkData extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $pid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $main_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $type_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $type_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $form_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $form_data_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $form_data_type;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $form_data;

    /**
     *
     * @var string
     * @Column(type="string", length=300, nullable=true)
     */
    public $files;

    /**
     *
     * @var string
     * @Column(type="string", length=300, nullable=true)
     */
    public $file_list;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $anchor_data;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $anchor_users;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_list;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_data;

    /**
     *
     * @var string
     * @Column(type="string", length=255, nullable=true)
     */
    public $flow_users;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $handle_status;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $read_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $return_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $more_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $valid_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $review_type;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $abstrakt_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $hidden_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $pressing_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $create_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $create_group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'work_data';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return WorkData[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return WorkData
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
