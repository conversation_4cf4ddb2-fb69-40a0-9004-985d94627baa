{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-7">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">物资信息</span>
                    </div>
                </div>
                <div class="portlet-body" style="min-height: 700px">
                    <div class="row">
                        <div class="col-sm-4" style="padding-left: 0">
                            <div class="search-page">
                                <div class="search-bar bordered">
                                    <div>
                                        <div id="tree"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-8" style="padding-left: 0">
                            <div class="search-page form-horizontal">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">物料：</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="goods" v-model="goods">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">供应商：</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="supplier" v-model="supplier">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 15px">
                                    <thead>
                                    <tr>
                                        <th>编码</th>
                                        <th>名称</th>
                                        <th>规格</th>
                                        <th>型号</th>
                                        <th>库存</th>
                                        <th>供应商</th>
                                        <th>选择</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="row, index in goods_list" v-if="row.show == 1">
                                        <td><span v-text="row.code"></span></td>
                                        <td><span v-text="row.name"></span></td>
                                        <td><span v-text="row.spec"></span></td>
                                        <td><span v-text="row.model"></span></td>
                                        <td><span v-text="row.stock_cnt + '('+row.unit+')'"></span></td>
                                        <td><span v-text="row.supplier_name"></span></td>
                                        <td>
                                            <a href="javascript:;" @click='addList(row)'>
                                                <i class="fa fa-arrow-right"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <div class="row" v-if="goods_list.length == 0" style="text-align: center;margin-top: 15px">
                                    <span>没有数据</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-5">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">盘点信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="min-height: 625px">
                            <div id="form_data" class="row">
                                {% if dispatcher.getActionName() == 'edit' %}
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>盘点单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}

                                {% endif %}
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>盘点日期</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker-ext" placeholder="请输入盘点日期" name="inventory_date" v-model="inventory_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                        <thead>
                                        <tr>
                                            <th>编码/名称</th>
                                            <th>规格/型号</th>
                                            <th>当前库存</th>
                                            <th>盘点数量</th>
                                            <th>盘后库存</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="row, index in detail_data">
                                            <td>
                                                <span v-text="row.code"></span>
                                                <br>
                                                <span v-text="row.name"></span>
                                            </td>
                                            <td>
                                                <span v-text="row.spec"></span>
                                                <br>
                                                <span v-text="row.model"></span>
                                            </td>
                                            <td>
                                                <span v-text="row.quantity_before + '(' +row.unit+ ')'"></span>
                                            </td>
                                            <td>
                                                <span v-text="row.quantity + '(' +row.unit+ ')'"></span>
                                            </td>
                                            <td>
                                                <div class="input-group" style="width: 140px">
                                                    <input @change="quantityChange(row)" type="number" class="form-control" :name="'quantity_after' + index" v-model="row.quantity_after" placeholder="盘后库存" number="true"  maxlength="10" required>
                                                    <span class="input-group-addon" v-text="row.unit"></span>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="javascript:;" @click='delList(index)' style="color: red;font-size: 20px">
                                                    <i class="fa fa-times"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div class="row"v-if="detail_data.length == 0" style="text-align: center;margin-top: 15px">
                                        <span>没有数据</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" @click="submit" class="btn btn-primary">提交盘点</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonInventory }},
        methods: {
            save(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(1);
            },
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.saveData(2);
            },
            saveData(type){
                if (this.detail_data.length == 0){
                    toastr.error('请添加明细');
                    return;
                }
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('purchase/inventory/edit/' ~ uid) }}';
                {% else %}
                var url= '{{ url('purchase/inventory/create') }}';
                {% endif %}

                showSpin();
                $.post(url, {
                    type:type,
                    inventory_date:app.inventory_date,
                    remarks:app.remarks,
                    files:encodeURI(JSON.stringify(app.files)),
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                    detail : encodeURI(JSON.stringify(app.detail_data)).replace(/\+/g,'%2B')
                }, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },
            delFile:function (index) {
                this.files.splice(index);
            },
            addList(goods){
                for(let item of this.detail_data){
                    if (item.id == goods.id){
                        toastr.error('已存在，不能重复添加');
                        return;
                    }
                }
                this.detail_data.push({...goods,quantity_before:goods.stock_cnt,quantity:'',quantity_after:''})
            },
            delList: function(idx) {
                this.detail_data.splice(idx, 1);
            },
            goodsShow(){
                for(let item of this.goods_list){
                    item.show = 1;
                    if (this.supplier != ''){
                        if (item.supplier_name.indexOf(this.supplier) == -1){
                            item.show = 0;
                        }
                    }

                    if (this.goods != '') {
                        let name = item.code + item.name + (item.spec || '') + (item.model || '');
                        let conditions = this.goods.split(' ');
                        let show_flag = 1;
                        for (let condition of conditions) {
                            if (name.indexOf(condition) < 0) {
                                show_flag = 0;
                                break;
                            }
                        }
                        item.show = show_flag;
                    }
                }
            },
            quantityChange:function (row) {
                if (row.quantity_after == ''){
                    row.quantity = '';
                    return;
                }
                row.quantity = (parseFloat(row.quantity_after) - parseFloat(row.quantity_before)).toFixed(4);
            }
        },
        watch:{
            type_uid: function(val) {
                showSpin();
                $.post('{{ url('purchase/goodstype/change/') }}' + val, function(rs) {
                    closeSpin();
                    app.supplier = '';
                    app.goods = '';
                    app.goods_list = rs.data;
                });
            },
            supplier: function(val) {
                this.goodsShow();
            },
            goods: function(val) {
                this.goodsShow();
            }
        }
    });

    var $tree = $('#tree');

    initUpLoaderPdf('purchase_inventory');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $(document).ready(function () {
        $tree.jstree({
            "core": {"data": {{ jsonTree | json_encode}}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            app.type_uid = data.node.original.uid;
            app.type_pid = data.node.parent;
        });
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}