{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">基本信息</span>
                    </div>
                </div>
                <div class="portlet-body form" >
                    <form id="form" class="form-horizontal">
                        <div class="form-body"  style="height: 76.5vh;overflow-y: auto;overflow-x: hidden">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">生产批次号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">客户</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="customer_name" v-model="customer_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品名称</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_name" v-model="product_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品规格</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_code" v-model="product_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 15px">
                                        <tbody>
                                        <tr v-for="row, index in bom_list">
                                            <td>
                                                <div v-text="row.bom_name"></div>
                                            </td>
                                            <td>
                                                <div v-text="row.quality_template_name"></div>
                                            </td>
                                            <td>
                                                <button type="button" @click="viewDetail(row)" class="btn blue btn-outline btn-sm">查看质检详情</button>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-blue"></i>
                        <span class="caption-subject font-blue bold" v-text="bom_item.bom_name + '     ' + bom_item.quality_template_name"></span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center"></div>
                </div>
                <div class="portlet-body" style="height: 85vh;overflow-y: auto;display: flex;flex-wrap: wrap">
                    <div v-for="(quality_item,quality_idx) in quality_list" style="margin-right: 15px">
                        <div style="display: flex">
                            <div style="font-size: 18px;color: #000;padding: 5px">日期：<span v-text="quality_item.work_date"></span></div>
                            <div style="font-size: 18px;color: #000;padding: 5px">作业者：<span v-text="quality_item.staff_name"></span></div>
                        </div>
                        <div style="display: flex;border-bottom: 1px #e2e2e2 solid;border-right: 1px #e2e2e2 solid;width: 600px">
                            <div>
                                <div class="item-content">
                                    <span>检验项目</span>
                                </div>
                                <div v-for="(form_item,form_idx) in bom_item.form_data"  class="item-content" :style="{height: getFormItemHeight(form_item)}">
                                    <div style="text-align: left">
                                        <span v-text="form_item.title"></span>
                                        <span v-if="form_item.unit != ''" v-text="'('+ form_item.unit +')'"></span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="item-content">
                                    <span>尺寸规格</span>
                                </div>
                                <div v-for="(form_item,form_idx) in bom_item.form_data"  class="item-content" :style="{height: getFormItemHeight(form_item)}">
                                    <div v-if="form_item.type == 7" style="text-align: left">
                                        <div>
                                            <span v-text="'最大值:' + form_item.standard_plus"></span>
                                        </div>
                                        <div>
                                            <span v-text="'最小值:' + form_item.standard_minus"></span>
                                        </div>
                                    </div>
                                    <div v-if="form_item.type == 8" style="text-align: left">
                                        <div>
                                            <span v-text="'标准值:' + form_item.standard_val"></span>
                                        </div>
                                        <div>
                                            <span v-text="'工差+:' + form_item.standard_plus"></span>
                                        </div>
                                        <div>
                                            <span v-text="'工差-:' + form_item.standard_minus"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-for="(time_item,time_idx) in quality_item.time_data">
                                <div class="item-content">
                                    <span v-text="time_item.name"></span>
                                </div>
                                <template v-if="time_item.check_data.length == 0">
                                    <div v-for="(data,form_idx) in bom_item.form_data">
                                        <div class="item-content" v-if="data.type == 1 || data.type == 3 || data.type == 4 || data.type == 5" :style="{height: getFormItemHeight(data)}">
                                            <span v-text="data.value"></span>
                                        </div>
                                        <div v-if="data.type == 2 || data.type == 6 || data.type == 7 || data.type == 8" :style="{height: getFormItemHeight(data)}">
                                            <div class="item-content" v-for="(value, value_index) in data.values" :style="{height: getItemContentHeight(data)}">
                                                <span>&nbsp;</span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div v-for="(data,form_idx) in time_item.check_data">
                                        <div class="item-content" v-if="data.type == 1 || data.type == 3 ||data.type == 5" :style="{height: getFormItemHeight(data)}">
                                            <span v-text="data.value"></span>
                                        </div>
                                        <div  v-if="data.type == 2" :style="{height: getFormItemHeight(data)}">
                                            <div class="item-content" v-for="(value, value_index) in data.values" :style="{height: getItemContentHeight(data)}">
                                                <span v-text="value"></span>
                                            </div>
                                        </div>
                                        <div v-if="data.type == 4" :style="{height: getFormItemHeight(data)}">
                                            <div class="item-content">
                                                <template v-for="(value, value_index) in data.values">
                                                    <span v-text="value"></span>
                                                    <span v-if="value_index < data.values.length - 1">,</span>
                                                </template>
                                            </div>
                                        </div>
                                        <div v-if="data.type == 6" :style="{height: getFormItemHeight(data)}">
                                            <div class="item-content" v-for="(value, value_index) in data.values" :style="{height: getItemContentHeight(data)}">
                                                <span :style="{color : data.results[value_index] == 1 ? 'red':'#000'}" v-text="data.list[value]"></span>
                                            </div>
                                        </div>
                                        <div v-if="data.type == 7 || data.type == 8" :style="{height: getFormItemHeight(data)}">
                                            <div class="item-content" v-for="(value, value_index) in data.values" :style="{height: getItemContentHeight(data)}">
                                                <span :style="{color : data.results[value_index] == 1 ? 'red':'#000'}" v-text="value"></span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {
            viewDetail(item){
                var url= '{{ url('mes/notice/quality/'~uid) }}';
                showSpin();
                $.post(url, {
                    notice_detail_id : app.id,
                    bom_id : item.product_bom_id,
                    quality_template_id : item.id
                }, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        app.quality_list = rs.data;
                        app.bom_item = item;
                    }
                    else {
                        toastr.error(rs.message);
                    }
                })
            },
            getFormItemHeight(form_item) {
                let baseHeight = form_item.values.length == 0 ? 30 : form_item.values.length * 30;

                // 设置最小高度
                let minHeight = 30; // 默认最小高度
                if (form_item.type == 7) {
                    minHeight = 60; // type=7 最小高度60px
                } else if (form_item.type == 8) {
                    minHeight = 90; // type=8 最小高度90px
                }

                return Math.max(baseHeight, minHeight) + 'px';
            },
            getItemContentHeight(form_item) {
                // 获取总高度（去掉px单位）
                let totalHeight = parseInt(this.getFormItemHeight(form_item));
                // 获取values数量，如果为0则按1计算
                let valueCount = form_item.values.length || 1;
                // 平分高度
                let itemHeight = Math.floor(totalHeight / valueCount);

                return itemHeight + 'px';
            }
        }
    });
</script>
<style>
    .item-content{
        border-top: 1px #e2e2e2 solid;
        border-left: 1px #e2e2e2 solid;
        width: 100px;
        padding: 5px;
        text-align: center;
        height: 30px;
    }
</style>