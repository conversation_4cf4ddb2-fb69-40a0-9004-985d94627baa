<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

class SysDict extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $dict_type;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $num_val;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $sort;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $default_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $lock_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_dict';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return SysDict[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return SysDict
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
