<?php
namespace Envsan\Common\Component;

use Phalcon\Mvc\Url as UrlResolver;

// 用来解决某些时候静态资源被客户端缓存的问题
class UrlResolver2 extends UrlResolver
{
    public function getStatic($uri = null) {
        $ret = parent::getStatic($uri);
        if(!empty($ret)) {
            $config = $this->getDI()->get('config');
            return $ret . '?v=' . $config->application->staticVersion;
        }
        return $ret;
    }
}
