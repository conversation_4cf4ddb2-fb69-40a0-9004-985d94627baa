<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseSupplier;
use Phalcon\Mvc\User\Component;

class SupplierService extends Component
{
    private $page_id = 23;

    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.name,
                t1.name_as,
                t1.remarks,
                t1.ext_val
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.code');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }


    /**
     * 取得供应商的dropdown
     * @return "供应商的dropdown"
     */
    public function select4Dropdown()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.code,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.code');
        // 执行查询并直接返回数组
        return $builder->getQuery()->execute()->toArray();
    }

    public function selectById($id)
    {
        return PurchaseSupplier::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseSupplier::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function create()
    {
        $row = new PurchaseSupplier();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        $code = $this->request->getPost('code', 'tstring');
        $name = $this->request->getPost('name', 'tstring');
        $name_as = $this->request->getPost('name_as', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $ext_data = str_replace('%2B','+',urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
        if (empty($code) || empty($name) || empty($name_as)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $now = DateUtil::now();
        $user = SessionData::user();
        if ($act == 'create') {
            $field_row = PurchaseSupplier::findFirst(['del_flag = 0 and name = ?1 and owner = ?2', 'bind' => [1 => $code , 2 => $user->owner]]);
            if (!empty($field_row)){
                return '名称重复';
            }
        } else {
            $field_row = PurchaseSupplier::findFirst(['del_flag = 0 and name = ?1 and owner = ?2 and id <> ?3', 'bind' => [1 => $code , 2 => $user->owner ,3 => $row->id]]);
            if (!empty($field_row)){
                return '名称重复';
            }
        }
        $this->db->begin();
        try {
            $row->code = $code;
            $row->name = $name;
            $row->name_as = $name_as;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data),JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("MesProduct表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function delete(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->del_flag = 1;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }
}