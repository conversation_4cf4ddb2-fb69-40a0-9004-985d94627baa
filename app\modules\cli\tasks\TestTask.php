<?php

namespace Envsan\Modules\Cli\Tasks;

use DateTime;
use dibi;
use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\EncodingUtil;
use Envsan\Modules\Cli\Util\FakeSession;
use Envsan\Modules\Mws\Model\Order;
use Envsan\Modules\Mws\Model\RealposV1;
use Envsan\Modules\Mws\Model\Store;
use Phalcon\Cli\Task;

class TestTask extends Task
{
    public function mainAction()
    {
        $conn = dibi::connect([
            'driver' => 'sqlsrv',
            'host' => '**************',
            'database' => 'HospitalDust',
            'username' => 'sp',
            'password' => 'sp123',
        ]);


        $fromid = 43020;
        $rows = $conn->query('SELECT * FROM tbOrder WHERE OrderId>' . $fromid)->fetchAll();

        foreach ($rows as $row) {
            echo EncodingUtil::gbk($row->MedicineType) . PHP_EOL;
        }
    }

    public function test2Action()
    {
        $date = new DateTime('2010-1-31 12:2:56');
        echo $date->format('Y-m-d H:i:s');

    }

    public function test3Action()
    {
        try {
            $this->modelsManager->executeQuery('update Envsan\Modules\Mws\Model\Store set quantity=quantity-1 where day=1 and owner=1');
        } catch (\Exception $e) {
            echo $e->getCode();
            echo $e->getMessage();
            echo 'error';
        }
    }

    public function test4Action()
    {
        FakeSession::createSession(1, 14);

        echo DateUtil::today();
        $order = Order::findFirst(['collect_date=?1 and hospital_id=?2', 'bind' => [1 => DateUtil::today(), 2=>5347]]);

        var_dump($order->toArray());
    }

    public function test6Action()
    {
        $client = new \GuzzleHttp\Client();
        $res = $client->request('get', 'http://www.baidu.com');
        echo $res->getStatusCode();
        echo $res->getBody();
    }

    public function test7Action()
    {
        $row = RealposV1::findFirst();
        var_dump($row->toArray());
    }
}