<?php
/**
 * Created by PhpStorm.
 * User: ZhuH
 * Date: 2017/10/16
 * Time: 16:16
 */

namespace Envsan\Common\Util;


class CheckUtil
{
    public static function is_empty($str) {
        if ($str === null || $str === '') {
            return true;
        } else {
            return false;
        }
    }

    public static function isPrice($num, $maxlength = 8)
    {
        $maxval = '1';
        for ($i = 0; $i < $maxlength - 3; $i++)
        {
            $maxval .= '0';
        }
        $maxval = CvtUtil::emptyToDouble($maxval);

        return !(CheckUtil::is_empty($num) || !CheckUtil::isDecimal($num) || CvtUtil::emptyToDouble($num) >= $maxval);
    }

    public static function isWeight($num, $maxlength = 8)
    {
        $maxval = '1';
        for ($i = 0; $i < $maxlength - 4; $i++)
        {
            $maxval .= '0';
        }
        $maxval = CvtUtil::emptyToDouble($maxval);

        return !(CheckUtil::is_empty($num) || !CheckUtil::isDecimal($num) || CvtUtil::emptyToDouble($num) >= $maxval);
    }

    public static function isInt($num) {
        return preg_match('/^\d+$/', $num);
    }

    public static function isDecimal($num) {
        return preg_match('/^\d+(\.\d+)?$/', $num);
    }

    public static function isIntegerBetween0And100($num) {
        return preg_match('/^(0|[1-9][0-9]?|100)(\.[0-9]{1,2})?$/', $num);
    }

    public static function isDecimal2($num) {
        return preg_match('/^\d+(\.\d{1,2})?$/', $num);
    }

    public static function isDecimal4($num) {
        return preg_match('/^\d+(\.\d{1,4})?$/', $num);
    }

    public static function isDecimalCommon($num, $decimalDigits = null) {
        // 基本验证
        if (!is_string($num) && !is_numeric($num)) {
            return false;
        }

        $num = trim((string)$num);
        if ($num === '') {
            return false;
        }

        // 如果没有指定小数位数，使用宽松验证
        if ($decimalDigits === null) {
            // 不允许前导零（除了单个0），整数位不限
            return preg_match('/^(0|[1-9]\d*)(\.\d+)?$/', $num);
        }

        if (!is_int($decimalDigits) || $decimalDigits <= 0) {
            throw new \InvalidArgumentException('小数位数必须是正整数');
        }

        // 不允许前导零，限制小数位数
        $decimalPattern = '(\.\d{1,' . $decimalDigits . '})?';
        $fullPattern = '/^(0|[1-9]\d*)' . $decimalPattern . '$/';

        return preg_match($fullPattern, $num);
    }

    public static function isExcelColNo($val) {
        return preg_match('/^[a-zA-Z]{1,2}$/', $val);
    }

    public static function isDate($date, $format='') {
        $unixTime = strtotime($date);
        if (!$unixTime) {
            return false;
        }

        if (empty($format)) {
            $format = 'Y-m-d';
        }

        if (date($format, $unixTime) != $date) {
            return false;
        }

        return true;
    }

    public static function isDateTime($date, $format = '') {
        $unixTime = strtotime($date);
        if (!$unixTime) {
            return false;
        }

        if (empty($format)) {
            $format = 'Y-m-d H:i:s';
        }

        if (date($format, $unixTime) != $date) {
            return false;
        }

        return true;
    }

    public static function isTime($time) {
        if (empty($time))
            return false;

        if (strlen($time) != 2 && strlen($time) != 5 && strlen($time) != 8)
            return false;

        $time = substr($time.':00:00', 0, 8);
        if (!preg_match('/^[0-2][0-9]:[0-5][0-9]:[0-5][0-9]?$/', $time))
            return false;

        $vals = explode(':', $time);
        if (intval($vals[0]) > 23 || intval($vals[1]) > 59 || intval($vals[2]) > 59)
            return false;

        return true;
    }

    public static function isPlateNo($plate) {
        if (mb_strlen($plate) != 7) {
            return false;
        }

        $header = mb_substr($plate, 0, 1);
        if (!in_array($header, ConstantUtil::$plate_header)) {
            return false;
        }

        $body = mb_substr($plate, 1);
        if (!preg_match('/^[a-zA-Z]{1}[a-zA-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]?$/', $body)) {
            return false;
        }

        return true;
    }
}