<?php

namespace Envsan\Common\Model;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Phalcon\Mvc\Model;
use Envsan\Common\Service\HistoryService;

class BaseModel extends Model
{
    private $beforeSaveData = null;
    private $historyService = null;

    protected $enableHistory = false;  // 子类可以重写

    public function initialize()
    {
        // 子类可以重写这个方法添加自己的初始化逻辑
    }

    /**
     * 重写save方法，添加历史记录功能
     * 所有继承BaseModel的类都自动记录历史
     */
    public function save($data = null, $whiteList = null)
    {
        try {
            if ($this->enableHistory) {
                // === BEFORE SAVE 逻辑 ===
                $this->recordHistoryBeforeSave();
            }

            // === 调用父类的save方法 ===
            $result = parent::save($data, $whiteList);

            // === 检查保存结果，失败时抛出异常 ===
            if (!$result) {
                $this->throwSaveException();
            }

            if ($this->enableHistory) {
                // === AFTER SAVE 逻辑 ===
                $this->recordHistoryAfterSave();
            }

            return $result;

        } catch (\Exception $e) {
            // 发生异常时清理数据
            if ($this->enableHistory) {
                $this->beforeSaveData = null;
            }
            
            // 获取表名
            $tableName = $this->getSource();
            
            // 记录异常日志
            Logger::error(
                "数据库操作异常",
                "表名: {$tableName}",
                "异常信息: " . $e->getMessage(),
                "文件: " . $e->getFile() . ":" . $e->getLine(),
                "堆栈: " . $e->getTraceAsString()
            );
            
            // 抛出带表名的异常
            throw new \Exception("{$tableName}表操作失败: ");
        }
    }


    /**
     * 重写save方法，添加历史记录功能
     * 所有继承BaseModel的类都自动记录历史
     */
    public function delete()
    {
        try {
            // === 调用父类的save方法 ===
            $result = parent::delete();
            // === 检查保存结果，失败时抛出异常 ===
            if (!$result) {
                $this->throwDeleteException();
            }
            return $result;
        } catch (\Exception $e) {
            // 获取表名
            $tableName = $this->getSource();
            // 记录异常日志
            Logger::error(
                "数据库操作异常",
                "表名: {$tableName}",
                "异常信息: " . $e->getMessage(),
                "文件: " . $e->getFile() . ":" . $e->getLine(),
                "堆栈: " . $e->getTraceAsString()
            );

            // 抛出带表名的异常
            throw new \Exception("{$tableName}表操作失败: ");
        }
    }

    /**
     * 保存失败时抛出异常
     * @throws \Exception
     */
    private function throwSaveException()
    {
        $messages = [];
        foreach ($this->getMessages() as $message) {
            $messages[] = $message->getMessage();
        }

        $errorMsg = empty($messages) ? "未知保存错误" : implode("; ", $messages);

        throw new \Exception("保存失败: " . $errorMsg);
    }


    private function throwDeleteException()
    {
        $messages = [];
        foreach ($this->getMessages() as $message) {
            $messages[] = $message->getMessage();
        }

        $errorMsg = empty($messages) ? "未知删除错误" : implode("; ", $messages);

        throw new \Exception("删除失败: " . $errorMsg);
    }

    /**
     * save前的逻辑 - 保存旧数据
     */
    private function recordHistoryBeforeSave()
    {
        $modelId = $this->getModelId();

        Logger::debug('BaseModel准备保存旧数据', 'model_id=' . $modelId);
        
        // 只有UPDATE操作才需要保存旧数据
        if ($modelId) {
            $modelClass = get_class($this);
            // 从数据库重新查询获取真实的原始数据
            try {
                $originalModel = $modelClass::findFirst([
                    'del_flag = 0 and id = ?1 and owner = ?2','bind' => [1 => $modelId, 2 => SessionData::user()->owner]
                ]);

                if ($originalModel) {
                    $this->beforeSaveData = $originalModel->toArray();
                }
            } catch (\Exception $e) {
                Logger::error('BaseModel获取原始数据失败',
                    get_class($this), 
                    'id=' . $modelId,
                    $e->getMessage()
                );
            }
        }
    }

    /**
     * 保存后处理
     */
    public function recordHistoryAfterSave()
    {
        try {
            if (!$this->historyService) {
                $this->historyService = new HistoryService();
            }

            // 获取中文事件名
            $eventName = $this->getEventName();
            
            // 更新或是逻辑删除
            if ($this->beforeSaveData !== null) {
                // 更新操作 - 有旧数据，只记录变化的字段
                $changedData = $this->getChangedData($this->beforeSaveData, $this->toArray());
                $this->historyService->recordUpdate($this, null, $changedData['old'], $changedData['new'], $eventName);
            } else {
                // 创建操作 - 没有旧数据
                $modelData = $this->getChangedData([], $this->toArray());
                $this->historyService->recordCreate($this, null, $eventName, $modelData['new']);
            }
            
            // 清理临时数据
            $this->beforeSaveData = null;
            
        } catch (\Exception $e) {
            Logger::error('BaseModel记录历史失败',
                get_class($this), 
                $e->getMessage(),
                $e->getFile() . ':' . $e->getLine()
            );
            // 不抛出异常，避免影响主业务
        }
    }

    /**
     * 获取模型ID
     */
    private function getModelId()
    {
        return $this->id;
    }

    /**
     * 获取中文事件名
     */
    private function getEventName(): string
    {
        try {
            // 获取当前请求URI
            $uri = $_SERVER['REQUEST_URI'] ?? '';
            
            // 使用ApiNameService获取准确的API中文名称
            $apiNameService = \Envsan\Common\Service\ApiNameService::getInstance();
            $eventName = $apiNameService->getApiNameByUri($uri);
            
            // 如果找到映射的中文名称就使用，否则使用原始URI
            return $eventName ?: $uri;
            
        } catch (\Exception $e) {
            return $_SERVER['REQUEST_URI'] ?? '数据操作';
        }
    }



    /**
     * 获取字段注释
     */
    private function getFieldComments()
    {
        $tableName = $this->getSource();
        $dbName = $this->getDI()->get('db')->getDescriptor()['dbname'];
        
        $sql = "SELECT COLUMN_NAME, COLUMN_COMMENT FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?";
                
        $result = $this->getDI()->get('db')->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, [$dbName, $tableName]);
        
        $comments = [];
        foreach ($result as $row) {
            $comments[$row['COLUMN_NAME']] = $row['COLUMN_COMMENT'] ?: $row['COLUMN_NAME'];
        }
        
        return $comments;
    }

    /**
     * 获取变更的数据
     */
    private function getChangedData($oldData, $newData)
    {
        $fieldComments = $this->getFieldComments();
        
        // 处理旧数据
        $oldResult = [];
        foreach ($oldData as $field => $value) {
            $fieldName = isset($fieldComments[$field]) ? $fieldComments[$field] : $field;
            $oldResult[$fieldName] = $value;
        }
        
        // 处理新数据
        $newResult = [];
        foreach ($newData as $field => $value) {
            $fieldName = isset($fieldComments[$field]) ? $fieldComments[$field] : $field;
            $newResult[$fieldName] = $value;
        }
        
        return [
            'old' => $oldResult,
            'new' => $newResult
        ];
    }

    /**
     * 统一数据类型，避免字符串和数值比较问题
     */
    private function normalizeValue($value, $field = null)
    {
        // null值保持不变
        if (is_null($value)) {
            return null;
        }
        
        // 所有值都转为字符串
        return (string)$value;
    }
}