<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseWwinstock;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\PurchaseWwinstockService;
use Envsan\Modules\Purchase\Service\PurchaseWwoutstockService;

/**
 * @name('外委入库')
 */
class WwinstockController extends SuperController
{
    private $page_id = 40;
    private $search_page_id = 41;

    /**
     * @name('管理')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseWwinstockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }


    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseWwinstockService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new PurchaseWwinstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }

        $common = new CommonService();
        $table = new TableService();
        $fs = new FileService();

        $jrow = (new PurchaseWwinstock())->toArray();
        $jrow['instock_date'] = DateUtil::today();
        $jrow['order_id'] = '';
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['detail_data'] = [];
        $jrow['files'] = [];
        $jrow['bom_list'] = [];
        $jrow['notice_code'] = '';
        $jrow['bom_msg'] = '没有找到匹配的记录';
        $this->view->jsonInstock = json_encode($jrow);

        $this->view->wwArriveList = $s->getWwArriveList();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'purchase:wwinstock:create'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseWwinstockService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $common = new CommonService();
        $fs = new FileService();
        $table = new TableService();
        $ots = new PurchaseWwoutstockService();
        $ext_data = CvtUtil::emptyToArray($row->ext_data);

        $order_code = '';
        $supplier_name = '';
        $order_row = $ots->getOrderData($row->order_id);
        if (!empty($order_row)) {
            $order_code = $order_row->order_code;
            $supplier_name = $order_row->supplier_name;
        }

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($row->files);
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['detail_data'] = CvtUtil::emptyToArray($row->detail_data);
        $jrow['order_code'] = $order_code;
        $jrow['supplier_name'] = $supplier_name;
        $jrow['bom_list'] = $s->getBomList($row->id);
        $jrow['notice_code'] = '';
        $jrow['bom_msg'] = '';
        $this->view->jsonInstock = json_encode($jrow);

        $this->view->wwArriveList = $s->getWwArriveList();
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('wwinstock/create');
    }

    /**
     * @acl({'link':'purchase:wwinstock:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwinstockService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:wwinstock:create'})
     */
    public function cancelAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwinstockService();
            $ret = new JsonData();
            $ret->message = $s->cancel($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new PurchaseWwinstockService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $fs = new FileService();
        $table = new TableService();
        $ext_data = CvtUtil::emptyToArray($row->ext_data);

        $order_code = '';
        $supplier_name = '';
        $ots = new PurchaseWwoutstockService();
        $order_row = $ots->getOrderData($row->order_id);
        if (!empty($order_row)) {
            $order_code = $order_row->order_code;
            $supplier_name = $order_row->supplier_name;
        }

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($row->files);
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['detail_data'] = CvtUtil::emptyToArray($row->detail_data);
        $jrow['order_code'] = $order_code;
        $jrow['supplier_name'] = $supplier_name;
        $this->view->jsonInstock = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseWwinstockService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function bomlistAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwinstockService();
            $jsonData = new JsonData();
            $jsonData->handleResult($s->getBomList($this->request->getPost('order_id', 'tstring')));
            return json_encode($jsonData);
        }
    }

    /**
     * @skipacl
     */
    public function searchexportAction()
    {
        $s = new PurchaseWwinstockService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id, $builder);
    }
}