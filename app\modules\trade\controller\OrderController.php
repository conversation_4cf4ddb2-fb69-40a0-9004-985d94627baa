<?php
namespace Envsan\Modules\Trade\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Trade\Model\TradeOrder;
use Envsan\Modules\Trade\Service\CommonService;
use Envsan\Modules\Trade\Service\CustomerService;
use Envsan\Modules\Trade\Service\OrderService;
use Envsan\Modules\Trade\Util\Constant;
use Envsan\Modules\Work\Service\WorkService;

/**
 * @name("销售订单")
 */
class OrderController extends SuperController
{
    private $page_id = 2;
    private $detail_page_id = 3;

    /**
     * @name("管理")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderService();
            $builder = $s->selectAll('list');
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("查询")
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderService();
            $builder = $s->selectAll('search');
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("创建")
     */
    public function createAction()
    {
        $s = new OrderService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $table = new TableService();
        $jrow = (new TradeOrder())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['files'] = [];
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $common = new CommonService();
        $this->view->types = Constant::$order_types;
        $this->view->customerList = $common->getCustomerList();
        $this->view->jsonData = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'trade:order:create'})
     */
    function editAction($uid)
    {
        $s = new OrderService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);
        if ($row->status > 10){
            die(ErrorHelper::WRONG_ID);
        }

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $ss = new CustomerService();
        $customer_row = $ss->selectById($row->customer_id);
        $jrow['customer_name'] = '';
        if (!empty($customer_row)){
            $jrow['customer_name'] = $customer_row->name;
        }
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
        $this->view->detail_page_id = $this->detail_page_id;
    }

    /**
     * @acl({'link':'trade:order:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'trade:order:create'})
     */
    public function cancelAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderService();
            $ret = new JsonData();
            $ret->handleResult($s->cancelReview());
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new OrderService();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $ss = new CustomerService();
        $customer_row = $ss->selectById($row->customer_id);
        $jrow['customer_name'] = '';
        if (!empty($customer_row)){
            $jrow['customer_name'] = $customer_row->name;
        }
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
        $this->view->detail_page_id = $this->detail_page_id;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new OrderService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}