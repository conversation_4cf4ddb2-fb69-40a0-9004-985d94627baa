<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}


<section class="content">
    <div class="box">
        <div class="box-body">
            <div id="tree"></div>
        </div>
        <div class="box-footer" id="app">
            <div>
                <button class="btn btn-primary" @click="confirm"><i class="fa fa-check"></i> 确定</button>
            </div>
        </div>
    </div>
</section>

<script>
    var app = new Vue({
        el:'#app',
        data:{
            id:'',uid:'',name:''
        },
        methods:{
            confirm:function(){
                $.post('{{ url('sys/group/switch') }}', app.$data, function (rs) {
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });

    var $tree = $('#tree');

    $(document).ready(function () {
        $tree.jstree({
            'core': {'data': {{ jsonTree }}}
        }).on('loaded.jstree', function() {
            $tree.jstree('select_node', '{{ group.id }}');
        });

        $tree.on('select_node.jstree', function (e, data) {
            app.id = data.node.id;
            app.uid = data.node.original.uid;
            app.name = data.node.text;
        });

        $tree.on('dblclick.jstree', function (e) {
            app.confirm();
        });
    });
</script>
