{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}

<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">来料报检单信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 72vh;overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">报检单单号</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="inspection_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">报检日期</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="inspection_day" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">报检时间</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="inspection_date" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">报检部门</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="inspection_department" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">检验类型</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="check_type" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">采购部门</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="department_name" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">到货单号</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="receipt_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                 <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">到货日期</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="receipt_date" readonly>
                                        </div>
                                    </div>
                                </div>                               
                                 <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">到货日期</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="receipt_date" readonly>
                                        </div>
                                    </div>
                                </div> 
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">供应商</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="supplier_name" readonly>
                                        </div>
                                    </div>
                                </div> 
                               {{ partial('view') }}
                                
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">报检单明细</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>物料编码</th>
                                    <th>物料名称</th>
                                    <th>规格型号</th>
                                    <th>库存计量数量</th>
                                    <th>采购计量数量</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="inspection_list.length == 0">
                                    <td colspan="13" style="text-align: center;">没有数据</td>
                                </tr>
                                <tr v-else v-for="inspection, inspection_idx in inspection_list">
                                    <td v-text="inspection.goods_code"></td>
                                    <td v-text="inspection.goods_name"></td>
                                    <td v-text="inspection.goods_model"></td>
                                    <td v-text="inspection.quantity + '(' + inspection.goods_deputy_unit + ')'"></td>
                                    <td v-text="inspection.purchase_quantity + '(' + inspection.goods_unit + ')'"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonPurchaseInspection }},
    });
</script>