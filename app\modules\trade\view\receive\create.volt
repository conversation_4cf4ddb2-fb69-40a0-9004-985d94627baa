{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>订单</label>
                                <div class="col-sm-8">
                                    <select class="bs-select form-control" name="order_id" v-model="order_id" data-size="8" data-live-search="true" required>
                                        <option value="">请选择</option>
                                        {% for row in order_list %}
                                            <option value="{{ row.id }}">{{ row.order_code ~ ' / ' ~ row.customer_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>收款日期</label>
                                <div class="col-sm-8">
                                    <div class="input-group date dtpicker">
                                        <input type="text" class="form-control" name="receive_date" v-model="receive_date" required>
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>收款金额</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="receive_money" v-model="receive_money" maxlength="11" number="true" required>
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ partial('form') }}
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件</label>
                                <div class="col-sm-10" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item, index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                        <div>
                                            <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                删除
                                            </a>
                                        </div>
                                    </div>
                                    <div class="btn btn-outline blue" style="width: 80px;" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">备注</label>
                                <div class="col-sm-10">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" name="remarks" v-model="remarks" maxlength="200" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{ partial('uploader_file') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonTradeReceive }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('trade/receive/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('trade/receive/create') }}';
                {% endif %}

                let param = JSON.parse(JSON.stringify(this.$data));
                param.files = encodeURI(JSON.stringify(app.files));
                param.ext_data = encodeURI(JSON.stringify(this.ext_data)).replace(/\+/g, '%2B');

                showSpin();
                $.post(url, param, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                }).error(function(rs) {
                    closeSpin();
                    console.log(rs);
                });
            },
            delFile: function(index) {
                this.files.splice(index);
            }
        }
    });

    initUpLoaderPdf('order_receive');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key: getUuid(),
                url_name: rs.file_name,
                url: rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: true,
        autoclose: true,
        format:'yyyy-mm-dd',
        pickerPosition: 'bottom-left'
    }).on('changeDate', function(ev) {
        let obj = $(this).find('input');
        app[$(obj).attr('name')] = $(obj).val();
    });
</script>
{{ partial('form_script') }}