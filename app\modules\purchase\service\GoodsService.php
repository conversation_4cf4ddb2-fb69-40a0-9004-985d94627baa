<?php
namespace Envsan\Modules\Purchase\Service;

use EasyWeChat\Support\Log;
use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Purchase\Model\PurchaseGoodsType;
use Envsan\Modules\Purchase\Model\PurchaseSupplier;
use Obs\ObsClient;
use Phalcon\Mvc\User\Component;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Common\Util\UUID;
use PHPExcel_Reader_Excel2007;
use Upyun\Upyun;

class GoodsService extends BaseService
{
    public function selectAll()
    {
        $uid = $this->request->get('uid', 'tstring');
        $goods_name = $this->request->get('name', 'tstring');

        $type_ids = [-1];
        if (!empty($uid)) {
            $gts = new GoodsTypeService();
            $row = $gts->selectByUid($uid);
            if (!empty($row)) {
                $type_ids = $gts->getTypeIds($row->id);
            }
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.type_id,
                a.code,
                a.spec,
                a.model,
                a.as_name,
                a.check_flag,
                a.warning_flag,
                round(a.warning_quantity , 4) as warning_quantity,
                a.taxation,
                a.name,
                a.unit,
                a.deputy_unit,
                a.is_batch_managed,
                round(a.unit_conversion_rate, 4) as unit_conversion_rate,
                a.inventory_code,
                a.formula_val,
                round(a.price,4) as price,
                round(a.price_hs,4) as price_hs,
                a.tax_rate,
                a.update_date,
                t.name as type_name,
                t.code as goods_type_code,
                t.type,
                t.short_name,
                s.name_as as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a.type_id = t.id', 't')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a.supplier_id = s.id', 's')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 'a.update_by = t2.id', 't2')
            ->where('a.del_flag = 0 and a.owner = :owner:', ['owner'=>SessionData::ownerId()])
            ->inWhere('a.type_id', $type_ids)
            ->orderBy('a.code asc');
        if (!CheckUtil::is_empty($goods_name)) {
            $cols = ['a.code', 'a.name', 'a.spec', 'a.model'];
            $cs = new CommonService();
            $builder = $cs->getGoodsBuilder($goods_name, $cols, $builder);
        }
        return $builder;
    }

    public function searchAll()
    {
        $type_idx =  $this->request->get('type_idx');
        $goods_code = $this->request->get('code', 'tstring');
        $goods_name = $this->request->get('name', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.type_id,
                a.code,
                a.spec,
                a.model,
                a.as_name,
                a.warning_flag,
                round(a.warning_quantity , 4) as warning_quantity,
                a.main_type,
                a.taxation,
                a.formula_val,
                a.name,
                a.unit,
                a.deputy_unit,
                a.update_date,
                t.name as type_name,
                t.code as goods_type_code,
                t.type,
                t.short_name,
                t2.real_name as user_name,
                a.update_by
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a.type_id = t.id', 't')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 'a.update_by = t2.id', 't2')
            ->where('a.del_flag = 0 and a.owner = :owner:',['owner'=>SessionData::ownerId()])
            ->orderBy('a.id desc');

        if (!CheckUtil::is_empty($goods_code)) {
            $builder->andWhere("a.code like ?1", [1 => "%$goods_code%"]);
        }
        if (!CheckUtil::is_empty($goods_name)) {
            $builder->andWhere("a.name like ?2", [2 => "%$goods_name%"]);
        }
        if (!CheckUtil::is_empty($type_idx)) {
            $builder->inWhere("a.type_id", $type_idx);
        }
        return $builder;
    }

    public function create()
    {
        return $this->saveGoods();
    }

    public function update($id)
    {
        return $this->saveGoods($id);
    }

    /**
     * 保存商品（新增或更新）
     * @param int|null $id 商品ID，null表示新增，有值表示更新
     * @return array
     */
    private function saveGoods($id = null)
    {
        return $this->executeInTransaction(function () use ($id) {

            $isUpdate = !is_null($id);
            
            // ===== 1. 获取请求参数 =====
            // 存货名称
            $name = $this->request->getPost('name', 'tstring');
            $spec = $this->request->getPost('spec', 'tstring');
            $cd = $this->request->getPost('cd', 'tstring');
            $kd = $this->request->getPost('kd', 'tstring');
            $hd = $this->request->getPost('hd', 'tstring');
            $weight = $this->request->getPost('weight', 'tstring');
            // 规格型号
            $model = $this->request->getPost('model', 'tstring');
            // 库存单位
            $deputy_unit = $this->request->getPost('deputy_unit', 'tstring');
            // 采购单位
            $unit = $this->request->getPost('unit', 'tstring');
            // 供应商
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');
            // 税目
            $taxation = $this->request->getPost('taxation', 'tstring');
            // 存货简称
            $as_name = $this->request->getPost('as_name', 'tstring');
            // 是否库存预警
            $warning_flag = $this->request->getPost('warning_flag', 'tstring');
            // 库存预警数量
            $warning_quantity = $this->request->getPost('warning_quantity', 'tstring');
            // 是否入库检验
            $check_flag = $this->request->getPost('check_flag', 'tstring');
            // 公式
            $formula_list = $this->request->getPost('formula_list');
            $formula_flag = $this->request->getPost('formula_flag', 'tstring');
            $price = $this->request->getPost('price', 'tstring');
            $tax_rate = $this->request->getPost('tax_rate', 'tstring');
            // 质检模板
            $quality_template_id = $this->request->getPost('quality_template_id', 'tstring');
            // 质检数据
            $quality_check_data = urldecode($this->request->getPost('form_data', 'tstring'));
            // 存货代码
            $inventory_code = $this->request->getPost('inventory_code', 'tstring');
            // 单元转换率
            $unit_conversion_rate = $this->request->getPost('unit_conversion_rate', 'tstring');
            // 新增时需要的额外字段
            if (!$isUpdate) {
                // 存货编码
                $code = $this->request->getPost('code', 'tstring');
                // 父类的id
                $type_id = $this->request->getPost('type_id', 'tstring');
            }

            // ===== 2. 验证数据 =====
            // 通用必填字段验证
            if (empty($name) || $warning_flag === '') {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }

            // 新增时的额外必填字段验证
            if (!$isUpdate) {
                if (empty($code) || empty($unit) || empty($deputy_unit) || 
                    empty($type_id) || empty($unit_conversion_rate)) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
            }

            // 尺寸验证
            if (!empty($cd) && !CheckUtil::isDecimal($cd)) {
                return $this->error('无效的长度');
            }
            if (!empty($kd) && !CheckUtil::isDecimal($kd)) {
                return $this->error('无效的宽度');
            }
            if (!empty($hd) && !CheckUtil::isDecimal($hd)) {
                return $this->error('无效的厚度');
            }
            if (!empty($weight) && !CheckUtil::isDecimal($weight)) {
                return $this->error('无效的单重');
            }

            // 价格验证
            if (!empty($price) && !CheckUtil::isDecimal($price)) {
                return $this->error('无效的单价');
            }
            Logger::error(3);
            // 税率验证
            if (!empty($tax_rate)) {
                if ($isUpdate) {
                    if (!CheckUtil::isDecimal($tax_rate)) {
                        return $this->error('无效的税率');
                    }
                } else {
                    if (!CheckUtil::isIntegerBetween0And100($tax_rate)) {
                        return $this->error('无效的税率');
                    }
                }
            }

            // 新增时的单位换算率验证
            if (!$isUpdate && !CheckUtil::isDecimal($unit_conversion_rate)) {
                return $this->error('无效的单位换算率');
            }

            // 新增时的特殊验证
            if (!$isUpdate) {
                // 验证类型
                $type_row = PurchaseGoodsType::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $type_id]]);
                if (empty($type_row)) {
                    return $this->error('物资类型错误');
                }
                
                // 验证编码
                if (!CheckUtil::isInt($code)) {
                    return $this->error('编码错误');
                }
                
                // 检查编码重复
                $goods_code = $type_row->code . $code;
                $repeat_goods = PurchaseGoods::findFirst(['del_flag = 0 and code = ?1', 'bind' => [1 => $goods_code]]);
                if (!empty($repeat_goods)) {
                    return $this->error('该编码已存在');
                }
            }
            // ===== 3. 准备数据模型 =====
            $user = SessionData::user();
            $now = DateUtil::now();
            
            if ($isUpdate) {
                $row = PurchaseGoods::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
                if (empty($row)) {
                    return $this->error('商品不存在');
                }
            } else {
                $row = new PurchaseGoods();
                // 新增时的特有字段
                $row->uid = UUID::make();
                $row->type_id = $type_row->id;
                $row->code = $type_row->code . $code;
                $row->is_batch_managed = 0;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }

            // 计算配方值
            $formula_val = '';
            if (!empty($formula_list)) {
                foreach ($formula_list as $formula_item) {
                    $formula_val .= $formula_item['l'];
                }
            }

            // 处理税率
            if ($isUpdate) {
                $tax_rate = CvtUtil::zeroToNull(round(CvtUtil::emptyToDouble($tax_rate) / 100, 2));
            } else {
                $tax_rate = CvtUtil::percentToDecimal($tax_rate);
            }

            // ===== 4. 共同字段赋值 =====
            $row->name = $name;
            //
            $row->taxation = CvtUtil::blankToNull($taxation);
            $row->deputy_unit = CvtUtil::blankToNull($deputy_unit);
            $row->supplier_id = CvtUtil::blankToNull($supplier_id);
            $row->model = CvtUtil::blankToNull($model);
            $row->check_flag = CvtUtil::emptyToInt($check_flag);
            $row->warning_flag = CvtUtil::emptyToInt($warning_flag);
            $row->warning_quantity = CvtUtil::blankToNull($warning_quantity);
            $row->as_name = CvtUtil::blankToNull($as_name);
            $row->spec = CvtUtil::blankToNull($spec);
            $row->cd = CvtUtil::blankToNull($cd);
            $row->kd = CvtUtil::blankToNull($kd);
            $row->hd = CvtUtil::blankToNull($hd);
            $row->weight = CvtUtil::blankToNull($weight);
            $row->formula_list = CvtUtil::arrayToNull($formula_list);
            $row->formula_val = CvtUtil::blankToNull($formula_val);
            $row->price = CvtUtil::blankToNull($price);
            $row->tax_rate = $tax_rate;
            $row->quality_template_id = CvtUtil::blankToNull($quality_template_id);
            $row->inventory_code = $inventory_code;
            $row->unit_conversion_rate = $unit_conversion_rate;
            $row->unit = $unit;
            $row->update_date = $now;
            $row->update_by = $user->id;
            Logger::error(5);
            // 新增时计算含税单价
            if (!$isUpdate) {
                $row->price_hs = round(CvtUtil::emptyToDouble($price) * (1 + $tax_rate), 2);
            }

            // ===== 5. 处理质检数据 =====
            $f_data = CvtUtil::emptyToArray($quality_check_data);
            foreach ($f_data as &$form_item) {
                if ($form_item['type'] == 2 || $form_item['type'] == 7 || $form_item['type'] == 8) {
                    $form_item['values'] = [];
                    $form_item['results'] = [];
                    for ($i = 0; $i < CvtUtil::emptyToInt($form_item['input_cnt']); $i++) {
                        $form_item['values'][] = '';
                        $form_item['results'][] = 0;
                    }
                } else if ($form_item['type'] == 6) {
                    $form_item['values'] = [];
                    $form_item['results'] = [];
                    for ($i = 0; $i < CvtUtil::emptyToInt($form_item['input_cnt']); $i++) {
                        $form_item['values'][] = 0;
                        $form_item['results'][] = 0;
                    }
                }
            }
            $row->quality_check_data = CvtUtil::arrayToNull($f_data);
            // ===== 6. 保存数据 =====
            $row->save();
            // ===== 7. 处理配方同步 =====
            if ($formula_flag == 1) {
                $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseGoods';
                $phql .= ' SET formula_list = ?1, formula_val = ?2';
                $phql .= ' WHERE del_flag = 0 and type_id = ?3';
                $result = $this->modelsManager->executeQuery($phql, [
                    1 => $row->formula_list,
                    2 => $row->formula_val,
                    3 => $row->type_id
                ]);

                if (!$result->success()) {
                    return $this->error('PurchaseGoods表更新失败');
                }
            }
            return $isUpdate ? null : $row; // 更新返回null，新增返回row对象
        });
    }

    public function selectGoodsById($id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.type_id,
                a.code,
                a.name,
                a.unit,
                a.as_name,
                a.check_flag,
                a.warning_flag,
                round(a.warning_quantity , 4) as warning_quantity,
                a.quality_template_id,
                a.quality_check_data,
                a.spec,
                round(a.cd, 2) as cd,
                round(a.kd, 2) as kd,
                round(a.hd, 2) as hd,
                round(a.weight, 3) as weight,
                a.model,
                a.taxation,
                a.deputy_unit,
                a.supplier_id,
                a.update_date,
                a.formula_list,
                round(a.price, 2) as price,
                round(a.price_hs, 2) as price_hs,
                round(a.tax_rate,2) * 100 as tax_rate,
                a.inventory_code,
                a.is_batch_managed,
                a.unit_conversion_rate,
                t.name as type_name,
                t.code as goods_type_code,
                t.type,
                t.short_name,
                a.update_by
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a.type_id = t.id', 't')
            ->where('a.del_flag = 0 and a.id = '.$id);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0) {
            return false;
        }
        return $rows[0];
    }

    public function deleteById($id)
    {
        $row = PurchaseGoods::findFirst(['id = ?1', 'bind' => [1 => $id]]);
        if ($row == false || $row->del_flag == 1) {
            return true;
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save();
    }

    public function setDetail($rows)
    {
        foreach ($rows as $row){
            $type_row = PurchaseGoodsType::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$row['type_id']]]);
            $row['type_name'] = $type_row->name;
        }
        return $rows;
    }

    public function selectById($id)
    {
        return PurchaseGoods::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function importExcel()
    {
        $ret = [];
        $ret['status'] = 'error';

        $files = $this->request->getUploadedFiles();
        if (count($files) == 0) {
            $ret['message'] = '无效的文件';
            return $ret;
        }
        $file = $files[0];
        $ext = pathinfo($file->getName(), PATHINFO_EXTENSION);
        if (empty($ext) || mb_strtolower($ext) !== 'xlsx') {
            $ret['message'] = '只支持xlsx后缀的文件';
            return $ret;
        }

        $path = $this->config->upyun->uploadDir.UUID::make().'.'.$ext;
        if ($file->moveTo($path)) {
            $ret = $this->readExcel($path);
            unlink($path);
            return $ret;
        }

        $ret['message'] = '导入失败';
        return $ret;
    }

    private function readExcel($path)
    {
        $ret = [];
        $ret['status'] = 'error';
        $ret['list'] = [];

        $excel_reader = new PHPExcel_Reader_Excel2007();
        $excel_reader->setReadDataOnly(true);
        $excel = $excel_reader->load($path);
        $sheet = $excel->getSheet();
        if ($sheet == null) {
            $ret['message'] = '无效的模板';
            return $ret;
        }

        $allRow = $sheet->getHighestRow();
        for ($i = 3; $i <= $allRow; $i++)
        {
            $goods_ret = $this->getGoodsRow($sheet, $i);
            if ($goods_ret['status'] == 'error') {
                return $goods_ret;
            }

            $ret['list'][] = $goods_ret['data'];
        }

        if (count($ret['list']) == 0) {
            $ret['message'] = '未找到数据';
            return $ret;
        }

        $ret['status'] = 'ok';
        return $ret;
    }

    private function getGoodsRow($sheet, $row_no)
    {
        $ret = [];
        $ret['status'] = 'error';
        $ret['data'] = [];

        $code = trim($sheet->getCell('A'.$row_no)->getValue());
        $name = trim($sheet->getCell('B'.$row_no)->getValue());
        $spec = trim($sheet->getCell('C'.$row_no)->getValue());
        $model = trim($sheet->getCell('D'.$row_no)->getValue());
        $quantity = trim($sheet->getCell('E'.$row_no)->getValue());
        $unit = trim($sheet->getCell('F'.$row_no)->getValue());
        $supplier_name = trim($sheet->getCell('G'.$row_no)->getValue());
        $remarks = trim($sheet->getCell('H'.$row_no)->getValue());

        if (CheckUtil::is_empty($code) && CheckUtil::is_empty($name) && CheckUtil::is_empty($spec) && CheckUtil::is_empty($model)
            && CheckUtil::is_empty($quantity) && CheckUtil::is_empty($unit) && CheckUtil::is_empty($supplier_name)
            && CheckUtil::is_empty($remarks)) {
            $ret['status'] = 'ok';
            return $ret;
        }

        if (CheckUtil::is_empty($code)) {
            $ret['message'] = '分类编码不能为空：行'.$row_no;
            return $ret;
        }
        if (CheckUtil::is_empty($name)) {
            $ret['message'] = '名称不能为空：行'.$row_no;
            return $ret;
        }
        if (CheckUtil::is_empty($spec)) {
            $ret['message'] = '规格不能为空：行'.$row_no;
            return $ret;
        }
        if (CheckUtil::is_empty($model)) {
            $ret['message'] = '型号不能为空：行'.$row_no;
            return $ret;
        }
        if (CheckUtil::is_empty($unit)) {
            $ret['message'] = '单位不能为空：行'.$row_no;
            return $ret;
        }
        if (!CheckUtil::is_empty($quantity) && !CheckUtil::isInt($quantity)) {
            $ret['message'] = '数量必须是正整数：行'.$row_no;
            return $ret;
        }

        $type_row = PurchaseGoodsType::findFirst(['del_flag = 0 and code = ?1', 'bind' => [1 => $code]]);
        if (empty($type_row)) {
            $ret['message'] = '分类编码不存在：行'.$row_no;
            return $ret;
        }

        $data = [
            'id' => '',
            'uid' => '',
            'name' => $name,
            'code' => $type_row->code.' '.$type_row->name,
            'unit' => $unit,
            'spec' => $spec,
            'model' => $model,
            'quantity' => $quantity,
            'supplier_id' => '',
            'supplier_name' => $supplier_name,
            'remarks' => $remarks,
            'type_id' => $type_row->id,
            'row_no' => $row_no
        ];

        $goods_row = PurchaseGoods::findFirst([
            'del_flag = 0 and name = ?1 and spec = ?2 and model = ?3 and code like ?4',
            'bind' => [1 => $name, 2 => $spec, 3 => $model, 4 => "$code%"]
        ]);
        if (!empty($goods_row)) {
            $data['id'] = $goods_row->id;
            $data['uid'] = $goods_row->uid;
            $data['code'] = $goods_row->code;
            $data['unit'] = $goods_row->unit;
            $data['supplier_id'] = $goods_row->supplier_id;
        }

        if (!empty($data['supplier_id'])) {
            $supplier_row = PurchaseSupplier::findFirst(['id = ?1', 'bind' => [1 => $data['supplier_id']]]);
            if (!empty($supplier_row)) {
                $data['supplier_name'] = $supplier_row->name;
            }

        } else if (!CheckUtil::is_empty($supplier_name)) {
            $supplier_row = PurchaseSupplier::findFirst(['del_flag = 0 and name = ?1', 'bind' => [1 => $supplier_name]]);
            if (!empty($supplier_row)) {
                $data['supplier_id'] = $supplier_row->id;
            }
        }

        $ret['status'] = 'ok';
        $ret['data'] = $data;
        return $ret;
    }

    public function getGoodsCode($type_code)
    {
        $goods_row = PurchaseGoods::findFirst([
            'code like ?1 and length(code) = ?2',
            'bind' => [1 => "$type_code%", 2 => strlen($type_code) + 4],
            'order' => 'code desc'
        ]);
        if (empty($goods_row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($goods_row->code, strlen($type_code))) + 1;
        }
        return substr('0000'.$cnt, -4);
    }

    public function selectGoodsByGoodsType()
    {
        $goods_code = $this->request->get('code', 'tstring');
        $goods_name = $this->request->get('name', 'tstring');
        $goods_spec = $this->request->get('spec', 'tstring');
        $goods_model = $this->request->get('model', 'tstring');

        $type_code = '-1';
        $row = PurchaseGoodsType::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $this->request->get('uid', 'tstring')]]);
        if (!empty($row)) {
            $type_code = $row->code;
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.type_id,
                a.code,
                ifnull(a.spec,\'\') as spec,
                ifnull(a.model,\'\') as model,
                a.as_name,
                a.warning_flag,
                round(a.warning_quantity , 4) as warning_quantity,
                a.taxation,
                a.name,
                a.unit,
                a.update_date,
                t.name as type_name,
                t.code as goods_type_code,
                t.type,
                t.short_name,
                t2.real_name as user_name,
                a.update_by
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a.type_id = t.id', 't')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 'a.update_by = t2.id', 't2')
            ->where('a.del_flag = 0 and a.code like :code:', ['code' => "$type_code%"])
            ->orderBy('a.code');

        if (!CheckUtil::is_empty($goods_code)) {
            $builder->andWhere("a.code like ?1", [1 => "%$goods_code%"]);
        }
        if (!CheckUtil::is_empty($goods_name)) {
            $builder->andWhere("a.name like ?2", [2 => "%$goods_name%"]);
        }
        if (!CheckUtil::is_empty($goods_spec)) {
            $builder->andWhere("a.spec like ?3", [3 => "%$goods_spec%"]);
        }
        if (!CheckUtil::is_empty($goods_model)) {
            $builder->andWhere("a.model like ?4", [4 => "%$goods_model%"]);
        }
        return $builder;
    }

    public function updateSize()
    {
        $goods_rows = PurchaseGoods::find(['del_flag = 0 and spec is not null and cd is null']);

        foreach ($goods_rows as $goods_row)
        {
            $spec = $goods_row->spec;
            if (preg_match('/^\d+(\.\d+)?\*\d+(\.\d+)?\*\d+(\.\d+)?$/', $spec)) {
                $spec_arr = explode('*', $spec);
                $goods_row->hd = $spec_arr[0];
                $goods_row->cd = $spec_arr[1];
                $goods_row->kd = $spec_arr[2];
                $goods_row->save();
            }
        }
        return 'ok';
    }

    public function getCheckList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.name,
                t1.form_data
            ')
            ->addFrom('Envsan\Modules\Quality\Model\QualityTemplate', 't1')
            ->where('t1.del_flag = 0 and t1.type = 2')
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row){
            $row['form_data'] = CvtUtil::emptyToArray($row['form_data']);
        }
        return $rows;
    }
}