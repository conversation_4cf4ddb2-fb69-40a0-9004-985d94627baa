(self["webpackChunksfp_ext"] = self["webpackChunksfp_ext"] || []).push([["src_view_process_plan_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=script&lang=js":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=script&lang=js ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var vuedraggable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! vuedraggable */ "./node_modules/vuedraggable/dist/vuedraggable.umd.js");
/* harmony import */ var vuedraggable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vuedraggable__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ __webpack_exports__["default"] = ({
  name: "plan",
  components: {
    draggable: (vuedraggable__WEBPACK_IMPORTED_MODULE_0___default())
  },
  data() {
    return {
      uid: '',
      sxdm: '',
      batch_sel_idx: -1,
      batch_sel_item: {},
      batch_list: [],
      data_list: [],
      task_list: [],
      staff_list: [],
      hour_list: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      editable: true,
      isDragging: false,
      delayedDragging: false
    };
  },
  created() {
    let uid = this.$route.query.uid || '';
    this.initData(uid);
  },
  methods: {
    initData(uid) {
      this.$http.post('mes/process/plan', {
        uid: uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.batch_sel_idx = -1;
          this.batch_sel_item = {};
          this.data_list = [];
          this.uid = rs.data.uid;
          this.batch_list = rs.data.batch_list;
          this.task_list = rs.data.task_list;
          this.staff_list = rs.data.staff_list;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    selBatch(idx) {
      let item = this.batch_list[idx];
      this.$http.post('mes/process/datalist', {
        uid: this.uid,
        product_id: item.product_id
      }).then(rs => {
        if (rs.status == 'ok') {
          this.batch_sel_idx = idx;
          this.batch_sel_item = item;
          this.data_list = rs.data.data_list;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    backBatch() {
      this.batch_sel_idx = -1;
      this.batch_sel_item = {};
      this.data_list = [];
    },
    onMove({
      relatedContext,
      draggedContext
    }) {
      const relatedElement = relatedContext.element;
      const draggedElement = draggedContext.element;
      if (relatedElement) {
        if (relatedElement.type == 1 || relatedElement.type == 2) {
          return false;
        }
        if (draggedElement.type == 0) {
          return false;
        }
        if (!this.task_list[relatedElement.task_idx].ship_ids.includes(draggedElement.ship_id)) {
          return false;
        }
      } else {
        return false;
      }
      return true;
    },
    taskChange(task_idx, elem) {
      let task = this.task_list[task_idx];
      let item = null;
      if (elem['added']) {
        item = elem.added;
        let type = task.list[item.newIndex].type;
        task.list[item.newIndex].task_idx = task_idx;
        task.list[item.newIndex].type = 2;
        task.list.splice(item.newIndex + 1, 1);
        this.saveData(task_idx, item.newIndex, task.id, type, item.element);
      } else if (elem['moved']) {
        item = elem.moved;
        this.saveData(task_idx, item.newIndex, task.id, 3, item.element);
      } else if (elem['removed']) {
        item = elem.removed;
        task.list.splice(item.oldIndex, 0, {
          type: 0,
          task_idx: task_idx,
          cnt: 1
        });
      }
      this.batch_sel_item.task_plan_cnt = this.getPlanConut(item.element.product_id);
    },
    saveData(task_idx, idx, task_id, type, obj) {
      if (type == 1) {
        this.$http.post('mes/process/save', {
          uid: this.uid,
          idx: idx,
          task_id: task_id,
          ...obj
        }).then(rs => {
          if (rs.status == 'ok') {
            let data = this.task_list[task_idx].list[idx];
            let hour = parseFloat(this.task_list[task_idx].hour);
            let cnt = parseFloat(this.task_list[task_idx].cnt);
            this.task_list[task_idx].hour = hour + parseFloat(obj.ship_hour * obj.p_cnt);
            this.task_list[task_idx].cnt = cnt + parseFloat(obj.p_cnt);
            data.cnt = rs.time_cnt;
            data.id = rs.plan_id;
            data.team_task_detail_id = task_id;
            this.selBatch(this.batch_sel_idx);
          } else {
            this.$message.error(rs.message);
            this.initData(this.uid);
          }
        }).catch(() => {
          this.$message.error('未知错误');
        });
      } else {
        this.$http.post('mes/process/change', {
          uid: this.uid,
          idx: idx,
          task_id: task_id,
          plan_id: obj.id
        }).then(rs => {
          if (rs.status == 'ok') {
            let data = this.task_list[task_idx].list[idx];
            let hour = parseFloat(this.task_list[task_idx].hour);
            let cnt = parseFloat(this.task_list[task_idx].cnt);
            this.task_list[task_idx].hour = hour + parseFloat(obj.ship_hour * obj.p_cnt);
            this.task_list[task_idx].cnt = cnt + parseFloat(obj.p_cnt);
            for (let item of this.task_list) {
              if (item.id == obj.team_task_detail_id) {
                item.hour -= parseFloat(obj.ship_hour * obj.p_cnt);
                item.cnt -= parseFloat(obj.p_cnt);
                break;
              }
            }
            data.cnt = rs.time_cnt;
            data.team_task_detail_id = task_id;
          } else {
            this.$message.error(rs.message);
          }
        }).catch(() => {
          this.$message.error('未知错误');
        });
      }
    },
    deleteTask(task_idx, list_idx) {
      let data = this.task_list[task_idx].list[list_idx];
      this.$http.post('mes/process/remove', {
        uid: this.uid,
        plan_id: data.id
      }).then(rs => {
        if (rs.status == 'ok') {
          this.task_list[task_idx].hour -= parseFloat(data.ship_hour * data.p_cnt);
          this.task_list[task_idx].cnt -= parseFloat(data.p_cnt);
          this.task_list[task_idx].list.splice(list_idx, 1, {
            type: 0,
            task_idx: task_idx,
            cnt: 1
          });
          if (this.batch_sel_idx != -1) {
            this.selBatch(this.batch_sel_idx);
          }
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    selectTask(task_idx, list_idx) {
      let product_id = this.task_list[task_idx].list[list_idx].product_id;
      let sel = 1;
      if (this.task_list[task_idx].list[list_idx].sel === 1) {
        sel = 0;
      }
      for (let item of this.task_list) {
        for (let k of item.list) {
          if (k.type > 0) {
            if (k.product_id == product_id) {
              k.sel = sel;
            }
          }
        }
      }
    },
    clearAll() {
      this.$http.post('mes/process/clear', {
        uid: this.uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.$message.success('操作成功！');
          this.initData(this.uid);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    planAuto() {},
    staffSelect(task_idx, staff_idx) {
      let task = this.task_list[task_idx];
      let staff_id = null;
      let staff = null;
      if (staff_idx > -1) {
        staff = this.staff_list[staff_idx];
        staff_id = staff.id;
      }
      this.$http.post('mes/process/setstaff', {
        uid: this.uid,
        task_id: task.id,
        staff_id: staff_id
      }).then(rs => {
        if (rs.status == 'ok') {
          this.$message.success('操作成功！');
          if (staff == null) {
            this.task_list[task_idx].staff_id = null;
            this.task_list[task_idx].name = null;
            this.task_list[task_idx].skill_level = 0;
            this.task_list[task_idx].rest_flag = 0;
          } else {
            this.task_list[task_idx].staff_id = staff.id;
            this.task_list[task_idx].name = staff.name;
            this.task_list[task_idx].skill_level = staff.skill_level;
            this.task_list[task_idx].rest_flag = staff.rest_flag;
          }
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    plusCount(data) {
      if (data.list[0].p_cnt >= data.list[0].g_cnt) {
        return;
      }
      data.list[0].p_cnt++;
    },
    minusCount(data) {
      if (data.list[0].p_cnt <= 1) {
        return;
      }
      data.list[0].p_cnt--;
    },
    getPlanConut(product_id) {
      let cnt = 0;
      for (let item of this.task_list) {
        for (let k of item.list) {
          if (k.type > 0) {
            if (k.product_id == product_id) {
              if (k.p_cnt > cnt) {
                cnt = k.p_cnt;
              }
            }
          }
        }
      }
      return cnt;
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 0,
        group: "description",
        disabled: !this.editable,
        ghostClass: "ghost"
      };
    }
  },
  watch: {
    isDragging(newValue) {
      if (newValue) {
        this.delayedDragging = true;
        return;
      }
      this.$nextTick(() => {
        this.delayedDragging = false;
      });
    },
    sxdm(v) {
      if (v == '') {
        for (let item of this.batch_list) {
          item.show_flag = 1;
        }
        return;
      }
      for (let item of this.batch_list) {
        if (item.sxdm.indexOf(v) > -1) {
          item.show_flag = 1;
        } else {
          item.show_flag = 0;
        }
      }
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=template&id=b94eec2c&scoped=true":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=template&id=b94eec2c&scoped=true ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "form-group form-group-lg panel panel-default"
  }, [_vm._m(0), _c('div', {
    staticClass: "panel-body"
  }, [_c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "width": "100%",
      "justify-content": "flex-start"
    }
  }, [_c('div', {
    staticStyle: {
      "width": "400px",
      "margin-right": "10px",
      "padding": "10px",
      "background-color": "#F2F2F2",
      "height": "90vh",
      "overflow-y": "auto",
      "padding-bottom": "50px"
    }
  }, [_c('div', {
    staticStyle: {
      "padding-bottom": "10px",
      "width": "100%",
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "space-between"
    }
  }, [_c('el-popconfirm', {
    attrs: {
      "confirm-button-text": "确认",
      "confirm-button-type": "danger",
      "cancel-button-text": "取消",
      "cancel-button-type": "default",
      "icon": "el-icon-info",
      "icon-color": "red",
      "title": "确认要清空吗？"
    },
    on: {
      "confirm": _vm.clearAll
    }
  }, [_c('el-button', {
    attrs: {
      "slot": "reference",
      "type": "danger"
    },
    slot: "reference"
  }, [_vm._v("一键清空")])], 1), _c('el-input', {
    attrs: {
      "placeholder": "请输入属性代码"
    },
    model: {
      value: _vm.sxdm,
      callback: function ($$v) {
        _vm.sxdm = $$v;
      },
      expression: "sxdm"
    }
  })], 1), _vm.batch_sel_idx == -1 ? _c('div', _vm._l(_vm.batch_list, function (batch, batch_idx) {
    return _c('div', {
      directives: [{
        name: "show",
        rawName: "v-show",
        value: batch.show_flag == 1,
        expression: "batch.show_flag == 1"
      }],
      key: batch_idx,
      staticStyle: {
        "margin-bottom": "10px",
        "border": "1px solid #E2E2E2",
        "padding": "5px",
        "border-radius": "5px",
        "background-color": "#FFF"
      }
    }, [_c('el-descriptions', {
      staticClass: "margin-top",
      attrs: {
        "title": batch.sxdm,
        "column": 2,
        "size": "mini"
      }
    }, [_c('template', {
      slot: "extra"
    }, [_c('el-button', {
      attrs: {
        "type": "primary",
        "size": "small"
      },
      on: {
        "click": function ($event) {
          return _vm.selBatch(batch_idx);
        }
      }
    }, [_vm._v("选择")])], 1), _c('el-descriptions-item', {
      attrs: {
        "label": "计划入库"
      }
    }, [_vm._v(_vm._s(batch.plan_cnt) + " 支")]), _c('el-descriptions-item', {
      attrs: {
        "label": "入库完成"
      }
    }, [_vm._v(_vm._s(batch.instock_cnt) + " 支")]), _c('el-descriptions-item', {
      attrs: {
        "label": "厂家"
      }
    }, [_vm._v(_vm._s(batch.customer_name))]), _c('el-descriptions-item', {
      attrs: {
        "label": "材质"
      }
    }, [_vm._v(_vm._s(batch.material_name))]), _c('el-descriptions-item', {
      attrs: {
        "label": "规格"
      }
    }, [_vm._v(_vm._s(batch.spec))]), _c('el-descriptions-item', {
      attrs: {
        "label": "待产辊数"
      }
    }, [_vm._v(_vm._s(batch.data_cnt) + " 支")]), batch.task_plan_cnt > 0 ? _c('el-descriptions-item', {
      attrs: {
        "label": "排产辊数",
        "content-style": "color:red"
      }
    }, [_vm._v(_vm._s(batch.task_plan_cnt) + " 支")]) : _vm._e()], 2)], 1);
  }), 0) : _c('div', [_c('div', {
    staticStyle: {
      "margin-bottom": "10px",
      "border": "1px solid #E2E2E2",
      "padding": "5px",
      "border-radius": "5px",
      "background-color": "#FFF"
    }
  }, [_c('el-descriptions', {
    staticClass: "margin-top",
    attrs: {
      "title": _vm.batch_sel_item.sxdm,
      "column": 2,
      "size": "mini"
    }
  }, [_c('template', {
    slot: "extra"
  }, [_c('el-button', {
    attrs: {
      "type": "info",
      "plain": "",
      "size": "small"
    },
    on: {
      "click": _vm.backBatch
    }
  }, [_vm._v("返回")])], 1), _c('el-descriptions-item', {
    attrs: {
      "label": "计划入库"
    }
  }, [_vm._v(_vm._s(_vm.batch_sel_item.plan_cnt) + " 支")]), _c('el-descriptions-item', {
    attrs: {
      "label": "入库完成"
    }
  }, [_vm._v(_vm._s(_vm.batch_sel_item.instock_cnt) + " 支")]), _c('el-descriptions-item', {
    attrs: {
      "label": "客户"
    }
  }, [_vm._v(_vm._s(_vm.batch_sel_item.customer_name))]), _c('el-descriptions-item', {
    attrs: {
      "label": "材质"
    }
  }, [_vm._v(_vm._s(_vm.batch_sel_item.material_name))]), _c('el-descriptions-item', {
    attrs: {
      "label": "规格"
    }
  }, [_vm._v(_vm._s(_vm.batch_sel_item.spec))]), _c('el-descriptions-item', {
    attrs: {
      "label": "待产辊数"
    }
  }, [_vm._v(_vm._s(_vm.batch_sel_item.data_cnt) + " 支")]), _vm.batch_sel_item.task_plan_cnt > 0 ? _c('el-descriptions-item', {
    attrs: {
      "label": "排产辊数",
      "content-style": "color:red"
    }
  }, [_vm._v(_vm._s(_vm.batch_sel_item.task_plan_cnt) + " 支")]) : _vm._e()], 2)], 1), _vm._l(_vm.data_list, function (data, data_idx) {
    return _c('div', {
      key: data_idx,
      staticStyle: {
        "width": "100%",
        "padding-top": "10px"
      }
    }, [_c('div', {
      staticStyle: {
        "background-color": "#FFF",
        "padding": "10px 10px 10px 10px"
      }
    }, [_c('div', {
      staticStyle: {
        "width": "260px",
        "padding": "0",
        "display": "flex",
        "flex-direction": "row",
        "font-weight": "600"
      }
    }, [_c('div', {
      staticStyle: {
        "width": "60%",
        "line-height": "24px"
      }
    }, [_c('span', {
      domProps: {
        "textContent": _vm._s(data.ship_name)
      }
    })]), _c('div', {
      staticStyle: {
        "width": "20%",
        "line-height": "24px"
      }
    }, [_vm._v(" " + _vm._s(data.g_cnt + ' 支') + " ")]), (data.ship_type == 1 || data.ship_type == 9) && data.g_cnt > 0 ? _c('div', {
      staticStyle: {
        "width": "20%",
        "line-height": "24px"
      }
    }, [_c('a', {
      on: {
        "click": function ($event) {
          return _vm.plusCount(data);
        }
      }
    }, [_c('i', {
      staticClass: "el-icon-circle-plus-outline",
      staticStyle: {
        "font-size": "24px"
      }
    })]), _c('a', {
      on: {
        "click": function ($event) {
          return _vm.minusCount(data);
        }
      }
    }, [_c('i', {
      staticClass: "el-icon-remove-outline",
      staticStyle: {
        "font-size": "24px"
      }
    })])]) : _vm._e()]), (data.ship_type == 1 || data.ship_type == 9) && data.g_cnt > 0 ? _c('div', [_c('draggable', _vm._b({
      staticClass: "list-group",
      staticStyle: {
        "margin-bottom": "0 !important",
        "min-height": "40px"
      },
      attrs: {
        "handle": ".handle",
        "tag": "div",
        "move": _vm.onMove,
        "name": 'data_id_' + data.ship_id
      },
      on: {
        "start": function ($event) {
          _vm.isDragging = true;
        },
        "end": function ($event) {
          _vm.isDragging = false;
        }
      },
      model: {
        value: data.list,
        callback: function ($$v) {
          _vm.$set(data, "list", $$v);
        },
        expression: "data.list"
      }
    }, 'draggable', _vm.dragOptions, false), _vm._l(data.list, function (element, idx) {
      return _c('div', {
        key: idx,
        staticClass: "list-group-item",
        staticStyle: {
          "width": "260px",
          "padding": "0",
          "display": "flex",
          "flex-direction": "row",
          "height": "40px"
        }
      }, [_c('div', {
        staticStyle: {
          "width": "10%",
          "line-height": "40px",
          "text-align": "center"
        }
      }, [_c('i', {
        staticClass: "fa fa-hand-o-up handle"
      })]), _c('div', {
        staticStyle: {
          "width": "55%",
          "line-height": "40px"
        }
      }, [_c('span', {
        domProps: {
          "textContent": _vm._s(element.ship_name)
        }
      })]), _c('div', {
        staticStyle: {
          "width": "20%",
          "line-height": "40px"
        }
      }, [_vm._v(" " + _vm._s(element.p_cnt + ' 支') + " ")]), _c('div', {
        staticStyle: {
          "width": "15%",
          "line-height": "40px"
        }
      }, [_c('span', {
        staticStyle: {
          "color": "green"
        },
        domProps: {
          "textContent": _vm._s(element.ship_hour)
        }
      }), _vm._v(" H ")])]);
    }), 0)], 1) : _vm._e()])]);
  })], 2)]), _c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "width": "100%",
      "height": "90vh",
      "overflow": "auto",
      "justify-content": "flex-start",
      "padding-bottom": "500px"
    }
  }, [_c('div', {
    staticStyle: {
      "width": "150px",
      "margin-top": "20px",
      "border-top": "1px solid #E2E2E2",
      "border-left": "1px solid #E2E2E2"
    }
  }, _vm._l(_vm.task_list, function (task, task_idx) {
    return _c('div', {
      key: task_idx
    }, [_c('div', {
      staticStyle: {
        "margin-top": "10px",
        "background-color": "#FFDBCA",
        "width": "100%",
        "height": "40px",
        "display": "flex",
        "flex-direction": "row"
      }
    }, [_c('div', {
      staticStyle: {
        "width": "60px",
        "padding-left": "5px"
      }
    }, [_c('div', [_c('span', {
      staticStyle: {
        "font-size": "13px"
      },
      domProps: {
        "textContent": _vm._s(task.equ_code)
      }
    })]), _c('div', [_c('el-dropdown', {
      attrs: {
        "trigger": "click"
      },
      on: {
        "command": function ($event) {
          return _vm.staffSelect(task_idx, arguments[0]);
        }
      }
    }, [_c('span', {
      staticClass: "el-dropdown-link",
      domProps: {
        "textContent": _vm._s(task.staff_id == null ? '无' : task.name)
      }
    }), _c('el-dropdown-menu', {
      attrs: {
        "slot": "dropdown"
      },
      slot: "dropdown"
    }, [_c('el-dropdown-item', {
      attrs: {
        "command": "-1"
      }
    }, [_vm._v(" 取消选择 ")]), _vm._l(_vm.staff_list, function (staff, staff_idx) {
      return _c('el-dropdown-item', {
        key: staff_idx,
        attrs: {
          "command": staff_idx
        }
      }, [_vm._v(" " + _vm._s(staff.name) + " "), staff.rest_flag == 1 ? _c('span', {
        staticStyle: {
          "color": "red"
        }
      }, [_vm._v("(休)")]) : _vm._e()]);
    })], 2)], 1)], 1)]), _c('div', {
      staticStyle: {
        "font-size": "16px",
        "font-weight": "600",
        "vertical-align": "center",
        "line-height": "40px",
        "width": "40px"
      }
    }, [_c('span', {
      domProps: {
        "textContent": _vm._s(task.cnt)
      }
    }), _vm._v(" 支 ")]), _c('div', {
      staticStyle: {
        "font-size": "16px",
        "font-weight": "600",
        "vertical-align": "center",
        "line-height": "40px"
      }
    }, [task.rest_flag == 1 ? _c('div', [_c('span', {
      staticStyle: {
        "color": "red"
      }
    }, [_vm._v("休")])]) : _c('div', [_c('span', {
      domProps: {
        "textContent": _vm._s(task.hour)
      }
    }), _vm._v(" h ")])])])]);
  }), 0), _c('div', {
    staticStyle: {
      "min-width": "1200px",
      "min-height": "800px",
      "position": "relative"
    }
  }, [_c('div', {
    staticStyle: {
      "position": "absolute",
      "top": "0",
      "left": "0"
    }
  }, [_c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "padding-left": "10px",
      "padding-bottom": "5px",
      "color": "#000",
      "height": "20px"
    }
  }, _vm._l(_vm.hour_list, function (hour) {
    return _c('div', {
      key: hour,
      staticStyle: {
        "margin-left": "80px",
        "width": "20px",
        "text-align": "center"
      }
    }, [_c('span', {
      domProps: {
        "textContent": _vm._s(hour + 'H')
      }
    })]);
  }), 0), _c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "border-left": "1px solid #E2E2E2"
    }
  }, _vm._l(_vm.hour_list, function (hour) {
    return _c('div', {
      key: hour,
      staticStyle: {
        "min-height": "800px",
        "width": "100px",
        "border-right": "1px solid #E2E2E2",
        "border-top": "1px solid #E2E2E2"
      }
    });
  }), 0)]), _c('div', {
    staticStyle: {
      "z-index": "999",
      "padding-top": "20px"
    }
  }, _vm._l(_vm.task_list, function (task, task_idx) {
    return _c('div', {
      key: task_idx
    }, [_c('div', {
      staticStyle: {
        "margin-top": "10px"
      }
    }, [_c('draggable', _vm._b({
      staticClass: "list-group",
      staticStyle: {
        "position": "relative",
        "margin-bottom": "0 !important",
        "height": "40px",
        "background-color": "#F2F2F2"
      },
      attrs: {
        "handle": ".handle",
        "tag": "div",
        "move": _vm.onMove
      },
      on: {
        "change": function ($event) {
          return _vm.taskChange(task_idx, arguments[0]);
        }
      },
      model: {
        value: task.list,
        callback: function ($$v) {
          _vm.$set(task, "list", $$v);
        },
        expression: "task.list"
      }
    }, 'draggable', _vm.dragOptions, false), [_vm._l(task.list, function (element, idx) {
      return [element.type == 0 ? _c('div', {
        key: task.staff_id + '' + idx,
        staticClass: "list-group-item",
        staticStyle: {
          "position": "absolute",
          "top": "0",
          "height": "40px",
          "width": "25px",
          "padding": "0",
          "border": "0",
          "background": "transparent"
        },
        style: {
          'left': idx * 25 + 'px'
        }
      }) : _c('div', {
        key: task.equ_id + '' + idx,
        staticClass: "list-group-item",
        staticStyle: {
          "position": "absolute",
          "top": "0",
          "height": "40px",
          "display": "flex",
          "flex-direction": "row",
          "padding": "0",
          "justify-content": "space-between"
        },
        style: {
          'left': idx * 25 + 'px',
          'width': element.cnt * 25 + 'px !important',
          'borderColor': element.sel == 1 ? 'red' : ''
        }
      }, [_c('div', {
        staticStyle: {
          "width": "15px"
        }
      }, [_c('i', {
        staticClass: "fa fa-hand-o-up handle"
      }), _c('el-tooltip', {
        attrs: {
          "placement": "bottom",
          "effect": "light"
        }
      }, [_c('div', {
        attrs: {
          "slot": "content"
        },
        slot: "content"
      }, [_vm._v(" " + _vm._s(element.sxdm)), _c('br'), _vm._v(" " + _vm._s(element.customer_name)), _c('br'), _vm._v(" " + _vm._s(element.spec)), _c('br'), _vm._v(" " + _vm._s(element.ship_name)), _c('br'), _vm._v(" " + _vm._s(element.p_cnt) + " 支"), _c('br'), _vm._v(" " + _vm._s(element.ship_hour * element.p_cnt) + " h"), _c('br'), _c('div', {
        staticStyle: {
          "width": "100%",
          "display": "flex",
          "flex-direction": "row",
          "justify-content": "space-between",
          "margin-top": "5px"
        }
      }, [_c('i', {
        staticClass: "fa fa-check",
        staticStyle: {
          "color": "green",
          "cursor": "pointer",
          "font-size": "18px"
        },
        on: {
          "click": function ($event) {
            return _vm.selectTask(task_idx, idx);
          }
        }
      }), _c('a', {
        on: {
          "click": function ($event) {
            return _vm.deleteTask(task_idx, idx);
          }
        }
      }, [_c('i', {
        staticClass: "fa fa-close",
        staticStyle: {
          "color": "red",
          "cursor": "pointer",
          "font-size": "18px"
        }
      })])])]), _c('i', {
        staticClass: "fa fa-search"
      })])], 1), _c('div', {
        staticStyle: {
          "display": "flex",
          "flex-direction": "row",
          "width": "100%"
        }
      }, [_c('div', [_c('div', [_c('span', {
        domProps: {
          "textContent": _vm._s(element.sxdm)
        }
      })]), _c('div', [_c('span', [_vm._v(_vm._s(element.ship_name))])])]), _c('div', {
        staticStyle: {
          "line-height": "40px",
          "padding": "0 10px 0 10px",
          "white-space": "nowrap",
          "text-overflow": "ellipsis"
        }
      }, [_c('span', [_vm._v(_vm._s(element.p_cnt))]), _vm._v(" 支 "), _c('span', [_vm._v(_vm._s(element.p_cnt * element.ship_hour))]), _vm._v(" H ")])])])];
    })], 2)], 1)]);
  }), 0)])])])])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "panel-heading"
  }, [_c('h3', {
    staticClass: "panel-title"
  }, [_vm._v("机加工班次排产计划")])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "./node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.flip-list-move[data-v-b94eec2c] {\r\n  -webkit-transition: -webkit-transform 0.5s;\r\n  transition: -webkit-transform 0.5s;\r\n  transition: transform 0.5s;\r\n  transition: transform 0.5s, -webkit-transform 0.5s;\n}\n.no-move[data-v-b94eec2c] {\r\n  -webkit-transition: -webkit-transform 0s;\r\n  transition: -webkit-transform 0s;\r\n  transition: transform 0s;\r\n  transition: transform 0s, -webkit-transform 0s;\n}\n.ghost[data-v-b94eec2c] {\r\n  opacity: 0.5;\r\n  background: #c8ebfb;\n}\n.list-group[data-v-b94eec2c] {\r\n  min-height: 20px;\n}\n.list-group-item i[data-v-b94eec2c] {\r\n  cursor: pointer;\n}\r\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css */ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.id, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = (__webpack_require__(/*! !../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js")["default"])
var update = add("2d6a1fba", content, false, {"sourceMap":false,"shadowMode":false});
// Hot Module Replacement
if(false) // removed by dead control flow
{}

/***/ }),

/***/ "./src/view/process/plan.vue":
/*!***********************************!*\
  !*** ./src/view/process/plan.vue ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _plan_vue_vue_type_template_id_b94eec2c_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plan.vue?vue&type=template&id=b94eec2c&scoped=true */ "./src/view/process/plan.vue?vue&type=template&id=b94eec2c&scoped=true");
/* harmony import */ var _plan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plan.vue?vue&type=script&lang=js */ "./src/view/process/plan.vue?vue&type=script&lang=js");
/* harmony import */ var _plan_vue_vue_type_style_index_0_id_b94eec2c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css */ "./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");



;


/* normalize component */

var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _plan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _plan_vue_vue_type_template_id_b94eec2c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _plan_vue_vue_type_template_id_b94eec2c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "b94eec2c",
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/view/process/plan.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/view/process/plan.vue?vue&type=script&lang=js":
/*!***********************************************************!*\
  !*** ./src/view/process/plan.vue?vue&type=script&lang=js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css":
/*!*******************************************************************************************!*\
  !*** ./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_style_index_0_id_b94eec2c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css */ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=style&index=0&id=b94eec2c&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_style_index_0_id_b94eec2c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_style_index_0_id_b94eec2c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_style_index_0_id_b94eec2c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_style_index_0_id_b94eec2c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./src/view/process/plan.vue?vue&type=template&id=b94eec2c&scoped=true":
/*!*****************************************************************************!*\
  !*** ./src/view/process/plan.vue?vue&type=template&id=b94eec2c&scoped=true ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_template_id_b94eec2c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_template_id_b94eec2c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_plan_vue_vue_type_template_id_b94eec2c_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plan.vue?vue&type=template&id=b94eec2c&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/process/plan.vue?vue&type=template&id=b94eec2c&scoped=true");


/***/ })

}]);
//# sourceMappingURL=src_view_process_plan_vue.d8233fb9.js.map