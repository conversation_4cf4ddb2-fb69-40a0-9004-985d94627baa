<?php
namespace Envsan\Modules\Cli\Tasks;

class MainTask extends \Phalcon\Cli\Task
{
    private $map;
    private $module;
    private $Module;
    private $model;
    private $Model;
    private $ModelWithoutModule;
    private $arrIds;

    public function mainAction()
    {
        echo "Congratulations! You are now flying with Phalcon CLI!";
    }

    public function selAction()
    {

    }

    private function makeOutputDir()
    {
        return $this->config->cli->output.date("Ymdhis");
    }

    private function commonReplace($txt)
    {
        $txt = str_replace('{{Module}}', $this->Module, $txt);
        $txt = str_replace('{{Model}}', $this->Model, $txt);
        $txt = str_replace('{{ModelWithoutModule}}', $this->ModelWithoutModule, $txt);

        $txt = str_replace('{{module}}', $this->module, $txt);
        $txt = str_replace('{{model}}', $this->model, $txt);
        return $txt;
    }

    private function createController($dir, $flag = '')
    {
        if ($flag == 2) {
            $base_path = $this->config->cli->temp2;
        } else {
            $base_path = $this->config->cli->temp;
        }
        $src = $base_path.'controller.txt';
        $txt = file_get_contents($src);


        $txt = $this->commonReplace($txt);
        $dest = $dir."/$this->ModelWithoutModule"."Controller.php";
        file_put_contents($dest, $txt);
    }

    private function createService($dir, $flag = '')
    {
        if ($flag == 2) {
            $base_path = $this->config->cli->temp2;
        } else {
            $base_path = $this->config->cli->temp;
        }
        $src = $base_path.'service.txt';
        $txt = file_get_contents($src);

        $txt = $this->commonReplace($txt);

        $dest = $dir."/$this->Model"."Service.php";
        file_put_contents($dest, $txt);
    }

    private function createListView($dir, $flag = '')
    {
        if ($flag == 2) {
            $base_path = $this->config->cli->temp2;
        } else {
            $base_path = $this->config->cli->temp;
        }
        $src = $base_path.'list.volt';
        $txt = file_get_contents($src);

        $txt = $this->commonReplace($txt);

        $r = '';
        foreach ($this->arrIds as $item){
            $r .= "                    <th data-field=\"$item\">$item</th>".PHP_EOL;
        }

        $txt = str_replace('{{field}}', $r, $txt);

        $dest = $dir."/list.volt";
        file_put_contents($dest, $txt);
    }

    private function createCreateView($dir, $flag = '')
    {
        if ($flag == 2) {
            $base_path = $this->config->cli->temp2;
        } else {
            $base_path = $this->config->cli->temp;
        }
        $src = $base_path.'create.volt';
        $txt = file_get_contents($src);

        $txt = $this->commonReplace($txt);

        $dest = $dir."/create.volt";
        file_put_contents($dest, $txt);
    }

    private function createViewView($dir, $flag = '')
    {
        if ($flag == 2) {
            $base_path = $this->config->cli->temp2;
        } else {
            $base_path = $this->config->cli->temp;
        }
        $src = $base_path.'view.volt';
        $txt = file_get_contents($src);

        $txt = $this->commonReplace($txt);

        $dest = $dir."/view.volt";
        file_put_contents($dest, $txt);
    }

    private function parseArgs($args)
    {
        foreach ($args as $arg){
            $a = explode('=', $arg);
            $this->map["$a[0]"]=$a[1];
        }

        $this->Module = $this->map['module'];
        $this->module = strtolower($this->Module);
        $this->Model = $this->map['model'];
        $this->ModelWithoutModule = str_replace($this->Module, '', $this->Model);
        $this->model = strtolower(($this->ModelWithoutModule));
        $this->ModelWithoutModule = ucfirst($this->model);

        $ids = $this->map['ids'];
        $this->arrIds = explode(',', $ids);

        if(empty($this->module) || empty($this->model) || count($this->arrIds)<=0){
            die('Missing args'.PHP_EOL);
        }
    }

    public function makeAction($args)
    {
        print('Begin....'.PHP_EOL);
        $this->map = array();
        $this->parseArgs($args);
        $dir = $this->makeOutputDir();
        mkdir($dir);
        $this->createController($dir);
        $this->createService($dir);
        $this->createListView($dir);
        $this->createCreateView($dir);
        print('Done!'.PHP_EOL);
    }

    public function make2Action($args)
    {
        print('Begin....'.PHP_EOL);
        $this->map = array();
        $this->parseArgs($args);
        $dir = $this->makeOutputDir();
        mkdir($dir);
        $this->createController($dir, 2);
        $this->createService($dir, 2);
        $this->createListView($dir, 2);
        $this->createCreateView($dir, 2);
        $this->createViewView($dir, 2);
        print('Done!'.PHP_EOL);
    }

    public function useageAction()
    {
        echo 'php bootstrap_cli.php main make  module=fuck model=girl ids="id,name,address,contact,update_date"';
    }

    public function riAction()
    {
        echo 'sdfsdfsdf';
    }
}
