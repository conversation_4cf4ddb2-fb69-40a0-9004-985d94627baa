<?php

namespace Envsan\Common\Base;

use Envsan\Common\Data\SessionData;
use Phalcon\Mvc\Controller;
use Phalcon\Mvc\Model\Query\BuilderInterface;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;
use Phalcon\Paginator\Adapter\NativeArray as PaginatorArray;
use Phalcon\Paginator\Adapter\QueryBuilder as PageBuilder;

class ControllerBase extends Controller
{
    public function initialize()
    {
        $this->buildAssets();
    }

    protected function appendTitle($title)
    {
        $this->tag->setTitle($title . ' | ' . $this->config->application->frontTitle);
    }

    protected function forward($uri)
    {
        $uriParts = explode('/', $uri);
        $params = array_slice($uriParts, 2);
        $parts = count($uriParts);
        $action = '';

        if ($parts >= 2) {
            $controller = $uriParts[0];
            $action = $uriParts[1];
        }
        else if ($parts == 1) {
            $controller = $uriParts[0];
        }
        else {
            $controller = 'index';
        }

        return $this->dispatcher->forward(
            array(
                'controller' => $controller,
                'action' => $action,
                'params' => $params
            )
        );
    }

    protected function redirect($uri)
    {
        return $this->response->redirect($uri);
    }

    protected function buildAssets()
    {
        $this->assets->collection('js');
        $this->assets->collection('css');

        $this->assets->collection('validate')
            ->addJs('static/global/plugins/validate/jquery.validate.min.js')
            ->addJs('static/global/plugins/validate/messages_zh.min.js')
            ->addJs('static/global/plugins/validate/set-defaults.js');
    }

    protected function setJsonResponse()
    {
        $this->view->disable();
        $this->response->setContentType('application/json', 'UTF-8');
    }

    protected function setPdfResponse()
    {
        $this->view->disable();
        $this->response->setContentType('application/pdf', 'UTF-8');
    }

    protected function setExcelResponse($filename = '', $version = '2003')
    {
        $this->view->disable();
        if ($version == '2003') {
            header('Content-Type: application/vnd.ms-excel');
        }
        else {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        }
        header('Cache-Control: No-cache');
        if (!empty($filename)) {
            if ($version == '2003') {
                header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
            }
            else {
                header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
            }
        }
    }

    protected function setCsvResponse($filename = '')
    {
        $this->view->disable();
        header('Content-Type: application/vnd.ms-excel; charset=gb2312');
        header('Cache-Control: No-cache');
        if (!empty($filename)) {
            header('Content-Disposition: attachment;filename="' . $filename . '.csv"');
        }
    }

    protected function getPagination($rows, $defaultPageSize = 10)
    {
        $offset = intval($this->request->get('offset', 'int'));
        $offset = max($offset, 0);
        $limit = intval($this->request->get('limit', 'int'));
        if ($limit <= 0)
            $limit = $defaultPageSize;

        $pageNumber = ($offset / $limit + 1);
        $paginator = null;

        if ($rows instanceof BuilderInterface) {
            $paginator = new PageBuilder(array(
                "builder" => $rows,
                "limit" => $limit,
                "page" => $pageNumber
            ));
        }
        else {
            $paginator = new PaginatorModel([
                'data' => $rows,
                'limit' => $limit,
                'page' => $pageNumber
            ]);
        }


        $page = $paginator->getPaginate();
        $obj = new \stdClass();
        $obj->rows = $page->items;
        $obj->total = $page->total_items;
        $obj->paginator = $page;
        // 清空paginator中的数据，否则会产生多余的数据
        $obj->paginator->items = null;
        return $obj;
    }

    protected function getArrayPagination($rows, $defaultPageSize = 10)
    {
        $offset = intval($this->request->get('offset', 'int'));
        $offset = max($offset, 0);
        $limit = intval($this->request->get('limit', 'int'));
        if ($limit <= 0)
            $limit = $defaultPageSize;

        $pageNumber = ($offset / $limit + 1);
        $paginator = null;

        $paginator = new PaginatorArray([
            'data' => $rows,
            'limit' => $limit,
            'page' => $pageNumber
        ]);

        $page = $paginator->getPaginate();
        $obj = new \stdClass();
        $obj->rows = $page->items;
        $obj->total = $page->total_items;
        $obj->paginator = $page;
        $obj->paginator->items = null;
        return $obj;
    }

    public function beforeExecuteRoute(\Phalcon\Mvc\Dispatcher $dispatcher)
    {
        $module = strtolower($this->router->getModuleName());
        $controller = strtolower($dispatcher->getControllerName());
        $action = strtolower($dispatcher->getActionName());
        $controllerClass = $dispatcher->getControllerClass();

        // class级别的noacl
        $collection = $this->annotations->get($controllerClass)->getClassAnnotations();
        if ($collection != false && $collection->has('noacl'))
            return true;

        $annotations = $this->annotations->getMethod($controllerClass, $dispatcher->getActiveMethod());
        if ($annotations->has('noacl'))
            return true;

        if ($this->session->has('user')) {
            if (SessionData::isSuper())
                return true;

            if ($annotations->has('super') || ($collection != false && $collection->has('super'))) {
                $this->redirect('sys/error/error401');
                return false;
            }

            if ($annotations->has('admin') || ($collection != false && $collection->has('admin'))) {
                if (!$this->acl->isAdmin()) {
                    $this->redirect('sys/error/error401');
                    return false;
                }
                return true;
            }

            if ($annotations->has('skipacl') || ($collection != false && $collection->has('skipacl')))
                return true;

            $acl_links = [];
            if ($annotations->has('acl')) {
                $acl = $annotations->get('acl');
                $arr = $acl->getArgument(0);
                if (!empty($arr)){
                    $acl_links = explode(',', $arr['link']);
                }
            }
            if (!$this->acl->has("$module:$controller:$action")) {
                if (count($acl_links) > 0){
                    foreach ($acl_links as $acl_link){
                        if ($acl_link != '' && $this->acl->has($acl_link))
                            return true;
                    }
                }
                $this->redirect('sys/error/error401');
                return false;
            }
            return true;
        }

        $this->redirect('sys/login');
        return false;
    }

    public function afterExecuteRoute(\Phalcon\Mvc\Dispatcher $dispatcher)
    {
        if ($this->session->has(SessionData::$_OWNER_KEY)) {
            $this->view->owner = SessionData::owner();
        }
    }

    public function isCNameAccess()
    {
        $host = $_SERVER['HTTP_HOST'];
        if (stristr($host, $this->config->hostName)===false)
            return true;
        return false;
    }

    public function handleRequestPayload()
    {
        $contentType = strtolower($this->request->getHeader('CONTENT_TYPE'));
        switch ($contentType) {
            case 'application/json':
            case 'application/json;charset=utf-8':
                $jsonRawBody = $this->request->getJsonRawBody(true);
                if ($this->request->getRawBody() && !$jsonRawBody) {
                    die('Invalid JSON syntax!');
                }
                $_POST = $jsonRawBody;
                break;
        }
    }
    protected function setImageResponse()
    {
        $this->view->disable();
        $this->response->setContentType('image/jpeg', 'UTF-8');
    }
}
