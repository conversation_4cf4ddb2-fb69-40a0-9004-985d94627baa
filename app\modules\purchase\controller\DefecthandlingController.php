<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\CommonService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseDefectHandling;
use Envsan\Modules\Purchase\Model\PurchaseInspectionDetail;
use Envsan\Modules\Purchase\Service\PurchaseDefectHandlingService;
use Envsan\Modules\Purchase\Service\PurchaseInspectionService;
use Envsan\Modules\Sys\Model\SysDict;
use Envsan\Modules\Sys\Service\DictService;

use Envsan\Common\Data\SessionData;
use Envsan\Modules\Common\Util\Constant;

/**
 * @name('不良品处理')
 */
class DefecthandlingController extends SuperController
{
    private $page_id = 56;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseDefectHandlingService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            
            // 先处理不良品原因显示
            $pageRowsArray = $page->rows->toArray();
            $processedRows = $s->processDefectReasonsDisplay($pageRowsArray);
            
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $processedRows, $builder);
            
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }


    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new PurchaseDefectHandlingService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = (new PurchaseDefectHandling())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $this->view->defectReasonOptions = $s->getDictList("purchase:defect:reason");
        $this->view->defect_reasons_dis = CvtUtil::emptyToArray($jrow['defect_reasons']);
        $jrow['inspectionOptions'] = $s->getDefectHandingData();
        $this->view->jsonData = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'purchase:defecthandling:create'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseDefectHandlingService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = $row->toArray();
        $jrow['inspectionOptions'] = $s->getDefectHandingData();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, CvtUtil::emptyToArray($row->ext_data));
        $this->view->jsonData = json_encode($jrow);
        $this->view->defectReasonOptions = $s->getDictList("purchase:defect:reason");
        $this->view->defect_reasons_dis = CvtUtil::emptyToArray($jrow['defect_reasons']);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('defecthandling/create');
    }

    /**
     * @acl({'link':'purchase:defecthandling:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseDefectHandlingService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid, $type = '')
    {
        $s = new PurchaseDefectHandlingService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->jsonData = json_encode($jrow);
        $this->view->defectReasonOptions = $s->getDictList("purchase:defect:reason");
        $this->view->defect_reasons_dis = CvtUtil::emptyToArray($jrow['defect_reasons']);
        $this->view->extDataName = 'ext_data';
        $this->view->view_type = $type;
        $this->view->uid = $uid;
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseDefectHandlingService();
        $builder = $s->selectAll();

        // 获取所有数据并处理不良品原因显示
        $allData = $builder->getQuery()->execute();
        $processedData = $s->processDefectReasonsDisplay($allData->toArray());
        
        $table = new TableService();
        // 直接传入处理后的数组$processedData，和原始的$builder
        $ret = $table->getTableData($this->page_id, $processedData, $builder);

        $file_title = Constant::$page_extend_column[$this->page_id]['page_name'];
        $objExcel = new \PHPExcel();
        //设置属性
        $objExcel->getProperties()->setCreator(SessionData::owner()->company);
        $objExcel->getProperties()->setLastModifiedBy(SessionData::owner()->company);
        $objExcel->getProperties()->setTitle($file_title);
        $objExcel->setActiveSheetIndex();
        $objActSheet = $objExcel->getActiveSheet();
        $objActSheet->setTitle($file_title);
        $data_header = $ret['data_header'];
        $column_data = [];
        foreach ($data_header as $header_item){
            if ($header_item['show'] == 1){
                $column_data[] = $header_item;
            }
        }
        $rows = $ret['data_rows'];
        if (count($column_data) == 0) {
            die('未配置该页面展示信息');
        } else if (count($column_data) > 52) {
            die('展示信息过多');
        }
        $letters = $table->getLetters(count($column_data));
        $last_no = $letters[count($letters) - 1];
        $objActSheet->mergeCells('A1'.':'.$last_no.'1');
        $objActSheet->setCellValue('A1', $file_title);
        $objActSheet->getStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objActSheet->getStyle()->getFont()->setBold(true);
        foreach ($letters as $idx => $letter)
        {
            $unit = '';
            if (!empty($column_data[$idx]['unit'])){
                $unit = '(' . $column_data[$idx]['unit'] . ')';
            }
            $objActSheet->setCellValue($letter.'2', $column_data[$idx]['name'].$unit);
            $objActSheet->getColumnDimension($letter)->setWidth(20);
        }
        $objExcel->getActiveSheet()->getStyle('a1:'.$last_no.'2')->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
        $objExcel->getActiveSheet()->getStyle('a1:'.$last_no.'2')->getFont()->setBold(true);
        $val = '';
        $row_no = 3;
        foreach($rows as $row) {
            $row_no_start = $row_no;
            foreach ($letters as $idx => $letter)
            {
                $cell_no = $letter.$row_no;
                if (array_key_exists($column_data[$idx]['id'],$row)){
                    if ($column_data[$idx]['type'] == 2) {
                        $objActSheet->setCellValue($cell_no, $row[$column_data[$idx]['id']]);
                    } else {
                        $objExcel->getActiveSheet()->setCellValueExplicit($cell_no, $row[$column_data[$idx]['id']], \PHPExcel_Cell_DataType::TYPE_STRING);
                        $objExcel->getActiveSheet()->getStyle($cell_no)->getNumberFormat()->setFormatCode("@");
                    }
                } else {
                    $objActSheet->setCellValue($cell_no, '');
                }
            }
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getAlignment()->setWrapText(true);
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
            $row_no++;
        }
        $objActSheet->getStyle('A1:'.$last_no.($row_no - 1))->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
        //清除缓冲区,避免乱码
        ob_end_clean();
        // 设置页方向和规模
        $objExcel->getActiveSheet()->getPageSetup()->setOrientation(\PHPExcel_Worksheet_PageSetup::ORIENTATION_PORTRAIT);
        $objExcel->getActiveSheet()->getPageSetup()->setPaperSize(\PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
        $objExcel->setActiveSheetIndex(0);

        $timestamp = time();
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$file_title.'_'.$timestamp.'.xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objExcel, 'Excel2007');
        $objWriter->save('php://output');
        exit;
    }

    /**
     * @skipacl
     */
    public function getinfoAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseDefectHandlingService();
            $ret = new JsonData();
            $inspection_code = $this->request->getPost('inspection_code', 'tstring');
            $ret->handleResult($s->getDefectHandingData($inspection_code));
            return json_encode($ret);
        }
    }


    /**
     * @name('审核')
     */
    public function approvalAction() {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseDefectHandlingService();
            $ret = new JsonData();
            $ret->handleResult($s->approval());
            return json_encode($ret);
        }
    }

    /**
     * 审核通过的Action
     * @acl({'link':'purchase:defecthandling:approval'})
     */
    public function rejectAction() {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseDefectHandlingService();
            $ret = new JsonData();
            $ret->handleResult($s->reject());
            return json_encode($ret);
        }
    }

}