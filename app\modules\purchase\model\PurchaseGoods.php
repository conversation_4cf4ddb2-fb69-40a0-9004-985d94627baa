<?php

namespace Envsan\Modules\Purchase\Model;



use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseGoods extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $as_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $taxation;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $spec;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $cd;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $kd;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $hd;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $weight;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $model;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $type_id;

    /**
     *
     * @var string
     * @Column(type="string", length=30, nullable=false)
     */
    public $code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $unit;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $deputy_unit;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $supplier_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $warning_flag;

    /**
     *
     * @var double
     * @Column(type="double", length=4, nullable=true)
     */
    public $warning_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $check_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $use_flag;

    /**
     *
     * @var double
     * @Column(type="double", length=11, nullable=true)
     */
    public $price;

    /**
     *
     * @var double
     * @Column(type="double", length=11, nullable=true)
     */
    public $price_hs;

    /**
     *
     * @var double
     * @Column(type="double", length=4, nullable=true)
     */
    public $tax_rate;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $formula_list;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $formula_val;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quality_template_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quality_check_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $inventory_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $inventory_unit;

    /**
     *
     * @var integer
     * @Column(type="integer", length=1, nullable=true)
     */
    public $is_batch_managed;

    /**
     *
     * @var double
     * @Column(type="double", length=10, nullable=true)
     */
    public $unit_conversion_rate;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $customer_id;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_goods';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseGoods[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseGoods
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
