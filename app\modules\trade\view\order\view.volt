{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div class="page-content">
    <div class="row">
        <div id="app" class="col-sm-4">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">订单信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 72vh;overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">项目号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_code" v-model="code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">客户</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="customer_name" v-model="customer_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">下单日期</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" placeholder="请输入下单日期" readonly name="sign_date" v-model="sign_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">成交金额</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input  type="number" class="form-control" name="order_amount" v-model="order_amount" placeholder="请输入成交金额" number="true"  maxlength="10" readonly>
                                                <span class="input-group-addon">元</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('view') }}
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" readonly maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-8">
            <div class="portlet light portlet-detail" style="margin-bottom: 0;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">订单明细</span>
                    </div>
                </div>
                <div class="portlet-body">
                    {{ partial('table') }}
                </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        mounted: function() {
            let btn_list = [];
            btn_list.push({type:1, name:'查看详情'});
            app_ext_table.init({{ detail_page_id }}, btn_list, '{{ url('trade/orderdetail/list/json/' ~ uid) }}',false);
        },
        methods: {
            getParams: function() {
                return {}
            },
            search: function() {
                app_ext_table.search();
            },
            dataView: function(type, row) {
                if (type == 1) {
                    view(row.uid);
                }
            }
        }
    });

    function view(uid) {
        top.layer.open({
            title: '查看',
            type: 2,
            area: ['70em', '90%'],
            content: '{{ url('trade/orderdetail/view/') }}' + uid,
        });
    }

    function setTableHeight() {
        let h = $(window).height() - 35 - 8 - 27 - $(".portlet-detail .portlet-title").outerHeight(true)
            - $(".search-bar").outerHeight(true)
            - $(".search-bar-table").outerHeight(true) - $(".sum-bar").outerHeight(true)
            - $(".zh-table-footer").outerHeight(true);
        $(".zh-table-box-content").height(h);
    }

    $(function() {
        setTableHeight();
    });
</script>