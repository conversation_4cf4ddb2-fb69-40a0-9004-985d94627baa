<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

use Envsan\Common\Util\ModelUtil;

class RoleRes extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $role_id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $res_id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_role_res';
    }

    public static function find($params = null)
    {
        return parent::find(ModelUtil::build($params));
    }

    public static function findFirst($params = null)
    {
        return parent::findFirst(ModelUtil::build($params));
    }

    public function initialize()
    {
        $this->belongsTo('role_id', 'Envsan\Modules\Sys\Model\Role', 'id', array('alias' => 'Role'));
        $this->belongsTo('res_id', 'Envsan\Modules\Sys\Model\Resource', 'id', array('alias' => 'Resource'));
        ModelUtil::softDelete($this);
    }

    public function beforeDelete()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }

    public function beforeSave()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }
}
