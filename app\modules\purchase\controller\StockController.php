<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Service\StockService;
use Envsan\Modules\Purchase\Util\Constant;

/**
 * @name('库存')
 */
class StockController extends SuperController
{
    private $page_id = 55;

    /**
     * @name('查询列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new StockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('履历查询')
     */
    public function searchAction($type = '')
    {
        $s = new StockService();
        if ($type == 'json') {
            $this->setJsonResponse();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->stock_types = Constant::$stock_types;
    }
}