<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesViewInstockCnt extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $notice_detail_id;

    /**
     *
     * @var string
     * @Column(type="string", length=21, nullable=true)
     */
    public $instock_cnt;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_view_instock_cnt';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewInstockCnt[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewInstockCnt
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
