<?php

namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Model\User;
use GuzzleHttp\Client;
use Phalcon\Mvc\User\Component;

class GroupService extends Component
{
    /**
     * @param $cid 如果指定了cid, 那么该组织下面的子组织不在显示，简单防止父组织被移动到子组织下面的尴尬
     * @param bool $opened
     * @return string
     */
    public function selectTree($cid = 0, $opened = true,$select_ids = '')
    {
        $user = SessionData::user();

        $rows = null;
        if (SessionData::isAdmin() || SessionData::isSuper())
            $rows = Group::find(['pid=0', 'order' => 'id desc']);
        else {
            // 必须在取一次，不能直接使用session中的，因为可能被切换了!
            $rows = Group::find(['id=' . SessionData::groupId(), 'order' => 'id desc']);
        }
        $select_arr = array();
        if (!empty($select_ids)){
            $select_arr = explode(',', $select_ids);
        }
        $arr = array();
        foreach ($rows as $row) {
            $sub = new \stdClass();
            $sub->text = $row->name;
            $sub->id = $row->id;
            $sub->uid = $row->uid;
            $sub->state = new \stdClass();
            $sub->state->opened = $opened;
            for ($x = 0; $x < count($select_arr); $x++) {
                if ($select_arr[$x] == $row->id){
                    $sub->state->selected = true;
                }
            }
            array_push($arr, $sub);
        }

        $this->buildTree($cid, $arr, $user->owner, $opened,$select_arr);
        return json_encode($arr);
    }

    private function buildTree($cid, $arr, $owner, $opened,$select_arr)
    {
        foreach ($arr as $item) {
            if ($item->id == $cid)
                continue;

            $rows = Group::find(['pid=' . $item->id, 'order by id desc']);
            $child = array();
            foreach ($rows as $row) {
                $sub = new \stdClass();
                $sub->text = $row->name;
                $sub->id = $row->id;
                $sub->uid = $row->uid;
                $sub->state = new \stdClass();
                $sub->state->opened = $opened;
                for ($x = 0; $x < count($select_arr); $x++) {
                    if ($select_arr[$x] == $row->id){
                        $sub->state->selected = true;
                    }
                }
                array_push($child, $sub);
            }
            $item->children = $child;
            if (count($child)) {
                $this->buildTree($cid, $child, $owner, $opened,$select_arr);
            }
        }
    }

    public function selectTreeByID($group_id,$owner_id,$cid = 0, $opened = true,$select_ids = '')
    {
        $rows = Group::find(['id=' . $group_id, 'order' => 'id desc']);
        $select_arr = array();
        if (!empty($select_ids)){
            $select_arr = explode(',', $select_ids);
        }
        $arr = array();
        foreach ($rows as $row) {
            $sub = new \stdClass();
            $sub->text = $row->name;
            $sub->id = $row->id;
            $sub->uid = $row->uid;
            $sub->state = new \stdClass();
            $sub->state->opened = $opened;
            for ($x = 0; $x < count($select_arr); $x++) {
                if ($select_arr[$x] == $row->id){
                    $sub->state->selected = true;
                }
            }
            array_push($arr, $sub);
        }
        $this->buildTree($cid, $arr, $owner_id, $opened,$select_arr);
        return $arr;
    }

    public function create()
    {
        $row = new Group();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = trim($this->request->getPost('name', 'string'));
        $short_name = trim($this->request->getPost('short_name', 'string'));
        $code = trim($this->request->getPost('code', 'string'));
        $type = trim($this->request->getPost('type', 'string'));

        if ($name == '')
            return '名称不能为空';
        //同一名称和类型CHECK
        $check= Group::findFirst(['name=?1', 'bind' => [1 => $name]]);
        if ($check != null && $check->id != $row->id)
            return '该组织已存在,请更换组织名称';

        if ($this->isRepeatCode($code, $row->id)) {
            return '该组织编号已存在,请更换组织编号';
        }

        $pid = intval($this->request->getPost('pid', 'int'));
        $prow = $this->selectById($pid);
        if ($prow == null)
            return '上级部门不存在';

        if ($act == 'update' && $prow->id == $row->id)
            return '上级部门不能为自身';

        $user = SessionData::user();
        if ($prow->owner != $user->owner)
            return ErrorHelper::WRONG_OWNER;

        $row->pid = $prow->id;
        $row->name = $name;
        $row->short_name = $short_name;
        $row->code = $code;
        $row->type = CvtUtil::blankToNull($type);
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;

        if ($act == 'create') {
            $row->owner = $user->owner;
            $row->uid = UUID::make();
        }

        if ($row->save()) {
            $this->session->set('group_ids', $this->selectGroupIds($user->group_id));
            return '';
        }

        return ErrorHelper::UNKOWN;
    }

    private function isRepeatCode($code, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\Group')
            ->where('del_flag = 0 and code = ?1', [1 => $code]);
        if (!empty($id)) {
            $builder->andWhere('id <> ?2', [2 => $id]);
        }
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public function deleteByUid($uid)
    {
        $row = Group::findFirst(['uid=?1', 'bind' => [1 => $uid]]);
        if ($row == null)
            return '记录不存在';
        return $this->_deleteById($row, $row->id);
    }

    public function deleteById($id)
    {
        $row = Group::findFirst(['id=?1', 'bind' => [1 => $id]]);
        if ($row == null)
            return '记录不存在';
        return $this->_deleteById($row, $row->id);
    }

    private function _deleteById($row, $id)
    {
        if (!empty($row->customer_id)){
            return '该部门已经绑定客户，不能删除';
        }

        $child = Group::findFirst('del_flag = 0 and pid=' . $id);
        if ($child != null)
            return '该组织下面含有子组织';

        $users = User::find(['del_flag = 0 and group_id = ?1', 'bind' => [1 => $id]]);
        if (count($users) > 0) {
            return '该部门下尚有未删除的用户记录';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if ($row->save())
            return '';

        return ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return Group::findFirst(['id=?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return Group::findFirst(['uid=?1', 'bind' => [1 => $uid]]);
    }

    public function selectByCode($code)
    {
        return Group::findFirstDirect(['code=?1', 'bind' => [1 => $code]]);
    }

    public function selectByType($type)
    {
        return Group::find(['type=?1', 'bind' => [1 => $type]]);
    }

    // 根据组织的id找到它所有的子子孙孙的id，保存为数组
    public function selectGroupIds($pid)
    {
        $arr = array();
        array_push($arr, $pid);
        $this->selectChildPids($arr, $pid);
        return $arr;
    }

    // 递归调用
    private function selectChildPids(&$arr, $pid)
    {
        $rows = Group::find('del_flag = 0 and pid=' . $pid);
        foreach ($rows as $row) {
            array_push($arr, $row->id);
            $this->selectChildPids($arr, $row->id);
        }
    }

    public function selectTypes()
    {
        $ds = new DictService();
        $val = $ds->get('sys:group:type');
        if (!empty($val)) {
            $arr = explode(',', $val);
            return array_filter($arr);
        }
        return null;
    }

    public function selectParentById($id)
    {
        $group = $this->selectById($id);
        if ($group != null)
            return $this->selectById($group->pid);
        return null;
    }

    // 切换组织，更改session中的group_id和group_ids
    public function switchGroup()
    {
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $group = $this->selectByUid($uid);
        if ($group == null)
            return ErrorHelper::ROW_NOTEXIST;

        $this->session->set('group_id', $group->id);

        $ids = $this->selectGroupIds($group->id);
        $this->session->set('group_ids', $ids);
        return ErrorHelper::NOERROR;

    }

    public function createBarCodeImg($path)
    {
        // token获取
        // 钢联 https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxeccf48377a77f398&secret=6a7b8945ca7c0740d0ef6d34c695df18
        // 柒加 https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wx3ba3f3d6d32c8d03&secret=a848107c30d7e04c6677c6f10769ea74

        $path = str_replace('@', '/', $path);

        $token = '36_UoBage2hI6NOrZQuzEJyAtEaS6GdhfXzTqx1B0_JtW3mu1T5z_haUSb75FW1S1U-dRs0_mTL-d-BCZ-maZ72h0a5XtiI-KNye1VOb-Ra0AQXw3U-ZMrUOj2vEbW4aBfbsGQ5pLYgyW6ab9iWMKXbAAADBO';
        $url = 'https://api.weixin.qq.com/wxa/getwxacode?access_token='.$token;
        $client = new Client();
        $ret = $client->request('POST', $url, [
            'verify' => false,
            'json' => [
                'path' => $path,
                'width' => 800
            ]
        ])->getBody()->getContents();
        $path = $this->config->upload->uploadDir.time().'.jpg';
        $file = fopen($path, "w");
        fwrite($file, $ret);
        fclose($file);
    }

    public function getGroupList($type='')
    {
        $arr = array();
        $user = SessionData::user();
        if (!empty($user->auth_data_ids)){
            $arr = explode(',', $user->auth_data_ids);
        } else {
            $arr = $this->selectGroupIds($user->group_id);
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.id,a.uid,a.short_name as name')
            ->addFrom('Envsan\Modules\Sys\Model\Group', 'a')
            ->where('a.del_flag = 0')
            ->inWhere('a.id', $arr)
            ->orderBy('a.id');
        if (!empty($type)){
            $builder->andWhere("a.code = ?1", [1 => $type]);
        }

        return $builder->getQuery()->execute();
    }
}