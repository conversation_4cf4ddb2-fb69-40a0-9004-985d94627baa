{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}

<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">到货信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 72vh;overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">出库单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">外委加工单</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">供应商</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">出库日期</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="outstock_date" v-model="outstock_date" readonly/>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('view') }}
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item, index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" class="form-control" v-model="remarks" readonly rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button v-if="viewType == 'approval'" type="button" class="btn red" @click="showPopup('reject')" ><i class="fa fa-reply"></i> 驳回</button>
                            <button v-if="viewType == 'approval'" type="button" class="btn green" @click="showPopup('pass')" ><i class="fa fa-share"></i> 通过</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">到货明细</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>批次号</th>
                                        <th>产品名称</th>
                                        <th>产品规格</th>
                                        <th>外委工艺</th>
                                        <th>外委数量</th>
                                        <th>计价数量</th>
                                        <th>无税单价</th>
                                        <th>含税单价</th>
                                        <th>无税总价</th>
                                        <th>含税总价</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <tr v-if="detail_data.length == 0">
                                    <td colspan="11" style="text-align: center;">没有数据</td>
                                </tr>
                                <tr v-for="row, index in detail_data">
									<td v-text="index + 1"></td>
									<td v-text="row.code"></td>
									<td v-text="row.goods_name"></td>
									<td v-text="row.goods_model"></td>
									<td v-text="row.bom_name"></td>
									<td v-text="row.quantity"></td>
									<td v-text="row.pricing_quantity"></td>
									<td v-text="row.price"></td>
									<td v-text="row.price_hs"></td>
									<td v-text="row.total_money"></td>
									<td v-text="row.total_money_hs"></td>
								</tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="portlet light bordered" v-if="comment && status_name == '以驳回' && status == '10'">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-bubble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">审核意见</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; color: #333;">
                        ${ comment }
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 添加弹出窗口的HTML结构 -->
    <div id="custom-popup" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title">${model_title}</h4>
                </div>
                <div class="modal-body">
                    <label><span v-if="handleType == 'reject'" class="required">*</span>评论</label>
                    <div>
                        <textarea class="form-control" name="approval_comment" v-model="approval_comment" rows="3" maxlength="200" style="resize: none"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn green" @click="doPass"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn btn-default" @click="handleClose">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var viewType = '{{ view_type }}';
    var uid = '{{ uid }}';
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonOutstock }},
            viewType: viewType,
            model_title: '通过',
            approval_comment: '',
            handleType: ''
        },
        methods: {
            doPass: function() {
                if (this.handleType == 'reject' && !this.approval_comment ) {
                    toastr.error('必须输入评论内容！');
                    return ;
                }

                if (this.handleType == 'reject')  {
                    var url = '{{ url('purchase/wwoutstock/reject/') }}';
                } else {
                    var url = '{{ url('purchase/wwoutstock/approval/') }}';
                }
                
                showSpin();
                $.post(url, {
                    uid : uid,
                    comment:app.approval_comment,
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B')
                }, function (rs) {
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                }).fail(function() {
                    toastr.error('未知的异常');
                }).always(function() {
                    closeSpin(null); // 无论如何都关闭loading
                });

            },
            showPopup: function(type) {
                this.handleType = type
                $('#custom-popup').modal('show');
            },
            handleClose: function() {
              $('#custom-popup').modal('hide');
            },
        }
        
    });
</script>

<style>
.readonly-field {
    background-color: #f5f5f5;
    color: #333333;
    cursor: default;
}

/* Modal 样式美化 */
#custom-popup .modal-dialog {
    margin: 50px auto;
    max-width: 600px;
}

#custom-popup .modal-content {
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: none;
}

#custom-popup .modal-header {
    background: #2c3e50;
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 15px 20px;
    border-bottom: none;
}

#custom-popup .modal-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

#custom-popup .modal-header .close {
    color: white;
    opacity: 0.8;
    font-size: 28px;
    font-weight: 300;
    text-shadow: none;
}

#custom-popup .modal-header .close:hover {
    opacity: 1;
    color: #f8f9fa;
}

#custom-popup .modal-body {
    padding: 25px;
    background-color: #f8f9fa;
}

#custom-popup .modal-body textarea {
    border: 2px solid #ced4da;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background-color: #ffffff;
}

#custom-popup .modal-body textarea:focus {
    border-color: #2c3e50;
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
    outline: none;
}

#custom-popup .modal-footer {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    padding: 20px 25px;
    border-radius: 0 0 8px 8px;
}

#custom-popup .modal-footer .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

#custom-popup .modal-footer .btn.green {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
    border: none;
    color: white;
}

#custom-popup .modal-footer .btn.green:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(54, 209, 220, 0.4);
}

#custom-popup .modal-footer .btn-default {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

#custom-popup .modal-footer .btn-default:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-1px);
}

/* Modal 背景遮罩美化 */
#custom-popup.modal {
    background-color: transparent;
}

#custom-popup .modal-backdrop {
    background-color: transparent;
}
</style>