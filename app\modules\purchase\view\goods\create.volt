<!--jsTree -->
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div style="display: flex;">
                        <div style="width: 60%;padding-right: 30px;margin-right: 30px;border-right: 1px solid #e7ecf1;">
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>已选择的存货类型<span class="required"> * </span></label>
                                        <div>
                                            <input type="text" class="form-control" name="type_name" v-model="type_name" required readonly>
                                        </div>
                                    </div>
                                </div>
                                {% if dispatcher.getActionName() == 'edit' %}
                                    <div class="col-xs-6">
                                        <div class="form-group">
                                            <label>存货编码<span class="required"> * </span></label>
                                            <div>
                                                <input type="text" class="form-control" name="code" v-model="code" required :disabled="edit_flag == 1">
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="col-xs-6">
                                        <div class="form-group">
                                            <label>存货编码<span class="required"> * </span></label>
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" name="re_code" v-model="re_code" disabled/>
                                                <span class="input-group-addon no-border-lr">~</span>
                                                <input type="text" class="form-control date dtpicker" name="code" v-model="code" :disabled="edit_flag == 1"/>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>存货名称<span class="required"> * </span></label>
                                        <div>
                                            <input type="text" class="form-control" name="name" v-model="name" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>存货简称</label>
                                        <div>
                                            <input type="text" class="form-control" name="as_name" v-model="as_name" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>规格型号</label>
                                        <div>
                                            <input type="text" class="form-control" name="model" v-model="model" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>存货代码</label>
                                        <div>
                                            <input type="text" class="form-control" name="inventory_code" v-model="inventory_code" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>采购单位<span class="required"> * </span></label>
                                        <div>
                                            <input type="text" class="form-control" name="unit" v-model="unit" maxlength="10" required :disabled="edit_flag == 1">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>库存单位<span class="required"> * </span></label>
                                        <div>
                                            <input type="text" class="form-control" name="deputy_unit" v-model="deputy_unit" maxlength="10" required :disabled="edit_flag == 1">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>单位换算率<span class="required"> * </span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="unit_conversion_rate" v-model="unit_conversion_rate" required number="true" >
                                            <span class="input-group-addon">${unit}/${deputy_unit}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>供应商</label>
                                        <div>
                                            <select class="bs-select form-control" name="supplier_id" v-model="supplier_id"  data-live-search="true" data-size="8">
                                                <option value="">请选择供应商</option>
                                                {% for item in supplierList %}
                                                    <option value="{{ item.id }}">{{ item.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>税目</label>
                                        <div>
                                            <input type="text" class="form-control" name="taxation" v-model="taxation" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>税率</label>
                                        <div class="input-group">
                                            <input type="text" @input="onTaxRateChange()" class="form-control" name="tax_rate" v-model="tax_rate" number="true" >
                                            <span class="input-group-addon">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>未税单价</label>
                                        <div>
                                            <input  @input="onPriceChange()" type="text" class="form-control" name="price" v-model="price" number="true" maxlength="9">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>含税单价</label>
                                        <div>
                                            <input @input="onPriceHsChange()" type="text" class="form-control" name="price_hs" v-model="price_hs" number="true" maxlength="9">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>单重(KG)</label>
                                        <div>
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="weight" v-model="weight" number="true" maxlength="9">
                                                <span class="input-group-addon">KG</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>是否库存预警<span class="required"> * </span></label>
                                        <div>
                                            <select class="form-control bs-select"  name="warning_flag" v-model="warning_flag" required>
                                                <option value="">请选择</option>
                                                <option value="1">是</option>
                                                <option value="0">否</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="exec_flag == 1" class="col-xs-6">
                                    <div class="form-group">
                                        <label>预警值<span class="required"> * </span></label>
                                        <div>
                                            <input type="number" class="form-control" name="warning_quantity" v-model="warning_quantity" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>是否入库检验<span class="required"> * </span></label>
                                        <div>
                                            <select class="form-control bs-select"  name="check_flag" v-model="check_flag" required>
                                                <option value="">请选择</option>
                                                <option value="1">是</option>
                                                <option value="0">否</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6" v-if="check_flag == 1">
                                    <div class="form-group">
                                        <label>检验模板<span class="required"> * </span></label>
                                        <div>
                                            <select class="bs-select form-control" name="quality_template_id" v-model="quality_template_id" data-live-search="true" data-size="8" required>
                                                <option value="">请选择</option>
                                                {% for check in checkList %}
                                                    <option value="{{check['id']}}">{{ check['name'] }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12" v-if="quality_template_id != '' && quality_template_id != null">
                                    <template v-for="(check_item,check_item_idx) in quality_check_data">
                                        <div class="col-sm-12" style="display: flex;border-top:  1px solid #E2E2E2;">
                                            <div style="width: 35%;padding: 5px;">
                                                <span v-text="check_item.title"></span><span v-if="check_item.unit != ''" v-text="'('+check_item.unit+')'"></span><br/>
                                                (<span v-text="check_item.explain"></span>)
                                            </div>
                                            <div style="width: 65%;padding: 5px;">
                                                <div v-if="check_item.type == 3 || check_item.type == 4 || check_item.type == 6">
                                                    <span v-for="(item,idx) in check_item.list" v-text=" check_item.list.length == idx+1 ? item :  item + ','"></span>
                                                </div>
                                                <div v-if="check_item.type == 7">
                                                    <span v-text="check_item.formula_val"></span>
                                                    <div class="input-group">
                                                        <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_plus' + check_item_idx" v-model="check_item.standard_plus" maxlength="10" required>
                                                        <span class="input-group-addon">最大值</span>
                                                    </div>
                                                    <div class="input-group">
                                                        <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_minus' + check_item_idx" v-model="check_item.standard_minus" maxlength="10" required>
                                                        <span class="input-group-addon">最小值</span>
                                                    </div>
                                                </div>
                                                <div v-if="check_item.type == 8">
                                                    <div class="input-group">
                                                        <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_val' + check_item_idx" v-model="check_item.standard_val" maxlength="10" required>
                                                        <span class="input-group-addon">标准值</span>
                                                    </div>
                                                    <div class="input-group">
                                                        <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_plus' + check_item_idx" v-model="check_item.standard_plus" maxlength="10" required>
                                                        <span class="input-group-addon">工差(+)</span>
                                                    </div>
                                                    <div class="input-group">
                                                        <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_minus' + check_item_idx" v-model="check_item.standard_minus" maxlength="10" required>
                                                        <span class="input-group-addon">工差(-)</span>
                                                    </div>
                                                </div>
                                                <div v-if="check_item.type == 1 || check_item.type == 2 || check_item.type == 5">
                                                    &nbsp;
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <div style="flex: 1;">
                            <div class="form-group">
                                <label>长/宽/厚(mm)</label>
                                <div style="display: flex;">
                                    <input type="text" class="form-control" name="cd" v-model="cd" @keyup="setSpec" number="true" maxlength="8" placeholder="请输入长度" style="flex: 1;border-top-left-radius: 0;border-bottom-left-radius: 0;">
                                    <input type="text" class="form-control" name="kd" v-model="kd" @keyup="setSpec" number="true" maxlength="8" placeholder="请输入宽度" style="flex: 1;border-radius: 0;border-left: 0;border-right: 0;">
                                    <input type="text" class="form-control" name="hd" v-model="hd" @keyup="setSpec" number="true" maxlength="8" placeholder="请输入厚度" style="flex: 1;border-top-right-radius: 0;border-bottom-right-radius: 0;">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>规格</label>
                                <div>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="spec" v-model="spec">
                                        <span class="input-group-addon">mm</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>请输入计算公式</label>
                                <div style="margin-bottom: 10px;">
                                    <input type="text" class="form-control" name="formula_val" v-model="formula_val" readonly>
                                </div>
                                <div style="display: flex;flex-direction: row;flex-wrap: wrap;align-items: flex-start;">
                                    <div v-for="input_item,input_idx in input_list" style="padding: 3px">
                                        <button @click="keyInput(input_item)" type="button" class="btn blue btn-outline" v-text="input_item"></button>
                                    </div>
                                    <div style="padding: 3px;">
                                        <button @click="delInput" type="button" class="btn red btn-outline">删除</button>
                                    </div>
                                    <div class="input-group" style="padding: 3px;">
                                        <input style="width: 120px" type="text" class="form-control" name="input_val" v-model="input_val" placeholder="变量名称">
                                        <span class="input-group-btn">
                                            <button  @click="addInput" type="button" class="btn green btn-outline">变量</button>
                                        </span>
                                    </div>
                                    <div style="padding: 3px;">
                                        <button  @click="addCol('厚度', 'hd')" type="button" class="btn green btn-outline">厚度</button>
                                    </div>
                                    <div style="padding: 3px;">
                                        <button  @click="addCol('长度', 'cd')" type="button" class="btn green btn-outline">长度</button>
                                    </div>
                                    <div style="padding: 3px;">
                                        <button  @click="addCol('宽度', 'kd')" type="button" class="btn green btn-outline">宽度</button>
                                    </div>
                                    <div style="padding: 3px;">
                                        <button  @click="addFormula('向上取整(', 'Math.ceil(')" type="button" class="btn yellow btn-outline">向上取整</button>
                                    </div>
                                    <div style="padding: 3px;">
                                        <button  @click="addFormula('向下取整(', 'Math.floor(')" type="button" class="btn yellow btn-outline">向下取整</button>
                                    </div>
                                    <div style="padding: 3px;">
                                        <button  @click="addFormula('四舍五入(', 'Math.round(')" type="button" class="btn yellow btn-outline">四舍五入</button>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>计算公式同分类同步更新</label>
                                <div>
                                    <select class="form-control bs-select"  name="formula_flag" v-model="formula_flag" required>
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i>取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{ partial('check') }}
<script>
    var check_list = {{ checkList | json_encode }};
    var inputLayerIndex;
    var app = new Vue({
        el: '#app',
        data: Object.assign({{ jsonGoods }}, {
            lastInputField: 'price' // 记录最后输入的字段，默认为不含税单价
        }),
        methods: {
            onPriceChange() {
                this.lastInputField = 'price'; // 记录最后输入的是不含税单价
                if (this.price !== '' && this.price !== null) {
                    const priceValue = Number(this.price) || 0;
                    const rateValue = Number(this.tax_rate) || 0;
                    this.price_hs = (priceValue * (1 + rateValue / 100)).toFixed(2);
                }
            },

            onPriceHsChange() {
                this.lastInputField = 'price_hs'; // 记录最后输入的是含税单价
                if (this.price_hs !== '' && this.price_hs !== null) {
                    const priceHsValue = Number(this.price_hs) || 0;
                    const rateValue = Number(this.tax_rate) || 0;
                    if (rateValue === 0) {
                        this.price = priceHsValue.toFixed(2);
                    } else {
                        this.price = (priceHsValue / (1 + rateValue / 100)).toFixed(2);
                    }
                }
            },

            onTaxRateChange() {
                // 根据最后输入的字段来决定计算哪个价格
                if (this.lastInputField === 'price') {
                    // 如果最后输入的是不含税单价，则重新计算含税单价
                    if (this.price !== '' && this.price !== null) {
                        const priceValue = Number(this.price) || 0;
                        const rateValue = Number(this.tax_rate) || 0;
                        this.price_hs = (priceValue * (1 + rateValue / 100)).toFixed(2);
                    }
                } else if (this.lastInputField === 'price_hs') {
                    // 如果最后输入的是含税单价，则重新计算不含税单价
                    if (this.price_hs !== '' && this.price_hs !== null) {
                        const priceHsValue = Number(this.price_hs) || 0;
                        const rateValue = Number(this.tax_rate) || 0;
                        if (rateValue === 0) {
                            this.price = priceHsValue.toFixed(4);
                        } else {
                            this.price = (priceHsValue / (1 + rateValue / 100)).toFixed(2);
                        }
                    }
                }
            },
            setSpec() {
                let cd = isDecimal(this.cd) ? Number(this.cd) : '';
                let kd = isDecimal(this.kd) ? Number(this.kd) : '';
                let hd = isDecimal(this.hd) ? Number(this.hd) : '';

                this.spec = this.trimRightChar(cd + '*' + kd + '*' + hd,'*');
            },
            trimRightChar(str, char) {
                const escapedChar = char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义特殊字符
                const regex = new RegExp(escapedChar + '+$');
                return str.replace(regex, '');
            },
            addInput(){
                if (app.input_val == ''){
                    return;
                }
                app.formula_list.push({t:2,l:app.input_val,v:''});
                app.input_val = '';
            },
            addCol(label, col) {
                app.formula_list.push({t: 3, l: label, v: '', c: col});
            },
            addFormula(label, v) {
                app.formula_list.push({t: 4, l: label, v: v});
            },
            keyInput(val){
                app.formula_list.push({t:1,l:val,v:val});
            },
            delInput(){
                if (app.formula_list.length == 0){
                    return;
                }
                app.formula_list.splice(app.formula_list.length-1,1);
            },
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                {% if dispatcher.getActionName() == 'create' %}
                if(this.code.length > 4){
                    alertWarning('编码后缀不得超过4位');
                    return;
                }
                {% endif %}
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('purchase/goods/edit/'~r_id) }}';
                {% else %}
                var url= '{{ url('purchase/goods/create') }}';
                {% endif %}
                if (app.formula_list.length > 0){
                    let val = '';
                    for(let item of app.formula_list) {
                        if (item.t == 2) {
                            val += '1';
                        } else {
                            if (item.t == 3) {
                                item.v = this[item.c];
                            }
                            val += item.v;
                        }
                    }
                    try {
                        eval(val);
                    } catch (e){
                        alertWarning('计算公式不正确');
                        return;
                    }
                }

                for(let check_item of app.quality_check_data){
                    if (check_item.type == 7){
                        if (check_item.standard_plus == '' || check_item.standard_minus == '' ){
                            toastr.error('请输入检验标准值');
                            return;
                        }
                    } else if (check_item.type == 8){
                        if (check_item.standard_val == '' || check_item.standard_plus == '' || check_item.standard_minus == '' ){
                            toastr.error('请输入检验标准值');
                            return;
                        }
                    }
                }
                app.$data['form_data'] = encodeURI(JSON.stringify(app.quality_check_data));
                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        },
        computed: {
            formula_val(){
                let val = '';
                for(let item of this.formula_list){
                    val += item.l;
                }
                return val;
            }
        },
        watch: {
            warning_flag: function(val) {
                if(val == 1){
                    this.exec_flag = 1;
                }else
                    this.exec_flag = 0;
            },
            check_flag:function (v){
                this.quality_template_id = '';
                this.quality_check_data = [];
            },
            quality_template_id:function (v){
                if (v == '') {
                    this.quality_check_data = [];
                    return;
                }
                for(let check_item of check_list) {
                    if (check_item.id == v) {
                        this.quality_check_data = JSON.parse(JSON.stringify(check_item.form_data));
                        break;
                    }
                }
            }
        }
    });


    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);

</script>
