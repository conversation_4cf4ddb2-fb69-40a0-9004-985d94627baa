<?php
namespace Envsan\Common\Util\Excel;

use Envsan\Common\Component\Logger;

class Check
{
    public static function typeCheck($type, $val)
    {
        $check_flg = true;
        try {
            switch ($type) {
                case 'integer':
                    intval($val);
                    break;
                case 'date':
                    //匹配时间格式为2016-02-16或2016-02-16 23:59:59前面为0时可以不写
                    $patten = "/^\d{4}[\-](0?[1-9]|1[012])[\-](0?[1-9]|[12][0-9]|3[01])(\s+(0?[0-9]|1[0-9]|2[0-3])\:(0?[0-9]|[1-5][0-9])\:(0?[0-9]|[1-5][0-9]))?$/";
                    if (!preg_match($patten, $val)) {
                        $check_flg = false;
                    }
                    break;
                case 'string':
                    //特殊字符替换

                    break;
                default:
                    $check_flg = false;
                    break;
            }
        } catch (\Exception $e) {
            $check_flg = false;
        }
        return $check_flg;
    }

    public static function lengthCheck($length, $val)
    {
        $check_flg = true;
        try {
            if (!empty($val)) {
                if (intval($length) <= strlen($val)) {
                    $check_flg = false;
                }
            }
        } catch (\Exception $e) {
            $check_flg = false;
        }
        return $check_flg;
    }

    public static function emptyCheck($nullable, $val)
    {
        $check_flg = true;
        try {
            if ($nullable) {
                if (empty($val)) {
                    $check_flg = false;
                }
            }
        } catch (\Exception $e) {
            $check_flg = false;
        }
        return $check_flg;
    }

    public static function checkMsg($err_node_arr)
    {
        $msg = '';
        foreach ($err_node_arr as $code) {
            switch ($code) {
                case 1:
                    $msg .= '内容类型不正确,';
                    break;
                case 2:
                    $msg .= '内容长度过长,';
                    break;
                case 3:
                    $msg .= '内容必须输入,';
                    break;
                default:
                    break;
            }
        }
        return $msg;
    }

    public static function dateCheck($val){
        $is_date=strtotime($val)?strtotime($val):false;
        if($is_date===false){
            return '';
        }else{
            return date('Y-m-d',$is_date);
        }
    }

    public static function datetimeCheck($val){
        $is_date=strtotime($val)?strtotime($val):false;
        if($is_date===false){
            return '';
        }else{
            return date('Y-m-d H:i:s',$is_date);
        }
    }

    public static function doubleCheck($val,$cell){
        $val = trim($val);
        if (empty($val)){
            return 0;
        } else {
            $v = is_numeric($val) ? true : false;
            if($v){
                return round(doubleval($val),3);
            }else{
                if (substr($cell,0,1) == '='){
                    $val = $cell->getOldCalculatedValue();
                    $v = is_numeric($val) ? true : false;
                    if($v){
                        return round(doubleval($val),3);
                    } else {
                        return -1;
                    }
                } else {
                    return -1;
                }

            }
        }
    }
}