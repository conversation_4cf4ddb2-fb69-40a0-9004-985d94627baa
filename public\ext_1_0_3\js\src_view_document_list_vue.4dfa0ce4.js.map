{"version": 3, "file": "js/src_view_document_list_vue.4dfa0ce4.js", "mappings": ";;;;;;;;;;;AA8CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/document/list.vue", "webpack://sfp_ext/./src/view/document/list.vue", "webpack://sfp_ext/./src/view/document/list.vue?d346", "webpack://sfp_ext/./src/view/document/list.vue?b26e", "webpack://sfp_ext/./src/view/document/list.vue?5249", "webpack://sfp_ext/./src/view/document/list.vue?4788", "webpack://sfp_ext/./src/view/document/list.vue?5301", "webpack://sfp_ext/./src/view/document/list.vue?fb5d"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div style=\"padding: 15px;display: flex\">\r\n            <a href=\"javascript:;\" @click=\"selectAll\">\r\n                <i v-if=\"all_sel == 1\" style=\"font-size: 20px;color: #0080FF\" class=\"el-icon-success\"></i>\r\n                <i v-else style=\"font-size: 20px;color: #898989\" class=\"el-icon-circle-check\"></i>\r\n                <span style=\"font-size: 16px\">\r\n                    全选\r\n                </span>\r\n            </a>\r\n            <a href=\"javascript:;\" @click=\"printPreview\" style=\"margin-left: 15px\">\r\n                <i style=\"font-size: 20px;color: #898989\" class=\"el-icon-printer\"></i>\r\n                <span style=\"font-size: 16px\">\r\n                    打印\r\n                </span>\r\n            </a>\r\n        </div>\r\n        <div style=\"display: flex;\">\r\n            <div>\r\n                <div v-for=\"(d,d_idx) in data_list\" :key=\"d_idx\">\r\n                    <div :style=\"{minHeight:ele_data.height+'px'}\" style=\"display: flex;flex-direction: column;justify-content: center;padding: 15px\">\r\n                        <a href=\"javascript:;\" style=\"font-size: 20px;\" @click=\"d.sel == 1 ? d.sel = 0 : d.sel = 1\">\r\n                            <i v-if=\"d.sel == 1\" style=\"color: #0080FF\" class=\"el-icon-success\"></i>\r\n                            <i v-else style=\"color: #898989\" class=\"el-icon-circle-check\"></i>\r\n                        </a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"container-print-page\">\r\n                <div v-for=\"(data,data_idx) in data_list\" :key=\"data_idx\" :id=\"'container-print-page'+data_idx\">\r\n                    <div class=\"container1-page\" :style=\"{\r\n                    width:ele_data.width+'px',\r\n                    minHeight:ele_data.height+'px',\r\n                    borderWidth: ele_data.border[0]+'px '+ele_data.border[1]+'px '+ele_data.border[2]+'px '+ele_data.border[3]+'px',\r\n                    borderStyle : 'solid',\r\n                    borderColor : '#898989',\r\n                    margin : ele_data.margin[0]+'px '+ele_data.margin[1]+'px '+ele_data.margin[2]+'px '+ele_data.margin[3]+'px'}\">\r\n                        <print-item :tasks=\"ele_data.tasks\" :styleProps=\"{width:ele_data.width+'px'}\" :data=\"data\"/>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import printItem from \"./print_item\";\r\n    export default {\r\n        name: \"printList\",\r\n        display: \"printList\",\r\n        order: 15,\r\n        components: {\r\n            printItem\r\n        },\r\n        data() {\r\n            return {\r\n                all_sel:1,\r\n                uid : '',\r\n                doc_id: '',\r\n                ele_data: {\r\n                    id: 'page',\r\n                    name:'',\r\n                    width : 960,\r\n                    height : 50,\r\n                    border: [0,0,0,0],\r\n                    margin: [0,0,0,0],\r\n                    tasks:[]\r\n                },\r\n                data_list:[]\r\n            };\r\n        },\r\n        created(){\r\n            this.uid = this.$route.query.uid || '';\r\n            this.doc_id = this.$route.query.doc_id || '';\r\n            this.init(this.uid, this.doc_id);\r\n        },\r\n        methods: {\r\n            init(uid,doc_id){\r\n                this.$http.post('printing/template/list', {uid:uid,doc_id:doc_id}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        for (let d of rs.data) {\r\n                            d.sel = 1;\r\n                        }\r\n                        this.data_list = rs.data;\r\n                        this.ele_data = rs.form_data;\r\n                        // 等待 DOM 更新后再打印\r\n                        // this.$nextTick(() => {\r\n                        //     setTimeout(() => {\r\n                        //         try {\r\n                        //             window.print();\r\n                        //         } catch (error) {\r\n                        //             this.$message.error('打印失败：' + error.message);\r\n                        //         }\r\n                        //     }, 500); // 增加延迟确保渲染完成\r\n                        // });\r\n                    } else {\r\n                        this.$message.error(rs.message || '获取打印数据失败');\r\n                    }\r\n                }).catch((error) => {\r\n                    this.$message.error('获取打印数据失败：' + (error.message || '未知错误'));\r\n                });\r\n            },\r\n            selectAll(){\r\n                if (this.all_sel == 1){\r\n                    this.all_sel = 0;\r\n                } else {\r\n                    this.all_sel = 1;\r\n                }\r\n                for (let d of this.data_list) {\r\n                    d.sel = this.all_sel;\r\n                }\r\n            },\r\n            printPreview(){\r\n                const iframe  = document.createElement(\"iframe\");\r\n                const f  = document.body.appendChild(iframe);\r\n                iframe.id = \"myIframe\";\r\n                iframe.setAttribute(\r\n                    \"style\",\r\n                    \"position:absolute;width:0;height:0;top:-10px;left:-10px;\"\r\n                );\r\n                const w = f.contentWindow || f.contentDocument;\r\n                // eslint-disable-next-line prefer-const\r\n                const doc = f.contentDocument || f.contentWindow.document;\r\n                doc.open();\r\n                for(let i=0;i<this.data_list.length;i++){\r\n                    if (this.data_list[i].sel == 1){\r\n                        doc.write($('#container-print-page'+i).html());\r\n                    }\r\n                }\r\n                doc.close();\r\n                w.print();\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style scoped>\r\n    .container-print-page{\r\n        background-color: #fff;\r\n        text-align: center;\r\n        margin-top: 10px;\r\n        padding: 0;\r\n    }\r\n    .container1-page{\r\n        background-color: #fff;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticStyle:{\"padding\":\"15px\",\"display\":\"flex\"}},[_c('a',{attrs:{\"href\":\"javascript:;\"},on:{\"click\":_vm.selectAll}},[(_vm.all_sel == 1)?_c('i',{staticClass:\"el-icon-success\",staticStyle:{\"font-size\":\"20px\",\"color\":\"#0080FF\"}}):_c('i',{staticClass:\"el-icon-circle-check\",staticStyle:{\"font-size\":\"20px\",\"color\":\"#898989\"}}),_c('span',{staticStyle:{\"font-size\":\"16px\"}},[_vm._v(\" 全选 \")])]),_c('a',{staticStyle:{\"margin-left\":\"15px\"},attrs:{\"href\":\"javascript:;\"},on:{\"click\":_vm.printPreview}},[_c('i',{staticClass:\"el-icon-printer\",staticStyle:{\"font-size\":\"20px\",\"color\":\"#898989\"}}),_c('span',{staticStyle:{\"font-size\":\"16px\"}},[_vm._v(\" 打印 \")])])]),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',_vm._l((_vm.data_list),function(d,d_idx){return _c('div',{key:d_idx},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\",\"padding\":\"15px\"},style:({minHeight:_vm.ele_data.height+'px'})},[_c('a',{staticStyle:{\"font-size\":\"20px\"},attrs:{\"href\":\"javascript:;\"},on:{\"click\":function($event){d.sel == 1 ? d.sel = 0 : d.sel = 1}}},[(d.sel == 1)?_c('i',{staticClass:\"el-icon-success\",staticStyle:{\"color\":\"#0080FF\"}}):_c('i',{staticClass:\"el-icon-circle-check\",staticStyle:{\"color\":\"#898989\"}})])])])}),0),_c('div',{staticClass:\"container-print-page\"},_vm._l((_vm.data_list),function(data,data_idx){return _c('div',{key:data_idx,attrs:{\"id\":'container-print-page'+data_idx}},[_c('div',{staticClass:\"container1-page\",style:({\n                width:_vm.ele_data.width+'px',\n                minHeight:_vm.ele_data.height+'px',\n                borderWidth: _vm.ele_data.border[0]+'px '+_vm.ele_data.border[1]+'px '+_vm.ele_data.border[2]+'px '+_vm.ele_data.border[3]+'px',\n                borderStyle : 'solid',\n                borderColor : '#898989',\n                margin : _vm.ele_data.margin[0]+'px '+_vm.ele_data.margin[1]+'px '+_vm.ele_data.margin[2]+'px '+_vm.ele_data.margin[3]+'px'})},[_c('print-item',{attrs:{\"tasks\":_vm.ele_data.tasks,\"styleProps\":{width:_vm.ele_data.width+'px'},\"data\":data}})],1)])}),0)])])\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.container-print-page[data-v-0c24aac1]{\\n    background-color: #fff;\\n    text-align: center;\\n    margin-top: 10px;\\n    padding: 0;\\n}\\n.container1-page[data-v-0c24aac1]{\\n    background-color: #fff;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=0c24aac1&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"3433b382\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=0c24aac1&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=0c24aac1&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./list.vue?vue&type=template&id=0c24aac1&scoped=true\"\nimport script from \"./list.vue?vue&type=script&lang=js\"\nexport * from \"./list.vue?vue&type=script&lang=js\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=0c24aac1&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c24aac1\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('0c24aac1')) {\n      api.createRecord('0c24aac1', component.options)\n    } else {\n      api.reload('0c24aac1', component.options)\n    }\n    module.hot.accept(\"./list.vue?vue&type=template&id=0c24aac1&scoped=true\", function () {\n      api.rerender('0c24aac1', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/document/list.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=0c24aac1&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=template&id=0c24aac1&scoped=true\""], "names": [], "sourceRoot": ""}