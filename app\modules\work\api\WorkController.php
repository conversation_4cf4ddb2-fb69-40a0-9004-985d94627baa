<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Work\Service\DataService;
use Envsan\Modules\Work\Service\WorkService;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Work\Util\Constant;

class WorkController extends SuperController
{
    public function initAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = '';
            $user = SessionData::user();
            $group_row = Group::findFirst('id='.$user->group_id);
            $ret->group_name = '';
            if (!empty($group_row)){
                $ret->group_name = $group_row->name;
            }
            $ret->type_list = $rs->getTypeList();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function getflowAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $rtn = $rs->getFlow();
            $ret->message = $rtn->message;
            if (empty( $ret->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function goodsAction(){
        if ($this->request->isPost()) {
            $rs = new WorkService();
            $ret = new JsonData();
            $rtn = $rs->getGoodsData();
            $ret->message = $rtn->message;
            if (empty( $ret->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function createAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function editAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $rtn = $rs->getEditData();
            $ret->message = $rtn->message;
            if (empty($ret->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function mylistAction()
    {
        $as = new WorkService();
        $builder = $as->selectAll();
        $page = $this->getPagination($builder);
        $page->rows = $as->setDetail($page->rows->toArray());
        $offset = intval($this->request->get('offset', 'int'));
        if ($offset == 0) {
            $page->count = $as->getCount($builder);
        }
        return json_encode($page);
    }

    public function reviewAction()
    {
        $as = new WorkService();
        $builder = $as->selectReviewAll();
        $page = $this->getPagination($builder);
        $page->rows = $page->rows->toArray();
        $offset = intval($this->request->get('offset', 'int'));
        if ($offset == 0){
            $page->count = $as->getCount($builder);
        }
        return json_encode($page);
    }

    public function readAction()
    {
        $as = new WorkService();
        $builder = $as->selectReadAll();
        $page = $this->getPagination($builder);
        $page->rows = $as->setDetail($page->rows->toArray());
        $offset = intval($this->request->get('offset', 'int'));
        if ($offset == 0){
            $page->count = $as->getCount($builder);
        }
        return json_encode($page);
    }

    public function moreAction($uid){
        $ds = new DataService();
        $row = $ds->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);
        $as = new WorkService();
        $builder = $as->selectMore($row);
        $page = $this->getPagination($builder);
        $page->rows = $page->rows->toArray();
        foreach ($page->rows as &$row) {
            $anchor_data = json_decode($row['anchor_data'],true);
            if ($row['status'] < 70){
                $row['anchor_name'] = $anchor_data['name'];
            } else {
                $row['anchor_name'] = '已完成';
            }
            $row['anchor_data'] = null;
        }
        return json_encode($page);
    }

    public function readsaveAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->readSave();
            if (empty($ret->message)){
                $ret->uid = $rs->checkNextReadData();
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function viewAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $cs = new WorkService();
            $rtn = $cs->getWorkData();
            $ret->message = $rtn->message;
            if (empty($ret->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function deleteAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $cs = new WorkService();
            $ret->message = $cs->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function commentAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->commentSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function passAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->pass();
            if (empty($ret->message)){
                $ret->uid = $rs->checkNextReviewData();
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function rejectAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->reject();
            if (empty($ret->message)){
                $ret->uid = $rs->checkNextReviewData();
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function cancelAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->cancelSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function pressingAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->pressingSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function knowAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->knowSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function userlistAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $cs = new WorkService();
            return json_encode($cs->getUserList());
        }
    }
}
