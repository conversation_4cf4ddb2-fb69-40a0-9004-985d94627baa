<?php

namespace Envsan\Modules\Purchase\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseDefectHandling extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $handling_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=5200, nullable=true)
     */
    public $goods_model;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var double
     * @Column(type="double", length=10, nullable=true)
     */
    public $arrival_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $inspection_id;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $inspection_code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $supplier_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $supplier_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $defect_reasons;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $handling_method;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $handling_method_name;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $comment;

    /**
     *
     * @var double
     * @Column(type="double", length=10, nullable=true)
     */
    public $rejected_qty;

    /**
     *
     * @var double
     * @Column(type="double", length=10, nullable=true)
     */
    public $available_qty;

    /**
     *
     * @var integer
     * @Column(type="integer", length=1, nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $owner;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $approved_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_defect_handling';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseDefectHandling[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseDefectHandling
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }

}
