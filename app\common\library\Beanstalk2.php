<?php
namespace Envsan\Common\Library;

use Phalcon\Queue\Beanstalk;
use Phalcon\Queue\Beanstalk\Job;

class Beanstalk2 extends Beanstalk
{
    public function put2($data)
    {
        $priority = self::DEFAULT_PRIORITY;
        $delay = self::DEFAULT_DELAY;
        $ttr = self::DEFAULT_TTR;

        $length = strlen($data);
        $this->write('put ' . $priority . ' ' . $delay . ' ' . $ttr . ' ' . $length . '\r\n' . $data);

        $response = $this->readStatus();
        $status = $response[0];

        if( $status != 'INSERTED' && $status != 'BURIED') {
            return false;
        }

        return intval($response[1]);
    }

    public function reserve($timeout = null)
    {
        if ($timeout != null)
            $command = 'reserve-with-timeout ' . $timeout;
        else
            $command = 'reserve';

        $this->write($command);

        $response = $this->readStatus();
        if (empty($response))
            return false;

        if( $response[0] != 'RESERVED')
            return false;

        return new Job($this, $response[1], $this->read($response[2]));
    }
}