{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;padding-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="form-group">
                        <label>拒绝原因<span class="required"> * </span></label>
                        <div>
                            <textarea class="form-control" name="remarks" v-model="remarks" rows="3" maxlength="200" style="resize: none" required></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn blue" onclick="uploadImg()"><i class="fa fa-upload"></i>&nbsp;上传文件</button>
                        <div class="comment-img-group">
                            <div v-for="(url, idx) in files" class="comment-img-box">
                                <a :href="base_path + url" data-lightbox="reject_file" data-title="原因图片" title="查看图片" style="position: relative;">
                                    <div class="btn-times" @click="delImg(idx, $event)">
                                        <i class="fa fa-times-circle"></i>
                                    </div>
                                    <img :src="base_path + url">
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{ partial('uploader_work') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            remarks: '',
            base_path: '{{ base_path }}',
            files: []
        },
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                let param = {
                    uid: '{{ uid }}',
                    files: encodeURI(JSON.stringify(this.files)),
                    value: this.remarks
                };

                showSpin();
                $.post('{{ url('work/work/reject/' ~ uid) }}', param, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result2 = 'ok';
                        top.window.data_uid = rs.uid;
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            delImg: function (idx, e) {
                e.preventDefault();
                e.stopPropagation();

                this.files.splice(idx, 1);
            }
        }
    });

    initUpLoader('{{ url('work/work/uploadimg/reject') }}');
    var select_files;
    var upload_index = 0;
    var upload_msg = [];
    var loading_idx;
    function filesQueued(files) {
        if ((app.files.length + files.length) > 5) {
            alertWarning("最多上传5张照片");
            return false;
        }

        upload_msg = [];
        upload_index = 0;
        select_files = files;
        fileUpload();
    }

    function fileUpload() {
        loading_idx = showLoading("照片上传中(" + (upload_index + 1) + "/" + select_files.length + ")，请稍后...");
        uploader.options.formData = {img_no: upload_index};
        uploader.upload(select_files[upload_index]);
        upload_index++;
    }

    function uploadSuccess(rs) {
        if (rs.status == 'ok') {
            app.files.push(rs.file_name);
        } else {
            upload_msg.push(rs.message);
        }

        if (upload_index < select_files.length) {
            fileUpload();
        }
    }

    function uploadFinished() {
        layer.close(loading_idx);
        if (upload_msg.length > 0) {
            var msg = '';
            for (var i = 0; i < upload_msg.length; i++) {
                msg += upload_msg[i] + '<br>';
            }
            msg = msg.substring(0, msg.length - 4);
            alertWarning(msg);
        }
    }
</script>
<style>
    .comment-img-group {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin: 10px -7px 0;
    }

    .comment-img-box {
        width: 150px;
        height: 100px;
        margin: 10px 7px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .comment-img-box img {
        max-width: 150px;
        max-height: 100px;
    }

    .btn-times {
        position: absolute;
        top: -8px;
        right: -8px;
        cursor: pointer;
    }

    .btn-times i {
        font-size: 20px;
        line-height: 20px;
        color: #e7505a;
    }
    .btn-times:hover i {
        color: #e12330;
    }
</style>
