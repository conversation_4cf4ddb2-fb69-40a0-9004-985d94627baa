<?php

namespace Envsan\Common\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Model\OperationHistory;
use Phalcon\Mvc\User\Component;

class HistoryService extends Component
{

    /**
     * 记录创建历史
     */
    public function recordCreate($model, $tableName = null, $eventName = null, $newData = null)
    {
        try {
            $startTime = microtime(true);
            
            $history = new OperationHistory();
            
            $requestUri = $this->getRequestUri();
            $history->event_name = $requestUri;
            $history->table_name = $tableName ?: $model->getSource();
            $history->record_id = $model->id;
            $history->operation_type = 'INSERT';
            $history->old_data = null;
            $history->new_data = json_encode($newData, JSON_UNESCAPED_UNICODE);
            $history->operator_user = $this->getCurrentUserName();
            $history->ip_address = $this->getClientIp();
            $history->update_date = date('Y-m-d H:i:s');
            $history->update_by = $this->getCurrentUserId();
            $history->del_flag = 0;
            $history->owner = $this->getCurrentOwner();
            
            \Envsan\Common\Component\Logger::info('HistoryService记录CREATE历史', 
                'table=' . $history->table_name, 
                'record_id=' . $history->record_id, 
                'event=' . $requestUri,
                'operator=' . $history->operator_user
            );
            
            $result = $history->save();
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            if ($result) {
                \Envsan\Common\Component\Logger::debug('CREATE历史记录成功', 
                    'history_id=' . $history->id, 
                    'time=' . $executionTime . 'ms'
                );
            } else {
                \Envsan\Common\Component\Logger::error('CREATE历史记录失败', 
                    implode(',', array_map('strval', $history->getMessages()))
                );
            }
            
            return $result;
            
        } catch (\Exception $e) {
            \Envsan\Common\Component\Logger::error('CREATE历史记录异常', 
                $e->getMessage(), 
                $e->getFile() . ':' . $e->getLine()
            );
            return false;
        }
    }

    /**
     * 记录更新历史
     */
    public function recordUpdate($model, $tableName = null, $oldData = null, $changedData = null, $eventName = null)
    {
        try {
            $startTime = microtime(true);
            
            $currentData = $model->toArray();
            
            // 判断是否为逻辑删除
            $operationType = 'UPDATE';
            if (isset($currentData['del_flag']) && $currentData['del_flag'] == 1) {
                $operationType = 'DELETE';
            }
            
            // 使用变化的数据替代完整的新数据（如果提供了的话）
            $newDataToRecord = $changedData ?: $currentData;
            
            $history = new OperationHistory();
            
            $requestUri = $this->getRequestUri();
            $history->event_name = $requestUri;
            $history->table_name = $tableName ?: $model->getSource();
            $history->record_id = $model->id;
            $history->operation_type = $operationType;
            $history->old_data = $oldData ? json_encode($oldData, JSON_UNESCAPED_UNICODE) : null;
            $history->new_data = json_encode($newDataToRecord, JSON_UNESCAPED_UNICODE);
            $history->operator_user = $this->getCurrentUserName();
            $history->ip_address = $this->getClientIp();
            $history->update_date = date('Y-m-d H:i:s');
            $history->update_by = $this->getCurrentUserId();
            $history->del_flag = 0;
            $history->owner = $this->getCurrentOwner();
            
            \Envsan\Common\Component\Logger::info('HistoryService记录UPDATE历史', 
                'table=' . $history->table_name, 
                'record_id=' . $history->record_id, 
                'type=' . $operationType,
                'event=' . $requestUri,
                'changed_fields=' . ($oldData ? count($oldData) : 0),
                'operator=' . $history->operator_user
            );
            
            $result = $history->save();
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            if ($result) {
                \Envsan\Common\Component\Logger::debug('UPDATE历史记录成功', 
                    'history_id=' . $history->id, 
                    'time=' . $executionTime . 'ms'
                );
            } else {
                \Envsan\Common\Component\Logger::error('UPDATE历史记录失败', 
                    implode(',', array_map('strval', $history->getMessages()))
                );
            }
            
            return $result;
            
        } catch (\Exception $e) {
            \Envsan\Common\Component\Logger::error('UPDATE历史记录异常', 
                $e->getMessage(), 
                $e->getFile() . ':' . $e->getLine()
            );
            return false;
        }
    }

    /**
     * 记录删除历史
     */
    public function recordDelete($model, $tableName = null)
    {
        $history = new OperationHistory();
        
        $history->event_name = $this->getRequestUri();
        $history->table_name = $tableName ?: $model->getSource();
        $history->record_id = $model->id;
        $history->operation_type = 'DELETE';
        $history->old_data = json_encode($model->toArray(), JSON_UNESCAPED_UNICODE);
        $history->new_data = null;
        $history->operator_user = $this->getCurrentUserName();
        $history->ip_address = $this->getClientIp();
        $history->update_date = date('Y-m-d H:i:s');
        $history->update_by = $this->getCurrentUserId();
        $history->del_flag = 0;
        $history->owner = $this->getCurrentOwner();
        
        return $history->save();
    }

    private function getCurrentOwner()
    {
        try {
            return SessionData::user()->owner ?? 1;
        } catch (\Exception $e) {
            return 1;
        }
    }

    private function getCurrentUserId()
    {
        try {
            return SessionData::user()->id;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getCurrentUserName()
    {
        try {
            return SessionData::user()->real_name ?? SessionData::user()->name ?? 'system';
        } catch (\Exception $e) {
            return 'system';
        }
    }

    private function getClientIp()
    {
        try {
            return $this->request->getClientAddress();
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getRequestUri()
    {
        try {
            // 解析URI
            $uri = $this->request->getURI();
            return $this->parseUriToModuleFormat($uri);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 将URI解析为模块:控制器:动作格式
     * 例如: /sys/user/create -> sys:user:create
     * 例如: /mes/plan/edit/123 -> mes:plan:edit
     * 例如: /api/mes/user/create -> mes:user:create (API路由特殊处理)
     */
    private function parseUriToModuleFormat($uri)
    {
        // 移除查询参数
        $uri = strtok($uri, '?');
        
        // 移除开头的斜杠并分割路径
        $parts = array_filter(explode('/', trim($uri, '/')));
        
        // 如果没有足够的部分，返回原URI
        if (count($parts) < 2) {
            return $uri;
        }
        
        // API路径特殊处理：/api/module/controller/action
        if ($parts[0] === 'api' && count($parts) >= 4) {
            $module = $parts[1] ?? '';      // api后的第一个是module
            $controller = $parts[2] ?? '';  // 第二个是controller  
            $action = $parts[3] ?? 'index'; // 第三个是action
        } else {
            // 普通路径：/module/controller/action
            $module = $parts[0] ?? '';
            $controller = $parts[1] ?? '';
            $action = $parts[2] ?? 'index';
        }
        
        // 过滤掉数字ID参数（通常在第4个位置或action中包含数字）
        if (is_numeric($action)) {
            $action = 'view'; // 如果action是数字，通常是查看操作
        }
        
        // 如果action包含特殊字符，只取主要部分
        $action = preg_replace('/[^a-zA-Z]/', '', $action);
        if (empty($action)) {
            $action = 'index';
        }
        
        return "{$module}:{$controller}:{$action}";
    }

}