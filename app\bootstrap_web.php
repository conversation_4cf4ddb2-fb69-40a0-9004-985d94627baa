<?php

use Phalcon\Di\FactoryDefault;
use Phalcon\Mvc\Application;
use Envsan\Common\Component\Logger as Logger;

error_reporting(E_ALL);

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

class TimeComputer
{
    private $starttime;

    public function start()
    {
        $this->starttime = microtime(true);
    }

    public function end()
    {
        if($_SERVER['CONTENT_TYPE']==='') {
            $endtime = microtime(true);
            $timediff = $endtime - $this->starttime;
            $timediff = round($timediff * 1000, 1);
            //echo PHP_EOL.'<!--run time: '.$timediff.'ms-->';
        }
    }
}

try {
    $tc = new TimeComputer();
    $tc->start();

    /**
     * The FactoryDefault Dependency Injector automatically registers the services that
     * provide a full stack framework. These default services can be overidden with custom ones.
     */
    $di = new FactoryDefault();

    /**
     * Include general services
     */
    require APP_PATH . '/config/services.php';

    /**
     * Include web environment specific services
     */
    require APP_PATH . '/config/services_web.php';

    /**
     * Get config service for use in inline setup below
     */
    $config = $di->getConfig();

    /**
     * Include Autoloader
     */
    include APP_PATH . '/config/loader.php';

    include APP_PATH . '/vendor/autoload.php';

    /**
     * Handle the request
     */
    $application = new Application($di);

    /**
     * Register application modules
     */
    $application->registerModules([
        'common'   => ['className' => 'Envsan\Modules\Common\Module'],
        'sys'      => ['className' => 'Envsan\Modules\Sys\Module'],
        'printing' => ['className' => 'Envsan\Modules\Printing\Module'],
        'work'     => ['className' => 'Envsan\Modules\Work\Module'],
        'trade'    => ['className' => 'Envsan\Modules\Trade\Module'],
        'purchase' => ['className' => 'Envsan\Modules\Purchase\Module'],
        'equ'      => ['className' => 'Envsan\Modules\Equ\Module'],
        'mes'      => ['className' => 'Envsan\Modules\Mes\Module'],
        'quality'  => ['className' => 'Envsan\Modules\Quality\Module'],
        'screen'  => ['className' => 'Envsan\Modules\Screen\Module'],
    ]);

    /**
     * Include routes
     */
    require APP_PATH . '/config/routes.php';

    echo  $application->handle()->getContent();

    $tc->end();
} catch (\Exception $e) {
    echo $e->getMessage() . '<br>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';

    Logger::error($e->getMessage(), $e->getTraceAsString());
}
