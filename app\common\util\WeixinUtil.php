<?php
namespace Envsan\Common\Util;

use CodeItNow\BarcodeBundle\Utils\QrCode;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use GuzzleHttp\Client;
use Phalcon\Mvc\User\Component;

class WeixinUtil extends Component
{
    public function getAccessTokenWx($wx_appid, $wx_secret, $js_ticket_flag = false, $refresh = false)
    {
        try {
            $redis_key = $wx_appid.($js_ticket_flag ? '0' : '1');
            $redis = new \Redis();
            $redis->connect($this->config->redis->host, $this->config->redis->port);
            $redis->auth($this->config->redis->password);
            $token = $redis->get($redis_key);
            if ($token == false || $refresh) {
                $token = [];
                $url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid='.$wx_appid.'&secret='.$wx_secret;
                $client = new Client();
                $res = $client->request('get', $url, [
                    'verify' => false
                ]);
                $body_data = json_decode($res->getBody(), true);
                if (array_key_exists('errcode', $body_data)) {
                    Logger::error('【获取公众号Token失败】'.$body_data['errcode'],$wx_appid);
                    return false;
                }

                $token['access_token'] = $body_data['access_token'];
                $expire_time = CvtUtil::emptyToInt($body_data['expires_in']);
                $before_seconds = 10 * 60;
                $token['jssdk_ticket'] = '';
                if ($js_ticket_flag){
                    $url = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token='.$token['access_token'].'&type=jsapi';
                    $client = new Client();
                    $res = $client->request('get', $url, [
                        'verify' => false
                    ]);
                    $body_data = json_decode($res->getBody(), true);
                    if (!empty($body_data['errcode'])) {
                        Logger::error('【获取共享公众号jssdk_ticket失败】'.$body_data['errcode']);
                    } else {
                        $token['jssdk_ticket'] = $body_data['ticket'];
                    }
                }
                if ($expire_time > $before_seconds) {
                    $diff_seconds = $expire_time - $before_seconds;
                } else {
                    $diff_seconds = $expire_time;
                }
                $redis->setex($redis_key, $diff_seconds, json_encode($token));
            } else {
                $token = json_decode($token,true);
            }
            return $token;
        } catch (\Exception $e) {
            Logger::error('【获取公众号Token失败】'.$e->getMessage());
            return false;
        }
    }

    public function sendReviewMsg($open_id, $state, $type_name, $submit_by, $time)
    {
        $owner = SessionData::owner();
        if ($owner == false) {
            return '会话过期';
        }

        $token = $this->getAccessTokenWx($owner->wx_appid,$owner->wx_secret);
        if ($token == false) {
            return 'AccessToken获取失败';
        }

        $body_data = $this->templateSend($token, $owner, $open_id, $state, $type_name, $submit_by, $time);
        if ($body_data['errcode'] == 0) {
            return '';
        } else if ($body_data['errcode'] == 40001) {
            $token = $this->getAccessTokenWx($owner->wx_appid, $owner->wx_secret, false, true);
            if ($token == false) {
                return 'AccessToken获取失败';
            }

            $body_data = $this->templateSend($token, $owner, $open_id, $state, $type_name, $submit_by, $time);
            if ($body_data['errcode'] == 0) {
                return '';
            } else {
                Logger::error('【微信消息再次发送失败】'.$body_data['errcode'].'：'.$body_data['errmsg']);
                return '消息发送失败';
            }
        } else {
            Logger::error('【微信消息发送失败】'.$body_data['errcode'].'：'.$body_data['errmsg']);
            return '消息发送失败';
        }
    }

    private function templateSend($token, $owner, $open_id, $state, $type_name, $submit_by, $time)
    {
        $wx_data = CvtUtil::emptyToArray($owner->wx_data);

        $rdi_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$owner->wx_appid.'&redirect_uri=';
        $rdi_url .= urlencode('http://'.$owner->base_url.'/api/work/index/index');
        $rdi_url .= '&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect';

        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token='.$token['access_token'];
        $client = new Client();

        $data = [
            'thing8' => ['value' => $type_name],
            'thing10' => ['value' => $submit_by],
            'time12' => ['value' => $time]
        ];

        $res = $client->request('post', $url, [
            'verify' => false,
            'body' => json_encode([
                'touser' => $open_id,
                'template_id' => $wx_data['tid_review'],
                'url' => $rdi_url,
                'data' => $data
            ], JSON_UNESCAPED_UNICODE)
        ]);
        return json_decode($res->getBody(), true);
    }
}