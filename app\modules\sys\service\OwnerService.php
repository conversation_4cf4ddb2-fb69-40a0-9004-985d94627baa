<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Dict;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Model\Owner;
use Envsan\Modules\Sys\Model\Role;
use Envsan\Modules\Sys\Model\Sequence;
use Envsan\Modules\Sys\Model\User;
use Phalcon\Mvc\User\Component;

class OwnerService extends Component
{
    public function selectAll($find='')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\Owner')
            ->orderBy('id desc')
            ->where('del_flag=0');

        if($find!='' ){
            $builder->andWhere("contact like ?1 or company like ?1", [1 => "%$find%"]);
        }
        return $builder;
    }

    public function create()
    {
        $row = new Owner();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $domain = $this->request->getPost('domain', ['string', 'trim']);
        $erow = $this->selectByDomain($domain);
        if($erow!=null && $erow->id!=$row->id)
            return '二级域名已存在!';

        $contact = $this->request->getPost('contact', ['string', 'trim']);
        $company = $this->request->getPost('company', ['string', 'trim']);
        $short_name = $this->request->getPost('short_name', ['string', 'trim']);
        $start_date = $this->request->getPost('start_date', ['string', 'trim']);
        $end_date = $this->request->getPost('end_date', ['string', 'trim']);
        $mobile = $this->request->getPost('mobile', ['string', 'trim']);
        $is_contract_auto = $this->request->getPost('is_contract_auto', ['string', 'trim']);
        $is_weighing_auto = $this->request->getPost('is_weighing_auto', ['string', 'trim']);
        $is_stock_run = $this->request->getPost('is_stock_run', ['string', 'trim']);
        $goods_ids = $this->request->getPost('goods_ids', ['string', 'trim']);
        $goods_names = $this->request->getPost('goods_names', ['string', 'trim']);
        if(empty($domain) ||
            empty($contact) ||
            empty($company) ||
            empty($short_name) ||
            empty($mobile) ||
            empty($start_date) ||
            empty($end_date) ||
            empty($goods_ids) ||
            empty($goods_names))
            return ErrorHelper::WRONG_INPUT;

        if($end_date<=$start_date)
            return '账户有效期间不正确';

        $row->domain = $domain;
        $row->contact = $contact;
        $row->company = $company;
        $row->short_name = $short_name;
        $row->start_date = $start_date;
        $row->end_date = $end_date;
        $row->mobile = $mobile;
        $row->goods_ids = $goods_ids;
        $row->goods_names = $goods_names;
        $row->is_contract_auto = $is_contract_auto;
        $row->is_weighing_auto = $is_weighing_auto;
        $row->is_stock_run = $is_stock_run;
        $row->status = Owner::STATUS_NORMAL;
        $user = SessionData::user();
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;

        $this->db->begin();
        try {
            $row->save();
            if($act=='create') {
                $this->createUser($row);
                $this->createDict($row);
                $this->createSequence($row);
            }
            $this->db->commit();
            return '';
        }
        catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage(), $e->getTraceAsString());
            return '创建失败';
        }
    }

    private function createDict($owner)
    {
        $dicts = [
            ['name'=>'组织类型', 'sector'=>'', 'section'=>'sys:group:type', 'visible'=>Dict::DICT_VISIBLE_NO, 'val'=>'集团,公司,部门,工厂', 'comment'=>'组织类型'],
        ];

        foreach ($dicts as $dict){
            $row = new Dict();
            $row->uid = UUID::make();
            $row->name = $dict['name'];
            $row->sector = $dict['sector'];
            $row->section = $dict['section'];
            $row->visible = Dict::DICT_VISIBLE_YES;

            if(isset($dict['val']))
                $row->val = $dict['val'];
            else
                $row->val = '';

            $row->update_date = DateUtil::now();
            $row->update_by = 0;
            $row->owner = $owner->id;

            if(isset($dict['comment']))
                $row->comment = $dict['comment'];
            if(isset($dict['visible']))
                $row->visible = $dict['visible'];

            $row->save();
        }
    }

    private function createUser($owner)
    {
        $group = new Group();
        $group->uid = UUID::make();
        $group->pid = 0;
        $group->name = $owner->company;
        $group->type = '';
        $group->update_date = DateUtil::now();
        $group->update_by = 0;
        $group->owner = $owner->id;

        $group->save();

        $role = new Role();
        $role->uid = UUID::make();
        $role->group_id = $group->id;
        $role->name = '系统管理员';
        $role->identity = 'admin';
        $role->create_date = DateUtil::now();
        $role->update_date = DateUtil::now();
        $role->update_by = 0;
        $role->owner = $owner->id;
        $role->save();

        $user = new User();
        $user->uid = UUID::make();
        $user->group_id = $group->id;
        $user->role_id = $role->id;
        $user->empno = '00001';
        $user->login_name = $owner->domain.'admin';
        $user->password = $owner->domain.'admin123';
        $user->real_name = $owner->contact;
        $user->gender = 0;
        $user->email = '';
        $user->mobile = $owner->mobile;
        $user->type = 0;
        $user->account_status = User::ACCOUNT_NORMAL;
        $user->create_date = DateUtil::now();
        $user->update_date = DateUtil::now();
        $user->update_by = 0;
        $user->owner = $owner->id;
        $user->save();
    }

    private function createSequence($owner){
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\Sequence')
            ->where('owner = 1');
        $sequence_rows = $builder->getQuery()->execute();
        foreach ($sequence_rows as $sequence_row) {
            $sequence = new Sequence();
            $sequence->no_count = 0;
            $sequence->type = $sequence_row->type;
            $sequence->header = $sequence_row->header;
            $sequence->num_length = $sequence_row->num_length;
            $sequence->remarks = $sequence_row->remarks;
            $sequence->owner = $owner->id;
            $sequence->save();
        }
    }

    public function selectById($id)
    {
        return Owner::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function selectByDomain($domain)
    {
        return Owner::findFirst(['domain=?1', 'bind'=>[1=>$domain]]);
    }

    public function deleteById($id)
    {
        $row = Owner::findFirst(['id=?1', 'bind'=>[1=>$id]]);
        if($row!=null)
            return $row->delete();
        return false;
    }

    public function disableById($id, $action)
    {
        $row = Owner::findFirst(['id=?1', 'bind'=>[1=>$id]]);
        if($row!=null){
            $row->status = ($action=='yes' ? Owner::STATUS_DISABLED:Owner::STATUS_NORMAL);
            if($row->save())
                return '';
        }
        return ErrorHelper::UNKOWN;
    }
}