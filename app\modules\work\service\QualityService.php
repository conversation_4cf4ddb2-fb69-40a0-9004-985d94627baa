<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Mes\Model\MesCheckLogs;
use Envsan\Modules\Mes\Model\MesNotice;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Model\MesProductQuality;
use Envsan\Modules\Sys\Model\SysDict;

class QualityService extends BaseService
{
    public function getData(){
        $rtn = new \stdClass();
        $code = $this->request->getPost('code', 'tstring');
        if (empty($code)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        if (mb_strlen($code) != 18){
            $rtn->message = '二维码不正确';
            return $rtn;
        }
        $yw_type = mb_substr($code,0,3);
        if ($yw_type != 'pch') {
            $rtn->message = '请扫描批次单二维码';
            return $rtn;
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t2.id,
                t2.uid,
                t2.status,
                t2.product_id,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->where('t2.del_flag = 0 and t2.uid = ?1',[1 => $code])
            ->orderBy('t2.id');
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            $rtn->message = '批次单不存在';;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        if ($row['status'] > 10){
            $rtn->message = '批次已生产完成，不能质检';
            return $rtn;
        }
        $row['bom_id'] = '';
        $row['bom_name'] = '';
        $row['quality_template_id'] = '';
        $row['quality_template_name'] = '';
        $row['error_cnt'] = '';
        $row['error_remarks'] = '';
        $row['check_data'] = [];
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                    t.id,
                    t.uid,
                    t.quality_template_name,
                    t.form_data,
                    t.remarks,
                    t.product_bom_id,
                    t1.name as bom_name,
                    t1.drawing_data,
                    CONCAT(t1.name, \'（\',t.quality_template_name,\'）\') as name
                ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductQuality', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom', 't.product_bom_id = t1.id','t1')
            ->where('t1.del_flag = 0 and t1.product_id = ?1', [1 => $row['product_id']])
            ->orderBy('t1.data_sort asc,t1.id asc');
        $quality_list = $builder->getQuery()->execute()->toArray();
        foreach ($quality_list as &$quality_item){
            $quality_item['form_data'] = CvtUtil::emptyToArray($quality_item['form_data']);
            $quality_item['drawing_data'] = CvtUtil::emptyToArray($quality_item['drawing_data']);
        }
        $cts = new \Envsan\Modules\Common\Service\CommonService();
        $us = new \Envsan\Modules\Sys\Service\UserService();
        $rtn->message = '';
        $rtn->data = [
            'data' => $row,
            'quality_list' => $quality_list,
            'error_types' => $cts->getDictList('mes:check:errors'),
            'tool_list' => $cts->getDictList('mes:check:tools'),
            'worker_list' => $us->getWorkers(),
        ];
        return $rtn;
    }

    public function getDetail(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.bom_id,
                t1.notice_detail_id,
                t1.quality_template_name,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name,
                t5.name as bom_name,
                ifnull(t1.error_cnt,0) as error_cnt,
                t1.error_flag,
                t1.error_type,
                t1.error_remarks,
                t1.work_date,
                t1.create_time,
                t1.check_data,
                t1.staff_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesCheckLogs', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
            ->where('t1.del_flag = 0 and t1.uid = ?1',[1 => $uid])
            ->orderBy('t1.create_time desc,t1.id');
        $rows = $builder->getQuery()->execute()->toArray();
        if (count($rows) == 0){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0];
        $row['check_data'] = CvtUtil::emptyToArray($row['check_data']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function saveData()
    {

        return $this->executeInTransaction(function () {
            $notice_detail_id = $this->request->getPost('notice_detail_id', 'tstring');
            $bom_id = $this->request->getPost('bom_id', 'tstring');
            $quality_template_id = $this->request->getPost('quality_template_id', 'tstring');
            $error_result = $this->request->getPost('error_result', 'tstring');
            $error_cnt = $this->request->getPost('error_cnt', 'tstring');
            $error_type = $this->request->getPost('error_type', 'tstring');
            $error_remarks = $this->request->getPost('error_remarks', 'tstring');
            $check_data = urldecode($this->request->getPost('check_data', 'tstring'));
            $production_workers = $this->request->getPost('production_workers', 'tstring');
            $check_tools = $this->request->getPost('check_tools', 'tstring');
            if (empty($notice_detail_id) || empty($bom_id) || empty($quality_template_id) || empty($check_tools) || empty($production_workers)) {
                return $this->error(ErrorHelper::WRONG_INPUT . '1');
            }
            if (!empty($error_result)) {
                if (empty($error_cnt) || empty($error_type)) {
                    return $this->error(ErrorHelper::WRONG_INPUT . '2');
                }
            }
            $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $notice_detail_id]]);
            if (empty($notice_detail_row)) {
                return $this->error(ErrorHelper::WRONG_INPUT . '3');
            }
            if ($notice_detail_row->status > 10) {
                return $this->error('批次已生产完成，不能报工');
            }
            $quality_row = MesProductQuality::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $quality_template_id]]);
            if (empty($quality_row)) {
                return $this->error(ErrorHelper::WRONG_INPUT . '4');
            }
            $bom_row = MesProductBom::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $bom_id]]);
            if (empty($bom_row)) {
                return $this->error(ErrorHelper::WRONG_INPUT . '5');
            }

            $sysDicts = SysDict::find(['del_flag = 0']);
            $tools_name = [];
            foreach ($check_tools as $check_tool) {
                foreach ($sysDicts as $sysDict) {
                    if ($sysDict->dict_type == 'mes:check:tools' && $sysDict->id == $check_tool) {
                        $tools_name[] = $sysDict->name;
                        break;
                    }
                }
            }
            $tools_name_str = !empty($tools_name) ? implode(",", $tools_name) : '';

            $userService = new \Envsan\Modules\Sys\Service\UserService();
            $workers = $userService->getWorkers();

            $workers_name = [];
            foreach ($production_workers as $worker) {
                foreach ($workers as $record) {
                    if ($record['id'] == $worker) {
                        $workers_name[] = $record['name'];
                        break;
                    }
                }
            }
            $workers_name_str = !empty($workers_name) ? implode(",", $workers_name) : '';

            $user = SessionData::user();
            $now = DateUtil::now();
            $common = new \Envsan\Modules\Mes\Service\CommonService();
            $work_date = $common->getWorkDate();
            $check_data = CvtUtil::emptyToArray($check_data);
            $logs = new MesCheckLogs();
            $logs->uid = UUID::make();
            $logs->work_date = $work_date;
            $logs->work_month = $common->getWorkMonth($work_date);
            $logs->notice_detail_id = $notice_detail_row->id;
            $logs->bom_id = $bom_row->id;
            $logs->quality_template_id = $quality_row->id;
            $logs->quality_template_name = $quality_row->quality_template_name;
            if (!empty($error_result)) {
                $ps = new ProduceService();
                $error_cost = $ps->getCost($notice_detail_row->product_id, $bom_row->data_sort);
                $logs->error_money = $error_cost;
                $logs->error_cnt = CvtUtil::emptyToInt($error_cnt);
                $logs->error_type = $error_type;
                $logs->error_remarks = CvtUtil::blankToNull($error_remarks);
                $logs->error_flag = 1;
            } else {
                $logs->error_money = null;
                $logs->error_cnt = null;
                $logs->error_type = null;
                $logs->error_remarks = null;
                $logs->error_flag = 0;
            }
            $logs->check_val = CvtUtil::arrayToNull($common->getCheckDataValue($check_data));
            $logs->check_data = CvtUtil::arrayToNull($check_data);
            $logs->check_tools = CvtUtil::arrayToNull($check_tools);
            $logs->tools_name = $tools_name_str;
            $logs->production_workers = CvtUtil::arrayToNull($production_workers);
            $logs->workers_name = $workers_name_str;
            $logs->staff_id = $user->id;
            $logs->staff_name = $user->real_name;
            $logs->create_time = $now;
            $logs->update_date = $now;
            $logs->update_by = $user->id;
            $logs->del_flag = 0;
            $logs->owner = $user->owner;
            $logs->save();
        });

    }

    public function getList(){
        $rtn = new \stdClass();
        $report_date = $this->request->getPost('report_date', 'tstring');
        $common = new \Envsan\Modules\Mes\Service\CommonService();
        $today = $common->getWorkDate();
        if (empty($report_date)){
            $report_date = $today;
        } else {
            if($report_date > $today){
                $rtn->message = '不能选择未来日期';
                return $rtn;
            }
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.bom_id,
                t1.notice_detail_id,
                t1.quality_template_name,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name,
                t5.name as bom_name,
                ifnull(t1.error_cnt,0) as error_cnt,
                t1.error_flag,
                t1.error_type,
                t1.error_remarks,
                t1.create_time
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesCheckLogs', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
            ->where('t1.del_flag = 0 and t1.work_date = ?1 and t1.staff_id = ?2',[1 => $report_date, 2 => SessionData::user()->id])
            ->orderBy('t1.create_time desc,t1.id');
        $report_list = $builder->getQuery()->execute()->toArray();
        foreach ($report_list as &$report_item){
            $report_item['create_time'] = date('H:i',strtotime($report_item['create_time']));
        }
        $rtn->message = '';
        $rtn->data = [
            'report_date' => $report_date,
            'report_list' => $report_list
        ];
        return $rtn;
    }

    public function getNoticeBatch()
    {
        return MesNotice::find(['del_flag = 0 and status = ?1', 'bind' => [1 => 30]])->toArray();
    }

    public function getProducts($batch_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                 t2.id,
                 t2.uid,
                 t3.code as notice_code,
                 t4.name,
                 t4.code
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->where('t2.del_flag = 0 and t3.status = ?1 and t3.id = ?2',[1 => 30, 2 => $batch_id])
            ->orderBy('t2.id desc');
        return $builder->getQuery()->execute()->toArray();
    }
}