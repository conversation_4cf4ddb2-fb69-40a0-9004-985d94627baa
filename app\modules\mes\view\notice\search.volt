{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered" style="margin-bottom: 0;padding-bottom: 0">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-6 col-lg-6">
                        <h3 class="page-title">生产完成情况统计</h3>
                    </div>
                    <div class="col-md-6 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        {{ partial('table') }}
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            print : {{  print }}
        },
        mounted:function (){
            let btn_list = [];
            btn_list.push({type:1,name:'生产详情'});
            btn_list.push({type:2,name:'质检详情'});
            btn_list.push({type:3,name:'打印批次单',fn: (data) => {return data.status == 10}});
            app_ext_table.init({{ page_id }},btn_list,'{{ url('mes/notice/search/json') }}');
        },
        methods: {
            search: function() {
                app_ext_table.search();
            },
            reset: function() {
                app_ext_table.reset();
            },
            excel:function (){
                app_ext_table.excel("{{ url('mes/notice/export2') }}");
            },
            getParams: function() {
                return {}
            },
            dataView:function (type,row){
                if (type == 1){
                    produce(row.uid);
                } else if (type == 2){
                    quality(row.uid);
                } else if (type == 3){
                    print(row.uid);
                }
            }
        }
    });

    function produce(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title:'生产详情',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('mes/notice/produce/') }}' + uid,
            end: function() {

            }
        });
    }

    function quality(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title:'质检详情',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('mes/notice/quality/') }}'+ uid,
            end: function() {

            }
        });
    }


    function print(uid) {
        if (app.print.uid == ''){
            toastr.error('请配置打印模板！');
            return;
        }
        window.open("{{'/common/ext/print?uid='}}"+app.print.uid+"&doc_id="+uid);
    }
</script>