<?php

namespace Envsan\Modules\Common\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Util\Constant;
use Envsan\Modules\Mes\Model\MesBom;
use Envsan\Modules\Mes\Model\MesShipType;
use Envsan\Modules\Sys\Model\DataLogs;
use Envsan\Modules\Sys\Model\ExtendColumn;
use Phalcon\Mvc\User\Component;

class LogsService extends Component
{
    public function saveDataLogs($page_id,$data_row){
        $user = SessionData::user();
        $now = DateUtil::now();
        $data = $data_row->toArray();
        $col_data = [];
        if (array_key_exists($page_id,Constant::$page_extend_column)) {
            foreach (Constant::$page_extend_column[$page_id]['header_data'] as $header_item){
                if ($header_item['base'] == 1){
                    if ($header_item['type'] == 99 || $header_item['type'] == 100){
                        if (array_key_exists($header_item['id'],$data)){
                            $ext_val = CvtUtil::emptyToArray($data[$header_item['id']]);
                            foreach ($ext_val as $k => $v) {
                                $col_data[$k] = $v;
                            }
                        }
                    } else if ($header_item['type'] == 101){
                        $col_data['bom_data'] = $data['bom_data'];
                    } else {
                        if (array_key_exists($header_item['id'],$data)) {
                            $col_data[$header_item['id']] = $data[$header_item['id']];
                        }
                    }
                }
            }
        }
        if (array_key_exists('files',$data)) {
            $col_data['files'] = $data['files'];
        }
        $row = DataLogs::findFirst(['del_flag = 0 and owner = ?1 and page_id = ?2 and uid = ?3'
            ,'bind'=>[1=> $user->owner,2=>$page_id,3=>$data['uid']]]);
        if (empty($row)){
            $row = new DataLogs();
            $row->uid = $data['uid'];
            $row->page_id = $page_id;
            $row->del_flag = 0;
            $row->owner = $user->owner;
            $change_logs = [];
            $col_data['upd_name'] = $user->real_name;
            $col_data['upd_time'] = $now;
            $change_logs[] = $col_data;
            $row->change_logs = json_encode($change_logs,JSON_UNESCAPED_UNICODE);
        } else {
            $change_logs = CvtUtil::emptyToArray($row->change_logs);
            $change_log = [];
            foreach ($change_logs as $change_item){
                foreach ($change_item as $key => $val){
                    $change_log[$key] = $val;
                }
            }
            $change_flag = true;
            $new_log = [];
            foreach ($col_data as $col_key => $col_val){
                if (array_key_exists($col_key,$change_log)){
                    if ($col_key == 'files') {
                        $before_flies = CvtUtil::emptyToArray($change_log[$col_key]);
                        $before_file = '';
                        foreach ($before_flies as $file){
                            $before_file .= $file['key'];
                        }
                        $after_files = CvtUtil::emptyToArray($col_val);
                        $after_file = '';
                        foreach ($after_files as $file){
                            $after_file .= $file['key'];
                        }
                        if ($before_file != $after_file){
                            $new_log[$col_key] = $col_val;
                            $change_flag = false;
                        }
                    } else if($col_key == 'bom_data') {
                        $bom_data = CvtUtil::emptyToArray($change_log[$col_key]);
                        $before_bom = '';
                        foreach ($bom_data as $bom){
                            $before_bom .= $bom['id'].$bom['value'];
                        }
                        $after_bom_data = CvtUtil::emptyToArray($col_val);
                        $after_bom = '';
                        foreach ($after_bom_data as $bom){
                            $after_bom .= $bom['id'].$bom['value'];
                        }
                        if ($before_bom != $after_bom){
                            $new_log[$col_key] = $col_val;
                            $change_flag = false;
                        }
                    } else {
                        if ($change_log[$col_key] != $col_val){
                            $new_log[$col_key] = $col_val;
                            $change_flag = false;
                        }
                    }
                } else {
                    $new_log[$col_key] = $col_val;
                    $change_flag = false;
                }
            }
            if ($change_flag){
                return;
            }
            $new_log['upd_name'] = $user->real_name;
            $new_log['upd_time'] = $now;
            $change_logs[] = $new_log;
            $row->change_logs = json_encode($change_logs,JSON_UNESCAPED_UNICODE);
        }
        $row->data_id = $data['id'];
        if (!$row->save()) {
            throw new \Exception("DataLogs表更新失败");
        }
    }

    public function getDataLogs($log_row){
        $page_id = $log_row->page_id;
        $tb = new TableService();
        $col_data = [];
        if (array_key_exists($log_row->page_id,Constant::$page_extend_column)){
            foreach (Constant::$page_extend_column[$page_id]['header_data'] as $header_item){
                if ($header_item['base'] == 1){
                    if ($header_item['type'] == 99){
                        $form_data = [];
                        $ext_row = $tb->selectExtendColumn($header_item['page_id']);
                        if (!empty($ext_row)){
                            $form_data = CvtUtil::emptyToArray($ext_row->form_data);
                        }
                        foreach ($form_data as $form_item){
                            $col_data[$form_item['id']] =  [
                                'id' => $form_item['id'],
                                'name' => $form_item['title'],
                                'unit' => $form_item['unit'],
                                'list' => [],
                            ];
                        }
                    } else if ($header_item['type'] == 100){
                        $bom_row = MesBom::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$log_row->uid]]);
                        if (!empty($bom_row)){
                            $form_data = [];
                            $ext_row = $this->selectShipTypeExt($bom_row->ship_type_id);
                            if (!empty($ext_row)){
                                $form_data = CvtUtil::emptyToArray($ext_row->form_data);
                            }
                            foreach ($form_data as $form_item){
                                $col_data[$form_item['id']] = [
                                    'id' => $form_item['id'],
                                    'name' => $form_item['title'],
                                    'unit' => $form_item['unit'],
                                    'list' => []
                                ];
                            }
                        }
                    } else if ($header_item['type'] == 102){
                        $form_data = $tb->selectDict($header_item['page_id']);
                        $form_data = $form_data->toArray();
                        foreach ($form_data as $form_item){
                              $col_data[$form_item['code']] = [
                                'id' => $form_item['code'],
                                'name' => $form_item['name'],
                                'unit' => '',
                                'list' => []
                            ];
                        }
                    } else {
                        $col_data[$header_item['id']] = [
                            'id' => $header_item['id'],
                            'name' => $header_item['name'],
                            'unit' => $header_item['unit'],
                            'list' => []
                        ];
                    }
                }
            }
        }
        $col_data['files'] = [
            'id' => 'files',
            'name' => '文件',
            'unit' => '',
            'list' => []
        ];
        $col_data['upd_name'] =  [
            'id' => 'upd_name',
            'name' => '操作人',
            'unit' => '',
            'list' => [],
        ];
        $col_data['upd_time'] =  [
            'id' => 'upd_time',
            'name' => '操作时间',
            'unit' => '',
            'list' => [],
        ];
        $change_logs = CvtUtil::emptyToArray($log_row->change_logs);
        foreach ($col_data as $col_key => $col_item){
            foreach ($change_logs as $c_item){
                if ($col_key == 'flies' || $col_key == 'bom_data'){
                    $col_data[$col_key]['list'][] = [];
                } else {
                    $col_data[$col_key]['list'][] = '';
                }
            }
        }

        foreach ($change_logs as $change_idx => $change_item){
            foreach ($change_item as $change_key => $change_val){
                if (array_key_exists($change_key,$col_data)){
                    if ($change_key == 'files' || $change_key == 'bom_data'){
                        $col_data[$change_key]['list'][$change_idx] = CvtUtil::emptyToArray($change_val);
                    } else {
                        $col_data[$change_key]['list'][$change_idx] = $change_val;
                    }
                }
            }
        }
        $col_list = [];
        foreach ($col_data as $col_key => $col_item){
            $col_list[] = $col_item;
        }
        return $col_list;
    }

    private function selectShipTypeExt($type_id){
        return MesShipType::findFirst([
            'del_flag = 0 and id = ?1',
            'bind' => [
                1 => $type_id
            ]
        ]);
    }
}