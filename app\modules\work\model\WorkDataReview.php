<?php

namespace Envsan\Modules\Work\Model;

use Envsan\Common\Model\BaseModel;

class WorkDataReview extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $data_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $anchor_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $anchor_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $user_id;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $user_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $hidden_keys;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $text;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $files;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'work_data_review';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return WorkDataReview[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return WorkDataReview
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
