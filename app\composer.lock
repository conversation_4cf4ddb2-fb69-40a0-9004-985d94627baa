{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f94847f3d712a6c7fd0c204493793853", "packages": [{"name": "adbario/php-dot-notation", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/eee4fc81296531e6aafba4c2bbccfc5adab1676e", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0|^6.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "time": "2019-01-01T23:59:15+00:00"}, {"name": "alibabacloud/client", "version": "1.5.22", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "900c5b11aefe542048070fb2287e014dd2833e51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/900c5b11aefe542048070fb2287e014dd2833e51", "reference": "900c5b11aefe542048070fb2287e014dd2833e51", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "clagiordano/weblibs-configmanager": "^1.0", "danielstjules/stringy": "^3.1", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3", "mtdowling/jmespath.php": "^2.4", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Client\\": "src"}, "files": ["src/Functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "time": "2020-05-12T02:06:59+00:00"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/572d0f8e099e8630ae7139ed3fdedb926c7a760f", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*", "satooshi/php-coveralls": "*"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.6.0"}, "time": "2022-08-03T08:06:01+00:00"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "d1869310b9f57812581ff56cf4b456cbcc0d0516"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/d1869310b9f57812581ff56cf4b456cbcc0d0516", "reference": "d1869310b9f57812581ff56cf4b456cbcc0d0516", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "time": "2016-08-23T19:11:20+00:00"}, {"name": "codeitnowin/barcode", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/codeitnowin/barcode-generator.git", "reference": "6325a15ae904ec401b947e1a3e868de1c2cc80b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/codeitnowin/barcode-generator/zipball/6325a15ae904ec401b947e1a3e868de1c2cc80b0", "reference": "6325a15ae904ec401b947e1a3e868de1c2cc80b0", "shasum": ""}, "require": {"ext-gd": "*", "php": ">=5.3.2"}, "type": "library", "autoload": {"psr-4": {"CodeItNow\\": "CodeItNow/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "er.akhtar<PERSON><EMAIL>"}], "description": "Barcode & Qr Code generator library by http://www.codeitnow.in. You can use it with Custom PHP application or any PHP Framework such as Laravel, Cakephp, Yii, Codeigneter etc.", "homepage": "http://www.codeitnow.in", "keywords": ["Symfony2", "barcode", "cakephp", "code", "generator", "laravel", "qr", "qrcode", "symfony"], "time": "2018-10-25T18:32:10+00:00"}, {"name": "danielst<PERSON>les/stringy", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "shasum": ""}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2017-06-12T01:10:27+00:00"}, {"name": "dibi/dibi", "version": "v3.0.8", "source": {"type": "git", "url": "https://github.com/dg/dibi.git", "reference": "0e5d951dfb672da85c64c3f078e9f650d5d49605"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/dibi/zipball/0e5d951dfb672da85c64c3f078e9f650d5d49605", "reference": "0e5d951dfb672da85c64c3f078e9f650d5d49605", "shasum": ""}, "require": {"php": ">=5.4.4"}, "replace": {"dg/dibi": "*"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "~2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"], "files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Dibi is Database Abstraction Library for PHP", "homepage": "https://dibiphp.com", "keywords": ["access", "database", "dbal", "mssql", "mysql", "odbc", "oracle", "pdo", "postgresql", "sqlite", "sqlsrv"], "time": "2017-06-10T01:05:18+00:00"}, {"name": "doctrine/cache", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "b6f544a20f4807e81f7044d31e679ccbb1866dc3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/b6f544a20f4807e81f7044d31e679ccbb1866dc3", "reference": "b6f544a20f4807e81f7044d31e679ccbb1866dc3", "shasum": ""}, "require": {"php": "~5.5|~7.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.0", "predis/predis": "~1.0", "satooshi/php-coveralls": "~0.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Caching library offering an object-oriented API for many cache backends", "homepage": "http://www.doctrine-project.org", "keywords": ["cache", "caching"], "time": "2016-10-29T11:16:17+00:00"}, {"name": "flc/alidayu", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/flc1125/alidayu.git", "reference": "f33b2baed2274ee90ec109c5135ec983688b99b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/flc1125/alidayu/zipball/f33b2baed2274ee90ec109c5135ec983688b99b5", "reference": "f33b2baed2274ee90ec109c5135ec983688b99b5", "shasum": ""}, "require": {"php": ">=5.4.0"}, "type": "library", "autoload": {"psr-4": {"Flc\\Alidayu\\": "src/Alidayu/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "叶子坑 - 阿里大于API", "keywords": ["al<PERSON><PERSON>", "flc"], "time": "2016-09-20T14:07:08+00:00"}, {"name": "gregwar/captcha", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/Gregwar/Captcha.git", "reference": "2f704c8bc3f1bb5f58994af2729e77ac4e3172e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Gregwar/Captcha/zipball/2f704c8bc3f1bb5f58994af2729e77ac4e3172e6", "reference": "2f704c8bc3f1bb5f58994af2729e77ac4e3172e6", "shasum": ""}, "require": {"ext-gd": "*", "php": ">=5.3.0"}, "type": "<PERSON><PERSON>a", "autoload": {"psr-4": {"Gregwar\\Captcha\\": "/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.gregwar.com/"}, {"name": "<PERSON>", "email": "jeremy.j.living<PERSON>@gmail.com"}], "description": "Captcha generator", "homepage": "https://github.com/Gregwar/Captcha", "keywords": ["bot", "<PERSON><PERSON>a", "spam"], "time": "2017-04-19T08:10:55+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/f4db5a78a5ea468d4831de7f0bf9d9415e348699", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0 || ^5.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2017-06-22T18:50:49+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"], "time": "2017-03-20T17:10:46+00:00"}, {"name": "huaweicloud/huaweicloud-sdk-php", "version": "3.1.43", "source": {"type": "git", "url": "https://github.com/huaweicloud/huaweicloud-sdk-php-v3.git", "reference": "afd8980d3869ab5b7a38ef4d74c92e9a3eaf109a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/huaweicloud/huaweicloud-sdk-php-v3/zipball/afd8980d3869ab5b7a38ef4d74c92e9a3eaf109a", "reference": "afd8980d3869ab5b7a38ef4d74c92e9a3eaf109a", "shasum": ""}, "require": {"guzzlehttp/guzzle": ">=6.3.0", "guzzlehttp/promises": ">=1.3.1", "guzzlehttp/psr7": ">=1.4.2", "monolog/monolog": ">=1.23.0", "php": ">=5.6.0", "psr/http-message": ">=1.0.1"}, "type": "library", "autoload": {"psr-4": {"HuaweiCloud\\SDK\\": "Services/", "HuaweiCloud\\SDK\\_As\\": "Services/As/", "HuaweiCloud\\SDK\\Core\\": "Core/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "HuaweiCloud_SDK", "email": "<EMAIL>", "homepage": "https://sdkcenter.developer.huaweicloud.com/?language=PHP"}], "description": "Huawei Cloud SDK for PHP", "keywords": ["api", "php", "rest", "sdk"], "support": {"issues": "https://github.com/huaweicloud/huaweicloud-sdk-php-v3/issues", "source": "https://github.com/huaweicloud/huaweicloud-sdk-php-v3/tree/v3.1.43"}, "time": "2023-06-29T09:40:28+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.24", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "cdf8f8efaf993bc687e78e4622f5eebd0b8b3bf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/cdf8f8efaf993bc687e78e4622f5eebd0b8b3bf3", "reference": "cdf8f8efaf993bc687e78e4622f5eebd0b8b3bf3", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "johnkary/phpunit-speedtrap": "~1.0@dev", "phpunit/phpunit": "*"}, "type": "library", "autoload": {"classmap": ["Mobile_Detect.php"], "psr-0": {"Detection": "namespaced/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "time": "2016-11-11T14:56:25+00:00"}, {"name": "monolog/monolog", "version": "1.23.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/fd8c787753b3a2ad11bc60c063cff1358a32a3b4", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2017-06-19T01:22:40+00:00"}, {"name": "mpdf/mpdf", "version": "v6.1.2", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "da078bc2669d3f98553ac41f920ead4c17c951ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/da078bc2669d3f98553ac41f920ead4c17c951ad", "reference": "da078bc2669d3f98553ac41f920ead4c17c951ad", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0", "setasign/fpdi": "1.6.*"}, "require-dev": {"phpunit/phpunit": "^4.7"}, "suggest": {"ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"classmap": ["mpdf.php", "classes"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "A PHP class to generate PDF files from HTML with Unicode/UTF-8 and CJK support", "homepage": "http://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "time": "2016-07-20T12:31:58+00:00"}, {"name": "mtdowling/cron-expression", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/mtdowling/cron-expression.git", "reference": "9504fa9ea681b586028adaaa0877db4aecf32bad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mtdowling/cron-expression/zipball/9504fa9ea681b586028adaaa0877db4aecf32bad", "reference": "9504fa9ea681b586028adaaa0877db4aecf32bad", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "abandoned": "dragonmantank/cron-expression", "time": "2017-01-23T04:29:33+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2016-12-03T22:08:25+00:00"}, {"name": "obs/esdk-obs-php", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/huaweicloud/huaweicloud-sdk-php-obs.git", "reference": "297d95da88e39247faccf712fa5a09f1d426fb38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/huaweicloud/huaweicloud-sdk-php-obs/zipball/297d95da88e39247faccf712fa5a09f1d426fb38", "reference": "297d95da88e39247faccf712fa5a09f1d426fb38", "shasum": ""}, "require": {"guzzlehttp/guzzle": "6.3.0", "guzzlehttp/promises": "1.3.1", "guzzlehttp/psr7": "1.4.2", "monolog/monolog": "1.23.0", "php": ">=5.6.0", "psr/http-message": "1.0.1", "psr/log": "1.0.2"}, "type": "library", "autoload": {"psr-4": {"Obs\\": "Obs/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "OBS PHP SDK", "keywords": ["OBS", "php"], "time": "2019-01-12T17:59:51+00:00"}, {"name": "overtrue/socialite", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/overtrue/socialite.git", "reference": "306d6df42e1d0849bbaeb791ec94c6d012b2b615"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/socialite/zipball/306d6df42e1d0849bbaeb791ec94c6d012b2b615", "reference": "306d6df42e1d0849bbaeb791ec94c6d012b2b615", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~5.0|~6.0", "php": ">=5.4.0", "symfony/http-foundation": "~2.6|~2.7|~2.8|~3.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Overtrue\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A collection of OAuth 2 packages that extracts from laravel/socialite.", "keywords": ["login", "o<PERSON>h", "qq", "social", "wechat", "weibo"], "time": "2017-07-03T00:36:16+00:00"}, {"name": "overtrue/wechat", "version": "3.3.11", "source": {"type": "git", "url": "https://github.com/w7corp/easywechat.git", "reference": "5074d36e1a72df13579c84ed0471d8e0f08122bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/w7corp/easywechat/zipball/5074d36e1a72df13579c84ed0471d8e0f08122bb", "reference": "5074d36e1a72df13579c84ed0471d8e0f08122bb", "shasum": ""}, "require": {"doctrine/cache": "~1.4", "ext-openssl": "*", "guzzlehttp/guzzle": "~6.2", "monolog/monolog": "^1.17", "overtrue/socialite": ">=1.0.25", "php": ">=5.5.0", "pimple/pimple": "~3.0", "symfony/http-foundation": "~2.6|~2.7|~2.8|~3.0", "symfony/psr-http-message-bridge": "~0.3|^1.0"}, "require-dev": {"mockery/mockery": "^0.9.9", "overtrue/phplint": "dev-master", "phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"EasyWeChat\\": "src/"}, "files": ["src/Payment/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "微信SDK", "keywords": ["sdk", "wechat", "weixin", "weixin-sdk"], "time": "2017-07-17T11:09:02+00:00"}, {"name": "phalcon/incubator", "version": "3.0.x-dev", "source": {"type": "git", "url": "https://github.com/phalcon/incubator.git", "reference": "de9ecc77199d259e0b069e249b137b05a7374355"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phalcon/incubator/zipball/de9ecc77199d259e0b069e249b137b05a7374355", "reference": "de9ecc77199d259e0b069e249b137b05a7374355", "shasum": ""}, "require": {"ext-phalcon": "^3.0", "php": ">=5.5", "swiftmailer/swiftmailer": "~5.2"}, "require-dev": {"codeception/aerospike-module": "^1.0", "codeception/codeception": "^2.2", "codeception/mockery-module": "^0.2", "codeception/specify": "^0.4", "codeception/verify": "^0.3", "phalcongelist/dd": "^1.0", "phpdocumentor/reflection-docblock": "2.0.4", "phpunit/phpunit": "^4.8", "squizlabs/php_codesniffer": "^2.7", "vlucas/phpdotenv": "^2.4"}, "suggest": {"duncan3dc/fork-helper": "To use extended class to access the beanstalk queue service", "ext-aerospike": "*", "sergeyklay/aerospike-php-stubs": "The most complete Aerospike PHP stubs which allows autocomplete in modern IDEs"}, "type": "library", "autoload": {"psr-4": {"Phalcon\\": "Library/Phalcon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Phalcon Team", "email": "<EMAIL>", "homepage": "http://phalconphp.com/en/team"}, {"name": "Contributors", "homepage": "https://github.com/phalcon/incubator/graphs/contributors"}], "description": "Adapters, prototypes or functionality that can be potentially incorporated to the C-framework.", "homepage": "http://phalconphp.com", "keywords": ["framework", "incubator", "phalcon"], "time": "2017-03-09T22:48:26+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "shasum": ""}, "require": {"ext-xml": "*", "ext-xmlwriter": "*", "php": ">=5.2.0"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "http://phpexcel.codeplex.com", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "abandoned": "phpoffice/phpspreadsheet", "time": "2015-05-01T07:00:55+00:00"}, {"name": "phpoffice/phpword", "version": "0.12.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "3f136501b3dfea94766a704e17782c8a942a7b9b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/3f136501b3dfea94766a704e17782c8a942a7b9b", "reference": "3f136501b3dfea94766a704e17782c8a942a7b9b", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=5.3.3"}, "require-dev": {"dompdf/dompdf": "0.6.*", "mpdf/mpdf": "5.*", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "3.7.*", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "1.*", "tecnick.com/tcpdf": "6.*"}, "suggest": {"dompdf/dompdf": "Used to write PDF", "ext-gd2": "Used to add images", "ext-xmlwriter": "Used to write DOCX and ODT", "ext-xsl": "Used to apply XSL style sheet to main document part of OOXML template", "ext-zip": "Used to write DOCX and ODT"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (DOCX, ODT, RTF, HTML, PDF)", "homepage": "http://phpoffice.github.io", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "time": "2015-01-03T09:42:48+00:00"}, {"name": "pimple/pimple", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "729436408ef52f9845f9b60d60b2563f314bf1a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/729436408ef52f9845f9b60d60b2563f314bf1a9", "reference": "729436408ef52f9845f9b60d60b2563f314bf1a9", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/container": "^1.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"psr-0": {"Pimple": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "http://pimple.sensiolabs.org", "keywords": ["container", "dependency injection"], "time": "2017-07-17T17:05:21+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10T12:19:37+00:00"}, {"name": "setasign/fpdi", "version": "1.6.2", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "shasum": ""}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use \"tecnickcom/tcpdf\" as an alternative there's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF."}, "type": "library", "autoload": {"classmap": ["filters/", "fpdi.php", "fpdf_tpl.php", "fpdi_pdf_parser.php", "pdf_context.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "time": "2017-05-11T14:25:49+00:00"}, {"name": "sidroberts/phalcon-cron", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/SidRoberts/phalcon-cron.git", "reference": "15154ccdaccd51d2ba5970a298fc04a118eaec7c"}, "dist": {"type": "zip", "url": "https://files.phpcomposer.com/files/SidRoberts/phalcon-cron/15154ccdaccd51d2ba5970a298fc04a118eaec7c.zip", "reference": "15154ccdaccd51d2ba5970a298fc04a118eaec7c", "shasum": ""}, "require": {"ext-phalcon": ">= 1.3.4", "mtdowling/cron-expression": "~1.0", "php": ">= 5.4"}, "require-dev": {"codeception/codeception": "2.1.2", "codeclimate/php-test-reporter": "dev-master", "phpunit/php-code-coverage": "~2.0"}, "type": "library", "autoload": {"psr-4": {"Sid\\Phalcon\\Cron\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.SidRoberts.co.uk", "role": "Developer"}], "description": "Cron component for Phalcon.", "time": "2016-02-24T15:31:57+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v5.4.8", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "9a06dc570a0367850280eefd3f1dc2da45aef517"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/9a06dc570a0367850280eefd3f1dc2da45aef517", "reference": "9a06dc570a0367850280eefd3f1dc2da45aef517", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "http://swiftmailer.org", "keywords": ["email", "mail", "mailer"], "time": "2017-05-01T15:54:03+00:00"}, {"name": "symfony/http-foundation", "version": "v3.3.5", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "e307abe4b79ccbbfdced9b91c132fd128f456bc5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/e307abe4b79ccbbfdced9b91c132fd128f456bc5", "reference": "e307abe4b79ccbbfdced9b91c132fd128f456bc5", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2017-07-17T14:07:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "f29dca382a6485c3cbe6379f0c61230167681937"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/f29dca382a6485c3cbe6379f0c61230167681937", "reference": "f29dca382a6485c3cbe6379f0c61230167681937", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2017-06-09T14:24:12+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "66085f246d3893cbdbcec5f5ad15ac60546cf0de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/66085f246d3893cbdbcec5f5ad15ac60546cf0de", "reference": "66085f246d3893cbdbcec5f5ad15ac60546cf0de", "shasum": ""}, "require": {"php": ">=5.3.3", "psr/http-message": "~1.0", "symfony/http-foundation": "~2.3|~3.0"}, "require-dev": {"symfony/phpunit-bridge": "~2.7|~3.0"}, "suggest": {"psr/http-message-implementation": "To use the HttpFoundation factory", "zendframework/zend-diactoros": "To use the Zend Diactoros factory"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-7"], "time": "2016-09-14T18:37:20+00:00"}, {"name": "upyun/sdk", "version": "3.0.0-RC5", "source": {"type": "git", "url": "https://github.com/upyun/php-sdk.git", "reference": "8676e4007dd52c219f6f553dc01eee200894bc51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/upyun/php-sdk/zipball/8676e4007dd52c219f6f553dc01eee200894bc51", "reference": "8676e4007dd52c219f6f553dc01eee200894bc51", "shasum": ""}, "require": {"ext-curl": "*", "guzzlehttp/guzzle": "~6.0", "php": ">=5.5.0"}, "require-dev": {"phpdocumentor/phpdocumentor": "^2.9", "phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Upyun\\": "src/Upyun/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "totoleo", "email": "<EMAIL>"}, {"name": "lfeng", "email": "<EMAIL>"}, {"name": "lvtongda", "email": "<EMAIL>"}, {"name": "saba<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "UPYUN sdk for php", "homepage": "https://github.com/upyun/php-sdk/", "keywords": ["sdk", "upyun"], "time": "2017-02-16T02:58:34+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"phalcon/incubator": 20, "upyun/sdk": 5, "sidroberts/phalcon-cron": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"ext-json": "*"}, "platform-dev": [], "plugin-api-version": "2.1.0"}