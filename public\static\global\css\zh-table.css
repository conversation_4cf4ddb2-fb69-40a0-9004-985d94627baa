.zh-table .zh-col {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 8px;
}

.zh-table .zh-col .zh-row {
    width: 100%;
    padding: 8px;
}

.zh-table .zh-split {
    padding: 0 !important;
}

.zh-table-header {
    display: flex;
    flex-direction: row;
    background-color: #3598dc;
    color: #FFFFFF;
    font-weight: bold;
    border: 1px solid #3598dc;
    border-top: none;
    border-bottom: none;
}

.zh-table .zh-col > .zh-row:not(:first-child) {
    border-top: 1px solid #e7ecf1;
}

.zh-table-body .row-main {
    background-color: #fff7d5;
}

.zh-table-header .zh-col,
.zh-table-body .zh-row > .zh-col,
.zh-table-body .row-main > .zh-col {
    padding: 8px;
    line-height: 24px;
    text-align: center;
    border-right: 1px solid #ddd;
}

.zh-table-header .zh-col {
    border-right: 1px solid transparent;
}

.zh-table-big .zh-table-body > .zh-row > .zh-col,
.zh-table-big .zh-table-body .row-main > .zh-col {
    line-height: 34px;
}

.zh-table-header .zh-col:last-child,
.zh-table-body > .zh-row > .zh-col:last-child,
.zh-table-body .row-main > .zh-col:last-child {
    flex: 1;
    border-right: 0;
}

.zh-table-loading {
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: #FFFFFF;
}

.zh-table-body {
    color: #4e5a64;
}

.zh-table-body > .zh-row {
    display: flex;
    flex-direction: row;
    /*border-bottom: 1px solid #ddd;*/
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
}

.zh-table-body > .zh-row:last-child {
    border-bottom: 1px solid #ddd;
}

.zh-table-hover .zh-table-body > .zh-row:hover {
    background-color: #eef1f5;
}

.zh-col.success {
    background-color: #eeffe7;
}

.zh-table-body .zh-row-box {
    width: 100%;
}

.zh-table-body .row-main {
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #D1F0EA;
}

.zh-table-body .row-sub {
    width: 100%;
    padding: 10px 20px 20px;
}

.zh-table-body .row-sub table {
    margin-bottom: 0;
}

.zh-table-body .row-sub .table > thead > tr > th {
    padding: 3px 8px;
    color: white;
    text-align: center;
}

.zh-col.success {
    background-color: #eeffe7;
}

.zh-table-body .text-red {
    text-decoration: underline;
    margin-right: 0 !important;
}

.zh-table-body > .zh-row:nth-of-type(2n+1) {
    background-color: #fbfcfd;
}

.zh-table .icheck-inline label {
    margin-top: 0 !important;
}

.zh-table div[class*="icheckbox_"] {
    margin-right: 0 !important;
}

.row-sub .table-bordered {
    border: 1px solid #b3b3b3 !important;
}

.row-sub .table-bordered > thead > tr > th {
    background-color: #b3b3b3 !important;
    border: 1px solid #eeeeee;
    border-bottom: 1px solid #eeeeee !important;
}

.row-sub .table-bordered > thead > tr > th:first-child {
    border-left-color: #b3b3b3;
}

.row-sub .table-bordered > thead > tr > th:last-child {
    border-right-color: #b3b3b3;
}

.row-sub .table-bordered > tbody > tr > td {
    border: 1px solid #eeeeee;
    text-align: center;
}