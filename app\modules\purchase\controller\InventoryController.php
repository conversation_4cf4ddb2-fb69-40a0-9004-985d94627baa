<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInventory;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\InventoryService;


/**
 * @name('盘点')
 */
class InventoryController extends SuperController
{
    private $page_id = 28;
    private $search_page_id = 33;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new InventoryService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }


    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new InventoryService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $rs = new InventoryService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $table = new TableService();
        $jrow = (new PurchaseInventory())->toArray();
        $jrow['inventory_date'] = DateUtil::today();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['detail_data'] = [];
        $jrow['files'] = [];
        $jrow['goods_list'] = [];
        $jrow['goods'] = '';
        $jrow['supplier'] = '';
        $jrow['type_uid'] = '';
        $jrow['type_pid'] = '';
        $gs = new GoodsTypeService();
        $this->view->jsonTree = $gs->selectTree();
        $this->view->jsonInventory = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
    }

    /**
     * @acl({'link':'purchase:inventory:create'})
     */
    function editAction($uid)
    {
        $rs = new InventoryService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($row->status > 10)
            die('已提交入库');
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['goods_list'] = [];
        $jrow['goods'] = '';
        $jrow['supplier'] = '';
        $jrow['type_uid'] = '';
        $jrow['type_pid'] = '';
        $gs = new GoodsTypeService();
        $this->view->jsonTree = $gs->selectTree();
        $this->view->jsonInventory = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
        $this->view->pick('inventory/create');
    }

    /**
     * @acl({'link':'purchase:inventory:create'})
     */
    public function deleteAction(){
        $s = new InventoryService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:inventory:create'})
     */
    public function cancelAction(){
        $s = new InventoryService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->cancel();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $rs = new InventoryService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        $jrow = $row->toArray();
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = $rs->getDetail($jrow['id']);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonInventory = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new InventoryService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }
}