<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesViewPlanFinishSituation extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $pid;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $name;

    /**
     *
     * @var double
     * @Column(type="double", nullable=true)
     */
    public $data_sort;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $produce_cnt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $ship_type_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $notice_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $notice_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $start_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $end_date;

    /**
     *
     * @var double
     * @Column(type="double", length=32, nullable=false)
     */
    public $plan_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=21, nullable=false)
     */
    public $finish_cnt;

    /**
     *
     * @var double
     * @Column(type="double", length=32, nullable=false)
     */
    public $error_cnt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $date_warning_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $count_warning_flag;

    /**
     *
     * @var string
     * @Column(type="string", length=22, nullable=false)
     */
    public $all_plan_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=22, nullable=false)
     */
    public $use_plan_cnt;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_view_plan_finish_situation';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewPlanFinishSituation[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewPlanFinishSituation
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
