<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}

<div class="page-content">

    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <div class="col-sm-1">
                            <h5>部门管理</h5>
                        </div>
                        <div  class="col-sm-11">
                            <button type="button" class="btn btn yellow" onclick="create()" title="新建">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn green" onclick="edit()" title="编辑">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn red" onclick="del()" title="删除">
                                <i class="fa fa-trash-o"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div id="tree"></div>
            </div>
        </div>
    </div>
</div>


<script>
    var app = new Vue({
        el:'#app',
        data:{
            uid:'', pid:''
        },
        methods:{
        }
    });

    var $tree = $('#tree');

    $(document).ready(function () {
        $tree.jstree({
            "core": {"data": {{ jsonTree }}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            app.uid = data.node.original.uid;
            app.pid = data.node.parent;
        });
    });

    function refreshTree() {
        $.post('{{ url('sys/group/list/json') }}', function (rs) {
            if(rs.status=='ok'){
                $tree.jstree(true).settings.core.data = JSON.parse(rs.data);
                $tree.jstree('refresh');
            }
        })
    }

    function create() {
        top.window.layer_result='';
        top.layer.open({
            title:'新建',
            type: 2,
            area: makeArea('40em', '80%'),
            content: '{{ url('sys/group/create') }}',
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    refreshTree();
                }
            }
        });
    }

    function edit() {
        if(app.uid==''){
            layer.alert('请先选择组织!');
            return;
        }

        if(app.pid=='#'){
            layer.alert('该组织不可被编辑');
            return;
        }

        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: makeArea('40em', '80%'),
            content: '{{ url('sys/group/edit/') }}' + app.uid,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    refreshTree();
                }
            }
        });
    }

    function del(){
        if(app.uid==''){
            layer.alert('请先选择组织!');
            return;
        }

        var dlg = layer.confirm('确认删除吗?', function(){
            layer.close(dlg);
            showSpin();
            $.post("{{ url('sys/group/delete') }}", app.$data, function (rs) {
                closeSpin();
                if(rs.status=='ok'){
                    toastr.success('操作成功!');
                    refreshTree();
                }
                else {
                    toastr.error('操作失败!'+rs.message);
                }
            })
        });
    }
</script>
