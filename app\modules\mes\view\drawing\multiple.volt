{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vue.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/Sortable.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vuedraggable.umd.min.js') %}
{{ assets.outputJs('validate') }}
<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="form-group">
                        <label><span class="required">*</span>图号：</label>
                        <div>
                            <input type="text" class="form-control" name="code" v-model="code" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label><span class="required">*</span>合并后文件名称：</label>
                        <div>
                            <input type="text" class="form-control" name="drawing_name" v-model="drawing_name" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label><span class="required">*</span>版本号：</label>
                        <div>
                            <input type="text" class="form-control" name="version_code" v-model="version_code" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>说明：</label>
                        <div>
                            <textarea class="form-control" name="remarks" v-model="remarks" maxlength="200" rows="3" style="resize: none;"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label><span class="required"> * </span>上传图纸(PDF文件)：<span style="color: red"> 多文件合并上传 </span></label>
                        <div style="display: flex;">
                            <div style="width: 60%;padding-right: 15px">
                                <draggable tag="ul" :list="files" group="files_data" class="list-group" handle=".handle">
                                    <li
                                            class="list-group-item"
                                            v-for="item,index in files"
                                            :key="index"
                                    >
                                        <i class="fa fa-align-justify handle" style="margin-right: 10px"></i>
                                        <span v-text="item.name"></span>
                                        <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="removeFile(index)">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </li>
                                </draggable>
                            </div>
                            <div style="width: 40%;text-align: center">
                                <div id="dnd_drawing" class="rv-img-item rv-img-dnd">
                                    <div><i class="fa fa-plus"></i></div>
                                    <div>拖拽此处上传</div>
                                </div>
                                <div class="btn btn-primary" style="width: 150px;margin-top: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{ partial('uploader_exec') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        components: {
            draggable: window.vuedraggable
        },
        methods: {
            removeFile(index){
                const files = uploader.getFiles();
                const file = files.find(f => f.id === this.files[index].id);
                if (file) {
                    if (uploader.removeFile) {
                        uploader.removeFile(file, true);
                    } else {
                        uploader.cancelFile(file);
                    }

                    this.files.splice(index,1);
                }
            },
            submit: function (e) {
                e.preventDefault();
                if (!$('#form').validate().form()) {
                    return;
                }
                if (app.files.length === 0) {
                    toastr.error('请先选择文件！');
                    return;
                }
                if (app.files.length === 1) {
                    toastr.error('只有一个文件，请使用单一文件上传！');
                    return;
                }
                showSpin();
                file_upload_list = [];
                let ids = '';
                for(let file of app.files){
                    ids += file.id + '|';
                }
                uploader.upload();
            },
            save(){

            }
        }
    });

    initUpLoaderPdf('drawing');

    function fileQueued(file) {
        if (!app.files.some(item => item.name === file.name)) {
            app.files.push(file);
            app.drawing_name = app.files[0].name;
        } else {
            toastr.error(file.name +'文件名重复');
            uploader.removeFile(file, true);
        }
    }

    function uploadSuccess(rs) {
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.drawing_url = rs.path;
            setTimeout(()=>{
                var url = '{{ url('mes/drawing/create/') ~ uid}}';
                $.post(url, {
                    code:app.code,
                    drawing_name:app.drawing_name,
                    drawing_url:app.drawing_url,
                    version_code:app.version_code,
                    remarks:app.remarks
                }, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },100);
        } else {
            toastr.error(rs.message);
        }
    }

    let spOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(spOption);

</script>
<style>

    .rv-img-dnd {
        width: 100%;
        height: 120px;
        border: 2px dotted #9d9d9d;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #666;
    }

    .rv-img-dnd i {
        font-size: 30px;
    }

</style>