{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>生产批次</label>
                                <div class="col-sm-8">
                                    <select class="form-control" name="notice_id" v-model="notice_id"  required>
                                        <option value="">请选择</option>
                                        <option v-for="row in noticeList" :value="row['id']">
                                            ${ row['code'] }
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>产品/规格</label>
                                <div class="col-sm-8">
                                    <select class="form-control" name="notice_detail_id" v-model="notice_detail_id" required>
                                        <option value="">请选择</option>
                                        <option v-for="row in noticeDetails" :value="row['id']">
                                            ${ row['product_name'] } / ${ row['product_code'] }
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>工艺</label>
                                <div class="col-sm-8">
                                    <select
                                      id="product_bom_id"
                                      class="form-control"
                                      v-model="product_bom_id"
                                      required
                                    >
                                      <option value="">请选择工艺</option>
                                      <option v-for="row in productBomList" :value="row['id']">
                                          ${ row['name'] }
                                      </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>产品数量</label>
                                <div class="col-sm-8">
                                    <input type="number" class="form-control" placeholder="输入产品数量" name="product_quantity" v-model="product_quantity" readonly required/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions right">
                    <button type="button" @click="submit" class="btn btn-primary">提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }},
            product_quantity: null
        },
        created() {
            if (!this.notice_id) {
                this.notice_id = '';
            }

            if (!this.product_bom_id) {
                this.product_bom_id = '';
            }

            if (!this.notice_detail_id) {
                this.notice_detail_id = '';
            }
        },
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                var url= '{{ url('purchase/orderww/createwwplan') }}';
                
                if (!/^[1-9]\d*$/.test(app.product_quantity)) {
                    toastr.error('产品数量必须是正整数');
                    return;
                }
                                  
                commonAjaxRequest(url, {
                    type: '2',
                    notice_id: app.notice_id,
                    notice_detail_id: app.notice_detail_id,
                    product_id: app.product_id,
                    product_bom_id: app.product_bom_id,
                    product_quantity: app.product_quantity,
                    product_bom_name: app.productBomList.find(function(item) {
                        return item.id == app.product_bom_id;
                    }).name,
                    remarks: app.remarks
                }, function (rs) {
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                })
            },
            onNoticeChange: function(newVal) {
                this.noticeDetails = [];
                this.productBomList = [];
                this.product_quantity = 0;
                this.notice_detail_id = '';
                this.product_bom_id = '';
                if (newVal) {
                    var that = this;
                    commonAjaxRequest('{{ url('purchase/orderww/changenotice') }}', {
                        notice_id: newVal,
                    }, function(rs) {
                        that.noticeDetails = rs.data;
                    });
                }
            },
            onNoticeDetailChange: function(newVal) {
                this.productBomList = [];
                this.product_quantity = 0;
                this.product_bom_id = '';
                if (newVal) {
                    selectedDetail = this.noticeDetails.find(function(item) {
                        return item.id == newVal;
                    });

                    this.product_id = selectedDetail.product_id
                    
                    var that = this;
                    commonAjaxRequest('{{ url('purchase/orderww/changenoticedetail') }}', {
                        notice_detail_id: newVal
                    }, function(rs) {
                        that.productBomList = rs.data;
                        that.product_quantity = rs.data[0].quantity;
                    });
                } else {
                    this.product_id = '';
                }
            },
        },
        watch: {
            notice_id: function(val) {
                this.onNoticeChange(val);
            },
            notice_detail_id: function(val) {
                this.onNoticeDetailChange(val);
            },
        }
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}