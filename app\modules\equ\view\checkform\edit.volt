{{ assets.outputJs('validate') }}
<div class="page-content">
    <div id="app">
        <div class="row" style="padding-left: 15px">
            <div class="col-sm-4" style="padding-left: 0">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">编辑表单</span>
                        </div>
                    </div>
                    <div class="portlet-body" style="min-height: 773px;">
                        <form id="form" class="form-horizontal" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">
                                            项目
                                        </label>
                                        <div class="col-sm-4">
                                            <input type="text" name="data.name" class="form-control" v-model="data.name" required/>
                                        </div>
                                        <label v-if="row_no > 0" class="col-sm-2 control-label">
                                            序号
                                        </label>
                                        <div v-if="row_no > 0" class="col-sm-4">
                                            <input type="text" v-model="row_no" class="form-control" readonly/>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">
                                            标题
                                        </label>
                                        <div class="col-sm-4">
                                            <input type="text"  v-model="data.label1_title" class="form-control"/>
                                        </div>
                                        <label class="col-sm-2 control-label">
                                            内容
                                        </label>
                                        <div class="col-sm-4">
                                            <textarea style="resize: none;" rows="3"  v-model="data.label1_value" class="form-control"></textarea>
                                        </div>

                                    </div>
                                    <div class="form-group" style="margin-top: 10px">
                                        <label class="col-sm-2 control-label">
                                            标题
                                        </label>
                                        <div class="col-sm-4">
                                            <input type="text"  v-model="data.label2_title" class="form-control"/>
                                        </div>
                                        <label class="col-sm-2 control-label">
                                            内容
                                        </label>
                                        <div class="col-sm-4">
                                            <textarea style="resize: none;" rows="3"  v-model="data.label2_value" class="form-control"></textarea>
                                        </div>

                                    </div>
                                    <div class="form-group" style="margin-top: 10px">
                                        <label class="col-sm-2 control-label">
                                            标题
                                        </label>
                                        <div class="col-sm-4">
                                            <input type="text"  v-model="data.label3_title" class="form-control"/>
                                        </div>
                                        <label class="col-sm-2 control-label">
                                            内容
                                        </label>
                                        <div class="col-sm-4">
                                            <textarea style="resize: none;" rows="3"  v-model="data.label3_value" class="form-control"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-actions" style="text-align: right">
                                <button type="button" class="btn default" @click="cleanForm">取消</button>
                                <template>
                                    <button v-if="data.id == ''" type="button" class="btn blue" @click="addForm"><i class="fa fa-plus"></i> 添加</button>
                                    <button v-else type="button" class="btn yellow" @click="editFormSave"><i class="fa fa-check"></i> 保存</button>
                                </template>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-sm-8" style="padding-left: 0">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">表单详细</span>
                        </div>
                        <div class="actions">
                            <div class="input-group input-icon" style="width: 300px;float: right;margin-left: 20px;">
                                <i class="fa fa-bars tooltips" data-original-title="表单名称" data-container="body"></i>
                                <input type="text" maxlength="20" class="form-control" name="name" v-model="name" placeholder="表单名称">
                                <span class="input-group-btn">
                                     <button class="btn yellow" @click="saveFormData" style="width: 80px"> <i class="fa fa-check"></i> 保存</button>
                                </span>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label style="margin-top: 7px;" class="col-sm-4 control-label">类型：</label>
                                <div class="col-sm-8" style="margin-left: -20px">
                                    <select name="type" v-model="type" class="bs-select form-control">
                                        <option value="1">点检</option>
                                        <option value="2">保养磨耗</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label style="margin-top: 7px;" class="col-sm-6 control-label">文件番号：</label>
                                <div class="col-sm-6" style="margin-left: -20px">
                                    <input type="text" maxlength="20" class="form-control" name="code" v-model="code" placeholder="文件番号">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label style="margin-top: 7px;" class="col-sm-4 control-label">备注：</label>
                                <div class="col-sm-8" style="margin-left: -20px">
                                    <input type="text" maxlength="20" class="form-control" name="remarks" v-model="remarks" placeholder="备注 ">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body" style="height: 773px;overflow: auto">
                        <table class="factory-table" style="margin-bottom: 0;">
                            <thead>
                            <tr style="background-color: #3598DC;color: #FFFFFF">
                                <th>序号</th>
                                <th>排序</th>
                                <th>项目</th>
                                <th>标题1</th>
                                <th>内容1</th>
                                <th>标题2</th>
                                <th>内容2</th>
                                <th>标题3</th>
                                <th>内容3</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <template v-for="item,idx in form_data">
                                <tr>
                                    <td v-text="idx+1"></td>
                                    <td>
                                        <a href="javascript:;" @click="formSort(1,idx)">
                                            <i class="fa fa-long-arrow-up"></i>
                                        </a>
                                        <a href="javascript:;" @click="formSort(2,idx)">
                                            <i class="fa fa-long-arrow-down"></i>
                                        </a>
                                    </td>
                                    <td v-text="item.name"></td>
                                    <td v-text="item.label1_title"></td>
                                    <td v-text="item.label1_value"></td>
                                    <td v-text="item.label2_title"></td>
                                    <td v-text="item.label2_value"></td>
                                    <td v-text="item.label3_title"></td>
                                    <td v-text="item.label3_value"></td>
                                    <td>
                                        <a href="javascript:;" @click="copyForm(idx)" title="复制" style="color: #c49f47;font-size: 16px;margin-right: 5px;" >
                                            <i class="fa fa-copy"></i>
                                        </a>
                                        <a href="javascript:;" @click="editForm(idx)" title="编辑" style="color: blue;font-size: 16px;margin-right: 5px;" >
                                            <i class="fa fa-edit"></i>
                                        </a>
                                        <a href="javascript:" @click="delForm(idx)" title="删除" style="color: red;font-size: 20px">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </td>
                                </tr>
                            </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var defaultForm = {{ defaultForm | json_encode }};
    var app = new Vue({
        el: '#app',
        data: {{ jsonEquCheckForm }},
        methods: {
            getUuid() {
                var s = [];
                var hexDigits = "0123456789abcdef";
                var firstDigits = "abcdefghij";
                s[0] = firstDigits.substr(Math.floor(Math.random() * 0x9), 1);
                for (var i = 1; i < 10; i++) {
                    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
                }
                return s.join("");
            },
            addForm() {
                if( !$('#form').validate().form() )
                    return;

                this.data.id = this.getUuid();
                this.form_data.push(JSON.parse(JSON.stringify(this.data)));
                this.cleanForm();
            },
            copyForm(idx) {
                this.row_no = 0;
                let data = JSON.parse(JSON.stringify(this.form_data[idx]));
                data.id = '';
                this.data = data;
                app.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
            },
            editForm(idx){
                this.row_no = idx + 1;
                this.data = JSON.parse(JSON.stringify(this.form_data[idx]));
                app.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
            },
            editFormSave(){
                if( !$('#form').validate().form() )
                    return;
                if (this.data.id == ''){
                    toastr.error('ID不存在!');
                    return;
                }

                for(let i = 0; i < this.form_data.length; i++){
                    if (this.form_data[i].id == this.data.id){
                        this.form_data[i] =  JSON.parse(JSON.stringify(this.data));
                        break;
                    }
                }
                this.cleanForm();
            },
            delForm(idx) {
                let item = this.form_data[idx];
                if (this.data.id == item.id) {
                    this.cleanForm();
                }

                this.form_data.splice(idx, 1);
            },
            cleanForm() {
                this.row_no = 0;
                this.data = {...defaultForm};
                app.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
            },

            formSort (sort,idx) {
                let item = this.form_data[idx];
                if (sort == 1){
                    //up
                    if (idx == 0){
                        return;
                    }
                    this.form_data.splice(idx,1);
                    this.form_data.splice(idx-1,0,item);
                } else {
                    //down
                    if(idx == this.form_data.length-1) {
                        return;
                    }
                    this.form_data.splice(idx,1);
                    this.form_data.splice(idx+1,0,item);
                }
            },
            saveFormData(){
                if (this.name == ''){
                    toastr.error('请添写表单名称');
                    return;
                }
                var url= '{{ url('equ/checkform/edit/')~uid}}';
                showSpin();
                $.post(url, {
                    name : this.name,
                    remarks : this.remarks,
                    code : this.code,
                    type : this.type,
                    data: encodeURI(JSON.stringify(this.form_data))
                }, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        toastr.success('保存成功！');
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            },
        },
    });
    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);

</script>

<style>
    .factory-table {
        margin-bottom: 0;
        width: 100%;
        margin-top: 10px
    }

    .factory-table > thead > tr > th, .factory-table > tbody > tr > td {
        border: 1px solid #F2F2F2;
        height: 35px;
        padding-left: 5px ;
        background-color: #ffffff;
    }

    .factory-table > thead > tr > th {
        font-weight: normal;
        background-color: #3598DC;
    }

    .detail-table {
        margin-bottom: 0;
        width: 100%;
        margin-top: 10px
    }

    .detail-table > thead > tr > th, .detail-table > tbody > tr > td {
        border: 1px solid #F2F2F2;
        height: 35px;
        padding-left: 5px ;
        background-color: #ffffff;
    }

    .detail-table > thead > tr > th {
        font-weight: normal;
        background-color: #B2B2B2;
    }
</style>