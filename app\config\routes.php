<?php

$router = $di->get("router");
$router->removeExtraSlashes(true);

foreach ($application->getModules() as $key => $module) {
    $namespace = str_replace('Module','Controller', $module["className"]);
    $namespace = str_replace('Controllers','Modules', $namespace);
    $api = str_replace('Controller','Api', $namespace);

    $router->add('/'.$key.'/:params', [
        'namespace' => $namespace,
        'module' => $key,
        'controller' => 'index',
        'action' => 'index',
        'params' => 1
    ])->setName($key);

    $router->add('/'.$key.'/:controller/:params', [
        'namespace' => $namespace,
        'module' => $key,
        'controller' => 1,
        'action' => 'index',
        'params' => 2
    ]);

    $router->add('/'.$key.'/:controller/:action/:params', [
        'namespace' => $namespace,
        'module' => $key,
        'controller' => 1,
        'action' => 2,
        'params' => 3
    ]);

    $router->add('/api/'.$key.'/:params', [
        'namespace' => $api,
        'module' => $key,
        'controller' => 'index',
        'action' => 'index',
        'params' => 1
    ])->setName($key);

    $router->add('/api/'.$key.'/:controller/:action/:params', [
        'namespace' => $api,
        'module' => $key,
        'controller' => 1,
        'action' => 2,
        'params' => 3
    ]);
}

$di->set("router", $router);