<?php

use Phalcon\Cli\Console as ConsoleApp;
use Phalcon\Di\FactoryDefault\Cli as FactoryDefault;

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

$di = new FactoryDefault();

include APP_PATH . '/config/services.php';
include APP_PATH . '/config/services_cron.php';

include APP_PATH . '/config/loader.php';
include APP_PATH . '/vendor/autoload.php';

$config = $di->getConfig();
$console = new ConsoleApp($di);

$console->registerModules([
    'cron' => ['className' => 'Envsan\Modules\Cron\Module']
]);

$di->setShared("console", $console);

$arguments = ['module' => 'cron'];

foreach ($argv as $k => $arg) {
    if ($k == 1) {
        $arguments['task'] = $arg;
    }
    elseif ($k == 2) {
        $arguments['action'] = $arg;
    }
    elseif ($k >= 3) {
        $arguments['params'][] = $arg;
    }
}

try {
    $console->handle($arguments);
    echo PHP_EOL;
} catch (Exception $e) {
    echo $e->getMessage() . PHP_EOL;
    echo $e->getTraceAsString() . PHP_EOL;
    exit(255);
}
