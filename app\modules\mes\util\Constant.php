<?php
namespace Envsan\Modules\Mes\Util;

class Constant
{
    public static $week_days = array('日', '一', '二', '三', '四', '五', '六');
    public static $ship_flow_icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAYAAAHeEJUAAAAABGdBTUEAALGPC/xhBQAAAvVJREFUOBGNVEFrE0EU/mY3bQoiFlOkaUJrQUQoWMGePLX24EH0IIoHKQiCV0G8iE1covgLiqA/QTzVm1JPogc9tIJYFaQtlhQxqYjSpunu+L7JvmUTU3AgmTfvffPNN++9WSA1DO182f6xwILzD5btfAoQmwL5KJEwiQyVbSVZ0IgRyV6PTpIJ81E5ZvqfHQR0HUOBHW4L5Et2kQ6Zf7iAOhTFAA8s0pEP7AXO1uAA52SbqGk6h/6J45LaLhO64ByfcUzM39V7ZiAdS2yCePPEIQYvTUHqM/n7dgQNfBKWPjpF4ISk8q3J4nB11qw6X8l+FsF3EhlkEMfrjIer3wJTLwS2aCNcj4DbGxXTw00JmAuO+Ni6bBxVUCvS5d9aa04+so4pHW5jLTywuXAL7jJ+D06sl82Sgl2JuVBQn498zkc2bGKxULHjCnSMadBKYDYYHAtsby1EQ5lNGrQd4Y3v4Zo0XdGEmDno46yCM9Tk+RiJmUYHS/aXHPNTcjxcbTFna000PFJHIVZ5lFRqRpJWk9/+QtlOUYJj9HG5pVFEU7zqIYDVsw2s+AJaD8wTd2umgSCCyUxgGsS1Y6TBwXQQTFuZaHcd8gAGioE90hlsY+wMcs30RduYtxanjMGal8H5dMW67dmT1JFtYUEe8LiQLRsPZ6IIc7A4J5tqco3T0pnv/4u0kyzrYUq7gASuEyI8VXKvB9Odytv6jS/PNaZBln0nioJG/AVQRZvApOdhjj3Jt8QC8Im09SafwdBdvIpztpxWxpeKCC+EsFdS8DCyuCn2munFpL7ctHKp+Xc5cMybeIyMAN33SPL3ZR9QV1XVwLyzHm6Iv0/yeUuUb7PPlZC4D4HZkeu6dpF4v9j9MreGtMbxMMRLIcjJic9yHi7WQ3yVKzZVWUr5UrViJvn1FfUlwe/KYVfYyWRLSGNu16hR01U9IacajXPei0wx/5BqgInvJN+MMNtNme7ReU9SBbgntovn0kKHpFg7UogZvaZiOue/q1SBo9ktHzQAAAAASUVORK5CYII=';
    public static $group_flow_icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAgZJREFUOE+tlD9oE2EYxn/PRQeh2KqLi0gXKaIFNwd1003BISAoocldIm1ALSpOTm7aIaJUm7tLInUQugg66aaCa0WR4qAgLi4a/wxiTF77xQbSkKQh+C0H3/c+v3u+e597xX9e2oiXTFpidJTDqmPVn7xYWlK9n6YvcDZpW75v5YkHh9Ygz2t1jlUq+tUL2hcYZCwlcU9w34QwTgOpMNbiUMCsb+eBgmDKDIcsY5wLS7o1FDCdtl0J8UpimwOY8RWPySjSp6GATpTN2h7qPGsCEhwJQ70buikzU7azluAMxqUmRMyZWIwifR7KYRBYWkZpndhIhyVVhgI60dm07TWPR02DDY4vlPV24Cu7EI+Nsd8JqlVet0KcTdsbtxeWtc89e9X9+ypty/et4IGLiltzYazLzcZ0AIOMzUlcdGcNuBnHutDCrAMGvs0LpmmwoM0UikWtdAPmcjbBH2ZN5BowH8fKdwdmzJeIMKbDku62ijoduv0gsLyM23Q0qfPK22W8x/hmHgfiWF+6OcynbMfvTSyvxmikVme8UlG1q8P2Nxu89BKcKhb1sd2h79tur8EDPA5KzBQj3WnvQ9fhkPXtBjTD/AN4aHB0rYNPzTgpMQJcD2Nd6YxQz2mTzdiJ1Z/tGmKyQ7SMx9Uw1ONuedxwwAaBjavBhBObx0oU6cPAwe5XOOjZX7rgzBUtr1hcAAAAAElFTkSuQmCC';
    public static $shift_types = [
        '白班' => ['start_time'=>'07:30','end_time'=>'16:30'],
        '夜班' => ['start_time'=>'20:00','end_time'=>'06:00'],
    ];

    public static $product_status = [
        10 => '待提交',
        20 => '待审核',
        30 => '已提交'
    ];

    public static $ship_plan_types = [
        1 => '产量排产',
        2 => '时间排产'
    ];

    public static $product_bom_status = [
        10 => '待确认',
        20 => '已确认'
    ];

    public static $check_handle_types = ['返修','报废'];

    public static $check_status = [
        10 => '待提交',
        20 => '已提交'
    ];

    public static $check_error_status = [
        10 => '待认领',
        20 => '待审批',
        30 => '待处置',
        40 => '待确认',
        50 => '已完成'
    ];

    public static $drawing_status = array(10 => '待提交', 20 => '使用中', 30 => '暂停使用', 40 => '待发布');

    public static $drawing_version_status = [10 => '待提交', 20 => '待审核', 30 => '审核通过'];

    public static $notice_status = array(10 => '待提交', 20 => '待审批', 30 => '已下达', 40 => '停产');

    public static $report_status = array(10 => '待提交', 20 => '已提交');

    public static $notice_detail_status = array(10 => '待提交', 20 => '待审批', 30 => '已下达', 40 => '停产');

    public static $bom_work_types = array(1 => '日工', 2 => '计件');

    public static $bom_outsourcing = array(0 => '否', 1 => '是');

    public static $quality_times = [
        ['name' => '7:30-9:30','begin' => '07:00','end' => '09:30' ,'check_data'=>[]],
        ['name' => '9:35-12:00','begin' => '09:30','end' => '12:00' ,'check_data'=>[]],
        ['name' => '12:30-14:30','begin' => '12:00','end' => '14:30' ,'check_data'=>[]],
        ['name' => '14:35-16:00','begin' => '14:30','end' => '16:00' ,'check_data'=>[]],
    ];
}