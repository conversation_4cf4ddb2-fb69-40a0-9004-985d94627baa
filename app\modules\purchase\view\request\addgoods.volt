{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <!-- 已选择物料区域 -->
    <div class="row" v-if="selectedGoods.length > 0">
        <div class="col-sm-12">
            <div class="portlet light portlet-selected" style="margin-bottom: 10px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-check font-blue"></i>
                        <span class="caption-subject font-blue bold">已选择物料 (${ selectedGoods.length })</span>
                    </div>
                    <div class="actions">
                        <button type="button" class="btn blue" @click="confirmSelection">
                            <i class="fa fa-check"></i> 确认选择
                        </button>
                        <button type="button" class="btn default" @click="clearAll">
                            <i class="fa fa-times"></i> 清空
                        </button>
                    </div>
                </div>
                <div class="portlet-body" style="max-height: 200px; overflow-y: auto;">
                    <div class="selected-items">
                        <span v-for="(item, index) in selectedGoods" :key="item.id" 
                              class="label label-info selected-item">
                            ${ item.code } - ${ item.name }
                            <i class="fa fa-times" @click="removeSelected(index)" style="margin-left: 5px; cursor: pointer;"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 物料选择区域 -->
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-goods" style="margin-bottom: 0;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">物料信息</span>
                    </div>
                    <div class="actions">
                        <span class="text-muted">请先选择左侧目录，然后选择物料</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="search-page">
                                <div class="search-bar bordered">
                                    <div class="tree-box">
                                        <div id="tree"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-8">
                            <div class="search-page">
                                <div class="row table-search-bar">
                                    <div class="col-sm-4">
                                        <div class="input-group">
                                            <span class="input-group-addon">物料</span>
                                            <input type="text" class="form-control" name="goods" v-model="goods">
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <div class="input-group">
                                            <span class="input-group-addon">供应商</span>
                                            <input type="text" class="form-control" name="supplier" v-model="supplier">
                                        </div>
                                    </div>
                                    <div class="col-sm-4" v-if="goods_list.length > 0">
                                        <button type="button" class="btn btn-sm blue" @click="selectAllVisible">
                                            <i class="fa fa-check-square-o"></i> 全选当前页
                                        </button>
                                    </div>
                                </div>
                                <div class="zh-table-box">
                                    <div class="zh-table-box-content">
                                        <table class="table table-bordered table-big">
                                            <thead>
                                            <tr>
                                                <th style="width: 50px;">选择</th>
                                                <th>编码</th>
                                                <th style="width: 130px;">名称</th>
                                                <th>规格型号</th>
                                                <th>库存数量</th>
                                                <th>供应商</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr v-if="goods_list.length == 0">
                                                <td colspan="6" style="text-align: center;">
                                                    <span v-if="!uid">请选择左侧目录</span>
                                                    <span v-else>没有数据</span>
                                                </td>
                                            </tr>
                                            <tr v-for="row, index in goods_list" v-if="row.show == 1">
                                                <td style="text-align: center; vertical-align: middle;">
                                                    <input type="checkbox" :value="row.id" :checked="isSelected(row.id)" @change="toggleSelection(row)" style="margin: 0;">
                                                </td>
                                                <td><span v-text="row.code"></span></td>
                                                <td><span v-text="row.name"></span></td>
                                                <td><span v-text="row.model"></span></td>
                                                <td><span v-text="row.stock_cnt + ' ' + row.deputy_unit"></span></td>
                                                <td><span v-text="row.supplier_name"></span></td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{ partial('check') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            uid: '',
            goods_list: [],
            supplier: '',
            goods: '',
            selectedGoods: [], // 已选择的物料列表
        },
        methods: {
            goodsShow() {
                for (let item of this.goods_list) {
                    item.show = 1;
                    if (this.supplier != '') {
                        if (item.supplier_name.indexOf(this.supplier) == -1){
                            item.show = 0;
                        }
                    }

                    if (this.goods != '') {
                        let name = item.code + item.name + (item.model || '');
                        let conditions = this.goods.split(' ');
                        let show_flag = 1;
                        for (let condition of conditions) {
                            if (name.indexOf(condition) < 0) {
                                show_flag = 0;
                                break;
                            }
                        }
                        item.show = show_flag;
                    }
                }
            },
            isSelected(goodsId) {
                return this.selectedGoods.some(item => item.id === goodsId);
            },
            toggleSelection(goods) {
                let isCurrentlySelected = this.isSelected(goods.id);
                
                if (isCurrentlySelected) {
                    // 从已选择列表移除
                    this.selectedGoods = this.selectedGoods.filter(item => item.id !== goods.id);
                } else {
                    // 添加到已选择列表
                    this.selectedGoods.push({...goods});
                }
            },
            removeSelected(index) {
                this.selectedGoods.splice(index, 1);
            },
            clearAll() {
                this.selectedGoods = [];
            },
            selectAllVisible() {
                this.goods_list.forEach(item => {
                    if (item.show == 1 && !this.isSelected(item.id)) {
                        this.selectedGoods.push({...item});
                    }
                });
            },
            confirmSelection() {
                if (this.selectedGoods.length === 0) {
                    toastr.warning('请至少选择一个物料');
                    return;
                }
                // 将选中的物料数据传回父页面
                if (window.parent) {
                    // 通过 postMessage 或者其他方式传递数据给父页面                  
                    top.window.layer_data2 = this.selectedGoods;
                    top.window.layer_result2 = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                } else {
                    toastr.success('已选择 ' + this.selectedGoods.length + ' 个物料');
                }
            }
        },
        watch: {
            uid: function(val) {
                if (!val) return;
                app.uid = val;
                commonAjaxRequest('{{ url('purchase/goodstype/change/') }}' + val, {}, function(rs) {
                    console.log(1,rs)
                    // 成功回调
                    app.supplier = '';
                    app.goods = '';
                    app.goods_list = rs.data;
                    // 为每个物料添加 show 字段用于筛选显示
                    app.goods_list.forEach(item => {
                        item.show = 1;
                    });
                });
            },
            supplier: function() {
                this.goodsShow();
            },
            goods: function() {
                this.goodsShow();
            }
        },
        mounted() {
            // 检查已选择的物料（如果是编辑模式）
            const urlParams = new URLSearchParams(window.location.search);
            const selectedIds = urlParams.get('selected');
            if (selectedIds) {
                // 处理已选择的物料ID
                console.log('已选择的物料ID:', selectedIds);
            }
        }
    });

    function initSize() {
        let h = $(window).height() - 35 - 27;
        
        // 计算已选择区域的高度
        let selectedHeight = $('.portlet-selected').length > 0 ? $('.portlet-selected').outerHeight(true) : 0;

        let goods_body_height = h - 8 - $(".alert").outerHeight(true)
            - $(".portlet-goods .portlet-title").outerHeight(true) - selectedHeight;

        $(".portlet-goods .tree-box").height(goods_body_height);

        $(".portlet-goods .zh-table-box-content").height(goods_body_height
            - $(".portlet-goods .table-search-bar").outerHeight(true));
    }

    var $tree = $('#tree');

    $(function () {
        $tree.jstree({
            "core": {"data": {{ jsonTree | json_encode}}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            app.uid = data.node.original.uid;
        });

        initSize();
        
        // 监听窗口大小变化
        $(window).resize(function() {
            initSize();
        });
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: true,
        autoclose: true,
        format: 'yyyy-mm-dd',
        pickerPosition: 'bottom-left'
    }).on('changeDate', function () {
        let obj = $(this).find('input');
        app.form[$(obj).attr('name')] = $(obj).val();
    });

    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);
</script>
<style>
    .tree-box {
        overflow-y: auto;
    }

    .table-search-bar {
        margin-bottom: 20px;
    }

    .zh-table-box table thead {
        z-index: 3;
    }
    
    .selected-items {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .selected-item {
        display: inline-flex;
        align-items: center;
        margin: 2px;
        padding: 5px 8px;
        font-size: 12px;
    }
    
    .selected-item i {
        cursor: pointer;
        opacity: 0.8;
    }
    
    .selected-item i:hover {
        opacity: 1;
    }
    
    .table tbody tr td {
        vertical-align: middle !important;
    }
    
    .table tbody tr td input[type="checkbox"] {
        margin: 0 !important;
        vertical-align: middle;
    }
</style>