<?php
namespace Envsan\Modules\Trade\Controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'trade']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->moduleExistThenDie('trade');
        $res = [
            [
                'name' => '客户管理',
                'identity' => 'trade:customer',
                'action' => [
                    ['name' => '客户管理', 'identity' => 'trade:customer:list', 'comment' => ''],
                    ['name' => '创建客户', 'identity' => 'trade:customer:create', 'comment' => '']
                ]
            ],
            [
                'name' => '开票管理',
                'identity' => 'trade:invoice',
                'action' => [
                    ['name' => '开票管理', 'identity' => 'trade:invoice:list', 'comment' => ''],
                    ['name' => '创建开票', 'identity' => 'trade:invoice:create', 'comment' => '']
                ]
            ],
            [
                'name' => '订单管理',
                'identity' => 'trade:order',
                'action' => [
                    ['name' => '订单管理', 'identity' => 'trade:order:list', 'comment' => ''],
                    ['name' => '创建订单', 'identity' => 'trade:order:create', 'comment' => ''],
                    ['name' => '订单查询', 'identity' => 'trade:order:search', 'comment' => '']
                ]
            ],
            [
                'name' => '订单详细查询',
                'identity' => 'trade:orderdetail',
                'action' => [
                    ['name' => '订单详细查询', 'identity' => 'trade:orderdetail:list', 'comment' => ''],
                ]
            ],

            [
                'name' => '库存查询',
                'identity' => 'trade:stock',
                'action' => [
                    ['name' => '库存查询', 'identity' => 'trade:stock:list', 'comment' => ''],
                ]
            ],
            [
                'name' => '收款管理',
                'identity' => 'trade:receive',
                'action' => [
                    ['name' => '收款管理', 'identity' => 'trade:receive:list', 'comment' => ''],
                    ['name' => '创建收款', 'identity' => 'trade:receive:create', 'comment' => ''],
                ]
            ],
            [
                'name' => '发货管理',
                'identity' => 'trade:outstock',
                'action' => [
                    ['name' => '发货管理', 'identity' => 'trade:outstock:list', 'comment' => ''],
                    ['name' => '创建发货', 'identity' => 'trade:outstock:create', 'comment' => ''],
                    ['name' => '发货查询', 'identity' => 'trade:outstock:search', 'comment' => ''],
                ]
            ],
        ];

        $ret = new JsonData();
        $this->db->begin();
        try {
            $this->makePackage('trade', '销售模块', '1.0', '提供销售模块功能');
            $module = $this->makeModule('trade', '销售模块');
            foreach ($res as $controller) {
                $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                foreach ($controller['action'] as $action) {
                    $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage(), $e->getTraceAsString());
            $ret->message = '发生错误';
        }
        $ret->emptyIsOk();
        die(json_encode($ret,JSON_UNESCAPED_UNICODE));
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}