<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Work\Util\Constant;
use Phalcon\Mvc\User\Component;
use Upyun\Upyun;

class CommonService extends Component
{
    function uploadImage($folder_name = '') {
        $ret = array();
        $ret['status'] = 'error';
        $ret['message'] = '';
        if ($this->request->hasFiles() == true) {
            $config = new \Upyun\Config($this->config->upyun->bucket, $this->config->upyun->user, $this->config->upyun->password);
            $upyun = new UpYun($config);
            foreach ($this->request->getUploadedFiles() as $file) {
                if ($file->getSize() > 10 * 1024 * 1024) {
                    break;
                }

                $ext = pathinfo($file->getName(), PATHINFO_EXTENSION);
                if ($ext !== 'jpg' && $ext !== 'jpeg' && $ext !== 'png') {
                    break;
                }

                $path = $this->config->upyun->uploadDir.UUID::make().'.'.$ext;

                try {
                    if ($file->moveTo($path)) {
                        $file_handle = fopen($path, 'r');

                        if (!empty($folder_name)) {
                            $folder_name = $folder_name.'/';
                        } else {
                            $user = SessionData::user();
                            $folder_name = $user->owner.'/'.$user->group_id.'/'.DateUtil::ymd().'/';
                        }

                        $filename = '/image/'.$folder_name.time().'.'.$ext;
                        $upyun->write($filename, $file_handle);
                        if (is_resource($file_handle))
                            fclose($file_handle);
                        unlink($path); //删除临时文件

                        $ret['status'] = 'ok';
                        $ret['path'] = $this->getImagePath();
                        $ret['file_name'] = $filename;
                    }
                } catch (\Exception $e) {
                    Logger::error($e->getMessage(), $e->getTraceAsString());
                }
                break;
            }
        }
        return $ret;
    }

    public function checkPlate($plate)
    {
        $regular = "/[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新使]{1}[A-Z]{1}[0-9a-zA-Z]{5}$/u";
        preg_match($regular, $plate, $match);
        if (isset($match[0])) {
            return true;
        }
    }

    public function getGroupList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.uid,a.id,a.name')
            ->addFrom('Envsan\Modules\Sys\Model\Group', 'a')
            ->where('a.del_flag = 0')
            ->orderBy('a.id');
        ModelUtil::limitGroup('a.id', $builder);
        return $builder->getQuery()->execute();
    }

    public function getCkGroupList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.uid,a.id,a.name')
            ->addFrom('Envsan\Modules\Sys\Model\Group', 'a')
            ->where('a.del_flag = 0 and a.code = ?1',[1=>'ck'])
            ->orderBy('a.id');
        ModelUtil::limitGroup('a.id', $builder);
        return $builder->getQuery()->execute();
    }

    public function getConfig($id){

        $jrow = '';
        $builder = $this->modelsManager->createBuilder()
            ->columns('t1.*')
            ->addFrom('Envsan\Modules\Work\Model\WorkConfig','t1')
            ->where('t1.del_flag = 0 and t1.id = '.$id);

        //登录用户组织限制配置
        ModelUtil::limitGroup('t1.group_id', $builder);

        $jrows = $builder->getQuery()->execute()->toArray();

        if (count($jrows) > 0){
            $jrow = $jrows[0];
        }
        return $jrow;
    }

    public function getFormList(){
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.uid,a.id,a.name')
            ->addFrom('Envsan\Modules\Work\Model\WorkForm', 'a')
            ->where('a.del_flag = 0 and a.group_id = ?1',[1=>$user->group_id])
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }

    public function getWorkList($main_id,$pid){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.uid,
                t1.type_name as name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->where('t1.del_flag=0 and t1.main_id = ?1',[1=>$main_id])
            ->orderBy('t1.id asc');
        $list = $builder->getQuery()->execute()->toArray();
        if ($pid != $main_id){
            $arr = [];
            $this->getPFlowList($arr,$pid);
            foreach ($arr as $item){
                array_unshift($list,$item);
            }
        }
        return $list;
    }

    public function getPFlowList(&$arr,$pid){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.uid,
                t1.id,
                t1.pid,
                t1.type_name as name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->where('t1.del_flag=0 and t1.id = ?1',[1=>$pid])
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0){
            $row = $rows[0];
            array_push($arr,['uid'=>$row->uid,'name'=>$row->name]);
            if ($row->id != $row->pid){
                $this->getPFlowList($arr,$row->pid);
            }
        }
    }

    public function getServiceData($form_data_list){
        $data = [];
        $detailes = [];
        foreach ($form_data_list as $form_data){
            foreach ($form_data as $form_row){
                if ($form_row['type'] == 99){
                    foreach ($form_row['data_list'] as $data_list ){
                        $detail_item = [];
                        foreach ($data_list as $data_item){
                            foreach (Constant::$service_input_types as $service_input_type){
                                if ($service_input_type['id'] == $data_item['id']){
                                    if (!empty($data_item['value'])){
                                        $detail_item[$data_item['id']] = $data_item['value'];
                                        break;
                                    }
                                }
                            }
                        }
                        array_push($detailes,$detail_item);
                    }
                } else {
                    foreach (Constant::$service_input_types as $service_input_type){
                        if ($service_input_type['id'] == $form_row['id']){
                            if (!empty($form_row['value'])){
                                $data[$form_row['id']] = $form_row['value'];
                                break;
                            }
                        }
                    }
                }
            }
        }
        $data['details'] = $detailes;
        return $data;
    }

    public function getWorkFormList($main_id,$pid){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.plate,
                t1.type_name,
                t1.form_data
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->where('t1.del_flag=0 and t1.main_id = ?1',[1=>$main_id])
            ->orderBy('t1.id asc');
        $list = $builder->getQuery()->execute()->toArray();
        $type_name = $list[0]['type_name'];
        if ($pid != $main_id){
            $arr = [];
            $this->getPFormFlowList($arr,$pid,$type_name);
            foreach ($arr as $item){
                array_unshift($list,$item);
            }
        }
        $form_list = [];
        foreach ($list as $item){
            if (!empty($item['form_data'])){
                $form_data = json_decode($item['form_data'],true);
                array_push($form_list,$form_data);
            }
        }
        $data = $this->getServiceData($form_list);
        return ['name' => $type_name,'data' => $data];
    }

    public function getPFormFlowList(&$arr,$pid,&$type_name){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.pid,
                t1.plate,
                t1.type_name,
                t1.form_data
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->where('t1.del_flag=0 and t1.id = ?1',[1=>$pid])
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0){
            $row = $rows[0];
            $type_name = $row->type_name;
            array_push($arr,['uid'=>$row->id,'form_data'=>$row->form_data]);
            if ($row->id != $row->pid){
                $this->getPFormFlowList($arr,$row->pid,$type_name);
            }
        }
    }

    public function getUserData(){
        $u = SessionData::user();
        $user = new \stdClass();
        $user->group_id = $u->group_id;
        $user->owner = $u->owner;
        $user->imgdir = $this->config->upyun->host;
        $user->bucket = '';
        $user->operator ='';
        $user->aliyun_ocr =  '';
        return $user;
    }

    public  function getSupplierList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkSupplier','t1')
            ->where('t1.del_flag=0')
            ->orderBy('t1.id asc');
        return  $builder->getQuery()->execute();
    }

    public function setFromData(&$form_data){
        foreach ($form_data as &$form_row){
            if ($form_row['type'] == 99){
                $list_data =  json_decode(json_encode($form_row['list'],JSON_UNESCAPED_UNICODE),true);
                $form_row['list'] = $list_data;
            }
        }
    }

    public function margeFormData(&$form_data,$value_data){
        foreach ($form_data as &$form){
            foreach ($value_data as $value){
                if ($form['id'] == $value['id']){
                    if ($form['type'] == 99){
                        foreach ($value['data_list'] as $data){
                            $d_list = json_decode(json_encode($form['list'],JSON_UNESCAPED_UNICODE),true);
                            foreach ($d_list as &$s_form){
                                foreach ($data as $s_value){
                                    if ($s_form['id'] == $s_value['id']){
                                        $s_form['value'] = $s_value['value'];
                                        break;
                                    }
                                }
                            }
                            array_push($form['values'],$d_list);
                        }
                    } else {
                        $form['value'] = $value['value'];
                    }
                    break;
                }
            }
        }
    }
}