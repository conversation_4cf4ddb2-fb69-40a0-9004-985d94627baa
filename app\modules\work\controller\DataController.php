<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\BaiduUtil;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Company;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Work\Model\WorkDibang;
use Envsan\Modules\Work\Model\WorkDelivery;
use Envsan\Modules\Work\Service\CommonService;
use Envsan\Modules\Work\Model\WorkData;
use Envsan\Modules\Work\Service\DataService;
use Envsan\Modules\Work\Service\GoodsService;
use Envsan\Modules\Work\Util\Constant;

/**
 * @name('审批')
 */
class DataController extends SuperController
{
    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        $s = new DataService();
        if ($type == 'json') {
            $this->setJsonResponse();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

}