<?php
namespace Envsan\Modules\Screen;

use Phalcon\Config;
use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\ModuleDefinitionInterface;
use Phalcon\Mvc\View;
use Phalcon\Mvc\View\Engine\Php as PhpEngine;

class Module implements ModuleDefinitionInterface
{
    /**
     * Registers an autoloader related to the module
     *
     * @param DiInterface $di
     */
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'Envsan\Modules\Screen\Api'        => __DIR__ . '/api/',
            'Envsan\Modules\Screen\Controller'     => __DIR__ . '/controller/',
            'Envsan\Modules\Screen\Service'    => __DIR__ . '/service/',
            'Envsan\Modules\Sys\Model'       => APP_PATH . '/modules/sys/model/',
            'Envsan\Modules\Mes\Model'       => APP_PATH . '/modules/mes/model/',
            'Envsan\Modules\Mes\Service'     => APP_PATH . '/modules/mes/service/',
            'Envsan\Modules\Mes\Util'    => APP_PATH . '/modules/mes/util/',
            'Envsan\Modules\Common\Service'     => APP_PATH . '/modules/common/service/',
            'Envsan\Modules\Trade\Model'       => APP_PATH . '/modules/trade/model/',
            'Envsan\Modules\Trade\Service'     => APP_PATH . '/modules/trade/service/',
            'Envsan\Modules\Purchase\Model'       => APP_PATH . '/modules/purchase/model/',
            'Envsan\Modules\Purchase\Service'     => APP_PATH . '/modules/purchase/service/',
            'Envsan\Modules\Equ\Model'       => APP_PATH . '/modules/equ/model/',
            'Envsan\Modules\Equ\Util'    => APP_PATH . '/modules/equ/util/',
            'Envsan\Modules\Common\Util'    => APP_PATH . '/modules/common/util/',
        ]);

        $loader->register();
    }

    /**
     * Registers services related to the module
     *
     * @param DiInterface $di
     */
    public function registerServices(DiInterface $di)
    {
        /**
         * Try to load local configuration
         */
        if (file_exists(__DIR__ . '/config/config.php')) {
            $override = new Config(include __DIR__ . '/config/config.php');
            $config = $di->get('config');

            if ($config instanceof Config) {
                $config->merge($override);
            } else {
                $config = $override;
            }
        }

        $di->setShared('view', function () {
            $view = new View();
            $view->setDI($this);
            $view->setViewsDir(__DIR__ . '/view/');
            $view->setPartialsDir(APP_PATH . '/modules/common/view/include/');
            $view->registerEngines([
                '.volt'  => 'voltShared',
                '.phtml' => PhpEngine::class
            ]);

            return $view;
        });

        $di->setShared('log', function() use($di) {
            $config = $di->get('config');
            $logger = new \Phalcon\Logger\Adapter\File( $config->application->logDir.'screen-'.date("Ymd").'.log' );
            return $logger;
        });
    }
}
