{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    计划日期<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <div class="input-group date dtpicker">
                                        <input type="text" class="form-control" name="plan_date" v-model="plan_date" required>
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    设备<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <select  name="item_id" class="form-control bs-select" v-model="item_id" data-size="8" data-live-search="true" required>
                                        <option value="">请选择</option>
                                        {% for item in equ_list %}
                                            <option value="{{ item.id }}">{{ item.code }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    模板<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <select  name="form_id" class="form-control bs-select" v-model="form_id" required>
                                        <option value="">请选择</option>
                                        {% for item in form_list %}
                                            <option value="{{ item['id'] }}">{{ item['name'] }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonLog }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName()=='edit' %}
                var url= '{{ url('equ/maintain/edit/'~id) }}';
                {% else %}
                var url= '{{ url('equ/maintain/create') }}';
                {% endif %}

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });
    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        format:'yyyy-mm-dd',
        todayBtn: false,
        autoclose: true,
        fontAwesome: true,
        pickerPosition: 'bottom-left'
    }).on('changeDate', function() {
        let obj = $(this).find('input');
        app[$(obj).attr('name')] = $(obj).val();
    });
</script>
