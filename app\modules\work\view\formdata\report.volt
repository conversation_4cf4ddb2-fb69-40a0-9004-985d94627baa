{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}

<div  id="app_ext_table" class="page-content" class="page-content">
    <div class="search-page">
        <div class="search-bar bordered" style="margin-bottom: 0;padding-bottom: 0">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-6 col-lg-6">
                        <h3 class="page-title">{{ form_name }}</h3>
                    </div>
                    <div class="col-md-6 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="appSearch">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div>
            <div class="search-bar bordered search-bar-table form-horizontal" style="margin-bottom: 0" v-if="param.conditions.length > 0">
                <div class="row">
                    <div v-for="(item, idx) in param.conditions" class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label" v-text="item.name"></label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <span v-if="item.type != 6" class="input-group-addon" v-text="item.search_type_name"></span>
                                    <input v-if="item.type != 6" type="text" class="form-control" :name="'col_' + idx" v-model="item.val"/>
                                    <div v-if="item.type == 6" class="input-group">
                                        <input type="text" class="form-control date dtpicker-search" :name="'val!' + idx" v-model="item.val"/>
                                        <span class="input-group-addon no-border-lr">至</span>
                                        <input type="text" class="form-control date dtpicker-search" :name="'val_end!' + idx" v-model="item.val_end"/>
                                    </div>
                                    <span class="input-group-btn">
                                <button type="button" class="btn red" @click="removeCondition(idx)"><i class="fa fa-times"></i></button>
                            </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zh-table-group">
                <div v-if="list_msg != ''" style="text-align: center;width: 100%;background-color: #FFF;padding: 15px" v-text="list_msg"></div>
                <template v-else>
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>
                                        序号
                                    </th>
                                    <th v-for="item in column_data" v-if="item.show == 1">
                                        <div style="display: flex;align-items: center;">
                                            <div v-text="item.name"></div>
                                            <div v-if="item.condition != '' && search_show" class="col-btn-search" @click="openCondition(item)">
                                                <i class="fa fa-search"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="td-last t-last">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="row, idx in list">
                                    <td>
                                        <div v-text="(paginator.current - 1) * param.limit + idx + 1"></div>
                                    </td>
                                    <td v-for="column in column_data" v-if="column.show == 1">
                                        <div style="max-width: 200px">
                                            <template v-if="column.type == 9">
                                                <template v-if="row[column.id].c == ''">
                                                    <span v-text="row[column.id].v"></span>
                                                    <span v-if="column.unit != ''" v-text="'('+column.unit+')'"></span>
                                                </template>
                                                <template v-else>
                                            <span :class="['label', `label-${row[column.id].c}`]">
                                                <span v-text="row[column.id].v"></span>
                                                <span v-if="column.unit != ''" v-text="'('+column.unit+')'"></span>
                                            </span>
                                                </template>
                                            </template>
                                            <template v-else>
                                                <span v-text="row[column.id]"></span>
                                                <span v-if="column.unit != ''" v-text="'('+column.unit+')'"></span>
                                            </template>
                                        </div>
                                    </td>
                                    <td class="td-last" :class="active_idx == idx ? 'td-active' : ''" >
                                        <div>
                                            <div class="btn-group">
                                                <button type="button" class="btn blue btn-outline" @click="dataView(row)">
                                                    详情
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div v-if="empty_msg != ''" style="text-align: center;width: 100%;background-color: #FFF;padding: 15px" v-text="empty_msg"></div>
                        </div>
                    </div>
                    <div class="zh-table-footer" v-if="list.length > 0">
                        <div class="zh-left">
                            <div style="margin-right: 5px;">显示第 <span v-text="paginator.limit * (paginator.current - 1) + 1"></span> 到第 <span v-text="paginator.limit * paginator.current"></span> 条记录，总共 <span v-text="paginator.total_items"></span> 条记录</div>
                            <div style="display: flex;align-items: center;">
                                <div>每页显示</div>&nbsp;
                                <span class="btn-group dropup">
                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                            <span class="page-size" v-text="paginator.limit"></span>
                            <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            <li><a href="javascript:void(0)" @click="updateLimit(10)">10</a></li>
                            <li><a href="javascript:void(0)" @click="updateLimit(25)">25</a></li>
                            <li><a href="javascript:void(0)" @click="updateLimit(50)">50</a></li>
                        </ul>
                    </span>&nbsp;
                                <div>条记录</div>
                            </div>
                        </div>
                        <div class="zh-right pull-right">
                            <ul class="pagination" v-if="paginator.total_pages > 0">
                                <li class="page-pre" @click="prevPage" :class="paginator.current == 1 ? 'disabled' : ''"><a href="javascript:void(0)">‹</a>
                                </li>
                                <li v-if="paginator.current >= 5" @click="setPage(1)" class="page-number"  :class="paginator.current == 1 ? 'active' : ''"><a href="javascript:void(0)">1</a>
                                </li>
                                <li v-if="paginator.current >= 5" class="page-last-separator disabled"><a href="javascript:void(0)">...</a>
                                </li>
                                <template v-if="paginator.total_pages > 5">
                                    <template v-if="paginator.current >= 5">
                                        <li class="page-number" @click="setPage(paginator.current -1)"><a href="javascript:void(0)" v-text="paginator.current -1"></a>
                                        </li>
                                        <li v-if="paginator.total_pages > paginator.current + 1" class="page-number active" @click="setPage(paginator.current)"><a href="javascript:void(0)" v-text="paginator.current"></a>
                                        </li>
                                        <li v-if="paginator.total_pages > paginator.current + 1" class="page-number" @click="setPage(paginator.current + 1)"><a href="javascript:void(0)"  v-text="paginator.current + 1"></a>
                                        </li>
                                    </template>
                                    <template v-else>
                                        <li v-for="n in 5" class="page-number" :class="paginator.current == n ? 'active' : ''" @click="setPage(n)"><a href="javascript:void(0)" v-text="n"></a>
                                        </li>
                                    </template>
                                </template>
                                <template v-else>
                                    <li v-for="n in paginator.total_pages" class="page-number" :class="paginator.current == n ? 'active' : ''"  @click="setPage(n)"><a href="javascript:void(0)" v-text="n"></a>
                                    </li>
                                </template>
                                <li v-if="paginator.total_pages > 5" class="page-last-separator disabled"><a href="javascript:void(0)">...</a>
                                </li>
                                <li v-if="paginator.total_pages > 5" class="page-last"  @click="setPage(paginator.total_pages)"><a href="javascript:void(0)" v-text="paginator.total_pages"></a>
                                </li>
                                <li class="page-next" @click="nextPage" :class="paginator.current == paginator.total_pages ? 'disabled' : ''"><a href="javascript:void(0)">›</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>

<script>
    var c_idx;
    var app_ext_table = new Vue({
        el: '#app_ext_table',
        data: {
            url:'{{ url('work/formdata/reportlist/'~form_id~'/json') }}',
            page_id:'999',
            list_msg: '正在努力的加载数据中，请稍后...',
            empty_msg: '',
            param: {
                conditions: [],
                offset : 0,
                limit: 10
            },
            list: [],
            column_data: [],
            sum_data:{},
            btn_list:[],
            active_idx: 0,
            paginator:{
                items: null,
                first: 0,
                before:0,
                current: 1,
                last: 0,
                next: 0,
                total_pages: 0,
                total_items: 0,
                limit: 10
            },
            search_show : true
        },
        mounted:function (){
           setTimeout(()=>{
               this.getConditionData();
               this.search();
           },200);
        },
        methods: {
            getConditionData:function (){
                $.post('{{ url('common/table/condition/') }}'+this.page_id, {}, (rs)=>{
                    this.param.conditions = rs;
                    this.$nextTick(function() {
                        setTableHeight();
                    });
                })
            },
            appSearch(){
                this.search();
            },
            search: function(offset) {
                this.list_msg = '正在努力的加载数据中，请稍后...';
                let param = {};
                param.conditions = encodeURIComponent(JSON.stringify(this.param.conditions));
                param.offset = offset || 0;
                param.limit = this.param.limit;
                app_ext_table.list = [];
                $.get(this.url, param, (rs) => {
                    app_ext_table.list_msg = '';
                    if (rs.data.data_rows.length == 0) {
                        app_ext_table.empty_msg = '没有找到匹配的记录';
                    } else {
                        app_ext_table.empty_msg = '';
                    }
                    app_ext_table.list = rs.data.data_rows;
                    app_ext_table.column_data = rs.data.data_header;
                    app_ext_table.sum_data = rs.data.sum_data;
                    app_ext_table.paginator = rs.paginator;
                    app_ext_table.$nextTick(function() {
                        $(".dropdown-toggle").click(function() {
                            app_ext_table.active_idx = $(this).attr('data-idx');
                        });

                        setTableHeight();

                        $('.dtpicker-search').datetimepicker(
                            {
                                language: "zh-CN",
                                startView: 2,
                                minView: 2,
                                todayBtn: false,
                                autoclose: true,
                                format: 'yyyy-mm-dd'
                            }
                        ).on('changeDate', function(ev) {
                            let names = $(this).attr('name').split('!');
                            app_ext_table.param.conditions[names[1]][names[0]] = $(this).val();
                        });
                    });
                });
            },
            reset: function() {
                this.clearConditions();
                this.param.offset = 0;
                this.$nextTick(function() {
                    $(".bs-select").selectpicker('refresh');
                    $(".bs-select-page").selectpicker('refresh');
                });
                this.search();
            },
            dataView:function (type,row){
            },
            updateLimit: function(val) {
                this.param.limit = val;
                this.param.offset = 0;
                this.search();
            },
            setPage: function(page_no) {
                this.paginator.current = page_no;
                let offset = (this.paginator.current - 1) * this.param.limit
                this.search(offset);
            },
            prevPage: function() {
                if (this.paginator.current == 1) {
                    return;
                }
                let offset = (this.paginator.current - 2) * this.param.limit
                this.search(offset);
            },
            nextPage: function() {
                if (this.paginator.current == this.paginator.total_pages) {
                    return;
                }
                let offset = this.paginator.current * this.param.limit
                this.search(offset);
            },
            getParams: function() {
                let condition = '?conditions=' + encodeURIComponent(JSON.stringify(this.param.conditions));
                return condition;
            },
            excel: function () {
                window.open("{{ url('work/formdata/export/'~form_id) }}" + this.getParams());
            },
            clearConditions: function() {
                for (let i = 0; i < this.param.conditions.length; i++) {
                    this.param.conditions[i].val = '';
                    this.param.conditions[i].val_end = '';
                }
            },
            openCondition: function(item) {
                let html = '<div style="display: flex;align-items: center;justify-content: center;padding: 20px;">'
                    +'<div><span id="c_title">@title@</span>：</div>'
                    +'<div @condition_style@ style="margin: 0 15px;">'
                    +'<select class="bs-select-s form-control" name="condition_type">'
                    +'<option value="=" selected>等于</option>'
                    +'<option value="＜">小于</option>'
                    +'<option value="＜=">小于等于</option>'
                    +'<option value=">">大于</option>'
                    +'<option value=">=">大于等于</option>'
                    +'<option value="like">包含</option>'
                    +'</select>'
                    +'</div>'
                    +'<div @condition_style@>'
                    +'<input type="text" id="c_input" class="form-control" name="condition_val" value="">'
                    +'</div>'
                    +'<div @date_style@>'
                    +'<input type="text" id="c_input_date_begin" class="date dtpicker form-control" name="date_begin" value="" style="width: 150px;">'
                    +'<span class="input-group-addon" style="width: 40px;line-height: 20px;height: 34px">至</span>'
                    +'<input type="text" id="c_input_date_end" class="date dtpicker form-control" name="date_end" value="" style="width: 150px;">'
                    +'</div>'
                    +'<input type="hidden" id="hidden_item_id" value="@item_id@">'
                    +'</div>'
                    +'<div style="text-align: center;margin-top: 30px;">'
                    +'<button type="button" class="btn green" onclick="addCondition()" style="margin-right: 20px;width: 120px;">确认</button>'
                    +'<button type="button" class="btn default" onclick="closeCondition()" style="width: 120px;">取消</button>';
                html = html.replace('@title@', item.name).replace('@item_id@', item.id);
                if (item.type == 6) {
                    html = html.replace('@input_style@', 'style="display: none;"')
                        .replaceAll('@condition_style@', 'style="display: none;"')
                        .replace('@date_style@', 'style="display: flex;flex-direction: row"');
                } else {
                    html = html.replace('@input_style@', '').replace('@date_style@', 'style="display: none;"');
                }
                c_idx = layer.open({
                    type: 1,
                    title: '新增检索条件',
                    area: ['50em', '25em'],
                    zIndex: 1000,
                    content: html,
                    success: function () {
                        $('.dtpicker').datetimepicker(
                            {
                                language: "zh-CN",
                                startView: 2,
                                minView: 2,
                                todayBtn: false,
                                autoclose: true,
                                format: 'yyyy-mm-dd'
                            }
                        );

                        $('.bs-select-s').selectpicker({
                            iconBase: 'fa',
                            tickIcon: 'fa-check',
                            noneSelectedText: '请选择'
                        });
                    }
                });
            },
            removeCondition(idx) {
                this.param.conditions.splice(idx, 1);
                saveCondition();
            },
            dataView(row){
                top.window.layer_result = '';
                top.layer.open({
                    title: '查看详情',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/view/') }}' + row.uid + '/1',
                    end: function() {
                        if (top.window.layer_result == 'ok') {
                            toastr.success('操作完毕');
                            $table.bootstrapTable('refresh');
                        }
                    }
                });
            }
        }
    });

    function saveCondition() {
        showSpin();
        $.post('{{ url('common/table/savecdt/') }}'+app_ext_table.page_id, { condition: encodeURI(JSON.stringify(app_ext_table.param.conditions)) }, function(rs) {
            closeSpin();
            if (rs.status == 'ok') {
                app_ext_table.search();
                toastr.success('操作完毕');
            } else {
                toastr.error('保存失败！' + rs.message);
            }
        });
    }

    function addCondition() {
        let $ct = $("#c_title");
        let item_id = $("#hidden_item_id").val();
        let $cs_opt = $(".bs-select-s").find("option:selected");
        let item = null;
        for(let column_item of app_ext_table.column_data){
            if (column_item.id == item_id){
                item = JSON.parse(JSON.stringify(column_item));
                break;
            }
        }
        if (item != null){
            item.search_type = $cs_opt.val();
            item.search_type_name = $cs_opt.text();
            item.val_end = '';
            if (item.type == 6) {
                item.val = $("#c_input_date_begin").val();
                item.val_end = $("#c_input_date_end").val();
            } else {
                item.val = $("#c_input").val();
            }
            let search_item;
            let edit_flag = 0;
            for (let i = 0; i < app_ext_table.param.conditions.length; i++) {
                search_item = app_ext_table.param.conditions[i];
                if (search_item.id == item.id) {
                    app_ext_table.param.conditions.splice(i, 1, item);
                    edit_flag = 1;
                    break;
                }
            }
            if (edit_flag === 0) {
                app_ext_table.param.conditions.push(item);
            }
            app_ext_table.$nextTick(function() {
                $('.dtpicker-search').datetimepicker(
                    {
                        language: "zh-CN",
                        startView: 2,
                        minView: 2,
                        todayBtn: false,
                        autoclose: true,
                        format: 'yyyy-mm-dd'
                    }
                ).on('changeDate', function(ev) {
                    let names = $(this).attr('name').split('!');
                    app_ext_table.param.conditions[names[1]][names[0]] = $(this).val();
                });
            });
            saveCondition();
            closeCondition();
        }
    }

    function closeCondition() {
        layer.close(c_idx);
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    });

    function setTableHeight() {
        let h = $(window).height() - 35 - $(".search-bar").outerHeight(true)
            - $(".search-bar-table").outerHeight(true) - $(".sum-bar").outerHeight(true)
            - $(".zh-table-footer").outerHeight(true);
        $(".zh-table-box-content").height(h);
    }

    $(function() {
        setTableHeight();
    });
</script>
<style>
    .zh-table-box table tbody tr > :nth-child(1) {
        position: sticky;
        left: 0;
        border-left: 0 !important;
        z-index: 1;
    }

    .t-first {
        position: sticky;
        left: 0;
        border-left: 0 !important;
        z-index: 2;
        background-color: #3598DC !important;
    }

    .zh-table-box table tr .td-last {
        position: sticky;
        right: 0;
    }

    .t-last {
        background-color: #3598DC !important;
        border-right: 0 !important;
    }

    .td-active {
        z-index: 1;
    }

    .zh-table-box table tbody tr > :nth-child(1),
    .zh-table-box table tbody tr > .td-last {
        border: 0;
        padding: 0;
    }

    .zh-table-box table tbody tr > :nth-child(1) > div,
    .zh-table-box table tbody tr > .td-last > div {
        padding: 8px;
        border-right: 1px solid #e7ecf1;
        border-bottom: 1px solid #e7ecf1;
        line-height: 36px;
        height: 54px;
    }

    .zh-table-box table tbody tr > .td-last > div {
        border-left: 1px solid #e7ecf1;
    }
</style>