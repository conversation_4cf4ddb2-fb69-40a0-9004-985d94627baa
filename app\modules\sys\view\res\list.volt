<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}

<div id="app">
    <section class="page-content">
        <div class="form-actions">
            <button class="btn btn blue" onclick="create('module')">新建模块</button>
            <button class="btn btn blue" onclick="create('controller')">新建控制器</button>
            <button class="btn btn blue" onclick="create('action')">新建动作</button>
        </div>

        <div style="margin-top: 30px;" class="form-actions" v-show="uid!=''">
            <button class="btn btn blue" onclick="edit()"><i class="fa fa-edit"></i> 编辑</button>
            <button class="btn btn red" onclick="del()"><i class="fa fa-times"></i> 删除</button>
        </div>

        <div class="box">
            <div class="box-header with-border">
                <h3 class="box-title">权限动作管理</h3>
            </div>
            <div class="box-body">
                <div id="tree"></div>
            </div>
            <div class="box-footer">
                <p class="text-orange"><i class="fa fa-info-circle"></i> 如果要追加分类或者动作，请先点击选择对应的模块和分类！</p>
            </div>
        </div>
    </section>

</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{
            mid:'', cid:'', id:'', type:'', uid:''
        },
        methods:{
        }
    });

    var $tree = $('#tree');

    $(document).ready(function () {
        $tree.jstree({
            'plugins':['wholerow'],
            'core': {'data': {{ jsonTree }}}
        });

        $tree.on('select_node.jstree', function (e, data) {
            app.uid = data.node.original.uid;
            app.type = data.node.original.type;
            app.id = data.node.id;

            if(app.type=='module') {
                app.mid = app.id;
                app.cid = '';
            }
            else if(app.type=='action') {
                app.cid = data.node.parents[0];
                app.mid = data.node.parents[1];
            }
            else if(app.type=='controller') {
                app.cid = data.node.id;
                app.mid = data.node.parent;
            }
        });
    });

    function refreshTree() {
        $.post('{{ url('sys/res/list/json') }}', function (rs) {
            if(rs.status=='ok'){
                $tree.jstree(true).settings.core.data = JSON.parse(rs.data);
                $tree.jstree('refresh');
            }
        })
    }

    function create(type) {
        var id='';
        if(type=='module')
            id = 'module';
        else if(type=='controller')
            id = app.mid;
        else if(type=='action')
            id = app.cid;

        if(id==''){
            layer.alert('请点击选择功能节点');
            return;
        }

        window.layer_result='';
        layer.open({
            title:'新建',
            type: 2,
            area: makeArea('40em', '80%'),
            content: '{{ url('sys/res/create') }}/'+type + '?id=' + id,
            end:function(){
                if(window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    refreshTree();
                }
            }
        });
    }

    function edit() {
        window.layer_result='';
        layer.open({
            title:'编辑',
            type: 2,
            area: makeArea('40em', '90%'),
            content: '{{ url('sys/res/edit/') }}' + app.id,
            end:function(){
                if(window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    refreshTree();
                }
            }
        });
    }

    function del(){
        var dlg = layer.confirm('确认删除吗?', function(){
            layer.close(dlg);
            showSpin();
            $.post("{{ url('sys/res/delete') }}", app.$data, function (rs) {
                closeSpin();
                if(rs.status=='ok'){
                    toastr.success('操作成功!');
                    refreshTree();
                }
                else {
                    toastr.error('操作失败!'+rs.message);
                }
            })
        });
    }
</script>
