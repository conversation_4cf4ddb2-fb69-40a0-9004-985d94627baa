<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}

<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="form-group">
                        <label>物资类型<span class="required"> * </span></label>
                        <div id="tree"></div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="confirm"><i class="fa fa-check"></i> 确定</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{
            uid:'', pid:'',idx:[]
        },
        methods: {
            confirm: function() {
                top.window.layer_result = this.idx;
                top.layer.close(top.layer.getFrameIndex(window.name));
            }
        },
        watch: {
            uid: function(val) {
                showSpin();
                $.post('{{ url('purchase/goodstype/searchbytype/') }}' + val, function(rs) {
                    closeSpin();
                    if(rs != null){
                        app.idx = rs;
                    }
                    else {
                        toastr.error('选择失败!');
                    }
                });
            }
        },
    });

    $(function () {
        var $tree = $('#tree');
        $tree.jstree({
            "core": {"data": {{ jsonTree | json_encode }}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            app.uid = data.node.original.uid;
            app.pid = data.node.parent;
        });
    });
</script>
