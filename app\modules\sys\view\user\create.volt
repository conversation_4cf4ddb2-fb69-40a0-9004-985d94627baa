{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>
                                    员工号
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="empno" class="form-control" v-model="empno" required/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    姓名<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input name="real_name" type="text" class="form-control" minlength="2" v-model="real_name" required/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-8 control-label">
                                    姓名拼音首字母缩写<span class="required"> * </span>
                                </label>
                                <div class="col-sm-4">
                                    <input name="name_py" type="text" class="form-control" minlength="2" v-model="name_py" required/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    登录名称<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input name="login_name" type="text" class="form-control" minlength="2" v-model="login_name" required/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    登录密码<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input name="password" type="password" class="form-control" minlength="2" v-model="password" required/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    性别<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <select name="gender" class="bs-select form-control" v-model="gender" required>
                                        <option value="">请选择</option>
                                        <option value="0">男</option>
                                        <option value="1">女</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">手机</label>
                                <div class="col-sm-8">
                                    <input name="mobile" type="text" class="form-control" autocomplete="false" v-model="mobile"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    岗位<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <select name="role_id" class="bs-select form-control" v-model="role_id" required>
                                        <option value="">请选择</option>
                                        {% for row in roles %}
                                            <option value="{{ row.id }}">{{ row.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">邮箱</label>
                                <div class="col-sm-8">
                                    <input name="email" type="email" class="form-control" autocomplete="false" v-model="email"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">地址</label>
                                <div class="col-sm-8">
                                    <input name="address" type="text" class="form-control" v-model="address"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">账号状态</label>
                                <div class="col-sm-8">
                                    <select name="account_status" class="bs-select form-control" v-model="account_status" required>
                                        <option value="0">启用</option>
                                        <option value="1">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">数据权限</label>
                                <div class="col-sm-8">
                                    <select name="type" class="bs-select form-control" v-model="type" required>
                                        <option value="0">组织数据权限</option>
                                        <option value="1">个人数据权限</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    组织<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="group" v-model="group_name" required readonly>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default btn-flat" @click="selectGroup"><i class="fa fa-search"></i></button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    工时成本
                                </label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input  type="number" class="form-control" name="cost" v-model="cost" placeholder="工时成本" number="true"  maxlength="10">
                                        <span class="input-group-addon">元/小时</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {# DELETE RC-LQ-968 20240220 BY WXX #}
{#                    <div class="row">#}
{#                        <div class="col-sm-12">#}
{#                            <div class="form-group">#}
{#                                <label class="col-sm-2 control-label">#}
{#                                    组织数据权限#}
{#                                </label>#}
{#                                <div class="col-sm-10">#}
{#                                    <div class="input-group">#}
{#                                        <textarea rows="2" class="form-control" maxlength="1000" name="auth_data_names" v-model="auth_data_names" readonly></textarea>#}
{#                                        <span class="input-group-btn">#}
{#                                            <button type="button" class="btn btn-default btn-flat" @click="selectAuth"><i class="fa fa-search"></i></button>#}
{#                                        </span>#}
{#                                    </div>#}
{#                                </div>#}
{#                            </div>#}
{#                        </div>#}
{#                    </div>#}
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonUser }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if user is not empty %}
                    var url= '{{ url('sys/user/edit/'~user.uid) }}';
                {% else %}
                    var url= '{{ url('sys/user/create') }}';
                {% endif %}

                if (app.role_id == 2){
                    if (app.cost == '' || app.cost == null){
                        toastr.error('请输入工时成本！');
                        return;
                    }
                }

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status == 'ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            selectGroup: function() {
                top.window.group_layer_result = null;
                top.layer.open({
                    title: '选择组织',
                    type: 2,
                    area: ['35em', '60%'],
                    content: '{{ url('sys/group/sel') }}',
                    end: function() {
                        if (top.window.group_layer_result != null) {
                            var rs = top.window.group_layer_result;
                            app.group_name = rs.name;
                            app.group_uid = rs.uid;
                        }
                    }
                });
            },
             // DELETE RC-LQ-968 20240220 BY WXX
            // selectAuth:function () {
            //     top.window.layer_result = null;
            //     top.layer.open({
            //         title: '数据权限',
            //         type: 2,
            //         area: ['50em', '80%'],
            {#        content: '{{ url('sys/user/sel-multiple/') }}'+app.auth_data_ids,#}
            //         end: function() {
            //             if (top.window.layer_result == 'ok') {
            //                 app.auth_data_names = top.window.result_names;
            //                 app.auth_data_ids = top.window.result_ids;
            //             }
            //         }
            //     });
            // }
        }
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check'
    });
</script>
