{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}

<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">到货信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 72vh;overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                <!-- <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">业务类型</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="business_type_name" readonly>
                                        </div>
                                    </div>
                                </div> -->
                                <!-- <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">采购类型</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="purchase_type_name" readonly>
                                        </div>
                                    </div>
                                </div> -->
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">单据号</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="receipt_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">到货日期</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="receipt_date" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">供应商</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="supplier_name" readonly>
                                        </div>
                                    </div>
                                </div>
        
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">部门</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="department_name" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">币种</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="currency" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">汇率</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="exchange_rate" readonly>
                                        </div>
                                    </div>
                                </div>
                               {{ partial('view') }}
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">备注</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control readonly-field" v-model="remarks" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button v-if="viewType == 'approval'" type="button" class="btn red" @click="showPopup('reject')" ><i class="fa fa-reply"></i> 驳回</button>
                            <button v-if="viewType == 'approval'" type="button" class="btn green" @click="showPopup('pass')" ><i class="fa fa-share"></i> 通过</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">到货明细</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>物料编码</th>
                                    <th>物料名称</th>
                                    <th>规格型号</th>
                                    <th>库存计量数量(入库)</th>
                                    <th>采购计量数量(入库)</th>
                                    <th>换算率</th>
                                    <th>税率(%)</th>
                                    <th>未税单价</th>
                                    <th>含税单价</th>
                                    <th>未税金额</th>
                                    <th>税额</th>
                                    <th>含税金额</th>
                                    <th>检验</th>
                                    <th>单号</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="apply_list.length == 0">
                                    <td colspan="13" style="text-align: center;">没有数据</td>
                                </tr>
                                <tr v-else v-for="apply, apply_idx in apply_list">
                                    <td v-text="apply.goods_code"></td>
                                    <td v-text="apply.goods_name"></td>
                                    <td v-text="apply.goods_model"></td>
                                    <td v-text="apply.quantity + ' (' + apply.goods_deputy_unit + ')'"></td>
                                    <td v-text="apply.purchase_quantity + ' (' + apply.goods_unit + ')'"></td>
                                    <td v-text="apply.unit_conversion_rate"></td>
                                    <td v-text="apply.tax_rate * 100"></td>
                                    <td v-text="apply.price"></td>
                                    <td v-text="apply.price_hs"></td>
                                    <td v-text="parseFloat(apply.total_money || 0).toFixed(2)"></td>
                                    <td v-text="parseFloat(apply.tax_money || 0).toFixed(2)"></td>
                                    <td v-text="parseFloat(apply.total_money_hs || 0).toFixed(2)"></td>
                                    <td v-text="apply.check_status"></td>
                                    <td>
                                        <span v-if="apply.apply_code" v-text="apply.apply_code"></span>
                                        <span v-else v-text="apply.order_code"></span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="portlet light bordered" v-if="comment">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-bubble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">审核意见</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; color: #333;">
                        ${ comment }
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 添加弹出窗口的HTML结构 -->
    <div id="custom-popup" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title">${model_title}</h4>
                </div>
                <div class="modal-body">
                    <label><span v-if="handleType == 'reject'" class="required">*</span>评论</label>
                    <div>
                        <textarea class="form-control" name="approval_comment" v-model="approval_comment" rows="3" maxlength="200" style="resize: none"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn green" @click="doPass"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn btn-default" @click="handleClose">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var viewType = '{{ view_type }}';
    var uid = '{{ uid }}';
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonPurchaseReceipt }},
            viewType: viewType,
            model_title: '通过',
            approval_comment: '',
            handleType: ''
        },
        methods: {
            doPass: function() {
                if (this.handleType == 'reject' && !this.approval_comment ) {
                    toastr.error('必须输入评论内容！');
                    return ;
                }

                if (this.handleType == 'reject')  {
                    var url = '{{ url('purchase/receipt/reject/') }}';
                } else {
                    var url = '{{ url('purchase/receipt/approval/') }}';
                }
                
                showSpin();
                $.post(url, {
                    uid : uid,
                    comment:app.approval_comment,
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B')
                }, function (rs) {
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                }).fail(function() {
                    toastr.error('未知的异常');
                }).always(function() {
                    closeSpin(null); // 无论如何都关闭loading
                });

            },
            showPopup: function(type) {
                this.handleType = type
                $('#custom-popup').modal('show');
            },
            handleClose: function() {
              $('#custom-popup').modal('hide');
            },
        }
        
    });
</script>

<style>
.readonly-field {
    background-color: #f5f5f5;
    color: #333333;
    cursor: default;
}

/* Modal 样式美化 */
#custom-popup .modal-dialog {
    margin: 50px auto;
    max-width: 600px;
}

#custom-popup .modal-content {
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: none;
}

#custom-popup .modal-header {
    background: #2c3e50;
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 15px 20px;
    border-bottom: none;
}

#custom-popup .modal-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

#custom-popup .modal-header .close {
    color: white;
    opacity: 0.8;
    font-size: 28px;
    font-weight: 300;
    text-shadow: none;
}

#custom-popup .modal-header .close:hover {
    opacity: 1;
    color: #f8f9fa;
}

#custom-popup .modal-body {
    padding: 25px;
    background-color: #f8f9fa;
}

#custom-popup .modal-body textarea {
    border: 2px solid #ced4da;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background-color: #ffffff;
}

#custom-popup .modal-body textarea:focus {
    border-color: #2c3e50;
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
    outline: none;
}

#custom-popup .modal-footer {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    padding: 20px 25px;
    border-radius: 0 0 8px 8px;
}

#custom-popup .modal-footer .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

#custom-popup .modal-footer .btn.green {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
    border: none;
    color: white;
}

#custom-popup .modal-footer .btn.green:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(54, 209, 220, 0.4);
}

#custom-popup .modal-footer .btn-default {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

#custom-popup .modal-footer .btn-default:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-1px);
}

/* Modal 背景遮罩美化 */
#custom-popup.modal {
    background-color: transparent;
}

#custom-popup .modal-backdrop {
    background-color: transparent;
}
</style>