<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Util\ModelUtil;
use Envsan\Common\Model\BaseModel;

class Owner extends BaseModel
{
    const STATUS_NORMAL = 0;
    const STATUS_DISABLED = 1;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $domain;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $contact;

    /**
     *
     * @var string
     * @Column(type="string", length=30, nullable=false)
     */
    public $company;

    /**
     *
     * @var string
     * @Column(type="string", length=30, nullable=false)
     */
    public $short_name;

    /**
     *
     * @var string
     * @Column(type="string", length=30, nullable=true)
     */
    public $base_url;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $address;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $lng;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $lat;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $mobile;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $tel;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $start_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $end_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $skip_review;

    /**
     *
     * @var string
     * @Column(type="string", length=18, nullable=true)
     */
    public $wx_appid;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $wx_secret;

    /**
     *
     * @var string
     * @Column(type="string", length=18, nullable=true)
     */
    public $wx_gzh_appid;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $wx_gzh_secret;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $wx_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;


    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_owner';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return Owner[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return Owner
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
