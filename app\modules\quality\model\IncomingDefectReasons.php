<?php

namespace Envsan\Modules\Quality\Model;

use Envsan\Common\Model\BaseModel;

class IncomingDefectReasons extends BaseModel
{
    public $id;
    public $handling_id;
    public $reason_code;
    public $reason_name;
    public $quantity;
    public $created_at;

    public function initialize()
    {
        parent::initialize();
        $this->setSource('incoming_defect_reasons');
        
        // 关联主表
        $this->belongsTo('handling_id', 'Envsan\Modules\Quality\Model\IncomingDefectHandling', 'id', [
            'alias' => 'handling'
        ]);
    }

    /**
     * 获取常见不良原因选项
     */
    public static function getCommonReasonOptions()
    {
        return [
            'dimension_error' => '尺寸偏差',
            'appearance_defect' => '外观缺陷',
            'material_issue' => '材质问题',
            'function_defect' => '功能缺陷',
            'packaging_damage' => '包装破损',
            'label_error' => '标签错误',
            'color_difference' => '色差',
            'surface_scratch' => '表面划伤',
            'deformation' => '变形',
            'other' => '其他'
        ];
    }

    /**
     * 获取原因名称
     */
    public function getReasonNameText()
    {
        $options = self::getCommonReasonOptions();
        return isset($options[$this->reason_code]) ? $options[$this->reason_code] : $this->reason_name;
    }
} 