<?php

namespace Envsan\Modules\Quality\Model;

use Envsan\Common\Model\BaseModel;

class IncomingDefectHandling extends BaseModel
{
    public $id;
    public $inspection_type;
    public $document_no;
    public $handling_date;
    public $specification;
    public $material_name;
    public $unit;
    public $arrival_quantity;
    public $inspection_no;
    public $supplier_id;
    public $supplier_name;
    public $defect_reasons;
    public $handling_method;
    public $defect_quantity;
    public $qualified_quantity;
    public $final_quantity;
    public $status;
    public $remark;
    public $created_by;
    public $created_at;
    public $updated_by;
    public $updated_at;
    public $owner_id;

    public function initialize()
    {
        parent::initialize();
        $this->setSource('incoming_defect_handling');
    }

    /**
     * 获取检验类型选项
     */
    public static function getInspectionTypeOptions()
    {
        return [
            'sampling' => '抽检',
            'full' => '全检'
        ];
    }

    /**
     * 获取处理方式选项
     */
    public static function getHandlingMethodOptions()
    {
        return [
            'all_accept' => '全部让步接收',
            'partial_return' => '部分退货(其余接收)',
            'partial_scrap' => '部分报废(其余接收)',
            'all_reject' => '全部拒收',
            'sorting' => '选别部分(其余退货)'
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            'pending' => '待处理',
            'processing' => '处理中',
            'completed' => '已完成'
        ];
    }

    /**
     * 获取状态文本
     */
    public function getStatusText()
    {
        $options = self::getStatusOptions();
        return isset($options[$this->status]) ? $options[$this->status] : $this->status;
    }

    /**
     * 获取检验类型文本
     */
    public function getInspectionTypeText()
    {
        $options = self::getInspectionTypeOptions();
        return isset($options[$this->inspection_type]) ? $options[$this->inspection_type] : $this->inspection_type;
    }

    /**
     * 获取处理方式文本
     */
    public function getHandlingMethodText()
    {
        $options = self::getHandlingMethodOptions();
        return isset($options[$this->handling_method]) ? $options[$this->handling_method] : $this->handling_method;
    }

    /**
     * 解析不良品原因JSON
     */
    public function getDefectReasonsArray()
    {
        if (empty($this->defect_reasons)) {
            return [];
        }
        return json_decode($this->defect_reasons, true) ?: [];
    }

    /**
     * 设置不良品原因JSON
     */
    public function setDefectReasonsArray($reasons)
    {
        $this->defect_reasons = json_encode($reasons, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 验证数量平衡
     */
    public function validateQuantityBalance()
    {
        return $this->arrival_quantity == ($this->qualified_quantity + $this->defect_quantity);
    }

    /**
     * 计算最终入库数量
     */
    public function calculateFinalQuantity()
    {
        switch ($this->handling_method) {
            case 'all_accept':
                // 全部让步接收：合格品 + 不良品
                $this->final_quantity = $this->qualified_quantity + $this->defect_quantity;
                break;
            case 'partial_return':
            case 'partial_scrap':
                // 部分退货/报废：合格品 + 让步接收的不良品
                $this->final_quantity = $this->qualified_quantity + $this->accepted_defect_quantity;
                break;
            case 'all_reject':
                // 全部拒收：0
                $this->final_quantity = 0;
                break;
            case 'sorting':
                // 选别：合格品 + 选别后的合格品
                $this->final_quantity = $this->qualified_quantity + $this->sorted_quantity;
                break;
            default:
                $this->final_quantity = $this->qualified_quantity;
        }
    }

    /**
     * 获取不良品原因明细
     */
    public function getDefectReasonsDetail()
    {
        return IncomingDefectReasons::find([
            'conditions' => 'handling_id = :handling_id:',
            'bind' => ['handling_id' => $this->id]
        ]);
    }
} 