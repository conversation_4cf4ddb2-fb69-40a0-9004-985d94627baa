<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">订单</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="order_code" v-model="order_code" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">客户</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="customer_name" v-model="customer_name" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">收款日期</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="invoice_date" v-model="invoice_date" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">收款金额</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="invoice_money" v-model="invoice_money" readonly>
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ partial('view') }}
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件</label>
                                <div class="col-sm-10" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item, index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">备注</label>
                                <div class="col-sm-10">
                                    <textarea style="resize: none;" class="form-control" v-model="remarks" maxlength="200" rows="3" readonly></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonTradeInvoice }},
    });
</script>