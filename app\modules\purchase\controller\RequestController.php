<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Service\NoticeService;
use Envsan\Modules\Purchase\Model\PurchaseRequest;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\PurchaseRequestService;
use Envsan\Modules\Purchase\Util\Constant;

/**
 * @name('采购请求')
 */
class RequestController extends SuperController
{
    private $page_id = 44;
    private $search_page_id = 45;


    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseRequestService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setDetail($page->rows->toArray());
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @acl({'link':'purchase:request:list'})
     */
    public function createAction()
    {
        $s = new PurchaseRequestService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }

        $gs = new GoodsTypeService();
        $table = new TableService();
        $fs = new FileService();

        $jrow = (new PurchaseRequest())->toArray();
        $jrow['apply_date'] = DateUtil::today();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['files'] = [];
        $jrow['detail_data'] = [];
        $jrow['productList'] = [];
        $ns = new NoticeService();
        $this->view->jsonPurchaseRequest = json_encode($jrow);
        $this->view->purchaseTypes = Constant::$normal_purchase_types;
        $this->view->noticeList = $ns->getAllActiveNotice();

        $this->view->extDataName = 'form.ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonTree = $gs->selectTree();
    }


    /**
     * @skipacl
     * 物料选择页面
     */
    public function addgoodsAction($type = 'page', $ids = '')
    {
        $this->view->ids = $ids;
        $gs = new GoodsTypeService();
        $this->view->jsonTree = $gs->selectTree();
    }


    /**
     * @skipacl
     * 物料选择页面
     */
    public function addnoticeAction($type = '', $notice_id, $product_id, $ids = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseRequestService();
            $builder = $s->getPurchaseGoods($notice_id, $product_id, $ids);
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->ids = $ids;
        $this->view->notice_id = $notice_id;
        $this->view->product_id = $product_id;
    }

    /**
     * @acl({'link':'purchase:request:list'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseRequestService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $table = new TableService();
        $fs = new FileService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);

        $jrow = $row->toArray();
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $jrow['productList'] = $s->getProductList($jrow['notice_id']);
        $ns = new NoticeService();
        $this->view->jsonPurchaseRequest = json_encode($jrow);
        $this->view->purchaseTypes = Constant::$normal_purchase_types;
        $this->view->noticeList = $ns->getAllActiveNotice();

        $this->view->uid = $uid;
        $this->view->extDataName = 'form.ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('request/create');
    }

    /**
     * @acl({'link':'purchase:request:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseRequestService();
            $ret = new JsonData();
            $ret->handleResult($s->deleteByUid($this->request->getPost('uid', 'tstring')));
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new PurchaseRequestService();
        $row = $s->getRequestData($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $fs = new FileService();

        $jrow = $row->toArray();
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $this->view->jsonPurchaseRequest = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseRequestService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseRequestService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setSearchDetail($page->rows->toArray());
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @skipacl
     */
    public function searchexportAction()
    {
        $s = new PurchaseRequestService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function changenoticeAction()
    {
        $rs = new PurchaseRequestService();
        if ($this->request->isPost()) {
            $notice_id = $this->request->getPost('notice_id', 'int');
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->getProductList($notice_id));
            return json_encode($ret);
        }
    }
}