<script>
    /**
     * 通用AJAX请求函数
     * @param {string} url - 请求URL
     * @param {object} data - 请求数据(已处理好的数据)
     * @param {function} successCallback - 成功时的回调函数
     * @param {function} failCallback - 失败时的回调函数(可选)
     * @param {function} alwaysCallback - 无论成功失败都会执行的回调函数(可选)
     */
    function commonAjaxRequest(url, data, successCallback, failCallback, alwaysCallback) {
        // 显示加载动画
        showSpin();
        
        $.post(url, data)
            .done(function(rs) {
                if (rs.status === 'ok') {
                    // 调用成功回调
                    if (typeof successCallback === 'function') {
                        successCallback(rs);
                    }
                } else {
                    // 显示错误信息
                    toastr.error('操作失败！' + (rs.message || ''));
                    
                    // 调用失败回调(如果有)
                    if (typeof failCallback === 'function') {
                        failCallback(rs);
                    }
                }
            })
            .fail(function(xhr, textStatus, errorThrown) {
                // 显示网络错误
                toastr.error('未知的异常');
                
                // 调用失败回调(如果有)
                if (typeof failCallback === 'function') {
                    failCallback({ status: 'error', message: textStatus, xhr: xhr });
                }
            })
            .always(function(data) {
                // 关闭加载动画
                closeSpin(null);
                
                // 调用always回调(如果有)
                if (typeof alwaysCallback === 'function') {
                    alwaysCallback(data);
                }
            });
    }

    // 安全转换函数
    function safeNumber(value, defaultValue = 0) {
        if (value === '' || value === null || value === undefined) {
            return defaultValue;
        }
        const num = Number(value);
        return isNaN(num) ? defaultValue : num;
    }
</script>