{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="table-search-bar row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">采购类型</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="purchase_type_name" v-model="purchase_type_name" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申请单号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="apply_code" v-model="apply_code" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申请日期</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="apply_date" v-model="apply_date" readonly>
                                </div>
                            </div>
                        </div>
                        <template v-if="purchase_type == 1">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">生产批次</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="notice_code" v-model="notice_code" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">产品名称</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="product_name" v-model="product_name" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">规格型号</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="product_code" v-model="product_code" readonly>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">项目号</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="order_code" v-model="order_code" readonly>
                                    </div>
                                </div>
                            </div> -->
                        </template>
                        {{ partial('view') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea class="form-control" name="remark" v-model="remarks" rows="3" style="resize: none;" readonly></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="zh-table-box">
                <div class="zh-table-box-content">
                    <table class="table table-bordered table-big">
                        <thead>
                        <tr>
                            <th>编码</th>
                            <th>名称</th>
                            <th>规格型号</th>
                            <th style="width: 190px;">库存计量数量</th>
                            <th style="width: 190px;">采购计量数量</th>

                        </tr>
                        </thead>
                        <tbody>
                        <tr v-if="detail_data.length == 0">
                            <td colspan="5" style="text-align: center;">没有数据</td>
                        </tr>
                        <tr v-for="row, index in detail_data">
                            <td v-text="row.code"></td>
                            <td v-text="row.name"></td>
                            <td v-text="row.model"></td>
                            <td>
                                <span v-text="row.quantity"></span>
                                <span v-if="row.deputy_unit" v-text="row.deputy_unit"></span>
                            </td>
                            <td>
                                <span v-text="(row.purchase_quantity  || 0)"></span>
                                <span v-if="row.unit" v-text="row.unit"></span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonPurchaseRequest }},
    });

    function initSize() {
        $(".zh-table-box-content").height($(window).height() - 40 - 27 - $("#form").outerHeight(true));
    }

    $(function () {
        initSize();
    });
</script>