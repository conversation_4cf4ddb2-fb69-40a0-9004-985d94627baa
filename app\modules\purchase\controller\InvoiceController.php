<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInvoice;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\PurchaseInvoiceService;


/**
 * @name('开票管理')
 */
class InvoiceController extends SuperController
{
    private $page_id = 36;
    private $search_page_id = 37;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseInvoiceService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new PurchaseInvoiceService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $oss_util = new FileService();
        $common = new CommonService();

        $jrow = (new PurchaseInvoice())->toArray();
        $jrow['invoice_date'] = date('Y-m-d');
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['files'] = [];
        $jrow['goods'] = '';
        $jrow['instock_list'] = [];
        $jrow['detail_list'] = [];
        $this->view->jsonPurchaseInvoice = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->order_list = $common->getOrderList();
    }

    /**
     * @acl({'link':'purchase:invoice:create'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseInvoiceService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $oss_util = new FileService();
        $common = new CommonService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['goods'] = '';
        $jrow['instock_list'] = $s->getInstockDetailList($row->order_id, $row->id);
        $jrow['detail_list'] = $s->getInvoiceDetailList($row->id);
        $this->view->jsonPurchaseInvoice = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->order_list = $common->getOrderList();
        $this->view->pick('invoice/create');
    }

    /**
     * @acl({'link':'purchase:invoice:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseInvoiceService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new PurchaseInvoiceService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $table = new TableService();
        $oss_util = new FileService();
        $common = new CommonService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $order_rows = $common->getOrderList($row->order_id);
        if (count($order_rows) > 0) {
            $order_code = $order_rows[0]->order_code;
            $supplier_name = $order_rows[0]->supplier_name;
        } else {
            $order_code = '';
            $supplier_name = '';
        }

        $jrow = $row->toArray();
        $jrow['order_code'] = $order_code;
        $jrow['supplier_name'] = $supplier_name;
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_list'] = $s->getInvoiceDetailList($row->id);
        $this->view->jsonPurchaseInvoice = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseInvoiceService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function orderAction($order_id)
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseInvoiceService();
            $ret = new JsonData();
            $ret->message = '';
            $ret->list = $s->getInstockDetailList($order_id);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseInvoiceService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new PurchaseInvoiceService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }
}