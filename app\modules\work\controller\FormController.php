<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Modules\Work\Model\WorkForm;
use Envsan\Modules\Work\Service\FormService;
use Envsan\Modules\Work\Util\Constant;

/**
 * @name('表单')
 */
class FormController extends SuperController
{

    /**
     * @name('管理')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new FormService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'work:form:list'})
     */
    public function createAction()
    {
        if($this->request->isPost()){
            $this->setJsonResponse();
            $rs = new FormService();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new WorkForm())->toArray();
        $this->view->jsonForm = json_encode($jrow);
    }

    /**
     * @acl({'link':'work:form:list'})
     */
    function editAction($uid='')
    {
        $rs = new FormService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->save($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        if (empty($jrow['form_data'])){
            $jrow['form_data'] = [];
        } else {
            $jrow['form_data'] = json_decode($jrow['form_data'],true);
        }
        $jrow['data'] = Constant::$form_data_template;
        $jrow['type'] = 1;
        $jrow['select_value'] = '';
        $jrow['detail_data'] = ['id'=>''];
        $jrow['select_id'] = '';
        $jrow['add_flag'] = 1;
        $this->view->uid = $uid;
        $this->view->jsonForm = json_encode($jrow);
        $this->view->inputTypes = Constant::$input_types;
        $this->view->serviceInputTypes = Constant::$service_input_types;
        $this->view->defaultData = Constant::$form_data_template;
    }

    /**
     * @acl({'link':'work:form:list'})
     */
    public function deleteAction(){
        $s = new FormService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}