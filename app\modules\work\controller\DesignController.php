<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Work\Model\WorkDataChangeLogs;
use Envsan\Modules\Work\Service\CommonService;
use Envsan\Modules\Work\Service\DesignService;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Work\Util\Constant;

/**
 * @name('流程')
 */
class DesignController extends SuperController
{
    /**
     * @name('管理')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new DesignService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $cs = new CommonService();
        $this->view->groupList = $cs->getGroupList();
        $this->view->docTypes =ConstantUtil::$review_doc_types;
    }

    /**
     * @acl({'link':'work:design:list'})
     */
    public function createAction($id='')
    {
        $ds = new DesignService();
        if (empty($id)){
            $this->view->id = '';
            $this->view->name = '';
            $this->view->anchor = '[]';
            $this->view->connects = '{}';
        } else {
            $s = new DesignService();
            $row = $s->selectById($id);
            if (empty($row)){
                die(ErrorHelper::WRONG_ID);
            }
            $this->view->id = $row->id;
            $this->view->name = $row->name;
            $this->view->anchor = $row->design_anchor;
            $this->view->connects = $row->design_connects;
        }
        $this->view->users = json_encode($ds->getUserList());
    }

    /**
     * @acl({'link':'work:design:list'})
     */
    public function changeAction()
    {
        $ds = new DesignService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $ds->changeSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $jrow = (new WorkDataChangeLogs())->toArray();
        $this->view->jsonData = json_encode($jrow);
        $this->view->userList = $ds->getChangeUserList();
    }

    /**
     * @acl({'link':'work:design:list'})
     */
    public function saveAction()
    {
        $ds = new DesignService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $ds->save();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'work:design:list'})
     */
    public function bindAction($id=''){
        $s = new DesignService();
        $row = $s->selectById($id);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->bind($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $this->view->id = $row->id;
        $this->view->name = $row->name;
        $this->view->status = $row->status;
        $this->view->group_id = CvtUtil::nullToBlank($row->group_id);
        $this->view->form_id = CvtUtil::nullToBlank($row->form_id);
        $this->view->flowList = $s->getFlowList($row);
        $this->view->flow_ids = json_encode(CvtUtil::emptyToArray($row->flow_ids));
        $this->view->flow_error_ids = json_encode(CvtUtil::emptyToArray($row->flow_error_ids));
        $this->view->flow_more_ids = json_encode(CvtUtil::emptyToArray($row->flow_more_ids));
        if (empty($row->data_status)){
            $this->view->data_status = '[]';
        } else {
            $this->view->data_status = $row->data_status;
        }
        $cs = new CommonService();
        $this->view->groupList = $cs->getGroupList();
        $this->view->formList = $cs->getFormList();
        $this->view->abstrakt_keys = json_encode(CvtUtil::emptyToArray($row->abstrakt_keys),JSON_UNESCAPED_UNICODE);
        if (empty($row->doc_type)){
            $this->view->doc_type = '';
            $this->view->form_list = '[]';
        } else {
            $this->view->doc_type = $row->doc_type;
            $this->view->form_list = json_encode($s->getTempItems($row->doc_type),JSON_UNESCAPED_UNICODE);
        }
        $this->view->hidden_list = json_encode($s->getDesignFlowList($row->flow_list,$row->hidden_data),JSON_UNESCAPED_UNICODE);
        $this->view->flowList = $cs->getFormList();
        $this->view->types = ConstantUtil::$review_doc_types;
    }

    /**
     * @acl({'link':'work:design:list'})
     */
    public function deleteAction(){
        $s = new DesignService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'work:design:list'})
     */
    public function viewAction($id)
    {
        $s = new DesignService();
        $ret = $s->getViewData($id);
        if ($ret == false)
            die(ErrorHelper::WRONG_ID);

        $this->view->flow_list = json_decode(json_encode($ret['flow_list']));
        $this->view->connect_list = json_decode(json_encode($ret['connect_list']));
    }

    /**
     * @skipacl
     */
    public function selectuserAction(){
        $select_name = $this->request->getPost('select_name', 'tstring');
        if($select_name == ''){
            return '';
        }else{
            $user_row = User::findFirst(['del_flag = 0 and real_name = ?1', 'bind'=>[1=>"$select_name"]]);
            if(empty($user_row)){
                return '';
            }else{
                return json_encode($user_row);
            }
        }
    }

    /**
     * @skipacl
     */
    public function getformAction($doc_type){
        $s = new DesignService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            return json_encode($s->getTempItems($doc_type),JSON_UNESCAPED_UNICODE);
        }
    }
}