<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseStockLogs extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=30, nullable=true)
     */
    public $batch_no;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $data_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $data_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $sign;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $money;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $money_hs;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;
    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_user;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_stock_logs';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseStockLogs[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseStockLogs
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
