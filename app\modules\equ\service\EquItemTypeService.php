<?php
namespace Envsan\Modules\Equ\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Equ\Model\EquItemType;
use Envsan\Modules\Equ\Util\Constant;
use Envsan\Modules\Sys\Service\DictService;
use Phalcon\Mvc\User\Component;

class EquItemTypeService extends Component
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.name,
                t1.type_code,
                t1.type_name,
                t1.ext_val,
                t2.name as ship_type_name
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItemType', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesShipType', 't1.ship_type_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new EquItemType();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = $this->request->getPost('name', 'tstring');
        $type_code = $this->request->getPost('type_code', 'tstring');
        $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));

        if (empty($name) || CheckUtil::is_empty($type_code))
            return ErrorHelper::WRONG_INPUT;

        $type_name = Constant::$equ_type_arr[$type_code];
        if (empty($type_name)) {
            return '无效的类型';
        }

        if ($this->isRepeat($name, $row->id)) {
            return '名称重复';
        }

        $table = new TableService();
        $now = DateUtil::now();
        $user = SessionData::user();
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $row->name = $name;
        $row->type_code = $type_code;
        $row->type_name = $type_name;
        $row->ext_data = CvtUtil::arrayToNull($ext_data);
        $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
        $row->update_date = $now;
        $row->update_by = $user->id;

        if ($act == 'create') {
            $row->uid = UUID::make();
            $row->create_date = $now;
            $row->create_by = $user->id;
            $row->del_flag = 0;
            $row->group_id = $user->group_id;
            $row->owner = $user->owner;
        }
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Equ\Model\EquItemType')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return EquItemType::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return EquItemType::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getList()
    {
        return EquItemType::find([
            'columns' => 'id, name, type_name',
            'conditions' => 'del_flag = 0 and owner = ?1',
            'bind' => [1 => SessionData::user()->owner],
            'order' => 'id'
        ]);
    }
}