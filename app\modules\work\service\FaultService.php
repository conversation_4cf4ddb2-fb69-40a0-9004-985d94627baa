<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Equ\Model\EquFault;
use Envsan\Modules\Equ\Model\EquFaultHistory;
use Envsan\Modules\Equ\Model\EquItem;
use Envsan\Modules\Equ\Model\EquOutsideCompany;
use Envsan\Modules\Equ\Util\Constant;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\DictService;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class FaultService extends Component
{
    public function selectAll()
    {
        $last_id = $this->request->getPost('last_id', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.uid,
                a.fault_no,
                a.equ_code,
                a.fault_level,
                a.begin_dt,
                a.begin_describe,
                a.status
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquFault', 'a')
            ->where('a.del_flag = 0 and a.status < 40 and a.owner = '.SessionData::ownerId())
            ->orderBy('a.begin_dt desc')
            ->limit(20);

        if (!empty($last_id)) {
            $builder->andWhere('a.id < ?1', [1 => $last_id]);
        }

        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            $row['fault_level_name'] = Constant::$fault_level_arr[$row['fault_level']];
            $row['status_name'] = Constant::$fault_status_arr[$row['status']];
            if ($row['status'] == 10) {
                $row['status_class'] = 'primary';
            } else if ($row['status'] == 20) {
                $row['status_class'] = 'warning';
            } else if ($row['status'] == 30) {
                $row['status_class'] = 'primary';
            } else {
                $row['status_class'] = 'success';
            }
        }
        return $rows;
    }

    public function getAddIndexData()
    {
        $ret = [];
        $ret['status'] = 'error';
        $ret['data'] = null;
        $ret['equ_list'] = [];
        $ret['equ_index'] = 0;
        $ret['equ_type_index'] = 0;

        $ret['equ_type_list'] = Constant::$equ_type_arr;

        $uid = $this->request->getPost('uid', 'tstring');
        if (!empty($uid)) {
            $row = $this->selectByUid($uid);
            if (empty($row) || $row->del_flag == 1) {
                $ret['message'] = '故障单已失效，无法操作';
                return $ret;
            } else if ($row->status != 10) {
                $ret['message'] = '故障单已处理，无法操作';
                return $ret;
            }

            $cs = new FileService();
            $base_path = $cs->getImagePath();
            $begin_files = CvtUtil::emptyToArray($row->begin_files);
            foreach ($begin_files as &$begin_file)
            {
                $begin_file = $base_path.$begin_file;
            }

            $jrow = $row->toArray();
            $jrow['fault_level_name'] = Constant::$fault_level_arr[$row->fault_level];
            $jrow['begin_dt'] = substr($jrow['begin_dt'], 0, 16);
            $jrow['begin_files'] = $begin_files;
            $ret['data'] = $jrow;

            $equ_list = $this->getEquList($row->equ_type);
            foreach ($equ_list as $index => $item)
            {
                if ($item->id == $row->equ_id) {
                    $ret['equ_index'] = $index;
                    break;
                }
            }
            $ret['equ_list'] = $equ_list;
        }

        $ret['status'] = 'ok';
        $ret['fault_level_list'] = Constant::$fault_level_arr;
        return $ret;
    }

    public function getEquList($equ_type)
    {
        if (CheckUtil::is_empty($equ_type)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.code
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItem', 'a')
            ->innerJoin('Envsan\Modules\Equ\Model\EquItemType', 'a.type_id = t.id', 't')
            ->where('a.del_flag = 0 and t.type_code = ?1', [1 => $equ_type])
            ->orderBy('a.code');
        return $builder->getQuery()->execute();
    }

    public function submit()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $equ_type = $this->request->getPost('equ_type', 'tstring');
        $equ_id = $this->request->getPost('equ_id', 'tstring');
        $fault_level = $this->request->getPost('fault_level', 'tstring');
        $begin_dt = $this->request->getPost('begin_dt', 'tstring');
        $begin_describe = $this->request->getPost('begin_describe', 'tstring');
        $begin_files = urldecode($this->request->getPost('begin_files', 'tstring'));

        if (CheckUtil::is_empty($equ_type) || empty($equ_id) || CheckUtil::is_empty($fault_level)
            || empty($begin_dt) || empty($begin_describe) || empty($begin_files)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $equ_type_name = Constant::$equ_type_arr[$equ_type];
        if (empty($equ_type_name)) {
            return '无效的设备类型';
        }

        $equ_row = EquItem::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $equ_id]]);
        if (empty($equ_row)) {
            return '无效的设备';
        }

        $user = SessionData::user();
        $now = DateUtil::now();

        $this->db->begin();
        try {
            if (empty($uid)) {
//                if (!empty($equ_row->fault_id)) {
//                    return '该设备有未完成的故障单';
//                }

                $ss = new SequenceService();

                $row = new EquFault();
                $row->uid = UUID::make();
                $row->fault_no = $ss->useSequence(11);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = SessionData::ownerId();
            } else {
                $row = $this->selectByUid($uid);
                if (empty($row) || $row->del_flag == 1) {
                    return ErrorHelper::WRONG_ID;
                } else if ($row->status != 10) {
                    return '故障单已处理，不能编辑';
                }

//                if ($equ_row->fault_id != $row->id) {
//                    return '该设备绑定的故障单无效';
//                }
            }

            $row->equ_id = $equ_id;
            $row->equ_code = $equ_row->code;
            $row->equ_type = $equ_type;
            $row->equ_type_name = $equ_type_name;
            $row->fault_level = $fault_level;
            $row->fault_month = substr($begin_dt, 0, 7);
            $row->fault_date = substr($begin_dt, 0, 10);
            $row->begin_dt = $begin_dt.':00';
            $row->begin_describe = $begin_describe;
            $row->begin_files = $begin_files;
            $row->begin_submit_dt = $now;
            $row->begin_by = $user->id;
            $row->status = 10;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【创建故障单】equ_fault表更新失败");
            }

            if (!$this->saveHistory($row, $row->begin_describe)) {
                throw new \Exception("【创建故障单】equ_fault_history表更新失败");
            }

            if (empty($equ_row->fault_id) || $fault_level == 1) {
                $equ_row->fault_id = $row->id;
                if (!$equ_row->save()) {
                    throw new \Exception("【创建故障单】equ_item表更新失败");
                }
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function selectByUid($uid)
    {
        return EquFault::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    private function getEquTypeName($code)
    {
        $ds = new DictService();
        $types = $ds->getEquType();
        foreach ($types as $type)
        {
            if ($type->code == $code) {
                return $type->name;
            }
        }
        return '';
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            return '记录已失效，请下拉刷新页面';
//        } else if ($row->status != 10) {
//            return '记录已处理，无法删除，请下拉刷新页面';
        }


        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();

            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【删除故障单】equ_fault表更新失败");
            }

            $phql = 'UPDATE Envsan\Modules\Equ\Model\EquItem';
            $phql .= ' SET fault_id = null, update_date = ?2, update_by = ?3';
            $phql .= ' WHERE fault_id = ?1';
            $result = $this->modelsManager->executeQuery($phql, [
                1 => $row->id,
                2 => $now,
                3 => $user->id
            ]);
            if (!$result->success()) {
                throw new \Exception('【删除模具故障单】equ_item表更新失败！');
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function getEndIndexData()
    {
        $ret = [];
        $ret['status'] = 'error';

        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }

        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            $ret['message'] = '故障单已失效，无法操作';
            return $ret;
//        } else if ($row->status == 20) {
//            $ret['message'] = '外协修理中，无法操作';
//            return $ret;
        } else if ($row->status == 40) {
            $ret['message'] = '故障已解除';
            return $ret;
        }

        $cs = new FileService();
        $base_path = $cs->getImagePath();
        $begin_files = CvtUtil::emptyToArray($row->begin_files);
        foreach ($begin_files as &$begin_file)
        {
            $begin_file = $base_path.$begin_file;
        }

        $jrow = $row->toArray();
        $jrow['begin_files'] = $begin_files;

        $ret['status'] = 'ok';
        $ret['data'] = $jrow;
        return $ret;
    }

    public function end()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $end_dt = $this->request->getPost('end_dt', 'tstring');
        $end_describe = $this->request->getPost('end_describe', 'tstring');
        $end_files = urldecode($this->request->getPost('end_files', 'tstring'));

        if (empty($uid) || empty($end_dt) || empty($end_describe)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            return ErrorHelper::WRONG_ID;
//        } else if ($row->status == 20) {
//            return '外协修理中，不能解除';
        } else if ($row->status == 40) {
            return '故障已解除';
        }

        $equ_row = EquItem::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $row->equ_id]]);
        if (empty($equ_row)) {
            return '无效的设备';
        }

        $this->db->begin();
        try {
            $user = SessionData::user();
            $now = DateUtil::now();

            $end_files = CvtUtil::emptyToArray($end_files);

            $row->end_dt = $end_dt.':00';
            $row->end_describe = $end_describe;
            if (count($end_files) == 0) {
                $row->end_files = null;
            } else {
                $row->end_files = json_encode($end_files, JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
            }
            $row->end_submit_dt = $now;
            $row->end_by = $user->id;
            $row->status = 40;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【故障单完结】equ_fault表更新失败");
            }

            if (!$this->saveHistory($row, $row->end_describe)) {
                throw new \Exception("【创建故障单】equ_fault_history表更新失败");
            }

            if (!$this->updateEqu($equ_row)) {
                throw new \Exception("【故障单完结】equ_item表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    private function updateEqu($equ_row)
    {
        $fault_row = EquFault::findFirst([
            'del_flag = 0 and equ_id = ?1 and status < 40',
            'bind' => [1 => $equ_row->id],
            'order' => 'fault_level desc'
        ]);
        if (empty($fault_row)) {
            $equ_row->fault_id = null;
        } else {
            $equ_row->fault_id = $fault_row->id;
        }
        return $equ_row->save();
    }

    public function getRepairIndexData()
    {
        $ret = [];
        $ret['status'] = 'error';

        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }

        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            $ret['message'] = '故障单已失效，无法操作';
            return $ret;
        } else if ($row->status > 20) {
            $ret['message'] = '外协修理已完成，无法操作';
            return $ret;
        }

        $cs = new FileService();
        $base_path = $cs->getImagePath();
        $begin_files = CvtUtil::emptyToArray($row->begin_files);
        foreach ($begin_files as &$begin_file)
        {
            $begin_file = $base_path.$begin_file;
        }

        $jrow = $row->toArray();
        $jrow['begin_files'] = $begin_files;

        $ret['status'] = 'ok';
        $ret['data'] = $jrow;
        $ret['repair_user_list'] = $this->getRepairUserList();
        $ret['repair_company_list'] = $this->getRepairCompanyList();
        return $ret;
    }

    public function getRepairUserList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.real_name as name
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User', 'a')
            ->where('a.del_flag = 0 and a.role_id > 1')
            ->orderBy('a.name');
        return $builder->getQuery()->execute();
    }

    public function getRepairCompanyList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.name
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquOutsideCompany', 'a')
            ->where('a.del_flag = 0')
            ->orderBy('a.name');
        return $builder->getQuery()->execute();
    }

    public function repairSubmit()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $repair_user = $this->request->getPost('repair_user', 'tstring');
        $repair_dt = $this->request->getPost('repair_dt', 'tstring');
        $repair_describe = $this->request->getPost('repair_describe', 'tstring');
        $repair_company_id = urldecode($this->request->getPost('repair_company_id', 'tstring'));

        if (empty($uid) || empty($repair_user) || empty($repair_dt) || empty($repair_describe)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $row = $this->selectByUid($uid);
        if (empty($row)) {
            return ErrorHelper::WRONG_ID;
        } else if ($row->del_flag == 1) {
            return '故障单已作废，无法操作';
        } else if ($row->status > 20) {
            return '外协修理已完成，无法操作';
        }

        $user_row = User::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $repair_user]]);
        if (empty($user_row)) {
            return '责任人无效';
        }

        $repair_company = null;
        if (!empty($repair_company_id)) {
            $company_row = EquOutsideCompany::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $repair_company_id]]);
            if (empty($company_row)) {
                return '外协供应商无效';
            }
            $repair_company = $company_row->name;
        }

        $this->db->begin();
        try {
            $user = SessionData::user();
            $now = DateUtil::now();

            $row->repair_dt = $repair_dt.':00';
            $row->repair_user = $repair_user;
            $row->repair_user_name = $user_row->real_name;
            $row->repair_describe = $repair_describe;
            $row->repair_company_id = CvtUtil::blankToNull($repair_company_id);
            $row->repair_company = $repair_company;
            $row->repair_submit_dt = $now;
            $row->repair_by = $user->id;
            $row->status = 20;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【故障单外协修理】equ_fault表更新失败");
            }

            if (!$this->saveHistory($row, $row->repair_describe)) {
                throw new \Exception("【故障单外协修理】equ_fault_history表更新失败");
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function getDetailIndexData()
    {
        $ret = [];
        $ret['status'] = 'error';
        $ret['data'] = null;

        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }

        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            $ret['message'] = '故障单已失效，无法查看';
            return $ret;
        }

        $cs = new FileService();
        $base_path = $cs->getImagePath();
        $begin_files = CvtUtil::emptyToArray($row->begin_files);
        foreach ($begin_files as &$begin_file)
        {
            $begin_file = $base_path.$begin_file;
        }

        $jrow = $row->toArray();
        $jrow['fault_level_name'] = Constant::$fault_level_arr[$row->fault_level];
        $jrow['begin_dt'] = substr($jrow['begin_dt'], 0, 16);
        $jrow['begin_files'] = $begin_files;

        $ret['status'] = 'ok';
        $ret['data'] = $jrow;
        return $ret;
    }

    public function selectRepairAll()
    {
        $last_dt = $this->request->getPost('last_dt', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.uid,
                a.fault_no,
                a.equ_code,
                a.fault_level,
                a.begin_dt,
                a.begin_describe,
                a.repair_dt,
                a.repair_user_name,
                a.repair_company
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquFault', 'a')
            ->where('a.del_flag = 0 and a.status = 20 and a.owner = '.SessionData::ownerId())
            ->orderBy('a.repair_dt desc')
            ->limit(20);

        if (!empty($last_dt)) {
            $builder->andWhere('a.repair_dt > ?1', [1 => $last_dt]);
        }

        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            $row['fault_level_name'] = Constant::$fault_level_arr[$row['fault_level']];
            $row['repair_dt'] = substr($row['repair_dt'], 0, 16);
        }
        return $rows;
    }

    public function getRepairMoneyIndexData()
    {
        $ret = [];
        $ret['status'] = 'error';

        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }

        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            $ret['message'] = '故障单已失效，无法操作';
            return $ret;
        } else if ($row->status != 20) {
            $ret['message'] = '记录状态无效，无法操作';
            return $ret;
        }

        $jrow = $row->toArray();
        $jrow['repair_dt'] = substr($jrow['repair_dt'], 0, 16);

        $ret['status'] = 'ok';
        $ret['data'] = $row;
        return $ret;
    }

    public function repairMoneySubmit()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $repair_money = $this->request->getPost('repair_money', 'tstring');
        $repair_money_describe = $this->request->getPost('repair_money_describe', 'tstring');

        if (empty($uid) || empty($repair_money) || empty($repair_money_describe)) {
            return ErrorHelper::WRONG_INPUT;
        }

        if (!CheckUtil::isDecimal($repair_money)) {
            return '修理费用金额无效';
        }

        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            return ErrorHelper::WRONG_ID;
        } else if ($row->status != 20) {
            return '记录状态无效，无法操作';
        }

        $this->db->begin();
        try {
            $user = SessionData::user();
            $now = DateUtil::now();

            $row->repair_money = $repair_money;
            $row->repair_money_describe = $repair_money_describe;
            $row->repair_finish_dt = $now;
            $row->repair_finish_by = $user->id;
            $row->status = 30;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【故障单外协修理】equ_fault表更新失败");
            }

            if (!$this->saveHistory($row, $row->repair_money_describe)) {
                throw new \Exception("【故障单外协修理】equ_fault_history表更新失败");
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function saveHistory($fault_row, $fault_describe)
    {
        $row = new EquFaultHistory();
        $row->fault_id = $fault_row->id;
        $row->equ_id = $fault_row->equ_id;
        $row->equ_code = $fault_row->equ_code;
        $row->fault_status = $fault_row->status;
        $row->fault_describe = $fault_describe;
        $row->create_date = $fault_row->update_date;
        $row->del_flag = 0;
        return $row->save();
    }
}