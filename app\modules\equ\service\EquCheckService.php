<?php
namespace Envsan\Modules\Equ\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Equ\Model\EquItemCheckLogs;
use Envsan\Modules\Equ\Util\Constant;
use Phalcon\Mvc\User\Component;

class EquCheckService extends Component
{
    private function getCommonBuilder()
    {
        $equ_id = $this->request->get('equ_id', 'tstring');
        $date_begin = $this->request->get('date_begin', 'tstring');
        $date_end = $this->request->get('date_end', 'tstring');
        $form_id = $this->request->get('form_id', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Equ\Model\EquItemCheckLogs', 'a')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItem', 'a.item_id = e.id','e')
            ->leftJoin('Envsan\Modules\Equ\Model\EquCheckForm', 'a.form_id = c.id','c')
            ->where('a.del_flag = 0 and a.owner = ?1', [1 => SessionData::ownerId()]);

        if (!CheckUtil::is_empty($equ_id)) {
            $builder->andWhere("a.item_id = ?2", [2 => $equ_id]);
        }
        if (!CheckUtil::is_empty($date_begin)) {
            $builder->andWhere("a.check_date >= ?3", [3 => $date_begin]);
        }
        if (!CheckUtil::is_empty($date_end)) {
            $builder->andWhere("a.check_date <= ?4", [4 => $date_end]);
        }
        if (!CheckUtil::is_empty($form_id)) {
            $builder->andWhere("a.form_id = ?5", [5 => $form_id]);
        }
        return $builder;
    }

    public function selectAll()
    {
        $builder = $this->getCommonBuilder()
            ->columns('
                a.id,
                a.status,
                a.check_date,
                a.remarks,
                e.code as equ_code,
                c.name as form_name,
                c.code as form_code
            ')
            ->orderBy('a.check_date desc');
        return $builder;
    }

    public function setDetail($rows)
    {
        foreach ($rows as &$temp)
        {
            $temp['status_name'] = Constant::$check_logs_status[$temp['status']];
        }
        return $rows;
    }

    public function selectById($id)
    {
        return EquItemCheckLogs::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function getExportData()
    {
        $builder = $this->getCommonBuilder()
            ->columns('
                a.check_date,
                a.remarks,
                a.form_data,
                e.code as equ_code,
                c.name as form_name,
                c.code as form_code,
                u.real_name as create_name,
                g.name as group_name
            ')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 'a.create_by = u.id', 'u')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'c.group_id = g.id', 'g')
            ->orderBy('a.check_date');
        return $builder->getQuery()->execute();
    }

    public function exportExcel()
    {
        $rows = $this->getExportData();
        if (count($rows) == 0) {
            die('没有找到符合条件的数据');
        }
        $check_row = $rows[0];
        $form_no = CvtUtil::nullToBlank($check_row->form_code);
        $equ_no = CvtUtil::nullToBlank($check_row->equ_code);
        $group_name = CvtUtil::nullToBlank($check_row->group_name);
        $create_name = CvtUtil::nullToBlank($check_row->create_name);
        $remarks = CvtUtil::nullToBlank($check_row->remarks);
        $form_type_name = strpos($check_row->form_name, '油压') !== false ? '油压' : '电动';

        $file_title = '点检数据_'.date('Ymd');

        try {
            $ts = new TableService();
            $objExcel = new \PHPExcel();
            $ts->initExcelProperties($objExcel, $file_title);
            $objActSheet = $objExcel->getActiveSheet();
            $objActSheet->setTitle($file_title);

            $objActSheet->getColumnDimension()->setWidth(14);

            $objActSheet->setCellValue('A1', '【'.$equ_no.'】成型　'.$form_type_name.'　机械日常点检表');
            $objActSheet->getStyle()->getFont()->setBold(true)->setSize(24);

            $objActSheet->setCellValue('A2', '文件番号');
            $objActSheet->setCellValue('B2', $form_no);
            $objActSheet->getStyle('B2')->getFont()->setBold(true)->setSize(18);

            $item_key_list = [];
            $item_type_map = [];
            $header_list = [];
            foreach ($rows as $row)
            {
                $list = CvtUtil::emptyToArray($row->form_data);
                foreach ($list as $idx => $item)
                {
                    if ($idx == 0) {
                        $header_list = [$item['label1_title'], $item['label2_title'], $item['label3_title']];
                    }

                    $item_key = $item['name'].'_'.$item['label1_value'].'_'.$item['label2_value'].'_'.$item['label3_value'];
                    if (!in_array($item_key, $item_key_list)) {
                        $item_key_list[] = $item_key;
                        $item_type_map[$item['name']]['cnt'] = ($item_type_map[$item['name']]['cnt'] ?? 0) + 1;
                        $item_type_map[$item['name']]['list'][] = $item_key;
                    }
                }
            }

            $objActSheet->setCellValue('A3', '点检项目');
            $current_no = 4;
            foreach ($header_list as $header)
            {
                $objActSheet->setCellValue('A'.$current_no, $header);
                $current_no++;
            }

            $current_no = 3;
            $letter_start = 'B';
            $item_list = [];
            foreach ($item_type_map as $item_name => $map_item)
            {
                $objActSheet->setCellValue($letter_start.$current_no, $item_name);

                $letter_end = $ts->getExcelColumn($letter_start, $map_item['cnt'] - 1);
                $objActSheet->mergeCells($letter_start.$current_no.':'.$letter_end.$current_no);
                $letter_start = $ts->getNextExcelNo($letter_end);

                $item_list = array_merge($item_list, $map_item['list']);
            }

            $start_no = $current_no + 1;
            $letter_start = 'B';
            $letter_current = 'B';
            $letter_map = [];
            foreach ($item_list as $item_key)
            {
                $current_no = $start_no;
                $keys = explode('_', $item_key);

                $objActSheet->setCellValue($letter_current.$current_no, $keys[1]);
                $objActSheet->setCellValue($letter_current.++$current_no, $keys[2]);
                $objActSheet->setCellValue($letter_current.++$current_no, $keys[3]);
                $letter_map[$item_key] = $letter_current;

                $objActSheet->getColumnDimension($letter_current)->setWidth(20);
                $letter_current = $ts->getNextExcelNo($letter_current);
            }

            $footer_letter_start = $letter_current;
            $item_letters = array_values($letter_map);
            $objActSheet->mergeCells($letter_start.'2:'.$item_letters[count($item_letters) - 2].'2');

            $objActSheet->setCellValue($item_letters[count($item_letters) - 1].'2', '担当部门');
            $objActSheet->setCellValue($letter_current.'2', $group_name);

            $objActSheet->mergeCells($letter_current.'3:'.$letter_current.$current_no);
            $objActSheet->setCellValue($letter_current.'3', '点检者');

            $letter_current = $ts->getNextExcelNo($letter_current);
            $objActSheet->mergeCells($letter_current.'3:'.$letter_current.$current_no);
            $objActSheet->setCellValue($letter_current.'3', '确认者');

            $letter_current = $ts->getNextExcelNo($letter_current);
            $objActSheet->mergeCells($letter_current.'3:'.$letter_current.$current_no);
            $objActSheet->setCellValue($letter_current.'3', '备注');
            $letter_end = $letter_current;

            $objActSheet->mergeCells('A1:'.$letter_end.'1');
            $objActSheet->mergeCells($footer_letter_start.'2:'.$letter_end.'2');
            $current_no++;

            foreach ($rows as $row)
            {
                $objActSheet->setCellValue('A'.$current_no, date('Y/n/j', strtotime($row->check_date)));

                $footer_letter = $footer_letter_start;
                $objActSheet->setCellValue($footer_letter.$current_no, $create_name);
                $footer_letter = $ts->getNextExcelNo($footer_letter);
                $objActSheet->setCellValue($footer_letter.$current_no, '');
                $footer_letter = $ts->getNextExcelNo($footer_letter);
                $objActSheet->setCellValue($footer_letter.$current_no, $remarks);

                $list = CvtUtil::emptyToArray($row->form_data);
                foreach ($list as $item)
                {

                    $item_key = $item['name'].'_'.$item['label1_value'].'_'.$item['label2_value'].'_'.$item['label3_value'];
                    if (!array_key_exists($item_key, $letter_map)) {
                        continue;
                    }

                    $letter = $letter_map[$item_key];
                    $objActSheet->setCellValue($letter.$current_no, CvtUtil::nullToBlank($item['value']));
                }
                $current_no++;
            }

            $objActSheet->getStyle('A'.$start_no.':'.$letter_end.($current_no - 1))->getAlignment()->setWrapText(true);
            $objActSheet->getStyle('A1:'.$letter_end.($current_no - 1))->getAlignment()
                ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER)
                ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
            $objActSheet->getStyle('A1:'.$letter_end.($current_no - 1))->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);

            $objActSheet->setCellValue('A'.$current_no, '备　注');
            $objActSheet->getStyle('A'.$current_no)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
            $objActSheet->getStyle('A'.$current_no)->getFont()->setBold(true);
            $objActSheet->setCellValue('B'.$current_no, '成型机日常点检工作，在8:00~20:00间完成。点检完后，点检者须在表内相应栏内做好标记：正常表示为“√”异常表示为“×”　　发现异常');
            $objActSheet->getRowDimension($current_no)->setRowHeight(20);

            $objRichText = new \PHPExcel_RichText();
            $objRichText->createText('时立即报告上司。异常处理后成型机故障履历表中详细记录，每日班长进行现场确认，并在确认内容栏内划“√”月末提交课长确认。※');
            $boldText = $objRichText->createTextRun('如果停机请注明，并注明停机原因。');
            $boldText->getFont()->setBold(true);
            $objActSheet->setCellValue('B'.++$current_no, $objRichText);
            $objActSheet->getRowDimension($current_no)->setRowHeight(20);

            $objActSheet->getStyle('A1:'.$letter_end.'1')->getBorders()->getTop()->setBorderStyle(\PHPExcel_Style_Border::BORDER_MEDIUM);
            $objActSheet->getStyle($letter_end.'1:'.$letter_end.$current_no)->getBorders()->getRight()->setBorderStyle(\PHPExcel_Style_Border::BORDER_MEDIUM);
            $objActSheet->getStyle('A'.$current_no.':'.$letter_end.$current_no)->getBorders()->getBottom()->setBorderStyle(\PHPExcel_Style_Border::BORDER_MEDIUM);
            $objActSheet->getStyle('A1:A'.$current_no)->getBorders()->getLeft()->setBorderStyle(\PHPExcel_Style_Border::BORDER_MEDIUM);

            $objActSheet->setCellValue('A'.++$current_no, '修正日期：　'.date('Y年n月j日'));
            $objActSheet->setCellValue($footer_letter_start.$current_no, SessionData::owner()->company);

            $objActSheet->getStyle('A1:'.$letter_end.($current_no - 1))->getFont()->setName('SimSun');

            $ts->exportFile($objExcel, $file_title);
        } catch (\Exception $e) {
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
    }
}