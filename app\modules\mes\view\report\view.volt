{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">生产情况</span>
                    </div>
                </div>
                <div class="portlet-body form" >
                    <form id="form" class="form-horizontal">
                        <div class="form-body"  style="height: 77vh;overflow-y: auto;overflow-x: hidden">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>生产日期</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="report_date" v-model="report_date" readonly/>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('view') }}
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3" readonly></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">生产日报</span>
                    </div>
                    <div class="actions">
                    </div>
                </div>
                <div class="portlet-body" style="height: 85.5vh;overflow-y: auto">
                    <div v-for="work_item,work_idx in report_data" style="margin-bottom: 15px">
                        <div style="width: 100%;background-color: #F2F2F2;display: flex;flex-direction: row;padding: 8px;border: 1px solid #E2E2E2">
                            <div>
                                <span v-text="work_item.real_name" style="font-weight: bold"></span>
                            </div>
                            <div style="display: flex;color:#0080FF;padding-left: 10px;font-weight: bold">
                                <div style="margin-right: 10px">
                                    时薪：<span v-text="work_item.cost + ' 元/H'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    日工：<span v-text="work_item.day_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    计件：<span v-text="work_item.jj_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    其他工作：<span v-text="work_item.other_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    补助：<span v-text="work_item.day_bz_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px;color: red">
                                    总工资：<span v-text="work_item.sum_money + ' 元'"></span>
                                </div>
                            </div>
                        </div>
                        <div style="border: 1px solid #E2E2E2">
                            <div v-if="work_item.produce_list.length > 0">
                                <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                    <thead>
                                    <tr>
                                        <th>班次</th>
                                        <th>批次号</th>
                                        <th>产品名称</th>
                                        <th>工艺名称</th>
                                        <th>基准</th>
                                        <th>生产性</th>
                                        <th>工资方式</th>
                                        <th>合格数量</th>
                                        <th>加工费(件/元)</th>
                                        <th>工时(H)</th>
                                        <th>工资系数</th>
                                        <th>不良数</th>
                                        <th>不良原因</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="d_item, d_index in work_item.produce_list">
                                        <td>
                                            <span v-if="d_item.shift_type == 1">白班</span>
                                            <span v-else>夜班</span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.notice_code'></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.product_code'></span>
                                            <br>
                                            <span v-text='d_item.product_name'></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.bom_code'></span>
                                            <br>
                                            <span v-text='d_item.bom_name'></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.scxjz != ''" v-text="d_item.scxjz + '(件/H)'"></span>
                                            <br>
                                            <span v-if="d_item.jzz != ''" v-text="d_item.jzz + '(H)'"></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.scx != ''" v-text="d_item.scx + '%'"></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.work_type == 1">日工</span>
                                            <span v-else>计件</span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.cnt"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.one_cost"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.hour"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.wages_ratio"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.error_cnt"></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.error_type'></span>
                                            <br>
                                            <span v-text='d_item.error_remarks'></span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div v-if="work_item.other_list.length > 0">
                                <div style="display: flex;width: 100%;padding: 5px;flex-wrap: wrap">
                                    <div style="width: 50%;padding-right: 5px" v-for="o_item, o_index in work_item.other_list">
                                        <div style="width: 100%;display: flex;background-color: #F2F2F2;padding: 5px;border: 1px solid #e2e2e2;border-radius: 5px">
                                            <div style="width: 15%;line-height: 36px">
                                                <span v-text="o_item.produce_type"></span>
                                            </div>
                                            <div style="width: 20%;line-height: 36px">
                                                <span v-text="o_item.remarks"></span>
                                            </div>
                                            <div style="width: 15%;line-height: 36px">
                                                <span v-if="o_item.shift_type == 1">白班</span>
                                                <span v-else>夜班</span>
                                            </div>
                                            <div style="width: 25%;line-height: 36px">
                                                <span>工时(H)：</span>
                                                <span v-text="o_item.hour"></span>
                                            </div>
                                            <div style="width: 25%;line-height: 36px">
                                                <span>工资系数：</span>
                                                <span v-text="o_item.wages_ratio"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {

        }
    });
</script>