<?php
namespace Envsan\Modules\Trade\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Trade\Model\TradeOrder;
use Envsan\Modules\Trade\Model\TradeOrderDetail;
use Envsan\Modules\Trade\Service\CommonService;
use Envsan\Modules\Trade\Service\CustomerService;
use Envsan\Modules\Trade\Service\OrderDetailService;
use Envsan\Modules\Trade\Service\OrderService;

/**
 * @name("销售订单详细")
 */
class OrderdetailController extends SuperController
{
    private $page_id = 3;

    /**
     * @skipacl
     */
    public function listAction($type = '',$uid = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderDetailService();
            $builder = $s->selectAll($uid);
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @skipacl
     * 取得产品库存信息
     */
    public function stockinfoAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderDetailService();
            $ret =  $s->getStockInfo();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'trade:order:create'})
     */
    public function createAction($order_uid)
    {
        $o = new OrderService();
        $order_row = $o->selectByUid($order_uid);
        if (empty($order_row)) {
            die('订单已失效');
        } else if ($order_row->status > 10){
            die('订单已提交');
        }
        $s = new OrderDetailService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create($order_row->id));
            return json_encode($ret);
        }
        $table = new TableService();
        $jrow = (new TradeOrderDetail())->toArray();
        $jrow['new_flag'] = 0;
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['files'] = [];
        $jrow['stock_cnt'] = 0;
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $this->view->productList = $s->selectProductList($order_row->customer_id);
        $this->view->jsonData = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->order_uid = $order_uid;
    }

    /**
     * @acl({'link':'trade:order:create'})
     */
    function editAction($uid)
    {
        $s = new OrderDetailService();
        if ($this->request->isPost()) {
            $row = $s->selectByUid($uid);
            if (empty($row)) {
                die('订单明细已失效');
            } else if ($row->status > 10){
                die('订单明细已提交');
            }
            $o = new OrderService();
            $order_row = $o->selectById($row->order_id);
            if (empty($order_row)){
                die(ErrorHelper::WRONG_ID);
            }
            if ($order_row->status > 10){
                die(ErrorHelper::WRONG_ID);
            }
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        } else {
            $row = $s->selectByUid2($uid);
            if (empty($row)) {
                die('订单明细已失效');
            } else if ($row->status > 10){
                die('订单明细已提交');
            }
            $o = new OrderService();
            $order_row = $o->selectById($row->order_id);
            if (empty($order_row)){
                die(ErrorHelper::WRONG_ID);
            }
            if ($order_row->status > 10){
                die(ErrorHelper::WRONG_ID);
            }
            $table = new TableService();
            $ext_data =  CvtUtil::emptyToArray($row->ext_data);
            $jrow = $row->toArray();
            $oss_util = new FileService();
            $jrow['base_path'] = $oss_util->getImagePath();
            $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
            $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
            $jrow['stock_cnt'] = 0;
            $this->view->productList = $s->selectProductList($order_row->customer_id);
            $this->view->jsonData = json_encode($jrow);
            $this->view->uid = $uid;
            $this->view->extDataName = 'ext_data';
            $this->view->extDataCnt = 6;
            $this->view->pick('orderdetail/create');
        }
    }

    /**
     * @acl({'link':'trade:order:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderDetailService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new OrderDetailService();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }
}