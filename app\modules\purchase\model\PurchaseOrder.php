<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseOrder extends BaseModel
{
    const STATUS_START = 10;
    const STATUS_REVIEW = 20;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $request_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $order_code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $ww_plan_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $order_type;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $order_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $supplier_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $begin_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $end_date;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $total_weight;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $total_money;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $total_money_hs;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $finish_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $finish_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $detail_data;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var double
     * @Column(type="double", length=11, nullable=true)
     */
    public $total_quantity;


    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $reject_status;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $reject_remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $review_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $review_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_order';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseOrder[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseOrder
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
