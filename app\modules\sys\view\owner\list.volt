<?php
use Envsan\Modules\Sys\Model\Owner;
?>

<!-- Content Header (Page header) -->
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">用户一览</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">名称</label>
                            <div class="col-md-9">
                                <input name="content" type="text" class="form-control" v-model="content"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-offset-8 col-md-4 col-lg-offset-9 col-lg-3" style="text-align: right;">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('sys/owner/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="id">ID</th>
                    <th data-field="domain">域名</th>
                    <th data-field="contact">联系人</th>
                    <th data-field="company">单位</th>
                    <th data-field="plan">套餐</th>
                    <th data-field="tel" data-formatter="telFormatter">电话</th>
                    <th data-field="start_date">开始日期</th>
                    <th data-field="start_date">结束日期</th>
                    <th data-field="status" data-formatter="statusFormatter">状态</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
<div id="act" style="display: none;">
    <div class="btn-group">
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                操作 <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
                <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-pencil"></i> 编辑</a></li>
                <li><a href="javascript:" onclick="disable('@id@', 'yes')">冻结</a></li>
                <li><a href="javascript:" onclick="disable('@id@', 'no')">解冻</a></li>
            </ul>
        </div>
    </div>
</div>
<script>
    var $table = $('#table');
    var app = new Vue({
        el:'#app',
        data:{
            content:''
        },
        methods:{
            search : function () {
                this.content = this.content.trim();
                $table.bootstrapTable('selectPage', 1);
            },
            params : function (p) {
                p.content = this.content;
                return p;
            },
            reset : function () {
                this.content = '';
                $table.bootstrapTable('refresh');
            }
        }
    });


    $table.bootstrapTable();
    var actHtml = $('#act').html();

    function actionFormatter(v, row, idx) {
        return actHtml.replace(/@id@/g, row.id);
    }

    function telFormatter(v, row, idx) {
        if(row.mobile!='' && row.tel!='')
            return row.mobile + '/' + row.tel;
        if(row.mobile!='')
            return row.mobile;
        return row.tel;
    }

    function statusFormatter(v, row, idx) {
        if(v=='<?=Owner::STATUS_NORMAL?>')
            return '<span class="label label-success">正常</span>';
        else if(v=='<?=Owner::STATUS_DISABLED?>')
            return '<span class="label label-danger">冻结</span>';
    }

    function create() {
        window.layer_result='';
        layer.open({
            title:'新建',
            type: 2,
            area: makeArea('40em', '100%'),
            content: '{{ url('sys/owner/create') }}',
            end:function(){
                if(window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(id) {
        window.layer_result='';
        layer.open({
            title:'编辑',
            type: 2,
            area: makeArea('40em', '100%'),
            content: '{{ url('sys/owner/edit/') }}' + id,
            end:function(){
                if(window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function disable(id, action){
        var dlg = layer.confirm('确认执行这个操作吗！', function(){
            showSpin();
            $.post("{{ url('sys/owner/disable') }}", {id:id, action:action}, function (rs) {
                closeSpin(dlg);
                if(rs.status=='ok'){
                    toastr.success('操作成功!');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败!');
                }
            })
        });
    }
</script>
