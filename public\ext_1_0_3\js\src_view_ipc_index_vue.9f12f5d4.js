(self["webpackChunksfp_ext"] = self["webpackChunksfp_ext"] || []).push([["src_view_ipc_index_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=script&lang=js":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=script&lang=js ***!
  \**********************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.push.js */ "./node_modules/core-js/modules/es.array.push.js");
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _js_global__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../js/global */ "./src/js/global.js");


/* harmony default export */ __webpack_exports__["default"] = ({
  name: "plan",
  components: {},
  data() {
    return {
      key_list: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '·', '删除'],
      step_type: 0,
      group_uid: '',
      group_name: 'MES工控机',
      token_key: '',
      user_key: '',
      user_uid: '',
      user_name: '',
      user_code: '',
      work_hour: 0,
      errors: [],
      ships: [],
      xt_list: [],
      repair_list: [],
      over_work_type: 1,
      types: [],
      boms: [],
      bom_list: [],
      work_list: [],
      begin_work_show: false,
      other_work_show: false,
      bom_key: '',
      work_status: 0,
      // 0 未开始 10 工作中 20 暂停
      over_work_show: false,
      stop_work_show: false,
      continue_work_show: false,
      stop_data: {
        uid: ''
      },
      sel_ship_id: '',
      stop_data_error: '',
      stop_data_type: '',
      close_cnt: 30
    };
  },
  created() {
    this.init();
    this.setFocus();
  },
  methods: {
    selXt(idx) {
      for (let i = 0; i < this.xt_list.length; i++) {
        this.xt_list[i].sel = 0;
      }
      this.xt_list[idx].sel = 1;
    },
    init() {
      this.group_uid = _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].getItem('token');
      this.group_name = _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].getItem('name');
      if (this.group_uid == '' || this.group_uid == null) {
        this.step_type = 1;
      } else {
        this.step_type = 2;
      }
    },
    setUserFocus() {
      this.user_key = '';
      // eslint-disable-next-line no-undef
      $('#user_key').focus();
    },
    setBomFocus() {
      this.bom_key = '';
      // eslint-disable-next-line no-undef
      $('#bom_key').focus();
    },
    setOverBomFocus() {
      this.bom_key = '';
      // eslint-disable-next-line no-undef
      $('#over_bom_key').focus();
    },
    setFocus() {
      if (this.step_type == 2) {
        // eslint-disable-next-line no-undef
        $('#user_key').focus();
      } else if (this.step_type == 3) {
        if (!(this.begin_work_show || this.other_work_show || this.over_work_show || this.stop_work_show || this.continue_work_show)) {
          if (this.close_cnt <= 1) {
            this.step_type = 2;
          } else {
            this.close_cnt--;
          }
        } else {
          this.close_cnt = 30;
        }
      }
      setTimeout(() => {
        this.setFocus();
      }, 1000);
    },
    setToken() {
      if (this.token_key == '') {
        this.$message.error('请输入TOKEN');
        return;
      }
      if (this.token_key.length != 32) {
        this.$message.error('TOKEN长度不正确');
        return;
      }
      this.$http.post('ipc/index/token', {
        uid: this.token_key
      }).then(rs => {
        if (rs.status == 'ok') {
          _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].setItem('token', rs.data.uid);
          _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].setItem('name', rs.data.name);
          this.$message.success('设置成功');
          this.token_key = '';
          this.init();
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    handleKeypress(e) {
      if (e.code == 'Enter') {
        if (this.step_type == 2) {
          if (this.user_key.length != 10) {
            this.user_key = '';
            this.$message.error('二维码格式不正确');
            return;
          }
          this.getUserData(this.user_key);
          this.user_key = '';
        } else if (this.step_type == 3) {
          if (this.bom_key.length != 15) {
            this.bom_key = '';
            this.$message.error('二维码格式不正确');
            return;
          }
          if (this.begin_work_show) {
            this.getBomData(this.bom_key, '');
          } else if (this.over_work_show) {
            this.getBomData(this.bom_key, this.stop_data.uid);
          }
          this.bom_key = '';
        }
      }
    },
    getBomData(bom_key, detail_uid) {
      this.$http.post('ipc/index/get-bom', {
        uid: bom_key,
        detail_uid: detail_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          let data = rs.data;
          if (detail_uid == '') {
            for (let bom of this.bom_list) {
              if (bom.uid == data.uid) {
                this.$message.error('不能重复扫码');
                return;
              }
            }
            this.bom_list.push(data);
          } else {
            for (let bom of this.boms) {
              if (bom.uid == data.uid) {
                this.$message.error('不能重复扫码');
                return;
              }
            }
            this.boms.push(data);
          }
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    getUserData(user_key) {
      this.$http.post('ipc/index/user', {
        uid: user_key,
        group_uid: this.group_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          let work_status = 0;
          for (let item of rs.data.work_list) {
            if (item.status <= 20) {
              work_status += parseInt(item.status);
            }
          }
          this.step_type = 3;
          this.user_uid = rs.data.user_uid;
          this.user_name = rs.data.user_name;
          this.user_code = rs.data.user_code;
          this.work_status = work_status;
          this.work_list = rs.data.work_list;
          this.work_hour = rs.data.work_hour;
          this.close_cnt = 30;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    workBegin() {
      this.begin_work_show = true;
      this.bom_list = [];
      setTimeout(() => {
        this.setBomFocus();
      }, 100);
    },
    stopWork() {
      for (let item of this.work_list) {
        if (item.status == 20) {
          this.$message.error('已存在暂停工作');
          return;
        }
      }
      for (let item of this.work_list) {
        if (item.status == 10) {
          this.stop_work_show = true;
          this.stop_data = item;
          return;
        }
      }
      this.$message.error('没有工作中内容');
    },
    stopWorkSave() {
      if (!this.stop_data.uid) {
        this.$message.error('数据异常');
        return;
      }
      this.$http.post('ipc/index/stop-work', {
        uid: this.stop_data.uid,
        user_uid: this.user_uid,
        group_uid: this.group_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.stop_work_show = false;
          let work_status = 0;
          for (let item of rs.data.work_list) {
            if (item.status <= 20) {
              work_status += parseInt(item.status);
            }
          }
          this.work_status = work_status;
          this.work_list = rs.data.work_list;
          this.work_hour = rs.data.work_hour;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    workBeginSave() {
      this.$http.post('ipc/index/work-begin', {
        user_uid: this.user_uid,
        group_uid: this.group_uid,
        bom_data: encodeURIComponent(JSON.stringify(this.bom_list))
      }).then(rs => {
        if (rs.status == 'ok') {
          this.begin_work_show = false;
          let work_status = 0;
          for (let item of rs.data.work_list) {
            if (item.status <= 20) {
              work_status += parseInt(item.status);
            }
          }
          this.work_status = work_status;
          this.work_list = rs.data.work_list;
          this.work_hour = rs.data.work_hour;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    continueWork() {
      for (let item of this.work_list) {
        if (item.status == 20) {
          this.continue_work_show = true;
          this.stop_data = item;
          return;
        }
      }
      this.$message.error('没有暂停工作');
    },
    continueWorkSave() {
      if (!this.stop_data.uid) {
        this.$message.error('数据异常');
        return;
      }
      this.$http.post('ipc/index/continue-work', {
        uid: this.stop_data.uid,
        user_uid: this.user_uid,
        group_uid: this.group_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.continue_work_show = false;
          let work_status = 0;
          for (let item of rs.data.work_list) {
            if (item.status <= 20) {
              work_status += parseInt(item.status);
            }
          }
          this.work_status = work_status;
          this.work_list = rs.data.work_list;
          this.work_hour = rs.data.work_hour;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    overWork() {
      for (let item of this.work_list) {
        if (item.status == 10) {
          this.getOverWork(item);
          return;
        }
      }
      for (let item of this.work_list) {
        if (item.status == 20) {
          this.getOverWork(item);
          return;
        }
      }
      this.$message.error('没有工作中');
    },
    getOverWork(item) {
      this.stop_data = item;
      this.$http.post('ipc/index/over-data', {
        uid: item.uid,
        group_uid: this.group_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.over_work_type = 1;
          this.ships = rs.data.ships;
          this.errors = rs.data.errors;
          this.types = rs.data.types;
          this.xt_list = rs.data.xt_list;
          this.repair_list = rs.data.repair_list;
          this.boms = rs.data.bom_data;
          this.stop_data.work_hour = rs.data.work_hour;
          this.stop_data.work_type = rs.data.work_type;
          this.sel_ship_id = '';
          this.stop_data_error = '';
          this.stop_data_type = '';
          this.over_work_show = true;
          setTimeout(() => {
            this.setOverBomFocus();
          }, 500);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    numKeypress(key) {
      if (this.sel_ship_id == '') {
        this.$message.error('请选择填写项目');
        return;
      }
      for (let bom of this.boms) {
        for (let item of bom.ships) {
          if (item.id == this.sel_ship_id) {
            if (key == '删除') {
              item.cnt = '';
            } else {
              if (key == '·') {
                if (item.cnt == '') {
                  return;
                }
                if (item.cnt.indexOf('.') > -1) {
                  return;
                }
                item.cnt += '.';
              } else {
                item.cnt += key + '';
              }
            }
            break;
          }
        }
      }
    },
    overWorkSave() {
      if (!this.stop_data.uid) {
        this.$message.error('数据异常');
        return;
      }
      let datas = [];
      let ship_list = [];
      if (this.over_work_type == 1) {
        if (this.boms.length == 0) {
          this.$message.error('请扫图纸二位码');
          return;
        }
        datas = this.boms;
        for (let ship of this.ships) {
          if (ship.sel == 1) {
            ship_list.push(ship);
          }
        }
        if (ship_list.length == 0) {
          this.$message.error('请选择工作内容');
          return;
        }
      } else if (this.over_work_type == 2) {
        for (let item of this.xt_list) {
          if (item.sel == 1) {
            datas.push(item);
            break;
          }
        }
        if (datas.length == 0) {
          this.$message.error('请选择协同工作项目');
          return;
        }
      } else if (this.over_work_type == 3) {
        for (let item of this.repair_list) {
          if (item.sel == 1) {
            datas.push(item);
          }
        }
        if (datas.length == 0) {
          this.$message.error('请选择返修项目');
          return;
        }
      } else if (this.over_work_type == 4) {
        for (let item of this.types) {
          if (item.sel == 1) {
            datas.push(item);
          }
        }
        if (datas.length == 0) {
          this.$message.error('请选择其他工作内容');
          return;
        }
      } else {
        this.$message.error('请选择工作类型');
        return;
      }
      this.$http.post('ipc/index/over-work', {
        work_type: this.over_work_type,
        uid: this.stop_data.uid,
        group_uid: this.group_uid,
        stop_data_error: this.stop_data_error,
        ships: encodeURIComponent(JSON.stringify(ship_list)),
        datas: encodeURIComponent(JSON.stringify(datas))
      }).then(rs => {
        if (rs.status == 'ok') {
          this.over_work_show = false;
          let work_status = 0;
          for (let item of rs.data.work_list) {
            if (item.status <= 20) {
              work_status += parseInt(item.status);
            }
          }
          this.work_status = work_status;
          this.work_list = rs.data.work_list;
          this.work_hour = rs.data.work_hour;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    otherWorkSave() {
      this.$http.post('ipc/index/other-work', {
        user_uid: this.user_uid,
        group_uid: this.group_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.other_work_show = false;
          let work_status = 0;
          for (let item of rs.data.work_list) {
            if (item.status <= 20) {
              work_status += parseInt(item.status);
            }
          }
          this.work_status = work_status;
          this.work_list = rs.data.work_list;
          this.work_hour = rs.data.work_hour;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    tokenShow() {
      this.step_type = 1;
      this.token_key = '';
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=template&id=5f1e5064&scoped=true":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=template&id=5f1e5064&scoped=true ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticStyle: {
      "padding": "15px",
      "background-color": "#F2FEFF",
      "height": "100vh"
    }
  }, [_c('el-card', {
    staticClass: "box-card"
  }, [_c('div', {
    staticClass: "clearfix",
    staticStyle: {
      "display": "flex",
      "flex-direction": "row"
    },
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('div', {
    staticStyle: {
      "width": "50%"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px",
      "color": "#409eff"
    },
    domProps: {
      "textContent": _vm._s(_vm.group_name)
    }
  })]), _c('div', {
    staticStyle: {
      "width": "50%",
      "text-align": "right"
    }
  }, [_vm.group_uid != '' ? _c('el-button', {
    attrs: {
      "type": "primary",
      "plain": "",
      "size": "mini"
    },
    on: {
      "click": _vm.tokenShow
    }
  }, [_vm._v("设置")]) : _vm._e()], 1)]), _c('div', {
    staticStyle: {
      "height": "85vh",
      "overflow": "auto"
    }
  }, [_vm.step_type == 1 ? _c('div', [_c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "center",
      "padding-top": "100px"
    }
  }, [_c('el-input', {
    staticStyle: {
      "width": "300px"
    },
    attrs: {
      "placeholder": "请输入TOKEN"
    },
    model: {
      value: _vm.token_key,
      callback: function ($$v) {
        _vm.token_key = $$v;
      },
      expression: "token_key"
    }
  }), _c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.setToken
    }
  }, [_vm._v("设置")])], 1), _c('div', {
    staticStyle: {
      "text-align": "center",
      "margin-top": "60px"
    }
  }, [_c('el-button', {
    staticStyle: {
      "width": "200px",
      "height": "120px"
    },
    attrs: {
      "type": "info",
      "plain": ""
    },
    on: {
      "click": _vm.init
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("关闭")])])])], 1)]) : _vm._e(), _vm.step_type == 2 ? _c('div', {
    staticStyle: {
      "padding-top": "50px"
    }
  }, [_c('div', {
    staticStyle: {
      "width": "500px",
      "height": "350px",
      "margin": "auto",
      "border": "2px solid #B2B2B2",
      "padding": "30px",
      "border-radius": "10px",
      "text-align": "center"
    }
  }, [_c('i', {
    staticClass: "el-icon-s-custom",
    staticStyle: {
      "font-size": "150px"
    }
  }), _c('div', [_c('span', {
    staticStyle: {
      "font-size": "50px"
    }
  }, [_vm._v("请扫描工卡")])]), _c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "center",
      "margin-top": "10px"
    }
  }, [_c('input', {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.user_key,
      expression: "user_key"
    }],
    staticClass: "form-control",
    staticStyle: {
      "ime-mode": "disabled",
      "width": "200px"
    },
    attrs: {
      "id": "user_key",
      "type": "text",
      "placeholder": "请扫描工卡",
      "autoComplete": "off"
    },
    domProps: {
      "value": _vm.user_key
    },
    on: {
      "keypress": _vm.handleKeypress,
      "input": function ($event) {
        if ($event.target.composing) return;
        _vm.user_key = $event.target.value;
      }
    }
  }), _c('el-button', {
    attrs: {
      "type": "primary",
      "plain": "",
      "size": "small"
    },
    on: {
      "click": _vm.setUserFocus
    }
  }, [_vm._v("扫码")])], 1), _c('div', {
    staticStyle: {
      "margin-top": "10px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "16px",
      "color": "red"
    }
  }, [_vm._v("如果扫码没反应，请点击上方（扫码）按钮后再扫码")])])])]) : _vm._e(), _vm.step_type == 3 ? _c('div', [_c('div', [_c('el-descriptions', {
    staticClass: "margin-top",
    staticStyle: {
      "font-size": "20px"
    },
    attrs: {
      "column": 3,
      "border": ""
    }
  }, [_c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_c('i', {
    staticClass: "el-icon-user"
  }), _vm._v(" 姓名 ")]), _c('span', {
    staticStyle: {
      "font-weight": "bold"
    },
    domProps: {
      "textContent": _vm._s(_vm.user_name)
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_c('i', {
    staticClass: "el-icon-mobile-phone"
  }), _vm._v(" 工号 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.user_code)
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_c('i', {
    staticClass: "el-icon-time"
  }), _vm._v(" 总工时 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.work_hour + '（H）')
    }
  })], 2)], 1)], 1), _c('div', {
    staticStyle: {
      "width": "100%",
      "height": "62vh",
      "padding": "15px 0",
      "overflow": "auto"
    }
  }, [[_c('el-table', {
    staticStyle: {
      "width": "100%",
      "font-size": "16px"
    },
    attrs: {
      "data": _vm.work_list
    }
  }, [_c('el-table-column', {
    attrs: {
      "width": "140px",
      "prop": "work_name",
      "label": "工作"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "begin_time",
      "label": "工作时间"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return _vm._l(JSON.parse(scope.row.time_data), function (time, time_index) {
          return _c('div', {
            key: time_index
          }, [_c('span', {
            domProps: {
              "textContent": _vm._s(time.begin)
            }
          }), _vm._v(" ~ "), _c('span', {
            domProps: {
              "textContent": _vm._s(time.end)
            }
          })]);
        });
      }
    }], null, false, 2710230586)
  }), _c('el-table-column', {
    attrs: {
      "prop": "hour",
      "label": "工时(H)"
    }
  }), _c('el-table-column', {
    attrs: {
      "width": "450px",
      "label": "工作内容"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return [scope.row.work_type == 2 || scope.row.work_type == 4 ? _c('div', {
          staticStyle: {
            "display": "flex"
          }
        }, _vm._l(JSON.parse(scope.row.work_data), function (work, work_index) {
          return _c('div', {
            key: work_index
          }, [_c('span', {
            domProps: {
              "textContent": _vm._s(work.ship_name)
            }
          }), work_index < JSON.parse(scope.row.work_data).length - 1 ? _c('span', [_vm._v(",")]) : _vm._e()]);
        }), 0) : _c('div', _vm._l(JSON.parse(scope.row.work_data), function (work, work_index) {
          return _c('div', {
            key: work_index
          }, [_c('span', {
            domProps: {
              "textContent": _vm._s(work.order_code)
            }
          }), _vm._v("/ "), _c('span', {
            domProps: {
              "textContent": _vm._s(work.product_name)
            }
          }), _vm._v("/ "), _c('span', {
            domProps: {
              "textContent": _vm._s(work.bom_name)
            }
          }), _vm._v("/ "), _c('span', {
            domProps: {
              "textContent": _vm._s(work.ship_name)
            }
          })]);
        }), 0)];
      }
    }], null, false, 3347347189)
  }), _c('el-table-column', {
    attrs: {
      "prop": "remarks",
      "label": "备注"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "work_status",
      "label": "状态"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return [scope.row.status == 10 ? _c('div', {
          staticStyle: {
            "display": "flex",
            "flex-direction": "row"
          }
        }, [_c('i', {
          staticClass: "el-icon-video-play",
          staticStyle: {
            "font-size": "30px",
            "color": "#00DB00"
          }
        }), _c('div', {
          staticStyle: {
            "margin-left": "5px",
            "height": "30px",
            "line-height": "30px"
          }
        }, [_c('span', {
          staticStyle: {
            "font-size": "20px"
          }
        }, [_vm._v("工作中")])])]) : _vm._e(), scope.row.status == 20 ? _c('div', {
          staticStyle: {
            "display": "flex",
            "flex-direction": "row"
          }
        }, [_c('i', {
          staticClass: "el-icon-video-pause",
          staticStyle: {
            "font-size": "30px",
            "color": "red"
          }
        }), _c('div', {
          staticStyle: {
            "margin-left": "5px",
            "height": "30px",
            "line-height": "30px"
          }
        }, [_c('span', {
          staticStyle: {
            "font-size": "20px"
          }
        }, [_vm._v("暂停")])])]) : _vm._e(), scope.row.status == 30 ? _c('div', {
          staticStyle: {
            "display": "flex",
            "flex-direction": "row"
          }
        }, [_c('i', {
          staticClass: "el-icon-circle-check",
          staticStyle: {
            "font-size": "30px",
            "color": "#0080FF"
          }
        }), _c('div', {
          staticStyle: {
            "margin-left": "5px",
            "height": "30px",
            "line-height": "30px"
          }
        }, [_c('span', {
          staticStyle: {
            "font-size": "20px"
          }
        }, [_vm._v("完成")])])]) : _vm._e()];
      }
    }], null, false, 4064544140)
  })], 1)]], 2), _c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "center",
      "border-top": "1px solid #D2D2D2",
      "padding-top": "15px"
    }
  }, [_vm.work_status == 20 ? _c('el-button', {
    staticStyle: {
      "width": "200px",
      "height": "120px"
    },
    attrs: {
      "type": "warning",
      "plain": ""
    },
    on: {
      "click": _vm.continueWork
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("继续工作")])]), _c('div', {
    staticStyle: {
      "margin-top": "15px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v("(继续暂停工作)")])])]) : _vm._e(), _vm.work_status == 0 || _vm.work_status == 20 ? _c('el-button', {
    staticStyle: {
      "width": "200px",
      "height": "120px"
    },
    attrs: {
      "type": "primary",
      "plain": ""
    },
    on: {
      "click": _vm.workBegin
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("开始工作")])])]) : _vm._e(), _vm.work_status == 10 ? _c('el-button', {
    staticStyle: {
      "width": "200px",
      "height": "120px"
    },
    attrs: {
      "type": "danger",
      "plain": ""
    },
    on: {
      "click": _vm.stopWork
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("暂停工作")])]), _c('div', {
    staticStyle: {
      "margin-top": "15px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v("(开始其他工作)")])])]) : _vm._e(), _vm.work_status == 10 || _vm.work_status == 20 || _vm.work_status == 30 ? _c('el-button', {
    staticStyle: {
      "width": "200px",
      "height": "120px"
    },
    attrs: {
      "type": "success",
      "plain": ""
    },
    on: {
      "click": _vm.overWork
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("工作完成")])]), _c('div', {
    staticStyle: {
      "margin-top": "15px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v("(填报工作)")])])]) : _vm._e(), _c('el-button', {
    staticStyle: {
      "width": "200px",
      "height": "120px"
    },
    attrs: {
      "type": "info",
      "plain": ""
    },
    on: {
      "click": function ($event) {
        _vm.step_type = 2;
      }
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("关闭")])]), _c('div', {
    staticStyle: {
      "margin-top": "15px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    },
    domProps: {
      "textContent": _vm._s('(' + _vm.close_cnt + 'S)')
    }
  })])])], 1)]) : _vm._e()])]), _c('el-dialog', {
    attrs: {
      "title": "确定开始其他工作吗？",
      "visible": _vm.other_work_show
    },
    on: {
      "update:visible": function ($event) {
        _vm.other_work_show = $event;
      }
    }
  }, [_c('div', [_c('div', {
    staticStyle: {
      "text-align": "center"
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px",
      "color": "#FF0000"
    }
  }, [_vm._v("其他工作（无图纸）")])])])]), _c('div', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.otherWorkSave
    }
  }, [_vm._v("确 定")]), _c('el-button', {
    on: {
      "click": function ($event) {
        _vm.other_work_show = false;
      }
    }
  }, [_vm._v("取 消")])], 1)]), _c('el-dialog', {
    attrs: {
      "title": "确定开始工作吗？",
      "visible": _vm.begin_work_show
    },
    on: {
      "update:visible": function ($event) {
        _vm.begin_work_show = $event;
      }
    }
  }, [_c('div', [_c('div', {
    staticStyle: {
      "text-align": "center"
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px",
      "color": "#3a8ee6"
    }
  })])]), _c('div', {
    staticStyle: {
      "text-align": "center"
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("请扫描图纸上方二维码")])]), _c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "center",
      "margin-top": "10px"
    }
  }, [_c('input', {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.bom_key,
      expression: "bom_key"
    }],
    staticClass: "form-control",
    staticStyle: {
      "ime-mode": "disabled",
      "width": "200px"
    },
    attrs: {
      "id": "bom_key",
      "type": "text",
      "placeholder": "请扫描图纸二维码",
      "autoComplete": "off"
    },
    domProps: {
      "value": _vm.bom_key
    },
    on: {
      "keypress": _vm.handleKeypress,
      "input": function ($event) {
        if ($event.target.composing) return;
        _vm.bom_key = $event.target.value;
      }
    }
  }), _c('el-button', {
    attrs: {
      "type": "primary",
      "plain": "",
      "size": "small"
    },
    on: {
      "click": _vm.setBomFocus
    }
  }, [_vm._v("扫码")])], 1), _c('div', {
    staticStyle: {
      "margin-top": "10px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "16px",
      "color": "red"
    }
  }, [_vm._v("如果扫码没反应，请点击上方（扫码）按钮后再扫码")])])]), _c('div', [_c('el-table', {
    staticStyle: {
      "width": "100%",
      "font-size": "16px"
    },
    attrs: {
      "data": _vm.bom_list
    }
  }, [_c('el-table-column', {
    attrs: {
      "prop": "order_code",
      "label": "项目号"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "product_name",
      "label": "产品"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "product_code",
      "label": "产品号"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "model_name",
      "label": "型号"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "bom_name",
      "label": "工艺"
    }
  }), _c('el-table-column', {
    attrs: {
      "fixed": "right",
      "label": "操作",
      "width": "120"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return [_c('el-button', {
          attrs: {
            "type": "danger",
            "size": "small"
          },
          nativeOn: {
            "click": function ($event) {
              $event.preventDefault();
              return _vm.bom_list.splice(scope.$index, 1);
            }
          }
        }, [_vm._v(" 删除 ")])];
      }
    }])
  })], 1)], 1)]), _c('div', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.workBeginSave
    }
  }, [_vm._v("确 定")]), _c('el-button', {
    on: {
      "click": function ($event) {
        _vm.begin_work_show = false;
      }
    }
  }, [_vm._v("取 消")])], 1)]), _c('el-dialog', {
    attrs: {
      "title": "确定暂停工作吗？",
      "visible": _vm.stop_work_show
    },
    on: {
      "update:visible": function ($event) {
        _vm.stop_work_show = $event;
      }
    }
  }, [_c('div', [_c('el-descriptions', {
    staticClass: "margin-top",
    attrs: {
      "column": 2,
      "border": ""
    }
  }, [_c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 工作 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.stop_data.work_name)
    }
  })], 2)], 1)], 1), _c('div', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.stopWorkSave
    }
  }, [_vm._v("确 定")]), _c('el-button', {
    on: {
      "click": function ($event) {
        _vm.stop_work_show = false;
      }
    }
  }, [_vm._v("取 消")])], 1)]), _c('el-dialog', {
    attrs: {
      "title": "确定继续工作吗？",
      "visible": _vm.continue_work_show
    },
    on: {
      "update:visible": function ($event) {
        _vm.continue_work_show = $event;
      }
    }
  }, [_c('div', [_c('el-descriptions', {
    staticClass: "margin-top",
    attrs: {
      "column": 2,
      "border": ""
    }
  }, [_c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 工作 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.stop_data.work_name)
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 产品 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.stop_data.product_name)
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 客户 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.stop_data.customer_name)
    }
  })], 2)], 1)], 1), _c('div', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.continueWorkSave
    }
  }, [_vm._v("确 定")]), _c('el-button', {
    on: {
      "click": function ($event) {
        _vm.continue_work_show = false;
      }
    }
  }, [_vm._v("取 消")])], 1)]), _c('el-dialog', {
    attrs: {
      "title": "确定完成工作吗？",
      "visible": _vm.over_work_show,
      "fullscreen": true
    },
    on: {
      "update:visible": function ($event) {
        _vm.over_work_show = $event;
      }
    }
  }, [_c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "min-height": "80vh"
    }
  }, [_c('div', {
    staticStyle: {
      "width": "50%"
    }
  }, [_c('el-descriptions', {
    staticClass: "margin-top",
    attrs: {
      "column": 2,
      "border": ""
    }
  }, [_c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 工作 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.stop_data.work_name)
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 工作时长 ")]), _c('span', {
    domProps: {
      "textContent": _vm._s(_vm.stop_data.work_hour + ' 小时')
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 异常说明 ")]), _c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "placeholder": "请选择工作异常说明"
    },
    model: {
      value: _vm.stop_data_error,
      callback: function ($$v) {
        _vm.stop_data_error = $$v;
      },
      expression: "stop_data_error"
    }
  }, [_c('el-option', {
    key: "",
    attrs: {
      "label": "无异常",
      "value": ""
    }
  }), _vm._l(_vm.errors, function (item) {
    return _c('el-option', {
      key: item.name,
      attrs: {
        "label": item.name,
        "value": item.name
      }
    });
  })], 2)], 2)], 1), _c('div', [_c('div', {
    staticStyle: {
      "padding": "10px 0",
      "font-size": "18px"
    }
  }, [_c('span', [_vm._v("请选择工作内容")])]), _c('div', [_c('div', {
    staticStyle: {
      "margin-top": "20px",
      "display": "flex"
    }
  }, [_c('div', {
    staticClass: "check-ship",
    style: {
      borderColor: _vm.over_work_type == 1 ? '#FF8000' : '#D2D2D2',
      width: '240px'
    },
    on: {
      "click": function ($event) {
        _vm.over_work_type = 1;
      }
    }
  }, [_c('div', [_c('i', {
    staticClass: "el-icon-picture",
    style: {
      color: _vm.over_work_type == 1 ? '#FF8000' : '#D2D2D2'
    }
  })]), _c('div', [_c('span', {
    style: {
      color: _vm.over_work_type == 1 ? '#FF8000' : '#898989'
    }
  }, [_vm._v("图纸生产")])])]), _vm.stop_data.work_type == null ? _c('div', {
    staticClass: "check-ship",
    style: {
      borderColor: _vm.over_work_type == 2 ? '#FF8000' : '#D2D2D2',
      width: '240px'
    },
    on: {
      "click": function ($event) {
        _vm.over_work_type = 2;
      }
    }
  }, [_c('div', [_c('i', {
    staticClass: "el-icon-s-help",
    style: {
      color: _vm.over_work_type == 2 ? '#FF8000' : '#D2D2D2'
    }
  })]), _c('div', [_c('span', {
    style: {
      color: _vm.over_work_type == 2 ? '#FF8000' : '#898989'
    }
  }, [_vm._v("协同生产")])])]) : _vm._e(), _vm.stop_data.work_type == null ? _c('div', {
    staticClass: "check-ship",
    style: {
      borderColor: _vm.over_work_type == 3 ? '#FF8000' : '#D2D2D2',
      width: '240px'
    },
    on: {
      "click": function ($event) {
        _vm.over_work_type = 3;
      }
    }
  }, [_c('div', [_c('i', {
    staticClass: "el-icon-s-tools",
    style: {
      color: _vm.over_work_type == 3 ? '#FF8000' : '#D2D2D2'
    }
  })]), _c('div', [_c('span', {
    style: {
      color: _vm.over_work_type == 3 ? '#FF8000' : '#898989'
    }
  }, [_vm._v("返修")])])]) : _vm._e(), _vm.stop_data.work_type == null ? _c('div', {
    staticClass: "check-ship",
    style: {
      borderColor: _vm.over_work_type == 4 ? '#FF8000' : '#D2D2D2',
      width: '240px'
    },
    on: {
      "click": function ($event) {
        _vm.over_work_type = 4;
      }
    }
  }, [_c('div', [_c('i', {
    staticClass: "el-icon-warning",
    style: {
      color: _vm.over_work_type == 4 ? '#FF8000' : '#D2D2D2'
    }
  })]), _c('div', [_c('span', {
    style: {
      color: _vm.over_work_type == 4 ? '#FF8000' : '#898989'
    }
  }, [_vm._v("其他工作")])])]) : _vm._e()]), _vm.over_work_type == 1 ? _c('div', {
    staticStyle: {
      "margin-top": "20px",
      "display": "flex",
      "flex-wrap": "wrap"
    }
  }, _vm._l(_vm.ships, function (ship, ship_idx) {
    return _c('div', {
      key: ship_idx,
      staticClass: "check-ship",
      style: {
        borderColor: ship.sel == 1 ? '#0080FF' : '#D2D2D2'
      },
      on: {
        "click": function ($event) {
          ship.sel == 1 ? ship.sel = 0 : ship.sel = 1;
        }
      }
    }, [_c('div', [_c('i', {
      staticClass: "el-icon-circle-check",
      style: {
        color: ship.sel == 1 ? '#0080FF' : '#D2D2D2'
      }
    })]), _c('div', [_c('span', {
      style: {
        color: ship.sel == 1 ? '#0080FF' : '#898989'
      },
      domProps: {
        "textContent": _vm._s(ship.name)
      }
    })])]);
  }), 0) : _vm._e(), _vm.over_work_type == 2 ? _c('div', {
    staticStyle: {
      "margin-top": "20px",
      "display": "flex",
      "flex-wrap": "wrap"
    }
  }, _vm._l(_vm.xt_list, function (xt, xt_idx) {
    return _c('div', {
      key: xt_idx,
      staticClass: "check-ship",
      style: {
        borderColor: xt.sel == 1 ? '#0080FF' : '#D2D2D2'
      },
      on: {
        "click": function ($event) {
          return _vm.selXt(xt_idx);
        }
      }
    }, [_c('span', {
      style: {
        color: xt.sel == 1 ? '#0080FF' : '#898989'
      },
      domProps: {
        "textContent": _vm._s(xt.staff_name + '（' + xt.begin_time + '-' + xt.end_time + '）')
      }
    })]);
  }), 0) : _vm._e(), _vm.over_work_type == 3 ? _c('div', {
    staticStyle: {
      "margin-top": "20px",
      "display": "flex",
      "flex-wrap": "wrap"
    }
  }, _vm._l(_vm.repair_list, function (repair, repair_idx) {
    return _c('div', {
      key: repair_idx,
      staticClass: "check-ship",
      style: {
        borderColor: repair.sel == 1 ? '#0080FF' : '#D2D2D2',
        flexDirection: 'column'
      },
      on: {
        "click": function ($event) {
          repair.sel == 1 ? repair.sel = 0 : repair.sel = 1;
        }
      }
    }, [_c('span', {
      style: {
        color: repair.sel == 1 ? '#0080FF' : '#898989',
        fontSize: '20px'
      },
      domProps: {
        "textContent": _vm._s(repair.error_code + '/' + repair.product_name + '/' + repair.bom_name)
      }
    }), _c('span', {
      style: {
        color: repair.sel == 1 ? '#0080FF' : '#898989',
        fontSize: '20px'
      },
      domProps: {
        "textContent": _vm._s('贴签号:' + repair.error_code)
      }
    })]);
  }), 0) : _vm._e(), _vm.over_work_type == 4 ? _c('div', {
    staticStyle: {
      "margin-top": "20px",
      "display": "flex",
      "flex-wrap": "wrap"
    }
  }, _vm._l(_vm.types, function (t, t_idx) {
    return _c('div', {
      key: t_idx,
      staticClass: "check-ship",
      style: {
        borderColor: t.sel == 1 ? '#0080FF' : '#D2D2D2'
      },
      on: {
        "click": function ($event) {
          t.sel == 1 ? t.sel = 0 : t.sel = 1;
        }
      }
    }, [_c('div', [_c('i', {
      staticClass: "el-icon-circle-check",
      style: {
        color: t.sel == 1 ? '#0080FF' : '#D2D2D2'
      }
    })]), _c('div', [_c('span', {
      style: {
        color: t.sel == 1 ? '#0080FF' : '#898989'
      },
      domProps: {
        "textContent": _vm._s(t.name)
      }
    })])]);
  }), 0) : _vm._e()])])], 1), _vm.over_work_type == 1 ? _c('div', {
    staticStyle: {
      "width": "50%"
    }
  }, [_c('div', {
    staticStyle: {
      "text-align": "center"
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("请扫描图纸上方二维码")])]), _c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "center",
      "margin-top": "10px"
    }
  }, [_c('input', {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.bom_key,
      expression: "bom_key"
    }],
    staticClass: "form-control",
    staticStyle: {
      "ime-mode": "disabled",
      "width": "200px"
    },
    attrs: {
      "id": "over_bom_key",
      "type": "text",
      "placeholder": "请扫描图纸二维码",
      "autoComplete": "off"
    },
    domProps: {
      "value": _vm.bom_key
    },
    on: {
      "keypress": _vm.handleKeypress,
      "input": function ($event) {
        if ($event.target.composing) return;
        _vm.bom_key = $event.target.value;
      }
    }
  }), _c('el-button', {
    attrs: {
      "type": "primary",
      "plain": "",
      "size": "small"
    },
    on: {
      "click": _vm.setOverBomFocus
    }
  }, [_vm._v("点击扫码")])], 1), _c('div', {
    staticStyle: {
      "margin-top": "10px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "16px",
      "color": "red"
    }
  }, [_vm._v("如果扫码没反应，请点击上方（扫码）按钮后再扫码")])])]), _c('div', [_c('el-table', {
    staticStyle: {
      "width": "100%",
      "font-size": "16px"
    },
    attrs: {
      "data": _vm.boms
    }
  }, [_c('el-table-column', {
    attrs: {
      "prop": "order_code",
      "label": "项目号"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "product_name",
      "label": "产品"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "product_code",
      "label": "产品号"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "model_name",
      "label": "型号"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "bom_name",
      "label": "工艺"
    }
  }), _c('el-table-column', {
    attrs: {
      "fixed": "right",
      "label": "操作",
      "width": "120"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return [_c('el-button', {
          attrs: {
            "type": "danger",
            "size": "small"
          },
          nativeOn: {
            "click": function ($event) {
              $event.preventDefault();
              return _vm.boms.splice(scope.$index, 1);
            }
          }
        }, [_vm._v(" 删除 ")])];
      }
    }], null, false, 2107747319)
  })], 1)], 1)]) : _vm._e()]), _c('div', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.overWorkSave
    }
  }, [_vm._v("确 定")]), _c('el-button', {
    on: {
      "click": function ($event) {
        _vm.over_work_show = false;
      }
    }
  }, [_vm._v("取 消")])], 1)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "./node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.check-ship[data-v-5f1e5064]{\n    padding: 10px;\n    font-size: 24px;\n    font-weight: 600;\n    border: 2px solid #D2D2D2;\n    min-width: 200px;\n    max-width: 300px;\n    border-radius: 10px;\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -ms-flex-pack: distribute;\n        justify-content: space-around;\n    margin-right: 30px;\n    cursor: pointer;\n    margin-bottom: 30px;\n}\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css */ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.id, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = (__webpack_require__(/*! !../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js")["default"])
var update = add("2676db8f", content, false, {"sourceMap":false,"shadowMode":false});
// Hot Module Replacement
if(false) // removed by dead control flow
{}

/***/ }),

/***/ "./src/view/ipc/index.vue":
/*!********************************!*\
  !*** ./src/view/ipc/index.vue ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_5f1e5064_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=5f1e5064&scoped=true */ "./src/view/ipc/index.vue?vue&type=template&id=5f1e5064&scoped=true");
/* harmony import */ var _index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js */ "./src/view/ipc/index.vue?vue&type=script&lang=js");
/* harmony import */ var _index_vue_vue_type_style_index_0_id_5f1e5064_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css */ "./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");



;


/* normalize component */

var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_5f1e5064_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _index_vue_vue_type_template_id_5f1e5064_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "5f1e5064",
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/view/ipc/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/view/ipc/index.vue?vue&type=script&lang=js":
/*!********************************************************!*\
  !*** ./src/view/ipc/index.vue?vue&type=script&lang=js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css":
/*!****************************************************************************************!*\
  !*** ./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5f1e5064_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css */ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5f1e5064_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5f1e5064_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5f1e5064_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5f1e5064_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./src/view/ipc/index.vue?vue&type=template&id=5f1e5064&scoped=true":
/*!**************************************************************************!*\
  !*** ./src/view/ipc/index.vue?vue&type=template&id=5f1e5064&scoped=true ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_5f1e5064_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_5f1e5064_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_5f1e5064_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=5f1e5064&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/ipc/index.vue?vue&type=template&id=5f1e5064&scoped=true");


/***/ })

}]);
//# sourceMappingURL=src_view_ipc_index_vue.9f12f5d4.js.map