<?php

namespace Envsan\Modules\Equ\Model;


use Envsan\Common\Model\BaseModel;
class EquItemShip extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $equ_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $ship_level;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $ship_type_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $ship_type_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'equ_item_ship';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquItemShip[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquItemShip
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
