<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Modules\Purchase\Service\PurchasePlanService;


/**
 * @name('采购用料计划')
 */
class PlanController extends SuperController
{

    /**
     * @name('列表')
     */
    public function listAction()
    {
        $ps = new PurchasePlanService();
        $rtn = $ps->getPlanData();
        $this->view->goods_data = json_encode($rtn['goods_data'],JSON_UNESCAPED_UNICODE);
        $this->view->day_list = json_encode($rtn['day_list'],JSON_UNESCAPED_UNICODE);
    }
}