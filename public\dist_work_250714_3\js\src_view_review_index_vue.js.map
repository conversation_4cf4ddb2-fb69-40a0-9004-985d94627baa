{"version": 3, "file": "js/src_view_review_index_vue.js", "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AC4EA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACxOA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACxCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/review/index.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/review/index.vue", "webpack://rrts-manager/./src/view/review/index.vue?f010", "webpack://rrts-manager/./src/view/review/index.vue?0678", "webpack://rrts-manager/./src/view/review/index.vue?6efa", "webpack://rrts-manager/./src/view/review/index.vue?af87", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/review/index.vue?7999", "webpack://rrts-manager/./src/view/review/index.vue?a715", "webpack://rrts-manager/./src/view/review/index.vue?95b1", "webpack://rrts-manager/./src/view/review/index.vue?c107", "webpack://rrts-manager/./src/view/review/index.vue?c966"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div>\r\n        <m-header :is_back=\"true\" name=\"审批\">\r\n            <div slot=\"right\" style=\"padding-right: 15px\">\r\n                <button slot=\"right\" class=\"btn-search\" @click=\"openSearch\"><van-icon name=\"filter-o\"/> 筛选</button>\r\n            </div>\r\n        </m-header>\r\n        <van-tabs v-model=\"active\" sticky swipeable color=\"#5B62E4\">\r\n            <van-tab title-style=\"font-size:16px;\" :badge=\"cnt_data._1 == 0 ? '' : cnt_data._1\">\r\n                <div slot=\"title\">\r\n                    <div style=\"height: 60px;width: 20vw\">\r\n                        <div style=\"text-align: center\">\r\n                            <van-icon name=\"todo-list-o\" size=\"30\"/>\r\n                        </div>\r\n                        <div style=\"text-align: center;margin-top: 5px\">待审批</div>\r\n                    </div>\r\n                </div>\r\n            </van-tab>\r\n            <van-tab title-style=\"font-size:16px;\" :badge=\"cnt_data._2 == 0 ? '' : cnt_data._2\" >\r\n                <div slot=\"title\">\r\n                    <div style=\"height: 60px;width: 20vw\">\r\n                        <div style=\"text-align: center\">\r\n                            <van-icon name=\"passed\" size=\"30\"/>\r\n                        </div>\r\n                        <div style=\"text-align: center;margin-top: 5px\">已审批</div>\r\n                    </div>\r\n                </div>\r\n            </van-tab>\r\n            <van-tab title-style=\"font-size:16px;\" :badge=\"cnt_data._3 == 0 ? '' : cnt_data._3\">\r\n                <div slot=\"title\">\r\n                    <div style=\"height: 60px;width: 20vw\">\r\n                        <div style=\"text-align: center\">\r\n                            <van-icon name=\"star-o\" size=\"30\"/>\r\n                        </div>\r\n                        <div style=\"text-align: center;margin-top: 5px\">已发起</div>\r\n                    </div>\r\n                </div>\r\n            </van-tab>\r\n            <van-tab title-style=\"font-size:16px;\" :badge=\"cnt_data._4 == 0 ? '' : cnt_data._4\">\r\n                <div slot=\"title\">\r\n                    <div style=\"height: 60px;width: 20vw\">\r\n                        <div style=\"text-align: center\">\r\n                            <van-icon name=\"bullhorn-o\" size=\"30\"/>\r\n                        </div>\r\n                        <div style=\"text-align: center;margin-top: 5px\">抄送于我</div>\r\n                    </div>\r\n                </div>\r\n            </van-tab>\r\n        </van-tabs>\r\n        <div class=\"review\" @scroll=\"scroll\" ref=\"scroll\">\r\n            <van-pull-refresh v-model=\"list_pull_loading\" @refresh=\"getList(1)\">\r\n                <van-list\r\n                        :immediate-check=\"false\"\r\n                        v-model=\"list_loading\"\r\n                        :finished=\"list_finished\"\r\n                        finished-text=\"没有更多了\"\r\n                        :offset=\"20\"\r\n                        @load=\"more\"\r\n                >\r\n                    <div v-for=\"(item,index) in list\" :key=\"index\" class=\"review-content\">\r\n                        <div class=\"title\">\r\n                            {{item.type_name}}\r\n                            <span v-if=\"item.review_type == 1\" style=\"color:red;\" >(撤销审批)</span>\r\n                            <van-tag v-if=\"item.status == 15\" type=\"primary\" size=\"large\">审批中</van-tag>\r\n                            <van-tag v-if=\"item.handle_status == 1\" type=\"success\" size=\"large\">通过</van-tag>\r\n                            <van-tag v-if=\"item.handle_status == 2\" type=\"danger\" size=\"large\">拒绝</van-tag>\r\n                            <van-tag v-if=\"item.handle_status == 3\" type=\"danger\" size=\"large\">撤回</van-tag>\r\n                            <van-tag v-if=\"item.pressing_flag > 0\" type=\"danger\" size=\"large\">催办 +{{item.pressing_flag}}</van-tag>\r\n                            <van-tag v-if=\"item.read_flag == 0\" type=\"danger\" size=\"large\">未读</van-tag>\r\n                        </div>\r\n                        <div class=\"content\">\r\n                            <div class=\"item\">\r\n                                <div class=\"title2\">\r\n                                    部门 :\r\n                                </div>\r\n                                <div class=\"value\">\r\n                                    {{item.group_name}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"content\">\r\n                            <div class=\"item\">\r\n                                <div class=\"title2\">\r\n                                    提交人 :\r\n                                </div>\r\n                                <div class=\"value\">\r\n                                    {{item.create_name}} {{item.create_date}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div  class=\"content\" v-for=\" (abstrakt_item,abstrakt_index) in item.abstrakt_data\" :key=\"abstrakt_index\">\r\n                            <div class=\"item\">\r\n                                <div class=\"title2\">\r\n                                    {{abstrakt_item.name}} :\r\n                                </div>\r\n                                <div class=\"value\">\r\n                                    {{abstrakt_item.value}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div v-if=\"item.remarks != ''\" class=\"content\">\r\n                            <div class=\"item\">\r\n                                <div class=\"title2\">\r\n                                    备注 :\r\n                                </div>\r\n                                <div class=\"value\">\r\n                                    {{item.remarks}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <van-cell title=\"查看详情\" title-style=\"font-size:16px\" is-link @click=\"view(item)\" />\r\n                    </div>\r\n                </van-list>\r\n                <div v-if=\"list.length == 0\" style=\"height: 60vh;width: 100%\">\r\n\r\n                </div>\r\n            </van-pull-refresh>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import base from '../../components/base';\r\n    import m_header from '../../components/header';\r\n\r\n    export default {\r\n        extends: base,\r\n        name: \"review\",\r\n        data () {\r\n            return {\r\n                search_show:false,\r\n                date_show:false,\r\n                offset:0,\r\n                limit:10,\r\n                pos:0,\r\n                active: 0,\r\n                list_loading: false,\r\n                list_pull_loading:false,\r\n                list_finished: false,\r\n                list:[],\r\n                cnt_data:{'_1' : 0,'_2' : 0,'_3' : 0,'_4' : 0}\r\n            };\r\n        },\r\n        components: {\r\n            m_header\r\n        },\r\n        created(){\r\n            this.$hub.$on('refreshlist', () => {\r\n                this.offset = 0;\r\n                this.list_finished = false;\r\n                this.getList();\r\n            });\r\n        },\r\n        methods:{\r\n            onLoad(){\r\n                this.active = Math.round(this.$route.params.type-1);\r\n                this.offset = 0;\r\n                this.list = [];\r\n                this.list_loading = false;\r\n                this.list_finished = false;\r\n                this.getList();\r\n            },\r\n            onShow(){\r\n                this.$refs['scroll'].scrollTop = this.pos;\r\n            },\r\n            getList(t = ''){\r\n                if (t == ''){\r\n                    this.list_loading = true;\r\n                } else {\r\n                    this.list_pull_loading = true;\r\n                }\r\n                this.offset = 0;\r\n                this.list_finished = false;\r\n                let me = this;\r\n                let type = this.active +1;\r\n                this.$http.get('work/review/list?limit='+this.limit+'&offset='+this.offset, {type:type}).then((rs) => {\r\n                    let data = rs.data;\r\n                    me.list_loading = false;\r\n                    me.list_pull_loading = false;\r\n                    me.list = data.rows;\r\n                    me.cnt_data = data.cnt_data;\r\n                    if (data.rows.length < this.limit){\r\n                        me.list_finished = true;\r\n                    } else {\r\n                        me.offset += data.paginator.limit;\r\n                    }\r\n                    this.$hub.$emit('setbadge',data.cnt_data);\r\n                });\r\n            },\r\n            more(){\r\n                if (this.list_pull_loading){\r\n                    return;\r\n                }\r\n                this.list_loading = true;\r\n                this.list_finished = false;\r\n                let me = this;\r\n                let type = this.active +1;\r\n                this.$http.get('work/review/list/more?limit='+this.limit+'&offset='+this.offset, {type:type}).then((rs) => {\r\n                    console.log(rs);\r\n                    let data = rs.data;\r\n                    me.list_loading = false;\r\n                    for(let i =0; i < data.rows.length; i++){\r\n                        me.list.push(data.rows[i]);\r\n                    }\r\n                    if (data.rows.length < this.limit){\r\n                        me.list_finished = true;\r\n                    } else {\r\n                        me.offset += data.paginator.limit;\r\n                    }\r\n                });\r\n            },\r\n            scroll(event) {\r\n                this.pos = event.target.scrollTop;\r\n            },\r\n            view(item){\r\n                if (item.status == 10){\r\n                    this.$router.push({ name: 'work/request', params: {uid:item.uid}})\r\n                } else {\r\n                    this.$router.push({name: 'work',params: { uid: item.uid,type : 2,src : 1}});\r\n                }\r\n            },\r\n            openSearch(){\r\n                this.$router.push({name: 'review/search'});\r\n            }\r\n        },\r\n        watch:{\r\n            active(val){\r\n                this.offset = 0;\r\n                this.list_finished = false;\r\n                this.getList();\r\n            }\r\n        }\r\n    }\r\n\r\n</script>\r\n<style>\r\n    .van-tab__text--ellipsis {\r\n        display: block;\r\n    }\r\n    .van-tabs--line .van-tabs__wrap {\r\n        height: 80px;\r\n    }\r\n\r\n    .van-info {\r\n        top: 5px;\r\n        right: 20px;\r\n        font-size: 14px;\r\n        padding: 1px 4px;\r\n    }\r\n</style>\r\n<style scoped>\r\n    .btn-search {\r\n        border: 0;\r\n        background-color: transparent;\r\n        color: #FFFFFF;\r\n    }\r\n\r\n    .review{\r\n        position: absolute;\r\n        top:140px;\r\n        left: 0;\r\n        width: 100%;\r\n        height: calc(100vh - 150px);\r\n        overflow: auto;\r\n    }\r\n\r\n    .review-content{\r\n        margin: 15px;\r\n        background-color: #FFFFFF;\r\n        border-radius: 6px;\r\n        overflow: hidden;\r\n        position: relative;\r\n    }\r\n\r\n    .review-content .title{\r\n        color: #000000;\r\n        padding: 10px 15px;\r\n        font-size: 18px;\r\n    }\r\n\r\n    .review-content .reject{\r\n        position: absolute;\r\n        top:22px;\r\n        right: 0;\r\n        width: 80px;\r\n        height: 30px;\r\n        transform:rotate(40deg)\r\n    }\r\n\r\n    .review-content .content{\r\n        border-bottom: 1px #F2F2F2 solid;\r\n        padding: 5px 0;\r\n    }\r\n\r\n    .review-content .content .item{\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-start;\r\n        padding: 1px 15px;\r\n    }\r\n    .review-content .content .item .title2{\r\n        width: 100px;\r\n        height: 25px;\r\n        line-height: 25px;\r\n        vertical-align: center;\r\n        color: #888888;\r\n    }\r\n\r\n    .review-content .content .item .value{\r\n        height: 25px;\r\n        line-height: 25px;\r\n        vertical-align: center;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('m-header',{attrs:{\"is_back\":true,\"name\":\"审批\"}},[_c('div',{staticStyle:{\"padding-right\":\"15px\"},attrs:{\"slot\":\"right\"},slot:\"right\"},[_c('button',{staticClass:\"btn-search\",attrs:{\"slot\":\"right\"},on:{\"click\":_vm.openSearch},slot:\"right\"},[_c('van-icon',{attrs:{\"name\":\"filter-o\"}}),_vm._v(\" 筛选\")],1)])]),_c('van-tabs',{attrs:{\"sticky\":\"\",\"swipeable\":\"\",\"color\":\"#5B62E4\"},model:{value:(_vm.active),callback:function ($$v) {_vm.active=$$v},expression:\"active\"}},[_c('van-tab',{attrs:{\"title-style\":\"font-size:16px;\",\"badge\":_vm.cnt_data._1 == 0 ? '' : _vm.cnt_data._1}},[_c('div',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_c('div',{staticStyle:{\"height\":\"60px\",\"width\":\"20vw\"}},[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('van-icon',{attrs:{\"name\":\"todo-list-o\",\"size\":\"30\"}})],1),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"5px\"}},[_vm._v(\"待审批\")])])])]),_c('van-tab',{attrs:{\"title-style\":\"font-size:16px;\",\"badge\":_vm.cnt_data._2 == 0 ? '' : _vm.cnt_data._2}},[_c('div',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_c('div',{staticStyle:{\"height\":\"60px\",\"width\":\"20vw\"}},[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('van-icon',{attrs:{\"name\":\"passed\",\"size\":\"30\"}})],1),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"5px\"}},[_vm._v(\"已审批\")])])])]),_c('van-tab',{attrs:{\"title-style\":\"font-size:16px;\",\"badge\":_vm.cnt_data._3 == 0 ? '' : _vm.cnt_data._3}},[_c('div',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_c('div',{staticStyle:{\"height\":\"60px\",\"width\":\"20vw\"}},[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('van-icon',{attrs:{\"name\":\"star-o\",\"size\":\"30\"}})],1),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"5px\"}},[_vm._v(\"已发起\")])])])]),_c('van-tab',{attrs:{\"title-style\":\"font-size:16px;\",\"badge\":_vm.cnt_data._4 == 0 ? '' : _vm.cnt_data._4}},[_c('div',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_c('div',{staticStyle:{\"height\":\"60px\",\"width\":\"20vw\"}},[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('van-icon',{attrs:{\"name\":\"bullhorn-o\",\"size\":\"30\"}})],1),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"5px\"}},[_vm._v(\"抄送于我\")])])])])],1),_c('div',{ref:\"scroll\",staticClass:\"review\",on:{\"scroll\":_vm.scroll}},[_c('van-pull-refresh',{on:{\"refresh\":function($event){return _vm.getList(1)}},model:{value:(_vm.list_pull_loading),callback:function ($$v) {_vm.list_pull_loading=$$v},expression:\"list_pull_loading\"}},[_c('van-list',{attrs:{\"immediate-check\":false,\"finished\":_vm.list_finished,\"finished-text\":\"没有更多了\",\"offset\":20},on:{\"load\":_vm.more},model:{value:(_vm.list_loading),callback:function ($$v) {_vm.list_loading=$$v},expression:\"list_loading\"}},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"review-content\"},[_c('div',{staticClass:\"title\"},[_vm._v(\" \"+_vm._s(item.type_name)+\" \"),(item.review_type == 1)?_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(\"(撤销审批)\")]):_vm._e(),(item.status == 15)?_c('van-tag',{attrs:{\"type\":\"primary\",\"size\":\"large\"}},[_vm._v(\"审批中\")]):_vm._e(),(item.handle_status == 1)?_c('van-tag',{attrs:{\"type\":\"success\",\"size\":\"large\"}},[_vm._v(\"通过\")]):_vm._e(),(item.handle_status == 2)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"拒绝\")]):_vm._e(),(item.handle_status == 3)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"撤回\")]):_vm._e(),(item.pressing_flag > 0)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"催办 +\"+_vm._s(item.pressing_flag))]):_vm._e(),(item.read_flag == 0)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"未读\")]):_vm._e()],1),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 部门 : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(item.group_name)+\" \")])])]),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 提交人 : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(item.create_name)+\" \"+_vm._s(item.create_date)+\" \")])])]),_vm._l((item.abstrakt_data),function(abstrakt_item,abstrakt_index){return _c('div',{key:abstrakt_index,staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" \"+_vm._s(abstrakt_item.name)+\" : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(abstrakt_item.value)+\" \")])])])}),(item.remarks != '')?_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 备注 : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(item.remarks)+\" \")])])]):_vm._e(),_c('van-cell',{attrs:{\"title\":\"查看详情\",\"title-style\":\"font-size:16px\",\"is-link\":\"\"},on:{\"click\":function($event){return _vm.view(item)}}})],2)}),0),(_vm.list.length == 0)?_c('div',{staticStyle:{\"height\":\"60vh\",\"width\":\"100%\"}}):_vm._e()],1)],1)],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.van-tab__text--ellipsis {\\n    display: block;\\n}\\n.van-tabs--line .van-tabs__wrap {\\n    height: 80px;\\n}\\n.van-info {\\n    top: 5px;\\n    right: 20px;\\n    font-size: 14px;\\n    padding: 1px 4px;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.btn-search[data-v-3278897c] {\\n    border: 0;\\n    background-color: transparent;\\n    color: #FFFFFF;\\n}\\n.review[data-v-3278897c]{\\n    position: absolute;\\n    top:140px;\\n    left: 0;\\n    width: 100%;\\n    height: calc(100vh - 150px);\\n    overflow: auto;\\n}\\n.review-content[data-v-3278897c]{\\n    margin: 15px;\\n    background-color: #FFFFFF;\\n    border-radius: 6px;\\n    overflow: hidden;\\n    position: relative;\\n}\\n.review-content .title[data-v-3278897c]{\\n    color: #000000;\\n    padding: 10px 15px;\\n    font-size: 18px;\\n}\\n.review-content .reject[data-v-3278897c]{\\n    position: absolute;\\n    top:22px;\\n    right: 0;\\n    width: 80px;\\n    height: 30px;\\n    transform:rotate(40deg)\\n}\\n.review-content .content[data-v-3278897c]{\\n    border-bottom: 1px #F2F2F2 solid;\\n    padding: 5px 0;\\n}\\n.review-content .content .item[data-v-3278897c]{\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-start;\\n    padding: 1px 15px;\\n}\\n.review-content .content .item .title2[data-v-3278897c]{\\n    width: 100px;\\n    height: 25px;\\n    line-height: 25px;\\n    vertical-align: center;\\n    color: #888888;\\n}\\n.review-content .content .item .value[data-v-3278897c]{\\n    height: 25px;\\n    line-height: 25px;\\n    vertical-align: center;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3278897c&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"1ff256e1\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3278897c&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3278897c&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&id=3278897c&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"b8fca16a\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&id=3278897c&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&id=3278897c&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=3278897c&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3278897c&lang=css\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=3278897c&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3278897c\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('3278897c')) {\n      api.createRecord('3278897c', component.options)\n    } else {\n      api.reload('3278897c', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=3278897c&scoped=true\", function () {\n      api.rerender('3278897c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/review/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3278897c&lang=css\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&id=3278897c&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=3278897c&scoped=true\""], "names": [], "sourceRoot": ""}