{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="form-group">
                        <label><span class="required">*</span>版本号：</label>
                        <div>
                            <input type="text" class="form-control" name="code" v-model="code" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>版本说明：</label>
                        <div>
                            <textarea class="form-control" name="remarks" v-model="remarks" maxlength="200" rows="3" style="resize: none;"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label><span class="required"> * </span>上传图纸(PDF文件)：</label>
                        <div style="display: flex;align-items: center;justify-content: flex-end">
                            <div v-if="drawing_url" style="margin-right: 10px;">
                                <a :href="base_path + drawing_url" target="_blank" v-text="drawing_name"></a>
                            </div>
                            <div>
                                <div id="dnd_drawing" class="rv-img-item rv-img-dnd">
                                    <div><i class="fa fa-plus"></i></div>
                                    <div>拖拽此处上传</div>
                                </div>
                                <div class="btn btn-primary" style="width: 150px;margin-top: 10px" onclick="upyunUpload()"><i class="fa fa-upload"></i> 上传</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>
{{ partial('upyun_uploader') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonVersion }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() ){
                    return;
                }

                if ((this.drawing_url || '') == '') {
                    alertWarning('请上传图纸');
                    return;
                }

                {% if dispatcher.getActionName() == 'uploadedit' %}
                var url = '{{ url('mes/drawing/uploadedit/' ~ uid ~ '/' ~ version_uid) }}';
                {% else %}
                var url = '{{ url('mes/drawing/upload/' ~ uid) }}';
                {% endif %}

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result2 = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    } else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            }
        }
    });

    initUpyunUpLoader('drawing', 'pdf');
    function upyunUploadSuccess(rs) {
        closeSpin(null);
        toastr.success("上传成功！");
        app.drawing_name = rs.file_name;
        app.drawing_url = rs.file_url;
    }
</script>
<style>
    .rv-img-dnd {
        width: 100%;
        height: 120px;
        border: 2px dotted #9d9d9d;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #666;
    }

    .rv-img-dnd i {
        font-size: 30px;
    }
</style>