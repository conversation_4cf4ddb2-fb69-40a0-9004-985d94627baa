{"version": 3, "file": "js/src_view_produce_list_vue.js", "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACwEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;ACtOA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/produce/list.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/produce/list.vue", "webpack://rrts-manager/./src/view/produce/list.vue?5728", "webpack://rrts-manager/./src/view/produce/list.vue?65df", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/produce/list.vue?4045", "webpack://rrts-manager/./src/view/produce/list.vue?5627", "webpack://rrts-manager/./src/view/produce/list.vue?4b3e", "webpack://rrts-manager/./src/view/produce/list.vue?e036"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"工时统计\" is_back=\"1\"></m-header>\r\n        <m-body padding=\"false\">\r\n            <div>\r\n                <van-cell required title=\"选择工作日期\" :value=\"report_date\" @click=\"report_date_show = true\" is-link/>\r\n                <van-calendar v-model=\"report_date_show\" @confirm=\"onConfirm\" :min-date=\"minDate\" :max-date=\"maxDate\"/>\r\n            </div>\r\n            <div>\r\n                <div v-for=\"(report_item,report_idx) in report_list\" :key=\"report_item.uid\"\r\n                     class=\"review-content\">\r\n                    <div class=\"title\">\r\n                        <div>\r\n                            <span v-text=\"report_item.notice_code\"></span>/\r\n                            <span v-text=\"report_item.product_name\"></span>\r\n                        </div>\r\n                        <div>\r\n                            <van-tag v-if=\"report_item.shift_type == 1\" type=\"success\" size=\"large\">白班</van-tag>\r\n                            <van-tag v-if=\"report_item.shift_type == 2\" type=\"warning\" size=\"large\">晚班</van-tag>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"content\">\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                工艺名称:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"report_item.bom_name\"></span>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                工时:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"report_item.hour + '(小时)'\"></span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"content\">\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                生产数量:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"report_item.cnt + '(件)'\"></span>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                不合格数量:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"report_item.error_cnt + '(件)'\"></span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"content\">\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                不合格类型:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"report_item.error_type\"></span>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                不合格说明:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"report_item.error_remarks\"></span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div v-if=\"edit_flag == 1\" style=\"padding: 5px;display: flex;justify-content: flex-end\">\r\n                        <van-button style=\"margin-right: 15px\" plain icon=\"edit\" @click=\"editReport(report_item.uid)\" type=\"info\" size=\"small\">编辑</van-button>\r\n                        <van-button plain icon=\"delete-o\" type=\"danger\" @click=\"deleteReport(report_item.uid)\" size=\"small\">删除</van-button>\r\n                    </div>\r\n                </div>\r\n                <div v-for=\"(other_item,other_idx) in other_list\" :key=\"other_item.uid\"\r\n                     class=\"review-content\">\r\n                    <div class=\"title\">\r\n                        <span v-text=\"other_item.produce_type\"></span>\r\n                        <div>\r\n                            <van-tag v-if=\"other_item.shift_type == 1\" type=\"success\" size=\"large\">白班</van-tag>\r\n                            <van-tag v-if=\"other_item.shift_type == 2\" type=\"warning\" size=\"large\">晚班</van-tag>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"content\" style=\"flex-direction: column\">\r\n                        <div class=\"item\" style=\"width: 100%;justify-content: flex-start\">\r\n                            <div class=\"title2\">\r\n                                工时:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"other_item.hour + '(小时)'\"></span>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"item\"  style=\"width: 100%;justify-content: flex-start\">\r\n                            <div class=\"title2\">\r\n                                工作说明:\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                <span v-text=\"other_item.remarks\"></span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div v-if=\"edit_flag == 1\" style=\"padding: 5px;display: flex;justify-content: flex-end\">\r\n                        <van-button style=\"margin-right: 15px\" plain icon=\"edit\" @click=\"editOther(other_item.uid)\" type=\"info\" size=\"small\">编辑</van-button>\r\n                        <van-button plain icon=\"delete-o\" type=\"danger\" @click=\"deleteOther(other_item.uid)\" size=\"small\">删除</van-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {Dialog} from \"vant\";\r\n    import base from '../../components/base';\r\n    export default {\r\n        name: \"produceList\",\r\n        extends: base,\r\n        components: {},\r\n        data() {\r\n            return {\r\n                loading: true,\r\n                report_date_show:false,\r\n                edit_flag: 0,\r\n                report_date:'',\r\n                report_list:[],\r\n                other_list:[],\r\n                minDate: new Date(2025, 0, 1),\r\n                maxDate: new Date(),\r\n            }\r\n        },\r\n        methods: {\r\n            onLoad() {\r\n                this.init('');\r\n            },\r\n            onShow() {\r\n\r\n            },\r\n            init(report_date){\r\n                this.$http.post('/work/produce/list',{report_date:report_date}).then((rs) => {\r\n                    if (rs.status === 'ok') {\r\n                        this.report_date_show = false;\r\n                        this.edit_flag = rs.data.edit_flag;\r\n                        this.report_date = rs.data.report_date;\r\n                        this.report_list = rs.data.report_list;\r\n                        this.other_list = rs.data.other_list;\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '异常消息',\r\n                            message: rs.message,\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                }).catch(() => {\r\n                    Dialog.alert({\r\n                        title: '网络异常',\r\n                        message: '网络异常',\r\n                    }).then(() => {\r\n                        this.$router.back();\r\n                    });\r\n                });\r\n            },\r\n            onConfirm(date) {\r\n                this.report_date = date.Format('yyyy-MM-dd');\r\n                this.report_date_show = false;\r\n                this.init(this.report_date);\r\n            },\r\n            editReport(uid){\r\n                this.$router.push({name: 'produce/edit',params: {uid : uid,cb:()=>{\r\n                    this.init('');\r\n                }}});\r\n            },\r\n            deleteReport(uid){\r\n                Dialog.confirm({\r\n                    title: '确认',\r\n                    message: '确认删除吗？',\r\n                })\r\n                .then(() => {\r\n                    this.$http.post('work/produce/delete', {\r\n                        uid : uid\r\n                    }).then((rs) => {\r\n                        if (rs.status == 'ok'){\r\n                            this.$toast.success('操作成功');\r\n                            this.init('');\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$toast.fail('网络异常');\r\n                    });\r\n                })\r\n                .catch(() => {\r\n                    // on cancel\r\n                });\r\n            },\r\n            editOther(uid){\r\n                this.$router.push({name: 'produce/other',params: {uid : uid , cb:()=>{\r\n                    this.init('');\r\n                }}});\r\n            },\r\n            deleteOther(uid){\r\n                Dialog.confirm({\r\n                    title: '确认',\r\n                    message: '确认删除吗？',\r\n                })\r\n                .then(() => {\r\n                    this.$http.post('work/produce/deleteother', {\r\n                        uid : uid\r\n                    }).then((rs) => {\r\n                        if (rs.status == 'ok'){\r\n                            this.$toast.success('操作成功');\r\n                            this.init('');\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$toast.fail('网络异常');\r\n                    });\r\n                })\r\n                .catch(() => {\r\n                    // on cancel\r\n                });\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n\r\n    .review{\r\n        position: absolute;\r\n        top:140px;\r\n        left: 0;\r\n        width: 100%;\r\n        height: calc(100vh - 150px);\r\n        overflow: auto;\r\n    }\r\n\r\n    .review-content{\r\n        margin: 10px;\r\n        background-color: #FFFFFF;\r\n        border-radius: 6px;\r\n        overflow: hidden;\r\n        position: relative;\r\n    }\r\n\r\n    .review-content .title{\r\n        color: #000000;\r\n        padding: 10px 15px;\r\n        font-size: 18px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n    }\r\n\r\n    .review-content .reject{\r\n        position: absolute;\r\n        top:22px;\r\n        right: 0;\r\n        width: 80px;\r\n        height: 30px;\r\n        transform:rotate(40deg)\r\n    }\r\n\r\n    .review-content .content{\r\n        border-bottom: 1px #F2F2F2 solid;\r\n        padding: 5px 0;\r\n        display: flex;\r\n    }\r\n\r\n    .review-content .content .item{\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content:space-between;\r\n        padding: 1px 15px;\r\n        width: 50%;\r\n    }\r\n    .review-content .content .item .title2{\r\n        width: 100px;\r\n        height: 25px;\r\n        line-height: 25px;\r\n        vertical-align: center;\r\n        color: #888888;\r\n    }\r\n\r\n    .review-content .content .item .value{\r\n        height: 25px;\r\n        line-height: 25px;\r\n        vertical-align: center;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"工时统计\",\"is_back\":\"1\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[_c('div',[_c('van-cell',{attrs:{\"required\":\"\",\"title\":\"选择工作日期\",\"value\":_vm.report_date,\"is-link\":\"\"},on:{\"click\":function($event){_vm.report_date_show = true}}}),_c('van-calendar',{attrs:{\"min-date\":_vm.minDate,\"max-date\":_vm.maxDate},on:{\"confirm\":_vm.onConfirm},model:{value:(_vm.report_date_show),callback:function ($$v) {_vm.report_date_show=$$v},expression:\"report_date_show\"}})],1),_c('div',[_vm._l((_vm.report_list),function(report_item,report_idx){return _c('div',{key:report_item.uid,staticClass:\"review-content\"},[_c('div',{staticClass:\"title\"},[_c('div',[_c('span',{domProps:{\"textContent\":_vm._s(report_item.notice_code)}}),_vm._v(\"/ \"),_c('span',{domProps:{\"textContent\":_vm._s(report_item.product_name)}})]),_c('div',[(report_item.shift_type == 1)?_c('van-tag',{attrs:{\"type\":\"success\",\"size\":\"large\"}},[_vm._v(\"白班\")]):_vm._e(),(report_item.shift_type == 2)?_c('van-tag',{attrs:{\"type\":\"warning\",\"size\":\"large\"}},[_vm._v(\"晚班\")]):_vm._e()],1)]),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 工艺名称: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(report_item.bom_name)}})])]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 工时: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(report_item.hour + '(小时)')}})])])]),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 生产数量: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(report_item.cnt + '(件)')}})])]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 不合格数量: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(report_item.error_cnt + '(件)')}})])])]),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 不合格类型: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(report_item.error_type)}})])]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 不合格说明: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(report_item.error_remarks)}})])])]),(_vm.edit_flag == 1)?_c('div',{staticStyle:{\"padding\":\"5px\",\"display\":\"flex\",\"justify-content\":\"flex-end\"}},[_c('van-button',{staticStyle:{\"margin-right\":\"15px\"},attrs:{\"plain\":\"\",\"icon\":\"edit\",\"type\":\"info\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editReport(report_item.uid)}}},[_vm._v(\"编辑\")]),_c('van-button',{attrs:{\"plain\":\"\",\"icon\":\"delete-o\",\"type\":\"danger\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.deleteReport(report_item.uid)}}},[_vm._v(\"删除\")])],1):_vm._e()])}),_vm._l((_vm.other_list),function(other_item,other_idx){return _c('div',{key:other_item.uid,staticClass:\"review-content\"},[_c('div',{staticClass:\"title\"},[_c('span',{domProps:{\"textContent\":_vm._s(other_item.produce_type)}}),_c('div',[(other_item.shift_type == 1)?_c('van-tag',{attrs:{\"type\":\"success\",\"size\":\"large\"}},[_vm._v(\"白班\")]):_vm._e(),(other_item.shift_type == 2)?_c('van-tag',{attrs:{\"type\":\"warning\",\"size\":\"large\"}},[_vm._v(\"晚班\")]):_vm._e()],1)]),_c('div',{staticClass:\"content\",staticStyle:{\"flex-direction\":\"column\"}},[_c('div',{staticClass:\"item\",staticStyle:{\"width\":\"100%\",\"justify-content\":\"flex-start\"}},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 工时: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(other_item.hour + '(小时)')}})])]),_c('div',{staticClass:\"item\",staticStyle:{\"width\":\"100%\",\"justify-content\":\"flex-start\"}},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 工作说明: \")]),_c('div',{staticClass:\"value\"},[_c('span',{domProps:{\"textContent\":_vm._s(other_item.remarks)}})])])]),(_vm.edit_flag == 1)?_c('div',{staticStyle:{\"padding\":\"5px\",\"display\":\"flex\",\"justify-content\":\"flex-end\"}},[_c('van-button',{staticStyle:{\"margin-right\":\"15px\"},attrs:{\"plain\":\"\",\"icon\":\"edit\",\"type\":\"info\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editOther(other_item.uid)}}},[_vm._v(\"编辑\")]),_c('van-button',{attrs:{\"plain\":\"\",\"icon\":\"delete-o\",\"type\":\"danger\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.deleteOther(other_item.uid)}}},[_vm._v(\"删除\")])],1):_vm._e()])})],2)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.review[data-v-437aed8e]{\\n    position: absolute;\\n    top:140px;\\n    left: 0;\\n    width: 100%;\\n    height: calc(100vh - 150px);\\n    overflow: auto;\\n}\\n.review-content[data-v-437aed8e]{\\n    margin: 10px;\\n    background-color: #FFFFFF;\\n    border-radius: 6px;\\n    overflow: hidden;\\n    position: relative;\\n}\\n.review-content .title[data-v-437aed8e]{\\n    color: #000000;\\n    padding: 10px 15px;\\n    font-size: 18px;\\n    display: flex;\\n    justify-content: space-between;\\n}\\n.review-content .reject[data-v-437aed8e]{\\n    position: absolute;\\n    top:22px;\\n    right: 0;\\n    width: 80px;\\n    height: 30px;\\n    transform:rotate(40deg)\\n}\\n.review-content .content[data-v-437aed8e]{\\n    border-bottom: 1px #F2F2F2 solid;\\n    padding: 5px 0;\\n    display: flex;\\n}\\n.review-content .content .item[data-v-437aed8e]{\\n    display: flex;\\n    flex-direction: row;\\n    justify-content:space-between;\\n    padding: 1px 15px;\\n    width: 50%;\\n}\\n.review-content .content .item .title2[data-v-437aed8e]{\\n    width: 100px;\\n    height: 25px;\\n    line-height: 25px;\\n    vertical-align: center;\\n    color: #888888;\\n}\\n.review-content .content .item .value[data-v-437aed8e]{\\n    height: 25px;\\n    line-height: 25px;\\n    vertical-align: center;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=437aed8e&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"12b5fdbe\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=437aed8e&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=437aed8e&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./list.vue?vue&type=template&id=437aed8e&scoped=true\"\nimport script from \"./list.vue?vue&type=script&lang=js\"\nexport * from \"./list.vue?vue&type=script&lang=js\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=437aed8e&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"437aed8e\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('437aed8e')) {\n      api.createRecord('437aed8e', component.options)\n    } else {\n      api.reload('437aed8e', component.options)\n    }\n    module.hot.accept(\"./list.vue?vue&type=template&id=437aed8e&scoped=true\", function () {\n      api.rerender('437aed8e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/produce/list.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=437aed8e&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./list.vue?vue&type=template&id=437aed8e&scoped=true\""], "names": [], "sourceRoot": ""}