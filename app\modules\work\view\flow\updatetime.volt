{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('css').addCss('static/global/plugins/chosen/chosen.css') %}
{% do assets.collection('js').addJs('static/global/plugins/chosen/chosen.jquery.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jqzoom-master/css/jquery.jqzoom.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jqzoom-master/js/jquery.jqzoom-core.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>审批日期</label>
                                <div class="input-group" style="width: 225px">
                                    <input type="date" class="form-control date dtpicker" name="ctt_date" v-model="view_date"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>审批时间</label>
                                <div class="input-group" style="width: 225px">
                                    <input type="time" class="form-control date dtpicker" name="ctt_date" v-model="view_time"/>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            view_date: '{{ view_date }}',
            view_time:'{{ view_time }}'
        },
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                var url= '{{ url('work/flow/updatetime/'~r_id~'/'~index) }}';

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status == 'ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            }

        },
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check'
    });
</script>
