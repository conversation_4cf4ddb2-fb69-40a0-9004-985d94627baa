{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">物资查询</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">存货编号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="code" v-model="code"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">存货名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="name" v-model="name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">存货分类</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="type_name" v-model="type_name" readonly/>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default btn-flat" onclick="searchbytype()"><i class="fa fa-search"></i></button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('purchase/goodstype/search/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="code">存货编码</th>
                    <th data-field="name">存货名称</th>
                    <th data-field="spec">规格</th>
                    <th data-field="model">型号</th>
                    <th data-field="as_name">存货简称</th>
                    <th data-field="type_name">存货分类</th>
                    <th data-field="unit">单位</th>
                    <th data-field="taxation">税目</th>
                    <th data-field="warning_flag" data-formatter="statusFormatter">预警</th>
                    <th data-field="warning_quantity">预警值</th>
                    <th data-field="quantity" data-formatter="quantityFormatter">当前库存</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
    <div id="act" style="display: none;">
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                操作 <span class="caret"></span>
            </button>
            <ul class="dropdown-menu pull-right" role="menu">
                <li><a href="javascript:" onclick="batchStock('@id@')"><i class="fa fa-fw fa-eye"></i> 查看批次库存</a></li>
            </ul>
        </div>
    </div>
</div>


<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            code: '',
            type_idx:[],
            type_name:'',
            name: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.code = this.code;
                p.type_idx = this.type_idx;
                p.name = this.name;
                p.type_name = this.type_name;
                return p;
            },
            reset: function() {
                this.code = '';
                this.name = '';
                this.type_idx = [];
                this.type_name = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable();
    function actionFormatter(v, row, idx) {
        var actHtml = $('#act').html();
        return actHtml.replace(/@id@/g, row.id);
    }

    function quantityFormatter(v, row){
        return parseFloat(v) + '（'+row.unit+'）';
    }

    function statusFormatter(v, row, idx) {
        let rtn_html = '';
        if (v == 1) {
            rtn_html = '<span class="label label-warning">预警</span>';
        } else if (v == 0) {
            rtn_html = '<span class="label label-default">不预警</span>';
        }
        return rtn_html;
    }

    function batchStock(id) {
        top.window.layer_result='';
        top.layer.open({
            title:'查看批次库存',
            type: 2,
            area: ['60%', '80%'],
            content: '{{ url('work/stock/batch/') }}'+id,
            end:function(){
            }
        });
    }

    function searchbytype() {
        top.window.layer_result=[];
        top.layer.open({
            title:'选择商品品类',
            type: 2,
            area: makeArea('40em', '80%'),
            content: '{{ url('purchase/goodstype/searchbytype') }}',
            end:function(){
                if(top.window.layer_result.length > 0){
                    app.type_idx = [];
                    for(let a=0;a<top.window.layer_result.length;a++){
                        app.type_idx[a] = top.window.layer_result[a].id;
                    }
                    app.type_name = top.window.layer_result[0].name;
                    app.$nextTick(function() {
                        $('.bs-select').selectpicker('refresh');
                    });
                }
            }
        });
    }

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });

</script>