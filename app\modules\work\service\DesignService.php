<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Modules\Work\Model\WorkDataChangeLogs;
use Envsan\Modules\Work\Model\WorkDesign;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Work\Model\WorkForm;
use Envsan\Modules\Work\Util\Constant;
use Envsan\Modules\Work\Util\FormConstant;
use Phalcon\Mvc\User\Component;

class DesignService extends Component
{
    public function selectAll()
    {
        $group_id = $this->request->get('group_id', 'tstring');
        $type_name = $this->request->get('type_name', 'tstring');
        $in_out_type = $this->request->get('in_out_type', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.doc_type,
                t1.name,
                t1.status,
                t1.update_date,
                t2.real_name,
                t3.name as group_name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkDesign','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\User','t1.update_by = t2.id','t2')
            ->leftJoin('Envsan\Modules\Sys\Model\Group','t1.group_id = t3.id','t3')
            ->where('t1.del_flag=0 and t1.flow_type = 0 and t1.owner = ?1',[1=>SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $gs = new GroupService();
        $user = SessionData::user();
        $ids = $gs->selectGroupIds($user->group_id);
        $builder->inWhere('t1.create_group_id', $ids);

        if (!CheckUtil::is_empty($group_id)) {
            $builder->andWhere("t1.group_id = ?2", [2 => $group_id]);
        }
        if (!CheckUtil::is_empty($type_name)) {
            $builder->andWhere("t1.name like ?3", [3 => "%$type_name%"]);
        }
        if (!CheckUtil::is_empty($in_out_type)) {
            $builder->andWhere("t1.doc_type = ?4", [4 => $in_out_type]);
        }
        return $builder;
    }

    public function getFlowList($row){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkDesign','t1')
            ->where('t1.del_flag=0 and t1.status = 1 and t1.id <> ?1 and t1.owner = ?2',[1=>$row->id,2=>SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $gs = new GroupService();
        $user = SessionData::user();
        $group_ids = $gs->selectGroupIds($user->group_id);
        $builder->inWhere('t1.create_group_id', $group_ids);
        return $builder->getQuery()->execute();
    }

    public function getUserList(){
        $builder = $this->modelsManager->createBuilder()
        ->columns('
            t1.id,
            t1.real_name as name,
            t1.group_id,
            t2.name as group_name
        ')
        ->addFrom('Envsan\Modules\Sys\Model\User','t1')
        ->leftJoin('Envsan\Modules\Sys\Model\Group','t1.group_id = t2.id','t2')
        ->where('t1.del_flag = 0 and t1.role_id > 1 and t1.account_status = 0 and t1.owner = '.SessionData::ownerId())
        ->orderBy('t1.group_id,t1.id');
        ModelUtil::limitGroup('t1.group_id', $builder);
        $users = $builder->getQuery()->execute();
        $list = array();
        $group_id = '';
        $group = null;
        $cnt = 0;
        foreach ($users as  $row){
            if ($cnt == 0) {
                $group_id = $row->group_id;
                $group = new \stdClass();
                $group->id = $row->group_id;
                $group->name = $row->group_name;
                $group->list = [];
                array_push($list, $group);
            } else {
                if ($group_id != $row->group_id) {
                    $group_id = $row->group_id;
                    $group = new \stdClass();
                    $group->id = $row->group_id;
                    $group->name = $row->group_name;
                    $group->list = [];
                    array_push($list, $group);
                }
            }
            $cnt++;
            $user = new \stdClass();
            $user->id = $row->id;
            $user->name = $row->name;
            $user->checked = 0;
            array_push($list[count($list)-1]->list, $user);
        }
        return $list;
    }

    public function selectById($id)
    {
        return WorkDesign::findFirst(['del_flag=0 and id=?1', 'bind'=>[1=>$id]]);
    }

    public function save()
    {
        $id = $this->request->getPost('id', 'tstring');
        $name = $this->request->getPost('name', 'tstring');
        $flow = urldecode($this->request->getPost('flow', 'tstring')) ;
        $connects = urldecode($this->request->getPost('connects', 'tstring'));
        if(empty($name) || empty($flow) || empty($connects)){
            return ErrorHelper::WRONG_INPUT;
        }
        $user = SessionData::user();
        if(empty($id)){
            $row = new WorkDesign();
            $row->create_group_id = $user->group_id;
        } else {
            $row = $this->selectById($id);
            if (empty($row)){
                return ErrorHelper::WRONG_INPUT;
            }
        }
        $now = DateUtil::now();
        $row->name = $name;
        $row->design_anchor = $flow;
        $row->design_connects = $connects;
        $flow_obj = json_decode($flow,true);
        $flow_list = [];
        $flow_users = [];
        array_push($flow_users,[
            'anchor' => 'begin',
            'id' => '',
            'name' => '',
            'notify' => 0,
            'type' => 1,
            'status' => 1,
            'text' => '',
            'time' => ''
        ]);
        foreach ($flow_obj as $idx => $flow){
            $stock = 0;
            if (array_key_exists('stock',$flow)){
                $stock = $flow['stock'];
            }
            $target = $flow['target'];
            if ($idx+1 == count($flow_obj)){
                $target = '';
            }
            array_push($flow_list,[
                'id' => $flow['id'],
                'name' => $flow['name'],
                'type' => $flow['type'],
                'stock' => $stock,
                'target' => $target,
            ]);
            foreach ($flow['list'] as $anchor){
                array_push($flow_users,[
                    'anchor' => $flow['id'],
                    'id' => $anchor['id'],
                    'name' => $anchor['name'],
                    'notify' => $anchor['notify'],
                    'type' => 1,
                    'status' => 0,
                    'text' => '',
                    'time' => ''
                ]);
            }
        }
        $notify_flow_flag = 0;
        if (count($flow_list) == 1){
            $notify_flow_flag = 1;
            foreach ($flow_users as $flow_user){
                if ($flow_user['anchor'] != 'begin'){
                    if ($flow_user['notify'] == 0){
                        $notify_flow_flag = 0;
                        break;
                    }
                }
            }
        }
        $row->notify_flow_flag = $notify_flow_flag;
        $row->flow_list = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
        $row->flow_data = json_encode($flow_users,JSON_UNESCAPED_UNICODE);
        $row->del_flag = 0;
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->owner = $user->owner;
        if (!$row->save()) {
           return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function changeSave()
    {
        $old_user_id = $this->request->getPost('old_user_id', 'tstring');
        $new_user_id = $this->request->getPost('new_user_id', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        if(empty($old_user_id) || empty($new_user_id) || empty($remarks)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($old_user_id == $new_user_id){
            return '审批人没有变化';
        }
        $old_row = User::findFirstDirect(['del_flag = 0 and id = ?1','bind'=>[1=>$old_user_id]]);
        if (empty($old_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $new_row = User::findFirstDirect(['del_flag = 0 and id = ?1','bind'=>[1=>$new_user_id]]);
        if (empty($new_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $this->db->begin();
        try {
            $user = SessionData::user();
            $now = DateUtil::now();
            $design_rows = WorkDesign::find(['del_flag = 0 and owner = ?1', 'bind'=>[1 => $user->owner]]);
            $change_row = new WorkDataChangeLogs();
            $change_row->old_user_id = $old_row->id;
            $change_row->new_user_id = $new_row->id;
            $change_row->remarks = $remarks;
            $change_row->update_date = $now;
            $change_row->update_by = $user->id;
            if (!$change_row->save()) {
                throw new \Exception("【采购审批通过】WorkPurchase表更新失败");
            }
            foreach ($design_rows as $design_row){
                $design_anchor = CvtUtil::emptyToArray($design_row->design_anchor);
                $flow_data = CvtUtil::emptyToArray($design_row->flow_data);
                foreach ($design_anchor as &$design_item) {
                    foreach ($design_item['list'] as &$design) {
                        if ($design['id'] == $old_row->id) {
                            $design['id'] = $new_row->id;
                            $design['name'] = $new_row->real_name;
                        }
                    }
                }
                foreach ($flow_data as &$flow_item) {
                    if ($flow_item['id'] == $old_row->id) {
                        $flow_item['id'] = $new_row->id;
                        $flow_item['name'] = $new_row->real_name;
                    }
                }
                $design_row->design_anchor = json_encode($design_anchor,JSON_UNESCAPED_UNICODE);
                $design_row->flow_data = json_encode($flow_data,JSON_UNESCAPED_UNICODE);
                $design_row->update_date = $now;
                $design_row->update_by = $user->id;
                if (!$design_row->save()) {
                    throw new \Exception("【采购审批通过】WorkPurchase表更新失败");
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function bind($row){
        $name = $this->request->getPost('name', 'tstring');
        $doc_type = $this->request->getPost('doc_type', 'tstring');
        $status = $this->request->getPost('status', 'tstring');
        $group_id = $this->request->getPost('group_id', 'tstring');
        $form_id = $this->request->getPost('form_id', 'tstring');
        $data_status = $this->request->getPost('data_status');
        $flow_ids = $this->request->getPost('flow_ids');
        $flow_error_ids = $this->request->getPost('flow_error_ids');
        $flow_more_ids = $this->request->getPost('flow_more_ids');
        $abstrakt_keys = $this->request->getPost('abstrakt_keys');
        $hidden_list = $this->request->getPost('hidden_list');

        if(empty($name) || empty($group_id)){
            return ErrorHelper::WRONG_INPUT;
        }
        $group_row = Group::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$group_id]]);
        if (empty($group_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->name = $name;
        $row->doc_type = CvtUtil::blankToNull($doc_type);
        if (empty($row->doc_type)){
            $row->form_id = CvtUtil::blankToNull($form_id);
        } else {
            $row->form_id = null;
        }
        if (empty($status)){
            $row->status = 0;
        } else {
            $row->status = 1;
        }
        if (count($data_status) == 0){
            $row->data_status = null;
        } else {
            $row->data_status = json_encode($data_status,JSON_UNESCAPED_UNICODE);
        }
        if (empty($flow_ids)) {
            $row->flow_ids = null;
        } else{
            $row->flow_ids = json_encode($flow_ids);
        }
        if (empty($flow_error_ids)) {
            $row->flow_error_ids = null;
        } else{
            $row->flow_error_ids = json_encode($flow_error_ids);
        }
        if (empty($flow_more_ids)) {
            $row->flow_more_ids = null;
        } else{
            $row->flow_more_ids = json_encode($flow_more_ids);
        }

        if (empty($abstrakt_keys)) {
            $row->abstrakt_keys = null;
        } else{
            $row->abstrakt_keys = json_encode($abstrakt_keys,JSON_UNESCAPED_UNICODE);
        }

        if (empty($hidden_list)) {
            $row->hidden_data = null;
        } else{
            $row->hidden_data = json_encode($hidden_list,JSON_UNESCAPED_UNICODE);
        }


        $row->group_id = $group_row->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()) {
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function delete(){
        $id = $this->request->getPost('id');
        if (empty($id)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectById($id);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }


    public function getViewData($id)
    {
        $row = $this->selectById($id);
        if ($row == false || $row->del_flag == 1) {
            return false;
        }

        $form_uid = null;
        if (!empty($row->form_id)) {
            $form = WorkForm::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $row->form_id]]);
            if ($form != false) {
                $form_uid = $form->uid;
            }
        }

        $cnt = 0;
        $flow_map = [];
        $cols = [];

        $data = [];
        $data['id'] = $row->id.'_'.$cnt.'_';
        $data['name'] = $row->name;
        $data['type'] = 'point';
        $data['form_uid'] = $form_uid;

        array_push($cols, $data);
        $flow_map[$cnt] = $cols;

        $connect_list = [];
        $this->getSubFlow($row->id, $row->flow_ids, $flow_map, $connect_list, $cnt, 1);
        $this->getSubFlow($row->id, $row->flow_error_ids, $flow_map, $connect_list, $cnt, 0);

        $ret = [];
        $ret['flow_list'] = array_values($flow_map);
        $ret['connect_list'] = $connect_list;
        return $ret;
    }

    private function getSubFlow($main_id, $ids, &$flow_map, &$connect_list, $cnt, $flag)
    {
        if (empty($ids) || $cnt == 10) {
            return;
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.name,
                a.flow_ids,
                a.flow_error_ids,
                f.uid as form_uid
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkDesign', 'a')
            ->leftJoin('Envsan\Modules\Work\Model\WorkForm', 'a.form_id = f.id and f.del_flag = 0', 'f')
            ->where('a.del_flag = 0')
            ->inWhere('a.id', CvtUtil::emptyToArray($ids));
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0) {
            return;
        }

        $next_cnt = $cnt + 1;
        if (!array_key_exists($next_cnt, $flow_map)) {
            $flow_map[$next_cnt] = [];
        }
        $cols = $flow_map[$next_cnt];

        $label_id = $main_id.'_'.$next_cnt.'_'.$flag;
        $flag_val = substr($flag, -1);
        $data = [];
        $data['id'] = $label_id;
        $data['name'] = $flag_val == 1 ? '正常' : '异常';
        $data['type'] = 'label';
        $data['val'] = $flag_val;
        array_push($cols, $data);
        $flow_map[$next_cnt] = $cols;

        $connect = [];
        $connect['src'] = $main_id.'_'.$cnt.'_'.substr($flag, 0, strlen($flag) - 1);
        $connect['tar'] = $label_id;
        array_push($connect_list, $connect);

        $next_cnt = $next_cnt + 1;
        if (!array_key_exists($next_cnt, $flow_map)) {
            $flow_map[$next_cnt] = [];
        }
        $cols = $flow_map[$next_cnt];
        foreach ($rows as $row)
        {
            $point_id = $row->id.'_'.$next_cnt.'_'.$flag;
            $data = [];
            $data['id'] = $point_id;
            $data['name'] = $row->name;
            $data['type'] = 'point';
            $data['form_uid'] = $row->form_uid;
            array_push($cols, $data);

            $connect = [];
            $connect['src'] = $label_id;
            $connect['tar'] = $point_id;
            array_push($connect_list, $connect);

            $this->getSubFlow($row->id, $row->flow_ids, $flow_map, $connect_list, $next_cnt, $flag.'1');
            $this->getSubFlow($row->id, $row->flow_error_ids, $flow_map, $connect_list, $next_cnt, $flag.'0');
        }
        $flow_map[$next_cnt] = $cols;
    }

    public function getTempItems($doc_type){
        if (empty($doc_type)){
            return [];
        }
        $form_list = [];
        if (array_key_exists($doc_type,FormConstant::$form_data_list)){
            $form_item = FormConstant::$form_data_list[$doc_type];
            foreach ($form_item as $key => $item){
                $form_list[] = [
                    'key' => $key.'_1',
                    'name' => $item['name'],
                    'type' => 1
                ];
            }
        }
        if (array_key_exists($doc_type,FormConstant::$detail_data_list)){
            $form_item = FormConstant::$detail_data_list[$doc_type];
            foreach ($form_item as $key => $item){
                $form_list[] = [
                    'key' => $key.'_2',
                    'name' => $item['name'].'(明细)',
                    'type' => 2
                ];
            }
        }
        return $form_list;
    }

    public function getDesignFlowList($flow_list,$hidden_data){
        $flow_list = CvtUtil::emptyToArray($flow_list);
        $hidden_data = CvtUtil::emptyToArray($hidden_data);
        $list = [];
        foreach ($flow_list as $flow_item){
            $keys = [];
            if (array_key_exists($flow_item['id'],$hidden_data)){
                $keys = $hidden_data[$flow_item['id']]['keys'];
            }
            $list[$flow_item['id']] = [
                'id' => $flow_item['id'],
                'name' => $flow_item['name'],
                'keys' => $keys
            ];
        }
        return $list;
    }

    public function getChangeUserList(){
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                id , real_name as name
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User')
            ->where('del_flag = 0 and id <> ?1 and ifnull(role_id,0) > 1 and account_status = 0 and owner = ?2', [1 =>$user->id,2=>$user->owner]);
        return $builder->getQuery()->execute();
    }
}