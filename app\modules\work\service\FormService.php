<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Work\Model\WorkData;
use Envsan\Modules\Work\Model\WorkDesign;
use Envsan\Modules\Work\Model\WorkDelivery;
use Envsan\Modules\Work\Model\WorkForm;
use Envsan\Modules\Work\Model\WorkInstock;
use Envsan\Modules\Work\Util\Constant;
use Phalcon\Mvc\User\Component;

class FormService extends Component
{
    public function selectAll()
    {
        $name = $this->request->get('name', 'tstring');

        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.uid,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkForm','t1')
            ->where('t1.del_flag = 0 and t1.group_id = ?1',[1=>$user->group_id])
            ->orderBy('t1.id desc');
        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("t1.name like ?2", [2 => "%$name%"]);
        }
        return $builder;
    }

    public function create()
    {
        $row = new WorkForm();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = trim($this->request->getPost('name', 'string'));
        if (empty($name))
            return ErrorHelper::WRONG_INPUT;

        $user = SessionData::user();
        $row->name = $name;
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        $row->group_id = $user->group_id;

        if ($act == 'create') {
            $row->owner = $user->owner;
            $row->uid = UUID::make();
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function save($row){
        $data = urldecode($this->request->getPost('data', 'string'));
        $name = $this->request->getPost('name', 'string');
        if (empty($data)){
            return ErrorHelper::WRONG_INPUT;
        }
        $data = json_decode($data,true);
        $user = SessionData::user();
        $row->name = $name;
        $row->form_data = json_encode($data,JSON_UNESCAPED_UNICODE);
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectByUid($uid)
    {
        return WorkForm::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }

    public function selectById($id)
    {
        return WorkForm::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function delete(){
        $uid = $this->request->getPost('uid');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $design = WorkDesign::findFirst(['del_flag = 0 and form_id = ?1','bind'=>[1=>$row->id]]);
        if (!empty($design)){
            return '表单已经被使用';
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }
}