<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

class DataLogs extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $page_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $data_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $change_logs;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_data_logs';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return DataLogs[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return DataLogs
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
