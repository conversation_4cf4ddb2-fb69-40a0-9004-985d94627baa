<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'work']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->setJsonResponse();
        if($this->request->isPost()) {
            $this->moduleExistThenDie('work');

            $res = [
                [
                    'name' => '审批查询',
                    'identity' => 'work:data',
                    'action' => [
                        ['name' => '审批查询', 'identity' => 'work:data:search', 'comment' => ''],
                    ]
                ],
                [
                    'name' => '流程管理',
                    'identity' => 'work:design',
                    'action' => [
                        ['name' => '流程管理', 'identity' => 'work:design:list', 'comment' => ''],
                    ]
                ],
                [
                    'name' => '表单管理',
                    'identity' => 'work:form',
                    'action' => [
                        ['name' => '表单管理', 'identity' => 'work:form:list', 'comment' => ''],
                    ]
                ],
                [
                    'name' => '自定义审批',
                    'identity' => 'work:formdata',
                    'action' => [
                        ['name' => '自定义审批', 'identity' => 'work:formdata:list', 'comment' => ''],
                        ['name' => '自定义审批报表', 'identity' => 'work:formdata:reportlist', 'comment' => '']
                    ]
                ],
                [
                    'name' => '权限管理',
                    'identity' => 'work:role',
                    'action' => [
                        ['name' => '权限设定', 'identity' => 'work:role:userlist', 'comment' => ''],
                        ['name' => '权限管理', 'identity' => 'work:role:list', 'comment' => '']
                    ]
                ],
            ];

            $ret = new JsonData();
            $this->db->begin();
            try {
                $this->makePackage('work', '采购模块', '1.0', '管理采购信息');
                $module = $this->makeModule('work', '采购模块');
                foreach ($res as $controller) {
                    $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                    foreach ($controller['action'] as $action) {
                        $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                    }
                }
                $this->db->commit();
            } catch (\Exception $e) {
                $this->db->rollback();
                Logger::error($e->getMessage(), $e->getTraceAsString());
                $ret->message = '发生错误';
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}