{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}

{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    二级域名<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="domain" v-model="domain" required placeholder="用于用户唯一登录的域名，只能是英文和数字">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    套餐<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="plan" v-model="plan" required placeholder="输入用户购买的套餐类型">
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    联系人<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="contact" v-model="contact" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    单位<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="company" v-model="company" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    单位简称<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="short_name" v-model="short_name" required placeholder="请输入该单位的简称，比如中国移动有限公司简称中国移动">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    账户开始日期<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="datetime" class="form-control dtpicker" name="start_date" id="start_date" data-date-format="yyyy-mm-dd hh:ii" required>
                                        <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">
                                账户结束日期<span class="required"> * </span>
                            </label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="datetime" class="form-control dtpicker" name="end_date" id="end_date" data-date-format="yyyy-mm-dd hh:ii" required placeholder="在结束日期之后，用户将不能使用本系统">
                                    <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">
                                手机<span class="required"> * </span>
                            </label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="mobile" v-model="mobile" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">
                                座机
                            </label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="tel" v-model="tel">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">
                                地址
                            </label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="address" v-model="address">
                            </div>
                        </div>
                    </div>
                </div>



                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonOwner }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                this.start_date = $('#start_date').val();
                this.end_date = $('#end_date').val();

                if(this.end_date<=this.start_date)
                    return toastr.error('结束日期必须大于开始日期');

                {% if dispatcher.getActionName()=='edit' %}
                    var url= '{{ url('sys/owner/edit/'~owner.id) }}';
                {% else %}
                    var url= '{{ url('sys/owner/create') }}';
                {% endif %}

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });

    $('#start_date').val(app.start_date);
    $('#end_date').val(app.end_date);

    // 初始化dtpicker
    $('.dtpicker').datetimepicker({language: "zh-CN", todayBtn: true, autoclose: true});
</script>
