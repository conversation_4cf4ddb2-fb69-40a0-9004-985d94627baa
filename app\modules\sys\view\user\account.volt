{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="portlet box green">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="fa fa-address-card fa-fw"></i>我的账户
                    </div>
                    <div class="tools">
                        <a href="javascript:void(0);" class="collapse"> </a>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="formAccount" class="form-horizontal form-row-seperated">
                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-3">登录名</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">{{ user.login_name }}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">姓名</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">{{ user.real_name }}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">角色</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">{{ user.role_name }}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">组织</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">{{ user.group_name }}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">账号创建日期</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">{{ user.create_date }}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">邮箱</label>
                                <div class="col-md-9">
                                    <input name="email" id="email" type="email" class="form-control" placeholder="请输入您的邮箱地址，当密码丢失可以通过邮箱找回" v-model="user.email"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">手机</label>
                                <div class="col-md-9">
                                    <input name="mobile" id="mobile" type="text" class="form-control" placeholder="请输入您的手机号，非常重要" v-model="user.mobile"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">地址</label>
                                <div class="col-md-9">
                                    <input name="address" id="address" type="text" class="form-control" placeholder="请输入您的联系地址" v-model="user.address"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-offset-3 col-md-9">
                                    <button class="btn green" @click="saveDetail"><i class="fa fa-save"></i> 保存</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="portlet box blue">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="fa fa-key fa-fw"></i>修改密码
                    </div>
                    <div class="tools">
                        <a href="javascript:;" class="collapse"> </a>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal form-row-seperated">
                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-3">旧密码</label>
                                <div class="col-md-9">
                                    <input name="old_pwd" type="password" class="form-control" required placeholder="请输入您的旧密码" v-model.trim="old_pwd"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">新密码</label>
                                <div class="col-md-9">
                                    <input name="pwd1" id="pwd1" type="password" class="form-control" required minlength="2" placeholder="请输入您的新密码" v-model.trim="pwd1"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">确认新密码</label>
                                <div class="col-md-9">
                                    <input name="pwd2" id="pwd2" type="password" class="form-control" required equalTo="#pwd1" placeholder="请再次输入您的新密码" v-model.trim="pwd2"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-offset-3 col-md-9">
                                    <button class="btn blue" @click="savePwd"><i class="fa fa-save"></i> 保存</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            old_pwd:'', pwd1:'', pwd2:'',
            user: {{ jsonUser }}
        },
        methods: {
            savePwd: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                showSpin();
                $.post('{{ url('sys/user/changepwd') }}', app.$data, function (rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        toastr.success('您的密码已修改，下次登录时请使用新密码!');
                    }
                    else {
                        toastr.error('操作失败!' + rs.message);
                    }
                })
            },
            saveDetail: function (e) {
                e.preventDefault();
                if( !$('#formAccount').validate().form() )
                    return;

                showSpin();
                $.post('{{ url('sys/user/account') }}', app.user, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        toastr.success('操作成功!');
                    }
                    else {
                        toastr.error('操作失败!' + rs.message);
                    }
                })
            }
        }
    });
</script>
