{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">字段管理</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">字段名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="name" v-model="name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   data-mobile-responsive="true"
                   data-query-params="app.params"
                   data-pagination="true"
                   data-url="{{ url('mes/ship/fieldlist/json') }}"
                   data-page-size="10"
                   data-side-pagination="server"
                   class="table table-striped table-condensed">
                <thead class="bg-blue">
                <tr>
                    <th data-field="name">字段名称</th>
                    <th data-field="type_name">输入类型</th>
                    <th data-field="unit">数字单位</th>
                    <th data-field="explain">字段说明</th>
                    <th data-field="list" data-formatter="listFormatter">选择值</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu pull-right" role="menu">
            <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-fw fa-check"></i>&nbsp;编辑</a></li>
            <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-fw fa-times"></i>&nbsp;删除</a></li>
        </ul>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            name: ''
        },
        methods: {
            search: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params : function (p) {
                p.name = this.name;
                return p;
            },
            reset: function () {
                this.name = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    var $table = $('#table');
    $table.bootstrapTable();

    function actionFormatter(v, row, idx) {
        let actHtml = $('#act').html();
        return actHtml.replace(/@id@/g, row.id);
    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '字段管理',
            type: 2,
            area: ['40em', '80%'],
            content: '{{ url('mes/ship/fieldcreate') }}',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(id) {
        top.window.layer_result = '';
        top.layer.open({
            title: '字段管理',
            type: 2,
            area: ['40em', '80%'],
            content: '{{ url('mes/ship/fieldedit/') }}' + id,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function del(id) {
        var dlg = top.layer.confirm('确认字段未被使用？', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('mes/ship/fielddelete') }}", {id: id}, function (rs) {
                closeSpin();
                if (rs.status == 'ok') {
                    toastr.success('操作成功！');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }

    function listFormatter(list){
        if (list == null){
            return '-';
        }
        list = JSON.parse(list);
        let str = '';
        for(let item of list){
            str += item + '，';
        }
        return str;
    }

</script>