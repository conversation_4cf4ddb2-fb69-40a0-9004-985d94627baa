<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">订单信息</span>
                    </div>
                </div>
                <div class="portlet-body" style="min-height: 700px">
                    <form id="form" class="form-horizontal">
                        <div class="form-body">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">订单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">订单日期</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_date" v-model="order_date" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">供应商</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">采购请求单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="request_code" v-model="request_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">总金额</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="total_money" v-model="total_money" readonly>
                                                <span class="input-group-addon">元</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('view') }}
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" rows="3" readonly></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div id="app_batch" class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">订单明细</span>
                    </div>
                </div>
                <div class="portlet-body" style="min-height: 700px">
                    <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 15px">
                        <thead>
                        <tr>
                            <th>编码</th>
                            <th>名称</th>
                            <th>规格型号</th>
                            <th>采购数量</th>
                            <th>库存需求数量</th>
                            <th>未税单价</th>
                            <th>含税单价</th>
                            <th>未税总金额</th>
                            <th>含税总金额</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="row, index in detail_data">
                            <td v-text="row.code"></td>
                            <td v-text="row.name"></td>
                            <td v-text="row.model"></td>
                            <td><span v-text="row.purchase_quantity + '('+row.purchase_unit+')'"></span></td>
                            <td><span v-text="row.inventory_quantity + '('+row.inventory_unit+')'"></span></td>
                            <td><span v-text="row.price + '(元/'+row.purchase_unit+')'"></span></td>
                            <td><span v-text="row.price_hs + '(元/'+row.purchase_unit+')'"></span></td>
                            <td><span v-text="row.total_money + '(元)'"></span></td>
                            <td><span v-text="row.total_money_hs + '(元)'"></span></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el:'#app',
        data: {{ jsonOrder }},
        methods:{

        }
    });
</script>
