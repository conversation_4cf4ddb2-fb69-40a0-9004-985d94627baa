<?php

namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Purchase\Model\PurchaseGoodsType;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\GoodsService;

/**
 * @name('物料类型')
 */
class GoodstypeController extends SuperController
{

    /**
     * @name('物料列表')
     */
    public function listAction()
    {
        $gs = new GoodsTypeService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            return json_encode($gs->selectTree());
        }

        $this->view->tree_list = $gs->selectTree();
    }

    /**
     * @acl({'link':'purchase:goodstype:list'})
     */
    public function searchAction($type = '')
    {
        $gs = new GoodsTypeService();

        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new GoodsService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setDetail($page->rows->toArray());
            return json_encode($page);
        }

        $this->view->type_list = $gs->gettypeList();

    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $gs = new GoodsTypeService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $gs = new GoodsTypeService();
            $ret = new JsonData();
            $ret->message = $gs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $tree_list = $gs->selectTree();
        $this->view->jsonTree = json_encode($tree_list);
        $jrow = (new PurchaseGoodsType())->toArray();
        $jrow['pname'] = '';
        $jrow['pid'] = '';
        $jrow['p_code'] = '';
        $this->view->jsonGoodsType = json_encode($jrow);
    }

    /**
     * @acl({'link':'purchase:goodstype:create'})
     */
    function editAction($uid = 0)
    {
        $gs = new GoodsTypeService();
        $row = $gs->selectByUid($uid);
        if ($row == null) {
            die(ErrorHelper::WRONG_ID);
        }

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $gs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        if ($row->pid != 0) {
            // 编辑的时候要选出父组织的名称
            $prow = $gs->selectById($row->pid);
            if ($prow != null)
                $row->pname = $prow->name;
        }

        $this->view->goodstype = $row;
        $jrow = $row->toArray();
        $jrow['pname'] = (isset($row->pname) ? $row->pname : '');
        $this->view->jsonGoodsType = json_encode($jrow);
        $this->view->code = $row->code;
    }

    /**
     * @acl({'link':'purchase:goodstype:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $gs = new GoodsTypeService();
            $msg = $gs->deleteByUid($this->request->getPost('uid', 'string'));
            if ($msg == '')
                $ret->status = JsonData::STATUS_OK;
            else
                $ret->message = $msg;
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function takecodeAction(){
        $gs = new GoodsTypeService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $rtn = $gs->takeCode();
            $ret->message =$rtn->message;
            if (empty( $ret->message)){
                $ret->data =$rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function changelevelAction($uid){

        $row = PurchaseGoodsType::findFirst(['del_flag=0 and uid = ?4', 'bind' => [4 => $uid]]);
        return json_encode($row->toArray());
    }

    /**
     * @skipacl
     */
    public function changeAction($uid)
    {
        $this->setJsonResponse();
        $common = new CommonService();
        $jsonData = new JsonData();
        $jsonData->handleResult($common->getGoodsList($uid));
        return json_encode($jsonData);
    }

    /**
     * @skipacl
     */
    public function searchbytypeAction($uid = ''){

        $gs = new GoodsTypeService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $gs = new GoodsTypeService();
            $type_idx = $gs->gettypeIdx($uid);
            return json_encode($type_idx);
        }
        $this->view->jsonTree = $gs->selectTree();
    }
}