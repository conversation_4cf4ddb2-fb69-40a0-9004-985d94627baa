<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class MesPlan extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $plan_month;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $plan_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $plan_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $notice_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $user_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $equ_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $bom_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $plan_cnt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $plan_hour;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $plan_sort;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $entrust_id;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_plan';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesPlan[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesPlan
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
