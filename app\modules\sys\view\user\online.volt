{% do assets.collection('css').addCss('static/pages/css/search.css') %}
<!-- Content Header (Page header) -->
<div class="page-content">
    <h3 class="page-title">在线用户</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <div>
                <button class="btn btn-danger" id="force-offline"><i class="fa fa-times"></i> 强制下线</button>
            </div>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('sys/user/online/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-checkbox="true">ID</th>
                    <th data-field="id">ID</th>
                    <th data-field="session_id">会话</th>
                    <th data-field="empno">工号</th>
                    <th data-field="login_name">登录名</th>
                    <th data-field="real_name">姓名</th>
                    <th data-field="ip">IP</th>
                    <th data-field="login_date">登录日期</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    $table.bootstrapTable();

    function getIdSelections() {
        return $.map($table.bootstrapTable('getSelections'), function (row) {
            return row.id
        });
    }

    $('#force-offline').click(function (e) {
        var map = getIdSelections();
        var ids = '';
        $.each(map, function (e) {
            ids += map[e]+',';
        });

        showSpin();
        $.post("{{ url('sys/user/force-off') }}", {ids:ids}, function (rs) {
            closeSpin(null);
            if(rs.status=='ok'){
                toastr.success('已下线:'+rs.message);
                $table.bootstrapTable('refresh');
            }
        })
    });
</script>
