{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body">
            <div class="panel-title">设备状态一览</div>
            <div class="equ-panel">
                <div v-for="item in equ_list" class="equ-col">
                    <div class="equ-item">
                        <div v-text="item.code"></div>
                        <div style="margin: 0 5px;">-</div>
                        <div class="equ-status" :class="item.status_class"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet light">
        <div class="portlet-body">
            <div class="panel-title">设备状态变化信息</div>
            <div class="history-box">
                <div class="history-row" v-for="info in fault_history" v-text="info"></div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            equ_list: {{ equ_list }},
            fault_history: {{ fault_history }}
        },
    });
</script>
<style>
    .equ-panel {
        display: flex;
        flex-wrap: wrap;
    }

    .panel-title {
        font-size: 16px;
        margin-bottom: 10px;
        font-weight: bold
    }

    .equ-col {
        width: 10%;
        display: flex;
        margin-bottom: 15px;
    }

    .equ-item {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 3px;
        padding: 6px 12px;
    }

    .equ-status {
        width: 16px;
        height: 16px;
        border-radius: 100%;
        background-color: #26C281;
    }

    .equ-status.red {
        background-color: #e7505a;
    }

    .equ-status.yellow {
        background-color: #F7CA18;
    }

    .history-row {
        color: #26c281;
        font-size: 16px;
        margin-bottom: 5px;
    }
</style>