<?php
namespace Envsan\Modules\Purchase\Util;

class Constant
{
    public static $formula_input = ['1','2','3','4','5','6','7','8','9','0','.','+','-','*','/','(',')','|'];

    public static $stock_types = [
        1 => '采购入库',
        2 => '领料出库',
        3 => '其他入库',
        4 => '盘点'
    ];

    public static $purchase_request_status = [
        10 => '待提交',
        20 => '待采购',
        30 => '已采购'
    ];

    public static $purchase_request_type_arr = [
        1 => '库房发起',
        2 => '项目发起'
    ];

    public static $purchase_apply_status = [
        10 => '待采购',
        20 => '采购中',
        30 => '已采购'
    ];

    public static $purchase_apply_entrust_status = [
        10 => '待采用',
        20 => '已绑定',
        30 => '已采购'
    ];

    public static $purchase_order_status = [
        10 => '待提交',
        20 => '待审核',
        30 => '执行中',
        40 => '完成'
    ];

    public static $purchase_instock_status = [
        10 => '待提交',
        20 => '已入库'
    ];

    public static $purchase_outstock_status = [
        10 => '待提交',
        15 => '待审批',
        20 => '已出库'
    ];

    public static $purchase_other_status = [
        10 => '待提交',
        20 => '已入库'
    ];

    public static $purchase_inventory_status = [
        10 => '待提交',
        20 => '已盘点'
    ];

    public static $purchase_invoice_status = [
        10 => '待提交',
        20 => '已开票'
    ];
    public static $purchase_pay_status = [
        10 => '待提交',
        20 => '已付款'
    ];

    public static $purchase_receipt_status = [
        10 => '待提交',
        20 => '待审核',
        30 => '已审核',
        40 => '完成'
    ];

    public static $purchase_inspection_status = [
        10 => '待提交',
        20 => '待审核',
        30 => '完成',
    ];

    public static $purchase_type_arr = [
        1 => '普通',
        2 => '外委'
    ];

    public static $normal_purchase_types = [
        ['id' => '1', 'name' => '生产采购'],
        ['id' => '2', 'name' => '库房采购']
    ];
}