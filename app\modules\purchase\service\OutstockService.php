<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Purchase\Model\PurchaseOutstock;
use Envsan\Modules\Purchase\Model\PurchaseOutstockDetail;
use Envsan\Modules\Purchase\Model\PurchaseStockLogs;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Work\Service\DataCommonService;
use Envsan\Modules\Work\Service\WorkService;
use Phalcon\Mvc\User\Component;

class OutstockService extends BaseService
{
    private $page_id = 26;

    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t2.code as notice_code,
                t1.outstock_date,
                t1.outstock_user,
                t1.remarks,
                t1.ext_val
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOutstock', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't1.notice_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->andWhere('t1.create_by = ?2',[2=>SessionData::user()->id])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.outstock_date,
                t1.outstock_user,
                t1.remarks,
                t1.ext_val,
                t99.goods_code,
                t99.goods_name,
                t99.goods_spec,
                t99.goods_model,
                t99.goods_unit,
                t99.input_val,
                t99.quantity
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOutstock', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOutstockDetail', 't1.id = t99.outstock_id', 't99')
            ->where('t1.del_flag = 0 and t99.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectById($id)
    {
        return PurchaseOutstock::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseOutstock::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function create($from)
    {
        $row = new PurchaseOutstock();
        return $this->build('create', $row, $from);
    }

    public function update($row, $from)
    {
        return $this->build('update', $row, $from);
    }

    public function build($act, $row, $from)
    {

        return $this->executeInTransaction(function () use ($act, $row, $from) {
            $type = urldecode($this->request->getPost('type', ['string', 'trim']));
            // 产品ID
            $notice_id = $this->request->getPost('notice_id', 'tstring');
            // 产品ID
            $product_id = $this->request->getPost('product_id', 'tstring');
            // 产品的BOMID
//            $product_bom_id = $this->request->getPost('product_bom_id', 'tstring');
            $outstock_date = $this->request->getPost('outstock_date', 'tstring');
            $outstock_user = $this->request->getPost('outstock_user', 'tstring');
            $remarks = $this->request->getPost('remarks', 'tstring');
            $files = urldecode($this->request->getPost('files', ['string', 'trim']));
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
            $detail = str_replace('%2B', '+', urldecode($this->request->getPost('detail', ['string', 'trim'])));
            if (empty($type) || empty($outstock_date) || empty($detail) || empty($outstock_user)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $files = CvtUtil::emptyToArray($files);
            $ext_data = CvtUtil::emptyToArray($ext_data);
            $detail = CvtUtil::emptyToArray($detail);
            $now = DateUtil::now();
            $user = SessionData::user();

            $row->notice_id = CvtUtil::blankToNull($notice_id);
            $row->product_id = CvtUtil::blankToNull($product_id);
//            $row->product_bom_id = CvtUtil::blankToNull($product_bom_id);
            $row->outstock_date = $outstock_date;
            $row->outstock_user = $outstock_user;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->detail_data = CvtUtil::arrayToNull($detail);;
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);

            if ($type == 1) {
                $row->status = 10;
            } else {
                $row->status = 15;
            }
            $row->status_name = Constant::$purchase_outstock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $ss = new SequenceService();
                $row->code = $ss->useSequence(14);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            // 更新PurchaseOutstock
            $row->save();

            // 提交的场合
            if ($row->status == 15) {
                $this->passSave($row->id);
            }
        });

    }

    public function delete(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 10){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->del_flag = 1;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function cancelReview()
    {
        $row = $this->selectByUid($this->request->getPost('uid', 'tstring'));
        if (empty($row) || $row->del_flag == 1) {
            return ErrorHelper::ROW_NOTEXIST;
        } else if ($row->status != PurchaseOutstock::STATUS_REVIEW) {
            return ErrorHelper::WRONG_STATUS;
        }

        $ws = new WorkService();
        return $ws->cancel(6, $row->id);
    }

    public function cancel(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 20){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
             $outstock_rows = PurchaseOutstockDetail::find(['del_flag = 0 and outstock_id = ?1','bind'=>[1=>$row->id]]);
             if (!$outstock_rows->delete()){
                 throw new \Exception("PurchaseOutstockDetail表更新失败");
             }
             $log_rows = PurchaseStockLogs::find(['del_flag = 0 and data_type = 2 and data_id = ?1','bind'=>[1=>$row->id]]);
             if (!$log_rows->delete()){
                throw new \Exception("PurchaseOutstockDetail表更新失败");
             }
             $row->status = 10;
             $row->status_name = Constant::$purchase_outstock_status[$row->status];
             $row->update_date = $now;
             $row->update_by = $user->id;
             if (!$row->save()){
                 throw new \Exception("PurchaseOutstock表更新失败");
             }
             $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function getDetail($outstock_id){
        return PurchaseOutstockDetail::find(['del_flag = 0 and outstock_id = ?1','bind'=>[1=>$outstock_id]]);
    }

    public function getBomData()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $rtn = new \stdClass();
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id as order_bom_id,
                t1.name as bom_name,
                t1.product_id,
                t2.order_detail_id,
                t3.code as order_code,
                t2.name as product_name,
                t4.name as customer_name,
                t2.goods_data
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesOrderBom', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t.order_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t3.customer_id = t4.id','t4')
            ->where('t1.del_flag = 0 and t.uid = ?1', [1 => $uid]);
        $bom_rows = $builder->getQuery()->execute();
        if (count($bom_rows) == 0){
            $rtn->message = '数据不存在';
            return $rtn;
        }
        $bom_row = $bom_rows[0]->toArray();
        $goods_data = CvtUtil::emptyToArray($bom_row['goods_data']);
        $goods_ids = [];
        foreach ($goods_data as $goods_item){
            $goods_ids[] = $goods_item['id'];
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.name,
                a.code,
                a.spec,
                a.model,
                a.unit,
                a.price,
                a.formula_list,
                ifnull(s.name_as,\'\') as supplier_name,
                ifnull(t.quantity,0) as stock_cnt,
                1 as show
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a.supplier_id = s.id', 's')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewStock', 'a.id = t.goods_id', 't')
            ->where('a.del_flag = 0 and a.owner = :owner:', ['owner'=>SessionData::ownerId()])
            ->inWhere('a.id',$goods_ids)
            ->orderBy('a.code asc');
        $bom_row['goods_data'] = $builder->getQuery()->execute();
        $rtn->message = '';
        $rtn->data = $bom_row;
        return $rtn;
    }

    public function getBomDataByID($id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id as order_bom_id,
                t1.name as bom_name,
                t1.product_id,
                t2.order_detail_id,
                t3.code as order_code,
                t2.name as product_name,
                t4.name as customer_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesOrderBom', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t.order_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t3.customer_id = t4.id','t4')
            ->where('t1.del_flag = 0 and t.id = ?1', [1 => $id]);
        $bom_rows = $builder->getQuery()->execute();
        if (count($bom_rows) == 0){
            return null;
        }
        return $bom_rows[0];
    }

    public function rejectSave($id,$reject_remarks)
    {
        $row = $this->selectById($id);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 15){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->status = 10;
        $row->status_name ='被驳回';
        $row->update_date = $now;
        $row->update_by = $user->id;
        // save
        $row->save();
    }

    public function passSave($id){
        $row = $this->selectById($id);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 15){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->status = 20;
        $row->status_name = Constant::$purchase_outstock_status[$row->status];
        $row->update_date = $now;
        $row->update_by = $user->id;
        // PurchaseOutstock更新状态
        $row->save();
        $detail = CvtUtil::emptyToArray($row->detail_data);
        foreach ($detail as $item){
            $input_val = '';
            if (array_key_exists('f_list',$item)){
                foreach ($item['f_list'] as $formula_item){
                    if ($formula_item['t'] == 2){
                        $input_val .= $formula_item['l'] . ':' . $formula_item['v'] . ',';
                    }
                }
            }
            $detail_row = new PurchaseOutstockDetail();
            $detail_row->uid = UUID::make();
            $detail_row->outstock_id = $row->id;
            $detail_row->goods_id = $item['id'];
            $detail_row->goods_code = $item['code'];
            $detail_row->goods_name = $item['name'];
            $detail_row->goods_spec = $item['spec'];
            $detail_row->goods_model = $item['model'];
            $detail_row->goods_unit = $item['deputy_unit'];
            $detail_row->input_val =  rtrim($input_val,',');
            $detail_row->quantity = $item['quantity'];
            $detail_row->update_date = $now;
            $detail_row->update_by = $user->id;
            $detail_row->del_flag = 0;
            $detail_row->owner = $user->owner;
            // save
            $detail_row->save();
            $logs = new PurchaseStockLogs();
            $logs->batch_no = $row->code;
            $logs->data_type = 2;
            $logs->data_id = $row->id;
            $logs->sign = 2;
            $logs->goods_id = $detail_row->goods_id;
            $logs->goods_name = $detail_row->goods_name;
            $logs->goods_unit = $detail_row->goods_unit;
            $logs->quantity = $detail_row->quantity * -1;
            $logs->update_date = $now;
            $logs->update_user = $user->real_name;
            $logs->del_flag = 0;
            $logs->owner = $user->owner;
            $logs->save();
        }
    }

    public function getGoodsList()
    {

        $product_id = $this->request->getPost('product_id', 'tstring');
        $notice_id = $this->request->getPost('notice_id', 'tstring');

        // 使用原生SQL查询
        $sql = "SELECT 
            a.id,
            a.uid,
            a.name,
            a.code,
            a.spec,
            a.model,
            a.unit,
            a.deputy_unit,
            a.price,
            round(a.unit_conversion_rate,4) as unit_conversion_rate,
            a.is_batch_managed,
            a.tax_rate,
            ifnull(s.name_as,'') as supplier_name,
            round(ifnull(t.quantity,0),4) as stock_cnt,
            1 as `show`,
            round(a.cd, 2) as cd,
            round(a.kd, 2) as kd,
            round(a.hd, 2) as hd,
            round(a.weight, 3) as weight,
            a.formula_list,
            e.quantity as notice_quantity,
            c.name as bom_name,
            b.quantity as unit_bom_goods_cnt,
            round(ifnull(e.quantity, 0) * ifnull(b.quantity, 0), 4) as total_need_quantity,
            round(ifnull(outstock_sum.total_outstock_qty, 0), 4) as total_outstock_quantity
        FROM purchase_goods a
        INNER JOIN mes_product_goods b ON a.id = b.goods_id AND b.del_flag = 0
        INNER JOIN mes_product_bom c ON c.product_id = b.product_id AND c.del_flag = 0 AND c.id = b.product_bom_id
        INNER JOIN mes_product d ON c.product_id = d.id AND d.del_flag = 0
        INNER JOIN mes_notice_detail e ON e.product_id = d.id AND e.del_flag = 0
        LEFT JOIN purchase_supplier s ON a.supplier_id = s.id
        LEFT JOIN purchase_view_stock t ON a.id = t.goods_id
        LEFT JOIN (
            SELECT 
                g.goods_id, 
                SUM(g.quantity) as total_outstock_qty 
            FROM purchase_outstock f 
            INNER JOIN purchase_outstock_detail g ON f.id = g.outstock_id 
            WHERE f.del_flag = 0 AND g.del_flag = 0 AND f.status = 20 AND f.notice_id = ?
            GROUP BY g.goods_id
        ) outstock_sum ON a.id = outstock_sum.goods_id
        WHERE a.del_flag = 0 AND d.id = ? AND a.owner = ? AND e.notice_id = ?
        ORDER BY a.code asc";
        
        return $this->db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, [
            $notice_id,      // 第一个 ? - 子查询中的notice_id
            $product_id,     // 第二个 ? - product_id
            SessionData::ownerId(),  // 第三个 ? - owner
            $notice_id       // 第四个 ? - 主查询中的notice_id
        ]);
    }


    /**
     * 取得所有已完成的产品
     * @return 所有已完成的产品
     */
    public function getNotices()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNotice', 't1')
            ->where('t1.del_flag = 0 and t1.status = 30')
            ->orderBy('t1.id asc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取得所有已完成的产品
     * @return 所有已完成的产品
     */
    public function getProductList($notice_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                CONCAT_WS(\'-\', t1.code, t1.name) as name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduct', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1.id = t2.product_id', 't2')
            ->where('t1.del_flag = 0 and t2.notice_id = ?1',  [1 => $notice_id])
            ->orderBy('t1.id asc');
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 取得产品对应的工艺
     * @return 工艺
     */
//    public function changeProduct($product_id)
//    {
//
//        return MesProductBom::find([
//            "columns"    => "id, name",
//            "conditions" => "del_flag = 0 and owner = :owner: and status = :status: and product_id = :product_id:",
//            "bind"       => [
//                "status"    => 20,
//                "product_id"    => $product_id,
//                "owner"    => SessionData::ownerId()
//            ],
//            "order"      => "update_date asc",
//        ])->toArray();
//    }

}