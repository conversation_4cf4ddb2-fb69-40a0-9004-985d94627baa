<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Service\NoticeService;
use Envsan\Modules\Purchase\Model\PurchaseApplyEntrust;
use Envsan\Modules\Purchase\Model\PurchaseOrder;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\OrderService;
use Envsan\Modules\Purchase\Service\OrderWwService;
use Envsan\Modules\Purchase\Service\PurchaseWwProcessService;
use Envsan\Modules\Purchase\Service\SupplierService;
use Envsan\Modules\Purchase\Util\Constant;


/**
 * @name('外委加工')
 */
class OrderwwController extends SuperController
{
    private $page_id = 18;
    private $search_page_id = 19;
    private $plan_page_id = 14;


    /**
     * @name('计划')
     */
    public function planAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderwwService();
            $builder = $s->selectPlanAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->plan_page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->plan_page_id;
    }


//    /**
//     * @name('临时外委')
//     * 这个已经不要了
//     */
//    public function temporarywwAction()
//    {
//        if ($this->request->isPost()) {
//            $this->setJsonResponse();
//            $s = new OrderService();
//            $ret = new JsonData();
//            $ret->handleResult($s->create(2));
//            return json_encode($ret);
//        }
//
//        $jrow = (new PurchaseApplyEntrust())->toArray();
//        $noticeService = new NoticeService();
//        $jrow['noticeList'] = $noticeService->getAllActiveNotice();
//        $jrow['noticeDetails'] = [];
//        $jrow['productBomList'] = [];
//        $jrow['materials'] = [];
//        $this->view->jsonData = json_encode($jrow);
//
//        $this->view->extDataName = 'ext_data';
//        $this->view->extDataCnt = 6;
//        $this->view->page_id = $this->page_id;
//        $this->view->page_name = 'ww';
//    }


    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderService();
            $builder = $s->selectAll(2);
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderWwService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }


//    /**
//     * 创建临时外委计划
//     * @return false|string|void
//     */
//    public function  createwwplanAction() {
//        if ($this->request->isPost()) {
//            $this->setJsonResponse();
//            $s = new OrderWwService();
//            $ret = new JsonData();
//            $ret->handleResult($s->createWwPlan());
//            return json_encode($ret);
//        }
//    }


    /**
     * @name('创建')
     */
    public function createAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderService();
            $ret = new JsonData();
            $ret->handleResult($s->create(2));
            return json_encode($ret);
        }

        $table = new TableService();
        $common = new CommonService();

        $jrow = (new PurchaseOrder())->toArray();
        $jrow['order_date'] = DateUtil::today();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['files'] = [];
        $this->view->jsonOrder = json_encode($jrow);

        $this->view->supplierList = $common->getSupplierList();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
        $this->view->page_name = 'ww';
        $this->view->pick('order/create');
    }

    /**
     * @acl({'link':'purchase:orderww:create'})
     */
    function editAction($uid)
    {
        $rs = new OrderWwService();
        $row = $rs->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);
        if ($row->status > 20) {
            die(ErrorHelper::WRONG_ID);
        }
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->detail($row));
            return json_encode($ret);
        }

        $table = new TableService();
        $ext_data = CvtUtil::emptyToArray($row->ext_data);
        $ss = new SupplierService();
        $supplier_row = $ss->selectById($row->supplier_id);

        $jrow = $row->toArray();
        // 外委计划选择的下拉
//        $jrow['ww_plan_list'] = $rs->selectWwPlanDropdown();
        $jrow['ww_list'] = CvtUtil::emptyToArray($jrow['detail_data']);;
        $jrow['supplier_name'] = empty($supplier_row) ? '' : $supplier_row->name;
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $this->view->jsonData = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
        $this->view->page_name = 'ww';
    }

    /**
     * 到货单关联的采购单
     * @acl({'link':'purchase:orderww:create'})
     */
    public function addplanAction($type,$ids = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OrderWwService();
            $builder = $s->selectPlanDetail($ids);
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->ids = $ids;
    }

    /**
     * @acl({'link':'purchase:orderww:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OrderWwService();
            $ret = new JsonData();
            $ret->handleResult($s->delete());
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $rs = new OrderService();
        $row = $rs->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            die('订单数据不存在');
        }

        $oss_util = new FileService();
        $ss = new SupplierService();
        $supplier_row = $ss->selectById($row->supplier_id);

        $jrow = $row->toArray();
        $jrow['ww_list'] = CvtUtil::emptyToArray($jrow['detail_data']);;
        $jrow['supplier_name'] = empty($supplier_row) ? '' : $supplier_row->name;
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $this->view->jsonData = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
    }


//    function viewplanAction($uid)
//    {
//        $rs = new OrderWwService();
//        $row = $rs->selectWwPlanByUid($uid);
//        if (empty($row) || $row->del_flag == 1) {
//            die('外围计划数据不存在');
//        }
//
//        $oss_util = new FileService();
//
//        $jrow = $row->toArray();
//        $jrow['ww_list'] = $rs->selectWwPlanDetail($uid);
//        $jrow['base_path'] = $oss_util->getImagePath();
//        $this->view->jsonData = json_encode($jrow);
//
//        $this->view->extDataName = 'ext_data';
//        $this->view->extDataCnt = 12;
//    }


    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new OrderService();
        $builder = $s->selectAll(2);
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function searchexportAction()
    {
        $s = new OrderWwService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function planexportAction()
    {
        $s = new OrderWwService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->plan_page_id, $builder);
    }

//    public function changenoticeAction()
//    {
//        if ($this->request->isPost()) {
//            $this->setJsonResponse();
//            $s = new OrderWwService();
//            $ret = new JsonData();
//            $ret->handleResult($s->changeNotice($this->request->getPost('notice_id')));
//            return json_encode($ret);
//        }
//    }
//
//
//    /**
//     * 临时外委的changenoticedetail
//     * @return false|string|void
//     */
//
//    public function changenoticedetailAction()
//    {
//        if ($this->request->isPost()) {
//            $this->setJsonResponse();
//            $s = new OrderWwService();
//            $ret = new JsonData();
//            $ret->handleResult($s->changeNoticeDetail($this->request->getPost('notice_detail_id')));
//            return json_encode($ret);
//        }
//    }

//    public function changebomdetailAction()
//    {
//        if ($this->request->isPost()) {
//            $this->setJsonResponse();
//            $s = new OrderWwService();
//            $ret = new JsonData();
//            $ret->handleResult($s->changeBomDetail($this->request->getPost('bom_id')));
//            return json_encode($ret);
//        }
//    }

    /**
     * @skipacl
     */
    public function processlistAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwProcessService();
            $ret = new JsonData();
            $ret->handleResult($s->selectData());
            return json_encode($ret);
        }
    }

}