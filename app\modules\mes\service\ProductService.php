<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesDrawing;
use Envsan\Modules\Mes\Model\MesDrawingVersion;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Model\MesProductGoods;
use Envsan\Modules\Mes\Model\MesProductQuality;
use Envsan\Modules\Mes\Model\MesShipType;
use Envsan\Modules\Mes\Util\Constant;
use Envsan\Modules\Purchase\Model\PurchaseApply;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseGoodsType;
use Envsan\Modules\Purchase\Model\PurchaseRequest;
use Envsan\Modules\Purchase\Model\PurchaseSupplier;
use Envsan\Modules\Purchase\Service\GoodsService;
use Envsan\Modules\Purchase\Util\Constant as PurchaseConstant;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Trade\Model\TradeCustomer;
use Envsan\Modules\Trade\Model\TradeOrderDetail;
use Envsan\Modules\Work\Service\DataCommonService;
use Phalcon\Mvc\User\Component;

class ProductService extends Component
{
    private $page_id = 4;

    public function selectAll($page_type){
        $builder = $this->modelsManager->createBuilder()
        ->columns('
            t1.id,
            t1.uid,
            t1.code,
            t1.name,
            t1.customer_id,
            t1.status,
            t1.status_name,
            t1.remarks,
            t1.ext_val,
            t2.name as customer_name,
            concat(t1.goods_type_code, \' \', t3.name) as goods_type_name
        ')
        ->addFrom('Envsan\Modules\Mes\Model\MesProduct', 't1')
        ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t1.customer_id = t2.id','t2')
        ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType','t1.goods_type_code = t3.code','t3')
        ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
        ->orderBy('t1.id desc');

        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectById($id)
    {
        return MesProduct::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return MesProduct::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function drawingDelete(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = MesDrawing::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status > 10){
            return ErrorHelper::WRONG_INPUT;
        }
        $user = SessionData::user();
        if ($row->create_by != $user->id){
            return '权限不足';
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns(' t1.id,t1.uid')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductBom', 't1')
            ->where('t1.del_flag = 0 and JSON_CONTAINS(t1.drawing_data,JSON_OBJECT(\'drawing_uid\', ?1)) and t1.product_id = ?2',[1 => $row->uid,2=>$row->product_id]);
        $bom_rows = $builder->getQuery()->execute();
        if (count($bom_rows) > 0){
            return '已经被使用，不能删除';
        }
        $this->db->begin();
        try {
            $row->del_flag = 1;
            $row->update_date = DateUtil::now();
            $row->update_by = $user->id;
            if (!$row->save()){
                throw new \Exception("MesDrawing表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return  '';
    }

    public function getShipTypes() {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid as type,
                t1.name as label
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesShipType', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as $row){
            $row['icon'] = Constant::$ship_flow_icon;
            $type_rows[] = $row;
        }
        return $type_rows;
    }

    public function getDrawingList($product_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.version_code,
                t1.status,
                t1.status_name,
                t1.remarks,
                t1.drawing_url,
                t1.drawing_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesDrawing', 't1')
            ->where('t1.del_flag = 0 and t1.product_id = ?1', [1 => $product_id])
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute()->toArray();
        return $rows;
    }

    public function getOrderData($order_detail_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.name,
                t1.cnt,
                t1.ext_data,
                t1.files,
                t1.remarks,
                t2.code as order_code,
                t3.name as customer_name
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrderDetail', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->where('t1.del_flag = 0 and t1.id = ?1', [1 => $order_detail_id])
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            return null;
        }
        return $rows[0];
    }

    public function getBomData($product_row)
    {
        $id = $this->request->getPost('id', 'tstring');
        $type = $this->request->getPost('type', 'tstring');
        $rtn = new \stdClass();
        if (empty($id) || empty($type)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $ship_type_row = MesShipType::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2', 'bind' => [1 => $type, 2 => SessionData::ownerId()]]);
        if (empty($ship_type_row)) {
            $rtn->message = '无效的工艺类型';
            return $rtn;
        }
        $bom_row = MesProductBom::findFirst(['del_flag = 0 and product_id = ?1 and uid = ?2','bind'=>[1=>$product_row->id,2=>$id]]);
        if (empty($bom_row)) {
            $bom_row = (new MesProductBom())->toArray();
            $bom_row['cnt'] = 1;
            $bom_row['status'] = 0;
            $bom_row['is_outsourcing'] = 0;
            $bom_row['name'] = $ship_type_row->name;
        } else {
            $bom_row = $bom_row->toArray();
        }

        $bom_row['is_outsourcing_name'] = empty($bom_row['is_outsourcing']) ? '否' : Constant::$bom_outsourcing[$bom_row['is_outsourcing']];
        $bom_row['work_type_name'] = empty($bom_row['work_type']) ? '' : Constant::$bom_work_types[$bom_row['work_type']];
        $bom_row['plan_type'] = $ship_type_row->plan_type;
        $bom_row['ship_type_id'] = $ship_type_row->id;
        $bom_row['ship_type'] = $ship_type_row->uid;
        $bom_row['ship_type_name'] = $ship_type_row->name;
        $bom_row['uid'] = $id;
        $bom_row['drawing_data'] = CvtUtil::emptyToArray($bom_row['drawing_data']);
        $bom_row['goods_data'] = CvtUtil::emptyToArray($bom_row['goods_data']);
        $bom_row['check_data'] = CvtUtil::emptyToArray($bom_row['check_data']);
        $bom_row['ext_data'] = $this->margeFormData($ship_type_row->form_data,$bom_row['ext_data']);
        $rtn->message = '';
        $rtn->data = $bom_row;
        return $rtn;
    }

    public function getCheckList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.name,
                t1.form_data
            ')
            ->addFrom('Envsan\Modules\Quality\Model\QualityTemplate', 't1')
            ->where('t1.del_flag = 0 and t1.type = 1')
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row){
            $row['form_data'] = CvtUtil::emptyToArray($row['form_data']);
        }
        return $rows;
    }

    public function margeFormData($data,$ext_data){
        $form_list = CvtUtil::emptyToArray($data);
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $form_data = [];
        foreach ($form_list as $form_item) {
            if (array_key_exists($form_item['id'],$ext_data)) {
                $form_item['value'] = $ext_data[$form_item['id']]['value'];
                $form_item['values'] = $ext_data[$form_item['id']]['values'];
            }
            $form_data[$form_item['id']] = $form_item;
        }
        return $form_data;
    }

    public function saveBom()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $bom_uid = $this->request->getPost('bom_uid', 'tstring');
        $ship_type_id = $this->request->getPost('ship_type_id', 'tstring');
        $name = $this->request->getPost('name', 'tstring');
        $is_outsourcing = $this->request->getPost('is_outsourcing', 'tstring');
        $cnt = $this->request->getPost('cnt', 'tstring');
        $produce_cnt = $this->request->getPost('produce_cnt', 'tstring');
        $produce_cost = $this->request->getPost('produce_cost', 'tstring');
        $work_type = $this->request->getPost('work_type', 'tstring');
        $one_cost = $this->request->getPost('one_cost', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $drawing_id = $this->request->getPost('drawing_id', 'tstring');
        $drawing_data = urldecode($this->request->getPost('drawing_data', 'tstring'));
        $check_data = str_replace('%2B','+',urldecode($this->request->getPost('check_data', 'tstring')));
        $goods_data = str_replace('%2B','+',urldecode($this->request->getPost('goods_data', 'tstring')));
        $ext_data = str_replace('%2B','+',urldecode($this->request->getPost('ext_data', 'tstring')));
        $rtn = new \stdClass();
        if (empty($ship_type_id) || empty($uid) || empty($bom_uid) || empty($name) || empty($cnt) || empty($work_type)
        ) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $ship_type_row = MesShipType::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$ship_type_id]]);
        if (empty($ship_type_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $product_row = $this->selectByUid($uid);
        if ($product_row == null || $product_row->del_flag == 1) {
            $rtn->message = '无效的产品';
            return $rtn;
        } else if ($product_row->status != 10) {
            $rtn->message = '已提交不能编辑';
            return $rtn;
        }
        $row = MesProductBom::findFirst([
            'del_flag = 0 and product_id = ?1 and uid = ?2',
            'bind' => [1 => $product_row->id, 2 => $bom_uid]
        ]);
        if (!empty($row)){
            if ($row->status != 10){
                $rtn->message = '已提交不能编辑';
                return $rtn;
            }
        }
        $drawing_data = CvtUtil::emptyToArray($drawing_data);
        $check_data = CvtUtil::emptyToArray($check_data);
        $goods_data = CvtUtil::emptyToArray($goods_data);
        $ext_data = CvtUtil::emptyToArray($ext_data);

        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            if (empty($row)){
                $row = new MesProductBom();
                $row->uid = $bom_uid;
                $row->product_id = $product_row->id;
                $row->ship_type_id = $ship_type_row->id;
                $row->update_date = $now;
                $row->update_by = $user->id;
                $row->status = 10;
                $row->status_name = Constant::$product_bom_status[$row->status];
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            $row->name = $name;
            $row->cnt = $cnt;
            $row->produce_cnt = CvtUtil::blankToNull($produce_cnt);
            $row->produce_cost = CvtUtil::blankToNull($produce_cost);
            $row->is_outsourcing = $is_outsourcing;
            $row->work_type = CvtUtil::blankToNull($work_type);
            $row->one_cost = CvtUtil::blankToNull($one_cost);
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->drawing_id = CvtUtil::blankToNull($drawing_id);
            $row->drawing_data = CvtUtil::arrayToNull($drawing_data);
            $row->check_data = CvtUtil::arrayToNull($check_data);
            $row->goods_data = CvtUtil::arrayToNull($goods_data);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data),JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()){
                throw new \Exception("MesProductBom表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function saveFlowData($product_row){
        $flow_data = str_replace('%2B','+',urldecode($this->request->getPost('flow_data', ['string', 'trim'])));
        $flow_data = CvtUtil::emptyToArray($flow_data);
        $user = SessionData::user();
        $now = DateUtil::now();
        $product_row->flow_data = json_encode($flow_data,JSON_UNESCAPED_UNICODE);
        $product_row->update_date = $now;
        $product_row->update_by = $user->id;
        if (!$product_row->save()) {
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function deleteBom()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $node_id = $this->request->getPost('node_id', 'tstring');
        if (empty($uid) || empty($node_id)){
            return ErrorHelper::WRONG_INPUT;
        }
        $product_row = $this->selectByUid($uid);
        if ($product_row == null || $product_row->del_flag == 1) {
            return '无效的产品';
        } else if ($product_row->status != 10) {
            return '已提交不能删除';
        }
        $product_bom_row = MesProductBom::findFirst([
            'del_flag = 0 and product_id = ?1 and uid = ?2',
            'bind' => [1 => $product_row->id, 2 => $node_id]
        ]);
        if (empty($product_bom_row)) {
            return '';
        }
        if ($product_bom_row->status != 10){
            return '已提交不能删除';
        }
        $product_bom_row->del_flag = 1;
        $product_bom_row->update_date = DateUtil::now();
        $product_bom_row->update_by = SessionData::user()->id;
        return $product_bom_row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function saveData($row){
        $type = $this->request->getPost('type', 'tstring');
        $name = $this->request->getPost('name', 'tstring');
        // 存货档案code
        $goods_type_code = $this->request->getPost('goods_type_code', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $ext_data = str_replace('%2B','+',urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
        $flow_data = str_replace('%2B','+',urldecode($this->request->getPost('flow_data', ['string', 'trim'])));
        if (empty($name) || empty($type)) {
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 10){
            return '状态已变更';
        }
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $flow_data = CvtUtil::emptyToArray($flow_data);
        $nodeObj = [];
        if ($type == 2){
            if (empty($flow_data)){
                return '请添加工艺流程';
            }
            $edges  = $flow_data['edges'];
            $nodes  = $flow_data['nodes'];
            if (count($nodes) == 0){
                return '请添加工艺流程';
            }
            foreach ($nodes as $node){
                $bom_row = MesProductBom::findFirst(['del_flag = 0 and product_id = ?1 and uid = ?2','bind'=>[1=>$row->id,2=>$node['id']]]);
                if (empty($bom_row)){
                    return '请编辑工艺信息';
                }
                $nodeObj[$node['id']] = [
                    'id' => $bom_row->id,
                    'pids' => [],
                    'nids' => [],
                    'puids' => [],
                    'nuids' => []
                ];
            }
            foreach ($edges as $edge){
                $nodeObj[$edge['sourceNodeId']]['nids'][] = CvtUtil::emptyToInt($nodeObj[$edge['targetNodeId']]['id']);
                $nodeObj[$edge['sourceNodeId']]['nuids'][] = $edge['targetNodeId'];
                $nodeObj[$edge['targetNodeId']]['pids'][] = CvtUtil::emptyToInt($nodeObj[$edge['sourceNodeId']]['id']);
                $nodeObj[$edge['targetNodeId']]['puids'][] = $edge['sourceNodeId'];
            }
            if (count($nodes) > 1){
                $end_node_cnt = 0;
                foreach ($nodeObj as $key => $node_item){
                    if (count($node_item['nids'])== 0){
                        $end_node_cnt ++;
                    } else if (count($node_item['nids']) > 1){
                        return '下一节点只支持一个分支';
                    }
                }
                if ($end_node_cnt > 1){
                    return '只能有一个终节点';
                }
            }

        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            $row->name = $name;
            $row->goods_type_code = $goods_type_code;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->flow_data = CvtUtil::arrayToNull($flow_data);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->node_data = CvtUtil::arrayToNull($nodeObj);
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data),JSON_UNESCAPED_UNICODE);
            if ($type == 2){
                $row->status = 20;
                $row->status_name = Constant::$product_status[$row->status];
            }
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("MesProduct表更新失败");
            }
            if ($type == 2){
                $dcm = new DataCommonService();
                $dcm->submitDesign($row->id, 2, $user->group_id);
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    /**
     * @throws \Exception
     */
    public function passSave($id)
    {
        $row = $this->selectById($id);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 20){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $nodeObj = CvtUtil::emptyToArray($row->node_data);

        foreach ($nodeObj as $key => $node_item){
            if (count($node_item['pids']) == 0){
                $this->setBomSort($nodeObj,$key,1);
            }
        }

        foreach ($nodeObj as $key => $node_item){
            $product_bom_row = MesProductBom::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$node_item['id']]]);
            if (!empty($product_bom_row)){
                sort($node_item['pids']);
                $product_bom_row->pids = CvtUtil::arrayToNull($node_item['pids']);
                $product_bom_row->pid_val = implode('_',$node_item['pids']);
                if (count($node_item['pids']) == 0){
                    $product_bom_row->pid = null;
                } else {
                    $product_bom_row->pid = $node_item['pids'][0];
                }
                if (count($node_item['nids']) == 0){
                    $product_bom_row->nid = null;
                } else {
                    $product_bom_row->nid = $node_item['nids'][0];
                }
                $product_bom_row->data_sort = $node_item['sort'];
                $product_bom_row->status = 20;
                $product_bom_row->status_name = Constant::$product_bom_status[$row->status];
                $product_bom_row->update_date = $now;
                $product_bom_row->update_by = $user->id;
                if (!$product_bom_row->save()) {
                    throw new \Exception("MesBom表更新失败");
                }
                $check_data = CvtUtil::emptyToArray($product_bom_row->check_data);
                $goods_data = CvtUtil::emptyToArray($product_bom_row->goods_data);
                foreach ($check_data as $check_item) {
                    $quality = new MesProductQuality();
                    $quality->uid = UUID::make();
                    $quality->product_id = $row->id;
                    $quality->product_bom_id = $product_bom_row->id;
                    $quality->quality_template_id = $check_item['id'];
                    $quality->quality_template_name = $check_item['name'];
                    $f_data = $check_item['form_data'];
                    foreach ($f_data as &$form_item){
                        if ($form_item['type'] == 2 || $form_item['type'] == 7 || $form_item['type'] == 8){
                            $form_item['values'] = [];
                            $form_item['results'] = [];
                            for($i = 0 ; $i < CvtUtil::emptyToInt($form_item['input_cnt']) ; $i++){
                                $form_item['values'][] = '';
                                $form_item['results'][] = 0;
                            }
                        } else if ($form_item['type'] == 6){
                            $form_item['values'] = [];
                            $form_item['results'] = [];
                            for($i = 0 ; $i < CvtUtil::emptyToInt($form_item['input_cnt']) ; $i++){
                                $form_item['values'][] = 0;
                                $form_item['results'][] = 0;
                            }
                        }
                    }
                    $quality->form_data = CvtUtil::arrayToNull($f_data);
                    $quality->remarks = '';
                    $quality->update_date = $now;
                    $quality->update_by = $user->id;
                    $quality->del_flag = 0;
                    $quality->owner = $user->owner;
                    if (!$quality->save()) {
                        throw new \Exception("MesProductQuality表更新失败");
                    }
                }
                foreach ($goods_data as $goods_item){
                    $goods = new MesProductGoods();
                    $goods->uid = UUID::make();
                    $goods->product_id = $row->id;
                    $goods->product_bom_id = $product_bom_row->id;
                    $goods->goods_id = $goods_item['id'];
                    $goods->code = $goods_item['code'];
                    $goods->name = $goods_item['name'];
                    $goods->spec = $goods_item['spec'];
                    $goods->model = $goods_item['model'];
                    $goods->unit = $goods_item['unit'];
                    $goods->quantity = $goods_item['quantity'];
                    $goods->remarks = $goods_item['remarks'];
                    $goods->formula_list = CvtUtil::arrayToNull($goods_item['f_list']);;
                    $formula_val = '';
                    foreach ($goods_item['f_list'] as $formula_item) {
                        if ($formula_item['t'] == 2){
                            $formula_val .= $formula_item['l'] . ':' . $formula_item['v'] .',';
                        }
                    }
                    $goods->formula_val = $formula_val;
                    $goods->update_date = $now;
                    $goods->update_by = $user->id;
                    $goods->del_flag = 0;
                    $goods->owner = $user->owner;
                    if (!$goods->save()) {
                        throw new \Exception("MesProductQuality表更新失败");
                    }
                }
            } else {
                throw new \Exception(ErrorHelper::WRONG_INPUT);
            }
        }

        $drawings = MesDrawing::find(['del_flag = 0 and product_id = ?1','bind'=>[1=>$row->id]]);
        foreach ($drawings as $drawing){
            $version = new MesDrawingVersion();
            $version->uid = $drawing->uid;
            $version->code = $drawing->version_code;
            $version->drawing_id = $drawing->id;
            $version->drawing_url = $drawing->drawing_url;
            $version->drawing_name = $drawing->drawing_name;
            $version->remarks = $drawing->remarks;
            $version->status = 30;
            $version->status_name = Constant::$drawing_version_status[$version->status];
            $version->upload_time = $drawing->create_date;
            $version->upload_by = $drawing->create_by;
            $version->update_date = $now;
            $version->update_by = $user->id;
            $version->del_flag = 0;
            $version->owner = $user->owner;
            if (!$version->save()) {
                throw new \Exception("MesProduct表更新失败");
            }
            $drawing->version_id = $version->id;
            $drawing->status = 20;
            $drawing->status_name = Constant::$drawing_status[$drawing->status];
            $drawing->update_date = $now;
            $drawing->update_by = $user->id;
            if (!$drawing->save()) {
                throw new \Exception("MesProduct表更新失败");
            }
        }

        ////// start 做成商品档案
        // 取得下一个存货编码
        $code_ary = $this->getNextGoodsCode($row->goods_type_code);
        $increment_part =  $code_ary['increment_part'];
        // 获取商品类别
        $goods_type_id = $this->getGoodsType($row->goods_type_code);
        // 创建客户产品存货档案
        $finished_goods = new PurchaseGoods();
        $finished_goods->uid = UUID::make();
        // 存货编码
        $finished_goods->code = $row->goods_type_code . $increment_part;
        // 商品大类别
        $finished_goods->type_id = $goods_type_id;
        // 存货名称
        $finished_goods->name = $row->name;
        // 规格型号
        $finished_goods->model = $row->code;
        // 存货代码
        $finished_goods->inventory_code = $row->inventory_code;
        // 是否批次管理
        $finished_goods->is_batch_managed = 0;
        // 库存单位
        $finished_goods->unit = $row->inventory_unit;
        // 委外单位
        $finished_goods->deputy_unit = $row->purchase_unit;
        // 税率
        $finished_goods->tax_rate = $row->tax_rate;
        // 税率
        $finished_goods->weight = $row->weight;
        // 客户ID
        $finished_goods->customer_id = $row->customer_id;

        $finished_goods->update_date = $now;
        $finished_goods->update_by = $user->id;
        $finished_goods->del_flag = 0;
        $finished_goods->owner = $user->owner;
        if (!$finished_goods->save()) {
            throw new \Exception("PurchaseGoods表更新失败");
        }

        $row->goods_code = $row->goods_type_code . $increment_part;
        $row->status = 30;
        $row->status_name = Constant::$product_status[$row->status];
        $row->update_date = $now;
        // 存储物品的商品ID
        $row->goods_id = $finished_goods->id;
        $row->update_by = $user->id;
        if (!$row->save()) {
            throw new \Exception("TradeOrder表更新失败");
        }

    }

    /**
     * 取得最大番号
     * @throws \Exception
     */
    public function getNextGoodsCode($goodsTypeCode): array
    {
        $prefixLength = strlen($goodsTypeCode);

        // 2. 查询 purchase_goods 表中符合条件的记录（code 前缀匹配，且总长度 = goods_type_code 长度 + 4）
        $purchaseGoods = PurchaseGoods::find([
            'conditions' => 'code LIKE ?1 AND LENGTH(code) = ?2',
            'bind' => [
                1 => $goodsTypeCode . '%',  // 前缀匹配
                2 => $prefixLength + 4,     // 总长度 = 前缀长度 + 4
            ],
            'order' => 'code DESC',         // 按 code 降序排序
            'limit' => 1                    // 只取最大的一个
        ]);

        if (count($purchaseGoods) === 0) {
            // 没有找到记录，使用 goodsTypeCode + '0001'
            $newCode = $goodsTypeCode . '0001';
            return [
                'code' => $newCode,
                'increment_part' => '0001',
                'prefix' => $goodsTypeCode,
            ];
        } else {
            // 找到记录，获取最大的 code
            $maxCode = $purchaseGoods[0]->code;

            // 提取后4位数字
            $lastFour = substr($maxCode, -4);

            // 后4位转数字+1，再补零到4位
            $nextNumber = str_pad((int)$lastFour + 1, 4, '0', STR_PAD_LEFT);

            // 拼接成新 code
            $newCode = $goodsTypeCode . $nextNumber;

            return [
                'code' => $newCode,
                'increment_part' => $nextNumber,
                'prefix' => $goodsTypeCode,
            ];
        }
    }

    /**
     * 存货大类编码
     * @param $next_code
     * @return string
     */
    public function getGoodsType($goods_type_code): string
    {
        // 1. 获取 tradeCustomer 的 goods_type_code
        $purchaseGoodsType = PurchaseGoodsType::findFirst([
            'del_flag = 0 and code = ?1',
            'bind' => [1 => $goods_type_code]
        ]);

        if (!$purchaseGoodsType) {
            throw new \Exception("大类不存在或已删除");
        }
        // 组合成新代码（假设前面部分不变）
        return $purchaseGoodsType->id;
    }

    private function setBomSort(&$nodeObj,$key,$sort){
        $nodeObj[$key]['sort'] = $sort;
        if (count($nodeObj[$key]['nuids']) > 0){
            $next_key = $nodeObj[$key]['nuids'][0];
            $sort++;
            $this->setBomSort($nodeObj,$next_key,$sort);
        }
    }

    public function rejectSave($id,$reject_remarks)
    {
        $row = $this->selectById($id);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 20){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->status = 10;
        $row->status_name ='被驳回';
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()) {
            throw new \Exception("【合同变更审批驳回】TradeOrderChange表更新失败");
        }
    }

}