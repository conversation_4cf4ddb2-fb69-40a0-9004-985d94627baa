<?php

namespace Envsan\Modules\Equ\Util;

class Constant
{
    public static $equ_status_arr = [10 => '在用', 20 => '停工', 30 => '保养', 40 => '大修'];

    public static $equ_type_arr = [
        0 => '热处理',
        1 => '管端成型',
        2 => '弯管机',
        3 => '仪表车',
        4 => '点焊机',
        5 => '锯床',
        6 => '数控车',
        7 => '打压',
        8 => '铣床',
        9 => '冲压',
        10 => '光饰机',
        11 => '压力机',
        12 => '攻丝机',
        13 => '淬火',
        14 => '打标机',
        15 => '焊接',
        16 => '线切割',
        17 => '磨床',
        18 => '剪板机',
        19 => '清洗机',
        20 => '钎焊炉',
        21 => '捆扎机',
        22 => '无心磨床',
        23 => '加工中心',
        24 => '插齿机',
        25 => '穿孔机',
        26 => '珩磨机',
        27 => '旋压机'
     ];

    public static $equ_run_status_arr = [10 => '待生产', 20 => '生产中', 30 => '调试中', 40 => '停机', 50 => '换模中', 60 => '待调试'];

    public static $operate_reason_type_arr = [0 => '停机', 1 => '设备调试', 2 => '换模', 3 => '等待调试'];

    public static $fault_level_arr = [0 => '可使用', 1 => '不可使用'];

    public static $fault_status_arr = [10 => '待处理', 20 => '外修中', 30 => '待解除', 40 => '完成'];

    public static $model_fault_status_arr = [10 => '待处理', 20 => '外修中', 30 => '待解除', 40 => '完成'];

    public static $check_logs_status = [10 => '待提交', 20 => '已完成'];

    public static $maintain_logs_status = [10 => '待提交', 20 => '已完成'];

    public static $check_form_type = [
        1 => '点检',
        2 => '保养磨耗'
    ];

    public static $form_data_template = [
        'id' => '',
        'name' => '',
        'label1_title'=>'',
        'label1_value'=>'',
        'label2_title'=>'',
        'label2_value'=>'',
        'label3_title'=>'',
        'label3_value'=>'',
        'label4_title'=>'',
        'label4_value'=>'',
        'label5_title'=>'',
        'label5_value'=>'',
        'value'=>'',
    ];
}