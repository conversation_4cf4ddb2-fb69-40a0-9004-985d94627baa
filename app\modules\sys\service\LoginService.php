<?php

namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Modules\Sys\Model\User;
use Phalcon\Mvc\User\Component;

class LoginService extends Component
{
    public function login($owner)
    {
        $name = $this->request->getPost('name', ['string', 'trim']);
        $pwd = $this->request->getPost('pwd', ['string', 'trim']);

        if (!empty($name) && !empty($pwd)) {
            $us = new UserService();
            $user = $us->selectUser($name, $pwd, $owner->id);
            if ($user != null && $user->account_status == 0) {
                // 超级管理员
                if ($user->type == User::TYPE_SUPER) {
                    $this->session->set('super', true);
                    $this->session->set('group_id', 0);
                    $this->session->set('group_ids', []);
                    $owner->id = 0;
                }
                $this->session->set('user', $user);
                $this->session->set('owner', $owner);
                $this->session->set('acl', $us->buildAcl($user));
                $this->session->set('home_page', ['id' => 'a-sys', 'url' => $this->config->site->homeUrl]);
                // 判断是否是管理员
                $rs = new RoleService();
                $role = $rs->selectById($user->role_id);
                if ($role != null) {
                    if ($role->identity == 'admin')
                        $this->session->set('admin', true);

                    $arr = [];
                    $group_id = 0;
                    //UPDATE RC-LQ-968 20240220 BY WXX START
                    if ($role->scope != 0) {
                        // 使用用户所属的组织进行权限限定
                        $gs = new GroupService();
                        $group_id = $user->group_id;
                        $arr = $gs->selectGroupIds($group_id);
                    }
                    else if ($role->group_id != 0) {
                        // 使用角色所属的组织进行权限限定
                        $gs = new GroupService();
                        $group_id = $role->group_id;
                        if (!empty($role->auth_data_ids)){
                            $arr = explode(',', $role->auth_data_ids);
                        } else {
                            $arr = $gs->selectGroupIds($group_id);
                        }
                    }
                    //UPDATE RC-LQ-968 20240220 BY WXX END
                    $this->session->set('group_id', $group_id);
                    $this->session->set('group_ids', $arr);

                    $home_page = [];
                    foreach (ConstantUtil::$home_page_list as $page)
                    {
                        if ($page['id'] == $role->home_page_id) {
                            $home_page = ['id' => $page['menu_id'], 'url' => $page['url']];
                        }
                    }
                    $this->session->set('home_page', $home_page);
                }
                return $user;
            }else if($user != null && $user->account_status == 1){
                return $user;
            }
        }
        return null;
    }

    public function logout()
    {
        // 只清除sys的session
        $this->session->remove('user');
        $this->session->remove('owner');
        $this->session->remove('super');
        $this->session->remove('acl');
        $this->session->remove('home_page');
        $this->session->remove('admin');
        $this->session->remove('group_id');
        $this->session->remove('group_ids');
    }
}