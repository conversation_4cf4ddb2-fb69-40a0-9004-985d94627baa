<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Work\Service\FormDataService;
use Envsan\Modules\Work\Service\FormService;
use Envsan\Modules\Work\Service\ReviewService;

/**
 * @name('自定义审批')
 */
class FormdataController extends SuperController
{
    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $as = new FormService();
            $builder = $as->selectAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'work:formdata:list'})
     */
    function reportAction($uid='')
    {
        $rs = new FormService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);
        $this->view->form_id = $row->id;
        $this->view->form_name = $row->name;
    }

    /**
     * @name('报表')
     */
    public function reportlistAction($form_id, $type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new FormDataService();
            $builder = $s->selectAll($form_id);
            $page = $this->getPagination($builder);
            $page->data = $s->getTableData($form_id, $page->rows);
            return json_encode($page);
        }
    }

    /**
     * @skipacl
     */
    public function exportAction($form_id)
    {
        $s = new FormDataService();
        $builder = $s->selectAll($form_id);
        $s->exportExcel($form_id,$builder);
    }
}