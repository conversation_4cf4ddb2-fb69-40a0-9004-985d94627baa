{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-detail" style="margin-bottom: 0;padding-top: 5px; padding-bottom: 5px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold">入库信息</span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center;justify-content: flex-end">
                        <button type="button" class="btn red" @click="clearDetailData" style="margin-right: 10px;"><i class="fa fa-refresh"></i> 清空</button>
                        <button type="button" class="btn blue" @click="prevSubmit(1, $event)" style="margin-right: 20px;"><i class="fa fa-save"></i> 保存</button>
                        <button type="button" class="btn green" @click="prevSubmit(2, $event)"><i class="fa fa-check"></i> 提交</button>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body">
                            <div id="form_data" class="row">
                                {% if dispatcher.getActionName() == 'edit' %}
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">入库单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">外委加工单</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">供应商</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>外委到货单</label>
                                            <div class="col-sm-8">
                                                <select class="bs-select form-control" name="order_id" v-model="order_id"  data-live-search="true" data-size="8" required>
                                                    <option value="">请选择外委到货单</option>
                                                    {% for item in wwArriveList %}
                                                        <option value="{{ item.id }}">{{ item.order_code }} / {{ item.supplier_name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>入库日期</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" name="instock_date" v-model="instock_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item, index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>批次号</th>
                                    <th>产品名称</th>
                                    <th>产品规格</th>
                                    <th>外委工艺</th>
                                    <th>入库数量</th>
                                    <th>计价数量</th>
                                    <th>无税单价</th>
                                    <th>含税单价</th>
                                    <th>未税金额</th>
                                    <th>税额</th>
                                    <th>含税金额</th>
                                    <th>是否质检</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="detail_data.length == 0">
                                    <td colspan="14" style="text-align: center;">没有找到匹配的记录</td>
                                </tr>
                                <tr v-for="row, index in detail_data">
                                    <td v-text="index + 1"></td>
                                    <td v-text="row.notice_code"></td>
                                    <td v-text="row.product_name"></td>
                                    <td v-text="row.product_code"></td>
                                    <td v-text="row.bom_name"></td>
                                    <td>
                                        <div class="input-group">
                                            <input type="number" class="form-control" :name="'quantity' + index" v-model="row.quantity" placeholder="数量" number="true" @keyup="sumMoney" @input="onQuantityChange(row)">
                                            <span class="input-group-addon" v-text="row.goods_deputy_unit"></span>
                                        </div>
                                    </td>
                                    <td v-text="(row.pricing_quantity || 0) + '(' + row.goods_unit + ')'"></td>
                                    <td >
                                        <div class="input-group">
                                            <input type="number" class="form-control" :name="'price' + index" v-model="row.price" placeholder="未税单价"  @keyup="sumMoney" @input="onPriceChange(row)" number="true">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="number" class="form-control" :name="'price_hs' + index" v-model="row.price_hs" placeholder="含税单价"  @keyup="sumMoney" @input="onPriceHsChange(row)"  number="true">
                                        </div>
                                    </td>
                                    <td v-text="row.total_amount"></td>
                                    <td v-text="row.tax_amount"></td>
                                    <td v-text="row.total_amount_hs"></td>
                                    <td v-text="row.check_status"></td>
                                    <td>
                                        <a href="javascript:;" @click='delList(index)' style="color: red;font-size: 20px">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12" style="padding-top: 10px;">
            <div class="portlet light portlet-bom" style="margin-bottom: 0;padding-top: 5px; padding-bottom: 5px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-yellow"></i>
                        <span class="caption-subject font-yellow bold">订单信息</span>
                    </div>
                    <!-- <div class="row table-search-bar">
                        <div class="col-sm-3">
                            <div class="input-group">
                                <span class="input-group-addon">工艺名称</span>
                                <input type="text" class="form-control" name="notice_code" v-model="notice_code">
                            </div>
                        </div>
                    </div> -->
                </div>
                <div class="portlet-body" style="min-height: 400px">
                    <div class="search-page">
                        
                        <div class="zh-table-box">
                            <div class="zh-table-box-content">
                                <table class="table table-bordered table-big">
                                    <thead>
                                    <tr>
                                        <th>批次号</th>
                                        <th>产品名称</th>
                                        <th>产品规格</th>
                                        <th>外委工艺</th>
                                        <th>到货数量</th>
                                        <th>计价数量</th>
                                        <th>无税单价</th>
                                        <th>含税单价</th>
                                        <th>未税金额</th>
                                        <th>税额</th>
                                        <th>含税金额</th>
                                        <th>是否质检</th>
                                        <th>选择</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-if="bom_msg">
                                        <td colspan="13" style="text-align: center;" v-text="bom_msg"></td>
                                    </tr>
                                    <tr v-for="row, index in bom_list" v-if="row.show_flag == 1">
                                        <td v-text="row.notice_code"></td>
                                        <td v-text="row.product_name"></td>
                                        <td v-text="row.product_code"></td>
                                        <td v-text="row.bom_name"></td>
                                        <td v-text="row.quantity"></td>
                                        <td v-text="row.pricing_quantity"></td>
                                        <td v-text="row.price"></td>
                                        <td v-text="row.price_hs"></td>
                                        <td v-text="row.total_amount"></td>
                                        <td v-text="row.tax_amount"></td>
                                        <td v-text="row.total_amount_hs"></td>
                                        <td v-text="row.check_status"></td>
                                        <td>
                                            <a href="javascript:;" @click='addList(row)'>
                                                <i class="fa fa-arrow-up"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonInstock }},
        methods: {
            onPriceChange(row) {
                if (row.price !== '' && row.price !== null) {
                    const priceValue = Number(row.price) || 0;
                    const rateValue = Number(row.tax_rate) || 0;
                    row.price_hs = (priceValue * (1 + rateValue)).toFixed(2);
                }
            },
            onPriceHsChange(row) {
                if (row.price_hs !== '' && row.price_hs !== null) {
                    const priceHsValue = Number(row.price_hs) || 0;
                    const rateValue = Number(row.tax_rate) || 0;
                    if (rateValue === 0) {
                        this.price = priceHsValue.toFixed(2);
                    } else {
                        row.price = (priceHsValue / (1 + rateValue)).toFixed(2);
                    }
                }
            },
            onQuantityChange(row) {
                if (row.quantity !== '' && row.quantity !== null) {
                    const quantityValue = Number(row.quantity) || 0;
                    const rateValue = Number(row.conversion_rate) || 0;
                    row.pricing_quantity = (quantityValue * (1 + rateValue)).toFixed(2);
                } else {
                    row.pricing_quantity = '';
                }
            },
            sumMoney() {
                for (let item of this.detail_data) {
                    let total_money = '';        // 不含税总价
                    let total_money_hs = '';     // 含税总价
                    let tax_amount = '';         // 税价
                    // 获取并验证数量、单价
                    const quantity = item.pricing_quantity;
                    const price = item.price;           // 不含税单价
                    const tax_rate = item.tax_rate;     // 税率（如：17表示17%）
                    
                    // 检查必要字段是否有效
                    if (isDecimal(quantity) && isDecimal(price)) {
                        const qty = Number(quantity);
                        const unitPrice = Number(price);
                        
                        // 计算不含税总价
                        total_money = Number((qty * unitPrice).toFixed(4));
                        
                        // 处理税率：空值当作0处理
                        let taxRateValue = 0;
                        if (isDecimal(tax_rate)) {
                            taxRateValue = Number(tax_rate);
                        }
                        // 如果tax_rate为空、null、undefined、''等，taxRateValue保持为0
                        tax_amount = Number((qty * unitPrice * taxRateValue).toFixed(4));
                        // 计算含税总价
                        total_money_hs = Number((qty * unitPrice * (1 + taxRateValue)).toFixed(4));
                    }
                    // 如果数量或单价无效，则保持空字符串
                    
                    // 设置计算结果
                    this.$set(item, 'tax_amount', tax_amount);
                    this.$set(item, 'total_amount', total_money);
                    this.$set(item, 'total_amount_hs', total_money_hs);
                }
            },
            prevSubmit(type, e) {
                e.preventDefault();

                if (this.detail_data.length == 0) {
                    toastr.error('请添加入库明细');
                    return;
                }

                for (let i = 0; i < this.detail_data.length; i++) {
                    let row = this.detail_data[i];
                    if (!isDecimal(row.quantity) || row.quantity <= 0) {
                        toastr.error('请输入有效的数量，大于0');
                        return;
                    }

                    if (!isDecimal(row.price)) {
                        toastr.error('请输入有效的未税单价');
                        return;
                    }

                    if (!isDecimal(row.price_hs)) {
                        toastr.error('请输入有效的含税单价');
                        return;
                    }
                }

                let param = JSON.parse(JSON.stringify(this.$data));
                param.type = type;
                param.ext_data = encodeURI(JSON.stringify(this.ext_data)).replace(/\+/g, '%2B');
                delete param.bom_list;

                if (type == 2) {
                    let dlg = top.layer.confirm('确认提交吗?', function() {
                        top.layer.close(dlg);
                        app.doSubmit(param);
                    });
                } else {
                    app.doSubmit(param);
                }
            },
            doSubmit(param) {
                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('purchase/wwinstock/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('purchase/wwinstock/create') }}';
                {% endif %}

                commonAjaxRequest(url, param, function(rs) {
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                });

            },
            delFile: function (index) {
                this.files.splice(index);
            },
            // 安全转换函数
            safeNumber: function(value, defaultValue = 0) {
                if (value === '' || value === null || value === undefined) {
                    return defaultValue;
                }
                const num = Number(value);
                return isNaN(num) ? defaultValue : num;
            },
            addList(bom) {
                // 调试输出
                console.log('准备添加的bom:', bom);
                console.log('当前detail_data:', this.detail_data);
                
                for (let item of this.detail_data) {
                    console.log('检查item.id:', item.id, 'bom.id:', bom.id);
                    if (item.id == bom.id) {
                        toastr.error('已存在，不能重复添加');
                        return;
                    }
                }
                
                // 深拷贝对象，避免引用问题
                const newItem = JSON.parse(JSON.stringify(bom));
                this.detail_data.push(newItem);
                console.log('添加后的detail_data:', this.detail_data);
            },
            delList: function(idx) {
                this.detail_data.splice(idx, 1);
            },
            clearDetailData() {
                this.detail_data = [];
                toastr.success('已清空入库明细');
            },
        },
        watch: {
            order_id: function(val) {
                if (val == '') {
                    app.bom_list = [];
                    app.detail_data = [];
                    return;
                }

                this.bom_msg = '正在努力的加载数据中，请稍后...';

                commonAjaxRequest('{{ url('purchase/wwinstock/bomlist') }}', {order_id: val}, function(rs) {
                    if (rs.data.length == 0) {
                        app.bom_msg = '没有找到匹配的记录';
                        app.bom_list = [];
                        app.detail_data = [];
                    } else {
                        app.bom_msg = '';
                        app.bom_list = rs.data;
                        app.detail_data = [];
                    }
                });
            },
            // bom_name: function() {
            //     this.bomShow();
            // }
        }
    });

    initUpLoaderPdf('purchase_wwinstock');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key: getUuid(),
                url_name: rs.file_name,
                url: rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

    function initSize() {
        // let portlet_height = $(window).height() - 35 - 27 - 8;

        // $(".portlet-bom .zh-table-box-content").height(portlet_height
        //     - $(".portlet-bom .portlet-title").outerHeight(true)
        //     - $(".portlet-bom .table-search-bar").outerHeight(true));

        // $(".portlet-detail .zh-table-box-content").height(portlet_height
        //     - $(".portlet-detail .portlet-title").outerHeight(true)
        //     - $(".portlet-detail #form").outerHeight(true));

    }

    $(function() {
        initSize();
    });
</script>
{{ partial('form_script') }}
<style>
    .table-search-bar {
        margin-bottom: 20px;
    }

    .zh-table-box table thead {
        z-index: 3;
    }
</style>