<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

use Envsan\Common\Util\ModelUtil;

class GroupConfig extends BaseModel
{

    const DICT_VISIBLE_YES=1;
    const DICT_VISIBLE_NO=0;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $group_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $section;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $type;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=true)
     */
    public $visible;

    /**
     *
     * @var string
     * @Column(type="string", length=300, nullable=true)
     */
    public $val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_group_config';
    }

    public static function find($params = null)
    {
        return parent::find(ModelUtil::build($params));
    }

    public static function findFirst($params = null)
    {
        return parent::findFirst(ModelUtil::build($params));
    }

    public function initialize()
    {
        $this->belongsTo('group_id', 'Envsan\Modules\Sys\Model\Group', 'id', array('alias' => 'Group'));
        ModelUtil::softDelete($this);
    }

    public function beforeDelete()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }

    public function beforeSave()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }
}
