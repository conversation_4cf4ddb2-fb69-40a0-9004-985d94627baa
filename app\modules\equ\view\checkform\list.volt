{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">模板管理</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">模板名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="name" v-model="name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">类型</label>
                            <div class="col-md-9">
                                <select  name="type" class="form-control bs-select" v-model="type" required>
                                    <option value="">请选择</option>
                                    {% for key,value in types %}
                                        <option value="{{ key }}">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   data-mobile-responsive="true"
                   data-query-params="app.params"
                   data-pagination="true"
                   data-url="{{ url('equ/checkform/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server"
                   class="table table-striped table-condensed">
                <thead class="bg-blue">
                <tr>
                    <th data-field="name">模板名称</th>
                    <th data-field="code">文件番号</th>
                    <th data-field="type_name">类型</th>
                    <th data-field="remarks">备注</th>
                    <th data-field="update_date">更新时间</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-pencil"></i> 编辑表单</a></li>
            <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-times"></i> 删除</a></li>
        </ul>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            name:'',
            type:''
        },
        methods: {
            search: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params : function (p) {
                p.name = this.name;
                p.type = this.type;
                return p;
            },
            reset: function () {
                this.name = '';
                this.type = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    var $table = $('#table');

    $table.bootstrapTable();
    var actHtml = $('#act').html();
    function actionFormatter(v, row, idx) {
        return actHtml.replace(/@id@/g, row.uid);
    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新建',
            type: 2,
            area: ['40em', '50em'],
            content: "{{ url('equ/checkform/create') }}",
            end: function () {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(id) {
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('equ/checkform/edit/') }}' + id,
            end:function(){
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function del(id) {
        var dlg = layer.confirm('确认删除吗?', function () {
            layer.close(dlg);
            showSpin();
            $.post("{{ url('equ/checkform/delete') }}", { uid: id }, function (rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作成功!');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败!');
                }
            })
        });
    }

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>