<?php
namespace Envsan\modules\printing\controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'printing']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->setJsonResponse();
//        if($this->request->isPost()) {
            $this->moduleExistThenDie('printing');

            $res = [
                [
                    'name' => '打印模板管理',
                    'identity' => 'printing:template',
                    'action' => [
                        ['name' => '打印模板管理', 'identity' => 'printing:template:list', 'comment' => ''],
                    ]
                ]
            ];

            $ret = new JsonData();
            $this->db->begin();
            try {
                $this->makePackage('printing', '打印模板管理', '1.0', '提供打印模板管理功能');
                $module = $this->makeModule('printing', '打印模板管理');
                foreach ($res as $controller) {
                    $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                    foreach ($controller['action'] as $action) {
                        $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                    }
                }
                $this->db->commit();
            } catch (\Exception $e) {
                $this->db->rollback();
                Logger::error($e->getMessage(), $e->getTraceAsString());
                $ret->message = '发生错误';
            }
            $ret->emptyIsOk();
            return json_encode($ret);
//        }
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}