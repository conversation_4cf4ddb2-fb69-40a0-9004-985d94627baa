<?php
namespace Envsan\Modules\Printing\Api;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Common\Service\FileService;

/**
 * @noacl
 */
class CommonController extends SuperController
{
    public function uploadAction()
    {
        $this->setJsonResponse();
        $s = new FileService();
        return json_encode($s->uploadImage('printing',1));
    }
}
