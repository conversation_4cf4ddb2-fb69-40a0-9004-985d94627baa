{{ assets.outputJs('validate') }}

<!-- Main content -->
<section class="content">
    <!-- Default box -->
    <div class="box" id="app">
        <div class="box-body">
            <form id="form">
                <div class="form-group">
                    <label class="control-label"><span class="text-red">*</span>角色：</label>
                    <select name="gender" class="form-control" required v-model="ruid">
                        <option value=""></option>
                        {% for row in roles %}
                            <option value="{{ row.uid }}">{{ row.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
        <div class="box-footer">
            <div class="btn-bar">
                <button class="btn btn-primary" @click="submit"><i class="fa fa-check"></i> 提交</button>
            </div>
        </div>
        <!-- /.box-body -->
    </div>
    <!-- /.box -->
</section>
<!-- /.content -->

<script>
    var app = new Vue({
        el:'#app',
        data:{
            {% if role is empty %}
                ruid:''
            {% else %}
                ruid:'{{ role.uid }}'
            {% endif %}
        },
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                showSpin();
                var url= '{{ url('sys/user/role/bind/'~user.uid) }}';
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });
</script>
