<?php
namespace Envsan\Modules\Trade\Service;

use Envsan\Common\Data\SessionData;
use Envsan\Modules\Common\Service\TableService;
use Phalcon\Mvc\User\Component;

class StockService extends Component
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code as product_code,
                t1.name as product_name,
                t1.ext_val as product_ext_val,
                t2.cnt,
                t3.name as customer_name,
                t3.ext_val as customer_ext_val
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduct', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewStock','t1.id = t2.product_id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t1.customer_id = t3.id','t3')
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectHistory($order_detail_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.work_month,
                t1.work_date,
                t1.cnt,
                t1.staff_name,
                t1.create_time,
                t1.remarks
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesStockLogs', 't1')
            ->where('t1.del_flag = 0 and t1.product_bom_id is null and t1.order_detail_id = ?1',[1 => $order_detail_id])
            ->orderBy('t1.work_date,t1.id');
        return $builder->getQuery()->execute();
    }
}