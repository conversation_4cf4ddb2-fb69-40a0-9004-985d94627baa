<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Work\Model\WorkForm;
use Envsan\Modules\Work\Util\Constant;
use Phalcon\Mvc\User\Component;
use PHPExcel_Cell_DataType;
use PHPExcel_Style_Alignment;

class FormDataService extends Component
{
    public function selectAll($form_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t2.id,
                t2.uid,
                t2.code,
                t2.create_date,
                t4.real_name as create_name,
                t1.json_data
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkDataForm', 't1')
            ->leftJoin('Envsan\Modules\Work\Model\WorkData','t1.data_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't2.create_group_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't2.create_by = t4.id', 't4')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.form_id = ?2', [1 => SessionData::ownerId() , 2=>$form_id])
            ->orderBy('t1.data_id desc,t1.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function getTableData($from_id,$page_rows){
        $field_list = [];
        $json_key_list = [];
        $form_row = WorkForm::findFirst(['id = ?1','bind'=>[1=>$from_id]]);
        $ext_data = CvtUtil::emptyToArray($form_row->form_data);
        foreach (Constant::$extend_column['header_data'] as $header_item){
            if ($header_item['show'] == 0){
                $field_list[] = $header_item;
            } else {
                if ($header_item['type'] < 99){
                    $field_list[] = $header_item;
                }
            }
            if ($header_item['type'] >= 99){
                $json_key_list[] = $header_item['id'];
            }
        }
        foreach ($ext_data as $ext_item){
            if ($ext_item['type'] == 99){
                foreach ($ext_item['list'] as $data_item) {
                    $data_item['name'] = $data_item['title'];
                    $id = $data_item['id'];
                    $data_item['condition'] = "json_extract(t1.json_data,'$.$id')";
                    $data_item['show'] = 1;
                    $field_list[] = $data_item;
                }
            } else {
                $ext_item['name'] = $ext_item['title'];
                $id = $ext_item['id'];
                $ext_item['condition'] = "json_extract(t1.json_data,'$.$id')";
                $ext_item['show'] = 1;
                $field_list[] = $ext_item;
            }
        }
        $header_data = [];
        $sum_data = [];
        $type_map = [];
        foreach ($field_list as $field_item)
        {
            $header_data[$field_item['id']] = '';
            $type_map[$field_item['id']] = $field_item['type'];
        }
        $header_data = json_encode($header_data,JSON_UNESCAPED_UNICODE);
        if (!is_array($page_rows)) {
            $page_rows = $page_rows->toArray();
        }
        $data_list = [];
        foreach ($page_rows as $page_row){
            foreach ($json_key_list as $json_key){
                if (array_key_exists($json_key,$page_row)){
                    $ext_val = CvtUtil::emptyToArray($page_row[$json_key]);
                    foreach ($ext_val as $k => $v) {
                        $page_row[$k] = $v;
                    }
                }
            }
            $clone_header_data = json_decode($header_data,true);
            foreach ($clone_header_data as $h_key => $h_val){
                if (array_key_exists($h_key,$page_row)){
                    if ($type_map[$h_key] == 2) {
                        // 王让改成所有数字类型都省略小数点后的0，杨同意
                        $clone_header_data[$h_key] =CvtUtil::emptyToDouble($page_row[$h_key]);
                    } else {
                        $clone_header_data[$h_key] = $page_row[$h_key];
                    }
                }
            }
            $data_list[] = $clone_header_data;
        }
        return [
            'data_header' => $field_list,
            'data_rows' => $data_list,
            'sum_data' => array_values($sum_data)
        ];
    }

    public function exportExcel($from_id,$builder){
        $form_row = WorkForm::findFirst(['id = ?1','bind'=>[1=>$from_id]]);
        $page_rows = $builder->getQuery()->execute();
        $ret = $this->getTableData($from_id, $page_rows);
        $file_title = $form_row->name;
        $objExcel = new \PHPExcel();
        //设置属性
        $objExcel->getProperties()->setCreator(SessionData::owner()->company);
        $objExcel->getProperties()->setLastModifiedBy(SessionData::owner()->company);
        $objExcel->getProperties()->setTitle($file_title);
        $objExcel->setActiveSheetIndex();
        $objActSheet = $objExcel->getActiveSheet();
        $objActSheet->setTitle($file_title);
        $data_header = $ret['data_header'];
        $column_data = [];
        foreach ($data_header as $header_item){
            if ($header_item['show'] == 1){
                $column_data[] = $header_item;
            }
        }
        $rows = $ret['data_rows'];
        if (count($column_data) == 0) {
            die('未配置该页面展示信息');
        } else if (count($column_data) > 52) {
            die('展示信息过多');
        }
        $letters = $this->getLetters(count($column_data));
        $last_no = $letters[count($letters) - 1];
        $objActSheet->mergeCells('A1'.':'.$last_no.'1');
        $objActSheet->setCellValue('A1', $file_title);
        $objActSheet->getStyle()->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objActSheet->getStyle()->getFont()->setBold(true);
        foreach ($letters as $idx => $letter)
        {
            $unit = '';
            if (!empty($column_data[$idx]['unit'])){
                $unit = '(' . $column_data[$idx]['unit'] . ')';
            }
            $objActSheet->setCellValue($letter.'2', $column_data[$idx]['name'].$unit);
            $objActSheet->getColumnDimension($letter)->setWidth(20);
        }
        $objExcel->getActiveSheet()->getStyle('a1:'.$last_no.'2')->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
        $objExcel->getActiveSheet()->getStyle('a1:'.$last_no.'2')->getFont()->setBold(true);
        $val = '';
        $row_no = 3;
        foreach($rows as $row) {
            $row_no_start = $row_no;
            foreach ($letters as $idx => $letter)
            {
                $cell_no = $letter.$row_no;
                if (array_key_exists($column_data[$idx]['id'],$row)){
                    if ($column_data[$idx]['type'] == 2) {
                        $objActSheet->setCellValue($cell_no, $row[$column_data[$idx]['id']]);
                    } else {
                        $objExcel->getActiveSheet()->setCellValueExplicit($cell_no, $row[$column_data[$idx]['id']], PHPExcel_Cell_DataType::TYPE_STRING);
                        $objExcel->getActiveSheet()->getStyle($cell_no)->getNumberFormat()->setFormatCode("@");
                    }
                } else {
                    $objActSheet->setCellValue($cell_no, '');
                }
            }
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getAlignment()->setWrapText(true);
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
            $objExcel->getActiveSheet()->getStyle('A'.$row_no_start.':'.$last_no.$row_no)->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
            $row_no++;
        }
        $objActSheet->getStyle('A1:'.$last_no.($row_no - 1))->getBorders()->getAllBorders()->setBorderStyle(\PHPExcel_Style_Border::BORDER_THIN);
        //清除缓冲区,避免乱码
        ob_end_clean();
        // 设置页方向和规模
        $objExcel->getActiveSheet()->getPageSetup()->setOrientation(\PHPExcel_Worksheet_PageSetup::ORIENTATION_PORTRAIT);
        $objExcel->getActiveSheet()->getPageSetup()->setPaperSize(\PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
        $objExcel->setActiveSheetIndex(0);

        $timestamp = time();
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$file_title.'_'.$timestamp.'.xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objExcel, 'Excel2007');
        $objWriter->save('php://output');
        exit;
    }

    public function getLetters($col_cnt)
    {
        $first_code = ord('A');
        $letters = [];
        $col_cnt_2 = 0;
        if ($col_cnt > 26) {
            $col_cnt_2 = $col_cnt - 26;
            $col_cnt = 26;
        }
        for ($i = 0; $i < $col_cnt; $i++)
        {
            array_push($letters, chr($first_code + $i));
        }
        if ($col_cnt_2 > 0) {
            for ($i = 0; $i < $col_cnt_2; $i++)
            {
                array_push($letters, 'A'.chr($first_code + $i));
            }
        }
        return $letters;
    }
}