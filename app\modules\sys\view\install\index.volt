{{ assets.outputJs('validate') }}

<!-- Main content -->
<div class="page-content">
    <div class="row">
        <div class="col-md-5 col-md-offset-4">
            <div class="portlet light" id="app">
                <div class="portlet-title">
                    <div class="caption">安装系统模块(SYS)</div>
                </div>
                <div class="portlet-body">
                    {#本模块是最基础的模块，含有如下功能:#}
                    {#<ul>#}
                        {#<li>登录用户管理</li>#}
                        {#<li>角色管理</li>#}
                        {#<li>系统参数设置</li>#}
                        {#<li>数据字典</li>#}
                    {#</ul>#}
                    {% if package is not empty %}
                        <div class="alert alert-danger"><i class="fa fa-info-circle"></i> 该模块已安装!版本：{{ package.version }}</div>
                    {% else %}
                        <button class="btn btn-primary" onclick="submit()"><i class="fa fa-check"></i> 安装</button>
                    {% endif %}
                </div>
                {#<div class="box-footer">#}
                    {#<button class="btn btn-primary" onclick="submit()"><i class="fa fa-check"></i> 安装</button>#}
                {#</div>#}
            </div>
        </div>
    </div>
</div>

<script>
function submit() {
    var dlg = top.layer.confirm('确定执行这个操作吗?', function () {
        top.layer.close(dlg);
        showSpin();
        $.post('{{ url('sys/install/exec') }}', '', function (rs) {
            closeSpin(null);
            if (rs.status == 'ok') {
                toastr.success('操作完毕!');
            }
            else {
                toastr.error('操作失败!' + rs.message);
            }
        })
    });
}
</script>
