<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInstockDetail;
use Envsan\Modules\Purchase\Model\PurchaseInvoiceDetail;
use Envsan\Modules\Purchase\Model\PurchasePay;
use Envsan\Modules\Purchase\Model\PurchasePayDetail;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class PurchasePayService extends Component
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
            't1.pay_no',
            't1.pay_date',
            't1.pay_money',
            't1.status',
            't1.status_name',
            't1.remarks',
            't2.order_code',
            't3.name as supplier_name',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchasePay', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchasePay();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $type = $this->request->getPost('type', 'tstring');
        $pay_date = $this->request->getPost('pay_date', 'tstring');
        $order_id = $this->request->getPost('order_id', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $files = urldecode($this->request->getPost('files', 'tstring'));
        $ext_data = str_replace('%2B','+',urldecode($this->request->getPost('ext_data', 'tstring')));
        $detail_ids = $this->request->getPost('detail_ids', 'tstring');
        if (empty($type) || empty($pay_date) || empty($order_id) || empty($detail_ids)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $show_msg = false;
        $table = new TableService();
        $files = CvtUtil::emptyToArray($files);
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
            $pay_money = PurchaseInstockDetail::sum([
                'column' => 'total_money',
                'conditions' => 'del_flag = 0 and id IN ('.$detail_ids.')',
            ]);

            $row->order_id = $order_id;
            $row->pay_date = $pay_date;
            $row->pay_money = $pay_money;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            if ($type == 1) {
                $row->status = 10;
            } else {
                $row->status = 20;
            }
            $row->status_name = Constant::$purchase_pay_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->pay_no = $ss->useSequence(20);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("purchase_pay表更新失败");
            }

            $detail_rows = PurchasePayDetail::find(['del_flag = 0 and pay_id = ?1', 'bind' => [1 => $row->id]]);
            $detail_ids = explode(',', $detail_ids);
            $old_ids = [];
            foreach ($detail_rows as $detail_row)
            {
                $old_ids[] = $detail_row->instock_detail_id;
                if (!in_array($detail_row->instock_detail_id, $detail_ids)) {
                    $detail_row->del_flag = 1;
                    $detail_row->update_date = $now;
                    $detail_row->update_by = $user->id;
                    if (!$detail_row->save()) {
                        throw new \Exception("purchase_pay_detail表更新失败");
                    }
                }
            }

            $new_ids = array_diff($detail_ids, $old_ids);
            foreach ($new_ids as $instock_detail_id)
            {
                $invoice_detail_row = PurchaseInvoiceDetail::findFirst([
                    'del_flag = 0 and instock_detail_id = ?1', 'bind' => [1 => $instock_detail_id]
                ]);
                if (empty($invoice_detail_row)) {
                    $show_msg = true;
                    throw new \Exception("采购开票明细不存在");
                }

                $detail_row = new PurchasePayDetail();
                $detail_row->pay_id = $row->id;
                $detail_row->invoice_id = $invoice_detail_row->invoice_id;
                $detail_row->invoice_detail_id = $invoice_detail_row->id;
                $detail_row->instock_detail_id = $instock_detail_id;
                $detail_row->create_date = $now;
                $detail_row->create_by = $user->id;
                $detail_row->update_date = $now;
                $detail_row->update_by = $user->id;
                $detail_row->del_flag = 0;
                $detail_row->group_id = $user->group_id;
                $detail_row->owner = $user->owner;
                if (!$detail_row->save()) {
                    throw new \Exception("purchase_pay_detail表更新失败");
                }
            }

            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            if ($show_msg) {
                return $e->getMessage();
            } else {
                Logger::error($e->getMessage());
                return ErrorHelper::UNKOWN;
            }
        }
        return '';
    }

    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchasePay')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return PurchasePay::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchasePay::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getInstockDetailList($order_id, $pay_id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.instock_detail_id,
                iv.invoice_no,
                itd.goods_code,
                itd.goods_name,
                itd.goods_spec,
                itd.goods_model,
                itd.goods_unit,
                itd.price,
                itd.quantity,
                itd.total_money,
                it.code as instock_code,
                it.instock_date,
                1 as show
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInvoiceDetail', 'a')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInvoice', 'a.invoice_id = iv.id and iv.del_flag = 0', 'iv')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 'a.instock_detail_id = itd.id and itd.del_flag = 0', 'itd')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInstock', 'itd.instock_id = it.id and it.del_flag = 0', 'it')
            ->where('a.del_flag = 0 and iv.status = 20 and pd.id is null and iv.order_id = ?1', [1 => $order_id])
            ->orderBy('a.id');

        if (empty($pay_id)) {
            $builder->leftJoin('Envsan\Modules\Purchase\Model\PurchasePayDetail',
                'a.id = pd.invoice_detail_id and pd.del_flag = 0', 'pd');
        } else {
            $builder->leftJoin('Envsan\Modules\Purchase\Model\PurchasePayDetail',
                'a.id = pd.invoice_detail_id and pd.del_flag = 0 and pd.pay_id <> '.$pay_id, 'pd');
        }

        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            $row['price'] = CvtUtil::emptyToDouble($row['price']);
            $row['quantity'] = CvtUtil::emptyToDouble($row['quantity']);
            $row['total_money'] = CvtUtil::emptyToDouble($row['total_money']);
        }
        return $rows;
    }

    public function getPayDetailList($pay_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.instock_detail_id,
                iv.invoice_no,
                itd.goods_code,
                itd.goods_name,
                itd.goods_spec,
                itd.goods_model,
                itd.goods_unit,
                itd.price,
                itd.quantity,
                itd.total_money,
                it.code as instock_code,
                it.instock_date
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchasePayDetail', 'a')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInvoice', 'a.invoice_id = iv.id and iv.del_flag = 0', 'iv')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 'a.instock_detail_id = itd.id and itd.del_flag = 0', 'itd')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInstock', 'itd.instock_id = it.id and it.del_flag = 0', 'it')
            ->where('a.del_flag = 0 and a.pay_id = ?1', [1 => $pay_id])
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.pay_no,
                t1.pay_date,
                t1.remarks,
                t1.ext_val,
                t2.order_code,
                t3.name as supplier_name,
                t5.invoice_no,
                t99.goods_code,
                t99.goods_name,
                t99.goods_spec,
                t99.goods_model,
                t99.goods_unit,
                t99.price,
                t99.quantity,
                t99.total_money
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchasePay', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchasePayDetail', 't1.id = t4.pay_id', 't4')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInvoice', 't4.invoice_id = t5.id', 't5')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 't4.instock_detail_id = t99.id', 't99')
            ->where('t1.del_flag = 0 and t4.del_flag = 0 and t99.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }
}