<?php
namespace Envsan\Common\Component;

use Envsan\Common\Data\SessionData;
use Phalcon\Mvc\User\Component;

class Acl extends Component
{
    public function has($act)
    {
        // 如果是管理员的话，则允许
        if(SessionData::isAdmin())
            return true;

        $acl=$this->session->get('acl');
        if($acl!=null)
            return $acl->isAllowed('role', 'permissions', $act);
        return false;
    }

    public function isSuper()
    {
        if(SessionData::isSuper())
            return true;
        return false;
    }

    public function isAdmin()
    {
        if(SessionData::isAdmin())
            return true;
        return false;
    }
}
