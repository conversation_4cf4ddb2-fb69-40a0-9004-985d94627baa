<div id="btn_select_pdf" style="display: none;">选择文件</div>
<script>
    var uploader = null;
    var uploader_server = 'http://**************:7733/';
    var file_upload_list = [];
    function initUpLoaderPdf(folder_name) {
        uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/main/lib/uploader/uploader.swf') }}',
            server:  uploader_server + 'upload',
            dnd: '#dnd_' + folder_name,
            pick: '#btn_select_pdf',
            accept: {
                title: 'Pdf',
                extensions: 'pdf',
                mimeTypes: 'application/pdf'
            },
            fileSingleSizeLimit: 10 * 1024 * 1024,
            fileNumLimit: 20,
            multiple: true
        });

        uploader.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，只支持pdf后缀的文件');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，文件大小不能超过100M');
            }
        });

        uploader.on('fileQueued', function(file) {
            fileQueued(file);
        });

        uploader.on('uploadSuccess', function(file, rs) {
            if (rs.status == 'ok'){
                file_upload_list.push({
                    id : file.id,
                    path : rs.path
                })
                if (file_upload_list.length == uploader.getFiles().length){
                    let ids = '';
                    for(let file of app.files){
                        ids += file.id + '|';
                    }
                    $.post(uploader_server + 'marge{{ upyun.baseDir }}'+ids, {
                        files : file_upload_list
                    } , function (rs) {
                        uploadSuccess(rs);
                    })
                }
            }
        });

        uploader.on('uploadError', function(file) {
            closeSpin();
            toastr.error('操作失败！');
        });
    }

    function uploadPdf() {
        $("#btn_select_pdf").find("label").click();
    }
</script>