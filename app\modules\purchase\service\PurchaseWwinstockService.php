<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesProduceLogs;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Model\MesStockLogs;
use Envsan\Modules\Purchase\Model\PurchaseApplyEntrust;
use Envsan\Modules\Purchase\Model\PurchaseOrder;
use Envsan\Modules\Purchase\Model\PurchaseSupplier;
use Envsan\Modules\Purchase\Model\PurchaseWwinstock;
use Envsan\Modules\Purchase\Model\PurchaseWwinstockDetail;
use Envsan\Modules\Purchase\Model\PurchaseWwoutstockDetail;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class PurchaseWwinstockService extends BaseService
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
            't1.code',
            't1.instock_date',
            't2.order_code',
            't1.status',
            't1.status_name',
            't1.remarks',
            't1.ext_val',
            't3.name as supplier_name',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwinstock', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseWwinstock();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {

        return $this->executeInTransaction(function () use ($act, $row) {
            $type = $this->request->getPost('type', 'tstring');
            $order_id = $this->request->getPost('order_id', 'tstring');
            $instock_date = $this->request->getPost('instock_date', 'tstring');
            $files = $this->request->getPost('files');
            $remarks = $this->request->getPost('remarks', 'tstring');
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
            $detail_list = $this->request->getPost('detail_data');

            if (empty($order_id) || empty($instock_date) || empty($detail_list))
                return ErrorHelper::WRONG_INPUT;

            if ($type != 1 && $type != 2)
                return ErrorHelper::WRONG_INPUT;

            $order_row = PurchaseOrder::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $order_id]]);
            if (empty($order_row)) {
                return ErrorHelper::WRONG_INPUT;
            }
            $supplier_row = PurchaseSupplier::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $order_row->supplier_id]]);
            if (empty($supplier_row)) {
                return ErrorHelper::WRONG_INPUT;
            }
            foreach ($detail_list as &$detail_item) {
                if (!CheckUtil::isDecimal($detail_item['quantity'])) {
                    return '无效的数量';
                }
                $detail_item['total_money'] = round(CvtUtil::emptyToDouble($detail_item['price']) * CvtUtil::emptyToDouble($detail_item['pricing_quantity']), 2);
                $detail_item['total_money_hs'] = round(CvtUtil::emptyToDouble($detail_item['price_hs']) * CvtUtil::emptyToDouble($detail_item['pricing_quantity']), 2);
            }
            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);

            $row->order_id = $order_id;
            $row->instock_date = $instock_date;
            $row->detail_data = CvtUtil::arrayToNull($detail_list);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->files = CvtUtil::arrayToNull($files);
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->status = $type == 1 ? 10 : 20;
            $row->status_name = Constant::$purchase_instock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->code = $ss->useSequence(6);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            $row->save();

            if ($type == 2) {
                foreach ($detail_list as $item) {
                    $detail_row = new PurchaseWwinstockDetail();
                    $detail_row->uid = UUID::make();
                    $detail_row->instock_id = $row->id;
                    $detail_row->entrust_id = $item['id'];
                    $detail_row->price = $item['price'];
                    $detail_row->price_hs = $item['price_hs'];
                    $detail_row->quantity = $item['quantity'];
                    $detail_row->pricing_quantity = $item['pricing_quantity'];
                    $detail_row->total_money = $item['total_money'];
                    $detail_row->total_money_hs = $item['total_money_hs'];
                    $detail_row->update_date = $now;
                    $detail_row->update_by = $user->id;
                    $detail_row->del_flag = 0;
                    $detail_row->owner = $user->owner;
                    $detail_row->save();
                    $entrust_row = PurchaseApplyEntrust::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $detail_row->entrust_id]]);
                    if (empty($entrust_row)) {
                        throw new \Exception(ErrorHelper::WRONG_INPUT);
                    }
                    $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $entrust_row->notice_detail_id]]);
                    if (empty($notice_detail_row)) {
                        throw new \Exception(ErrorHelper::WRONG_INPUT);
                    }
                    $bom_row = MesProductBom::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $entrust_row->product_bom_id]]);
                    if (empty($bom_row)) {
                        throw new \Exception(ErrorHelper::WRONG_INPUT);
                    }
                    $common = new \Envsan\Modules\Mes\Service\CommonService();
                    $log = new MesProduceLogs();
                    $log->uid = UUID::make();
                    $log->work_date = $row->instock_date;
                    $log->work_month = $common->getWorkMonth($row->instock_date);
                    $log->notice_detail_id = $entrust_row->notice_detail_id;
                    $log->bom_id = $entrust_row->product_bom_id;
                    $log->cost = $detail_row->price;
                    $log->cnt = $detail_row->quantity;
                    $log->work_type = 1;
                    $log->shift_type = 1;
                    $log->ww_instock_detail_id = $detail_row->id;
                    $log->staff_id = null;
                    $log->staff_name = $supplier_row->name;
                    $log->create_time = $now;
                    $log->update_date = $now;
                    $log->update_by = $user->id;
                    $log->del_flag = 0;
                    $log->owner = $user->owner;
                    $log->save();
                    if (empty($bom_row->nid)) {
                        $stock_log = new MesStockLogs();
                        $stock_log->uid = UUID::make();
                        $stock_log->produce_logs_id = $log->id;
                        $stock_log->notice_detail_id = $log->notice_detail_id;
                        $stock_log->product_id = $notice_detail_row->product_id;
                        $stock_log->work_month = $log->work_month;
                        $stock_log->work_date = $log->work_date;
                        $stock_log->cnt = $log->cnt;
                        $stock_log->staff_name = $supplier_row->name;
                        $stock_log->create_time = $now;
                        $stock_log->update_date = $now;
                        $stock_log->update_by = $user->id;
                        $stock_log->del_flag = 0;
                        $stock_log->owner = $user->owner;
                        $stock_log->save();
                    }
                }
            }
        });

    }

    public function selectById($id)
    {
        return PurchaseWwinstock::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseWwinstock::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        } else if ($row->status == 20) {
            return '已入库，不能删除';
        }

        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();

            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【外委入库删除】purchase_wwinstock表更新失败");
            }

            $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseWwinstockDetail';
            $phql .= ' SET del_flag = 1, update_date = ?2, update_by = ?3';
            $phql .= ' WHERE del_flag = 0 and outstock_id = ?1';
            $result = $this->modelsManager->executeQuery($phql, [
                1 => $row->id,
                2 => $now,
                3 => $user->id
            ]);
            if (!$result->success()) {
                throw new \Exception('【外委入库删除】purchase_wwinstock_detail表更新失败');
            }

            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function cancel($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        } else if ($row->status != 20) {
            return '未入库';
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            $row->status = 10;
            $row->status_name = Constant::$purchase_instock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【外委取消入库】purchase_wwinstock表更新失败");
            }
            $detail_rows = PurchaseWwinstockDetail::find(['del_flag = 0 and instock_id = ?1','bind'=>[1=>$row->id]]);
            foreach ($detail_rows as $detail_row){
                $detail_row->del_flag = 1;
                $detail_row->update_date = $now;
                $detail_row->update_by =  $user->id;
                if (!$detail_row->save()) {
                    throw new \Exception("【外委取消入库】purchase_wwinstock表更新失败");
                }
                $logs = MesProduceLogs::findFirst(['del_flag = 0 and ww_instock_detail_id = ?1','bind'=>[1=>$detail_row->id]]);
                if (!empty($logs)){
                    $logs->del_flag = 1;
                    $logs->update_date = $now;
                    $logs->update_by =  $user->id;
                    if (!$logs->save()) {
                        throw new \Exception("【外委取消入库】purchase_wwinstock表更新失败");
                    }
                    $stock_log = MesStockLogs::findFirst(['del_flag = 0 and produce_logs_id = ?1','bind'=>[1=>$logs->id]]);
                    if (!empty($stock_log)){
                        $stock_log->del_flag = 1;
                        $stock_log->update_date = $now;
                        $stock_log->update_by = $user->id;
                        if (!$stock_log->save()){
                            throw new \Exception("表更新失败");
                        }
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.code,
                a.instock_date,
                a.status,
                a.status_name,
                a.remarks,
                a.ext_val,
                o.order_code,
                o.order_date,
                s.name as supplier_name,
                d.price,
                d.quantity,
                d.total_money,
                t2.code as product_code,
                t2.name as product_name,
                t3.name as customer_name,
                t4.code as notice_code,
                t5.name as bom_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwinstock', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseWwinstockDetail', 'a.id = d.instock_id', 'd')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 'a.order_id = o.id', 'o')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'o.supplier_id = s.id', 's')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 'd.entrust_id = t.id','t')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t5.id','t5')
            ->where('a.del_flag = 0 and d.del_flag = 0 and a.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('a.id desc, d.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }


    public function getBomList($receipt_id)
    {
        if (empty($receipt_id)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t.product_name,
                t.bom_name,
                t.product_code,
                t.goods_unit,
                t.goods_deputy_unit,
                t.quantity,
                t.pricing_quantity,
                t.price,
                t.price_hs,
                t.total_amount,
                t.total_amount_hs,
                t.tax_amount,
                t.conversion_rate,
                t.tax_rate,
                t.check_flag,
                t.check_status,
                t.order_code,
                1 as show_flag,
                t4.code as notice_code
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwReceiptDetail', 't')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseWwReceipt', 't.receipt_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't2.id = t.apply_id', 't2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't2.notice_detail_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't4.id = t3.notice_id','t4')
            ->where('t.del_flag = 0 and t1.del_flag = 0 and t1.id = ?1', [1 => $receipt_id])
            ->orderBy('t.id asc');


//        $builder = $this->modelsManager->createBuilder()
//            ->columns('
//                t.id,
//                t.uid,
//                t.quantity as plan_cnt,
//                t2.code as product_code,
//                t2.name as product_name,
//                t3.name as customer_name,
//                t4.code,
//                t5.name as bom_name,
//                t10.price,
//                t10.price_hs,
//                t10.purchase_quantity as pricing_cnt,
//                t10.conversion_rate,
//                ifnull(t8.quantity,0) as out_cnt,
//                ifnull(t9.quantity,0) as in_cnt,
//                1 as show_flag
//            ')
//            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id','t1')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
//            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t5.id','t5')
//            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustOrderOut','t.id = t8.entrust_id','t8')
//            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustOrderIn','t8.entrust_id = t9.entrust_id and t8.order_id = t9.order_id','t9')
//            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't.purchase_order_id = t7.id and t7.id = ?1', 't7')
//            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrderDetail', 't7.id = t10.order_id and t.id = t10.apply_id', 't10')
//            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseReceipt', 't.purchase_order_id = t7.id and t7.id = ?1', 't7')
//            ->where('t1.del_flag = 0 and t8.order_id = ?1', [1 => $order_id])
//            ->orderBy('t1.id asc');
        return $builder->getQuery()->execute();
    }

    public function getWwArriveList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.order_code,
                t2.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwReceipt', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $builder->andWhere('t1.status = 30');
        return $builder->getQuery()->execute();
    }
}