<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

class SequenceDetail extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=false)
     */
    public $type;

    /**
     *
     * @var string
     * @Column(type="string", length=8, nullable=true)
     */
    public $seq_date;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $no_count;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_sequence_detail';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return SequenceDetail[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return SequenceDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
