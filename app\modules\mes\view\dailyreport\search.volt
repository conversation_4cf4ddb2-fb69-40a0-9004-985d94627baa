{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">工时日报查询</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-4">
                        <div class="form-group">
                            <label class="col-md-3 control-label">日报时间</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control date dtpicker" name="date_start" v-model="date_start" placeholder="开始时间"/>
                                    <span class="input-group-addon no-border-lr">至</span>
                                    <input type="text" class="form-control date dtpicker" name="date_end" v-model="date_end" placeholder="结束时间"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-4">
                        <div class="form-group">
                            <label class="col-md-3 control-label">生产批次</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" v-model="notice_code" placeholder="生产批次"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 col-lg-4">
                        <div class="form-group">
                            <label class="col-md-3 control-label">产品名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" v-model="product_name" placeholder="产品名称"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-4">
                        <div class="form-group">
                            <label class="col-md-3 control-label">规格型号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" v-model="product_code" placeholder="规格型号"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="false"
                   data-query-params="app.params"
                   data-url="{{ url('/mes/dailyreport/search/json') }}"
                   data-side-pagination="client">
                <thead class="bg-blue">
                <tr>
                    <th data-field="notice_code">生产批次</th>
                    <th data-field="product_name">产品名称</th>
                    <th data-field="product_code">规格型号</th>
                    <th data-field="total_hour">实际人工工时</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    
    // 获取昨天的日期
    function getYesterday() {
        var yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        var year = yesterday.getFullYear();
        var month = ('0' + (yesterday.getMonth() + 1)).slice(-2);
        var day = ('0' + yesterday.getDate()).slice(-2);
        return year + '-' + month + '-' + day;
    }
    
    var app = new Vue({
        el: '#app',
        data: {
            date_start: getYesterday(),
            date_end: getYesterday(),
            notice_code: '',
            product_name: '',
            product_code: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refresh');
            },
            params: function(p) {
                p.date_start = this.date_start;
                p.date_end = this.date_end;
                p.notice_code = this.notice_code;
                p.product_name = this.product_name;
                p.product_code = this.product_code;
                return p;
            },
            reset: function() {
                this.date_start = getYesterday();
                this.date_end = getYesterday();
                this.notice_code = '';
                this.product_name = '';
                this.product_code = '';
                $table.bootstrapTable('refresh');
            }
        }
    });

    $table.bootstrapTable();


    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>