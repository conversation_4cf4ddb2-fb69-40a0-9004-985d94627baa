<?php

namespace Envsan\Modules\Quality\Api;

use Envsan\Common\Base\ApiController;
use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DomainUtil;
use Envsan\Modules\Sys\Model\Owner;

class SuperController extends ApiController
{
    public function beforeExecuteRoute(\Phalcon\Mvc\Dispatcher $dispatcher)
    {
        // 不使用cookie
//        ini_set('session.use_cookies', '0');

        // 允许跨域
        header('Access-Control-Allow-Origin:*');

        // 设置所有的输出json格式
        $this->setJsonResponse();

        // 允许客户端上传SID作为session id
        header('Access-Control-Allow-Headers:SID, AUTH, Content-Type');

        // http会发送options查询各种支持的协议，die即可
        if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'OPTIONS')
            die();

//        SessionData::$_USER_KEY = 'ipcuser';
//        SessionData::$_OWNER_KEY = 'ipcowner';

        // 设置默认的owner
        if (!$this->session->has(SessionData::$_OWNER_KEY)) {
            if ($this->config->developMode) {
                $owner = Owner::findFirst(['id=?1', 'bind' => [1 => $this->config->developOwner]]);
                if($owner == null)
                    die(ErrorHelper::ROW_NOTEXIST);
            } else {
                $owner = DomainUtil::checkOwner(true);
            }

            $this->session->set(SessionData::$_OWNER_KEY, $owner);
        }

        // 处理json体请求
        $this->handleRequestPayload();

        $controllerClass = $dispatcher->getControllerClass();
        // class级别的noacl
        $collection = $this->annotations->get($controllerClass)->getClassAnnotations();
        if ($collection != false && $collection->has('noacl'))
            return true;

        $annotations = $this->annotations->getMethod($controllerClass, $dispatcher->getActiveMethod());
        if ($annotations->has('noacl'))
            return true;

        if ($this->session->has(SessionData::$_USER_KEY))
            return true;

        $this->make4011();
        return false;
    }
}