<?php

return [
    'header' => '销售中心',
    'id' => 'e-trade',
    'icon' => 'icon-basket-loaded',
    'module' => 'trade',
    'active' => false,
    'items' => [
        ['name' => '客户管理', 'controller' => 'customer', 'url' => 'trade/customer/list', 'identity' => 'trade:customer:list', 'icon' => 'fa fa-fw fa-briefcase'],
        ['name' => '订单管理','controller'=>'order', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '订单管理', 'url' => 'trade/order/list', 'identity' => 'trade:order:list'],
            ['name' => '订单查询', 'url' => 'trade/order/search', 'identity' => 'trade:order:search']
        ]],
        ['name' => '库存查询', 'controller' => 'stock', 'url' => 'trade/stock/list', 'identity' => 'trade:stock:list', 'icon' => 'fa fa-fw fa-briefcase'],
        ['name' => '发货管理','controller'=>'outstock', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '发货管理', 'url' => 'trade/outstock/list', 'identity' => 'trade:outstock:list'],
            ['name' => '发货查询', 'url' => 'trade/outstock/search', 'identity' => 'trade:outstock:search'],
            ['name' => '发货汇总', 'url' => 'trade/outstock/summary', 'identity' => 'trade:outstock:summary']
        ]],
        ['name' => '收款管理', 'controller' => 'receive', 'url' => 'trade/receive/list', 'identity' => 'trade:receive:list', 'icon' => 'fa fa-fw fa-credit-card'],
        ['name' => '开票管理', 'controller' => 'invoice', 'url' => 'trade/invoice/list', 'identity' => 'trade:invoice:list', 'icon' => 'fa fa-fw fa-cny'],
    ]
];