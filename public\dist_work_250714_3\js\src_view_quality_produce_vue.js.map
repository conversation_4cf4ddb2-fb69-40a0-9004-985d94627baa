{"version": 3, "file": "js/src_view_quality_produce_vue.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAmLA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACndA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://rrts-manager/src/view/quality/produce.vue", "webpack://rrts-manager/./src/view/quality/produce.vue", "webpack://rrts-manager/./src/view/quality/produce.vue?ac6c", "webpack://rrts-manager/./src/view/quality/produce.vue?252f", "webpack://rrts-manager/./src/view/quality/produce.vue?78fe"], "sourcesContent": ["<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"生产质检\" is_back=\"1\"></m-header>\r\n        <m-body padding=\"false\">\r\n            <div v-if=\"loading\" style=\"padding-top: 200px;text-align: center\">\r\n                <van-loading type=\"spinner\" color=\"#1989fa\" />\r\n            </div>\r\n            <div v-else style=\"padding-bottom: 200px\">\r\n                <van-cell-group>\r\n                    <van-cell title=\"生产批次号\" :value=\"data.notice_code\" />\r\n                    <van-cell title=\"产品名称\" :value=\"data.product_name\" />\r\n                    <van-cell title=\"规格型号\" :value=\"data.product_code\" />\r\n                    <van-field\r\n                            @click-input=\"quality_show_flag = true\"\r\n                            required\r\n                            name=\"质检工艺\"\r\n                            label=\"质检工艺\"\r\n                            :is-link=\"true\"\r\n                            placeholder=\"选择质检工艺\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                    />\r\n                    <van-popup\r\n                            v-model=\"quality_show_flag\"\r\n                            position=\"bottom\"\r\n                            style=\"height: 350px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <van-picker\r\n                                title=\"选择质检工艺\"\r\n                                show-toolbar\r\n                                value-key=\"name\"\r\n                                :columns=\"quality_list\"\r\n                                @cancel= \"quality_show_flag = false\"\r\n                                @confirm=\"selectQuality\"\r\n                        />\r\n                    </van-popup>\r\n                    <van-cell title=\"质检工艺\" :value=\"data.bom_name\" />\r\n                    <van-cell title=\"质检项目\" :value=\"data.quality_template_name\" />\r\n                    <van-field\r\n                            @click-input=\"worker_show_flag = true\"\r\n                            name=\"生产工人\"\r\n                            label=\"生产工人\"\r\n                            :is-link=\"true\"\r\n                            placeholder=\"选择生产工人\"\r\n                            :value=\"getSelectedWorkersText()\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                    />\r\n                    <van-popup\r\n                            v-model=\"worker_show_flag\"\r\n                            position=\"bottom\"\r\n                            style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <div style=\"text-align: center; font-size: 16px; font-weight: bold; margin-bottom: 20px;\">选择生产工人</div>\r\n                        <van-checkbox-group v-model=\"production_workers\" style=\"max-height: 280px; overflow-y: auto;\">\r\n                            <van-checkbox\r\n                                    v-for=\"worker in worker_list\"\r\n                                    :key=\"worker.id\"\r\n                                    :name=\"worker.id\"\r\n                                    style=\"display: flex; align-items: center; margin-bottom: 10px;\"\r\n                            >\r\n                                {{ worker.name }}\r\n                            </van-checkbox>\r\n                        </van-checkbox-group>\r\n                        <div style=\"margin-top: 20px;\">\r\n                            <van-button round block type=\"primary\" @click=\"confirmWorkerSelection\">确定</van-button>\r\n                        </div>\r\n                    </van-popup>\r\n                    <van-field\r\n                            @click-input=\"tool_show_flag = true\"\r\n                            name=\"测量工具\"\r\n                            label=\"测量工具\"\r\n                            :is-link=\"true\"\r\n                            placeholder=\"选择测量工具\"\r\n                            :value=\"getSelectedToolsText()\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                    />\r\n                    <van-popup\r\n                            v-model=\"tool_show_flag\"\r\n                            position=\"bottom\"\r\n                            style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <div style=\"text-align: center; font-size: 16px; font-weight: bold; margin-bottom: 20px;\">选择测量工具</div>\r\n                        <van-checkbox-group v-model=\"check_tools\" style=\"max-height: 280px; overflow-y: auto;\">\r\n                            <van-checkbox\r\n                                    v-for=\"tool in tool_list\"\r\n                                    :key=\"tool.id\"\r\n                                    :name=\"tool.id\"\r\n                                    style=\"display: flex; align-items: center; margin-bottom: 10px;\"\r\n                            >\r\n                                {{ tool.name }}\r\n                            </van-checkbox>\r\n                        </van-checkbox-group>\r\n                        <div style=\"margin-top: 20px;\">\r\n                            <van-button round block type=\"primary\" @click=\"confirmToolSelection\">确定</van-button>\r\n                        </div>\r\n                    </van-popup>\r\n                    <van-cell v-if=\"drawing_data.length > 0\" title=\"查看图纸\" @click=\"previewImg\" is-link/>\r\n                </van-cell-group>\r\n                <div>\r\n                    <template v-for=\"(item,idx) in check_data\">\r\n                        <quality-field :data=\"item\" @change=\"checkResult\"></quality-field>\r\n                    </template>\r\n                </div>\r\n                <van-cell-group  v-if=\"data.bom_id != ''\">\r\n                    <van-cell title=\"质检结果\">\r\n                        <template #right-icon>\r\n                            <van-tag v-if=\"error_result == 0\" type=\"success\">OK</van-tag>\r\n                            <van-tag v-else type=\"danger\">NG</van-tag>\r\n                        </template>\r\n                    </van-cell>\r\n                </van-cell-group>\r\n                <van-cell-group v-if=\"error_result == 1 && data.bom_id != ''\">\r\n                    <van-field\r\n                            @click-input=\"error_show = true\"\r\n                            type=\"number\"\r\n                            name=\"不合格数量\"\r\n                            label=\"不合格数量\"\r\n                            :is-link=\"true\"\r\n                            v-model=\"error_cnt\"\r\n                            placeholder=\"请输入不合格数量\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                            style=\"color: red\"\r\n                    >\r\n                        <template #button>\r\n                            <span>(件)</span>\r\n                        </template>\r\n                    </van-field>\r\n                    <van-number-keyboard\r\n                            :show=\"error_show\"\r\n                            theme=\"custom\"\r\n                            extra-key=\".\"\r\n                            close-button-text=\"完成\"\r\n                            @blur=\"error_show = false\"\r\n                            @input=\"onErrorInput\"\r\n                            @delete=\"onErrorDelete\"\r\n                    />\r\n                    <van-field\r\n                            @click-input=\"type_show_flag = true\"\r\n                            name=\"不合格类型\"\r\n                            label=\"不合格类型\"\r\n                            :is-link=\"true\"\r\n                            placeholder=\"选择不合格类型\"\r\n                            v-model=\"error_type\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                    />\r\n                    <van-popup\r\n                            v-model=\"type_show_flag\"\r\n                            position=\"bottom\"\r\n                            style=\"height: 350px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <van-picker\r\n                                title=\"选择不合格类型\"\r\n                                show-toolbar\r\n                                value-key=\"name\"\r\n                                :columns=\"error_types\"\r\n                                @cancel= \"type_show_flag = false\"\r\n                                @confirm=\"selectType\"/>\r\n                    </van-popup>\r\n                    <van-field\r\n                            :is-link=\"true\"\r\n                            v-model=\"error_remarks\"\r\n                            rows=\"2\"\r\n                            autosize\r\n                            type=\"textarea\"\r\n                            name=\"不合格原因\"\r\n                            label=\"不合格原因\"\r\n                            input-align=\"right\"\r\n                            placeholder=\"请输入不合格原因\"\r\n                    />\r\n                </van-cell-group>\r\n                <div style=\"position: absolute;bottom:0;width: 100%;padding: 10px;border-top: 1px #F2F2F2 solid;background-color: #FFFFFF;z-index: 99;\">\r\n                    <van-button  round block type=\"info\" @click=\"onSubmit\">提交</van-button>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {Dialog} from \"vant\";\r\n    import base from '../../components/base';\r\n    import qualityField from '../../components/quality_field';\r\n    import { ImagePreview } from 'vant';\r\n\r\n    export default {\r\n        name: \"qualityProduce\",\r\n        extends: base,\r\n        components: {qualityField,ImagePreview},\r\n        data() {\r\n            return {\r\n                code:'',\r\n                base_path:'',\r\n                loading: true,\r\n                data:{},\r\n                check_data:[],\r\n                drawing_data:[],\r\n                quality_list:[],\r\n                error_result:0,\r\n                cnt_show:false,\r\n                error_cnt:'',\r\n                error_show:false,\r\n                error_types : [],\r\n                error_type : '',\r\n                error_remarks : '',\r\n                type_show_flag:false,\r\n                quality_show_flag:false,\r\n                worker_list: [],\r\n                production_workers: [],\r\n                worker_show_flag: false,\r\n                tool_list: [],\r\n                check_tools: [],\r\n                tool_show_flag: false\r\n            }\r\n        },\r\n        methods: {\r\n            onLoad(){\r\n                let user = this.$store.state.user;\r\n                this.base_path = user.imgdir;\r\n                this.code = this.$route.params.code;\r\n                this.init();\r\n            },\r\n            onShow(){\r\n\r\n            },\r\n            init(){\r\n                this.$http.post('/work/quality/init',{code:this.code}).then((rs) => {\r\n                    if (rs.status === 'ok') {\r\n                        this.loading = false;\r\n                        this.data  =  rs.data.data;\r\n                        this.quality_list  =  rs.data.quality_list;\r\n                        this.error_types  =  rs.data.error_types;\r\n                        this.error_result = 0;\r\n                        this.check_data = [];\r\n                        // 初始化工人列表，实际应该从后端获取\r\n                        this.worker_list = rs.data.worker_list;\r\n                        // 初始化测量工具列表，实际应该从后端获取\r\n                        this.tool_list = rs.data.tool_list;\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '异常消息',\r\n                            message: rs.message,\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                }).catch(() => {\r\n                    Dialog.alert({\r\n                        title: '网络异常',\r\n                        message: '网络异常',\r\n                    }).then(() => {\r\n                        this.$router.back();\r\n                    });\r\n                });\r\n            },\r\n            selectQuality(obj){\r\n                if (this.data.bom_id == obj.product_bom_id && this.data.quality_template_id == obj.id){\r\n                    this.quality_show_flag = false;\r\n                    return;\r\n                }\r\n                this.data.bom_id = obj.product_bom_id;\r\n                this.data.bom_name = obj.bom_name;\r\n                this.data.quality_template_id = obj.id;\r\n                this.data.quality_template_name = obj.quality_template_name;\r\n                this.check_data = JSON.parse(JSON.stringify(obj.form_data));\r\n                this.drawing_data = obj.drawing_data;\r\n                this.error_result = 0;\r\n                this.quality_show_flag = false;\r\n            },\r\n            previewImg() {\r\n                let images = [];\r\n                for (let i = 0; i < this.drawing_data.length; i++) {\r\n                    images.push(this.base_path + this.drawing_data[i].url);\r\n                }\r\n                ImagePreview({\r\n                    maxZoom:5,\r\n                    images: images,\r\n                    startPosition: 0\r\n                });\r\n            },\r\n            checkResult(cb){\r\n                this.error_result = 0;\r\n                for(let check_item of this.check_data){\r\n                    check_item.result = 0;\r\n                    if (check_item.type == 6){\r\n                        for(let i = 0 ; i < check_item.values.length; i++){\r\n                            check_item.results[i] = 0;\r\n                            if (check_item.values[i] == 1){\r\n                                if (check_item.result == 0){\r\n                                    check_item.result = 1;\r\n                                }\r\n                                check_item.results[i] = 1;\r\n                            }\r\n                        }\r\n                    } else if (check_item.type == 7){\r\n                        check_item.result = 0;\r\n                        for(let i = 0 ; i < check_item.values.length; i++){\r\n                            check_item.results[i] = 0;\r\n                            if (check_item.values[i] != ''){\r\n                                let val = '';\r\n                                for(let item of check_item.formula_list) {\r\n                                    if (item.t == 3) {\r\n                                        val += check_item.values[i];\r\n                                    } else {\r\n                                        val += item.v;\r\n                                    }\r\n                                }\r\n                                try {\r\n                                    let res = eval(val);\r\n                                    res = Number(res.toFixed(4));\r\n                                    if (!(res >= parseFloat(check_item.standard_minus) && res <= parseFloat(check_item.standard_plus))){\r\n                                        check_item.results[i] = 1;\r\n                                    }\r\n                                } catch (e){\r\n                                    check_item.results[i] = 1;\r\n                                }\r\n                                if (check_item.result == 0){\r\n                                    check_item.result = check_item.results[i];\r\n                                }\r\n                            }\r\n                        }\r\n                    } else if (check_item.type == 8){\r\n                        check_item.result = 0;\r\n                        let min = parseFloat(check_item.standard_val) - parseFloat(check_item.standard_minus);\r\n                        let max = parseFloat(check_item.standard_val) + parseFloat(check_item.standard_plus);\r\n                        for(let i = 0 ; i < check_item.values.length; i++){\r\n                            check_item.results[i] = 0;\r\n                            if (check_item.values[i] != ''){\r\n                                let res = parseFloat(check_item.values[i]);\r\n                                if (!(res >= min && res <= max)){\r\n                                    check_item.results[i] = 1;\r\n                                }\r\n                                if (check_item.result == 0){\r\n                                    check_item.result = check_item.results[i];\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    if (this.error_result == 0){\r\n                        this.error_result = check_item.result;\r\n                    }\r\n                }\r\n                cb();\r\n            },\r\n            selectType(obj){\r\n                if (this.error_type == obj.name){\r\n                    this.type_show_flag = false;\r\n                    return;\r\n                }\r\n                this.error_type = obj.name;\r\n                this.type_show_flag = false;\r\n            },\r\n            onErrorInput(v){\r\n                try{\r\n                    if (parseFloat(this.error_cnt) > 99){\r\n                        return;\r\n                    }\r\n                    this.error_cnt = this.error_cnt + '' + v;\r\n                }catch (e){}\r\n            },\r\n            onErrorDelete(v){\r\n                if (this.error_cnt == ''){\r\n                    return;\r\n                }\r\n                this.error_cnt = this.error_cnt.substring(0,this.error_cnt.length-1);\r\n                if (this.error_cnt == ''){\r\n                    this.error_type = '';\r\n                    this.error_remarks = '';\r\n                }\r\n            },\r\n            getSelectedWorkersText(){\r\n                if (this.production_workers.length === 0) {\r\n                    return '';\r\n                }\r\n                let selectedNames = [];\r\n                for (let workerId of this.production_workers) {\r\n                    let worker = this.worker_list.find(w => w.id === workerId);\r\n                    if (worker) {\r\n                        selectedNames.push(worker.name);\r\n                    }\r\n                }\r\n                return selectedNames.join(', ');\r\n            },\r\n            confirmWorkerSelection(){\r\n                this.worker_show_flag = false;\r\n                if (this.production_workers.length > 0) {\r\n                    this.$toast.success(`已选择 ${this.production_workers.length} 名工人`);\r\n                }\r\n            },\r\n            getSelectedToolsText(){\r\n                if (this.check_tools.length === 0) {\r\n                    return '';\r\n                }\r\n                let selectedNames = [];\r\n                for (let toolId of this.check_tools) {\r\n                    let tool = this.tool_list.find(t => t.id === toolId);\r\n                    if (tool) {\r\n                        selectedNames.push(tool.name);\r\n                    }\r\n                }\r\n                return selectedNames.join(', ');\r\n            },\r\n            confirmToolSelection(){\r\n                this.tool_show_flag = false;\r\n                if (this.check_tools.length > 0) {\r\n                    this.$toast.success(`已选择 ${this.check_tools.length} 个测量工具`);\r\n                }\r\n            },\r\n            onSubmit(){\r\n                for(let item of this.check_data){\r\n                    if (item.required == 1){\r\n                        if (item.type == 1 || item.type == 3 || item.type == 5){\r\n                            if (item.value == ''){\r\n                                this.$toast.fail('请选择'+item.title);\r\n                                return;\r\n                            }\r\n                            if (item.values.length == 0){\r\n                                this.$toast.fail('请选择'+item.title);\r\n                                return;\r\n                            }\r\n                        } else if (item.type == 4){\r\n                            if (item.values.length == 0){\r\n                                this.$toast.fail('请选择'+item.title);\r\n                                return;\r\n                            }\r\n                        } else if (item.type == 2 || item.type == 7 || item.type == 8){\r\n                            for(let value of item.values){\r\n                                if (value == ''){\r\n                                    this.$toast.fail('请输入'+item.title);\r\n                                    return;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (this.error_result == 1){\r\n                    if (this.error_cnt == ''){\r\n                        this.$toast.fail('请输入不合格数量');\r\n                        return;\r\n                    }\r\n                    if (this.error_type == ''){\r\n                        this.$toast.fail('请选择不合格原因');\r\n                        return;\r\n                    }\r\n                }\r\n                this.$http.post('work/quality/save', {\r\n                    notice_detail_id: this.data.id,\r\n                    bom_id: this.data.bom_id,\r\n                    quality_template_id:this.data.quality_template_id,\r\n                    error_result:this.error_result,\r\n                    error_cnt:this.error_cnt,\r\n                    error_type:this.error_type,\r\n                    error_remarks:this.error_remarks,\r\n                    check_data:encodeURI(JSON.stringify(this.check_data)),\r\n                    production_workers: this.production_workers,\r\n                    check_tools: this.check_tools\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.$toast.success('提交成功');\r\n                        this.$router.back();\r\n                    } else {\r\n                        this.$toast.fail(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$toast.fail('网络异常');\r\n                });\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"生产质检\",\"is_back\":\"1\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[(_vm.loading)?_c('div',{staticStyle:{\"padding-top\":\"200px\",\"text-align\":\"center\"}},[_c('van-loading',{attrs:{\"type\":\"spinner\",\"color\":\"#1989fa\"}})],1):_c('div',{staticStyle:{\"padding-bottom\":\"200px\"}},[_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"生产批次号\",\"value\":_vm.data.notice_code}}),_c('van-cell',{attrs:{\"title\":\"产品名称\",\"value\":_vm.data.product_name}}),_c('van-cell',{attrs:{\"title\":\"规格型号\",\"value\":_vm.data.product_code}}),_c('van-field',{attrs:{\"required\":\"\",\"name\":\"质检工艺\",\"label\":\"质检工艺\",\"is-link\":true,\"placeholder\":\"选择质检工艺\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.quality_show_flag = true}}}),_c('van-popup',{staticStyle:{\"height\":\"350px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.quality_show_flag),callback:function ($$v) {_vm.quality_show_flag=$$v},expression:\"quality_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择质检工艺\",\"show-toolbar\":\"\",\"value-key\":\"name\",\"columns\":_vm.quality_list},on:{\"cancel\":function($event){_vm.quality_show_flag = false},\"confirm\":_vm.selectQuality}})],1),_c('van-cell',{attrs:{\"title\":\"质检工艺\",\"value\":_vm.data.bom_name}}),_c('van-cell',{attrs:{\"title\":\"质检项目\",\"value\":_vm.data.quality_template_name}}),_c('van-field',{attrs:{\"name\":\"生产工人\",\"label\":\"生产工人\",\"is-link\":true,\"placeholder\":\"选择生产工人\",\"value\":_vm.getSelectedWorkersText(),\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.worker_show_flag = true}}}),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.worker_show_flag),callback:function ($$v) {_vm.worker_show_flag=$$v},expression:\"worker_show_flag\"}},[_c('div',{staticStyle:{\"text-align\":\"center\",\"font-size\":\"16px\",\"font-weight\":\"bold\",\"margin-bottom\":\"20px\"}},[_vm._v(\"选择生产工人\")]),_c('van-checkbox-group',{staticStyle:{\"max-height\":\"280px\",\"overflow-y\":\"auto\"},model:{value:(_vm.production_workers),callback:function ($$v) {_vm.production_workers=$$v},expression:\"production_workers\"}},_vm._l((_vm.worker_list),function(worker){return _c('van-checkbox',{key:worker.id,staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"margin-bottom\":\"10px\"},attrs:{\"name\":worker.id}},[_vm._v(\" \"+_vm._s(worker.name)+\" \")])}),1),_c('div',{staticStyle:{\"margin-top\":\"20px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"primary\"},on:{\"click\":_vm.confirmWorkerSelection}},[_vm._v(\"确定\")])],1)],1),_c('van-field',{attrs:{\"name\":\"测量工具\",\"label\":\"测量工具\",\"is-link\":true,\"placeholder\":\"选择测量工具\",\"value\":_vm.getSelectedToolsText(),\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.tool_show_flag = true}}}),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.tool_show_flag),callback:function ($$v) {_vm.tool_show_flag=$$v},expression:\"tool_show_flag\"}},[_c('div',{staticStyle:{\"text-align\":\"center\",\"font-size\":\"16px\",\"font-weight\":\"bold\",\"margin-bottom\":\"20px\"}},[_vm._v(\"选择测量工具\")]),_c('van-checkbox-group',{staticStyle:{\"max-height\":\"280px\",\"overflow-y\":\"auto\"},model:{value:(_vm.check_tools),callback:function ($$v) {_vm.check_tools=$$v},expression:\"check_tools\"}},_vm._l((_vm.tool_list),function(tool){return _c('van-checkbox',{key:tool.id,staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"margin-bottom\":\"10px\"},attrs:{\"name\":tool.id}},[_vm._v(\" \"+_vm._s(tool.name)+\" \")])}),1),_c('div',{staticStyle:{\"margin-top\":\"20px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"primary\"},on:{\"click\":_vm.confirmToolSelection}},[_vm._v(\"确定\")])],1)],1),(_vm.drawing_data.length > 0)?_c('van-cell',{attrs:{\"title\":\"查看图纸\",\"is-link\":\"\"},on:{\"click\":_vm.previewImg}}):_vm._e()],1),_c('div',[_vm._l((_vm.check_data),function(item,idx){return [_c('quality-field',{attrs:{\"data\":item},on:{\"change\":_vm.checkResult}})]})],2),(_vm.data.bom_id != '')?_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"质检结果\"},scopedSlots:_vm._u([{key:\"right-icon\",fn:function(){return [(_vm.error_result == 0)?_c('van-tag',{attrs:{\"type\":\"success\"}},[_vm._v(\"OK\")]):_c('van-tag',{attrs:{\"type\":\"danger\"}},[_vm._v(\"NG\")])]},proxy:true}],null,false,1582244380)})],1):_vm._e(),(_vm.error_result == 1 && _vm.data.bom_id != '')?_c('van-cell-group',[_c('van-field',{staticStyle:{\"color\":\"red\"},attrs:{\"type\":\"number\",\"name\":\"不合格数量\",\"label\":\"不合格数量\",\"is-link\":true,\"placeholder\":\"请输入不合格数量\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.error_show = true}},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [_c('span',[_vm._v(\"(件)\")])]},proxy:true}],null,false,3021629863),model:{value:(_vm.error_cnt),callback:function ($$v) {_vm.error_cnt=$$v},expression:\"error_cnt\"}}),_c('van-number-keyboard',{attrs:{\"show\":_vm.error_show,\"theme\":\"custom\",\"extra-key\":\".\",\"close-button-text\":\"完成\"},on:{\"blur\":function($event){_vm.error_show = false},\"input\":_vm.onErrorInput,\"delete\":_vm.onErrorDelete}}),_c('van-field',{attrs:{\"name\":\"不合格类型\",\"label\":\"不合格类型\",\"is-link\":true,\"placeholder\":\"选择不合格类型\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.type_show_flag = true}},model:{value:(_vm.error_type),callback:function ($$v) {_vm.error_type=$$v},expression:\"error_type\"}}),_c('van-popup',{staticStyle:{\"height\":\"350px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.type_show_flag),callback:function ($$v) {_vm.type_show_flag=$$v},expression:\"type_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择不合格类型\",\"show-toolbar\":\"\",\"value-key\":\"name\",\"columns\":_vm.error_types},on:{\"cancel\":function($event){_vm.type_show_flag = false},\"confirm\":_vm.selectType}})],1),_c('van-field',{attrs:{\"is-link\":true,\"rows\":\"2\",\"autosize\":\"\",\"type\":\"textarea\",\"name\":\"不合格原因\",\"label\":\"不合格原因\",\"input-align\":\"right\",\"placeholder\":\"请输入不合格原因\"},model:{value:(_vm.error_remarks),callback:function ($$v) {_vm.error_remarks=$$v},expression:\"error_remarks\"}})],1):_vm._e(),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"width\":\"100%\",\"padding\":\"10px\",\"border-top\":\"1px #F2F2F2 solid\",\"background-color\":\"#FFFFFF\",\"z-index\":\"99\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"info\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"提交\")])],1)],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./produce.vue?vue&type=template&id=e3e3b682&scoped=true\"\nimport script from \"./produce.vue?vue&type=script&lang=js\"\nexport * from \"./produce.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e3e3b682\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('e3e3b682')) {\n      api.createRecord('e3e3b682', component.options)\n    } else {\n      api.reload('e3e3b682', component.options)\n    }\n    module.hot.accept(\"./produce.vue?vue&type=template&id=e3e3b682&scoped=true\", function () {\n      api.rerender('e3e3b682', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/quality/produce.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=template&id=e3e3b682&scoped=true\""], "names": [], "sourceRoot": ""}