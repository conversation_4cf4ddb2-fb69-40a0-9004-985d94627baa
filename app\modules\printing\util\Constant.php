<?php
namespace Envsan\Modules\Printing\Util;

class Constant
{
    public static $print_data_list = [
        6 => [
            'page_name' => '生产批次单',
            'page_data' => [
                'code' => ['name' => '批次号', 'type' => 1],
                'uid' => ['name' => '二维码', 'type' => 2],
                'customer_name' => ['name' => '客户', 'type' => 1],
                'product_code' => ['name' => '产品规格', 'type' => 1],
                'product_name' => ['name' => '产品名称', 'type' => 1],
                'plan_begin_date' => ['name' => '计划开工日', 'type' => 1],
                'plan_end_date' => ['name' => '计划完工日', 'type' => 1],
                'quantity' => ['name' => '生产数量', 'type' => 1],
                'remarks' => ['name' => '生产说明', 'type' => 1],
                'bom_names' => ['name' => '工艺路线', 'type' => 1],
                'print_user' => ['name' => '打印人', 'type' => 1],
                'print_date' => ['name' => '打印日期', 'type' => 1]
            ]
        ],
        24 => [
            'page_name' => '采购订单',
            'page_data' => [
                'order_code' => ['name' => '采购单号', 'type' => 1],
                'order_date' => ['name' => '采购日期', 'type' => 1],
                'supplier_name' => ['name' => '供应商', 'type' => 1],
                'group_name' => ['name' => '部门', 'type' => 1],
                'create_user' => ['name' => '订单创建人', 'type' => 1],
                'print_user' => ['name' => '打印人', 'type' => 1],
                'print_time' => ['name' => '打印时间', 'type' => 1],
                'review_user' => ['name' => '审核人', 'type' => 1],
                'remarks' => ['name' => '订单备注', 'type' => 1],
                'total_money' => ['name' => '未税金额合计', 'type' => 1],
                'total_money_hs' => ['name' => '含税金额合计', 'type' => 1],
                'detail' => [
                    'name' => '采购明细', 'type' => 99, 'data' => [
                        'goods_code' => ['name' => '物料编码', 'type' => 1],
                        'goods_name' => ['name' => '物料名称', 'type' => 1],
                        'goods_spec' => ['name' => '规格', 'type' => 1],
                        'goods_model' => ['name' => '型号', 'type' => 1],
                        'quantity' => ['name' => '数量', 'type' => 1],
                        'goods_unit' => ['name' => '单位', 'type' => 1],
                        'price' => ['name' => '单价', 'type' => 1],
                        'total_money' => ['name' => '未税金额', 'type' => 1],
                        'total_money_hs' => ['name' => '含税金额', 'type' => 1],
                    ]
                ]
            ]
        ],
        25 => [
            'page_name' => '采购入库',
            'page_data' => [
                'code' => ['name' => '入库单号', 'type' => 1],
                'instock_date' => ['name' => '入库日期', 'type' => 1],
                'order_code' => ['name' => '采购订单', 'type' => 1],
                'supplier_name' => ['name' => '供应商', 'type' => 1],
                'create_user' => ['name' => '创建人', 'type' => 1],
                'print_user' => ['name' => '打印人', 'type' => 1],
                'print_time' => ['name' => '打印时间', 'type' => 1],
                'remarks' => ['name' => '备注', 'type' => 1],
                'detail' => [
                    'name' => '采购入库明细', 'type' => 99, 'data' => [
                        'goods_code' => ['name' => '物料编码', 'type' => 1],
                        'goods_name' => ['name' => '物料名称', 'type' => 1],
                        'goods_spec' => ['name' => '规格', 'type' => 1],
                        'goods_model' => ['name' => '型号', 'type' => 1],
                        'quantity' => ['name' => '数量', 'type' => 1],
                        'goods_unit' => ['name' => '单位', 'type' => 1],
                        'price' => ['name' => '单价', 'type' => 1],
                        'total_money' => ['name' => '未税金额', 'type' => 1],
                        'total_money_hs' => ['name' => '含税金额', 'type' => 1],
                    ]
                ]
            ]
        ],
        26 => [
            'page_name' => '领料单',
            'page_data' => [
                'code'          => ['name'=> '领料单号','type'=>1],
                'outstock_date' => ['name'=>'领料日期','type'=>1],
                'outstock_user' => ['name'=>'领料人','type'=>1],
                'product_code'  => ['name'=>'产品编号','type'=>1],
                'product_name'  => ['name'=>'产品名称','type'=>1],
                'bom_name'      => ['name'=>'工艺名称','type'=>1],
                'order_code'    => ['name'=>'项目号','type'=>1],
                'customer_name' => ['name'=>'客户','type'=>1],
                'detail'        => ['name'=>'领料明细','type'=>99,
                    'data' => [
                        'goods_code'  => ['name'=>'物料编码','type'=>1],
                        'goods_name'  => ['name'=>'物料名称','type'=>1],
                        'goods_spec'  => ['name'=>'规格','type'=>1],
                        'goods_model' => ['name'=>'型号','type'=>1],
                        'quantity'    => ['name'=>'数量','type'=>1],
                        'goods_unit'  => ['name'=>'数量单位','type'=>1]
                    ]
                ]
            ]
        ],
        27 => [
            'page_name' => '其他入库',
            'page_data' => [
                'code' => ['name' => '入库单号', 'type' => 1],
                'other_date' => ['name' => '入库日期', 'type' => 1],
                'create_user' => ['name' => '创建人', 'type' => 1],
                'print_user' => ['name' => '打印人', 'type' => 1],
                'print_time' => ['name' => '打印时间', 'type' => 1],
                'remarks' => ['name' => '备注', 'type' => 1],
                'detail' => [
                    'name' => '其他入库明细', 'type' => 99, 'data' => [
                        'goods_code' => ['name' => '物料编码', 'type' => 1],
                        'goods_name' => ['name' => '物料名称', 'type' => 1],
                        'goods_spec' => ['name' => '规格', 'type' => 1],
                        'goods_model' => ['name' => '型号', 'type' => 1],
                        'quantity' => ['name' => '数量', 'type' => 1],
                        'goods_unit' => ['name' => '单位', 'type' => 1],
                        'price' => ['name' => '单价', 'type' => 1],
                        'total_money' => ['name' => '未税金额', 'type' => 1],
                        'total_money_hs' => ['name' => '含税金额', 'type' => 1],
                        'supplier_name' => ['name' => '供应商', 'type' => 1],
                    ]
                ]
            ]
        ],
    ];
}