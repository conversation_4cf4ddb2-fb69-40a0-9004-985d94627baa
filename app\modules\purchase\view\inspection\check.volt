{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">基本信息</span>
                    </div>
                </div>
                <div class="portlet-body form" >
                    <form id="form" class="form-horizontal">
                        <div class="form-body"  style="height: 76.5vh;overflow-y: auto;overflow-x: hidden">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">单据编号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="check_code" v-model="check_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">报检单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="inspection_code" v-model="inspection_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">到货单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="receipt_code" v-model="receipt_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">物料编码</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="goods_code" v-model="goods_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">物料名称</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="goods_name" v-model="goods_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">规格型号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="goods_model" v-model="goods_model" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">检验计量数量</label>
                                        <div class="col-sm-8">
                                            <div style="background-color: #f2f2f2" type="text" class="form-control" name="quantity" v-text="quantity + '(' + goods_deputy_unit + ')'"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">采购计量数量</label>
                                        <div class="col-sm-8">
                                            <div style="background-color: #f2f2f2" type="text" class="form-control" name="purchase_quantity" v-text="purchase_quantity + '(' + goods_unit + ')'"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">供应商</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检时间</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="check_time" v-model="check_time" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检人</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="check_user_name" v-model="check_user_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检结果</label>
                                        <div class="col-sm-8">
                                            <span v-if="check_result_flag == 1" class="label label-danger">NG</span>
                                            <span v-else class="label label-success">OK</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检说明</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="check_remarks" v-model="check_remarks" readonly/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-blue"></i>
                        <span class="caption-subject font-blue bold">详情</span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center"></div>
                </div>
                <div class="portlet-body" style="height: 85vh;overflow-y: auto;display: flex;flex-wrap: wrap">
                    <div style="display: flex;border-bottom: 1px #e2e2e2 solid;border-right: 1px #e2e2e2 solid;">
                        <div>
                            <div class="item-content">
                                <span>检验项目</span>
                            </div>
                            <div v-for="(form_item,form_idx) in check_data" :key="form_idx" class="item-content" :style="{height: getRowHeight(form_item), borderBottom: form_idx === check_data.length - 1 ? '1px #e2e2e2 solid' : 'none'}">
                                <div style="text-align: left">
                                    <span v-text="form_item.title"></span>
                                    <span v-if="form_item.unit != ''" v-text="'('+ form_item.unit +')'"></span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="item-content">
                                <span>尺寸规格</span>
                            </div>
                            <div v-for="(form_item,form_idx) in check_data" :key="form_idx" class="item-content" :style="{height: getRowHeight(form_item), borderBottom: form_idx === check_data.length - 1 ? '1px #e2e2e2 solid' : 'none'}">
                                <div v-if="form_item.type == 7" style="text-align: left">
                                    <div>
                                        <span v-text="'最大值:' + form_item.standard_plus"></span>
                                    </div>
                                    <div>
                                        <span v-text="'最小值:' + form_item.standard_minus"></span>
                                    </div>
                                </div>
                                <div v-if="form_item.type == 8" style="text-align: left">
                                    <div>
                                        <span v-text="'标准值:' + form_item.standard_val"></span>
                                    </div>
                                    <div>
                                        <span v-text="'工差+:' + form_item.standard_plus"></span>
                                    </div>
                                    <div>
                                        <span v-text="'工差-:' + form_item.standard_minus"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="item-content">
                                <span>实测值</span>
                            </div>
                            <div v-for="(data,form_idx) in check_data" :key="form_idx" class="item-content" :style="{height: getRowHeight(data), borderBottom: form_idx === check_data.length - 1 ? '1px #e2e2e2 solid' : 'none'}">
                                <div v-if="data.type == 1 || data.type == 3 || data.type == 5">
                                    <span v-text="data.value"></span>
                                </div>
                                <div v-if="data.type == 2">
                                    <div v-for="(value, value_index) in data.values" :key="value_index">
                                        <span v-text="value"></span>
                                    </div>
                                </div>
                                <div v-if="data.type == 4">
                                    <template v-for="(value, value_index) in data.values">
                                        <span v-text="value"></span>
                                        <span v-if="value_index < data.values.length - 1">,</span>
                                    </template>
                                </div>
                                <div v-if="data.type == 6">
                                    <div v-for="(value, value_index) in data.values" :key="value_index">
                                        <span :style="{color : data.results[value_index] == 1 ? 'red':'#000'}" v-text="data.list[value]"></span>
                                    </div>
                                </div>
                                <div v-if="data.type == 7 || data.type == 8">
                                    <div v-for="(value, value_index) in data.values" :key="value_index">
                                        <span :style="{color : data.results[value_index] == 1 ? 'red':'#000'}" v-text="value"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <a v-for="url, img_idx in check_images" class="lightbox-a" :href="base_path + url" :data-lightbox="'check_' + img_idx" style="margin-left: 10px;">
                            <img style="max-width: 500px;max-height: 340px;" :src="base_path + url" class="lightbox-image" />
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {
            getRowHeight: function(item) {
                // 根据内容类型和数量计算行高
                if (!item.values || item.values.length === 0) {
                    return '30px';
                }
                
                var baseHeight = 30; // 基础高度
                var rowHeight = baseHeight;
                
                // 根据类型计算高度
                if (item.type == 2 || item.type == 6 || item.type == 7 || item.type == 8) {
                    // 这些类型每个值一行
                    rowHeight = item.values.length * baseHeight;
                }
                
                // 如果是类型7，考虑最大值和最小值的显示需要的高度
                if (item.type == 7) {
                    rowHeight = Math.max(rowHeight, 2 * baseHeight); // 至少需要2行高度
                }
                
                // 如果是类型8，考虑标准值、工差+和工差-的显示需要的高度
                if (item.type == 8) {
                    rowHeight = Math.max(rowHeight, 3 * baseHeight); // 至少需要3行高度
                }
                
                return rowHeight + 'px';
            }
        }
    });
</script>
<style>
    .item-content{
        border-top: 1px #e2e2e2 solid;
        border-left: 1px #e2e2e2 solid;
        width: 100px;
        padding: 5px;
        text-align: center;
        height: 30px;
    }
</style>