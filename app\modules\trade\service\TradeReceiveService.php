<?php
namespace Envsan\Modules\Trade\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Trade\Model\TradeReceive;
use Phalcon\Mvc\User\Component;

class TradeReceiveService extends Component
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
            't1.receive_no',
            't1.receive_date',
            't1.receive_money',
            't1.remarks',
            't2.code as order_code',
            't3.name as customer_name',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Trade\Model\TradeReceive', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder', 't1.order_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't2.customer_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new TradeReceive();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $order_id = $this->request->getPost('order_id', 'tstring');
        $receive_date = $this->request->getPost('receive_date', 'tstring');
        $receive_money = $this->request->getPost('receive_money', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $files = urldecode($this->request->getPost('files', 'tstring'));
        $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));

        if (empty($order_id) || empty($receive_date) || !CheckUtil::isDecimal($receive_money))
            return ErrorHelper::WRONG_INPUT;

        $table = new TableService();
        $now = DateUtil::now();
        $user = SessionData::user();
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $files = CvtUtil::emptyToArray($files);

        $row->receive_date = $receive_date;
        $row->receive_money = $receive_money;
        $row->order_id = $order_id;
        $row->ext_data = CvtUtil::arrayToNull($ext_data);
        $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
        $row->files = CvtUtil::arrayToNull($files);
        $row->remarks = CvtUtil::blankToNull($remarks);
        $row->update_date = $now;
        $row->update_by = $user->id;

        if ($act == 'create') {
            $ss = new SequenceService();
            $row->uid = UUID::make();
            $row->receive_no = $ss->useSequence(17);
            $row->create_date = $now;
            $row->create_by = $user->id;
            $row->del_flag = 0;
            $row->group_id = $user->group_id;
            $row->owner = $user->owner;
        }
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return TradeReceive::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return TradeReceive::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getOrderList($order_id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.id, a.code as order_code, b.name as customer_name')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrder', 'a')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 'a.customer_id = b.id', 'b')
            ->where('a.del_flag = 0 and a.order_type = 1 and a.status >= 30 and a.owner = '.SessionData::ownerId())
            ->orderBy('a.code');
        if (!empty($order_id)) {
            $builder->andWhere('a.id = ?1', [1 => $order_id]);
        }
        return $builder->getQuery()->execute();
    }
}