<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesViewStock extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var string
     * @Column(type="string", length=21, nullable=true)
     */
    public $in_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=21, nullable=true)
     */
    public $out_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=21, nullable=true)
     */
    public $cnt;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_view_stock';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewStock[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewStock
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
