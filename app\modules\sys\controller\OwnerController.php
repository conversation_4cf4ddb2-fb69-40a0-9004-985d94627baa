<?php
namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Owner;
use Envsan\Modules\Sys\Service\OwnerService;

/**
 * @super
 */
class OwnerController extends SuperController
{
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OwnerService();
            $rows = $s->selectAll(trim($this->request->get('content', 'string')));
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
    }

    public function createAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OwnerService();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $jrow = (new Owner())->toArray();
        $jrow['goods_ids'] = '';
        $jrow['goods_names'] = '';
        $jrow['is_contract_auto'] = 0;
        $jrow['is_weighing_auto'] = 0;
        $jrow['is_stock_run'] = 0;
        $this->view->jsonOwner = json_encode($jrow);
    }

    public function editAction($id = '')
    {
        $s = new OwnerService();
        $row = $s->selectById($id);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $this->view->owner = $row;
        $jrow = $row->toArray();
        $this->view->jsonOwner = json_encode($jrow);
        $this->view->pick('owner/create');
    }

    public function disableAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new OwnerService();
            $ret = new JsonData();
            $action = $this->request->getPost('action', 'string');
            $ret->message = $rs->disableById($this->request->getPost('id', 'string'), $action);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function suggestAction($find='')
    {
        $this->setJsonResponse();
        if($find=='')
            return '';

        $os = new OwnerService();
        $builder = $os->selectAll($find);
        $rows = $builder->getQuery()->execute();
        return json_encode($rows);
    }
}