<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesOtherLogs extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $work_month;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $work_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $shift_type;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $hour;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $work_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $produce_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $staff_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $staff_name;

    /**
     *
     * @var double
     * @Column(type="double", nullable=true)
     */
    public $staff_cost;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_time;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $wages_ratio;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_other_logs';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesOtherLogs[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesOtherLogs
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
