<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesReport;
use Envsan\Modules\Mes\Service\ReportService;

/**
 * @name('生产日报')
 */
class ReportController extends SuperController
{
    private $page_id = 16;
    private $stat_page_id = 22;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ReportService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }


    /**
     * @name('工资统计')
     */
    public function statAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ReportService();
            $builder = $s->selectStatAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->stat_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->stat_page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new ReportService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }
        $table = new TableService();
        $oss_util = new FileService();
        $jrow = (new MesReport())->toArray();
        $jrow['report_date'] = DateUtil::yesterday();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['files'] = [];
        $jrow['report_data'] = [];
        $rtn =  $s->getReportData($jrow['report_date']);
        if ($rtn->message == ''){
            $jrow['report_data'] = $rtn->data;
        }
        $this->view->jsonData = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 4;
    }

    /**
     * @acl({'link':'mes:notice:create'})
     */
    function editAction($uid)
    {
        $s = new ReportService();
        $row = $s->selectByUid($uid);
        if(empty($row) || $row->del_flag == 1) {
            die(ErrorHelper::WRONG_ID);
        } else if ($row->status != 10) {
            die('已提交，不能编辑');
        }
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }
        $oss_util = new FileService();
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['report_data'] = CvtUtil::emptyToArray($jrow['report_data']);
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
        $this->view->pick('report/create');
    }

    /**
     * @skipacl
     */
    public function dateAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ReportService();
            $ret = new JsonData();
            $rtn =  $s->getReportData($this->request->getPost('report_date', 'tstring'));
            $ret->message = $rtn->message;
            if (empty($ret->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'mes:notice:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ReportService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function viewAction($uid){
        $s = new ReportService();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);
        $oss_util = new FileService();
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['data_sort'] = CvtUtil::emptyToInt($jrow['data_sort']);
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['report_data'] = CvtUtil::emptyToArray($jrow['report_data']);
        $this->view->jsonData = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
    }

    /**
     * @skipacl
     */
    public function detailAction($uid){
        $s = new ReportService();
        $row = $s->selectDetailByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);
        $jrow = $row->toArray();
        $jrow['produce_list'] = CvtUtil::emptyToArray($jrow['produce_list']);
        $jrow['other_list'] = CvtUtil::emptyToArray($jrow['other_list']);
        $this->view->jsonData = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new ReportService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }


    /**
     * @skipacl
     */
    public function statexportAction()
    {
        $s = new ReportService();
        $builder = $s->selectStatAll();
        $table = new TableService();
        $table->exportExcel($this->stat_page_id,$builder);
    }
}