<?php

namespace Envsan\Modules\Purchase\Model;



use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseGoodsType extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $pid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $short_name;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $type_level;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $part_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_goods_type';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseGoodsType[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseGoodsType
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
