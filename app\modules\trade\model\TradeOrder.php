<?php

namespace Envsan\Modules\Trade\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class TradeOrder extends BaseModel
{
    const STATUS_REVIEW = 20;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=30, nullable=false)
     */
    public $code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $order_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $order_type_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $customer_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $sign_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $manager_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $manager_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $files;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", length=300, nullable=true)
     */
    public $remarks;


    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     *
     * @var double
     * @Column(type="double", length=11, nullable=true)
     */
    public $order_amount;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'trade_order';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return TradeOrder[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return TradeOrder
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
