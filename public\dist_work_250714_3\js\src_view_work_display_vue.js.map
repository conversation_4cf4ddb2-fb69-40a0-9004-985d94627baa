{"version": 3, "file": "js/src_view_work_display_vue.js", "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACgJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACtWA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/work/display.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/work/display.vue", "webpack://rrts-manager/./src/view/work/display.vue?4f8b", "webpack://rrts-manager/./src/view/work/display.vue?1c87", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/work/display.vue?dcde", "webpack://rrts-manager/./src/view/work/display.vue?a123", "webpack://rrts-manager/./src/view/work/display.vue?b67e", "webpack://rrts-manager/./src/view/work/display.vue?c1d8"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div>\r\n        <div v-if=\"type == 2\" style=\"position: absolute;top:60px;right: 10px;z-index: 999\">\r\n            <van-button @click=\"doBack\" style=\"width: 40px;height: 40px;border-radius: 25px;font-size: 20px;padding-top: 5px\" type=\"info\">\r\n                <van-icon name=\"arrow-left\"/>\r\n            </van-button>\r\n        </div>\r\n        <div v-if=\"step==0\">\r\n        </div>\r\n        <div v-if=\"step==1\">\r\n            <van-empty image=\"error\" :description=\"message\" />\r\n        </div>\r\n        <div v-if=\"step==2\" class=\"main\">\r\n            <van-steps v-if=\"work_list.length > 1\" :active=\"active\" @click-step=\"stepClick\" >\r\n                <van-step v-for=\"(item,i) in work_list\" >{{item.name}}</van-step>\r\n            </van-steps>\r\n            <div :style=\"{overflow: 'auto',height: data.auth == 0 ? '100vh' : 'calc(100vh - 56px)',paddingBottom:'60px'}\" @scroll=\"scroll\" ref=\"scroll\">\r\n                <div class=\"header\">\r\n                    <div class=\"title\" v-text=\"data.title\"></div>\r\n                    <div class=\"title2\" v-text=\"data.group\"></div>\r\n                    <div class=\"title3\">\r\n                        <span v-text=\"data.anchor\"></span> &nbsp;&nbsp;&nbsp;&nbsp;\r\n                        <van-tag v-if=\"data.handle_status == 1\" type=\"success\" size=\"large\">通过</van-tag>\r\n                        <van-tag v-if=\"data.handle_status == 2\" type=\"danger\" size=\"large\">拒绝</van-tag>\r\n                        <van-tag v-if=\"data.handle_status == 3\" type=\"danger\" size=\"large\">撤销</van-tag>\r\n                    </div>\r\n                </div>\r\n                <div class=\"content\" style=\"padding: 0;overflow: hidden\">\r\n                    <div>\r\n                        <van-cell-group>\r\n                            <van-cell title=\"业务单号\" :value=\"data.code\" />\r\n                            <van-cell title=\"业务名称\" :value=\"data.type_name\" />\r\n                            <van-cell v-if=\"data.review_type == 1\" title=\"业务类型\">\r\n                                <template #extra>\r\n                                    <span style=\"color: red\">撤销审批</span>\r\n                                </template>\r\n                            </van-cell>\r\n                            <van-cell title=\"申请人\" :value=\"data.create_user\" />\r\n                            <van-cell title=\"申请时间\" :value=\"data.create_date\" />\r\n                            <template v-for=\"(item,i) in data.form_list\">\r\n                                <template v-if=\"item.type == 99\">\r\n                                    <template v-if=\"item.sum_list.length > 0 || item.data_list.length > 0\">\r\n                                        <van-cell :title=\"item.name\" icon=\"star\"/>\r\n                                        <van-cell v-for=\"(sum,j) in item.sum_list\" :title=\"sum.name\" :value=\"sum.value + sum.unit\" />\r\n                                        <div v-for=\"(d,k) in item.data_list\"  class=\"content\" style=\"padding: 0;overflow: hidden\">\r\n                                            <van-cell-group inset>\r\n                                                <template v-for=\"(v,l) in d\">\r\n                                                    <van-cell v-if=\"v.type == 100\" title=\"查看详情\" is-link @click=\"viewDetail(v)\"/>\r\n                                                    <van-cell v-else :title=\"v.name\" :value=\"v.value + v.unit\" />\r\n                                                </template>\r\n                                            </van-cell-group>\r\n                                        </div>\r\n                                    </template>\r\n                                </template>\r\n                                <van-cell v-else-if=\"item.type == 999\" title=\"图片\">\r\n                                    <template #extra>\r\n                                        <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;width: 79%\">\r\n                                            <div v-for=\"(file,i) in item.value\" @click=\"previewImg(file)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                                <van-image :src=\"base_path+file + '!small'\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                            </div>\r\n                                        </div>\r\n                                    </template>\r\n                                </van-cell>\r\n                                <van-cell v-else-if=\"item.type == 101\" :title=\"item.name\" is-link @click=\"viewDetail(item)\"/>\r\n                                <van-cell v-else :title=\"item.name\" :value=\"item.value + item.unit\" />\r\n                            </template>\r\n                            <van-cell v-if=\"data.remarks != null\" title=\"说明\" :value=\"data.remarks\" />\r\n                            <van-cell v-if=\"data.files.length > 0\" title=\"图片\">\r\n                                <template #extra>\r\n                                    <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;width: 79%\">\r\n                                        <div v-for=\"(file,i) in data.files\" @click=\"previewImg(file)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                            <van-image :src=\"base_path+file + '!small'\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                        </div>\r\n                                    </div>\r\n                                </template>\r\n                            </van-cell>\r\n                            <van-cell v-if=\"data.file_list.length > 0\" title=\"文件\">\r\n                                <template #extra>\r\n                                    <div class=\"file-box\">\r\n                                        <div v-for=\"file in data.file_list\" @click=\"openFile(file.path)\" class=\"file-item van-ellipsis\" v-text=\"file.name\"></div>\r\n                                    </div>\r\n                                </template>\r\n                            </van-cell>\r\n                            <van-cell v-if=\"data.more_flag == 1\" title=\"查看子流程\" is-link @click=\"viewMore\"/>\r\n                        </van-cell-group>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"flow_list.length > 0\" class=\"content\">\r\n                    <div>\r\n                        <div v-for=\"(item,index) in flow_list\" class=\"item\">\r\n                            <div class=\"title-border\">\r\n                                <div v-if=\"item.type == 4\" class=\"title\" style=\"background-color: #3296FB\">\r\n                                    <van-icon name=\"wap-home-o\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                                </div>\r\n                                <div v-else-if=\"item.type == 5\" class=\"title\" style=\"background-color: #FF2B2B\">\r\n                                    <van-icon name=\"stop\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                                </div>\r\n                                <div v-else-if=\"item.type == 6\" class=\"title\" style=\"background-color: #FF2B2B\">\r\n                                    <van-icon name=\"revoke\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                                </div>\r\n                                <div v-else class=\"title\" style=\"background-color: #3296FB\">\r\n                                    {{item.icon}}\r\n                                    <div v-if=\"item.type == 1\" class=\"border\" style=\"color:#4AB37E\">\r\n                                        <van-icon name=\"checked\"/>\r\n                                    </div>\r\n                                    <div v-else-if=\"item.type == 3\" class=\"border\" style=\"color:#4AB37E\">\r\n                                        <van-icon name=\"volume\"/>\r\n                                    </div>\r\n                                    <div v-else class=\"border\" style=\"color: #3296FB\">\r\n                                        <van-icon name=\"thumb-circle\" />\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"item-content\">\r\n                                <div class=\"top\">\r\n                                    <div :style=\"{flex:'1',color: item.status == 0 ? '#A2A2A2' : '#000000'}\">\r\n                                        <span>{{item.name}}</span>\r\n                                        <span> {{item.val}}</span>\r\n                                    </div>\r\n                                    <div style=\"width: 80px;margin-left: 5px\">\r\n                                        <span style=\"font-size: 13px;color: #898989;margin-top: -3px\">{{item.time}}</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"bottom\" :style=\"{borderLeft:flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'}\">\r\n                                    <div v-if=\"item.text != ''\" class=\"bottom-content\">\r\n                                        {{item.text}}\r\n                                    </div>\r\n                                    <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;margin-left: 35px;margin-top: 5px\">\r\n                                        <div v-for=\"(file,i) in item.files\" @click=\"previewImg(file)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                            <van-image :src=\"base_path+file + '!small'\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div v-if=\"item.send != ''\" class=\"bottom-send\">\r\n                                        {{item.send}}\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div>\r\n                        <div v-for=\"(item,idx) in anchor_list\" :key=\"idx\">\r\n                            <div style=\"display: flex;flex-direction: row;min-height: 60px;\">\r\n                                <div style=\"width: 35%;display: flex;flex-direction: row;\">\r\n                                    <div style=\"height: 100%;position: relative\">\r\n                                        <div style=\"height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;\">\r\n                                        </div>\r\n                                        <div v-if=\"idx == 0\" style=\"height: 18px;position: absolute;top: 0;width: 12px;background-color: #FFF\">\r\n                                        </div>\r\n                                        <div v-if=\"idx+1 == anchor_list.length\" style=\"height: calc(100% - 22px);position: absolute;top: 22px;width: 12px;background-color: #FFF\">\r\n                                        </div>\r\n                                        <div style=\"position: absolute;top: 15px;width: 15px;height: 15px;background-color: #aaaaaa;border-radius: 15px\">\r\n                                        </div>\r\n                                    </div>\r\n                                    <div style=\"padding-top:5px;padding-left: 15px\">\r\n                                        <span style=\"font-weight: 400\" v-text=\"item.name\"></span>\r\n                                        <div>\r\n                                            <span style=\"font-size: 12px;color: #898989\" v-if=\"item.type==1\">会签</span>\r\n                                            <span style=\"font-size: 12px;color: #898989\" v-if=\"item.type==2\">或签</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                                <div style=\"width: 65%;display: flex;flex-direction: row;flex-wrap: wrap;justify-content: flex-end;padding-right: 10px;padding-top: 5px\">\r\n                                    <van-tag v-for=\"(user,i) in item.list\" :key=\"user.id\" :type=\"item.type == 1 ? 'primary' : 'success'\" size=\"large\" style=\"height: 20px;margin: 2px\">{{user.name}}</van-tag>\r\n                                </div>\r\n                            </div>\r\n                            <div v-if=\"item.nlist.length > 0\" style=\"display: flex;flex-direction: row;min-height: 60px;\">\r\n                                <div style=\"width: 35%;display: flex;flex-direction: row;\">\r\n                                    <div style=\"height: 100%;position: relative\">\r\n                                        <div v-if=\"idx+1 != anchor_list.length\" style=\"height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;\">\r\n                                        </div>\r\n                                        <div v-else style=\"height: 100%;width: 8px;\">\r\n                                        </div>\r\n                                    </div>\r\n                                    <div style=\"padding-top:5px;padding-left: 15px\">\r\n                                        <span style=\"font-weight: 400;color: #898989\">抄送人</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div style=\"width: 65%;display: flex;flex-direction: row;flex-wrap: wrap;justify-content: flex-end;padding-right: 10px;padding-top: 5px\">\r\n                                    <van-tag v-for=\"(user,i) in item.nlist\" :key=\"user.id\" type=\"default\" size=\"large\" style=\"height: 20px;margin: 2px\">{{user.name}}</van-tag>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import base from '../../components/base';\r\n    import { Dialog } from 'vant';\r\n    import Config from \"@/config\";\r\n    export default {\r\n        extends: base,\r\n        name: \"work\",\r\n        data () {\r\n            return {\r\n                active:0,\r\n                uid:'',\r\n                pos:0,\r\n                step : 0,\r\n                message:'',\r\n                type:1,\r\n                src:2,\r\n                reject_show_flag:false,\r\n                reject_value:'',\r\n                base_path:'',\r\n                flow_list:[],\r\n                anchor_list:[],\r\n                user_list:[],\r\n                next_id : '',\r\n                next_list:[],\r\n                work_list:[],\r\n                main_id:'',\r\n                pid:'',\r\n                data:{}\r\n            };\r\n        },\r\n        components: {},\r\n        created(){\r\n            this.$hub.$on('refresh', (data) => {\r\n                this.refresh()\r\n            });\r\n        },\r\n        methods:{\r\n            onLoad(){\r\n                let user = this.$store.state.user;\r\n                this.base_path = user.imgdir;\r\n                this.pos = 0;\r\n                this.uid = this.$route.params.uid;\r\n                this.step = 0;\r\n                this.message = '';\r\n                this.type = this.$route.params.type;\r\n                this.src = this.$route.params.src;\r\n                this.pass_show_flag = false;\r\n                this.pass_value = '';\r\n                this.reject_show_flag = false;\r\n                this.reject_value = '';\r\n                this.next_id  = '';\r\n                this.$http.post('work/work/view', {uid: this.uid,src:this.src}).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.step = 2;\r\n                        this.main_id = rs.data.data.main_id;\r\n                        this.pid = rs.data.data.pid;\r\n                        this.flow_list = rs.data.flow_list;\r\n                        this.anchor_list = rs.data.anchor_list;\r\n                        this.user_list = rs.data.user_list;\r\n                        this.next_list = rs.data.next_list;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.data = rs.data.data;\r\n                        for(let i = 0 ; i < this.work_list.length ; i++){\r\n                            if (this.work_list[i].uid == this.uid){\r\n                                this.active = i;\r\n                                break;\r\n                            }\r\n                        }\r\n                    } else {\r\n                        this.step = 1;\r\n                        this.message = rs.message;\r\n                    }\r\n                });\r\n            },\r\n            refresh(){\r\n                this.$http.post('work/work/view', {uid: this.uid,src:this.src,main_id:this.main_id,pid:this.pid}).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.flow_list = rs.data.flow_list;\r\n                        this.anchor_list = rs.data.anchor_list;\r\n                        this.user_list = rs.data.user_list;\r\n                        this.next_list = rs.data.next_list;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.data = rs.data.data;\r\n                        for(let i = 0 ; i < this.work_list.length ; i++){\r\n                            if (this.work_list[i].uid == this.uid){\r\n                                this.active = i;\r\n                                break;\r\n                            }\r\n                        }\r\n                    } else {\r\n                        this.$toast.fail(rs.message);\r\n                    }\r\n                });\r\n            },\r\n            onShow(){\r\n                this.onLoad();\r\n                this.$refs['scroll'].scrollTop = this.pos;\r\n            },\r\n            previewImg(img) {\r\n                this.$router.push({name: 'preview/image',params: { images: [this.base_path + img]}});\r\n            },\r\n            openFile(path) {\r\n                if (/\\.pdf$/.test(path)) {\r\n                    this.$router.push({name: 'preview/pdf', params: { path: this.base_path + path }});\r\n                } else {\r\n                    window.open(this.base_path + path);\r\n                }\r\n            },\r\n            viewWeight(item){\r\n              this.$router.push({name: 'work/weightdetail',params: { uid: item.uid}});\r\n            },\r\n            viewDetail(v){\r\n                if (v.sort == 99){\r\n                    this.$router.push({name: 'work/accountsdetail',params: { id: v.id}});\r\n                } else if (v.sort == 999){\r\n                    this.$router.push({name: 'display',params: { uid: v.id,type : 2,src : 2}});\r\n                } else if (v.sort == 1){\r\n                    this.$router.push({name: 'instock/review_detail',params: { uid: v.instock_uid,auth: this.data.auth}});\r\n                } else if (v.sort == 2){\r\n                    if (v.business_type == 0){\r\n                        this.$router.push({name: 'outstock/detail', params: {uid: v.uid}});\r\n                    } else if(v.business_type == 1){\r\n                        this.$router.push({name: 'instock/detail', params: {uid: v.uid}});\r\n                    }else{\r\n                        this.$router.push({name: 'work/weightdetail',params: { uid: v.uid }});\r\n                    }\r\n                } else if (v.sort == 3){\r\n                    this.$router.push({name: 'outstock/search', params: { id: v.id, type: 'invoice'}});\r\n                } else if (v.sort == 4){\r\n                    if (v.pdf_name == ''){\r\n                        if (v.contract_type == 1){\r\n                            this.$router.push({name: 'preview/page',params: { path: Config.host+'/dist/#/contract/' + v.contract_key }});\r\n                        } else {\r\n                            this.$toast({\r\n                                message: '合同未上传！',\r\n                                position: 'top'\r\n                            });\r\n                        }\r\n                    } else {\r\n                        this.$router.push({name: 'preview/pdf',params: { path: this.base_path + this.data.pdf_name }});\r\n                    }\r\n                } else if (v.sort == 7){\r\n                    this.$router.push({name: 'trade/ticket', params: { id: v.id, type: 'invoice'}});\r\n                } else if (v.sort == 8){\r\n                    this.$router.push({name: 'pay/detail',params: { account_id: v.id,name:v.cardholder}});\r\n                } else if (v.sort == 9){\r\n                    this.$router.push({name: 'price/price_view', params: {price_id: v.price_id}});\r\n                }\r\n            },\r\n            viewMore(){\r\n                this.$router.push({name: 'work/more',params: { uid: this.data.uid}});\r\n            },\r\n            doBack() {\r\n                this.$router.back();\r\n            },\r\n            scroll(event) {\r\n                this.pos = event.target.scrollTop;\r\n            },\r\n            selectItem(idx){\r\n                this.next_id = this.next_list[idx].id;\r\n            },\r\n            stepClick(idx){\r\n                if (this.work_list[idx].uid == this.uid){\r\n                    return;\r\n                }\r\n                this.uid = this.work_list[idx].uid;\r\n                this.refresh();\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    .main{\r\n        background-color: #F2F2F2;\r\n        width: 100%;\r\n        padding-bottom: 10px;\r\n        height: 100vh;\r\n        overflow: hidden;\r\n    }\r\n    .header{\r\n        width: 100%;\r\n        min-height: 100px;\r\n        background-color: #FFFFFF;\r\n        padding: 15px;\r\n        box-sizing: border-box;\r\n    }\r\n    .header .title{\r\n        font-size: 24px;\r\n        letter-spacing: 1px;\r\n    }\r\n\r\n    .header .title2{\r\n        font-size: 14px;\r\n        color: #898989;\r\n        margin-top: 10px;\r\n    }\r\n\r\n    .header .title3{\r\n        font-size: 16px;\r\n        margin-top: 10px;\r\n        color: #CAA36D;\r\n        font-weight: bold;\r\n        letter-spacing: 1px;\r\n    }\r\n\r\n    .content{\r\n        min-height: 100px;\r\n        background-color: #FFFFFF;\r\n        margin: 15px;\r\n        padding: 15px;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 10px;\r\n    }\r\n    .item{\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-start;\r\n        margin-top: 4px;\r\n    }\r\n    .item .title{\r\n        width: 45px;\r\n        height: 45px;\r\n        line-height: 45px;\r\n        vertical-align: middle;\r\n        background-color: #3296FB;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 5px;\r\n        color: #FFFFFF;\r\n        text-align: center;\r\n        position: relative;\r\n        font-size: 16px;\r\n    }\r\n    .item .title .border{\r\n        position: absolute;\r\n        top: 31px;\r\n        left: 31px;\r\n        width: 18px;\r\n        height: 18px;\r\n        line-height: 18px;\r\n        border-radius: 9px;\r\n        background-color: #FFFFFF;\r\n        text-align: center;\r\n        padding-top: 1px;\r\n    }\r\n\r\n    .item .item-content{\r\n        width: 100%;\r\n        margin-left: 15px;\r\n        color: #888888;\r\n    }\r\n\r\n    .item .item-content .top {\r\n        min-height: 20px;\r\n        line-height: 20px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: space-between;\r\n        font-size: 16px;\r\n        margin-top: 2px;\r\n        vertical-align: top;\r\n    }\r\n\r\n    .item .item-content .bottom {\r\n        margin-left: -38px;\r\n        border-left: 4px #D2D2D2 solid;\r\n        padding-bottom: 10px;\r\n        min-height: 55px;\r\n    }\r\n\r\n    .item .bottom-content{\r\n        margin-left: 35px;\r\n        margin-top:5px;\r\n        min-height: 40px;\r\n        background-color: #F2F2F2;\r\n        border-radius: 5px;\r\n        padding: 10px;\r\n        color: #000000;\r\n    }\r\n\r\n    .item .bottom-send{\r\n        margin-left: 35px;\r\n        margin-top:5px;\r\n        font-size: 14px;\r\n    }\r\n\r\n    .item .title-border{\r\n        background-color: #FFFFFF;\r\n        width: 45px;\r\n        height: 52px;\r\n        z-index: 999;\r\n    }\r\n\r\n    .flow-title{\r\n        font-size: 20px;\r\n        margin-top: 10px;\r\n        margin-bottom: 15px;\r\n        font-weight: bold;\r\n        letter-spacing: 2px;\r\n    }\r\n\r\n    .footer{\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 56px;\r\n        border-top: 1px #E2E2E2 solid;\r\n        background-color: #FFFFFF;\r\n        z-index: 1000;\r\n        box-sizing: border-box;\r\n        padding-top: 5px;\r\n        padding-right: 15px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-end;\r\n    }\r\n\r\n    .footer .btn-left{\r\n        border-radius: 0;\r\n        border-top-left-radius:22px;\r\n        border-bottom-left-radius:22px;\r\n        font-size: 18px;\r\n        width: 90px;\r\n    }\r\n\r\n    .footer .btn-right{\r\n        border-radius: 0;\r\n        border-top-right-radius:22px;\r\n        border-bottom-right-radius:22px;\r\n        font-size: 18px;\r\n        width: 90px;\r\n    }\r\n    .footer .btn-right-left{\r\n        border-radius: 0;\r\n        border-top-left-radius:22px;\r\n        border-bottom-left-radius:22px;\r\n        border-top-right-radius:22px;\r\n        border-bottom-right-radius:22px;\r\n        font-size: 18px;\r\n        width: 90px;\r\n    }\r\n    .footer .btn-comment{\r\n        border:0;\r\n        margin-right: 10px;\r\n    }\r\n    .footer .btn-comment .icon{\r\n        font-size: 26px;\r\n        color: #929292;\r\n    }\r\n    .footer .btn-comment .text{\r\n        font-size: 14px;\r\n        color: #929292;\r\n        margin-top: -5px;\r\n    }\r\n    .input-text {\r\n        font-size: 18px;\r\n    }\r\n    .van-button{\r\n        width: 120px;\r\n        font-size: 17px;\r\n    }\r\n    .btn-footer{\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        background-color: #FFFFFF;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n        padding-bottom: 10px;\r\n    }\r\n\r\n    .file-box {\r\n        width: 79%;\r\n        display: flex;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .file-item {\r\n        margin-bottom: 20px;\r\n        font-size: 18px;\r\n        font-style: italic;\r\n        color: #3a94ec;\r\n    }\r\n\r\n    .file-box > .file-item:last-child {\r\n        margin-bottom: 0;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.type == 2)?_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"60px\",\"right\":\"10px\",\"z-index\":\"999\"}},[_c('van-button',{staticStyle:{\"width\":\"40px\",\"height\":\"40px\",\"border-radius\":\"25px\",\"font-size\":\"20px\",\"padding-top\":\"5px\"},attrs:{\"type\":\"info\"},on:{\"click\":_vm.doBack}},[_c('van-icon',{attrs:{\"name\":\"arrow-left\"}})],1)],1):_vm._e(),(_vm.step==0)?_c('div'):_vm._e(),(_vm.step==1)?_c('div',[_c('van-empty',{attrs:{\"image\":\"error\",\"description\":_vm.message}})],1):_vm._e(),(_vm.step==2)?_c('div',{staticClass:\"main\"},[(_vm.work_list.length > 1)?_c('van-steps',{attrs:{\"active\":_vm.active},on:{\"click-step\":_vm.stepClick}},_vm._l((_vm.work_list),function(item,i){return _c('van-step',[_vm._v(_vm._s(item.name))])}),1):_vm._e(),_c('div',{ref:\"scroll\",style:({overflow: 'auto',height: _vm.data.auth == 0 ? '100vh' : 'calc(100vh - 56px)',paddingBottom:'60px'}),on:{\"scroll\":_vm.scroll}},[_c('div',{staticClass:\"header\"},[_c('div',{staticClass:\"title\",domProps:{\"textContent\":_vm._s(_vm.data.title)}}),_c('div',{staticClass:\"title2\",domProps:{\"textContent\":_vm._s(_vm.data.group)}}),_c('div',{staticClass:\"title3\"},[_c('span',{domProps:{\"textContent\":_vm._s(_vm.data.anchor)}}),_vm._v(\"      \"),(_vm.data.handle_status == 1)?_c('van-tag',{attrs:{\"type\":\"success\",\"size\":\"large\"}},[_vm._v(\"通过\")]):_vm._e(),(_vm.data.handle_status == 2)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"拒绝\")]):_vm._e(),(_vm.data.handle_status == 3)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"撤销\")]):_vm._e()],1)]),_c('div',{staticClass:\"content\",staticStyle:{\"padding\":\"0\",\"overflow\":\"hidden\"}},[_c('div',[_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"业务单号\",\"value\":_vm.data.code}}),_c('van-cell',{attrs:{\"title\":\"业务名称\",\"value\":_vm.data.type_name}}),(_vm.data.review_type == 1)?_c('van-cell',{attrs:{\"title\":\"业务类型\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(\"撤销审批\")])]},proxy:true}],null,false,688513901)}):_vm._e(),_c('van-cell',{attrs:{\"title\":\"申请人\",\"value\":_vm.data.create_user}}),_c('van-cell',{attrs:{\"title\":\"申请时间\",\"value\":_vm.data.create_date}}),_vm._l((_vm.data.form_list),function(item,i){return [(item.type == 99)?[(item.sum_list.length > 0 || item.data_list.length > 0)?[_c('van-cell',{attrs:{\"title\":item.name,\"icon\":\"star\"}}),_vm._l((item.sum_list),function(sum,j){return _c('van-cell',{attrs:{\"title\":sum.name,\"value\":sum.value + sum.unit}})}),_vm._l((item.data_list),function(d,k){return _c('div',{staticClass:\"content\",staticStyle:{\"padding\":\"0\",\"overflow\":\"hidden\"}},[_c('van-cell-group',{attrs:{\"inset\":\"\"}},[_vm._l((d),function(v,l){return [(v.type == 100)?_c('van-cell',{attrs:{\"title\":\"查看详情\",\"is-link\":\"\"},on:{\"click\":function($event){return _vm.viewDetail(v)}}}):_c('van-cell',{attrs:{\"title\":v.name,\"value\":v.value + v.unit}})]})],2)],1)})]:_vm._e()]:(item.type == 999)?_c('van-cell',{attrs:{\"title\":\"图片\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"width\":\"79%\"}},_vm._l((item.value),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(file)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path+file + '!small',\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0)]},proxy:true}],null,true)}):(item.type == 101)?_c('van-cell',{attrs:{\"title\":item.name,\"is-link\":\"\"},on:{\"click\":function($event){return _vm.viewDetail(item)}}}):_c('van-cell',{attrs:{\"title\":item.name,\"value\":item.value + item.unit}})]}),(_vm.data.remarks != null)?_c('van-cell',{attrs:{\"title\":\"说明\",\"value\":_vm.data.remarks}}):_vm._e(),(_vm.data.files.length > 0)?_c('van-cell',{attrs:{\"title\":\"图片\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"width\":\"79%\"}},_vm._l((_vm.data.files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(file)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path+file + '!small',\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0)]},proxy:true}],null,false,3396324386)}):_vm._e(),(_vm.data.file_list.length > 0)?_c('van-cell',{attrs:{\"title\":\"文件\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('div',{staticClass:\"file-box\"},_vm._l((_vm.data.file_list),function(file){return _c('div',{staticClass:\"file-item van-ellipsis\",domProps:{\"textContent\":_vm._s(file.name)},on:{\"click\":function($event){return _vm.openFile(file.path)}}})}),0)]},proxy:true}],null,false,2499635437)}):_vm._e(),(_vm.data.more_flag == 1)?_c('van-cell',{attrs:{\"title\":\"查看子流程\",\"is-link\":\"\"},on:{\"click\":_vm.viewMore}}):_vm._e()],2)],1)]),(_vm.flow_list.length > 0)?_c('div',{staticClass:\"content\"},[_c('div',_vm._l((_vm.flow_list),function(item,index){return _c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title-border\"},[(item.type == 4)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#3296FB\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"wap-home-o\",\"size\":\"30\"}})],1):(item.type == 5)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#FF2B2B\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"stop\",\"size\":\"30\"}})],1):(item.type == 6)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#FF2B2B\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"revoke\",\"size\":\"30\"}})],1):_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#3296FB\"}},[_vm._v(\" \"+_vm._s(item.icon)+\" \"),(item.type == 1)?_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#4AB37E\"}},[_c('van-icon',{attrs:{\"name\":\"checked\"}})],1):(item.type == 3)?_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#4AB37E\"}},[_c('van-icon',{attrs:{\"name\":\"volume\"}})],1):_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#3296FB\"}},[_c('van-icon',{attrs:{\"name\":\"thumb-circle\"}})],1)])]),_c('div',{staticClass:\"item-content\"},[_c('div',{staticClass:\"top\"},[_c('div',{style:({flex:'1',color: item.status == 0 ? '#A2A2A2' : '#000000'})},[_c('span',[_vm._v(_vm._s(item.name))]),_c('span',[_vm._v(\" \"+_vm._s(item.val))])]),_c('div',{staticStyle:{\"width\":\"80px\",\"margin-left\":\"5px\"}},[_c('span',{staticStyle:{\"font-size\":\"13px\",\"color\":\"#898989\",\"margin-top\":\"-3px\"}},[_vm._v(_vm._s(item.time))])])]),_c('div',{staticClass:\"bottom\",style:({borderLeft:_vm.flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'})},[(item.text != '')?_c('div',{staticClass:\"bottom-content\"},[_vm._v(\" \"+_vm._s(item.text)+\" \")]):_vm._e(),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"margin-left\":\"35px\",\"margin-top\":\"5px\"}},_vm._l((item.files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(file)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path+file + '!small',\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0),(item.send != '')?_c('div',{staticClass:\"bottom-send\"},[_vm._v(\" \"+_vm._s(item.send)+\" \")]):_vm._e()])])])}),0),_c('div',_vm._l((_vm.anchor_list),function(item,idx){return _c('div',{key:idx},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"min-height\":\"60px\"}},[_c('div',{staticStyle:{\"width\":\"35%\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"position\":\"relative\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"border-right\":\"1px #D2D2D2 solid\",\"width\":\"8px\"}}),(idx == 0)?_c('div',{staticStyle:{\"height\":\"18px\",\"position\":\"absolute\",\"top\":\"0\",\"width\":\"12px\",\"background-color\":\"#FFF\"}}):_vm._e(),(idx+1 == _vm.anchor_list.length)?_c('div',{staticStyle:{\"height\":\"calc(100% - 22px)\",\"position\":\"absolute\",\"top\":\"22px\",\"width\":\"12px\",\"background-color\":\"#FFF\"}}):_vm._e(),_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"15px\",\"width\":\"15px\",\"height\":\"15px\",\"background-color\":\"#aaaaaa\",\"border-radius\":\"15px\"}})]),_c('div',{staticStyle:{\"padding-top\":\"5px\",\"padding-left\":\"15px\"}},[_c('span',{staticStyle:{\"font-weight\":\"400\"},domProps:{\"textContent\":_vm._s(item.name)}}),_c('div',[(item.type==1)?_c('span',{staticStyle:{\"font-size\":\"12px\",\"color\":\"#898989\"}},[_vm._v(\"会签\")]):_vm._e(),(item.type==2)?_c('span',{staticStyle:{\"font-size\":\"12px\",\"color\":\"#898989\"}},[_vm._v(\"或签\")]):_vm._e()])])]),_c('div',{staticStyle:{\"width\":\"65%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"justify-content\":\"flex-end\",\"padding-right\":\"10px\",\"padding-top\":\"5px\"}},_vm._l((item.list),function(user,i){return _c('van-tag',{key:user.id,staticStyle:{\"height\":\"20px\",\"margin\":\"2px\"},attrs:{\"type\":item.type == 1 ? 'primary' : 'success',\"size\":\"large\"}},[_vm._v(_vm._s(user.name))])}),1)]),(item.nlist.length > 0)?_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"min-height\":\"60px\"}},[_c('div',{staticStyle:{\"width\":\"35%\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"position\":\"relative\"}},[(idx+1 != _vm.anchor_list.length)?_c('div',{staticStyle:{\"height\":\"100%\",\"border-right\":\"1px #D2D2D2 solid\",\"width\":\"8px\"}}):_c('div',{staticStyle:{\"height\":\"100%\",\"width\":\"8px\"}})]),_vm._m(0,true)]),_c('div',{staticStyle:{\"width\":\"65%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"justify-content\":\"flex-end\",\"padding-right\":\"10px\",\"padding-top\":\"5px\"}},_vm._l((item.nlist),function(user,i){return _c('van-tag',{key:user.id,staticStyle:{\"height\":\"20px\",\"margin\":\"2px\"},attrs:{\"type\":\"default\",\"size\":\"large\"}},[_vm._v(_vm._s(user.name))])}),1)]):_vm._e()])}),0)]):_vm._e()])],1):_vm._e()])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding-top\":\"5px\",\"padding-left\":\"15px\"}},[_c('span',{staticStyle:{\"font-weight\":\"400\",\"color\":\"#898989\"}},[_vm._v(\"抄送人\")])])\n}]\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.main[data-v-597fa565]{\\n    background-color: #F2F2F2;\\n    width: 100%;\\n    padding-bottom: 10px;\\n    height: 100vh;\\n    overflow: hidden;\\n}\\n.header[data-v-597fa565]{\\n    width: 100%;\\n    min-height: 100px;\\n    background-color: #FFFFFF;\\n    padding: 15px;\\n    box-sizing: border-box;\\n}\\n.header .title[data-v-597fa565]{\\n    font-size: 24px;\\n    letter-spacing: 1px;\\n}\\n.header .title2[data-v-597fa565]{\\n    font-size: 14px;\\n    color: #898989;\\n    margin-top: 10px;\\n}\\n.header .title3[data-v-597fa565]{\\n    font-size: 16px;\\n    margin-top: 10px;\\n    color: #CAA36D;\\n    font-weight: bold;\\n    letter-spacing: 1px;\\n}\\n.content[data-v-597fa565]{\\n    min-height: 100px;\\n    background-color: #FFFFFF;\\n    margin: 15px;\\n    padding: 15px;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 10px;\\n}\\n.item[data-v-597fa565]{\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-start;\\n    margin-top: 4px;\\n}\\n.item .title[data-v-597fa565]{\\n    width: 45px;\\n    height: 45px;\\n    line-height: 45px;\\n    vertical-align: middle;\\n    background-color: #3296FB;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 5px;\\n    color: #FFFFFF;\\n    text-align: center;\\n    position: relative;\\n    font-size: 16px;\\n}\\n.item .title .border[data-v-597fa565]{\\n    position: absolute;\\n    top: 31px;\\n    left: 31px;\\n    width: 18px;\\n    height: 18px;\\n    line-height: 18px;\\n    border-radius: 9px;\\n    background-color: #FFFFFF;\\n    text-align: center;\\n    padding-top: 1px;\\n}\\n.item .item-content[data-v-597fa565]{\\n    width: 100%;\\n    margin-left: 15px;\\n    color: #888888;\\n}\\n.item .item-content .top[data-v-597fa565] {\\n    min-height: 20px;\\n    line-height: 20px;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: space-between;\\n    font-size: 16px;\\n    margin-top: 2px;\\n    vertical-align: top;\\n}\\n.item .item-content .bottom[data-v-597fa565] {\\n    margin-left: -38px;\\n    border-left: 4px #D2D2D2 solid;\\n    padding-bottom: 10px;\\n    min-height: 55px;\\n}\\n.item .bottom-content[data-v-597fa565]{\\n    margin-left: 35px;\\n    margin-top:5px;\\n    min-height: 40px;\\n    background-color: #F2F2F2;\\n    border-radius: 5px;\\n    padding: 10px;\\n    color: #000000;\\n}\\n.item .bottom-send[data-v-597fa565]{\\n    margin-left: 35px;\\n    margin-top:5px;\\n    font-size: 14px;\\n}\\n.item .title-border[data-v-597fa565]{\\n    background-color: #FFFFFF;\\n    width: 45px;\\n    height: 52px;\\n    z-index: 999;\\n}\\n.flow-title[data-v-597fa565]{\\n    font-size: 20px;\\n    margin-top: 10px;\\n    margin-bottom: 15px;\\n    font-weight: bold;\\n    letter-spacing: 2px;\\n}\\n.footer[data-v-597fa565]{\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 56px;\\n    border-top: 1px #E2E2E2 solid;\\n    background-color: #FFFFFF;\\n    z-index: 1000;\\n    box-sizing: border-box;\\n    padding-top: 5px;\\n    padding-right: 15px;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-end;\\n}\\n.footer .btn-left[data-v-597fa565]{\\n    border-radius: 0;\\n    border-top-left-radius:22px;\\n    border-bottom-left-radius:22px;\\n    font-size: 18px;\\n    width: 90px;\\n}\\n.footer .btn-right[data-v-597fa565]{\\n    border-radius: 0;\\n    border-top-right-radius:22px;\\n    border-bottom-right-radius:22px;\\n    font-size: 18px;\\n    width: 90px;\\n}\\n.footer .btn-right-left[data-v-597fa565]{\\n    border-radius: 0;\\n    border-top-left-radius:22px;\\n    border-bottom-left-radius:22px;\\n    border-top-right-radius:22px;\\n    border-bottom-right-radius:22px;\\n    font-size: 18px;\\n    width: 90px;\\n}\\n.footer .btn-comment[data-v-597fa565]{\\n    border:0;\\n    margin-right: 10px;\\n}\\n.footer .btn-comment .icon[data-v-597fa565]{\\n    font-size: 26px;\\n    color: #929292;\\n}\\n.footer .btn-comment .text[data-v-597fa565]{\\n    font-size: 14px;\\n    color: #929292;\\n    margin-top: -5px;\\n}\\n.input-text[data-v-597fa565] {\\n    font-size: 18px;\\n}\\n.van-button[data-v-597fa565]{\\n    width: 120px;\\n    font-size: 17px;\\n}\\n.btn-footer[data-v-597fa565]{\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    background-color: #FFFFFF;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: center;\\n    padding-bottom: 10px;\\n}\\n.file-box[data-v-597fa565] {\\n    width: 79%;\\n    display: flex;\\n    flex-direction: column;\\n}\\n.file-item[data-v-597fa565] {\\n    margin-bottom: 20px;\\n    font-size: 18px;\\n    font-style: italic;\\n    color: #3a94ec;\\n}\\n.file-box > .file-item[data-v-597fa565]:last-child {\\n    margin-bottom: 0;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"de248ae8\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./display.vue?vue&type=template&id=597fa565&scoped=true\"\nimport script from \"./display.vue?vue&type=script&lang=js\"\nexport * from \"./display.vue?vue&type=script&lang=js\"\nimport style0 from \"./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"597fa565\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('597fa565')) {\n      api.createRecord('597fa565', component.options)\n    } else {\n      api.reload('597fa565', component.options)\n    }\n    module.hot.accept(\"./display.vue?vue&type=template&id=597fa565&scoped=true\", function () {\n      api.rerender('597fa565', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/work/display.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=template&id=597fa565&scoped=true\""], "names": [], "sourceRoot": ""}