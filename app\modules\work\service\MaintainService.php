<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Equ\Model\EquItem;
use Envsan\Modules\Equ\Model\EquItemMaintainLogs;
use Envsan\Modules\Equ\Util\Constant;
use Envsan\Modules\Sys\Service\DictService;
use Phalcon\Mvc\User\Component;

class MaintainService extends Component
{
    public function selectAll()
    {
        $user = SessionData::user();
        $last_id = $this->request->getPost('last_id', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.status,
                a.maintain_date,
                a.plan_date,
                a.remarks,
                e.code,
                c.name as check_name,
                u.real_name as crete_user
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItemMaintainLogs', 'a')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItem', 'a.item_id = e.id','e')
            ->leftJoin('Envsan\Modules\Equ\Model\EquCheckForm', 'a.form_id = c.id','c')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 'a.maintain_by = u.id','u')
            ->where('a.del_flag = 0 and a.status = 10 and a.owner = ?1',[1 => SessionData::ownerId()])
            ->orderBy('a.id desc')
            ->limit(20);

        if (!empty($last_id)) {
            $builder->andWhere('a.id < ?1', [1 => $last_id]);
        }

        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            $row['status_name'] = Constant::$maintain_logs_status[$row['status']];
        }
        return $rows;
    }

    public function getAddIndexData()
    {
        $ret = [];
        $ret['status'] = 'error';
        $ret['data'] = null;
        $ret['equ_index'] = 0;
        $ret['form_index'] = 0;
        $equ_list = $this->getEquList();
        $ret['equ_list'] = $equ_list;
        $form_list = $this->getFormList();
        $ret['form_list'] = $form_list;
        $id = $this->request->getPost('id', 'tstring');
        if (!empty($id)) {
            $row = $this->selectById($id);
            if (empty($row) || $row->del_flag == 1) {
                $ret['message'] = '点检记录已失效，无法操作';
                return $ret;
            } else if ($row->status != 10) {
                $ret['message'] = '点检记录已处理，无法操作';
                return $ret;
            }

            $jrow = $row->toArray();
            $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
            $ret['data'] = $jrow;

            foreach ($equ_list as $index => $item)
            {
                if ($item->id == $row->item_id) {
                    $ret['equ_index'] = $index;
                    $ret['equ_code'] = $item->code;
                    break;
                }
            }

            foreach ($form_list as $index => $item)
            {
                if ($item['id'] == $row->form_id) {
                    $ret['form_index'] = $index;
                    $ret['form_name'] = $item['name'];
                    break;
                }
            }
        }

        $ret['status'] = 'ok';
        return $ret;
    }

    public function getEquList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.code
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItem', 'a')
            ->where('a.del_flag = 0')
            ->orderBy('a.code');
        return $builder->getQuery()->execute();
    }

    public function getFormList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.name,
                a.form_data
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquCheckForm', 'a')
            ->where('a.del_flag = 0 and a.type = 2')
            ->orderBy('a.code');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row){
            $row['form_data'] = CvtUtil::emptyToArray($row['form_data']);
        }
        return $rows;
    }

    public function submit()
    {
        $id = $this->request->getPost('id', 'tstring');
        $type = $this->request->getPost('type', 'tstring');
        $form_id = $this->request->getPost('form_id', 'tstring');
        $equ_id = $this->request->getPost('equ_id', 'tstring');
        $maintain_date = $this->request->getPost('maintain_date', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $form_data = urldecode($this->request->getPost('form_data', 'tstring'));

        if (empty($form_id) || empty($type)
            || empty($equ_id) || empty($maintain_date) || empty($form_data)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $equ_row = EquItem::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $equ_id]]);
        if (empty($equ_row)) {
            return '无效的设备';
        }

        $user = SessionData::user();
        $now = DateUtil::now();

//        if (empty($id)) {
//            $row = new EquItemMaintainLogs();
//            $row->create_date = $now;
//            $row->create_by = $user->id;
//
//            $row->del_flag = 0;
//            $row->owner = SessionData::ownerId();
//        } else {
        $row = $this->selectById($id);
        if (empty($row) || $row->del_flag == 1) {
            return ErrorHelper::WRONG_ID;
        } else if ($row->status != 10) {
            return '保养单已处理，不能编辑';
        }
        $row->maintain_by = $user->id;
//        }
        $form_data_val = [];
        $form_data = CvtUtil::emptyToArray($form_data);
        foreach ($form_data as $form_item){
            $form_data_val[$form_item['id']] =  $form_item['value'];
        }
        $row->form_data = json_encode($form_data,JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES);
        $row->form_data_val = json_encode($form_data_val,JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES);
        $row->item_id = $equ_id;
        $row->form_id = $form_id;
        $row->remarks = $remarks;
        $row->maintain_date = $maintain_date;
        if ($type == 1){
            $row->status = 10;
        }else{
            $row->status = 20;
        }

        $row->update_date = $now;
        $row->update_by = $user->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return EquItemMaintainLogs::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    private function getEquTypeName($code)
    {
        $ds = new DictService();
        $types = $ds->getEquType();
        foreach ($types as $type)
        {
            if ($type->code == $code) {
                return $type->name;
            }
        }
        return '';
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectById($uid);
        if (empty($row) || $row->del_flag == 1) {
            return '记录已失效，请下拉刷新页面';
        } else if ($row->status != 10) {
            return '记录已处理，无法删除，请下拉刷新页面';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }
}