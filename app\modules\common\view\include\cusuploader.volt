<script>
    var img_path = '{{ config.upyun.host }}';
    function uploadImg(id) {
        $("#btn_select_" + id).find("label").click();
    }

    function initUpLoader(id, title, folder_name, img_name, big_img) {
        var uploader_class = '';
        if (big_img != null && big_img) {
            uploader_class = ' uploader-one-custom';
        }

        var html = '<div id="uploader" class="uploader-one' + uploader_class + '">';
        html += '<div id="' + id + '" class="img-body">';
        html += '<div class="uploader-default">';
        html += '<div class="btn-plus"><span title="上传图片" onclick="uploadImg(\'' + id + '\')">+</span></div>';
        html += '</div>';
        html += '<div class="uploader-wrap">';
        html += '<div class="uploader-table">';
        html += '<div class="uploader-table-cell">';
        html += '<a class="btn btn-default btn-circle btn-upload" href="javascript:void(0);" onclick="uploadImg(\'' + id + '\')" title="上传图片">';
        html += '<i class="fa fa-upload"></i>';
        html += '</a>';
        html += '<a id="btn_see" class="btn btn-default btn-circle" href="javascript:void(0);" data-lightbox="licences" data-title="' + title + '" title="查看图片">';
        html += '<i class="fa fa-search"></i>';
        html += '</a>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '<div id="btn_select_' + id + '" style="display: none;">选择图片</div>';
        html += '</div>';
        html += '<div class="uploader-title">' + title + '</div>';
        $("#" + id + "_area").append(html);

        if (img_name !== null && img_name !== '') {
            showImage(id, img_name, big_img);
        }

        var uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/lib/uploader/uploader.swf') }}',
            server: '',
            pick: '#btn_select_' + id,
            accept: {
                title: 'Images',
                extensions: 'jpg,jpeg,png',
                mimeTypes: 'image/jpg,image/jpeg,image/png'
            },
            fileSingleSizeLimit: 10 * 1024 * 1024,
            compress: false,
            resize: false,
            duplicate: true
        });

        uploader.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，图片格式只支持jpg, jpeg, png');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，图片大小不能超过10M');
            }
        });

        uploader.on('fileQueued', function() {
            showSpin();
            uploader.options.server = '{{ url('common/file/upload/') }}' + folder_name;
            uploader.upload();
        });

        uploader.on('uploadSuccess', function(file, rs) {
            closeSpin(null);
            if (rs.status == 'ok') {
                showImage(id, rs.file_name, big_img);
                uploadSuccess(id, rs);
            } else {
                toastr.error('操作失败！');
            }
        });

        uploader.on('uploadError', function(file) {
            closeSpin(null);
            toastr.error('操作失败！');
        });
    }

    function initUpLoaderReadOnly(id, title, img_name, big_img) {
        var uploader_class = '';
        if (big_img != null && big_img) {
            uploader_class = ' uploader-one-custom';
        }

        var html = '<div id="uploader" class="uploader-one' + uploader_class + '">';
        html += '<div id="' + id + '" class="img-body">';
        html += '<div class="uploader-wrap">';
        html += '<div class="uploader-table">';
        html += '<div class="uploader-table-cell">';
        html += '<a id="btn_see" class="btn btn-default btn-circle" href="javascript:void(0);" data-lightbox="licences" data-title="' + title + '" title="查看图片">';
        html += '<i class="fa fa-search"></i>';
        html += '</a>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '<div id="btn_select_' + id + '" style="display: none;">选择图片</div>';
        html += '</div>';
        html += '<div class="uploader-title">' + title + '</div>';
        $("#" + id + "_area").append(html);

        showImage(id, img_name, big_img);
    }

    function showImage(id, img_name, big_img) {
        var $img_body = $("#" + id);
        $img_body.find(".file-item").remove();

        var small_img = '';
        var img_style = '';
        if (big_img == null || big_img == false) {
            small_img = '!small';
        } else {
            img_style = ' style="height: ' + ($img_body.height() - 10) + 'px"';
        }

        var $div_img = $(
            '<div class="file-item thumbnail">' +
            '<img src="' + img_path + img_name + small_img + '"' + img_style + '>' +
            '</div>'
        );

        $img_body.find(".uploader-default").remove();
        $img_body.find(".uploader-wrap").before($div_img);
        $img_body.find("#btn_see").attr("href", img_path + img_name);
    }

    function removeImage(id) {
        var $img_body = $("#" + id);
        if ($img_body.find(".uploader-default").length == 0) {
            $img_body.find(".file-item").remove();

            var $div_img = $(
                '<div class="uploader-default">' +
                '<div><span title="上传图片" onclick="uploadImg(\'' + id + '\')">+</span></div>' +
                '</div>'
            );

            $img_body.find(".uploader-wrap").before($div_img);
            $img_body.find("#btn_see").attr("href", "javascript:void(0);");
        }
    }
</script>