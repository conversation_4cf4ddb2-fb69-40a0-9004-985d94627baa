{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">打印模板管理</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">模板名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="name" v-model="name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   data-mobile-responsive="true"
                   data-query-params="app.params"
                   data-pagination="true"
                   data-url="{{ url('printing/template/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server"
                   class="table table-striped table-condensed">
                <thead class="bg-blue">
                <tr>
                    <th data-field="template_name">模板名称</th>
                    <th data-field="page_name">页面名称</th>
                    <th data-field="default_flag" data-formatter="defaultFormatter">默认使用</th>
                    <th data-field="status" data-formatter="statusFormatter">启用状态</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu pull-right" role="menu">
            <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-fw fa-check"></i>&nbsp;编辑</a></li>
            <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-fw fa-times"></i>&nbsp;删除</a></li>
            <li><a href="javascript:" onclick="design('@id@')"><i class="fa fa-fw fa-check"></i>&nbsp;设计模板</a></li>
        </ul>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            name: ''
        },
        methods: {
            search: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params : function (p) {
                p.name = this.name;
                return p;
            },
            reset: function () {
                this.name = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    var $table = $('#table');
    $table.bootstrapTable();

    function actionFormatter(v, row, idx) {
        let actHtml = $('#act').html();
        return actHtml.replace(/@id@/g, row.uid);
    }

    function defaultFormatter(v){
        if (v == 1){
            return '默认使用';
        }
        return '';
    }

    function statusFormatter(v){
        if (v == 1){
            return '启用';
        }
        return '停用';
    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '打印模板管理',
            type: 2,
            area: ['40em', '60em'],
            content: '{{ url('printing/template/create') }}',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '打印模板管理',
            type: 2,
            area: ['40em', '60em'],
            content: '{{ url('printing/template/edit/') }}'+uid,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function design(id) {
        var contract_url = "{{'/common/ext/document?uid='}}"+id;
        top.window.layer_result = null;
        top.layer.open({
            title: '打印模板管理',
            resize: false,
            type: 2,
            area: ['100%', '100%'],
            content: contract_url,
            end: function () {
                if (top.window.layer_result == 'ok') {
                    //app.submitCommit('');
                }
            }
        });
    }


    function del(id) {
        var dlg = top.layer.confirm('确认删除吗？', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('printing/template/delete') }}", {uid: id}, function (rs) {
                closeSpin();
                if (rs.status == 'ok') {
                    toastr.success('操作成功！');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }
</script>