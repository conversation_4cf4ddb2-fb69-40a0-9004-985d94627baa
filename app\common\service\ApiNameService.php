<?php

namespace Envsan\Common\Service;

use Envsan\Common\Component\Logger;

/**
 * API名称服务
 * 用于根据URI获取对应的中文API名称
 */
class ApiNameService
{
    private static $apiNames = null;
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 私有构造函数
     */
    private function __construct()
    {
        $this->loadApiNames();
    }
    
    /**
     * 加载API名称映射
     */
    private function loadApiNames()
    {
        if (self::$apiNames === null) {
            $apiNamesFile = BASE_PATH . '/app/common/mapping/api_names.php';
            
            if (file_exists($apiNamesFile)) {
                try {
                    self::$apiNames = include $apiNamesFile;
                    Logger::info('ApiNameService加载API名称映射成功', 'count=' . count(self::$apiNames));
                } catch (\Exception $e) {
                    Logger::error('ApiNameService加载API名称映射失败', $e->getMessage());
                    self::$apiNames = [];
                }
            } else {
                Logger::info('ApiNameService未找到API名称映射文件', $apiNamesFile);
                self::$apiNames = [];
            }
        }
    }
    
    /**
     * 根据URI获取API中文名称
     * @param string $uri 请求URI，如 /sys/user/create
     * @return string 中文名称，如 "系统管理:新增用户"
     */
    public function getApiNameByUri(string $uri): string
    {
        try {
            // 解析URI为模块:控制器:动作格式
            $identity = $this->parseUriToIdentity($uri);
            
            if ($identity && isset(self::$apiNames[$identity])) {
                return self::$apiNames[$identity];
            }
            
            // 如果没有找到精确匹配，尝试模糊匹配
            $fuzzyName = $this->fuzzyMatchApiName($uri);
            if ($fuzzyName) {
                return $fuzzyName;
            }
            
            // 都没找到，返回基于URI的默认名称
            return $this->generateDefaultName($uri);
            
        } catch (\Exception $e) {
            Logger::error('ApiNameService获取API名称失败', $uri, $e->getMessage());
            return $this->generateDefaultName($uri);
        }
    }
    
    /**
     * 将URI解析为标识符格式
     * @param string $uri 如 /sys/user/create 或 /sys/user/create.json
     * @return string 如 sys:user:create
     */
    private function parseUriToIdentity($uri)
    {
        // 移除开头的斜杠和查询参数
        $uri = ltrim($uri, '/');
        $uri = strtok($uri, '?');
        
        // 移除文件扩展名
        $uri = preg_replace('/\.(json|html|xml)$/', '', $uri);
        
        // 分割路径
        $parts = array_filter(explode('/', $uri));
        
        if (count($parts) >= 3) {
            // 标准格式: module/controller/action
            return strtolower($parts[0]) . ':' . strtolower($parts[1]) . ':' . strtolower($parts[2]);
        }
        
        return null;
    }
    
    /**
     * 模糊匹配API名称
     */
    private function fuzzyMatchApiName($uri)
    {
        $parts = array_filter(explode('/', ltrim($uri, '/')));
        
        if (count($parts) < 2) {
            return null;
        }
        
        $module = strtolower($parts[0]);
        $controller = strtolower($parts[1]);
        
        // 尝试匹配模块和控制器
        foreach (self::$apiNames as $identity => $name) {
            $identityParts = explode(':', $identity);
            if (count($identityParts) >= 2 && 
                $identityParts[0] === $module && 
                $identityParts[1] === $controller) {
                // 找到同模块同控制器的API，提取类名部分
                $nameParts = explode(':', $name);
                if (count($nameParts) >= 1) {
                    return $nameParts[0] . ':数据操作';
                }
            }
        }
        
        return null;
    }
    
    /**
     * 生成默认名称
     */
    private function generateDefaultName($uri): string
    {
        $parts = array_filter(explode('/', ltrim($uri, '/')));
        
        // 模块映射
        $moduleMap = [
            'sys' => '系统管理',
            'user' => '用户管理',
            'role' => '角色管理', 
            'group' => '部门管理',
            'menu' => '菜单管理',
            'dict' => '字典管理',
            'log' => '日志管理',
            'config' => '配置管理',
            'product' => '产品管理',
            'order' => '订单管理',
            'inventory' => '库存管理',
            'quality' => '质量管理',
            'production' => '生产管理'
        ];
        
        // 动作映射
        $actionMap = [
            'create' => '新增',
            'add' => '新增', 
            'insert' => '新增',
            'update' => '修改',
            'edit' => '修改',
            'modify' => '修改',
            'delete' => '删除',
            'remove' => '删除',
            'save' => '保存',
            'import' => '导入',
            'export' => '导出',
            'list' => '列表',
            'view' => '查看',
            'detail' => '详情'
        ];
        
        $nameParts = [];
        
        foreach ($parts as $part) {
            $part = strtolower(strtok($part, '?'));
            $part = strtolower(strtok($part, '.'));
            
            if (isset($moduleMap[$part])) {
                $nameParts[] = $moduleMap[$part];
            } elseif (isset($actionMap[$part])) {
                $nameParts[] = $actionMap[$part];
            }
        }
        
        return implode(':', $nameParts) ?: '数据操作';
    }
    
    /**
     * 获取所有API名称映射
     */
    public function getAllApiNames()
    {
        return self::$apiNames;
    }
    
    /**
     * 重新加载API名称映射
     */
    public function reload()
    {
        self::$apiNames = null;
        $this->loadApiNames();
    }
} 