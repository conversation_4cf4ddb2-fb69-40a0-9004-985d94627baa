<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ owner.short_name }}</title>
    <link rel="shortcut icon" href="{{ static_url('favicon.ico') }}" />
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <!-- Bootstrap 3.3.6 -->
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/toastr/toastr.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/font-awesome-4.7.0/css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/simple-line-icons/simple-line-icons.min.css') }}"/>
    <link rel="stylesheet" href="{{ static_url('static/global/css/components.css') }}"/>
    <link rel="stylesheet" href="{{ static_url('static/layouts/layout/css/layout.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/layouts/layout/css/themes/darkblue.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/layouts/layout/css/custom.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/global/plugins/lightbox2/css/lightbox.css') }}">
    <link rel="stylesheet" href="{{ static_url('static/pages/css/index.css') }}">
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="page-header navbar navbar-fixed-top">
    <div class="page-header-inner">
        <div class="page-logo">
            <div class="sys-name" style="color: #fff;font-size: 18px;float: left;line-height: 50px;">{{ owner.short_name }}</div>
            <div class="menu-toggler sidebar-toggler">
                <span></span>
            </div>
        </div>
        <a href="javascript:void(0);" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse">
            <span></span>
        </a>

        {% if menu | length > 0 %}
            <div class="total-menu">
                <ul class="nav navbar-nav no-borders">
                    <li class="dropdown">
                        <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                            <i class="icon-grid"></i>
                        </a>
                        <div class="dropdown-menu hdropdown bigmenu animated">
                            <table>
                                <tbody id="tb_menu_main">
                                {% for index, menu_list in menu %}
                                    {% if index % 3 == 0 %}
                                        <tr>
                                    {% endif %}
                                    <td>
                                        <a id="{{ menu_list.id }}" href="javascript:void(0);" onclick="menuChange('{{ menu_list.id }}')">
                                            <i class="{{ menu_list.icon }}"></i>
                                            <h5>{{ menu_list.header }}</h5>
                                        </a>
                                    </td>
                                    {% if index%3 == 2 %}
                                        </tr>
                                    {% endif %}
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </li>
                </ul>
            </div>
        {% endif %}

        <div id="app_top_menu" class="top-menu">
            <ul class="nav navbar-nav pull-right">
                <li v-if="all_cnt > 0" class="dropdown dropdown-extended dropdown-notification" id="header_notification_bar">
                    <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                        <i class="icon-bell"></i>
                        <span class="badge badge-default" v-text="all_cnt"> </span>
                    </a>
                    <ul class="dropdown-menu">
                        <li class="external">
                            <h3>
                                <span class="bold">待办</span>
                            </h3>
                        </li>
                        <li>
                            <ul class="dropdown-menu-list scroller" data-handle-color="#637283">
                                <li v-for="row in all_page_list">
                                    <a href="javascript:;"  @click="openPage(row.url)">
                                        <span class="time" style="background-color: red;color: #fff;font-size: 13px;border-radius: 10px;width: 20px;text-align: center;" v-text="row.cnt"></span>
                                        <span class="details">
                                            <span class="label label-sm">
                                                <i style="color: #36c6d3;font-size: 18px" :class="row.icon"></i>
                                            </span>
                                            <span style="font-size: 14px" v-text="row.name"></span>
                                        </span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
                {% if !acl.isSuper() %}
                    <li class="dropdown dropdown-user">
                        <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                            <i class="fa fa-group" style="font-size: 14px;position: relative;"></i>
                            <span class="username username-hide-on-mobile"> {{ group.name }} </span>
                        </a>
                    </li>
                {% endif %}
                <li class="dropdown dropdown-user">
                    <a href="javascript:void(0);" class="dropdown-toggle" style="padding-right: 6px;" onclick="openHome()">
                        <i class="fa fa-home" style="font-size: 18px;top: 1px;position: relative;"></i>
                        <span class="username username-hide-on-mobile"> 主页 </span>
                    </a>
                </li>
                <li class="dropdown dropdown-user">
                    <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                        <span class="username username-hide-on-mobile"> {{ user.real_name }} </span>
                        <i class="fa fa-angle-down"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-default">
                        <li>
                            <a href="javascript:void(0);" onclick="openAccount()">
                                <i class="icon-user"></i> 我的账户
                            </a>
                        </li>
                        <li class="divider"> </li>
                        <li>
                            <a href="javascript:void(0);" onclick="logout()">
                                <i class="icon-logout"></i> 退出
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="clearfix"> </div>
<div class="page-container">
    <div style="display: flex;">
        <div id="app_menu" class="page-sidebar-wrapper">
            <div class="page-sidebar navbar-collapse collapse">
                <ul v-html="menu_list" class="page-sidebar-menu page-header-fixed" data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200" style="padding-top: 20px">
                </ul>
            </div>
        </div>
        <iframe id="content-frame" src="{{ url(home_page['url']) }}" style="display: block;flex: 1;"
                allowtransparency="true" scrolling="auto" frameborder="0"></iframe>
    </div>
</div>

{% include "include/loader.volt" %}

<!-- jQuery 2.2.3 -->
<script src="{{ static_url('static/global/plugins/jquery/jquery-2.2.3.min.js') }}"></script>
<!-- Bootstrap 3.3.6 -->
<script src="{{ static_url('static/global/plugins/bootstrap/js/bootstrap.min.js') }}"></script>
<script src="{{ static_url('static/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js') }}"></script>
<!-- toastr -->
<script src="{{ static_url('static/global/plugins/toastr/toastr.min.js') }}"></script>
<script src="{{ static_url('static/global/plugins/layer/layer.js') }}"></script>
<script src="{{ static_url('static/global/plugins/qurl/qurl.js') }}"></script>
<script src="{{ static_url('static/global/plugins/bootbox/bootbox.min.js') }}"></script>
<script src="{{ static_url('static/global/js/app.js') }}"></script>
<script src="{{ static_url('static/global/js/custom.js') }}"></script>
<script src="{{ static_url('static/layouts/layout/js/layout.js') }}"></script>
<script src="{{ static_url('static/layouts/layout/js/demo.min.js') }}"></script>
<script src="{{ static_url('static/layouts/global/js/quick-sidebar.min.js') }}"></script>
<script src="{{ static_url('static/global/plugins/vue/vue.js') }}"></script>
<script>
    var app_top_menu = new Vue({
        el: '#app_top_menu',
        data: {{ data | json_encode }},
        methods: {
            openPage: function(href) {
                $('#content-frame').prop('src', href);
                url.query('frame', encodeURIComponent(href));
                menuSlideUp();
            },
            openKnow: function() {
                top.window.layer_result = '';
                top.layer.open({
                    title: '更多通知',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/knowlist') }}',
                    end: function() {
                        if (top.window.layer_result == 'ok') {
                            toastr.success('操作完毕');
                            app_top_menu.refresh();
                        }
                    }
                });
            },
            openDetail: function(row, type) {
                top.window.layer_result = '';
                top.layer.open({
                    title: '业务详情',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/view/') }}' + row.uid + '/' + type,
                    end: function() {
                        if (top.window.layer_result == 'ok') {
                            toastr.success('操作完毕');
                            app_top_menu.refresh();
                        }
                    }
                });
            },
            openMy: function() {
                top.window.layer_result = '';
                top.layer.open({
                    title: '我的申请',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/mylist') }}',
                    end: function() {
                        if (top.window.layer_result == 'ok') {
                            toastr.success('操作完毕');
                            app_top_menu.refresh();
                        }
                    }
                });
            },
            openReview: function() {
                top.window.layer_result = '';
                top.layer.open({
                    title: '更多审批',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/reviewlist') }}',
                    end: function() {
                        if (top.window.layer_result == 'ok') {
                            toastr.success('操作完毕');
                            app_top_menu.refresh();
                        }
                    }
                });
            },
            refresh: function() {
                showSpin();
                $.post("{{ url('sys/home/<USER>') }}", function (rs) {
                    closeSpin();
                    app_top_menu.all_cnt = rs.all_cnt;
                    app_top_menu.all_page_list = rs.all_page_list;
                    app_top_menu.tz_page_list = rs.tz_page_list;
                })
            },
        }
    });

    var app_menu = new Vue({
        el: '#app_menu',
        data: {
            menu_list: ''
        }
    });

    var url = Qurl.create();
    $('.nav-link').not('.nav-toggle').click(function (e) {
        e.preventDefault();
        $('#content-frame').prop('src', $(this).prop('href') );
        url.query('frame', encodeURIComponent($(this).prop('href')));

        var me = this;
        var $menu = $('.page-sidebar-menu');
        $menu.find('li.nav-item.open').each(function() {
            var match = false;
            $(this).find('li').each(function(){
                if ($(this).find(' > a').attr('href') === $(me).attr('href')) {
                    match = true;
                    return;
                }
            });

            if (match === true) {
                return;
            }

            $(this).removeClass('open');
            $(this).find('> a > .arrow.open').removeClass('open');
            $(this).find('> .sub-menu').slideUp();
        });

        $menu.find('li.active').removeClass('active');
        $(this).parents('li').each(function () {
            $(this).addClass('active');
        });
    });

    function menuChange(id, init_flag, menu_list) {
        showSpin();
        $.post('{{ url('sys/index/menu/') }}' + id, function(rs) {
            closeSpin();
            if (typeof rs == 'string' && rs.indexOf('<!DOCTYPE html>') == 0) {
                location.href = '{{ url("sys/login") }}';
                return;
            }

            app_menu.menu_list = rs;

            app_menu.$nextTick(function() {
                $('.nav-link').not('.nav-toggle').click(function (e) {
                    e.preventDefault();

                    if ($(this).attr("target") == '_blank') {
                        window.open($(this).prop('href'));
                    } else {
                        $('#content-frame').prop('src', $(this).prop('href'));
                        url.query('frame', encodeURIComponent($(this).prop('href')));
                        url.query('menu_id', id);
                    }

                    var me = this;
                    var $menu = $('.page-sidebar-menu');
                    $menu.find('li.nav-item.open').each(function() {
                        var match = false;
                        $(this).find('li').each(function(){
                            if ($(this).find(' > a').attr('href') === $(me).attr('href')) {
                                match = true;
                                return;
                            }
                        });

                        if (match === true) {
                            return;
                        }

                        $(this).removeClass('open');
                        $(this).find('> a > .arrow.open').removeClass('open');
                        $(this).find('> .sub-menu').slideUp();
                    });

                    $menu.find('li.active').removeClass('active');
                    $(this).parents('li').each(function () {
                        $(this).addClass('active');
                    });
                });

                if (init_flag != 1 && !menu_list) {
                    let $items = $(".page-sidebar-menu > .nav-item");
                    if ($items.length > 0) {
                        openFirstMenu($items[0]);
                    }
                }

                openMenu(menu_list);
            });
        });
    }

    function openFirstMenu(obj) {
        $(obj).children(".nav-link").click();
        if ($(obj).children(".sub-menu").length > 0) {
            openFirstMenu($(obj).children(".sub-menu").children(".nav-item")[0]);
        }
    }

    function adjustFrame() {
        var h = $(window).height() - $('.page-header').outerHeight(true);
        $("#content-frame").height(h);
    }

    function refreshTopData(){
        $.post('{{ url('sys/index/refresh') }}', {}, function (rs) {
            app_top_menu.all_cnt = rs.all_cnt;
            app_top_menu.all_page_list = rs.all_page_list;
            app_top_menu.know_cnt = rs.know_cnt;
            app_top_menu.know_list = rs.know_list;
            app_top_menu.my_cnt = rs.my_cnt;
            app_top_menu.my_list = rs.my_list;
            app_top_menu.review_cnt = rs.review_cnt;
            app_top_menu.review_list = rs.review_list;
            app_top_menu.tz_page_list = rs.tz_page_list;
        })
    }

    $("#content-frame").load(function () {
        adjustFrame();
    });

    $(window).resize(function () {
        adjustFrame();
    });

    // 如果用户刷新的话，则保持当前frame
    $().ready(function() {
        let home_page_menu_id = '{{ home_page['id'] }}';
        var frame = url.query('frame');
        if (frame == '') frame = '{{ home_page['url'] }}';
        $("#content-frame").attr("src", decodeURIComponent(frame));

        var menu_id = url.query('menu_id');
        if (!menu_id) {
            menu_id = home_page_menu_id;
        }
        menuChange(menu_id, 1);
        localStorage.setItem('session', JSON.stringify({sid: '{{ sid }}'}));

        $("#content-frame").load(function() {
            closeSpin();
        });
        {% if pwd_flag %}
        // setTimeout(function (){
        //     var dlg = top.layer.confirm('您的密码为初始密码，请马上修改!', function(){
        //         top.layer.close(dlg);
        //         openAccount();
        //     });
        // },2000)
        {% endif %}
    });

    function openAccount() {
        var href = 'sys/user/account';
        $('#content-frame').prop('src', href);
        url.query('frame', encodeURIComponent(href));
        menuSlideUp();
    }

    function changeGroup(uid,name) {
        showSpin();
        $.post('{{ url('sys/group/switch') }}', {uid:uid}, function (rs) {
            closeSpin(null);
            if(rs.status=='ok'){
                $('#group_name').html(name);
                refreshFrame();
            }
            else {
                toastr.error('操作失败!'+rs.message);
            }
        })
    }

    function logout() {
        bootbox.dialog({
            message: "确定要退出吗？",
            buttons: {
                yes: {
                    label: "确定",
                    className: "green",
                    callback: function() {
                        location.href = '{{ url("sys/login/logout") }}';
                    }
                },
                no: {
                    label: "取消",
                    className: "default"
                }
            }
        });
    }

    function openHome() {
        var href = '{{ url(home_page['url']) }}';
        $('#content-frame').prop('src', href);
        url.query('frame', encodeURIComponent(href));
        menuSlideUp();
    }

    function menuSlideUp() {
        var $menu = $('.page-sidebar-menu');
        $menu.find('li.active').removeClass('active');
        $menu.find('li.nav-item.open > .sub-menu').slideUp();
        $menu.find('li.nav-item.open').removeClass('open');
        $menu.find('li.nav-item .arrow.open').removeClass('open');
    }

    function refreshFrame() {
        showSpin();
        document.getElementById('content-frame').contentDocument.location.reload(true);
    }

    function clickMenu(menu_id, menu_list) {
        if (url.query('menu_id') != menu_id) {
            menuChange(menu_id, 1, menu_list);
        } else {
            openMenu(menu_list);
        }
    }

    function openMenu(menu_list) {
        if (menu_list) {
            let menu_id = menu_list.shift();
            let $menu_obj = $(".page-sidebar-menu").children("#" + menu_id);
            $menu_obj.children("a").click();

            openSubMenu(menu_list, $menu_obj);
        }
    }

    function openSubMenu(menu_list, $menu_obj_arg) {
        if (menu_list.length > 0) {
            let menu_id = menu_list.shift();
            let $menu_obj = $menu_obj_arg.children('.sub-menu').children("#" + menu_id);
            $menu_obj.children("a").click();

            openSubMenu(menu_list, $menu_obj);
        }
    }
</script>
</body>
</html>
