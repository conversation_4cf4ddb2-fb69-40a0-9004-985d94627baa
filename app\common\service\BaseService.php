<?php
namespace Envsan\Common\Service;

use Envsan\Common\Component\Logger;
use Phalcon\Mvc\User\Component;

/**
 * 基础服务类，提供统一的返回结果方法
 */
class BaseService extends Component
{

    /**
     * 通用事务处理
     * @param callable $businessLogic 业务逻辑回调函数
     * @param string $successMessage 成功提示信息
     * @return array
     */
    protected function executeInTransaction($businessLogic, $successMessage = '操作成功') {
        $this->db->begin();
        try {
            // 执行业务逻辑
            $result = $businessLogic();
            if (is_array($result) && isset($result['success']) && !$result['success']) {
                // 如果业务逻辑返回了错误格式，转换为异常
                throw new \Exception($result['message'] ?? '业务逻辑执行失败');
            }
            $this->db->commit();
            return $this->success($result);
        } catch (\Exception $e) {
            $this->db->rollback();
            return $this->error($e->getMessage());
        }
    }

    /**
     * 返回成功结果
     * @param mixed $data 返回的数据
     * @return array 包含success=true和data的数组
     */
    protected function success($data = null)
    {
        return [
            'success' => true,
            'data' => $data
        ];
    }
    
    /**
     * 返回失败结果
     * @param string $message 错误信息
     * @return array 包含success=false和message的数组
     */
    protected function error($message)
    {
        return [
            'success' => false,
            'message' => $message
        ];
    }

    /**
     * 通用序号生成方法
     * @param string $modelClass 模型类名（如：'Envsan\Modules\Purchase\Model\PurchaseArrivalRejection'）
     * @param string $typeCode 类型代码前缀（如：'LLBLP2501'）
     * @param int $numberLength 序号长度，默认4位
     * @return string 格式化的序号（如：'0001'）
     */
    protected function generateSequenceNumber($modelClass, $typeCode, $numberLength = 4): string
    {
        $row = $modelClass::findFirst([
            'code like ?1 and length(code) = ?2',
            'bind' => [1 => "$typeCode%", 2 => strlen($typeCode) + $numberLength],
            'order' => 'code desc'
        ]);
        
        if (empty($row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($row->code, strlen($typeCode))) + 1;
        }
        
        return str_pad($cnt, $numberLength, '0', STR_PAD_LEFT);
    }
} 