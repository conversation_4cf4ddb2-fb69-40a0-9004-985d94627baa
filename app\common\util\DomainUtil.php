<?php
namespace Envsan\Common\Util;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Modules\Sys\Model\Owner;

class DomainUtil
{
    public static function host()
    {
        $protocol = 'http://';

        if (!empty($_SERVER['HTTPS'])
            || (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')) {
            $protocol = 'https://';
        }

        return $protocol.$_SERVER['HTTP_HOST'];
    }

    public static function subdomain()
    {
        if(empty($_SERVER['HTTP_HOST']))
            $host = '';
        else
            $host = $_SERVER['HTTP_HOST'];
        $pos = strpos($host, '.');
        if($pos===false)
            return '';
        return substr($host, 0, $pos);
    }

    public static function checkOwner($die=true)
    {
        static $owner=null;

        if($owner==null) {
            $domain = DomainUtil::subdomain();
            $owner = Owner::findFirst(['domain=?1', 'bind' => [1 => $domain]]);
        }

        if($owner==null && $die)
            die(ErrorHelper::ROW_NOTEXIST);
        return $owner;
    }
}