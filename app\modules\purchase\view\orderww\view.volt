{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}

<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-goods" style="padding-bottom: 5px;padding-top: 5px;margin-bottom: 10px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-basket-loaded font-yellow"></i>
                        <span class="caption-subject font-yellow bold">外委加工单信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="padding-top: 10px;padding-bottom: 10px;">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">订单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_code" v-model="order_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">订单日期</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_date" v-model="order_date" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">供应商</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">未税总金额</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="total_money" v-model="total_money" readonly>
                                                <span class="input-group-addon">元</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">含税总金额</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="total_money_hs" v-model="total_money_hs" readonly>
                                                <span class="input-group-addon">元</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('view') }}
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item, index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" class="form-control" v-model="remarks" maxlength="200" rows="3" readonly></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-sm-12">
            <div class="portlet light portlet-detail" style="margin-bottom: 0;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-handbag font-blue"></i>
                        <span class="caption-subject font-blue bold">外委加工单明细</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-bordered table-big" >
                                <thead>
                                <tr>
                                    <th>物料编码</th>
                                    <th>产品名称</th>
                                    <th>产品规格</th>
                                    <th>委外工序</th>
                                    <th>计量单位</th>
                                    <th>外委数量</th>
                                    <th>计价单位</th>
                                    <th>计价数量</th>
                                    <th>换算率</th>
                                    <th>未税单价(元)</th>
                                    <th>含税单价(元)</th>
                                    <th>税率</th>
                                    <th>税额</th>
                                    <th>未税金额</th>
                                    <th>税价合计</th>
                                    <th>订单号</th>
                                    <th>外委计划号</th>
                                    <th>生产批次号</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="ww_list.length == 0">
                                    <td colspan="18" style="text-align: center;">没有找到匹配的记录</td>
                                </tr>
                                <tr v-else v-for="row, index in ww_list">
                                    <td v-text="row.goods_code"></td>
                                    <td v-text="row.product_name"></td>
                                    <td v-text="row.product_model"></td>
                                    <td v-text="row.bom_name"></td>
                                    <td v-text="row.measurement_unit"></td>
                                    <td v-text="row.quantity"></td>
                                    <td v-text="row.price_unit"></td>
                                    <td v-text="row.pricing_quantity"></td>
                                    <td v-text="row.conversion_rate"></td>
                                    <td v-text="row.price"></td>
                                    <td v-text="row.price_hs"></td>
                                    <td>${row.tax_rate ? row.tax_rate * 100 : ''}</td>
                                    <td v-text="row.tax_money"></td>
                                    <td v-text="row.total_money"></td>
                                    <td v-text="row.total_money_hs"></td>
                                    <td v-text="row.order_code"></td>
                                    <td v-text="row.ww_plan_code"></td>
                                    <td v-text="row.notice_code"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

{{ partial('check') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }},
        },
    });

    function initSize() {
        let portlet_height = $(window).height() - 35 - 27 - 8;

        $(".portlet-detail .zh-table-box-content").height(portlet_height
            - $(".portlet-detail .portlet-title").outerHeight(true));

    }

    $(function () {
        initSize();
    });
</script>
<style>
    .zh-table-box table thead {
        z-index: 3;
    }
</style>