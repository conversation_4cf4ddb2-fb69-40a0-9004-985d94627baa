{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>供应商编号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" placeholder="请输入编号" name="code" v-model="code" required maxlength="50"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>供应商名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" placeholder="请输入名称" name="name" v-model="name" required maxlength="100"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>供应商简称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" placeholder="请输入简称" name="name_as" v-model="name_as" required maxlength="100"/>
                                </div>
                            </div>
                        </div>
                        {{ partial('form') }}
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" @click="submit" class="btn btn-primary">提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>
{{ partial('check') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonSupplier }},
        methods: {
            selectCatalogue: function() {
                top.window.layer_result = null;
                top.layer.open({
                    title: '选择目录',
                    type: 2,
                    area: ['35em', '60%'],
                    content: '{{ url('common/catalogue/sel/') ~ page_id }}',
                    end: function() {
                        if (top.window.layer_result != null) {
                            var rs = top.window.layer_result;
                            app.catalogue_id = rs.id;
                            app.catalogue_name = rs.name;
                        }
                    }
                });
            },
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('purchase/supplier/edit/' ~ uid) }}';
                {% else %}
                var url= '{{ url('purchase/supplier/create') }}';
                {% endif %}

                showSpin();
                $.post(url, {
                    code:app.code,
                    name:app.name,
                    name_as:app.name_as,
                    remarks:app.remarks,
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B')
                }, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            }
        }
    });
</script>
{{ partial('form_script') }}