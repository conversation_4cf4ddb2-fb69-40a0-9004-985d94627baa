{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">基本信息</span>
                    </div>
                </div>
                <div class="portlet-body form" >
                    <form id="form" class="form-horizontal">
                        <div class="form-body"  style="height: 76.5vh;overflow-y: auto;overflow-x: hidden">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">生产批次号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="notice_code" v-model="notice_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">客户</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="customer_name" v-model="customer_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品名称</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_name" v-model="product_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品规格</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_code" v-model="product_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检工艺</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="bom_name" v-model="bom_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检项目</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="quality_template_name" v-model="quality_template_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检日期</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="work_date" v-model="work_date" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检时间</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="create_time" v-model="create_time" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检人</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="staff_name" v-model="staff_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">质检结果</label>
                                        <div class="col-sm-8">
                                            <span v-if="error_flag == 1" class="label label-danger">NG</span>
                                            <span v-else class="label label-success">OK</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12" v-if="error_flag == 1">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">不良数量</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="error_cnt" v-model="error_cnt" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12" v-if="error_flag == 1">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">不良类型</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="error_type" v-model="error_type" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12" v-if="error_flag == 1">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">不良说明</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="error_remarks" v-model="error_remarks" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">生产工人</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="production_workers_names" v-model="production_workers_names" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">测量工具</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="check_tools_names" v-model="check_tools_names" readonly/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-blue"></i>
                        <span class="caption-subject font-blue bold">详情</span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center"></div>
                </div>
                <div class="portlet-body" style="height: 85vh;overflow-y: auto;display: flex;flex-wrap: wrap">
                    <div style="display: flex;border-bottom: 1px #e2e2e2 solid;border-right: 1px #e2e2e2 solid;">
                        <div>
                            <div class="item-content">
                                <span>检验项目</span>
                            </div>
                            <div v-for="(form_item,form_idx) in check_data" :key="form_idx" class="item-content" :style="{height: getRowHeight(form_item), borderBottom: form_idx === check_data.length - 1 ? '1px #e2e2e2 solid' : 'none'}">
                                <div style="text-align: left">
                                    <span v-text="form_item.title"></span>
                                    <span v-if="form_item.unit != ''" v-text="'('+ form_item.unit +')'"></span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="item-content">
                                <span>尺寸规格</span>
                            </div>
                            <div v-for="(form_item,form_idx) in check_data" :key="form_idx" class="item-content" :style="{height: getRowHeight(form_item), borderBottom: form_idx === check_data.length - 1 ? '1px #e2e2e2 solid' : 'none'}">
                                <div v-if="form_item.type == 7" style="text-align: left">
                                    <div>
                                        <span v-text="'最大值:' + form_item.standard_plus"></span>
                                    </div>
                                    <div>
                                        <span v-text="'最小值:' + form_item.standard_minus"></span>
                                    </div>
                                </div>
                                <div v-if="form_item.type == 8" style="text-align: left">
                                    <div>
                                        <span v-text="'标准值:' + form_item.standard_val"></span>
                                    </div>
                                    <div>
                                        <span v-text="'工差+:' + form_item.standard_plus"></span>
                                    </div>
                                    <div>
                                        <span v-text="'工差-:' + form_item.standard_minus"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="item-content">
                                <span>实测值</span>
                            </div>
                            <div v-for="(data,form_idx) in check_data" :key="form_idx" class="item-content" :style="{height: getRowHeight(data), borderBottom: form_idx === check_data.length - 1 ? '1px #e2e2e2 solid' : 'none'}">
                                <div v-if="data.type == 1 || data.type == 3 || data.type == 5">
                                    <span v-text="data.value"></span>
                                </div>
                                <div v-if="data.type == 2">
                                    <div v-for="(value, value_index) in data.values" :key="value_index">
                                        <span v-text="value"></span>
                                    </div>
                                </div>
                                <div v-if="data.type == 4">
                                    <template v-for="(value, value_index) in data.values">
                                        <span v-text="value"></span>
                                        <span v-if="value_index < data.values.length - 1">,</span>
                                    </template>
                                </div>
                                <div v-if="data.type == 6">
                                    <div v-for="(value, value_index) in data.values" :key="value_index">
                                        <span :style="{color : data.results[value_index] == 1 ? 'red':'#000'}" v-text="data.list[value]"></span>
                                    </div>
                                </div>
                                <div v-if="data.type == 7 || data.type == 8">
                                    <div v-for="(value, value_index) in data.values" :key="value_index">
                                        <span :style="{color : data.results[value_index] == 1 ? 'red':'#000'}" v-text="value"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {
            getRowHeight: function(item) {
                // 根据内容类型和数量计算行高
                if (!item.values || item.values.length === 0) {
                    return '30px';
                }
                
                var baseHeight = 30; // 基础高度
                var rowHeight = baseHeight;
                
                // 根据类型计算高度
                if (item.type == 2 || item.type == 6 || item.type == 7 || item.type == 8) {
                    // 这些类型每个值一行
                    rowHeight = item.values.length * baseHeight;
                }
                
                // 如果是类型7，考虑最大值和最小值的显示需要的高度
                if (item.type == 7) {
                    rowHeight = Math.max(rowHeight, 2 * baseHeight); // 至少需要2行高度
                }
                
                // 如果是类型8，考虑标准值、工差+和工差-的显示需要的高度
                if (item.type == 8) {
                    rowHeight = Math.max(rowHeight, 3 * baseHeight); // 至少需要3行高度
                }
                
                return rowHeight + 'px';
            }
        }
    });
</script>
<style>
    .item-content{
        border-top: 1px #e2e2e2 solid;
        border-left: 1px #e2e2e2 solid;
        width: 100px;
        padding: 5px;
        text-align: center;
        height: 30px;
    }
</style>