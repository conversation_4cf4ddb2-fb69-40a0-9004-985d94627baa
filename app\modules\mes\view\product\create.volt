{% do assets.collection('css').addCss('static/global/plugins/@logicflow/core/dist/style/index.css') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/core/dist/logic-flow.js') %}
{% do assets.collection('css').addCss('static/global/plugins/@logicflow/extension/lib/style/index.css') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/Menu.js') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/SelectionSelect.js') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/DndPanel.js') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/Group.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.worker.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vue.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/Sortable.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vuedraggable.umd.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}

{{ assets.outputJs('validate') }}
<!-- Main content -->
<div style="height: 100vh;width: 100%;background-color: #fff">
    <div style="display: flex;flex-direction: row;">
        <div id="app" style="width: 25vw">
            <div style="height:100vh;border-right: 1px #E2E2E2 solid;">
                <div class="portlet-body form">
                    <div class="tabbable-line boxless tabbable-reversed" style="border-bottom: 1px #36C6D3 SOLID;margin-top: 0;background-color: #f2f2f2;margin-bottom: 0">
                        <ul class="nav nav-tabs">
                            <li :class="tab_id == 1 ? 'active' : ''">
                                <a @click="tabClick(1)" style="min-width: 100px;text-align: center"
                                    data-toggle="tab" :aria-expanded="tab_id == 1 ? true : false">产品信息</a>
                            </li>
                            <li :class="tab_id == 2 ? 'active' : ''">
                                <a @click="tabClick(2)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="tab_id == 2 ? true : false">订单信息</a>
                            </li>
                            <li :class="tab_id == 3 ? 'active' : ''">
                                <a @click="tabClick(3)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="tab_id == 3 ? true : false">图纸管理</a>
                            </li>
                        </ul>
                    </div>
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 87vh;overflow-y: auto;">
                            <div id="form_data">
                                <div class="row" v-if="tab_id == 1">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>产品编号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="code" v-model="code" required maxlength="50" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>产品名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="name" v-model="name" required maxlength="100"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>存货档案分类</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="goods_type_text" v-model="goods_type_text" maxlength="20" required readonly>
                                                    <span class="input-group-btn">
                                                        <button type="button" class="btn btn-info" @click="showPopup">
                                                            <i class="fa fa-window-restore"></i>选择
                                                        </button>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {{ partial('form') }}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">备注</label>
                                            <div class="col-sm-8">
                                                <textarea style="resize: none;" placeholder="请输入备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" v-if="tab_id == 2">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">订单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">客户</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="customer_name" v-model="customer_name" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">产品名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="order_name" v-model="order_name" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    {{ partial('view2') }}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">备注</label>
                                            <div class="col-sm-8">
                                                <textarea style="resize: none;" class="form-control" v-model="order_remarks" readonly maxlength="200" rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">附件</label>
                                            <div class="col-sm-8" style="display: flex;flex-direction: column">
                                                <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="order_files.length > 0" v-for="item,index in order_files">
                                                    <div>
                                                        <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                            <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" v-if="tab_id == 3" >
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                        <thead>
                                        <tr>
                                            <th>图号</th>
                                            <th>类型/版本</th>
                                            <th>备注</th>
                                            <th v-if="select_card_id != ''">使用</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <template v-for="drawing_item,drawing_index in drawing_list">
                                            <tr>
                                                <td>
                                                    <span v-text="drawing_item.code"></span><br>
                                                    <span v-text="drawing_item.drawing_name"></span>
                                                </td>
                                                <td>
                                                    <span v-text="drawing_item.skill_type_name"></span><br>
                                                    <span v-text="drawing_item.version_code"></span>
                                                </td>
                                                <td>
                                                    <span v-text="drawing_item.remarks"></span>
                                                </td>
                                                <td v-if="select_card_id != ''">
                                                    <a href="javascript:;" @click="selectDrawing(drawing_index)">
                                                        <i class="fa fa-check-square" :style="{fontSize: '20px', color: drawing_item.id == select_drawing_id ? '#00D500' : '#D2D2D2'}"></i>
                                                    </a>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                                            操作 <span class="caret"></span>
                                                        </button>
                                                        <ul class="dropdown-menu pull-right" role="menu">
                                                            <li><a href="javascript:" @click="viewPdf(drawing_item.drawing_url)"><i class="fa fa-fw fa-eye"></i>&nbsp;查看图纸</a></li>
                                                            <li><a href="javascript:" @click="drawingDelete(drawing_item)"  v-if="drawing_item.status == 10"> <i class="fa fa-times"></i>&nbsp;删除</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>
                                        </tbody>
                                    </table>
                                    <div style="width: 100%;text-align: center;padding: 15px;">
                                        <div style="display: flex;justify-content: space-around;">
                                            <div class="btn btn-outline blue btn-sm" style="width: 150px;margin-top: 10px;margin-bottom: 10px"
                                                 @click="uploadDrawing(1)"><i class="fa fa-upload"></i> 单文件上传</div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 150px;margin-top: 10px;margin-bottom: 10px"
                                                 @click="uploadDrawing(2)"><i class="fa fa-upload"></i> 多文件合并上传</div>
                                        </div>
                                        <div v-for="image_item,image_index in drawing_images" style="margin-bottom: 20px;">
                                            <div style="border: 1px solid #c2cad8;border-bottom: 0;cursor: pointer" @click="drawingSelect(image_item)">
                                                <img style="width: 20vw" :src="image_item.base64" class="lightbox-image" />
                                            </div>
                                            <div style="border: 1px solid #c2cad8;padding: 6px 12px;background-color: #eef1f5;display: flex;align-items: center;justify-content: center;" v-text="image_item.url_name"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn blue" @click="save"><i class="fa fa-save"></i> 保存</button>
                            <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
            <div v-show="bom_show == 1" style="height:100vh;width: 25vw;border-left: 1px #E2E2E2 solid;position: absolute;top:0;right: 0;background-color: #fff;z-index: 9999">
                <div class="portlet-body form">
                    <div class="tabbable-line boxless tabbable-reversed" style="border-bottom: 1px #36C6D3 SOLID;margin-top: 0;background-color: #f2f2f2;margin-bottom: 0;position: relative">
                        <ul class="nav nav-tabs">
                            <li :class="bom_tab_id == 1 ? 'active' : ''">
                                <a @click="bomTabClick(1)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="bom_tab_id == 1 ? true : false">工艺信息</a>
                            </li>
                            <li :class="bom_tab_id == 2 ? 'active' : ''">
                                <a @click="bomTabClick(2)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="bom_tab_id == 2 ? true : false">检验标准</a>
                            </li>
                            <li :class="bom_tab_id == 3 ? 'active' : ''">
                                <a @click="bomTabClick(3)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="bom_tab_id == 3 ? true : false">原料/配件</a>
                            </li>
                        </ul>
                        <div style="position: absolute;top:10px;right: 15px">
                            <a @click="closeBom"><i style="font-size: 20px" class="fa fa-times"></i></a>
                        </div>
                    </div>
                    <form id="form_bom" class="form-horizontal">
                        <div class="form-body" style="height: 87vh;overflow-y: auto;">
                            <div id="form_data_bom">
                                <div class="row" v-show="bom_tab_id == 1">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">工艺类型</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="ship_type_name" v-model="bom_data.ship_type_name" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>工艺名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" placeholder="请输入工艺名称" class="form-control" name="bom_data_name" v-model="bom_data.name" required maxlength="100"/>
                                            </div>
                                        </div>
                                    </div>
{#                                    <div class="col-sm-12">#}
{#                                        <div class="form-group">#}
{#                                            <label class="col-sm-4 control-label"><span class="required">*</span>构成数量</label>#}
{#                                            <div class="col-sm-8">#}
{#                                                <input type="number" placeholder="请输入构成数量" class="form-control" number="true" name="bom_data_cnt" v-model="bom_data.cnt" maxlength="4" required>#}
{#                                            </div>#}
{#                                        </div>#}
{#                                    </div>#}
                                    <div class="col-sm-12" v-show="bom_data.plan_type == 1">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>生产性基准</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="number" placeholder="请输入生产性基准" class="form-control" number="true" name="bom_produce_cnt" v-model="bom_data.produce_cnt" maxlength="8" required>
                                                    <span class="input-group-addon">件/小时</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>加工成本</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="number" placeholder="请输入加工成本" class="form-control" number="true" name="bom_produce_cost" v-model="bom_data.produce_cost" maxlength="8" required>
                                                    <span class="input-group-addon">元/件</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>工资类型</label>
                                            <div class="col-sm-8">
                                                <select class="bs-select form-control" name="bom_work_type" v-model="bom_data.work_type" required>
                                                    <option value="">请选择</option>
                                                    {% for key,val in workTypes %}
                                                        <option value="{{key}}">{{ val }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12" v-show="bom_data.work_type == 2">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>计件工资</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="number" placeholder="请输入计件工资" class="form-control" number="true" name="bom_one_cost" v-model="bom_data.one_cost" maxlength="8" required>
                                                    <span class="input-group-addon">元/件</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">需要外委</label>
                                            <div class="col-sm-8">
                                                <select class="bs-select form-control" name="is_outsourcing" v-model="bom_data.is_outsourcing">
                                                     <option value="0">否</option>
                                                     <option value="1">是</option>
                                                 </select>
                                            </div>
                                        </div>
                                    </div>
                                    {{ partial('form2') }}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">备注</label>
                                            <div class="col-sm-8">
                                                <textarea style="resize: none;" placeholder="请输入备注" class="form-control" name="bom_data_remarks" v-model="bom_data.remarks" maxlength="200" rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12" v-if="bom_data.status < 20">
                                        <div style="display: flex;width: 100%;justify-content: space-between;padding-bottom: 10px">
                                            <div>
                                                <label class="control-label" style="font-weight: bold"><span class="required">*</span>请在左侧图纸管理中选择使用图纸</label>
                                            </div>
                                            <div class="btn-group">
                                                <button type="button" class="btn blue btn-outline btn-sm" @click="drawingSelectAll">全选</button>
                                                <button type="button" class="btn red btn-outline btn-sm" @click="drawingDeleteAll">全删</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12" style="text-align: center">
                                        <draggable tag="ul" :list="bom_data.drawing_data" group="bind_data" class="list-group" handle=".handle">
                                            <li
                                                class="list-group-item"
                                                v-for="drawing_item,drawing_index in bom_data.drawing_data"
                                                :key="drawing_index"
                                            >
                                                <i class="fa fa-align-justify handle" style="margin-right: 10px"></i>
                                                <a v-if="drawing_item.url != ''" class="lightbox-a"
                                                   :href="base_path + drawing_item.url" data-lightbox="goods-pic" :data-title="drawing_item.url_name">
                                                    <img style="width: 20vw" :src="base_path + drawing_item.url" class="lightbox-image" />
                                                </a>
                                                <a v-else class="lightbox-a" data-lightbox="goods-pic" :data-title="drawing_item.url_name">
                                                    <img style="width: 20vw" :src="drawing_item.base64" class="lightbox-image" />
                                                </a>
                                                <div>
                                                    <a style="color: red;font-size: 20px" v-if="drawing_item.status == 0" @click="bomDrawingDelete(drawing_index)"><i class="fa fa-times"></i></a>
                                                    <span v-text="drawing_item.url_name"></span>
                                                </div>
                                            </li>
                                        </draggable>
                                    </div>
                                </div>
                                <div class="row" v-show="bom_tab_id == 2">
                                    <div style="display: flex;flex-direction: row;">
                                        <select class="bs-select form-control" name="select_check_id" v-model="select_check_id" data-live-search="true" data-size="8" >
                                            <option value="">请选择</option>
                                            {% for check in checkList %}
                                                <option value="{{check['id']}}">{{ check['name'] }}</option>
                                            {% endfor %}
                                        </select>
                                        <div class="btn blue btn-outline" @click="addCheck" style="width: 100px;"><i class="fa fa-plus"></i> 添加检验项</div>
                                    </div>
                                    <div style="padding: 10px">
                                        <div v-for="(check,check_idx) in bom_data.check_data" class="row" style="border: 1px solid #E2E2E2;margin:0 0 15px 0;">
                                            <div style="background-color: #f2f2f2;padding: 5px;height: 40px">
                                                <div class="col-sm-6" style="font-size: 18px;color: #2b80e8">
                                                    <span v-text="check.name"></span>
                                                </div>
                                                <div class="col-sm-6" style="text-align: right">
                                                    <button @click="bom_data.check_data.splice(check_idx,1)" type="button" class="btn red btn-outline btn-sm"><i class="fa fa-close"></i>删除</button>
                                                </div>
                                            </div>
                                            <template v-for="(check_item,check_item_idx) in check.form_data">
                                                <div class="col-sm-12" style="display: flex;border-top:  1px solid #E2E2E2;">
                                                    <div style="width: 35%;padding: 5px;">
                                                        <span v-text="check_item.title"></span><span v-if="check_item.unit != ''" v-text="'('+check_item.unit+')'"></span><br/>
                                                        (<span v-text="check_item.explain"></span>)
                                                    </div>
                                                    <div style="width: 65%;padding: 5px;">
                                                        <div v-if="check_item.type == 3 || check_item.type == 4 || check_item.type == 6">
                                                            <span v-for="(item,idx) in check_item.list" v-text=" check_item.list.length == idx+1 ? item :  item + ','"></span>
                                                        </div>
                                                        <div v-if="check_item.type == 7">
                                                            <span v-text="check_item.formula_val"></span>
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_plus' + check_item_idx" v-model="check_item.standard_plus" maxlength="10" required>
                                                                <span class="input-group-addon">最大值</span>
                                                            </div>
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_minus' + check_item_idx" v-model="check_item.standard_minus" maxlength="10" required>
                                                                <span class="input-group-addon">最小值</span>
                                                            </div>
                                                        </div>
                                                        <div v-if="check_item.type == 8">
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_val' + check_item_idx" v-model="check_item.standard_val" maxlength="10" required>
                                                                <span class="input-group-addon">标准值</span>
                                                            </div>
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_plus' + check_item_idx" v-model="check_item.standard_plus" maxlength="10" required>
                                                                <span class="input-group-addon">工差(+)</span>
                                                            </div>
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" number="true" :name="'standard_minus' + check_item_idx" v-model="check_item.standard_minus" maxlength="10" required>
                                                                <span class="input-group-addon">工差(-)</span>
                                                            </div>
                                                        </div>
                                                        <div v-if="check_item.type == 1 || check_item.type == 2 || check_item.type == 5">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" v-show="bom_tab_id == 3">
                                    <div style="padding-bottom: 15px;width: 100%;display: flex;flex-direction: row;justify-content: flex-end">
                                        <button type="button" class="btn btn-outline blue btn-sm"  @click="addGoods">选择物料</button>
                                    </div>
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>编码/名称</th>
                                            <th>规格/型号</th>
                                            <th>数量</th>
                                            <th>供应商</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <template  v-for="bom_row, bom_idx in bom_data.goods_data">
                                            <tr>
                                                <td v-text="bom_idx + 1"></td>
                                                <td>
                                                    <span v-text="bom_row.code"></span><br>
                                                    <span v-text="bom_row.name"></span>
                                                </td>
                                                <td>
                                                    <span v-text="bom_row.model"></span>
                                                </td>
                                                <td>
                                                    <span v-text="bom_row.quantity"></span>(<span v-text="bom_row.deputy_unit"></span>)
                                                </td>
                                                <td>
                                                    <span v-text="bom_row.supplier_name"></span><br>
                                                    <span v-text="bom_row.remarks"></span>
                                                </td>
                                            </tr>
                                            <tr v-if="bom_row.f_list.length > 0">
                                                <td></td>
                                                <td colspan="4">
                                                    <div style="display: flex">
                                                        <div v-for="formula_item,formula_idx in bom_row.f_list" v-if="formula_item.t == 2" style="margin-right: 15px">
                                                            <span v-text="formula_item.l"></span>: <span v-text="formula_item.v"></span>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn btn-danger" @click="deleteNode(bom_data.uid)">删除</button>
                            <button type="button" class="btn btn-success" @click="bomSave">保存</button>
                            <button type="button" class="btn default" @click="closeBom"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- 添加弹出窗口的HTML结构 -->
            <div id="custom-popup" class="modal fade" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title">双击选择产品档案</h4>
                        </div>
                        <div class="modal-body">
                            <div>
                                <div id="tree"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" @click="handleClose">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="width: 75vw;position: relative;">
            <div class="logic-flow-view">
                <div style="position: absolute;top: 5px;left: 5px;z-index: 999;display: flex">
                    <input type="text" style="width: 100px;margin-right: 10px" class="form-control" name="searchShipType" id="searchShipType" maxlength="10" placeholder="筛选工艺">
                    <button type="button" class="btn blue btn-outline" onclick="lfCenter()"><i class="fa fa-location-arrow"></i>&nbsp;定位</button>
                </div>
                <!-- 画布 -->
                <div id="LF-Turbo"></div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_pdf') }}
{{ partial('upyun_base64') }}
{{ partial('customNode/shipCard') }}
<script>
    var pdfDoc = null;
    var ship_types = {{ shipTypes | json_encode }};
    var check_list = {{ checkList | json_encode }};
    var ship_flow_lf = null;
    var sel_tab_id = 1
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }},
        },
        components: {
            draggable: window.vuedraggable
        },
        methods: {
            selectDrawing(drawing_index){
                let drawing_item = app.drawing_list[drawing_index];
                if (app.select_drawing_id == drawing_item.id){
                    return;
                }
                app.select_drawing_id = drawing_item.id;
                showSpin();
                app.drawing_images = [];
                pdfjsLib.getDocument(app.base_path + drawing_item.drawing_url).promise.then((pdfDoc_) => {
                    pdfDoc = pdfDoc_;
                    let page_cnt =  pdfDoc.numPages;
                    app.renderPage(1,page_cnt,drawing_item.drawing_name,drawing_item.uid,drawing_item.drawing_url);
                })
            },
            renderPage(num,page_cnt,name,uid,drawing_url) {
                if (num > page_cnt){
                    closeSpin();
                    return;
                }
                pdfDoc.getPage(num).then((page)=> {
                    const viewport = page.getViewport({ scale: 2 });
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
                    ctx.imageSmoothingEnabled = true;
                    const renderContext = {
                        canvasContext: ctx,
                        viewport: viewport
                    };
                    const renderTask = page.render(renderContext);
                    renderTask.promise.then(()=> {
                        let base64 =  canvas.toDataURL('image/jpeg',0.95);
                        app.drawing_images.push({
                            drawing_uid : uid,
                            page_num : num,
                            base64 : base64,
                            url_name :num + '_'+ name,
                            url : '',
                            drawing_url:drawing_url,
                            status : 0
                        });
                        num++;
                        app.renderPage(num,page_cnt,name,uid,drawing_url);
                    });
                });
            },
            closeBom(){
                this.bom_show = 0;
                this.select_card_id = '';
                this.select_drawing_id = '';
            },
            viewPdf(drawing_path){
                top.layer.open({
                    title:'查看',
                    type: 2,
                    area: ['100%', '100%'],
                    content: '{{ url('common/file/pdf?path=')}}' + drawing_path,
                    end:function(){}
                });
            },
            uploadDrawing(type){
                top.window.layer_result='';
                top.layer.open({
                    title:'上传图纸',
                    type: 2,
                    area: ['40em', '80%'],
                    content: '{{ url('mes/drawing/create/'~uid)}}' + '/' + type,
                    end:function(){
                        if(top.window.layer_result=='ok'){
                            toastr.success('操作完毕');
                            app.getDrawingList();
                        }
                    }
                });
            },
            tabClick(id) {
                this.tab_id = id;
            },
            bomTabClick(id) {
                this.bom_tab_id = id;
            },
            drawingDelete(drawing_item){
                var dlg = top.layer.confirm('确认要删除（' + drawing_item.code + '）吗?', function() {
                    top.layer.close(dlg);
                    showSpin();
                    $.post("{{ url('mes/product/drawingdelete') }}", {uid: drawing_item.uid}, function(rs) {
                        closeSpin(dlg);
                        if (rs.status == 'ok') {
                            toastr.success('操作完毕');
                            app.drawing_images = [];
                            app.getDrawingList();
                        }
                        else {
                            toastr.error('操作失败！' + rs.message);
                        }
                    })
                });
            },
            drawingSelect(drawing_item){
                if (app.bom_show == 1 && app.bom_tab_id == 1){
                    for(let item of app.bom_data.drawing_data){
                        if (item.drawing_uid == drawing_item.drawing_uid && item.page_num == drawing_item.page_num){
                            toastr.error('不能重复选择！');
                            return;
                        }
                    }
                    app.bom_data.drawing_data.push(JSON.parse(JSON.stringify(drawing_item)));
                }
            },
            bomDrawingDelete(drawing_index){
                app.bom_data.drawing_data.splice(drawing_index,1);
            },
            drawingSelectAll() {
                if (app.bom_show == 1 && app.bom_tab_id == 1) {
                    app.bom_data.drawing_data = JSON.parse(JSON.stringify(app.drawing_images));
                }
            },
            drawingDeleteAll() {
                app.bom_data.drawing_data = [];
            },
            getDrawingList(){
                showSpin();
                $.post("{{ url('mes/product/drawinglist/' ~ uid) }}", function (rs) {
                    closeSpin(null);
                    app.drawing_list = rs;
                })
            },
            getShipData(data){
                showSpin();
                $.post('{{ url('mes/product/getbom/'~uid) }}', data, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        app.bom_show = 1;
                        app.bom_data = rs.data;
                        app.bom_ext_data = rs.data.ext_data;
                        app.$nextTick(function() {
                            $('.bs-select').selectpicker('refresh');
                        });
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            deleteNode(node_id) {
                showSpin();
                $.post("{{ url('mes/product/deletebom') }}", {
                    uid: '{{ uid }}',
                    node_id: node_id
                }, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        ship_flow_lf.deleteNode(node_id);
                        this.closeBom();
                        saveFlow();
                    }
                    else {
                        toastr.error(rs.message);
                    }
                })
            },
            bomSave(e){
                e.preventDefault();
                if(!$('#form_bom').validate().form() )
                    return;
                if (app.bom_data.cnt == '' || app.bom_data.cnt == null || app.bom_data.cnt == 0){
                    toastr.error('请输入构成数量');
                    return;
                }
                if (app.bom_data.name == '' || app.bom_data.name == null){
                    toastr.error('请输入工艺名称');
                    return;
                }
                if (app.bom_data.work_type == '' || app.bom_data.work_type == null){
                    toastr.error('请输入工资类型');
                    return;
                }
                if (app.bom_data.plan_type == 1){
                    if (app.bom_data.produce_cnt == '' || app.bom_data.produce_cnt == null || app.bom_data.produce_cnt == 0){
                        toastr.error('请输入生产性基准');
                        return;
                    }
                }

                for (let check of app.bom_data.check_data){
                   for(let check_item of check.form_data){
                        if (check_item.type == 7){
                            if (check_item.standard_plus == '' || check_item.standard_minus == '' ){
                                toastr.error('请输入检验标准值');
                                return;
                            }
                        } else if (check_item.type == 8){
                            if (check_item.standard_val == '' || check_item.standard_plus == '' || check_item.standard_minus == '' ){
                                toastr.error('请输入检验标准值');
                                return;
                            }
                        }
                   }
                }
                this.bomSubmit();
            },
            bomSubmit(){
                showSpin();
                this.uploadBase64(0,()=>{
                    let data = {
                        uid:'{{ uid }}',
                        bom_uid:app.bom_data.uid,
                        ship_type_id : app.bom_data.ship_type_id,
                        name:app.bom_data.name,
                        cnt:app.bom_data.cnt,
                        produce_cnt:app.bom_data.produce_cnt,
                        produce_cost:app.bom_data.produce_cost,
                        work_type:app.bom_data.work_type,
                        one_cost:app.bom_data.one_cost,
                        is_outsourcing:app.bom_data.is_outsourcing,
                        remarks:app.bom_data.remarks,
                        drawing_id:app.bom_data.drawing_id,
                        drawing_data:encodeURI(JSON.stringify(app.bom_data.drawing_data)),
                        check_data:encodeURI(JSON.stringify(app.bom_data.check_data)).replace(/\+/g,'%2B'),
                        goods_data:encodeURI(JSON.stringify(app.bom_data.goods_data)).replace(/\+/g,'%2B'),
                        ext_data:encodeURI(JSON.stringify(app.bom_ext_data)).replace(/\+/g,'%2B')
                    }
                    $.post('{{ url('mes/product/savebom') }}', data ,  (rs) => {
                        if (rs.status == 'ok') {
                            let nodeModel = ship_flow_lf.getNodeModelById(data.bom_uid);
                            nodeModel.setProperty('name',data.name);
                            nodeModel.setProperty('cnt',data.cnt);
                            nodeModel.setProperty('produce_cnt',data.produce_cnt);
                            saveFlow();
                        }
                        else {
                            toastr.error(rs.message);
                        }
                    })
                });
            },
            uploadBase64(idx,cb){
                if (idx >= app.bom_data.drawing_data.length){
                    cb();
                    return;
                }
                if (app.bom_data.drawing_data[idx].url == ''){
                    upyunUploadBase64('product', app.bom_data.drawing_data[idx].base64).then((url) => {
                        app.bom_data.drawing_data[idx].base64 = '';
                        app.bom_data.drawing_data[idx].url = url;
                        idx++;
                        app.uploadBase64(idx, cb);
                    }).catch((e) => {
                        toastr.error('上传失败！' + e);
                    });
                } else {
                    idx++;
                    this.uploadBase64(idx,cb);
                }
            },
            drawingCommit(){
                showSpin();
                this.uploadBase64(0,()=>{
                    let data = {
                        uid:'{{ uid }}',
                        bom_uid:app.bom_data.uid,
                        drawing_data:encodeURI(JSON.stringify(app.bom_data.drawing_data))
                    }
                    $.post('{{ url('mes/product/savedrawing') }}', data ,  (rs) => {
                        closeSpin(null);
                        if (rs.status == 'ok') {
                            toastr.success('操作成功');
                            for(let drawing_item of app.bom_data.drawing_data){
                                drawing_item.status = 1;
                            }
                        }
                        else {
                            toastr.error(rs.message);
                        }
                    })
                });
            },
            addGoods() {
                top.window.goods_layer_result = '';
                top.window.goods_data = app.bom_data.goods_data;
                top.layer.open({
                    title: '选择物资',
                    type: 2,
                    area: ['100%', '100%'],
                    content: '{{url("purchase/goods/sel")}}',
                    end: function () {
                        if(top.window.goods_layer_result == 'ok'){
                            setTimeout(()=>{
                                app.bom_data.goods_data = top.window.goods_data;
                                app.$forceUpdate();
                            },100);
                        }
                    }
                });
            },
            addCheck(){
                if (this.select_check_id == ''){
                    return;
                }
                if (this.bom_data.check_data.some(item=>item.id == this.select_check_id)){
                    toastr.error('不能重复添加');
                    return;
                }
                for(let check_item of check_list){
                    if (check_item.id == this.select_check_id){
                        this.bom_data.check_data.push(JSON.parse(JSON.stringify(check_item)));
                        break;
                    }
                }
                this.select_check_id = '';
                app.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
            },
            save(e){
                e.preventDefault();
                this.tab_id = 1;
                setTimeout(()=>{
                    if(!$('#form').validate().form()){
                        return;
                    }
                    this.saveData(1);
                },100);
            },
            submit(e){
                e.preventDefault();
                this.tab_id = 1;
                setTimeout(()=>{
                    if( !$('#form').validate().form() ){
                        return;
                    }
                    var dlg = top.layer.confirm('确认提交吗?', () => {
                        top.layer.close(dlg);
                        this.saveData(2);
                    });
                },100);
            },
            saveData(type){
                showSpin();
                $.post("{{ url('mes/product/create/'~uid) }}", {
                    type : type,
                    goods_type_code : app.goods_type_text ? app.goods_type_text.split(' ')[0] : '',
                    name : app.name,
                    remarks : app.remarks,
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                    flow_data:encodeURI(JSON.stringify(ship_flow_lf.getGraphData())).replace(/\+/g,'%2B')
                }, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error(rs.message);
                    }
                })
            },
            showPopup: function() {
                $('#custom-popup').modal('show');
            },
            handleClose: function() {
              $('#custom-popup').modal('hide');
            },
        },
        watch: {
          'bom_data.work_type'(newVal, oldVal) {
            if (newVal != '2'){
                app.bom_data.one_cost = null;
            }
          }
        }
    });

    initUpLoaderPdf('{{ uid }}');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.getDrawingList();
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    function lfCenter() {
        if (ship_flow_lf != null) {
            ship_flow_lf.translateCenter();
        }
    }

    function saveFlow() {
        showSpin();
        $.post('{{ url('mes/product/flowsave/') }}' + app.uid, {
            flow_data:encodeURI(JSON.stringify(ship_flow_lf.getGraphData())).replace(/\+/g,'%2B')
        }, function (rs) {
            closeSpin(null);
            if (rs.status == 'ok') {
                toastr.success('保存成功');
            }
            else {
                toastr.error('工艺流程操作失败！' + rs.message);
            }
        })
    }

    var $tree = $('#tree');
    $(function() {
        LogicFlow.use(Menu);
        LogicFlow.use(DndPanel);
        LogicFlow.use(SelectionSelect);
        ship_flow_lf = new LogicFlow({
            grid: true,
            nodeTextEdit:false,
            edgeTextEdit:false,
            container:document.querySelector("#LF-Turbo")
        });
        ship_flow_lf.extension.dndPanel.setPatternItems(ship_types);

        document.querySelector('#searchShipType').addEventListener('input', (e) => {
            const filtered = ship_types.filter(item =>
                item.label.includes(e.target.value)
            );
            ship_flow_lf.extension.dndPanel.setPatternItems(filtered);
        });


        for (let ship_type of ship_types){
            ship_flow_lf.register(shipCard(ship_type.type));
        }
        ship_flow_lf.extension.menu.setMenuConfig({
            nodeMenu: [
                {
                    text: "删除",
                    callback(node) {
                        app.deleteNode(node.id);
                    }
                }
            ],
            edgeMenu:[
                {
                    text: "删除",
                    callback(node) {
                        ship_flow_lf.deleteEdge(node.id);
                    }
                }
            ]
        });
        ship_flow_lf.setTheme({
            baseEdge: {
                stroke: "#0052D9",
                strokeWidth: 2,
            },
        });
        ship_flow_lf.render({{ flowData }});

        ship_flow_lf.on("node:dnd-add", (data) => {
            let nodeModel = ship_flow_lf.getNodeModelById(data.data.id);
            let type_name = '';
            for (let ship_type of ship_types){
                if (ship_type.type == data.data.type){
                    type_name = ship_type.label;
                    break;
                }
            }
            nodeModel.setProperty('id',data.data.id);
            nodeModel.setProperty('type',data.data.type);
            nodeModel.setProperty('type_name',type_name);
            nodeModel.setProperty('name',type_name);
            nodeModel.setProperty('cnt',1);
            nodeModel.setProperty('produce_cnt','');
            nodeModel.setProperty('status',0);
        });
        ship_flow_lf.on("custom:ship-card-click", (data) => {
            if (app.select_card_id == data.id) {
                return;
            }
            app.select_card_id = data.id;
            app.getShipData(data);
        });
        $('.page-container-bg-solid').attr('style','overflow:hidden');
        $('.page-sidebar-closed-hide-logo').attr('style','overflow:hidden');

        // 存货档案
        $tree.jstree({
            "core": {
                "data": {{ tree_list | json_encode }},
                "check_callback": function (operation, node, parent, position, more) {
                    // 允许展开所有节点
                    if (operation === "open_node") {
                        return true; // 允许展开所有节点
                    }
                    // 只允许选择没有子节点的节点
                    if (operation === "select_node") {
                        return node.children.length === 0; // 只有没有子节点的节点可以选择
                    }
                    return true;
                }
            },
            "plugins": ["wholerow"]
        });

        // 正确的双击事件处理
        $tree.on("dblclick.jstree", function (e) {
            // 获取双击的节点
            var node = $tree.jstree("get_node", e.target);

            // 如果点击的不是节点元素（比如空白处），node可能为false
            if (!node || node.length === 0) return;

            // 只有当节点没有子节点时才允许选择
            if (node.children.length === 0) {
                app.goods_type_text = node.original.text;
                console.log(app.goods_type_text);
                // app.goods_type_id = node.original.id;
                $('#custom-popup').modal('hide');
            }
        });
    });

</script>
{{ partial('form_script') }}
<style>
    #LF-Turbo {
        width: 100%;
        height: 98vh;
    }

    .logic-flow-view {
        margin: 10px;
        position: relative;
    }

    .lf-dndpanel {
        top: 50px;
        max-height: 800px;
        overflow-y: auto;
    }
</style>