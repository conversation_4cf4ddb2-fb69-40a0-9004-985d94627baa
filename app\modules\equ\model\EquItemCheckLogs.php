<?php

namespace Envsan\Modules\Equ\Model;


use Envsan\Common\Model\BaseModel;
class EquItemCheckLogs extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $item_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $form_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_date;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $form_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $form_data_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'equ_item_check_logs';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquItemCheckLogs[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquItemCheckLogs
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
