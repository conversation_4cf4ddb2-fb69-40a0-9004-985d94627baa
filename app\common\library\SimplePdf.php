<?php
namespace Envsan\Common\Library;

use mPDF;

class SimplePdf extends Mpdf
{
    private $first_page;
    private $style = [];

    public function __construct($landscape=false, $paper_size=4){
        parent::__construct('zh', $landscape?"A$paper_size-L":"A$paper_size");

        $this->first_page = true;
        $this->setAutoTopMargin = 'stretch';
        $this->setAutoBottomMargin = 'pad';

        // 主字体
        $this->style['body'] = '11pt';

        // 标题
        $this->style['title'] = '20pt';

        // 线条样式
        $this->style['line'] = 'solid';
    }

    public function applyFontSize($part, $size)
    {
        $this->style[$part] = $size;
    }

    public function applyLineStyle($style)
    {
        $this->style['line'] = $style;
    }

    public function applyHeader($header, $sub='', $other='')
    {
        $style = '
			<style>
			body, div, td, span, h1, h2, h3, h4, h5, h6, p{
				font-family: msyh;
				font-size: '.$this->style['body'].';
			}
									
			.btable{
			    border-collapse:collapse;
			    border-spacing:0;
			    border-left:0.1mm '.$this->style['line'].' #000;
			    border-top:0.1mm '.$this->style['line'].' #000;
            }
            .btable td{
                border-right:0.1mm '.$this->style['line'].' #000;
                border-bottom:0.1mm '.$this->style['line'].' #000;
            }
			</style>';

        $this->WriteHTML($style,1);

        $ht = '';
        if( $sub=='' )
            $ht .= '<div align="center" style="border-bottom: 0.1mm '.$this->style['line'].' #000000; font-weight: bold; font-size:'.$this->style['title'].'; padding-bottom: 2mm">'.$header.'</div>';
        else{
            $ht .= '<div align="center" style="font-weight: bold; font-size:'.$this->style['title'].';">'.$header;
            $ht .= '<span align="center" style="font-weight: normal; '.$this->style['line'].' #000000; padding: 1.2mm">'.$sub.'</span>'.'</div>';
        }

        $ht.=$other;

        if( empty($other) )
            $this->SetHTMLHeader($ht);
        else
            $this->SetHTMLHeader($ht, '', true);
    }

    public function applyFooter($txt='')
    {
        if($txt=='')
            $this->SetFooter('<div align="center" style="font-size: '.$this->style['body'].';">打印时间 ：{DATE Y/m/j G:i} 页码：{PAGENO}/{nb}</div>');
        else
            $this->SetFooter($txt);
    }

    public function make($filename='')
    {
        if( empty($filename) )
            $filename = 'pdf';
        $this->Output( $filename.'-'.time().'.pdf', 'I');
    }

    public function save($filename)
    {
        $this->Output($filename, 'f');
    }


    public function newPage()
    {
        if( !$this->first_page )
            parent::AddPage();
        $this->first_page=false;
    }

}