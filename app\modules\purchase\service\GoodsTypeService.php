<?php

namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseGoodsType;
use Phalcon\Mvc\User\Component;
use stdClass;

class GoodsTypeService extends Component
{
    public function selectTree($keywords = [], $level = 0)
    {
        $list = [];
        $conditions = ['del_flag = 0 AND owner = ?0'];
        $bind = [0 => SessionData::ownerId()];

        if (!empty($keywords)) {
            $conditions[] = "code is null";
            // 添加多个 LIKE 条件
            foreach ($keywords as $index => $keyword) {
                $conditions[] = "code LIKE ?".($index + 1);
                $bind[$index + 1] = $keyword . '%';
            }
            $where = implode(' OR ', array_slice($conditions, 1)); // LIKE 条件之间用 OR
            $fullCondition = $conditions[0] . ' AND (' . $where . ')';
        } else {
            $fullCondition = $conditions[0];
        }
        $rows = PurchaseGoodsType::find([
            'conditions' => $fullCondition,
            'bind' => $bind,
            'order' => 'pid, id'
        ])->toArray();

        $row = array_shift($rows);

        $data = [];
        $data['id'] = $row['id'];
        $data['uid'] = $row['uid'];
        $data['text'] = $row['code'].' '.$row['name'];
        $data['state'] = [
            'opened' => true,
            'selected' => false
        ];
        $data['children'] = $this->buildChildren($row['id'], $rows, 1, $level);
        array_push($list, $data);
        return $list;
    }

    private function buildChildren($pid, &$rows, $current_level = 1, $max_level = 0)
    {
        // 如果设置了最大层级且当前层级已达到最大层级，则不再构建子节点
        if ($max_level > 0 && $current_level >= $max_level) {
            return [];
        }
        $child_list = [];
        foreach ($rows as $idx => $row)
        {
            if ($row['pid'] == $pid) {
                $data = [];
                $data['id'] = $row['id'];
                $data['uid'] = $row['uid'];
                $data['text'] = $row['code'].' '.$row['name'];
                $data['state'] = ['opened' => false];
                array_push($child_list, $data);
                unset($rows[$idx]);
            }
        }

        foreach ($child_list as &$child)
        {
            $child['children'] = $this->buildChildren($child['id'], $rows, $current_level + 1, $max_level);
        }
        return $child_list;
    }

    public function create()
    {
        $code = trim($this->request->getPost('code', 'tstring'));
        $name = trim($this->request->getPost('name', 'tstring'));
        $short_name = trim($this->request->getPost('short_name', 'tstring'));
        $pid = intval($this->request->getPost('pid', 'int'));
        if (empty($code) || empty($name) || empty($short_name) || empty($pid)){
            return ErrorHelper::WRONG_INPUT;
        }
        if (!CheckUtil::isInt($code)){
            return '编码必须为两位数字';
        }
        $code = intval($code);
        if ($code < 1 || $code > 99){
            return '编码必须为两位数字';
        } else if ($code < 10){
            $code = '0'.$code;
        }
        $prow = $this->selectById($pid);
        if ($prow == null)
            return '父分类不存在';
        $pid = $prow->id;
        $code = $prow->code . $code;
        $type_level = CvtUtil::emptyToInt($prow->type_level) + 1;
        //同一名称和类型CHECK
        $check = PurchaseGoodsType::findFirst(['(name = ?1 or code = ?2) and del_flag = 0 and owner = ?3', 'bind' => [1 => $name, 2 => $code , 3=> SessionData::ownerId()]]);
        if ($check != null) {
            return '物资类型已存在';
        }
        $goods = PurchaseGoods::findFirst(['del_flag = 0 and type_id = ?1', 'bind' => [1 => $pid]]);
        if (!empty($goods)){
            return '该分类下已存在物资';
        }
        $user = SessionData::user();
        $row = new  PurchaseGoodsType();
        $row->uid = UUID::make();
        $row->pid = $pid;
        $row->name = $name;
        $row->short_name = $short_name;
        $row->code = $code;
        $row->type_level = $type_level;
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        $row->owner = $user->owner;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function update($row)
    {
        $name = trim($this->request->getPost('name', 'tstring'));
        $short_name = trim($this->request->getPost('short_name', 'tstring'));

        if (empty($short_name) || empty($name)) {
            return ErrorHelper::WRONG_INPUT;
        }

        //同一名称和类型CHECK
        $check=  PurchaseGoodsType::findFirst(['name = ?1 and del_flag = 0', 'bind' => [1 => $name]]);
        if ($check != null && $check->id != $row->id) {
            return '物资类型已存在';
        }

        $row->name = $name;
        $row->short_name = $short_name;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function deleteByUid($uid)
    {
        $row =  PurchaseGoodsType::findFirst(['uid=?1', 'bind' => [1 => $uid]]);
        if ($row == null)
            return '记录不存在';
        $child =  PurchaseGoodsType::findFirst('del_flag = 0 and pid=' . $row->id);
        if ($child != null)
            return '该组织下面含有子分类';
        $goods = PurchaseGoods::find(['del_flag = 0 and type_id = ?1', 'bind' => [1 => $row->id]]);
        if (count($goods) > 0) {
            return '该分类下尚有未删除的物资';
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if ($row->save())
            return '';

        return ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return  PurchaseGoodsType::findFirst(['id=?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return  PurchaseGoodsType::findFirst(['uid=?1', 'bind' => [1 => $uid]]);
    }


    /**
     * 通过code 查询
     * @param $code
     * @return PurchaseGoodsType
     */
    public function selectByCode($code)
    {
        return PurchaseGoodsType::findFirst(['code = ?1 and del_flag = 0', 'bind' => [1 => $code]]);
    }

    public function gettypeList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.id,a.name')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a')
            ->where('a.del_flag = 0')
            ->orderBy('a.id');

        return $builder->getQuery()->execute();
    }

    public function gettypeIdx($uid){
        $type_row = PurchaseGoodsType::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.id,a.name')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a')
            ->where('a.del_flag = 0 and a.code like ?1',[1 => "$type_row->code%"])
            ->orderBy('a.id');

        return $builder->getQuery()->execute()->toArray();
    }

    public function takeCode(){
        $id = $this->request->getPost('id', 'tstring');
        $rtn = new \stdClass();
        if (empty($id)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $type_row = $this->selectById($id);
        if (empty($type_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $p_code = $type_row->code;
        $code = 1;
        $builder = $this->modelsManager->createBuilder()
            ->columns('max(a.code) as code')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a')
            ->where('a.del_flag = 0 and a.pid = ?1',[1 => $type_row->id])
            ->orderBy('a.id');
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0){
            $code = CvtUtil::emptyToInt(substr($rows[0]->code, -2)) + 1;
        }
        if ($code < 10){
            $code = '0'.$code;
        }
        $rtn->message = '';
        $rtn->data =  [
            'p_code' => $p_code,
            'code' => $code
        ];
        return $rtn;
    }

    /**
     * 获取指定 PurchaseGoodsType 记录的所有子 ID
     *
     * @param int $parentId 父记录的 ID
     * @return array 包含所有子 ID 的数组
     */
    public function getTypeIds($parentId)
    {
        // 先把父ID加入结果集
        $allIds = [$parentId];

        // 获取所有子ID
        $childIds = $this->getAllChildIdsOptimized($parentId);

        // 合并结果
        return array_merge($allIds, $childIds);
    }

    private function getAllChildIdsOptimized($parentId)
    {
        // 一次性查询所有记录
        $allTypes = PurchaseGoodsType::find([
            'del_flag = 0',
            'order' => 'id'
        ])->toArray();

        // 构建ID到子节点的映射
        $childrenMap = [];
        foreach ($allTypes as $type) {
            $pid = $type['pid'];
            if (!isset($childrenMap[$pid])) {
                $childrenMap[$pid] = [];
            }
            $childrenMap[$pid][] = $type['id'];
        }

        // 使用迭代而非递归来收集所有子ID
        $result = [];
        $queue = [$parentId];

        while (!empty($queue)) {
            $current = array_shift($queue);

            // 如果当前节点有子节点
            if (isset($childrenMap[$current])) {
                foreach ($childrenMap[$current] as $childId) {
                    $result[] = $childId;
                    $queue[] = $childId;  // 将子节点加入队列继续处理
                }
            }
        }

        return $result;
    }

}