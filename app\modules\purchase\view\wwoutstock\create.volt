{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content" >
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-detail" style="margin-bottom: 0; padding-top: 5px; padding-bottom: 5px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold">出库信息</span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center;justify-content: flex-end">
                        <button type="button" class="btn blue" @click="prevSubmit(1, $event)" style="margin-right: 20px;"><i class="fa fa-save"></i> 保存</button>
                        <button type="button" class="btn green" @click="prevSubmit(2, $event)"><i class="fa fa-check"></i> 提交</button>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="padding-top: 5px; padding-bottom: 5px;">
                            <div id="form_data" class="row">
                                {% if dispatcher.getActionName() == 'edit' %}
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">出库单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    
                                {% endif %}
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>外委加工单</label>
                                        <div class="col-sm-8">
                                            <select class="bs-select form-control" name="order_id" v-model="order_id"  data-live-search="true" data-size="8" required>
                                                <option value="">请选择外委加工单</option>
                                                {% for item in orderList %}
                                                    <option value="{{ item.id }}">{{ item.order_code }} / {{ item.supplier_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>出库日期</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" name="outstock_date" v-model="outstock_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item, index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="zh-table-box">
                        <div class="zh-table-box-content" style="height: 200px;">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>批次号</th>
                                    <th>产品名称</th>
                                    <th>产品规格</th>
                                    <th>外委工艺</th>
                                    <th style="width: 200px;">出库数量</th>
                                    <th>计价数量</th>
                                    <th>无税单价</th>
                                    <th>含税单价</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="detail_data.length == 0">
                                    <td colspan="10" style="text-align: center;">没有找到匹配的记录</td>
                                </tr>
                                <tr v-for="row, index in detail_data">
                                    <td v-text="index + 1"></td>
                                    <td v-text="row.code"></td>
                                    <td v-text="row.goods_name"></td>
                                    <td v-text="row.goods_model"></td>
                                    <td v-text="row.bom_name"></td>
                                    <td>
                                        <div class="input-group">
                                            <input type="number" class="form-control" :name="'quantity_' + index" v-model="row.quantity" maxlength="10" @input="onQuantityChange(row)">
                                            <span class="input-group-addon" v-text="row.goods_deputy_unit"></span>
                                        </div>
                                    </td>
                                    <td v-text="row.pricing_quantity + '(' + row.goods_unit + ')'"></td>
                                    <td v-text="row.price"></td>
                                    <td v-text="row.price_hs"></td>
                                    <td>
                                        <a href="javascript:;" @click='delList(index)' style="color: red;font-size: 20px">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="portlet light portlet-bom" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-yellow"></i>
                        <span class="caption-subject font-yellow bold">订单信息</span>
                    </div>
                </div>
                <div class="portlet-body" style="max-height: 400px">
                    <div class="search-page">
                        <div class="row table-search-bar">
                            <div class="col-sm-3">
                                <div class="input-group">
                                    <span class="input-group-addon">生产批次号</span>
                                    <input type="text" class="form-control" name="notice_code" v-model="notice_code">
                                </div>
                            </div>
                        </div>
                        <div class="zh-table-box">
                            <div class="zh-table-box-content">
                                <table class="table table-bordered table-big">
                                    <thead>
                                    <tr>
                                        <th>批次号</th>
                                        <th>物料名称</th>
                                        <th>规格型号</th>
                                        <th>外委工艺</th>
                                        <th>外委数量</th>
                                        <th>计价数量</th>
                                        <th>待出数量</th>
                                        <th>已出数量</th>
                                        <th>已入数量</th>
                                        <th>无税单价</th>
                                        <th>含税单价</th>
                                        <th>选择</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-if="bom_list.length == 0">
                                        <td colspan="11" style="text-align: center;" v-text="bom_msg"></td>
                                    </tr>
                                    <tr v-for="row, index in bom_list" v-if="row.show_flag == 1">
                                        <td v-text="row.code"></td>
                                        <td v-text="row.goods_name"></td>
                                        <td v-text="row.goods_model"></td>
                                        <td v-text="row.bom_name"></td>
                                        <td v-text="row.quantity + '(' + row.goods_deputy_unit + ')'"></td>
                                        <td v-text="row.purchase_quantity + '(' + row.goods_unit + ')'"></td>
                                        <td v-text="row.wait_out_cnt"></td>
                                        <td v-text="row.out_cnt"></td>
                                        <td v-text="row.in_cnt"></td>
                                        <td>
                                            <span v-text="row.price"></span>
                                        </td>
                                         <td>
                                            <span v-text="row.price_hs"></span>
                                        </td>
                                        <td>
                                            <a href="javascript:;" @click='addList(row)'>
                                                <i class="fa fa-arrow-up"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonOutstock }},
        methods: {
            // 计算采购为单位的数量
            onQuantityChange(row) {
                if (row.quantity !== '' && row.quantity !== null) {
                    const quantityValue = Number(row.quantity) || 0;
                    const rateValue = Number(row.conversion_rate) || 1;
                    row.pricing_quantity = (quantityValue * rateValue).toFixed(4);
                }
            },
            prevSubmit(type, e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                if (this.detail_data.length == 0) {
                    toastr.error('请添加出库明细');
                    return;
                }

                for (let i = 0; i < this.detail_data.length; i++) {
                    let row = this.detail_data[i];
                    if (!isDecimal(row.quantity) || this.safeNumber(row.quantity) <= 0) {
                        toastr.error('【序号' + (i + 1) + '】出库数量只能是大于0的数字。');
                        return;
                    }

                    if (this.safeNumber(row.quantity)  >  this.safeNumber(row.wait_out_cnt) ){
                        toastr.error('【序号' + (i + 1) + '】出库数量不能大于待出库数量');
                        return;
                    }
                }

                let param = JSON.parse(JSON.stringify(this.$data));
                param.type = type;
                param.ext_data = encodeURI(JSON.stringify(this.ext_data)).replace(/\+/g, '%2B');
                delete param.bom_list;

                if (type == 2) {
                    let dlg = top.layer.confirm('确认提交吗?', function() {
                        top.layer.close(dlg);
                        app.doSubmit(param);
                    });
                } else {
                    app.doSubmit(param);
                }
            },
            // 安全转换函数
            safeNumber: function(value, defaultValue = 0) {
                if (value === '' || value === null || value === undefined) {
                    return defaultValue;
                }
                const num = Number(value);
                return isNaN(num) ? defaultValue : num;
            },
            doSubmit(param) {
                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('purchase/wwoutstock/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('purchase/wwoutstock/create') }}';
                {% endif %}

                commonAjaxRequest(url, param, function(rs) {
                    // 成功回调
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                });
            },
            delFile: function (index) {
                this.files.splice(index);
            },
            addList(bom) {
                for (let item of this.detail_data) {
                    if (item.uid == bom.uid) {
                        toastr.error('已存在，不能重复添加');
                        return;
                    }
                }
                this.detail_data.push({...bom, quantity: bom.wait_out_cnt});
                this.onQuantityChange(this.detail_data[this.detail_data.length - 1]);
            },
            delList: function(idx) {
                this.detail_data.splice(idx, 1);
            },
            bomShow() {
                for(let item of this.bom_list) {
                    item.show_flag = 1;
                    if (this.notice_code) {
                        if (item.code.indexOf(this.notice_code) < 0) {
                            item.show_flag = 0;
                        }
                    }
                }
            },
            onOrderChange(val) {
                app.detail_data = []
                if (val) {
                    var url = '{{ url('purchase/wwoutstock/orderchange/') }}';
                    commonAjaxRequest(url, {
                        order_id: val
                    }, function (rs) {
                        app.bom_list = rs.data
                    })
                } else {
                    app.bom_list = []
                }
                
            }
        },
        watch: {
            notice_code: function() {
                this.bomShow();
            },
            order_id: function(val) {
                this.onOrderChange(val);
            },
        }
    });

    initUpLoaderPdf('purchase_wwoutstock');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key: getUuid(),
                url_name: rs.file_name,
                url: rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

    function initSize() {
        let portlet_height = $(window).height() - 35 - 27 - 8;

        // $(".portlet-bom .zh-table-box-content").height(portlet_height
        //     - $(".portlet-bom .portlet-title").outerHeight(true)
        //     - $(".portlet-bom .table-search-bar").outerHeight(true));

        // $(".portlet-detail .zh-table-box-content").height(portlet_height
        //     - $(".portlet-detail .portlet-title").outerHeight(true)
        //     - $(".portlet-detail #form").outerHeight(true));

    }

    $(function() {
        initSize();
    });
</script>
{{ partial('form_script') }}
<style>
    .table-search-bar {
        margin-bottom: 20px;
    }

    .zh-table-box table thead {
        z-index: 3;
    }
</style>