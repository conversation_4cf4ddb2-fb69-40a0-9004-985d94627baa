<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Sys\Model\SysDict;
use Envsan\Modules\Sys\Model\User;
use Phalcon\Mvc\User\Component;

class CheckService extends Component
{
    public function view($uid)
    {
        $builder = $this->selectAll();
        $builder->andWhere('t1.uid = ?99',[99=>$uid]);
        $rows = $builder->getQuery()->execute();

        $processedResults = [];
        foreach ($rows as $result) {
            // 转换为数组
            $item = $result->toArray();

            // 处理生产工人
            $workerIds = json_decode($item['production_workers'], true) ?: [];
            $workerNames = [];
            if (!empty($workerIds)) {
                $workers = User::find([
                    'id IN ({ids:array}) and del_flag = 0 and account_status = 0 and role_id = 2',
                    'bind' => ['ids' => $workerIds]
                ]);
                foreach ($workers as $worker) {
                    $workerNames[] = $worker->real_name;
                }
            }
            $item['production_workers_names'] = implode(', ', $workerNames);

            // 处理检查工具
            $toolIds = json_decode($item['check_tools'], true) ?: [];
            $toolNames = [];
            if (!empty($toolIds)) {
                $tools = SysDict::find([
                    'id IN ({ids:array}) and del_flag = 0 and dict_type = ?2',
                    'bind' => ['ids' => $toolIds, 2=> 'mes:check:tools']
                ]);
                foreach ($tools as $tool) {
                    $toolNames[] = $tool->name;
                }
            }
            $item['check_tools_names'] = implode(', ', $toolNames);

            $processedResults[] = $item;
        }

        return $processedResults;
    }


    public function getWorkers(){

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.real_name,
                t1.cost
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User', 't1')
            ->where('t1.del_flag = 0 and t1.account_status = 0 and t1.role_id = 2')
            ->orderBy('t1.id');
        $user_list = $builder->getQuery()->execute()->toArray();
        // 取得角色是工人：所有工人
        $worker_list = [];
        // 取得角色是工人：所有工人
        foreach ($user_list as $user) {
            $worker_list[] = [
                'id' => $user['id'],
                'name' => $user['real_name']
            ];
        }
        return $worker_list;
    }
    public function selectAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.bom_id,
                t1.notice_detail_id,
                t1.quality_template_name,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name,
                t5.name as bom_name,
                ifnull(t1.error_cnt,0) as error_cnt,
                t1.error_flag,
                t1.error_type,
                t1.error_remarks,
                t1.work_date,
                t1.create_time,
                t1.staff_name,
                t2.quantity,
                t6.name as customer_name,
                t1.check_data,
                t1.production_workers,
                t1.check_tools
           ')
            ->addFrom('Envsan\Modules\Mes\Model\MesCheckLogs', 't1')
            // 检验完成的数据，不用判断是否停产
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
            // 检验完成的数据，不用判断是否停产
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t4.customer_id = t6.id','t6')
            ->where('t1.del_flag = 0 and t1.owner = ?1',[1 => SessionData::ownerId()])
            ->orderBy('t1.create_time desc,t1.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

}