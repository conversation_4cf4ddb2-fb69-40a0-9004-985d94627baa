<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

class UserColumn extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $user_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $page_id;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=false)
     */
    public $page_code;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $column_data;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $condition_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_user_column';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return UserColumn[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return UserColumn
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
