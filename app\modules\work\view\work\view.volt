{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('css').addCss('static/pages/css/work/view.css') %}
{% do assets.collection('css').addCss('static/global/plugins/sweetalert/sweetalert.css') %}
{% do assets.collection('js').addJs('static/global/plugins/sweetalert/sweetalert.min.js') %}
<div id="app" class="page-content">
    <div class="work-body">
        <div class="row">
            <div class="col-md-3">
                <div id="flow" class="portlet light bordered" style="margin-bottom: 0;">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">流程信息</span>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="work-line">
                            <div class="work-line-content">
                                <div v-for="(item,index) in data.flow_list" class="item">
                                    <div class="title-border">
                                        <div v-if="item.type == 4" class="title" style="background-color: #3296FB">
                                            <i class="fa fa-home" style="margin-top: 6px"></i>
                                        </div>
                                        <div v-if="item.type == 5" class="title" style="background-color: #FF2B2B">
                                            <i class="fa fa-stop" style="margin-top: 6px"></i>
                                        </div>
                                        <div v-if="item.type == 6" class="title" style="background-color: #FF2B2B">
                                            <i class="fa fa-reply" style="margin-top: 6px"></i>
                                        </div>
                                        <div style="display: inline-block" v-if="item.type!=4 && item.type!=5 && item.type!=6" class="title" style="background-color: #3296FB">
                                            ${item.icon}
                                            <div v-if="item.type == 1" class="border" style="color:#4AB37E">
                                                <i class="fa fa-check"></i>
                                            </div>
                                            <div v-if="item.type == 3" class="border" style="color:#4AB37E">
                                                <i class="fa fa-volume-up"></i>
                                            </div>
                                            <div v-if="item.type != 1 && item.type != 3" class="border" style="color: #3296FB">
                                                <i class="fa fa-thumbs-o-up"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item-content">
                                        <div class="top">
                                            <div v-if="item.status == 0" style="color: #A2A2A2;">
                                                <span>${item.name}</span>
                                                <span>${item.val}</span>
                                            </div>
                                            <div v-if="item.status != 0" style="color: #000000;">
                                                <span>${item.name}</span>
                                                <span>${item.val}</span>
                                            </div>
                                            <span style="font-size: 15px;color: #898989;margin-top: -3px">${item.time}</span>
                                        </div>
                                        <div class="bottom" :style="{borderLeft:data.flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'}">
                                            <div v-if="item.text != ''" class="bottom-content">
                                                ${item.text}
                                            </div>
                                            <div style="display: flex;flex-direction: row;flex-wrap: wrap;margin-left: 35px;margin-top: 5px">
                                                <div v-for="(file,i) in item.files" style="width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px">
                                                    <a :href="img_path + file" data-lightbox="flowitem" title="查看图片">
                                                        <img :src="img_path + file" width="80px" height="80px" class="img-view"/>
                                                    </a>
                                                </div>
                                            </div>
                                            <div v-if="item.send != ''" class="bottom-send">
                                                ${item.send}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="work-line-content">
                                <div v-for="(item,idx) in data.anchor_list">
                                    <div style="display: flex;flex-direction: row;min-height: 60px;">
                                        <div style="width: 35%;display: flex;flex-direction: row;">
                                            <div style="height: 100%;position: relative">
                                                <div style="height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;">
                                                </div>
                                                <div v-if="idx == 0" style="height: 18px;position: absolute;top: 0;width: 12px;background-color: #FFF">
                                                </div>
                                                <div v-if="idx+1 == data.anchor_list.length" style="height: calc(100% - 22px);position: absolute;top: 22px;width: 12px;background-color: #FFF">
                                                </div>
                                                <div style="position: absolute;top: 15px;width: 15px;height: 15px;background-color: #aaaaaa;border-radius: 15px !important;">
                                                </div>
                                            </div>
                                            <div style="padding-top:5px;padding-left: 15px">
                                                <span style="font-weight: 400" v-text="item.name"></span>
                                                <div>
                                                    <span style="font-size: 12px;color: #898989" v-if="item.type==1">会签</span>
                                                    <span style="font-size: 12px;color: #898989" v-if="item.type==2">或签</span>
                                                    <span style="font-size: 12px;color: #898989" v-if="item.type==3">终签</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="user-row">
                                            <div v-for="(user,i) in item.list">
                                                <div v-if="item.type == 1" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #3598dc;color: #ffffff">${user.name}</div>
                                                <div v-if="item.type != 1" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #00adbc;color: #ffffff">${user.name}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="item.nlist.length > 0" style="display: flex;flex-direction: row;min-height: 60px;">
                                        <div style="width: 35%;display: flex;flex-direction: row;">
                                            <div style="height: 100%;position: relative">
                                                <div v-if="idx+1 != data.anchor_list.length" style="height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;">
                                                </div>
                                                <div v-if="idx+1 == data.anchor_list.length" style="height: 100%;width: 8px;">
                                                </div>
                                            </div>
                                            <div style="padding-top:5px;padding-left: 15px">
                                                <span style="font-weight: 400;color: #898989">抄送人</span>
                                            </div>
                                        </div>
                                        <div class="user-row">
                                            <div v-for="(user,i) in item.nlist" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #3598dc;color: #ffffff">${user.name}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-9" style="padding-left: 0;">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">业务信息</span>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="data-row">
                            <div class="data-col">
                                <div class="data-title">业务状态</div>：
                                <div class="data-content">
                                    <span v-text="data.data.anchor"></span>
                                    <span v-if="data.data.status == 15" class="label label-primary" style="margin-left: 5px;">审批中</span>
                                    <span v-if="data.data.handle_status == 1" class="label label-success" style="margin-left: 5px;">通过</span>
                                    <span v-if="data.data.handle_status == 2" class="label label-danger" style="margin-left: 5px;">拒绝</span>
                                    <span v-if="data.data.handle_status == 3" class="label label-danger" style="margin-left: 5px;">撤销</span>
                                </div>
                            </div>
                            <div class="data-col">
                                <div class="data-title">提交部门</div>：
                                <div class="data-content" v-text="data.data.group"></div>
                            </div>
                            <div class="data-col">
                                <div class="data-title">业务单号</div>：
                                <div class="data-content" v-text="data.data.code"></div>
                            </div>
                            <div class="data-col">
                                <div class="data-title">业务类型</div>：
                                <div class="data-content">
                                    <span v-text="data.data.type_name"></span>
                                    <span v-if="data.data.review_type == 1" class="label label-danger">(撤销审批)</span>
                                </div>
                            </div>
                            <div class="data-col" v-for="(item,i) in data.data.form_list" v-if="item.type != 99 && item.show == 1">
                                <div class="data-title">${ item.name }</div>：
                                <div class="data-content">
                                    <div v-if="item.type < 77">
                                        <span>${ item.value } ${ item.unit }</span>
                                    </div>
                                    <div v-if="item.type == 77" style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;max-width: 390px" v-for="file in item.value">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="img_path + file.url" target="_blank">
                                                <div v-text="file.name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                    <div v-if="item.type == 88" style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;max-width: 390px" v-for="file,file_index in item.data_list">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="img_path + file.url" target="_blank">
                                                <div  v-text="file.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                    <div v-if="item.type == 80">
                                        <div>
                                            <button type="button"  class="btn btn-outline blue" @click="viewProduct(item.value)">查看详情</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="data-col">
                                <div class="data-title">说明</div>：
                                <div class="data-content"  v-text="data.data.remarks"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-for="(item, i) in data.data.form_list" v-if="item.type == 99" class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-star font-yellow"></i>
                            <span class="caption-subject font-yellow bold uppercase" v-text="item.name"></span>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table v-if="item.data_list.length > 0" class="table table-bordered search-table">
                            <thead class="bg-blue">
                                <tr>
                                    <th v-for="(header, header_idx) in item.data_list[0]" v-if="header.show == 1" v-text="header.name"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(row, row_idx) in item.data_list">
                                    <td v-for="(col, col_idx) in row" v-if="col.show == 1">
                                        <div v-if="col.type < 77">
                                            <span v-text="col.value"></span>
                                            <span v-if="col.unit" v-text="col.unit" style="margin-left: 5px;"></span>
                                        </div>
                                        <div v-if="col.type == 80">
                                            <button type="button"  class="btn btn-outline blue" @click="viewProduct(col.value)">查看详情</button>
                                        </div>
                                        <div v-if="col.type == 88">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;max-width: 390px" v-for="file,file_index in col.data_list">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="img_path + file.url" target="_blank">
                                                        <div  v-text="file.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="work-footer to-right" v-if="data.data.auth > 0">
        <button type="button" class="btn blue btn-circle btn-outline" @click="addComment"><i class="fa fa-commenting-o"></i>&nbsp;添加评论</button>
        <button v-if="data.data.auth == 2" type="button" class="btn red" @click="doReject" style="margin-left: 30px;"><i class="fa fa-reply"></i> 驳回</button>
        <button v-if="data.data.auth == 2" type="button" class="btn green" @click="doPass"><i class="fa fa-share"></i> 通过</button>
        <button v-if="data.data.auth == 3" type="button" class="btn yellow" @click="read" style="margin-left: 30px;"><i class="fa fa-check"></i>&nbsp;已知晓</button>
        <button v-if="data.data.auth == 4" type="button" class="btn red" @click="pressing" style="margin-left: 30px;"><i class="fa fa-bullhorn"></i>&nbsp;催办</button>
        <button v-if="data.data.auth == 4" type="button" class="btn red" @click="cancel" style="margin-left: 30px;"><i class="fa fa-backward"></i>&nbsp;撤回</button>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            remarks : '',
            data: {{ jsonData }},
            img_path: '{{ base_path }}'
        },
        methods: {
            view: function (uid) {
                top.layer.open({
                    title: '查看',
                    type: 2,
                    area: ['100%', '100%'],
                    content: "{{ url('work/weight/view/') }}" + uid,
                });
            },
            openDetail: function(uid) {
                window.open("{{ url('work/data/view/') }}" + uid);
            },
            addComment: function() {
                top.window.layer_result2 = '';
                top.layer.open({
                    title: '添加评论',
                    type: 2,
                    resize: false,
                    area: ['40em', '85%'],
                    content: '{{ url('work/work/comment/') }}' + this.data.data.uid,
                    end: function() {
                        if (top.window.layer_result2 == 'ok') {
                            app.refresh();
                        }
                    }
                });
            },
            refresh: function() {
                showSpin();
                $.post('{{ url('work/work/refresh') }}', { uid: this.data.data.uid }, function (rs) {
                    closeSpin(null);
                    app.data = rs.data;
                });
            },
            viewDetail: function(r) {
                if (r.sort == 1 || r.sort == 4) {
                    top.layer.open({
                        title: '查看详情',
                        type: 2,
                        resize: false,
                        area: ['100%', '100%'],
                        content: '{{ url('mes/product/view/') }}' + r.id
                    });
                } else if (r.sort == 5){
                    top.layer.open({
                        title: '详情',
                        type: 2,
                        resize: false,
                        area: ['100%', '100%'],
                        content: '{{ url('mes/quality/view/') }}' + r.id
                    });
                }
            },
            read: function() {
                let dlg = top.layer.confirm('确定要已知晓吗？', function() {
                    top.layer.close(dlg);
                    showSpin();
                    $.post("{{ url('work/work/read') }}", { uid: app.data.data.uid }, function (rs) {
                        closeSpin();
                        if (rs.status == 'ok') {
                            if (rs.uid != null && rs.uid != ''){
                                app.data.data.uid = rs.uid;
                                app.refresh();
                            } else {
                                top.window.layer_result = 'ok';
                                top.layer.close(top.layer.getFrameIndex(window.name));
                            }
                        }
                        else {
                            toastr.error('操作失败！' + rs.message);
                        }
                    })
                });
            },
            know: function() {
                let dlg = top.layer.confirm('确定要已知晓吗？', function() {
                    top.layer.close(dlg);
                    showSpin();
                    $.post("{{ url('work/work/know') }}", { uid: app.data.data.uid }, function (rs) {
                        closeSpin();
                        if (rs.status == 'ok') {
                            top.window.layer_result = 'ok';
                            top.layer.close(top.layer.getFrameIndex(window.name));
                        }
                        else {
                            toastr.error('操作失败！' + rs.message);
                        }
                    })
                });
            },
            doReject: function() {
                top.window.layer_result2 = '';
                top.layer.open({
                    title: '拒绝',
                    type: 2,
                    resize: false,
                    area: ['40em', '40em'],
                    content: '{{ url('work/work/reject/') }}' + this.data.data.uid,
                    end: function() {
                        if (top.window.layer_result2 == 'ok') {
                            if (top.window.data_uid != null && top.window.data_uid != ''){
                                app.data.data.uid = top.window.data_uid;
                                app.refresh();
                            } else {
                                top.window.layer_result = 'ok';
                                top.layer.close(top.layer.getFrameIndex(window.name));
                            }
                        }
                    }
                });
            },
            doPass: function() {
                top.window.layer_result2 = '';
                top.layer.open({
                    title: '通过',
                    type: 2,
                    resize: false,
                    area: ['40em', '40em'],
                    content: '{{ url('work/work/pass/') }}' + this.data.data.uid,
                    end: function() {
                        if (top.window.layer_result2 == 'ok') {
                            if (top.window.data_uid != null && top.window.data_uid != ''){
                                app.data.data.uid = top.window.data_uid;
                                app.refresh();
                            } else {
                                top.window.layer_result = 'ok';
                                top.layer.close(top.layer.getFrameIndex(window.name));
                            }
                        }
                    }
                });
            },
            cancel:function (){
                swal({
                    title: "确认撤回审批吗？",
                    type: "input",
                    showCancelButton: true,
                    inputPlaceholder: "请输入撤回原因",
                    confirmButtonText: "确定",
                    cancelButtonText: "取消"
                }, function (isConfirm) {
                    if (!(isConfirm === false)) {
                        if (isConfirm == ''){
                            alertWarning('请输入撤回原因！');
                            return;
                        }
                        showSpin();
                        $.post("{{ url('work/work/cancel') }}", {uid : app.data.data.uid , remarks : isConfirm}, function (rs) {
                            closeSpin();
                            if (rs.status == 'ok') {
                                top.window.layer_result = 'ok';
                                top.layer.close(top.layer.getFrameIndex(window.name));
                            }
                            else {
                                toastr.error('操作失败！' + rs.message);
                            }
                        })
                    }
                });
            },
            pressing:function (){
                swal({
                    title: "确认提交催办吗？",
                    type: "input",
                    showCancelButton: true,
                    inputPlaceholder: "请输入催办原因",
                    confirmButtonText: "确定",
                    cancelButtonText: "取消"
                }, function (isConfirm) {
                    if (!(isConfirm === false)) {
                        if (isConfirm == ''){
                            alertWarning('请输入催办原因！');
                            return;
                        }
                        showSpin();
                        $.post("{{ url('work/work/pressing') }}", {uid : app.data.data.uid , remarks : isConfirm}, function (rs) {
                            closeSpin();
                            if (rs.status == 'ok') {
                                app.refresh();
                            }
                            else {
                                toastr.error('操作失败！' + rs.message);
                            }
                        })
                    }
                });
            },
            viewProduct(uid){
                top.layer.open({
                    title:'查看',
                    type: 2,
                    area: ['100%', '100%'],
                    content: '{{ url('mes/product/view/') }}' + uid,
                });
            }
        }
    });

    function setSize() {
        let h = $(window).height() - $(".tabbable-line").outerHeight(true) - 35 - $(".work-footer").outerHeight(true);
        $('.work-body').height(h);
        $("#flow").css('min-height', h);
    }

    $(function() {
        setSize();
    });
</script>
