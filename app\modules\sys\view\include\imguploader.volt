<script>
    function uploadImg(id) {
        $("#btn_select_" + id).find("label").click();
    }

    function initUpLoader(id, title, url, img_path, img_name) {
        var html = '<div id="uploader" class="uploader-one" style="float: left;height: 240px">';
        html += '<div id="' + id + '" class="img-body">';
        html += '<div class="uploader-default">';
        html += '<div><span title="上传图片" onclick="uploadImg(\'' + id + '\')">+</span></div>';
        html += '</div>';
        html += '<div class="uploader-wrap">';
        html += '<div class="uploader-table">';
        html += '<div class="uploader-table-cell">';
        html += '<a class="btn btn-default btn-circle btn-upload" href="javascript:void(0);" onclick="uploadImg(\'' + id + '\')" title="上传图片">';
        html += '<i class="fa fa-upload"></i>';
        html += '</a>';
        html += '<a id="btn_see" class="btn btn-default btn-circle" href="javascript:void(0);" data-lightbox="licences" data-title="' + title + '" title="查看图片">';
        html += '<i class="fa fa-search"></i>';
        html += '</a>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '<div id="btn_select_' + id + '" style="display: none;">选择图片</div>';
        html += '</div>';
        if (title != null) {
            html += '<div class="uploader-title">' + title + '</div>';
        }
        html += '</div>';
        $("#" + id + "_area").append(html);

        var file_name = '';
        if (img_name !== null && img_name !== '') {
            file_name = img_name;
            showImage(id, '/upload/' + img_name);
        }

        var uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/global/plugins/uploader/uploader.swf') }}',
            server: '',
            pick: '#btn_select_' + id,
            accept: {
                title: 'Images',
                extensions: 'jpg,jpeg,png',
                mimeTypes: 'image/jpg,image/jpeg,image/png'
            },
            fileSingleSizeLimit: 2 * 1024 * 1024,
            compress: false,
            resize: false,
            duplicate: true
        });

        uploader.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，图片格式只支持jpg, jpeg, png');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，图片大小不能超过2M');
            }
        });

        uploader.on('fileQueued', function(file) {
            showSpin();
            uploader.options.server = url;
            uploader.upload();
        });

        uploader.on('uploadSuccess', function(file, rs) {
            closeSpin(null);
            if (rs.status == 'ok') {
                showImage(id, '/upload/' + rs.file_name);
                file_name = rs.file_name;
            } else {
                toastr.error('操作失败！');
            }
        });

        uploader.on('uploadError', function(file) {
            closeSpin(null);
            toastr.error('操作失败！');
        });

        return function () {
            return file_name;
        };
    }

    function showImage(id, path) {
        $("#" + id).find(".file-item").remove();

        var $div_img = $(
            '<div class="file-item thumbnail">' +
            '<img src="' + path + '">' +
            '</div>'
        );

        $("#" + id).find(".uploader-default").remove();
        $("#" + id).find(".uploader-wrap").before($div_img);
        $("#" + id).find("#btn_see").attr("href", path);
    }
</script>