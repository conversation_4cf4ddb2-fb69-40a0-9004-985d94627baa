<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class MesNotice extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $plan_begin_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $plan_end_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $customer_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $files;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var double
     * @Column(type="double", length=6, nullable=true)
     */
    public $data_sort;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $detail_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_notice';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesNotice[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesNotice
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
