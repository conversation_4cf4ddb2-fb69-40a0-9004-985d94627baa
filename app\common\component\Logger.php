<?php
namespace Envsan\Common\Component;

class Logger extends \Phalcon\Mvc\User\Component
{
    private static $_instance;
    public static function instance(){
        if(!(self::$_instance instanceof self))
            self::$_instance = new self;
        return self::$_instance;
    }

    public static function debug()
    {
        $s = '';
        $vars = func_get_args();
        foreach($vars as $value)
            $s = $s.$value.',';
        Logger::instance()->log->log($s, \Phalcon\Logger::DEBUG);
    }

    public static function info()
    {
        $s = '';
        $vars = func_get_args();
        foreach($vars as $value)
            $s = $s.$value.',';
        Logger::instance()->log->log($s, \Phalcon\Logger::INFO);
    }

    public static function error()
    {
        $s = '';
        $vars = func_get_args();
        foreach($vars as $value)
            $s = $s.$value.',';
        Logger::instance()->log->log($s, \Phalcon\Logger::ERROR);
    }

    public static function notice()
    {
        $s = '';
        $vars = func_get_args();
        foreach($vars as $value)
            $s = $s.$value.',';
        Logger::instance()->log->log($s, \Phalcon\Logger::NOTICE);
    }

    public static function warn()
    {
        $s = '';
        $vars = func_get_args();
        foreach($vars as $value)
            $s = $s.$value.',';
        Logger::instance()->log->log($s, \Phalcon\Logger::WARNING);
    }
}