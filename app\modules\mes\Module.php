<?php
namespace Envsan\Modules\Mes;

use Phalcon\Config;
use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\View;
use Phalcon\Mvc\View\Engine\Php as PhpEngine;
use Phalcon\Mvc\ModuleDefinitionInterface;

class Module implements ModuleDefinitionInterface
{
    /**
     * Registers an autoloader related to the module
     *
     * @param DiInterface $di
     */
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'Envsan\Modules\Mes\Api'        => __DIR__ . '/api/',
            'Envsan\Modules\Mes\Controller' => __DIR__ . '/controller/',
            'Envsan\Modules\Mes\Model'      => __DIR__ . '/model/',
            'Envsan\Modules\Mes\Service'    => __DIR__ . '/service/',
            'Envsan\Modules\Mes\Util'    => __DIR__ . '/util/',
            'Envsan\Modules\Common\Service'     => APP_PATH . '/modules/common/service/',
            'Envsan\Modules\Common\Util'     => APP_PATH . '/modules/common/util/',
            'Envsan\Modules\Sys\Model'       => APP_PATH . '/modules/sys/model/',
            'Envsan\Modules\Sys\Service'     => APP_PATH . '/modules/sys/service/',
            'Envsan\Modules\Trade\Model'       => APP_PATH . '/modules/trade/model/',
            'Envsan\Modules\Trade\Service'     => APP_PATH . '/modules/trade/service/',
            'Envsan\Modules\Trade\Util'    => APP_PATH . '/modules/trade/util/',
            'Envsan\Modules\Purchase\Model'       => APP_PATH . '/modules/purchase/model/',
            'Envsan\Modules\Purchase\Service'     => APP_PATH . '/modules/purchase/service/',
            'Envsan\Modules\Purchase\Util'    => APP_PATH . '/modules/purchase/util/',
            'Envsan\Modules\Work\Service'      => APP_PATH . '/modules/work/service/',
            'Envsan\Modules\Work\Model'      => APP_PATH . '/modules/work/model/',
            'Envsan\Modules\Work\Util'    => APP_PATH . '/modules/work/util/',
            'Envsan\Modules\Equ\Model'       => APP_PATH . '/modules/equ/model/',
            'Envsan\Modules\Equ\Service'     => APP_PATH . '/modules/equ/service/',
            'Envsan\Modules\Equ\Util'     => APP_PATH . '/modules/equ/util/',
            'Envsan\Modules\Quality\Service'      => APP_PATH . '/modules/quality/service/',
            'Envsan\Modules\Quality\Model'      => APP_PATH . '/modules/quality/model/',
            'Envsan\Modules\Quality\Util'    => APP_PATH . '/modules/quality/util/',
            'Envsan\Modules\Printing\Service'      => APP_PATH . '/modules/printing/service/',
            'Envsan\Modules\Printing\Model'      => APP_PATH . '/modules/printing/model/',
            'Envsan\Modules\Printing\Util'    => APP_PATH . '/modules/printing/util/',
        ]);

        $loader->register();
    }

    /**
     * Registers services related to the module
     *
     * @param DiInterface $di
     */
    public function registerServices(DiInterface $di)
    {
        /**
         * Try to load local configuration
         */
        if (file_exists(__DIR__ . '/config/config.php')) {
            $override = new Config(include __DIR__ . '/config/config.php');;
            $config = $di->get('config');

            if ($config instanceof Config) {
                $config->merge($override);
            } else {
                $config = $override;
            }
        }

        /**
         * Setting up the view component
         */
        $di->setShared('view', function () {
            $view = new View();
            $view->setDI($this);
            $view->setViewsDir(__DIR__ . '/view/');
            $view->setPartialsDir( APP_PATH . '/modules/common/view/include/');
            $view->registerEngines([
                '.volt'  => 'voltShared',
                '.phtml' => PhpEngine::class
            ]);

            return $view;
        });


        $di->setShared('log', function() use($di){
            $config = $di->get('config');
            $logger = new \Phalcon\Logger\Adapter\File( $config->application->logDir.'mes-'.date("Ymd").'.log' );
            return $logger;
        });

        if (file_exists(__DIR__ . '/config/message.php')) {
            $di->setShared('msg', function () {
                return new Config(include __DIR__ . '/config/message.php');
            });
        }

        if (file_exists(APP_PATH . '/modules/common/config/upyun.php')) {
            $di->setShared('upyun', function () {
                return new Config(include APP_PATH . '/modules/common/config/upyun.php');
            });
        }
    }
}
