.zh-table-group {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.zh-table-box {
    flex: 1;
}

.zh-table-box-content {
    width: 100%;
    overflow: auto;
    background: #ffffff;
}

.zh-table-box table {
    width: max-content !important;
    max-width: max-content !important;
    min-width: 100%;
    border: 0 !important;
    margin-bottom: 0;
}

.zh-table-box th, .zh-table-box td {
    text-align: left;
    vertical-align: middle !important;
}

.zh-table-box table thead {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #3598DC !important;
    color: #ffffff;
}

.zh-table-box table tbody td {
    background-color: #ffffff;
}

.zh-table-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
}

.zh-left {
    display: flex;
    align-items: center;
}

.zh-right {
    display: flex;
    align-items: center;
}

.zh-right .page-list {
    min-width: 60px;
}