.main {
    width: 1920px;
    height: 1080px;
    background: url("../img/bg.jpg") no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.header {
    height: 207px;
    background: url("../img/header.png") no-repeat;
    background-size: 100% 100%;
}

.body {
    flex: 1;
    margin-top: -50px;
    padding: 0 20px;
    display: flex;
    position: relative;
}

.tools-box {
    width: 210px;
    height: 47px;
    background: url('../img/border_box.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: -80px;
    color: #FFFFFF;
}

.tools-box.weather-box {
    right: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #cfcfd8;
}
.tools-box.time-box {
    right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.tools-box.time-box .time {
    font-size: 22px;
    line-height: 22px;
}

.page-groups {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ccc;
    font-size: 20px;
    width: 200px;
    position: absolute;
    top: -80px;
    left: 50px;
    z-index: 999;
}

.page-groups .page-item {
    background: rgba(28, 146, 243, 0.5);
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    transition: all .3s;
}

.page-groups .page-item:hover {
    background: rgba(28, 146, 243, 0.8);
    color: #fff;
    position: relative;
}

.page-groups .page-item::before {
    position: absolute;
    display: block;
    content: '';
    border: 20px solid transparent;
    transition: all .3s;
}

.page-groups .page-item.prev::before {
    border-right-color: rgba(28, 146, 243, 0.5);
    left: -40px;
}
.page-groups .page-item:hover.prev::before {
    border-right-color: rgba(28, 146, 243, 0.8);
}

.page-groups .page-item.next::before {
    border-left-color: rgba(28, 146, 243, 0.5);
    right: -40px;
}
.page-groups .page-item:hover.next::before {
    border-left-color: rgba(28, 146, 243, 0.8);
}

.tools-bar .btn-box {
    position: absolute;
    top: -80px;
    left: 320px;
    z-index: 999;
    line-height: 40px;
}

.tools-bar .btn-box .btn {
    color: #ccc;
    transition: all .3s;
}

.tools-bar .btn-box .btn.blue {
    background-color: rgba(28, 146, 243, 0.5);
}
.tools-bar .btn-box .btn.blue:hover {
    background-color: rgba(28, 146, 243, 0.8);
}

.tools-bar .btn-box .btn.green {
    background-color: rgba(34, 255, 214, 0.5);
}
.tools-bar .btn-box .btn.green:hover {
    background-color: rgba(34, 255, 214, 0.6);
}