<script>
    function uploadImg(id) {
        $("#btn_select_" + id).find("label").click();
    }

    function initUpLoader(id, title, url, img_path, img_name, big_img) {
        var uploader_class = '';
        if (big_img) {
            uploader_class = ' uploader-one-custom';
        }

        var html = '<div id="uploader" class="uploader-one' + uploader_class + '">';
        html += '<div id="' + id + '" class="img-body">';
        html += '<div class="uploader-default">';
        html += '<div class="btn-plus"><span title="上传图片" onclick="uploadImg(\'' + id + '\')">+</span></div>';
        html += '</div>';
        html += '<div class="uploader-wrap">';
        html += '<div class="uploader-table">';
        html += '<div class="uploader-table-cell">';
        html += '<a class="btn btn-default btn-circle btn-upload" href="javascript:void(0);" onclick="uploadImg(\'' + id + '\')" title="上传图片">';
        html += '<i class="fa fa-upload"></i>';
        html += '</a>';
        html += '<a id="btn_see" class="btn btn-default btn-circle" href="javascript:void(0);" data-lightbox="licences_default" data-title="' + title + '" title="查看图片">';
        html += '<i class="fa fa-search"></i>';
        html += '</a>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '<div id="btn_select_' + id + '" style="display: none;">选择图片</div>';
        html += '</div>';
        html += '<div class="uploader-title">' + title + '</div>';
        $("#" + id + "_area").append(html);

        var img_type;
        switch (id) {
            case 'img_finance_stamp':
                img_type = 1;
                break;
            case 'img_check_stamp':
                img_type = 2;
                break;
            case 'img_weight_stamp':
                img_type = 3;
                break;
            default:
                img_type = 0;
        }

        var file_name = '';
        if (img_name !== null && img_name !== '') {
            file_name = img_name;
            showImage(id, img_path + img_name, big_img);
        }

        var uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/lib/uploader/uploader.swf') }}',
            server: '',
            pick: '#btn_select_' + id,
            accept: {
                title: 'Images',
                extensions: 'jpg,jpeg,png',
                mimeTypes: 'image/jpg,image/jpeg,image/png'
            },
            fileSingleSizeLimit: 5 * 1024 * 1024,
            compress: false,
            resize: false,
            duplicate: true
        });

        uploader.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，图片格式只支持jpg, jpeg, png');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，图片大小不能超过5M');
            }
        });

        uploader.on('fileQueued', function(file) {
            showSpin();
            uploader.options.server = url;
            uploader.options.formData = {
                img_type: img_type
            };
            uploader.upload();
        });

        uploader.on('uploadSuccess', function(file, rs) {
            closeSpin(null);
            if (rs.status == 'ok') {
                showImage(id, rs.path + rs.file_name, big_img);
                file_name = rs.file_name;
            } else {
                toastr.error('图片上传失败！' + rs.message);
            }
        });

        uploader.on('uploadError', function(file) {
            closeSpin(null);
            toastr.error('图片上传失败！');
        });

        return function () {
            return file_name;
        };
    }

    function showImage(id, path) {
        var $img_body = $("#" + id);
        $img_body.find(".file-item").remove();

        var img_style = ' style="height: ' + ($img_body.height() - 10) + 'px"';

        var $div_img = $(
            '<div class="file-item thumbnail">' +
            '<img src="' + path + '"' + img_style + '>' +
            '</div>'
        );

        var $btn_see = $img_body.find("#btn_see");

        $img_body.find(".uploader-default").remove();
        $img_body.find(".uploader-wrap").before($div_img);
        $btn_see.attr("href", path);
        $btn_see.attr("data-lightbox", "licences");
    }
</script>