{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-7">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">物料信息</span>
                    </div>
                </div>
                <div class="portlet-body" style="min-height: 700px">
                    <div class="row">
                        <div class="col-sm-4" style="padding-left: 0">
                            <div class="search-page">
                                <div class="search-bar bordered">
                                    <div>
                                        <div id="tree"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-8" style="padding-left: 0">
                            <div class="search-page">
                                <div class="row">
                                    <div class="col-sm-5">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">编码/名称：</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="goods" v-model="goods">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-5">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">供应商：</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="supplier" v-model="supplier">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        {% if acl.has('purchase:goods:create') %}
                                            <button type="button" class="btn yellow" onclick="create()">
                                                <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                                <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 15px">
                                    <thead>
                                    <tr>
                                        <th>编码</th>
                                        <th>名称</th>
                                        <th>规格型号</th>
                                        <th>存货代码</th>
                                        <th>库存</th>
                                        <th>供应商</th>
                                        <th>选择</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="row, index in goods_list" v-if="row.show == 1">
                                        <td><span v-text="row.code"></span></td>
                                        <td><span v-text="row.name"></span></td>
                                        <td><span v-text="row.model"></span></td>
                                        <td><span v-text="row.inventory_code"></span></td>
                                        <td><span v-text="row.stock_cnt + '('+row.deputy_unit+')'"></span></td>
                                        <td><span v-text="row.supplier_name"></span></td>
                                        <td>
                                            <a href="javascript:;" @click='addList(row)'>
                                                <i class="fa fa-arrow-right"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <div class="row" v-if="goods_list.length == 0" style="text-align: center;margin-top: 15px">
                                    <span>没有数据</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-5">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">原料/配件</span>
                    </div>
                    <div class="actions"></div>
                </div>
                <div class="portlet-body form">
                    <button type="button" class="btn red btn-outline" @click="clearDetail">
                        <i class="fa fa-times"></i>&nbsp;清空列表
                    </button>
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="min-height: 625px">
                            <div class="list-box">
                                <div class="list-header">
                                    <div class="list-row">
                                        <div style="width: 8%;">序号</div>
                                        <div style="width: 16%;">编码</div>
                                        <div style="width: 15%;">名称</div>
                                        <div style="width: 14%;">规格</div>
                                        <div style="width: 14%;">型号</div>
                                        <div style="width: 10%;">存货代码</div>
                                        <div style="width: 17%;">供应商</div>
                                        <div style="width: 6%;">操作</div>
                                    </div>
                                </div>
                                <div v-if="detail_data.length == 0" style="text-align: center;margin-top: 15px">
                                    <span>没有数据</span>
                                </div>
                                <div v-else class="list-body">
                                    <div v-for="row, index in detail_data" class="list-item" :class="select_idx == index ? 'tr-selected' : ''">
                                        <div class="list-row">
                                            <div style="width: 8%;" v-text="index + 1"></div>
                                            <div style="width: 16%;" v-text="row.code"></div>
                                            <div style="width: 15%;" v-text="row.name"></div>
                                            <div style="width: 14%;" v-text="row.model"></div>
                                            <div style="width: 10%;" v-text="row.inventory_code"></div>
                                            <div style="width: 17%;" v-text="row.supplier_name"></div>
                                            <div style="width: 6%;">
                                                <button type="button" class="btn red" style="margin-right: 10px;" @click='delList(index)'><i class="fa fa-times"></i></button>
                                            </div>
                                        </div>
                                        <div style="display: flex">
                                            <div style="width: 8%;">&nbsp;</div>
                                            <div style="width: 92%;">
                                                <div v-if="row.f_list.length == 0" class="input-group" style="width:100%">
                                                    <input  type="number" class="form-control" :name="'quantity' + index" v-model="row.quantity" placeholder="数量" number="true"  maxlength="10" required>
                                                    <span class="input-group-addon" v-text="row.deputy_unit"></span>
                                                </div>
                                                <div v-else  style="display: flex;width:100%;flex-wrap: wrap">
                                                    <div style="display: flex;flex-direction: row;">
                                                        <div v-for="formula_item,formula_idx in row.f_list">
                                                            <div v-if="formula_item.t == 1 || formula_item.t == 3 || formula_item.t == 4" style="height: 32px;line-height: 32px;font-size: 16px;">
                                                                <span v-text="formula_item.l"></span>
                                                            </div>
                                                            <div v-else>
                                                                <input @change="keyInputChange(row)" style="width: 150px" type="number" class="form-control" :name="'formula_item' + formula_idx" v-model="formula_item.v" :placeholder="formula_item.l" number="true"  maxlength="10">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="input-group" style="width: 200px">
                                                        <span class="input-group-addon">=</span>
                                                        <input type="number" class="form-control" :name="'quantity' + index" v-model="row.quantity" placeholder="数量" number="true" readonly maxlength="10" required>
                                                        <span class="input-group-addon" v-text="row.deputy_unit"></span>
                                                    </div>
                                                </div>
                                                <div style="padding-top: 5px">
                                                    <div class="input-group">
                                                        <span class="input-group-addon">备注</span>
                                                        <input type="text" class="form-control" :name="'remarks' + index" v-model="row.remarks" maxlength="100">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" @click="save" class="btn btn-primary">确定</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_excel') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {
            keyInputChange(row){
                let val = '';
                for(let item of row.f_list) {
                    val += item.v || 0;
                }
                try {
                    let value = eval(val);
                    row.quantity = Number(value.toFixed(4));
                } catch (e){
                    row.quantity = '';
                }
                return row.quantity;
            },
            save(e) {
                e.preventDefault();

                let item;
                for (let i = 0; i < this.detail_data.length; i++) {
                    item = this.detail_data[i];
                    if (!item.code) {
                        toastr.error('编码不能为空：序号' + (i + 1));
                        return;
                    }

                    if (!isDecimal(item.quantity)) {
                        toastr.error('数量只能是正数：序号' + (i + 1));
                        return;
                    }
                }

                top.window.goods_layer_result = 'ok';
                top.window.goods_data = app.detail_data;
                top.layer.close(top.layer.getFrameIndex(window.name));
            },
            addList(goods) {
                for(let item of this.detail_data){
                    if (item.id == goods.id){
                        toastr.error('已存在，不能重复添加');
                        return;
                    }
                }
                if (goods.formula_list != null){
                    goods.f_list = JSON.parse(goods.formula_list);
                } else {
                    goods.f_list = [];
                }
                this.detail_data.push({...goods,quantity:'',remarks:''})
            },
            delList: function(idx) {
                this.detail_data.splice(idx, 1);
            },
            goodsShow() {
                for(let item of this.goods_list){
                    item.show = 1;
                    if (this.supplier != ''){
                        if (item.supplier_name.indexOf(this.supplier) == -1){
                            item.show = 0;
                        }
                    }
                    if (this.goods != ''){
                        let name = item.code + item.name;
                        if (name.indexOf(this.goods) == -1){
                            item.show = 0;
                        }
                    }
                }
            },
            selectGoodsType(val){
                showSpin();
                $.post('{{ url('purchase/goodstype/change/') }}' + val,{
                    formula_flag:1
                } , function(rs) {
                    closeSpin();
                    app.supplier = '';
                    app.goods = '';
                    app.goods_list = rs.data;
                });
            },
            clearDetail() {
                let dlg = top.layer.confirm('确认清空列表吗？', function() {
                    top.layer.close(dlg);
                    app.detail_data = [];
                });
            },
            selectRow(idx) {
                this.select_idx = this.select_idx == idx ? -1 : idx;
            }
        },
        watch:{
            type_uid: function(val) {
                this.selectGoodsType(val);
            },
            supplier: function(val) {
                this.goodsShow();
            },
            goods: function(val) {
                this.goodsShow();
            }
        }
    });

    function create() {
        if (!app.type_uid) {
            alertWarning('请选择物资类型!');
            return;
        }
        top.window.layer_result='';
        top.layer.open({
            title:'新建',
            type: 2,
            area: makeArea('40em', '80%'),
            content: '{{ url('purchase/goods/create/') }}' + app.type_uid,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    app.selectGoodsType(app.type_uid);
                }
            }
        });
    }

    function downloadTemplate() {
        window.open('{{ download_url }}')
    }

    initUpLoaderExcel('{{ url('purchase/goods/import') }}');
    function fileQueued(file) {
        showSpin();
        uploader.upload();
    }
    function uploadSuccess(rs, file) {
        closeSpin();
        if (rs.status == 'ok') {
            if (app.detail_data.length == 0) {
                app.detail_data = rs.list;
            } else {
                let item, item_key, detail, detail_key;
                for (let i = 0; i < rs.list.length; i++) {
                    item = rs.list[i];
                    item_key = item.name + '_' + item.model;
                    for (let j = 0; j < app.detail_data.length; j++) {
                        detail = app.detail_data[j];
                        detail_key = detail.name  + '_' + detail.model;
                        if (item_key == detail_key) {
                            alertWarning('导入的文件中，第' + item.row_no + '行商品【' + item.name + '】已存在，不能重复添加');
                            return;
                        }
                    }
                }
                app.detail_data = app.detail_data.concat(rs.list);
            }
        } else {
            toastr.error("导入失败！" + rs.message);
            uploader.removeFile(file, true);
            uploader.reset();
        }
    }

    var $tree = $('#tree');

    $(document).ready(function () {
        $tree.jstree({
            "core": {"data": {{ jsonTree | json_encode}}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            if (app.select_idx === -1) {
                app.type_uid = data.node.original.uid;
                app.type_pid = data.node.parent;
            } else {
                let row = app.detail_data[app.select_idx];
                row.code = data.node.original.text;
                row.type_id = data.node.original.id;
            }
        });
        app.detail_data = JSON.parse(JSON.stringify(top.window.goods_data));
    });
</script>
<style>
    .list-item.tr-selected {
        background-color: #fcf8e3;
    }

    .list-header {
        border-bottom: 2px solid #eee;
    }

    .list-header .list-row > div {
        font-weight: bold;
    }

    .list-row {
        display: flex;
    }

    .list-row > div {
        padding: 3px 6px;
        display: flex;
        align-items: center;
    }

    .list-item {
        border-bottom: 1px solid #eee;
        padding: 6px 0;
    }
</style>