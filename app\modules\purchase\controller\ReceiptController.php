<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseReceipt;
use Envsan\Modules\Purchase\Service\PurchaseReceiptService;
use Envsan\Modules\Sys\Service\DictService;

/**
 * @name('采购到货')
 */
class ReceiptController extends SuperController
{
    private $page_id = 50;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseReceiptService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new PurchaseReceiptService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());  // 使用已经执行过的结果，不要重复调用
            return json_encode($ret);
        }
        $table = new TableService();
        $jrow = (new PurchaseReceipt())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['purchase_order_list'] = $s->getOrders();
//        $dictService = new DictService();
//        $jrow['business_types'] = $dictService->selectDetailAll('purchase:order:types');
        $jrow['apply_list'] = [];
        $type_code = 'CGDH' . date('ym');
        $jrow['re_code'] = $type_code;
        $jrow['code'] = $s->getReceiptCode($type_code);
        $this->view->jsonPurchaseReceipt = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'purchase:receipt:create'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseReceiptService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, CvtUtil::emptyToArray($row->ext_data));
        $jrow['purchase_order_list'] = $s->getOrders();
        $dictService = new DictService();
//        $jrow['business_types'] = $dictService->selectDetailAll('purchase:order:types');
        // 一览选中的到货数据
        $jrow['apply_list'] = CvtUtil::emptyToArray($jrow['detail_data']);
        // 拆分 receipt_code：前面部分给 re_code，后四位给 code
        $receipt_code = $jrow['receipt_code'];
        $code_length = strlen($receipt_code);
        $jrow['re_code'] = substr($receipt_code, 0, $code_length - 4);  // 前面部分
        $jrow['code'] = substr($receipt_code, -4);
        $this->view->jsonPurchaseReceipt = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('receipt/create');
    }

    /**
     * @acl({'link':'purchase:receipt:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseReceiptService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid, $type = 'view')
    {
        $s = new PurchaseReceiptService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        // 一览选中的到货数据
        $jrow['apply_list'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $this->view->jsonPurchaseReceipt = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->view_type = $type;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseReceiptService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function addapplyAction($type, $order_id, $ids = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseReceiptService();
            $builder = $s->selectPurchaseOrderDetail($order_id ,$ids);
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->ids = $ids;
        $this->view->order_id = $order_id;
    }

    /**
     * @name('审核')
     */
    public function approvalAction() {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseReceiptService();
            $ret = new JsonData();
            $ret->handleResult($s->approval());
            return json_encode($ret);
        }
    }

    /**
     * 审核通过的Action
     * @acl({'link':'purchase:receipt:approval'})
     */
    public function rejectAction() {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseReceiptService();
            $ret = new JsonData();
            $ret->handleResult($s->reject());
            return json_encode($ret);
        }
    }

}