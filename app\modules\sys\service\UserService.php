<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\ActiveUser;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Model\Res;
use Envsan\Modules\Sys\Model\Role;
use Envsan\Modules\Sys\Model\User;
use Phalcon\Acl\Adapter\Memory as AclAdapter;
use Phalcon\Acl\Resource as AclResource;
use Phalcon\Acl\Role as AclRole;
use Phalcon\Mvc\User\Component;

class UserService extends Component
{
    public function selectAll()
    {
        return User::find(['order'=>'id desc']);
    }

    public function selectDetailAll()
    {
        $real_name = $this->request->get('real_name', ['string', 'trim']);
        $empno = $this->request->get('empno', ['string', 'trim']);
        $login_name = $this->request->get('login_name', ['string', 'trim']);
        $mobile = $this->request->get('mobile', ['string', 'trim']);
        $group_name = $this->request->get('group_name', ['string', 'trim']);

        $builder = $this->modelsManager->createBuilder()
            ->columns('u.id,
                    u.uid,
                    u.empno,
                    u.login_name,
                    u.real_name,
                    u.gender,
                    u.account_status,
                    u.mobile,
                    u.type,
                    g.name as group_name,
                    r.name as role_name')
            ->addFrom('Envsan\Modules\Sys\Model\User', 'u')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'u.group_id=g.id and g.del_flag=0', 'g')
            ->leftJoin('Envsan\Modules\Sys\Model\Role', 'u.role_id=r.id', 'r')
            ->orderBy('u.id desc')
            ->where('u.del_flag=0');

        if(!SessionData::isSuper())
            $builder->andWhere('u.owner='.SessionData::ownerId());

        if (!empty($real_name)) {
            $builder->andWhere("u.real_name like ?1", array(1 => "%$real_name%"));
        }
        if (!empty($empno)) {
            $builder->andWhere("u.empno like ?2", array(2 => "%$empno%"));
        }
        if (!empty($login_name)) {
            $builder->andWhere("u.login_name like ?3", array(3 => "%$login_name%"));
        }
        if (!empty($mobile)) {
            $builder->andWhere("u.mobile like ?4", array(4 => "%$mobile%"));
        }
        if (!empty($group_name)) {
            $builder->andWhere("g.name like ?5", array(5 => "%$group_name%"));
        }

        ModelUtil::limitGroup('u.group_id', $builder);
        return $builder;
    }

    public function selectDetailOne($id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\User', 'u')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'u.group_id=g.id and g.del_flag=0', 'g')
            ->leftJoin('Envsan\Modules\Sys\Model\Role', 'u.role_id=r.id', 'r')
            ->columns('u.id,
                    u.uid,
                    u.empno,
                    u.login_name,
                    u.real_name,
                    u.email,
                    u.address,
                    u.create_date,
                    u.gender,
                    u.account_status,
                    u.mobile,
                    g.name as group_name,
                    r.name as role_name')
            ->where('u.del_flag=0 and u.id='.intval($id))
            ->limit(1);

        return $builder->getQuery()->execute();
    }

    public function selectUser($name, $pwd, $owner)
    {
        return User::findFirstDirect([
            'login_name=?1 and password=?2 and del_flag=0 and owner=?3',
            'bind'=>[1=>$name, 2=>$pwd, 3=>$owner]
        ]);
    }

    public function buildAcl($user)
    {
        $acl = new AclAdapter();
        $acl->setDefaultAction(\Phalcon\Acl::DENY);

        $res_group = 'permissions';
        $role = Role::findFirst('id='.$user->role_id);
        if($role!=null) {
            if ($role->identity == 'admin')
                $this->session->set('admin', true);

            $acl_role = new AclRole('role');
            $acl->addRole( $acl_role );

            $acl_res = new AclResource($res_group);
            foreach ($role->roleRes as $item) {
                $res = Res::findFirst('id=' . $item->res_id);
                if($res!=null){
                    $acl->addResource($acl_res, $res->identity);
                    $acl->allow('role', $res_group, $res->identity);
                }
            }
        }
        return $acl;
    }

    public function createActiveUser($user)
    {
        $row = ActiveUser::findFirst('user_id='.$user->id);
        if($row==null)
            $row = new ActiveUser();

        $row->user_id = $user->id;
        $row->session_id = $this->session->getId();
        $row->ip = $this->request->getClientAddress();
        $row->login_date = DateUtil::now();
        $row->owner = $user->owner;

        return $row->save();
    }

    public function selectActiveUsers()
    {
        $sql = 'select a.id, a.session_id, a.ip, a.login_date, u.empno, u.real_name, u.login_name from \Envsan\Modules\Sys\Model\ActiveUser as a 
                inner join \Envsan\Modules\Sys\Model\User as u on a.user_id=u.id';

        if(!SessionData::isSuper())
            $sql .= ' where a.owner='.SessionData::ownerId();

        return $this->modelsManager->executeQuery($sql);
    }

    // todo! sql注入
    public function selectActiveUsersByIds($ids)
    {
        $ids = trim($ids);
        $arr = array_filter(explode(',', $ids));

        $owner = SessionData::ownerId();
        $s = implode(',', $arr);
        $sql = 'select * from Envsan\Modules\Sys\Model\ActiveUser where id in ('.$s.') and owner='.$owner;
        return $this->modelsManager->executeQuery($sql);
    }

    public function create()
    {
        $row = new User();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $user = SessionData::user();
        // 进行owner检查
        $guid = $this->request->getPost('group_uid', 'string');
        $empno = $this->request->getPost('empno', 'string');
        $login_name = $this->request->getPost('login_name', 'string');
        $mobile = $this->request->getPost('mobile', 'string');
        $type = $this->request->getPost('type', 'string');
        $cost = $this->request->getPost('cost', 'string');
//        $auth_data_ids = $this->request->getPost('auth_data_ids', 'string');
//        $auth_data_names = $this->request->getPost('auth_data_names', 'string');
        $group = Group::findFirst(['uid=?1', 'bind'=>[1=>$guid]]);
        if($group==null || $group->owner!=$user->owner)
            return '组织不正确';

        $rs = new RoleService();
        $role = $rs->selectById($this->request->getPost('role_id', 'string'));
        if($role!=null && $role->identity=='admin')
            return '无法绑定系统管理员角色操作';

        if (!CheckUtil::is_empty($empno) && $this->isRepeatEmpno($empno, $row->id)) {
            return '该工号已存在，请更换工号';
        }

        if ($this->isRepeatLoginName($login_name, $row->id)) {
            return '该登录名已存在，请更换登录名';
        }

        if (!CheckUtil::is_empty($mobile) && $this->isRepeatMobile($mobile, $row->id)) {
            return '该手机号已存在，请更换手机号';
        }

        $now = DateUtil::now();

        $row->empno = CvtUtil::blankToNull($empno);
        $row->login_name = $login_name;
        $row->real_name = $this->request->getPost('real_name', 'string');
        $row->name_py = $this->request->getPost('name_py', 'string');
        $row->password = $this->request->getPost('password', 'string');
        $row->gender = intval($this->request->getPost('gender', 'int'));
        $row->role_id = intval($this->request->getPost('role_id', 'string'));
        $row->mobile = CvtUtil::blankToNull($mobile);
        $row->email = $this->request->getPost('email', 'string');
        $row->address = $this->request->getPost('address', 'string');
        $row->cost = CvtUtil::blankToNull($cost);

        if (empty($type)){
            $row->type = 0;
        } else {
            $row->type = 1;
        }
//        if (empty($auth_data_ids)){
//            $row->auth_data_ids = null;
//            $row->auth_data_names = null;
//        } else {
//            $row->auth_data_ids = $auth_data_ids;
//            $row->auth_data_names = $auth_data_names;
//        }
        $row->group_id = $group->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->account_status = $this->request->getPost('account_status', 'string');
        if ($act == 'create') {
            $row->owner = $user->owner;
            $row->create_date = $now;
            $row->uid = UUID::make();
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    private function isRepeatEmpno($empno, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\User')
            ->where('del_flag = 0')
            ->andWhere('owner = '.SessionData::ownerId())
            ->andWhere('empno = ?2', [2 => $empno]);

        if (!empty($id)) {
            $builder->andWhere('id <> ?1', [1 => $id]);
        }
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0) {
            return true;
        } else {
            return false;
        }
    }

    private function isRepeatLoginName($login_name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\User')
            ->where('del_flag = 0')
            ->andWhere('owner = '.SessionData::ownerId())
            ->andWhere('login_name = ?2', [2 => $login_name]);

        if (!empty($id)) {
            $builder->andWhere('id <> ?1', [1 => $id]);
        }
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0) {
            return true;
        } else {
            return false;
        }
    }

    private function isRepeatMobile($mobile, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Sys\Model\User')
            ->where('del_flag = 0')
            ->andWhere('owner = '.SessionData::ownerId())
            ->andWhere('mobile = ?2', [2 => $mobile]);

        if (!empty($id)) {
            $builder->andWhere('id <> ?1', [1 => $id]);
        }
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public function selectByUid($uid)
    {
        return User::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }

    public function selectById($id)
    {
        return User::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function deleteByUid($uid)
    {
        $row = User::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
        if($row!=null) {
            $rs = new RoleService();
            $role = $rs->selectById($row->role_id);
            if($role!=null && $role->identity=='admin')
                return '无法删除管理员';

            $user = SessionData::user();
            if($row->id==$user->id)
                return '无法删除自己';

            if($row->delete())
                return '';
        }
        return ErrorHelper::UNKOWN;
    }

    public function bindRole($user, $uid)
    {
        $rs = new RoleService();
        $user_role = $rs->selectById($user->role_id);
        if($user_role!=null && $user_role->identity=='admin')
            return '无法对管理员进行该操作';

        $role = $rs->selectByUid($uid);
        if($role!=null){
            $user->role_id = $role->id;
            return $user->save();
        }
        return false;
    }

    public function unbindRole($row)
    {
        $rs = new RoleService();
        $role = $rs->selectById($row->role_id);
        if($role==null)
            return '角色不存在';

        if($role->identity=='admin')
            return '无法解除管理员的角色';

        $row->role_id = 0;
        if($row->save())
            return '';

        return ErrorHelper::UNKOWN;
    }

    public function changepwd()
    {
        $old_pwd = $this->request->getPost('old_pwd', ['string', 'trim']);
        $pwd1 = $this->request->getPost('pwd1', ['string', 'trim']);
        $pwd2 = $this->request->getPost('pwd2', ['string', 'trim']);
        $user = $this->selectById(SessionData::user()->id);

        if($old_pwd!=$user->password)
            return '旧密码不正确';

        if(empty($pwd1))
            return '密码不能为空';

        if($pwd1!=$pwd2)
            return '两次输入的密码不一致';

        if($user!=null){
            $user->password = $pwd1;
            if($user->save())
                return '';
        }
        return ErrorHelper::UNKOWN;
    }

    public function saveDetail(User $user)
    {
        $user->email = $this->request->getPost('email', ['string', 'trim']);
        $user->mobile = $this->request->getPost('mobile', ['string', 'trim']);
        $user->address = $this->request->getPost('address', ['string', 'trim']);

        if($user->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function getWorkers(){

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.real_name,
                t1.cost
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User', 't1')
            ->where('t1.del_flag = 0 and t1.account_status = 0 and t1.role_id = 2')
            ->orderBy('t1.id');
        $user_list = $builder->getQuery()->execute()->toArray();
        // 取得角色是工人：所有工人
        $worker_list = [];
        // 取得角色是工人：所有工人
        foreach ($user_list as $user) {
            $worker_list[] = [
                'id' => $user['id'],
                'name' => $user['real_name']
            ];
        }
        return $worker_list;
    }
}