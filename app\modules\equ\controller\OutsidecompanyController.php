<?php
namespace Envsan\Modules\Equ\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Equ\Model\EquOutsideCompany;
use Envsan\Modules\Equ\Service\EquOutsideCompanyService;


/**
 * @name('外协供应商')
 */
class OutsidecompanyController extends SuperController
{
    private $page_id = 48;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new EquOutsideCompanyService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new EquOutsideCompanyService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = (new EquOutsideCompany())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $this->view->jsonEquOutsideCompany = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'equ:outsidecompany:create'})
     */
    public function editAction($uid = '')
    {
        $s = new EquOutsideCompanyService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $this->view->jsonEquOutsideCompany = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('outsidecompany/create');
    }

    /**
     * @acl({'link':'equ:outsidecompany:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new EquOutsideCompanyService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new EquOutsideCompanyService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->jsonEquOutsideCompany = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new EquOutsideCompanyService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}