<?php
namespace Envsan\Common\Data;

use Phalcon\Mvc\User\Component;

class SessionData extends Component
{
    private static $_instance;

    public static function instance(){
        if(!(self::$_instance instanceof self))
            self::$_instance = new self;
        return self::$_instance;
    }

    public static $_OWNER_KEY = 'owner';
    public static $_USER_KEY = 'user';

    public static $_JOB_KEY = 'job_openid';

    public static function isAdmin()
    {
        return SessionData::instance()->session->get('admin')===true;
    }

    public static function isSuper()
    {
        return SessionData::instance()->session->get('super')===true;
    }

    public static function user()
    {
        return SessionData::instance()->session->get(SessionData::$_USER_KEY);
    }

    public static function ownerId()
    {
        return SessionData::owner()->id;
    }

    public static function owner()
    {
        return SessionData::instance()->session->get(SessionData::$_OWNER_KEY);
    }

    public static function spvUser()
    {
        if(SessionData::instance()->session->has('spv_user'))
            return SessionData::instance()->session->get('spv_user');
        return null;
    }

    public static function spvOwner()
    {
        if(SessionData::instance()->session->has('spv_owner'))
            return SessionData::instance()->session->get('spv_owner');
        return null;
    }

    public static function spvGroup()
    {
        if(SessionData::instance()->session->has('spv_group_id'))
            return SessionData::instance()->session->get('spv_group_id');
        return null;
    }

    public static function groupId()
    {
        if(SessionData::instance()->session->has('group_id'))
            return SessionData::instance()->session->get('group_id');
        return 0;
    }

    public static function groupIds()
    {
        if( SessionData::instance()->session->has('group_ids'))
            return SessionData::instance()->session->get('group_ids');
        return null;
    }
}