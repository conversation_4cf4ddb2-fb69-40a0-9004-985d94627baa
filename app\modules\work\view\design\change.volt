{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="form-group">
                        <label>原审批人<span class="required"> * </span></label>
                        <div>
                            <select name="old_user_id" class="form-control bs-select" v-model="old_user_id" data-live-search="true" data-size="8" required>
                                <option value="">请选择</option>
                                {% for user in userList %}
                                    <option value="{{ user.id }}">{{ user.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>新审批人<span class="required"> * </span></label>
                        <div>
                            <select name="new_user_id" class="form-control bs-select" v-model="new_user_id" data-live-search="true" data-size="8" required>
                                <option value="">请选择</option>
                                {% for user in userList %}
                                    <option value="{{ user.id }}">{{ user.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>更换原因<span class="required"> * </span></label>
                        <div>
                            <textarea required class="form-control" rows="3" name="remarks" v-model="remarks" maxlength="100" style="resize: none;"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                var url = '{{ url('work/design/change') }}';
                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            }
        }
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>
