<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInspection;
use Envsan\Modules\Purchase\Model\PurchaseInspectionDetail;
use Phalcon\Mvc\User\Component;

class PurchaseInspectionService extends Component
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
            't1.inspection_code',
            't1.inspection_day',
            't1.inspection_date',
            't1.inspection_department',
            't1.department_name',
            't1.supplier_name',
            't1.receipt_code'
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInspection', 't1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectCheckAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                 t1.id,
                 t1.uid,
                 t1.goods_code,
                 t1.goods_name,
                 t1.goods_model,
                 t1.goods_unit,
                 round(t1.quantity,4) as quantity,
                 t1.status,
                 t1.status_name,
                 t1.check_code,
                 t1.check_status,
                 t1.check_images,
                 IF(t1.check_result_flag = 0, "OK", IF(t1.check_result_flag = 1, "NG", "未知")) as check_result,
                 t1.check_val,
                 t1.check_data,
                 t1.check_user_id,
                 t1.check_user_name,
                 t1.check_time,
                 t1.check_remarks,
                 t1.quality_template_id,
                 t2.inspection_code,
                 t2.receipt_code,
                 t2.department_name,
                 t2.supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInspectionDetail','t1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInspection','t1.inspection_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.check_status = 20')
            ->orderBy('t2.inspection_day desc , t1.id');

        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseInspection();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = $this->request->getPost('name', 'tstring');
        $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));

        if (empty($name))
            return ErrorHelper::WRONG_INPUT;

        if ($this->isRepeat($name, $row->id)) {
            return '名称重复';
        }

        $table = new TableService();
        $now = DateUtil::now();
        $user = SessionData::user();
        $ext_data = CvtUtil::emptyToArray($ext_data);

        $row->name = $name;
        $row->ext_data = CvtUtil::arrayToNull($ext_data);
        $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
        $row->update_date = $now;
        $row->update_by = $user->id;

        if ($act == 'create') {
            $row->uid = UUID::make();
            $row->create_date = $now;
            $row->create_by = $user->id;
            $row->del_flag = 0;
            $row->group_id = $user->group_id;
            $row->owner = $user->owner;
        }
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInspection')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return PurchaseInspection::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    /**
     * 通过uid取得报检单信息
     * @param $uid
     * @return PurchaseInspection
     */
    public function selectByUid($uid)
    {
        return PurchaseInspection::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    /**
     * 通过报检单id取得报检单物料明细信息
     * @param $inspection_id
     * @return PurchaseInspectionDetail[]
     */
    public function selectInspectionDetail($inspection_id)
    {
        return PurchaseInspectionDetail::find(['inspection_id = ?1', 'bind' => [1 => $inspection_id]]);
    }

    /**
     * 通过报检单id取得报检单物料明细信息
     * @param $inspection_id
     * @return PurchaseInspectionDetail[]
     */
    public function selectDetailByUid($uid)
    {
        return PurchaseInspectionDetail::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    /**
     * 通过检验单uid取得检验单物料明细信息
     * @param $uid
     * @return void
     */
    public function selectInspectionDetail4one($uid)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                 t1.id,
                 t1.uid,
                 t1.goods_id,
                 t1.goods_code,
                 t1.goods_name,
                 t1.goods_model,
                 t1.goods_unit,
                 t1.goods_deputy_unit,
                 round(t1.quantity,4) as quantity,
                 round(t1.purchase_quantity,4) as purchase_quantity,
                 t1.status,
                 t1.status_name,
                 t1.check_code,
                 t1.check_time,
                 t1.check_user_name,
                 t1.check_status,
                 t1.check_remarks,
                 t1.check_result_flag,
                 t1.quality_template_id,
                 t1.check_data,
                 t1.check_images,
                 t2.inspection_code,
                 t2.receipt_code,
                 t2.supplier_name,
                 t3.name as template_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInspectionDetail', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInspection', 't1.inspection_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Quality\Model\QualityTemplate', 't1.quality_template_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.uid = ?1 and t1.owner = ?2', [1 => $uid, 2 => SessionData::user()->owner]);
        return $builder->getQuery()->execute()->getFirst();
    }
}