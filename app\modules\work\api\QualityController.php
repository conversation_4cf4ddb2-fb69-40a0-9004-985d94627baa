<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\QualityService;

class QualityController extends SuperController
{
    public function initAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new QualityService();
            $ret = new JsonData();
            $rtn = $s->getData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function listAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new QualityService();
            $ret = new JsonData();
            $rtn = $s->getList();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function batchAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new QualityService();
            $ret = new JsonData();
            $ret->handleResult($s->getNoticeBatch());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function productsAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new QualityService();
            $ret = new JsonData();
            $batch_id = $this->request->getPost('batch_id', 'tstring');
            Logger::error($batch_id);
            $ret->handleResult($s->getProducts($batch_id));
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function saveAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new QualityService();
            $ret = new JsonData();
            $ret->handleResult($s->saveData());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function detailAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new QualityService();
            $ret = new JsonData();
            $rtn = $s->getDetail();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }
}
