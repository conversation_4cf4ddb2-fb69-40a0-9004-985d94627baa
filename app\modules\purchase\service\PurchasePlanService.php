<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Mes\Util\Constant;
use Phalcon\Mvc\User\Component;

class PurchasePlanService extends Component
{
    public function getPlanData(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t2.id,
                t2.uid,
                t2.name,
                t2.code,
                t2.spec,
                t2.model,
                t2.unit,
                t1.plan_begin_date,
                t1.goods_id,
                t1.quantity as plan_cnt,
                round(ifnull(t3.quantity,0),4) as stock_cnt
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseViewPlan', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods','t1.goods_id = t2.id','t2')
            ->leftJoin('<PERSON>vsan\Modules\Purchase\Model\PurchaseViewStock', 't1.goods_id = t3.goods_id', 't3')
            ->orderBy('t1.goods_id,t1.plan_begin_date');
        $rows = $builder->getQuery()->execute()->toArray();
        $days = [];
        foreach ($rows as $row){
           if (!in_array($row['plan_begin_date'],$day_list)){
               $days[] = $row['plan_begin_date'];
           }
        }
        asort($days);
        $day_list = [];
        foreach ($days as $day){
            $day_list[] = [
                'key' => strtotime($day),
                'date' => $day,
                'date_show' => date('m/d', strtotime($day)),
                'week' => Constant::$week_days[date('w', strtotime($day))],
                'plan_cnt' => '',
                'last_cnt' => ''
            ];
        }
        $goods_obj = [];
        foreach ($rows as $row){
            $key = '_' . $row['id'];
            if (!array_key_exists($key,$goods_obj)){
                $row['day_list'] = json_decode(json_encode($day_list,JSON_UNESCAPED_UNICODE) ,true);
                $row['last_cnt'] = CvtUtil::emptyToDouble($row['stock_cnt']);
                $goods_obj[$key] = $row;
            }
            foreach ($goods_obj[$key]['day_list'] as &$day){
                if ($day['date'] == $row['plan_begin_date']){
                    $day['plan_cnt'] = CvtUtil::emptyToDouble($row['plan_cnt']);
                    $day['last_cnt'] = round($goods_obj[$key]['last_cnt'] - CvtUtil::emptyToDouble($row['plan_cnt']) , 4);
                    $goods_obj[$key]['last_cnt'] = $day['last_cnt'];
                    break;
                }
            }
        }
        return [
            'goods_data' => $goods_obj,
            'day_list' => $day_list
        ];
    }
}