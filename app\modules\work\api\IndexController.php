<?php
namespace Envsan\Modules\Work\Api;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\IndexService;

/**
 * @noacl
 */
class IndexController extends SuperController
{
    public function indexAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new IndexService();
            return json_encode($s->init(), JSON_UNESCAPED_UNICODE);
        }
    }
}