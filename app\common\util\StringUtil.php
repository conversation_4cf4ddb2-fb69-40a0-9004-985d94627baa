<?php

namespace Envsan\Common\Util;

class StringUtil
{
    const arr = [
        '0', '1', '2', '3', '4', '5',
        '6', '7', '8', '9', 'A', 'B',
        'C', 'D', 'E', 'F', 'G', 'H',
        'I', 'J', 'K', 'L', 'M', 'N',
        'O', 'P', 'Q', 'R', 'S', 'T',
        'U', 'V', 'W', 'X', 'Y', 'Z'
    ];

    public static function makeRandom($length = 6)
    {
        $code = '';
        for ($i = 0; $i < $length; $i++) {
            $code .= self::arr[mt_rand(0, count(self::arr) - 1)];
        }
        return $code;
    }
}