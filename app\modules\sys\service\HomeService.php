<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Mes\Service\AssignmentService;
use Envsan\Modules\Mes\Service\CheckService;
use Envsan\Modules\Mes\Service\ErrorService;
use Envsan\Modules\Mes\Service\PlanService;
use Envsan\Modules\Mes\Service\ProductService;
use Envsan\Modules\Trade\Service\OrderService;
use Envsan\Modules\Work\Service\ReviewService;
use Envsan\Modules\Work\Service\WorkService;
use Phalcon\Mvc\User\Component;

class HomeService extends Component
{
    public function getReviewData()
    {
        $ret = [];
        $ws = new ReviewService();
        $ret['data_cnt'] = $ws->getCntData();

        $tz_page_list = [];
        $tz_list = [];
        $all_page_list = [];
        $all_cnt = 0;
        foreach (ConstantUtil::$home_tz_list as $idx => $tz_data)
        {
            if (!SessionData::isSuper() && !$this->acl->has($tz_data['identity'])) {
                continue;
            }
            $tz_data['url'] = $this->url->get($tz_data['url']);
            $tz_data['cnt'] = $this->getDataCnt($tz_data['id']);

            if ($tz_data['cnt'] == 0) {
                continue;
            }

            array_push($tz_list, $tz_data);
            if (($idx + 1) == 5) {
                array_push($tz_page_list, $tz_list);
                $tz_list = [];
            }
            if ($tz_data['cnt'] > 0){
                $all_cnt += $tz_data['cnt'];
                $all_page_list[] = $tz_data;
            }
        }
        if (count($tz_list) > 0) {
            array_push($tz_page_list, $tz_list);
        }

        $ret['all_cnt'] = $all_cnt;
        $ret['all_page_list'] = $all_page_list;
        $ret['tz_page_list'] = $tz_page_list;
        return $ret;
    }

    public function getChartData()
    {
        $ret = [];
        $ret['status'] = 'error';

        $date_begin = $this->request->getPost('date_begin', 'tstring');
        $date_end = $this->request->getPost('date_end', 'tstring');
        $tab_idx = $this->request->getPost('tab_idx', 'tstring');

        if (empty($date_begin) || empty($date_end) || !CheckUtil::isDate($date_begin) || !CheckUtil::isDate($date_end)) {
            $ret['message'] = '请选择有效的统计日期';
            return $ret;
        } else if ($date_begin > $date_end) {
            $ret['message'] = '统计开始日期不能晚于统计结束日期';
            return $ret;
        } else {
            $diff_day = (strtotime($date_end) - strtotime($date_begin)) / 24 / 3600;
            if ($diff_day > 30) {
                $ret['message'] = '统计天数不能超过29天';
                return $ret;
            }
        }

        switch ($tab_idx)
        {
            case 0:
                $data = $this->getDataYx();
                break;
            case 1:
                $data = $this->getDataScyy();
                break;
            case 2:
                $data = $this->getDataJs();
                break;
            case 3:
                $data = $this->getDataZl();
                break;
            case 4:
                $data = $this->getDataGy();
                break;
            default:
                break;
        }

        $ret['status'] = 'ok';
        $ret['data'] = $data;
        return $ret;
    }

    private function getLineData()
    {
        $date_begin = $this->request->getPost('date_begin', 'tstring');
        $date_end = $this->request->getPost('date_end', 'tstring');

        $xAxis_data = [];
        $date = $date_begin;
        $data = [];
        $data2 = [];
        $data3 = [];
        while ($date <= $date_end)
        {
            array_push($xAxis_data, $date);
            $date = date('Y-m-d', strtotime("$date +1 day"));
            array_push($data, 0);
            array_push($data2, 0);
            array_push($data3, 0);
        }

        return [
            'xAxis_data' => $xAxis_data,
            'data' => $data,
            'data2' => $data2,
            'data3' => $data3
        ];
    }

    private function getDataYx()
    {
        $type_idx = $this->request->getPost('type_idx', 'tstring');
        $default_data = $this->getLineData();
        $xAxis_data = $default_data['xAxis_data'];
        $data = $default_data['data'];
        $data2 = $default_data['data2'];
        $type_list = ['合同', '回款', '发货', '开票', '新开户', '赔偿'];
        if($type_idx == 1 || $type_idx == 3){
            $sum_list = [
                [
                    'type' => '国内',
                    'title' => '累计总金额',
                    'val' => 0,
                    'unit' => '元',
                    'color' => 'blue'
                ],
                [
                    'type' => '外销',
                    'title' => '累计总金额',
                    'val' => 0,
                    'unit' => '元',
                    'color' => 'orange'
                ]
            ];
        }else{
            $sum_list = [
                [
                    'type' => '国内',
                    'title' => '累计总重量',
                    'val' => 0,
                    'unit' => '吨',
                    'color' => 'blue'
                ],
                [
                    'type' => '外销',
                    'title' => '累计总重量',
                    'val' => 0,
                    'unit' => '吨',
                    'color' => 'orange'
                ]
            ];
        }

        $rows = [];
        $weight_in = 0;
        $weight_out = 0;
        foreach ($rows as $row)
        {
            $weight = CvtUtil::emptyToDouble($row->weight);
            $date_idx = array_search($row->date_sign, $xAxis_data);
            if ($date_idx !== false) {
                if ($row->area_type == 1) {
                    $weight_in += $weight;
                    $data[$date_idx] = $weight;
                } else {
                    $weight_out += $weight;
                    $data2[$date_idx] = $weight;
                }
            }
        }

        $sum_list[0]['val'] = round($weight_in, 3);
        $sum_list[1]['val'] = round($weight_out, 3);
        return [
            'type_list' => $type_list,
            'sum_list' => $sum_list,
            'chart_data' => [
                'xAxis_data' => $xAxis_data,
                'data' => $data,
                'data2' => $data2
            ]
        ];
    }

    private function getDataScyy()
    {
        $default_data = $this->getLineData();
        $xAxis_data = $default_data['xAxis_data'];
        $data = $default_data['data'];
        $data2 = $default_data['data2'];
        $type_list = ['毛坯浇注', '热处理', '粗加', '精加', '入库'];
        $sum_list = [
            [
                'type' => '',
                'title' => '累计总支数',
                'val' => 0,
                'unit' => '支',
                'color' => 'blue'
            ],
            [
                'type' => '',
                'title' => '累计总重量',
                'val' => 0,
                'unit' => '吨',
                'color' => 'orange'
            ]
        ];

        $rows = [];
        $sum_cnt = 0;
        $sum_weight = 0;
        foreach ($rows as $row)
        {
            $cnt = CvtUtil::emptyToInt($row->cnt);
            $weight = CvtUtil::emptyToDouble($row->weight);
            $date_idx = array_search($row->work_date, $xAxis_data);
            if ($date_idx !== false) {
                $sum_cnt += $cnt;
                $sum_weight += $weight;
                $data[$date_idx] = $cnt;
                $data2[$date_idx] = $weight;
            }
        }

        $sum_list[0]['val'] = round($sum_cnt);
        $sum_list[1]['val'] = round($sum_weight, 3);

        return [
            'type_list' => $type_list,
            'sum_list' => $sum_list,
            'chart_data' => [
                'xAxis_data' => $xAxis_data,
                'data' => $data,
                'data2' => $data2
            ]
        ];
    }

    private function getDataJs()
    {
        $default_data = $this->getLineData();
        $xAxis_data = $default_data['xAxis_data'];
        $data = $default_data['data'];
        $data2 = $default_data['data2'];
        $type_list = ['工步', '违规', '纯废品', '留改废品', '废品改制'];
        $sum_list = [
            [
                'type' => '',
                'title' => '累计总支数',
                'val' => 0,
                'unit' => '支',
                'color' => 'blue'
            ],
            [
                'type' => '',
                'title' => '累计总重量',
                'val' => 0,
                'unit' => '吨',
                'color' => 'orange'
            ]
        ];
        $legend = ['支数', '重量'];

        $type_idx = $this->request->getPost('type_idx', 'tstring');
        switch ($type_idx)
        {
            case 0:
                $sum_list = [
                    [
                        'type' => '',
                        'title' => '累计总份数',
                        'val' => 0,
                        'unit' => '份',
                        'color' => 'blue'
                    ]
                ];
                $data2 = [];
                $legend = ['份数'];
                break;
            case 1:
                $sum_list = [
                    [
                        'type' => '',
                        'title' => '累计总项次',
                        'val' => 0,
                        'unit' => '项',
                        'color' => 'blue'
                    ]
                ];
                $data2 = [];
                $legend = ['项次'];
                break;
            default:
                break;
        }

        return [
            'type_list' => $type_list,
            'sum_list' => $sum_list,
            'chart_data' => [
                'xAxis_data' => $xAxis_data,
                'legend' => $legend,
                'data' => $data,
                'data2' => $data2
            ]
        ];
    }

    private function getDataZl()
    {
        $default_data = $this->getLineData();
        $xAxis_data = $default_data['xAxis_data'];
        $data = $default_data['data'];
        $data2 = $default_data['data2'];
        $data3 = $default_data['data3'];
        $type_list = ['粗检', '热后检'];
        $sum_list = [
            [
                'type' => '',
                'title' => '累计总检验数',
                'val' => 0,
                'unit' => '支',
                'color' => 'white'
            ],
            [
                'type' => '',
                'title' => '累计总缺陷数',
                'val' => 0,
                'unit' => '支',
                'color' => 'blue'
            ],
            [
                'type' => '',
                'title' => '累计总废品数',
                'val' => 0,
                'unit' => '支',
                'color' => 'orange'
            ]
        ];

        $rows = [];
        $sum_cnt_jy = 0;
        foreach ($rows as $row)
        {
            $cnt = CvtUtil::emptyToInt($row->cnt);
            $date_idx = array_search($row->work_date, $xAxis_data);
            if ($date_idx !== false) {
                $sum_cnt_jy += $cnt;
                $data[$date_idx] = $cnt;
            }
        }
        $sum_list[0]['val'] = round($sum_cnt_jy);

        $rows = [];
        $sum_cnt_qx = 0;
        $sum_cnt_fp = 0;
        foreach ($rows as $row)
        {
            $cnt = CvtUtil::emptyToInt($row->cnt);
            $date_idx = array_search($row->work_date, $xAxis_data);
            if ($date_idx !== false) {
                if ($row->exec_type == 3) {
                    $sum_cnt_fp += $cnt;
                    $data3[$date_idx] = $cnt;
                } else {
                    $sum_cnt_qx += $cnt;
                    $data2[$date_idx] = $cnt;
                }
            }
        }
        $sum_list[1]['val'] = round($sum_cnt_qx);
        $sum_list[2]['val'] = round($sum_cnt_fp);

        return [
            'type_list' => $type_list,
            'sum_list' => $sum_list,
            'chart_data' => [
                'xAxis_data' => $xAxis_data,
                'data' => $data,
                'data2' => $data2,
                'data3' => $data3
            ]
        ];
    }

    private function getDataGy()
    {
        $default_data = $this->getLineData();
        $xAxis_data = $default_data['xAxis_data'];
        $data = $default_data['data'];
        $data2 = $default_data['data2'];
        $data3 = $default_data['data3'];
        $sum_list = [
            [
                'type' => '',
                'title' => '累计总合同数',
                'val' => 0,
                'unit' => '份',
                'color' => 'white'
            ],
            [
                'type' => '',
                'title' => '累计总合同金额',
                'val' => 0,
                'unit' => '万元',
                'color' => 'blue'
            ],
            [
                'type' => '',
                'title' => '累计总付款金额',
                'val' => 0,
                'unit' => '万元',
                'color' => 'orange'
            ]
        ];

        return [
            'type_list' => [],
            'sum_list' => $sum_list,
            'chart_data' => [
                'xAxis_data' => $xAxis_data,
                'data' => $data,
                'data2' => $data2,
                'data3' => $data3
            ]
        ];
    }

    public function getDataCnt($type){
        $cnt = 0;
        switch ($type)
        {
            case 1:
                //生产通知
                $s = new PlanService();
                $builder = $s->selectAll();
                $builder->andWhere('t1.status = 30');
                $cnt = count($builder->getQuery()->execute());
                break;
            case 2:
                //生产质量异常
                $s = new ErrorService();
                $builder = $s->selectAll(3);
                $cnt = count($builder->getQuery()->execute());
                break;
            case 3:
                //技术派工
                $s = new OrderService();
                $builder = $s->selectAll('assign');
                $cnt = count($builder->getQuery()->execute());
                break;
            case 4:
                //技术质量异常
                $s = new ErrorService();
                $builder = $s->selectAll(3);
                $cnt = count($builder->getQuery()->execute());
                break;
            case 5:
                //技术质量异常
                $s = new ErrorService();
                $builder = $s->selectAll(1);
                $cnt = count($builder->getQuery()->execute());
                break;
            case 6:
                //产品管理
                $s = new ProductService();
                $builder = $s->selectAll('list');
                $cnt = count($builder->getQuery()->execute());
                break;
            case 7:
                //质量异常复检
                $s = new ErrorService();
                $builder = $s->selectAll(2);
                $cnt = count($builder->getQuery()->execute());
                break;
            case 8:
                //待质检查询
                $s = new CheckService();
                $builder = $s->selectWaitAll();
                $cnt = count($builder->getQuery()->execute());
                break;
            default:
                break;
        }
        return $cnt;
    }
}