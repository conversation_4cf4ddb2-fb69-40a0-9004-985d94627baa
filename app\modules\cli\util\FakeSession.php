<?php
namespace Envsan\Modules\Cli\Util;

use Envsan\Common\Data\SessionData;
use Envsan\Modules\Sys\Model\Owner;
use Envsan\Modules\Sys\Model\User;

class FakeSession
{
    public function get($key)
    {
        return $_SESSION[$key];
    }

    public function has($key)
    {
        die('error!not emplemented!');
    }

    // 模拟session，使model中的check正常
    public static function createSession($ownerId, $userId)
    {
        $owner = Owner::findFirst('id='.intval($ownerId));
        $_SESSION['owner'] = $owner;
        $_SESSION['super'] = false;

        SessionData::instance()->session = new FakeSession();

        $user = User::findFirstDirect('id='.intval($userId));
        $_SESSION['user'] = $user;
    }
}