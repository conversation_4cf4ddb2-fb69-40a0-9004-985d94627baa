<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Work\Model\WorkRole;
use Envsan\Modules\Work\Util\Constant;
use Phalcon\Mvc\User\Component;

class RoleService extends Component
{
    public function selectAll($find='')
    {
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                r.id,
                r.uid,
                r.name,
                r.update_date,
                g.name as group_name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkRole', 'r')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'r.group_id = g.id', 'g')
            ->where("r.del_flag = 0 and r.owner = ?1",[1=>$user->owner])
            ->orderBy('r.id desc');
        if(!empty($find)){
            $builder->andWhere("r.name like ?2", [2 => "%$find%"]);
        }
        return $builder;
    }

    public function selectUserAll($find='')
    {
        $group_id = $this->request->get('group_id', 'string');
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                r.uid,
                r.real_name,
                w.name as role_name,
                g.name as group_name
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User', 'r')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'r.group_id = g.id', 'g')
            ->leftJoin('Envsan\Modules\Work\Model\WorkRole', 'r.work_role_id = w.id', 'w')
            ->where("r.del_flag = 0 and r.owner = ?1",[1=>$user->owner])
            ->orderBy('r.id desc');
        if(!empty($find)){
            $builder->andWhere("r.real_name like ?2", [2 => "%$find%"]);
        }
        if(!empty($group_id)){
            $builder->andWhere("r.group_id = ?3", [3 => $group_id]);
        }
        return $builder;
    }

    public function create()
    {
        $row = new WorkRole();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = trim($this->request->getPost('name', 'string'));
        if (empty($name))
            return ErrorHelper::WRONG_INPUT;

        $user = SessionData::user();
        $row->name = $name;
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        $row->group_id = $user->group_id;

        if ($act == 'create') {
            $row->owner = $user->owner;
            $row->uid = UUID::make();
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectByUid($uid)
    {
        return WorkRole::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }

    public function selectById($id)
    {
        return WorkRole::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function deleteByUid($uid)
    {
        $row = WorkRole::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
        if($row==null)
            return ErrorHelper::WRONG_ID;

        if(User::count('del_flag = 0 and work_role_id='.$row->id)>0)
            return '有用户绑定到该角色上，无法删除，请先解除该所属用户的角色！';

        if($row->delete())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function saveAssign($row){
        $list = $this->request->getPost('list');
        if (empty($list))
            return ErrorHelper::WRONG_INPUT;
        $row->type_ids = json_encode($list,JSON_UNESCAPED_UNICODE);
        $user = SessionData::user();
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function saveRole($row){
        $role_id = $this->request->getPost('role_id');
        if (empty($role_id))
            return ErrorHelper::WRONG_INPUT;
        $row->work_role_id = $role_id;
        $user = SessionData::user();
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function getAssign($type_ids)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                0 as sel,
                t1.name,
                t1.status,
                t3.name as group_name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkDesign','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\Group','t1.group_id = t3.id','t3')
            ->where('t1.del_flag = 0 and doc_type is null and t1.status = 1 and t1.owner = ?1',[1=>SessionData::ownerId()])
            ->orderBy('t1.group_id,t1.id desc');
        $gs = new GroupService();
        $user = SessionData::user();
        $ids = $gs->selectGroupIds($user->group_id);
        $builder->inWhere('t1.create_group_id', $ids);
        $rows = $builder->getQuery()->execute()->toArray();
        $types = [];
        if (!empty($type_ids)){
            $types = json_decode($type_ids,true);
        }
        foreach ($rows as &$row){
            if (in_array($row['id'], $types)) {
                $row['sel'] = 1;
            }
        }
        return $rows;
    }

    public function getRoleList()
    {
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkRole','t1')
            ->where('t1.del_flag = 0 and t1.owner = ?1',[1=>$user->owner])
            ->orderBy('t1.id desc');
        return  $builder->getQuery()->execute();
    }
}