<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Common\Base\ApiController;
use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DomainUtil;
use Envsan\Modules\Sys\Model\Owner;

class SuperController extends ApiController
{
    public function beforeExecuteRoute(\Phalcon\Mvc\Dispatcher $dispatcher)
    {
        // 设置所有的输出json格式
        $this->setJsonResponse();
        // 允许跨域
        header('Access-Control-Allow-Origin:*');
        // 允许客户端上传SID作为session id，并添加自定义标志头
        header('Access-Control-Allow-Headers:SID, AUTH, Content-Type, X-Custom-Flag');

        // http会发送options查询各种支持的协议，die即可
        if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'OPTIONS')
            die();

        // 检查自定义标志
        $customFlag = $this->request->getHeader('X-Custom-Flag');
        if ($customFlag == 'noapi') {
            // 当标志为 noapi 时，调用父类的 beforeExecuteRoute 方法
            return parent::beforeExecuteRoute($dispatcher);
        } else {
            // 不使用cookie
            ini_set('session.use_cookies', '0');

            SessionData::$_USER_KEY = 'work_user';
            SessionData::$_OWNER_KEY = 'work_owner';

            // 设置默认的owner
            if (!$this->session->has(SessionData::$_OWNER_KEY)) {
                if ($this->config->developMode) {
                    $owner = Owner::findFirst(['id=?1', 'bind' => [1 => $this->config->developOwner]]);
                    if($owner == null)
                        die(ErrorHelper::ROW_NOTEXIST);
                } else {
                    $owner = DomainUtil::checkOwner(true);
                }

                $this->session->set(SessionData::$_OWNER_KEY, $owner);
            }

            // 处理json体请求 - 使用更安全的方式
            $this->handleRequestPayload();

            $module = strtolower($this->router->getModuleName());
            $controller = strtolower($dispatcher->getControllerName());
            $action = strtolower($dispatcher->getActionName());
            $controllerClass = $dispatcher->getControllerClass();

            // class级别的noacl
            $collection = $this->annotations->get($controllerClass)->getClassAnnotations();
            if ($collection != false && $collection->has('noacl'))
                return true;

            $annotations = $this->annotations->getMethod($controllerClass, $dispatcher->getActiveMethod());
            if ($annotations->has('noacl'))
                return true;

            if ($this->session->has(SessionData::$_USER_KEY)) {
//            if ($annotations->has('skipacl') || ($collection != false && $collection->has('skipacl')))
//                return true;
//
//            $acl_link = '';
//            if ($annotations->has('acl')) {
//                $acl = $annotations->get('acl');
//                $arr = $acl->getArgument(0);
//                if (!empty($arr))
//                    $acl_link = $arr['link'];
//            }
//
//            if (!$this->acl->has("$module:$controller:$action")) {
//                if ($acl_link != '' && $this->acl->has($acl_link))
//                    return true;
//
//                $this->make4013();
//                return false;
//            }

                return true;
            }

            $this->make4011();
            return false;
        }

    }

    /**
     * 重写父类的 handleRequestPayload 方法，提供更安全的JSON处理
     */
    public function handleRequestPayload()
    {
        $contentType = strtolower($this->request->getHeader('CONTENT_TYPE'));
        Logger::error('Work API Content-Type: ' . $contentType);
        
        switch ($contentType) {
            case 'application/json':
            case 'application/json;charset=utf-8':
                $rawBody = $this->request->getRawBody();
                Logger::error('Work API Raw body length: ' . strlen($rawBody));
                Logger::error('Work API Raw body: ' . substr($rawBody, 0, 500));
                
                if (!empty($rawBody)) {
                    $jsonRawBody = $this->request->getJsonRawBody(true);
                    if ($jsonRawBody !== null) {
                        $_POST = $jsonRawBody;
                        Logger::error('Work API JSON parsed successfully');
                    } else {
                        // JSON 解析失败时记录日志但不终止执行
                        Logger::error('Work API JSON parsing failed with Phalcon method');
                        // 尝试手动解析
                        $decoded = json_decode($rawBody, true);
                        if ($decoded !== null) {
                            $_POST = $decoded;
                            Logger::error('Work API JSON parsed successfully with json_decode');
                        } else {
                            Logger::error('Work API JSON decode failed: ' . json_last_error_msg());
                            // 如果JSON解析完全失败，不抛出错误，让程序继续执行
                            Logger::error('Work API Continuing execution despite JSON parse failure');
                        }
                    }
                }
                break;
            default:
                Logger::error('Work API Non-JSON content type, using default POST handling');
                break;
        }
    }

}