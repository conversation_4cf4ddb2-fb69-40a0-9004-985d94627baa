<?php

namespace Envsan\Modules\Screen\Api;

use Envsan\Modules\Screen\Service\IndexService;

class IndexController extends SuperController
{
    public function indexAction()
    {
        $this->redirect($this->config->screen->folder_name.'#/index');
    }

    public function pageAction()
    {
        if ($this->request->isPost()) {
            $s = new IndexService();
            return json_encode($s->getPageData());
        }
    }
}