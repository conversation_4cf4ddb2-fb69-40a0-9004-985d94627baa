"use strict";
(self["webpackChunksfp_ext"] = self["webpackChunksfp_ext"] || []).push([["src_view_yike_produce_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/yike/produce.vue?vue&type=script&lang=js":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/yike/produce.vue?vue&type=script&lang=js ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../config */ "./src/config.js");
/* harmony import */ var _js_global__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../js/global */ "./src/js/global.js");
/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ "./node_modules/socket.io-client/build/esm/index.js");
var socket = null;



/* harmony default export */ __webpack_exports__["default"] = ({
  name: "yikeproudce",
  components: {},
  data() {
    return {
      key_list: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '·', '删除'],
      step_type: 0,
      equ_uid: '',
      equ_name: '',
      token_key: '',
      user_key: '',
      batch_key: '',
      user_uid: '',
      user_name: '',
      begin_produce_show: false,
      begin_ft_show: false,
      finish_ft_show: false,
      produce_uid: '',
      produce_data: {},
      produce_list: [],
      detail_data: {},
      error_show: false,
      error_type: '',
      error_data: {
        error_type: '',
        error_code: '',
        uid: ''
      },
      error_types: []
    };
  },
  created() {
    this.init();
  },
  methods: {
    connectSocket(port_code, data_type) {
      if (socket) {
        socket.disconnect();
      }
      socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__["default"])(_config__WEBPACK_IMPORTED_MODULE_0__["default"].wss);
      let data_list = [];
      socket.on('connect', () => {
        socket.emit('register', {
          port: port_code
        });
        socket.on('real', data => {});
      });
    },
    setUserFocus() {
      setTimeout(() => {
        this.user_key = '';
        $('#user_key').focus();
      }, 100);
    },
    setBatchFocus() {
      setTimeout(() => {
        this.batch_key = '';
        $('#batch_key').focus();
      }, 100);
    },
    tokenShow() {
      this.step_type = 1;
      this.token_key = '';
    },
    setToken() {
      if (this.token_key == '') {
        this.$message.error('请输入TOKEN');
        return;
      }
      if (this.token_key.length != 32) {
        this.$message.error('TOKEN长度不正确');
        return;
      }
      this.$http.post('mes/ipc/token', {
        uid: this.token_key
      }).then(rs => {
        if (rs.status == 'ok') {
          _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].setItem('equ_uid', rs.data.uid);
          _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].setItem('equ_name', rs.data.name);
          this.$message.success('设置成功');
          this.token_key = '';
          this.init();
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    init() {
      this.equ_uid = _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].getItem('equ_uid');
      this.equ_name = _js_global__WEBPACK_IMPORTED_MODULE_1__["default"].getItem('equ_name');
      if (this.equ_uid == '' || this.equ_uid == null) {
        this.step_type = 1;
      } else {
        this.step_type = 2;
        this.setUserFocus();
      }
    },
    handleKeypress(e) {
      if (e.code == 'Enter') {
        if (this.step_type == 2) {
          if (this.user_key.length != 32) {
            this.user_key = '';
            this.$message.error('二维码格式不正确');
            return;
          }
          this.getUserData(this.user_key);
          this.user_key = '';
        }
      }
    },
    getUserData(user_key) {
      this.$http.post('mes/ipc/user', {
        uid: user_key,
        equ_uid: this.equ_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.user_uid = rs.data.user_uid;
          this.user_name = rs.data.user_name;
          this.error_types = rs.data.error_types;
          this.setData(rs.data.data);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(e => {
        this.$message.error('未知错误');
      });
    },
    setData(data) {
      this.step_type = data.step;
      this.produce_data = data.produce_data;
      this.produce_list = data.produce_list;
      this.detail_data = data.detail_data;
    },
    numKeypress(key) {
      if (key == '删除') {
        this.detail_data.loss_cnt = '';
      } else {
        if (key == '·') {
          if (!(this.detail_data.loss_cnt == '' || this.detail_data.loss_cnt.indexOf('.') > -1)) {
            this.detail_data.loss_cnt += '.';
          }
        } else {
          this.detail_data.loss_cnt += key + '';
        }
      }
    },
    beginProduce(uid) {
      this.produce_uid = uid;
      this.begin_produce_show = true;
    },
    beginProduceSave() {
      if (this.produce_uid == '') {
        this.$message.error('请选择生产母卷');
        return;
      }
      this.$http.post('mes/ipc/begin', {
        user_uid: this.user_uid,
        equ_uid: this.equ_uid,
        uid: this.produce_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.begin_produce_show = false;
          this.setData(rs.data);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    beginFtSave() {
      if (this.detail_data.loss_cnt == '') {
        this.$message.error('请输入损耗长度');
        return;
      }
      this.$http.post('mes/ipc/start', {
        user_uid: this.user_uid,
        equ_uid: this.equ_uid,
        uid: this.detail_data.uid,
        loss_cnt: this.detail_data.loss_cnt
      }).then(rs => {
        if (rs.status == 'ok') {
          this.begin_ft_show = false;
          this.setData(rs.data);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    finishFtSave() {
      this.$http.post('mes/ipc/finish', {
        user_uid: this.user_uid,
        equ_uid: this.equ_uid,
        uid: this.detail_data.uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.finish_ft_show = false;
          this.setData(rs.data);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    refreshData() {
      this.$http.post('mes/ipc/refresh', {
        equ_uid: this.equ_uid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.setData(rs.data);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    },
    logout() {
      this.step_type = 2;
      this.user_uid = '';
      this.user_name = '';
      this.setUserFocus();
    },
    errorShow(idx) {
      if (this.detail_data.status == 10) {
        return;
      }
      this.error_data.error_code = this.detail_data.product_data[idx].code_no;
      this.error_data.error_type = this.detail_data.product_data[idx].error_type == null ? '' : this.detail_data.product_data[idx].error_type;
      this.error_data.uid = this.detail_data.product_data[idx].uid;
      this.error_show = true;
    },
    errorSave() {
      if (this.error_data.uid == '') {
        return;
      }
      this.$http.post('mes/ipc/save', {
        user_uid: this.user_uid,
        equ_uid: this.equ_uid,
        uid: this.error_data.uid,
        error_type: this.error_data.error_type
      }).then(rs => {
        if (rs.status == 'ok') {
          this.error_show = false;
          this.setData(rs.data);
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('未知错误');
      });
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/yike/produce.vue?vue&type=template&id=7d71ae5c&scoped=true":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/yike/produce.vue?vue&type=template&id=7d71ae5c&scoped=true ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticStyle: {
      "padding": "15px",
      "background-color": "#F2FEFF",
      "height": "100vh"
    }
  }, [_c('el-card', {
    staticClass: "box-card"
  }, [_c('div', {
    staticClass: "clearfix",
    staticStyle: {
      "display": "flex",
      "flex-direction": "row"
    },
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('div', {
    staticStyle: {
      "width": "20%"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "30px",
      "color": "#409eff",
      "font-weight": "bold"
    },
    domProps: {
      "textContent": _vm._s(_vm.equ_name)
    }
  })]), _c('div', {
    staticStyle: {
      "width": "30%"
    }
  }, [_vm.user_name != '' ? _c('div', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 操作人： "), _c('span', {
    staticStyle: {
      "font-size": "30px",
      "color": "#409eff",
      "font-weight": "bold"
    },
    domProps: {
      "textContent": _vm._s(_vm.user_name)
    }
  })]) : _vm._e()]), _c('div', {
    staticStyle: {
      "width": "50%",
      "text-align": "right"
    }
  }, [_vm.equ_uid != '' ? _c('el-button', {
    staticStyle: {
      "font-size": "20px",
      "margin-right": "15px"
    },
    attrs: {
      "type": "primary",
      "plain": ""
    },
    on: {
      "click": _vm.tokenShow
    }
  }, [_vm._v("设置")]) : _vm._e(), _vm.user_uid != '' ? _c('el-button', {
    staticStyle: {
      "font-size": "20px",
      "margin-right": "15px"
    },
    attrs: {
      "type": "warning",
      "plain": ""
    },
    on: {
      "click": _vm.refreshData
    }
  }, [_vm._v("刷新")]) : _vm._e(), _vm.user_uid != '' ? _c('el-button', {
    staticStyle: {
      "font-size": "20px"
    },
    attrs: {
      "type": "danger",
      "plain": ""
    },
    on: {
      "click": _vm.logout
    }
  }, [_vm._v("退出登录")]) : _vm._e()], 1)]), _c('div', {
    staticStyle: {
      "height": "85vh",
      "overflow": "auto"
    }
  }, [_vm.step_type == 1 ? _c('div', [_c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "center",
      "padding-top": "100px"
    }
  }, [_c('el-input', {
    staticStyle: {
      "width": "300px"
    },
    attrs: {
      "placeholder": "请输入TOKEN"
    },
    model: {
      value: _vm.token_key,
      callback: function ($$v) {
        _vm.token_key = $$v;
      },
      expression: "token_key"
    }
  }), _c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.setToken
    }
  }, [_vm._v("设置")])], 1), _c('div', {
    staticStyle: {
      "text-align": "center",
      "margin-top": "60px"
    }
  }, [_c('el-button', {
    staticStyle: {
      "width": "200px",
      "height": "120px"
    },
    attrs: {
      "type": "info",
      "plain": ""
    },
    on: {
      "click": _vm.init
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("关闭")])])])], 1)]) : _vm._e(), _vm.step_type == 2 ? _c('div', {
    staticStyle: {
      "padding-top": "50px"
    }
  }, [_c('div', {
    staticStyle: {
      "width": "500px",
      "height": "350px",
      "margin": "auto",
      "border": "2px solid #B2B2B2",
      "padding": "30px",
      "border-radius": "10px",
      "text-align": "center"
    }
  }, [_c('i', {
    staticClass: "el-icon-s-custom",
    staticStyle: {
      "font-size": "150px"
    }
  }), _c('div', [_c('span', {
    staticStyle: {
      "font-size": "50px"
    }
  }, [_vm._v("请扫描工卡")])]), _c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "justify-content": "center",
      "margin-top": "10px"
    }
  }, [_c('input', {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.user_key,
      expression: "user_key"
    }],
    staticClass: "form-control",
    staticStyle: {
      "ime-mode": "disabled",
      "width": "200px"
    },
    attrs: {
      "id": "user_key",
      "type": "text",
      "placeholder": "请扫描工卡",
      "autoComplete": "off"
    },
    domProps: {
      "value": _vm.user_key
    },
    on: {
      "keypress": _vm.handleKeypress,
      "input": function ($event) {
        if ($event.target.composing) return;
        _vm.user_key = $event.target.value;
      }
    }
  }), _c('el-button', {
    attrs: {
      "type": "primary",
      "plain": "",
      "size": "small"
    },
    on: {
      "click": _vm.setUserFocus
    }
  }, [_vm._v("扫码")])], 1), _c('div', {
    staticStyle: {
      "margin-top": "10px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "16px",
      "color": "red"
    }
  }, [_vm._v("如果扫码没反应，请点击上方（扫码）按钮后再扫码")])])])]) : _vm._e(), _vm.step_type == 3 ? _c('div', [_c('div', {
    staticStyle: {
      "width": "100%",
      "height": "62vh",
      "padding": "15px 0",
      "overflow": "auto"
    }
  }, [[_c('el-table', {
    staticStyle: {
      "width": "100%",
      "font-size": "16px"
    },
    attrs: {
      "data": _vm.produce_list
    }
  }, [_c('el-table-column', {
    attrs: {
      "label": "母卷号"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return [_c('span', {
          staticStyle: {
            "font-size": "30px",
            "font-weight": "bold"
          },
          domProps: {
            "textContent": _vm._s(scope.row.base_code)
          }
        })];
      }
    }], null, false, 3051034155)
  }), _c('el-table-column', {
    attrs: {
      "prop": "goods_spec",
      "label": "母卷规格"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "order_code",
      "label": "订单号"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "name",
      "label": "产品名称"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "spec",
      "label": "产品规格"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "cnt",
      "label": "生产数量(卷)"
    }
  }), _c('el-table-column', {
    attrs: {
      "label": "操作"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return [_c('el-button', {
          staticStyle: {
            "width": "150px",
            "height": "60px"
          },
          attrs: {
            "type": "primary",
            "plain": ""
          },
          on: {
            "click": function ($event) {
              return _vm.beginProduce(scope.row.uid);
            }
          }
        }, [_c('div', [_c('span', {
          staticStyle: {
            "font-size": "24px"
          }
        }, [_vm._v("开始生产")])])])];
      }
    }], null, false, 1170310829)
  })], 1)], _vm.produce_list.length == 0 ? _c('div', {
    staticStyle: {
      "text-align": "center",
      "padding-top": "20px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v("暂时没有生产任务")])]) : _vm._e()], 2)]) : _vm._e(), _vm.step_type == 4 ? _c('div', [_c('div', [_c('el-descriptions', {
    staticClass: "margin-top",
    staticStyle: {
      "font-size": "20px"
    },
    attrs: {
      "column": 3,
      "border": ""
    }
  }, [_c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 母卷号 ")]), _c('span', {
    staticStyle: {
      "font-weight": "bold",
      "font-size": "30px"
    },
    domProps: {
      "textContent": _vm._s(_vm.produce_data.base_code)
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 母卷规格 ")]), _c('span', {
    staticStyle: {
      "font-weight": "bold",
      "font-size": "30px"
    },
    domProps: {
      "textContent": _vm._s(_vm.produce_data.goods_spec)
    }
  })], 2), _c('el-descriptions-item', [_c('template', {
    slot: "label"
  }, [_vm._v(" 当前进度 ")]), _c('span', {
    staticStyle: {
      "font-weight": "bold",
      "font-size": "30px"
    },
    domProps: {
      "textContent": _vm._s('第' + _vm.detail_data.line_no + '刀')
    }
  })], 2)], 1)], 1), _c('div', {
    staticStyle: {
      "padding": "15px",
      "text-align": "center",
      "font-weight": "bold",
      "font-size": "40px"
    }
  }, [_c('span', {
    domProps: {
      "textContent": _vm._s(_vm.detail_data.status_name)
    }
  })]), _c('div', {
    staticStyle: {
      "width": "100%",
      "display": "flex",
      "flex-wrap": "wrap"
    }
  }, _vm._l(_vm.detail_data.product_data, function (product_item, product_idx) {
    return _c('div', {
      key: product_idx,
      style: {
        width: '160px',
        height: '90px',
        margin: '10px',
        border: '1px solid #D2D2D2',
        textAlign: 'center',
        backgroundColor: product_item.error_flag == 1 ? '#FFB9B9' : '#F5F5F5'
      },
      on: {
        "click": function ($event) {
          return _vm.errorShow(product_idx);
        }
      }
    }, [_c('div', {
      staticStyle: {
        "display": "flex",
        "flex-direction": "column",
        "justify-content": "center",
        "height": "100%"
      }
    }, [_c('div', [_c('span', {
      staticStyle: {
        "font-size": "40px",
        "font-weight": "bold"
      },
      domProps: {
        "textContent": _vm._s(product_item.code_no)
      }
    })]), _c('div', [_c('span', {
      staticStyle: {
        "font-size": "20px"
      },
      domProps: {
        "textContent": _vm._s(product_item.error_type)
      }
    })])])]);
  }), 0), _c('div', {
    staticStyle: {
      "text-align": "center",
      "margin-top": "60px"
    }
  }, [_vm.detail_data.status == 10 ? _c('el-button', {
    staticStyle: {
      "width": "240px",
      "height": "80px"
    },
    attrs: {
      "type": "primary",
      "plain": ""
    },
    on: {
      "click": function ($event) {
        _vm.begin_ft_show = true;
      }
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("分条开始")])])]) : _vm._e(), _vm.detail_data.status == 20 ? _c('el-button', {
    staticStyle: {
      "width": "240px",
      "height": "80px"
    },
    attrs: {
      "type": "danger",
      "plain": ""
    },
    on: {
      "click": function ($event) {
        _vm.finish_ft_show = true;
      }
    }
  }, [_c('div', [_c('span', {
    staticStyle: {
      "font-size": "30px"
    }
  }, [_vm._v("分条完成")])])]) : _vm._e()], 1)]) : _vm._e()])]), _c('el-dialog', {
    attrs: {
      "title": "确认",
      "visible": _vm.begin_produce_show,
      "width": "30%"
    },
    on: {
      "update:visible": function ($event) {
        _vm.begin_produce_show = $event;
      }
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 确定开始生产吗？ ")]), _c('span', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    on: {
      "click": function ($event) {
        _vm.begin_produce_show = false;
      }
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 取 消")])]), _c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.beginProduceSave
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 确 定")])])], 1)]), _c('el-dialog', {
    attrs: {
      "title": " 确定开始分条吗？",
      "visible": _vm.begin_ft_show,
      "width": "50%"
    },
    on: {
      "update:visible": function ($event) {
        _vm.begin_ft_show = $event;
      }
    }
  }, [_c('div', [_c('div', {
    staticStyle: {
      "text-align": "center"
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "40px"
    }
  }, [_vm._v("请输入损耗长度")])]), _c('div', {
    staticStyle: {
      "text-align": "center",
      "margin-top": "15px"
    }
  }, [_c('el-input', {
    staticStyle: {
      "width": "300px",
      "font-size": "30px",
      "color": "#0080FF",
      "font-weight": "bold"
    },
    attrs: {
      "readonly": "",
      "type": "text"
    },
    model: {
      value: _vm.detail_data.loss_cnt,
      callback: function ($$v) {
        _vm.$set(_vm.detail_data, "loss_cnt", $$v);
      },
      expression: "detail_data.loss_cnt"
    }
  }, [_c('template', {
    slot: "append"
  }, [_vm._v("M")])], 2)], 1), _c('div', {
    staticStyle: {
      "padding": "20px"
    }
  }, [_c('div', {
    staticStyle: {
      "display": "flex",
      "flex-direction": "row",
      "border": "1px solid #f2f2f2",
      "background-color": "#fafafa",
      "width": "100%",
      "flex-wrap": "wrap",
      "padding": "5px"
    }
  }, _vm._l(_vm.key_list, function (key, key_idx) {
    return _c('div', {
      key: key_idx,
      staticStyle: {
        "padding": "5px"
      }
    }, [_c('el-button', {
      staticStyle: {
        "font-size": "30px"
      },
      attrs: {
        "type": "info",
        "plain": ""
      },
      on: {
        "click": function ($event) {
          return _vm.numKeypress(key);
        }
      }
    }, [_vm._v(_vm._s(key))])], 1);
  }), 0)])]), _c('span', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    on: {
      "click": function ($event) {
        _vm.begin_ft_show = false;
      }
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 取 消")])]), _c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.beginFtSave
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 确 定")])])], 1)]), _c('el-dialog', {
    attrs: {
      "title": "确定",
      "visible": _vm.finish_ft_show,
      "width": "30%"
    },
    on: {
      "update:visible": function ($event) {
        _vm.finish_ft_show = $event;
      }
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 确定完成分条吗？ ")]), _c('span', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    on: {
      "click": function ($event) {
        _vm.finish_ft_show = false;
      }
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 取 消")])]), _c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.finishFtSave
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 确 定")])])], 1)]), _c('el-dialog', {
    attrs: {
      "title": " 请选择不合格类型",
      "visible": _vm.error_show,
      "width": "40%"
    },
    on: {
      "update:visible": function ($event) {
        _vm.error_show = $event;
      }
    }
  }, [_c('div', {
    staticStyle: {
      "font-size": "40px",
      "font-weight": "bold",
      "text-align": "center"
    }
  }, [_c('apan', {
    domProps: {
      "textContent": _vm._s(_vm.error_data.error_code)
    }
  })], 1), _c('div', [_c('div', {
    staticStyle: {
      "margin-top": "20px"
    }
  }, [_c('el-radio-group', {
    model: {
      value: _vm.error_data.error_type,
      callback: function ($$v) {
        _vm.$set(_vm.error_data, "error_type", $$v);
      },
      expression: "error_data.error_type"
    }
  }, [_c('el-radio-button', {
    attrs: {
      "label": ""
    }
  }, [_c('div', {
    staticStyle: {
      "width": "100px",
      "height": "40px",
      "line-height": "40px",
      "font-size": "20px"
    }
  }, [_vm._v("合格")])])], 1), _c('el-radio-group', {
    attrs: {
      "fill": "red"
    },
    model: {
      value: _vm.error_data.error_type,
      callback: function ($$v) {
        _vm.$set(_vm.error_data, "error_type", $$v);
      },
      expression: "error_data.error_type"
    }
  }, _vm._l(_vm.error_types, function (error_item, error_idx) {
    return _c('el-radio-button', {
      key: error_idx,
      attrs: {
        "label": error_item.name
      }
    }, [_c('div', {
      staticStyle: {
        "width": "100px",
        "height": "40px",
        "line-height": "40px",
        "font-size": "20px"
      }
    }, [_vm._v(_vm._s(error_item.name))])]);
  }), 1)], 1)]), _c('span', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    on: {
      "click": function ($event) {
        _vm.error_show = false;
      }
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 取 消")])]), _c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.errorSave
    }
  }, [_c('span', {
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm._v(" 确 定")])])], 1)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./src/view/yike/produce.vue":
/*!***********************************!*\
  !*** ./src/view/yike/produce.vue ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _produce_vue_vue_type_template_id_7d71ae5c_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./produce.vue?vue&type=template&id=7d71ae5c&scoped=true */ "./src/view/yike/produce.vue?vue&type=template&id=7d71ae5c&scoped=true");
/* harmony import */ var _produce_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./produce.vue?vue&type=script&lang=js */ "./src/view/yike/produce.vue?vue&type=script&lang=js");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _produce_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _produce_vue_vue_type_template_id_7d71ae5c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _produce_vue_vue_type_template_id_7d71ae5c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "7d71ae5c",
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/view/yike/produce.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/view/yike/produce.vue?vue&type=script&lang=js":
/*!***********************************************************!*\
  !*** ./src/view/yike/produce.vue?vue&type=script&lang=js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/yike/produce.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/view/yike/produce.vue?vue&type=template&id=7d71ae5c&scoped=true":
/*!*****************************************************************************!*\
  !*** ./src/view/yike/produce.vue?vue&type=template&id=7d71ae5c&scoped=true ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_vue_vue_type_template_id_7d71ae5c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_vue_vue_type_template_id_7d71ae5c_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_vue_vue_type_template_id_7d71ae5c_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=template&id=7d71ae5c&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/yike/produce.vue?vue&type=template&id=7d71ae5c&scoped=true");


/***/ })

}]);
//# sourceMappingURL=src_view_yike_produce_vue.8f34c9c7.js.map