<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseWarehouse;
use Envsan\Modules\Purchase\Service\PurchaseWarehouseService;
use Envsan\Modules\Sys\Service\UserService;

/**
 * @name('仓库管理')
 */
class WarehouseController extends SuperController
{
    private $page_id = 1;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseWarehouseService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('创建')
     */
    public function createAction()
    {
        $s = new PurchaseWarehouseService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = (new PurchaseWarehouse())->toArray();

        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $this->view->jsonPurchaseWarehouse = json_encode($jrow);
        $userService = new UserService();
        $this->view->user_list = $userService->selectUsers();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'purchase:warehouse:create'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseWarehouseService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, CvtUtil::emptyToArray($row->ext_data));
        $this->view->jsonPurchaseWarehouse = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('warehouse/create');
    }

    /**
     * @acl({'link':'purchase:warehouse:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWarehouseService();
            $ret = new JsonData();
            $ret->handleResult($s->deleteByUid($this->request->getPost('uid', 'tstring')));
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new PurchaseWarehouseService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->jsonPurchaseWarehouse = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseWarehouseService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}