<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Work\Model\WorkData;
use Phalcon\Mvc\User\Component;
use Upyun\Upyun;

class DataService extends Component
{
    public function searchAll()
    {
        $user_name = $this->request->get('user_name', ['string', 'trim']);
        $group_name = $this->request->get('group_name', ['string', 'trim']);
        $type_name = $this->request->get('type_name', ['string', 'trim']);
        $content = $this->request->get('content', ['string', 'trim']);
        $status = $this->request->get('status', ['string', 'trim']);
        $date_start = $this->request->get('date_start', ['string', 'trim']);
        $date_end = $this->request->get('date_end', ['string', 'trim']);
        $builder = $this->modelsManager->createBuilder()
            ->columns( '
                t1.id,
                t1.uid,
                t1.code,
                t1.type_name,
                t1.review_type,
                ifnull(t1.remarks,\'\') as remarks,
                t1.create_date,
                t1.status,
                t1.pressing_flag,
                t1.handle_status,
                t1.abstrakt_data,
                t2.name as group_name,
                t3.real_name as create_name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.create_group_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        ModelUtil::limitGroup('t1.create_group_id', $builder);
        if (!CheckUtil::is_empty($type_name)) {
            $builder->andWhere("t1.type_name like ?5", [5 => "%$type_name%"]);
        }
        if (!CheckUtil::is_empty($content)) {
            $builder->andWhere("t1.form_data like ?6", [6 => "%$content%"]);
        }
        if (!empty($date_start)) {
            $builder->andWhere("t1.create_date >= ?7", [7 => $date_start.' 00:00:00']);
        }
        if (!empty($date_end)) {
            $builder->andWhere("t1.create_date <= ?8", [8 => $date_end.' 23:59:59']);
        }
        if (!empty($status)) {
            if ($status == 1){
                $builder->andWhere("t1.status = 15");
            } else if ($status == 2){
                $builder->andWhere("t1.status = 20 and t1.handle_status = 1");
            } else if ($status == 3){
                $builder->andWhere("t1.status = 20 and t1.handle_status > 1");
            }
        }
        if (!CheckUtil::is_empty($user_name)) {
            $builder->andWhere("t3.real_name like ?10", [10 => "%$user_name%"]);
        }
        if (!CheckUtil::is_empty($group_name)) {
            $builder->andWhere("t2.name like ?11", [11 => "%$group_name%"]);
        }
        return $builder;
    }

    public function getWorkData($row){
        $ws = new WorkService();
        $rtn = $ws->getFlowData($row,1);
        return $rtn;
    }

    public function selectById($id)
    {
        return WorkData::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function deleteById($id)
    {
        $row = WorkData::findFirst(['id = ?1', 'bind' => [1 => $id]]);
        if ($row != null)
            return $row->delete();
        return false;
    }

    public function selectByUid($uid)
    {
        return WorkData::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = WorkData::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
        if ($row == false || $row->del_flag == 1) {
            return true;
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save();
    }

    public function getGroupList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.uid,a.id,a.name')
            ->addFrom('Envsan\Modules\Sys\Model\Group', 'a')
            ->where('a.del_flag = 0')
            ->orderBy('a.id');
        ModelUtil::limitGroup('a.id', $builder);

        return $builder->getQuery()->execute();
    }

    public function getAllGroupList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.uid,a.id,a.name')
            ->addFrom('Envsan\Modules\Sys\Model\Group', 'a')
            ->where('a.del_flag = 0')
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }

    public function getSubList($row)
    {
        if ($row->more_flag == 0) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.uid,
                a.type_name as name,
                a.dest_name,
                a.src_name,
                a.plate,
                a.in_date,
                a.out_date
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData', 'a')
            ->where('a.del_flag = 0 and a.id <> ?1 and a.pid = ?1', [1 => $row->id])
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }

    public function getViewData()
    {
        $row = $this->selectByUid($this->request->getPost('uid', 'tstring'));
        if ($row == false) {
            die(ErrorHelper::WRONG_ID);
        }

        return $this->getWorkData($row);
    }

    public function setDetailSearch($rows){
        foreach ($rows as &$row){
            $row['form_data_type'] = ConstantUtil::$review_doc_types[$row['form_data_type']];
        }
        return $rows;
    }

    public function updateTime($row,$index){
        $view_date = $this->request->getPost('view_date', 'tstring');
        $view_time = $this->request->getPost('view_time', 'tstring');
        if (empty($view_date) || empty($view_time)){
            return ErrorHelper::WRONG_INPUT;
        }
        $flow_list = json_decode($row->flow_list,true);
        foreach ($flow_list as $key => &$item){
            if($key == $index){
                $time = substr_replace($view_date.$view_time, " ", 10, 0);
                $item['time'] = $time;
            }
        }
        $row->flow_list = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
        if (!$row->save()) {
            return ErrorHelper::UNKOWN;
        }
        return'';
    }
}