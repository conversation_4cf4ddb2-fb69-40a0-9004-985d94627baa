<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesShipType;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseSupplier;
use Envsan\Modules\Purchase\Model\PurchaseWwProcessPrice;

class PurchaseWwProcessService extends BaseService
{
    public function selectAllPagination()
    {
        $columns = [
            't1.id',
            't1.uid',
            't1.supplier_id',
            't1.supplier_code',
            't1.supplier_name',
            't1.price_flag',
            't1.goods_code',
            't1.goods_name',
            't1.good_inventory_code',
            't1.goods_model',
            't1.ship_type_id',
            't1.ship_type_name',
            't1.ship_explain',
            't1.measurement_unit',
            't1.price_unit',
            't1.unit_price',
            't1.conversion_rate',
            't1.currency',
            'round(t1.tax_rate * 100, 2) as tax_rate',
            't1.effective_date',
            't1.expiry_date',
            't1.remarks',
            't1.ext_data',
            't1.ext_val',
            't1.update_date',
            't1.update_by',
            't1.del_flag',
            't1.owner'
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns($columns)
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwProcessPrice', 't1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseWwProcessPrice();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        // 委外商
        $supplier_info = $this->request->getPost('supplier_info', 'tstring');
        // 委外商编码
        $supplier_code = $this->request->getPost('supplier_code', 'tstring');
        // 价格标识
        $price_flag_info = $this->request->getPost('price_flag_info', 'tstring');
        // 物料编码
        $goods_code = $this->request->getPost('goods_code', 'tstring');
        // 物料名称
        $goods_name = $this->request->getPost('goods_name', 'tstring');
        // 物料代码
        $good_inventory_code = $this->request->getPost('good_inventory_code', 'tstring');
        // 规格型号
        $goods_model = $this->request->getPost('goods_model', 'tstring');
        // 工序信息
        $ship_type_info = $this->request->getPost('ship_type_info', 'tstring');
        // 计量单位
        $measurement_unit = $this->request->getPost('measurement_unit', 'tstring');
        // 计价单位
        $price_unit = $this->request->getPost('price_unit', 'tstring');
        // 换算率
        $conversion_rate = $this->request->getPost('conversion_rate', 'tstring');
        // 加工费单价
        $unit_price = $this->request->getPost('unit_price', 'tstring');
        // 币种
        $currency = $this->request->getPost('currency', 'tstring');
        // 税率
        $tax_rate = $this->request->getPost('tax_rate', 'tstring');
        // 生效日期
        $effective_date = $this->request->getPost('effective_date', 'tstring');
        // 失效日期
        $expiry_date = $this->request->getPost('expiry_date', 'tstring');
        // 备注
        $remarks = $this->request->getPost('remarks', 'tstring');
        $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));

        if (empty($supplier_info) || empty($supplier_code))
            return '确认委外商信息是否输入';

        if (empty($price_flag_info))
            return '没选价格标识';

        if (empty($goods_code) || empty($goods_name) || empty($goods_model))
            return '确认物料信息输入';

        if (empty($ship_type_info))
            return '确认工序信息输入';

        if (empty($measurement_unit))
            return '确认计量单位输入';

        if (empty($price_unit))
            return '确认计价单位输入';

        if (!empty($tax_rate) && !CheckUtil::isDecimal4($tax_rate)) {
            return '无效的税率';
        } else {
            $tax_rate = CvtUtil::percentToDecimal($tax_rate);
        }

        if (floatval($conversion_rate) < 0 || !preg_match('/^\d{1,6}(\.\d{1,4})?$/', $conversion_rate)) {
            return '换算率格式错误';
        }

        if (floatval($unit_price) < 0 || !preg_match('/^\d{1,8}(\.\d{1,4})?$/', $unit_price)) {
            return '加工费格式错误';
        }

        if (empty($effective_date) || !CheckUtil::isDate($effective_date) || (!empty($expiry_date) && !CheckUtil::isDate($expiry_date))) {
            return '请选择有效的日期';
        } else if (!empty($expiry_date) && $effective_date > $expiry_date) {
            return '生效日期不能晚于失效日期';
        } else if (!empty($expiry_date))  {
            $today = strtotime(date('Y-m-d'));
            $expiry_time = strtotime($expiry_date);
            if ($expiry_time <= $today) {
                return '失效日期不能早于今天';
            }
        }

        // 分隔外委商的id和名称
        $supplier_arr = explode("-", $supplier_info);
        // 分隔工序的id和名称
        $ship_type_arr = explode("-", $ship_type_info);

        $this->db->begin();
        try {
            $row->uid = UUID::make();
            // 获取表单服务和当前时间
            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            // 将扩展数据转换为数组
            $ext_data = CvtUtil::emptyToArray($ext_data);

            // 为模型对象赋值
            // 委外商信息
            $row->supplier_id = $supplier_arr[0];        // 委外商ID
            $row->supplier_name = $supplier_arr[1];    // 委外商名称
            $row->supplier_code = $supplier_code;    // 委外商编码

            // 价格相关
            $row->price_flag = $price_flag_info;          // 价格标识
            $row->unit_price = $unit_price;          // 加工费单价
            $row->currency = $currency;              // 币种
            $row->tax_rate = $tax_rate;              // 税率

            // 物料信息
            $row->goods_code = $goods_code;          // 物料编码
            $row->goods_name = $goods_name;          // 物料名称
            $row->good_inventory_code = $good_inventory_code;  // 物料代码
            $row->goods_model = $goods_model;          // 规格型号

            // 工序信息
            $row->ship_type_id = $ship_type_arr[0];      // 工序ID
            $row->ship_type_name = $ship_type_arr[1];      // 工序名称

            // 单位信息
            $row->measurement_unit = $measurement_unit;  // 计量单位
            $row->price_unit = $price_unit;          // 计价单位
            $row->conversion_rate = $conversion_rate;    // 换算率

            // 时间信息
            $row->effective_date = $effective_date;  // 生效日期
            $row->expiry_date = CvtUtil::blankToNull($expiry_date);        // 失效日期

            // 其他信息
            $row->remarks = $remarks;                // 备注
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;

            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }

            if (!$row->save()) {
                // 获取具体的验证错误信息
                $messages = [];
                foreach ($row->getMessages() as $message) {
                    $messages[] = $message->getMessage();
                }
                throw new \Exception("更新失败: " . implode(", ", $messages));
            }
            $this->db->commit();
            return '';
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
    }

    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwProcessPrice')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return PurchaseWwProcessPrice::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseWwProcessPrice::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    /**
     * 获取供应商信息
     * @return array 统一格式的返回结果
     */
    public function changeSupplier()
    {
        $supplier_id = $this->request->getPost('supplier_id', 'tstring');
        $supplier_data = PurchaseSupplier::findFirst(['id = ?1 and del_flag = 0 and owner = ?2', 'bind' => [1 => $supplier_id, 2 => SessionData::user()->owner]]);
        if (!$supplier_data) {
            return $this->error(ErrorHelper::ROW_NOTEXIST);
        }
        return $this->success($supplier_data->toArray());
    }

    /**
     * 取得商品信息
     * @return array 统一格式的返回结果
     */
    public function getGoodsInfo()
    {
        $goods_code = $this->request->getPost('goods_code', 'tstring');
        $goods_data = PurchaseGoods::findFirst(['code = ?1 and del_flag = 0 and owner = ?2', 'bind' => [1 => $goods_code, 2 => SessionData::user()->owner]]);
        if (!$goods_data) {
            return $this->error(ErrorHelper::ROW_NOTEXIST);
        }
        return $this->success($goods_data->toArray());
    }

    /**
     * 取得工艺类型
     * @return MesShipType[]
     */
    public function getShipTypeList()
    {
        return MesShipType::find(['del_flag = 0 and owner = ?1', 'bind' => [1 => SessionData::ownerId()]]);
    }

    public function selectData()
    {
        $supplier_id = $this->request->getPost('supplier_id', 'tstring');
        $goods_code = $this->request->getPost('goods_code', 'tstring');
        $ship_type_id = $this->request->getPost('ship_type_id', 'tstring');

        $columns = [
            't1.id',
            't1.uid',
            't1.supplier_id',
            't1.supplier_code',
            't1.supplier_name',
            't1.price_flag',
            't1.goods_code',
            't1.goods_name',
            't1.good_inventory_code',
            't1.goods_model',
            't1.ship_type_id',
            't1.ship_type_name',
            't1.ship_explain',
            't1.measurement_unit',
            't1.price_unit',
            't1.unit_price',
            't1.conversion_rate',
            't1.currency',
            't1.tax_rate',
            't1.effective_date',
            't1.expiry_date',
            't1.remarks',
            't1.ext_data',
            't1.ext_val',
            't1.update_date',
            't1.update_by',
            't1.del_flag',
            't1.owner'
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns($columns)
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwProcessPrice', 't1')
            ->where('
                t1.del_flag = 0 and t1.owner = ?1 and supplier_id = ?2 
                and DATE(effective_date) <= DATE(?3) and (DATE(expiry_date) >= DATE(?3) or expiry_date is null)
                and goods_code = ?4
                and ship_type_id = ?5
            ', [1 => SessionData::ownerId(), 2 => $supplier_id, 3 => DateUtil::now(), 4 => $goods_code, 5 => $ship_type_id])
            ->orderBy('t1.id desc');

        return $builder->getQuery()->execute()->toArray();
    }


}