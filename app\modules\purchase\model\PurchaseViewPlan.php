<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
class PurchaseViewPlan extends BaseModel
{

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $plan_begin_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $quantity;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_view_plan';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewPlan[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewPlan
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
