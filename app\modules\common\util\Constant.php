<?php
namespace Envsan\Modules\Common\Util;

class Constant
{
    public static $page_extend_column = [
        1 => [
            'page_name' => '客户管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'name', 'name' => '客户名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '客户编号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 1, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        2 => [
            'page_name' => '销售订单管理',
            'header_data' => self::PAGE_COMMON_COLUMN['trade_order']
        ],
        3 => [
            'page_name' => '销售订单明细',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'new_flag_name', 'name' => '是否新品', 'unit' => '', 'show' => 1, 'condition' => 't1.new_flag_name'],
                ['base'=> 1,'type' => 1, 'id' => 'name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '产品规格', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 2, 'id' => 'cnt', 'name' => '订单数量', 'unit' => '个', 'show' => 1, 'condition' => 't1.cnt'],
                ['base'=> 1,'type' => 2, 'id' => 'price', 'name' => '未税单价', 'unit' => '元/个', 'show' => 1, 'condition' => 't1.price'],
                ['base'=> 1,'type' => 2, 'id' => 'price_hs', 'name' => '含税单价', 'unit' => '元/个', 'show' => 1, 'condition' => 't1.price_hs'],
                ['base'=> 1,'type' => 2, 'id' => 'tax_rate', 'name' => '税率', 'unit' => '%', 'show' => 1, 'condition' => 't1.tax_rate'],
                ['base'=> 1,'type' => 2, 'id' => 'weight', 'name' => '单重', 'unit' => 'KG', 'show' => 1, 'condition' => 't1.weight'],
                ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 3, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        4 => [
            'page_name' => '产品管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'goods_status', 'name' => 'goods_status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 1, 'id' => 'name', 'name' => '名称/型号', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_type_name', 'name' => '存货档案', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '产品备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 4, 'show' => 1, 'condition' => 't1.ext_val'],
                ['base'=> 1,'type' => 99, 'id' => 'order_detail_ext_val', 'name' => '订单', 'page_id' => 3, 'show' => 1, 'condition' => 't3.ext_val']
            ]
        ],
        5 => [
            'page_name' => '生产通知',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名', 'unit' => '', 'show' => 1, 'condition' => 't4.name'],
                ['base'=> 1,'type' => 1, 'id' => 'plan_begin_date', 'name' => '计划开工日', 'unit' => '', 'show' => 1, 'condition' => 't1.plan_begin_date'],
                ['base'=> 1,'type' => 1, 'id' => 'plan_end_date', 'name' => '计划完工日', 'unit' => '', 'show' => 1, 'condition' => 't1.plan_end_date'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 99, 'id' => 'product_ext_val', 'name' => '', 'page_id' => 5, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        6 => [
            'page_name' => '排产管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 9, 'id' => 'warning_flag', 'name' => '排产预警', 'unit' => '', 'show' => 1, 'condition' => '','data_values' => [1 => ['c'=>'danger','v'=>'排产预警'] , 0 => ['c'=>'','v'=>'']]],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品规格', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 1, 'id' => 'plan_begin_date', 'name' => '计划开工日', 'unit' => '', 'show' => 1, 'condition' => 't4.plan_begin_date'],
                ['base'=> 1,'type' => 1, 'id' => 'plan_end_date', 'name' => '计划完工日', 'unit' => '', 'show' => 1, 'condition' => 't4.plan_end_date'],
                ['base'=> 1,'type' => 2, 'id' => 'quantity', 'name' => '计划生产数量', 'unit' => '个', 'show' => 1, 'condition' => 't1.quantity'],
                ['base'=> 1,'type' => 2, 'id' => 'error_rate', 'name' => '不良率', 'unit' => '%', 'show' => 1, 'condition' => 't5.error_rate'],
                ['base'=> 1,'type' => 2, 'id' => 'finish_rate', 'name' => '生产完成进度', 'unit' => '%', 'show' => 1, 'condition' => 't5.finish_rate'],
                ['base'=> 1,'type' => 2, 'id' => 'plan_rate', 'name' => '排产完成进度', 'unit' => '%', 'show' => 1, 'condition' => 't5.plan_rate'],
                ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 99, 'id' => 'product_ext_val', 'name' => '', 'page_id' => 5, 'show' => 1, 'condition' => 't2.ext_val']
            ]
        ],
        7 => [
            'page_name' => '生产完成情况统计',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品规格', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 1, 'id' => 'plan_begin_date', 'name' => '计划开工日', 'unit' => '', 'show' => 1, 'condition' => 't4.plan_begin_date'],
                ['base'=> 1,'type' => 1, 'id' => 'plan_end_date', 'name' => '计划完工日', 'unit' => '', 'show' => 1, 'condition' => 't4.plan_end_date'],
                ['base'=> 1,'type' => 2, 'id' => 'quantity', 'name' => '计划生产数量', 'unit' => '个', 'show' => 1, 'condition' => 't1.quantity'],
                ['base'=> 1,'type' => 2, 'id' => 'instock_cnt', 'name' => '入库数量', 'unit' => '个', 'show' => 1, 'condition' => 't7.instock_cnt'],
                ['base'=> 1,'type' => 2, 'id' => 'error_cnt', 'name' => '不合格数量', 'unit' => '个', 'show' => 1, 'condition' => 't5.error_cnt'],
                ['base'=> 1,'type' => 2, 'id' => 'produce_rate', 'name' => '生产进度', 'unit' => '%', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 99, 'id' => 'product_ext_val', 'name' => '', 'page_id' => 5, 'show' => 1, 'condition' => 't2.ext_val']
            ]
        ],
        8 => [
            'page_name' => '质检查询',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'notice_code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't3.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名', 'unit' => '', 'show' => 1, 'condition' => 't6.name'],
                ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品规格', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't4.name'],
                ['base'=> 1,'type' => 2, 'id' => 'quantity', 'name' => '生产数量', 'unit' => '个', 'show' => 1, 'condition' => 't2.quantity'],
                ['base'=> 1,'type' => 1, 'id' => 'bom_name', 'name' => '质验工艺', 'unit' => '', 'show' => 1, 'condition' => 't5.name'],
                ['base'=> 1,'type' => 1, 'id' => 'quality_template_name', 'name' => '质验项目', 'unit' => '', 'show' => 1, 'condition' => 't1.quality_template_name'],
                ['base'=> 1,'type' => 1, 'id' => 'staff_name', 'name' => '质验人', 'unit' => '', 'show' => 1, 'condition' => 't1.staff_name'],
                ['base'=> 1,'type' => 1, 'id' => 'work_date', 'name' => '质验日期', 'unit' => '', 'show' => 1, 'condition' => 't1.work_date'],
                ['base'=> 1,'type' => 1, 'id' => 'create_time', 'name' => '质验时间', 'unit' => '', 'show' => 1, 'condition' => 't1.create_time'],
                ['base'=> 1,'type' => 9, 'id' => 'error_flag', 'name' => '质验结果', 'unit' => '', 'show' => 1, 'condition' => 't1.error_flag','data_values' => [1 => ['c'=>'danger','v'=>'NG'] , 0 => ['c'=>'success','v'=>'OK']]],
                ['base'=> 1,'type' => 2, 'id' => 'error_cnt', 'name' => '不良数量', 'unit' => '', 'show' => 1, 'condition' => 't1.error_cnt'],
                ['base'=> 1,'type' => 1, 'id' => 'error_type', 'name' => '不良类型', 'unit' => '', 'show' => 1, 'condition' => 't1.error_type'],
                ['base'=> 1,'type' => 1, 'id' => 'error_remarks', 'name' => '不良说明', 'unit' => '', 'show' => 1, 'condition' => 't1.error_remarks']
            ]
        ],
        9 => [
            'page_name' => '生产履历查询',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'notice_code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't3.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名', 'unit' => '', 'show' => 1, 'condition' => 't6.name'],
                ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品规格', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't4.name'],
                ['base'=> 1,'type' => 2, 'id' => 'quantity', 'name' => '生产数量', 'unit' => '个', 'show' => 1, 'condition' => 't2.quantity'],
                ['base'=> 1,'type' => 1, 'id' => 'bom_name', 'name' => '生产工艺', 'unit' => '', 'show' => 1, 'condition' => 't5.name'],
                ['base'=> 1,'type' => 1, 'id' => 'staff_name', 'name' => '生产人', 'unit' => '', 'show' => 1, 'condition' => 't1.staff_name'],
                ['base'=> 1,'type' => 1, 'id' => 'work_date', 'name' => '生产日期', 'unit' => '', 'show' => 1, 'condition' => 't1.work_date'],
                ['base'=> 1,'type' => 9, 'id' => 'shift_type', 'name' => '班次', 'unit' => '', 'show' => 1, 'condition' => '','data_values' => [1 => ['c'=>'success','v'=>'白班'] , 2 => ['c'=>'warning','v'=>'夜班']]],
                ['base'=> 1,'type' => 9, 'id' => 'work_type', 'name' => '工作类型', 'unit' => '', 'show' => 1, 'condition' => '','data_values' => [1 => ['c'=>'success','v'=>'日工'] , 2 => ['c'=>'warning','v'=>'记件']]],
                ['base'=> 1,'type' => 2, 'id' => 'cnt', 'name' => '生产数量', 'unit' => '件', 'show' => 1, 'condition' => 't1.cnt'],
                ['base'=> 1,'type' => 2, 'id' => 'hour', 'name' => '工时', 'unit' => '小时', 'show' => 1, 'condition' => 't1.hour'],
                ['base'=> 1,'type' => 2, 'id' => 'error_cnt', 'name' => '不良数量', 'unit' => '', 'show' => 1, 'condition' => 't1.error_cnt'],
                ['base'=> 1,'type' => 1, 'id' => 'error_type', 'name' => '不良类型', 'unit' => '', 'show' => 1, 'condition' => 't1.error_type'],
                ['base'=> 1,'type' => 2, 'id' => 'error_money', 'name' => '不良成本', 'unit' => '', 'show' => 1, 'condition' => 't1.error_money'],
                ['base'=> 1,'type' => 1, 'id' => 'error_remarks', 'name' => '不良说明', 'unit' => '', 'show' => 1, 'condition' => 't1.error_remarks']
            ]
        ],
        10 => [
            'page_name' => '其他工作查询',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'work_date', 'name' => '工作日期', 'unit' => '', 'show' => 1, 'condition' => 't1.work_date'],
                ['base'=> 1,'type' => 9, 'id' => 'shift_type', 'name' => '班次', 'unit' => '', 'show' => 1, 'condition' => '','data_values' => [1 => ['c'=>'success','v'=>'白班'] , 2 => ['c'=>'warning','v'=>'夜班']]],
                ['base'=> 1,'type' => 1, 'id' => 'staff_name', 'name' => '工作人', 'unit' => '', 'show' => 1, 'condition' => 't1.staff_name'],
                ['base'=> 1,'type' => 1, 'id' => 'produce_type', 'name' => '工作类型', 'unit' => '', 'show' => 1, 'condition' => 't1.produce_type'],
                ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '工作说明', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 2, 'id' => 'hour', 'name' => '工时', 'unit' => '小时', 'show' => 1, 'condition' => 't1.hour']
            ]
        ],
        11 => [
            'page_name' => '产成品库存查询',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 2, 'id' => 'cnt', 'name' => '库存数量', 'unit' => '个', 'show' => 1, 'condition' => 't2.cnt'],
                ['base'=> 1,'type' => 99, 'id' => 'product_ext_val', 'name' => '产品', 'page_id' => 4, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        12 => [
            'page_name' => '发货管理',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '发货单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'outstock_date', 'name' => '发货日期', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_date'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 12,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        13 => [
            'page_name' => '发货查询',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'outstock_uid','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'outstock_code','name' => '发货单号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 1,'type' => 6,'id' => 'outstock_date', 'name' => '发货日期', 'unit' => '', 'show' => 1, 'condition' => 't2.outstock_date'],
                ['base'=> 1,'type' => 1,'id' => 'outstock_user', 'name' => '发货人', 'unit' => '', 'show' => 1, 'condition' => 't7.real_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '发货备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1,'id' => 'order_code', 'name' => '项目号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base'=> 1,'type' => 1,'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 't6.name'],
                ['base'=> 1,'type' => 1,'id' => 'product_code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 't5.code'],
                ['base'=> 1,'type' => 1,'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't5.name'],
                ['base'=> 1,'type' => 2,'id' => 'quantity', 'name' => '发货数量', 'unit' => '个', 'show' => 1, 'condition' => 't1.quantity'],
                ['base'=> 1,'type' => 99,'id' => 'outstock_ext_val','name' => '发货','page_id'=> 12,'show' => 1,'condition' => 't2.ext_val'],
                ['base'=> 1,'type' => 99, 'id' => 'product_ext_val', 'name' => '产品', 'page_id' => 4, 'show' => 1, 'condition' => 't5.ext_val'],
                ['base'=> 1,'type' => 99, 'id' => 'order_detail_ext_val', 'name' => '订单明细', 'page_id' => 3, 'show' => 1, 'condition' => 't3.ext_val'],
                ['base'=> 1,'type' => 99, 'id' => 'order_ext_val', 'name' => '订单', 'page_id' => 2, 'show' => 1, 'condition' => 't4.ext_val'],
            ]
        ],
        14 => [
            'page_name' => '外委采购计划',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'entrust_code', 'name' => '外委计划号', 'unit' => '', 'show' => 1, 'condition' => 't.code'],
                ['base'=> 1,'type' => 1, 'id' => 'notice_code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品规格', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 1, 'id' => 'bom_name', 'name' => '外委工艺', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 6,'id' => 'start_date', 'name' => '计划出库日', 'unit' => '', 'show' => 1, 'condition' => 't6.start_date'],
                ['base'=> 1,'type' => 6,'id' => 'end_date', 'name' => '计划入库日', 'unit' => '', 'show' => 1, 'condition' => 't6.end_date'],
                ['base'=> 1,'type' => 2, 'id' => 'quantity', 'name' => '外委数量', 'unit' => '件', 'show' => 1, 'condition' => 't.quantity'],
                ['base'=> 1,'type' => 2,'id' => 'wait_out_cnt', 'name' => '待出库数量', 'unit' => '件', 'show' => 1, 'condition' => 't7.finish_cnt'],
                ['base'=> 1,'type' => 2,'id' => 'out_cnt', 'name' => '出库数量', 'unit' => '件', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 6,'id' => 'out_date', 'name' => '出库日期', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 2,'id' => 'in_cnt', 'name' => '入库数量', 'unit' => '件', 'show' => 1, 'condition' => 't8.finish_cnt'],
                ['base'=> 1,'type' => 6,'id' => 'in_date', 'name' => '入库日期', 'unit' => '', 'show' => 1, 'condition' => 't8.work_date'],
                ['base'=> 1,'type' => 1,'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name']
            ]
        ],
        15 => [
            'page_name' => '排产查询',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'notice_detail_uid', 'name' => 'notice_detail_uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品规格', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 1, 'id' => 'bom_name', 'name' => '工艺名称', 'unit' => '', 'show' => 1, 'condition' => 't6.name'],
                ['base'=> 1,'type' => 1, 'id' => 'equ_code', 'name' => '设备编号', 'unit' => '', 'show' => 1, 'condition' => 't5.code'],
                ['base'=> 1,'type' => 1, 'id' => 'equ_name', 'name' => '设备名称', 'unit' => '', 'show' => 1, 'condition' => 't5.name'],
                ['base'=> 1,'type' => 1, 'id' => 'plan_date', 'name' => '排产日期', 'unit' => '', 'show' => 1, 'condition' => 't.plan_date'],
                ['base'=> 1,'type' => 2, 'id' => 'plan_cnt', 'name' => '排产数量', 'unit' => '件', 'show' => 1, 'condition' => 't.plan_cnt'],
                ['base'=> 1,'type' => 2, 'id' => 'plan_hour', 'name' => '排产时长', 'unit' => '小时', 'show' => 1, 'condition' => 't.plan_hour']
            ]
        ],
        16 => [
            'page_name' => '生产日报',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'report_date', 'name' => '生产日期', 'unit' => '', 'show' => 1, 'condition' => 't1.report_date'],
                ['base'=> 1,'type' => 2, 'id' => 'user_cnt', 'name' => '出勤人数', 'unit' => '人', 'show' => 1, 'condition' => 't1.user_cnt'],
                ['base'=> 1,'type' => 2, 'id' => 'user_cost', 'name' => '人工成本', 'unit' => '元', 'show' => 1, 'condition' => 't1.user_cost'],
                ['base'=> 1,'type' => 1, 'id' => 'review_name', 'name' => '审核人', 'unit' => '', 'show' => 1, 'condition' => 't2.real_name'],
                ['base'=> 1,'type' => 1, 'id' => 'review_time', 'name' => '审核时间', 'unit' => '', 'show' => 1, 'condition' => 't1.review_time'],
                ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 16, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        17 => [
            'page_name' => '图纸管理',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'order_code', 'name' => '项目号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base' => 0, 'type' => 1, 'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 't6.name'],
                ['base' => 0, 'type' => 1, 'id' => 'product_code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base' => 0, 'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base' => 1, 'type' => 1, 'id' => 'code', 'name' => '图纸编号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base' => 1, 'type' => 1, 'id' => 'drawing_name', 'name' => '文件名称', 'unit' => '', 'show' => 1, 'condition' => 't1.drawing_name'],
                ['base' => 1, 'type' => 1, 'id' => 'version_code', 'name' => '版本号', 'unit' => '', 'show' => 1, 'condition' => 't1.version_code'],
                ['base' => 1, 'type' => 1, 'id' => 'upload_user', 'name' => '上传人', 'unit' => '', 'show' => 1, 'condition' => 't5.real_name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '说明', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
            ]
        ],
        18 => [
            'page_name' => '外委加工单管理',
            'header_data' => self::PAGE_COMMON_COLUMN['purchase_order']
        ],
        19 => [
            'page_name' => '外委加工单查询',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'order_code', 'name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 'a.order_code'],
                ['base' => 1, 'type' => 6, 'id' => 'order_date', 'name' => '订单日期', 'unit' => '', 'show' => 1, 'condition' => 'a.order_date'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 's.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 'a.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 'a.remarks'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 19, 'show' => 1, 'condition' => 'a.ext_val']
            ]
        ],
        20 => [
            'page_name' => '外委出库管理',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'code', 'name' => '出库单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base' => 1, 'type' => 6, 'id' => 'outstock_date', 'name' => '出库日期', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_date'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 19, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        21 => [
            'page_name' => '外委出库查询',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'code', 'name' => '出库单号', 'unit' => '', 'show' => 1, 'condition' => 'a.code'],
                ['base' => 1, 'type' => 6, 'id' => 'outstock_date', 'name' => '出库日期', 'unit' => '', 'show' => 1, 'condition' => 'a.outstock_date'],
                ['base' => 0, 'type' => 1, 'id' => 'order_code', 'name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 'o.order_code'],
                ['base' => 0, 'type' => 6, 'id' => 'order_date', 'name' => '订单日期', 'unit' => '', 'show' => 1, 'condition' => 'o.order_date'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 's.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 'a.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 'a.remarks'],
                ['base' => 0, 'type' => 2, 'id' => 'quantity', 'name' => '出库数量', 'unit' => '', 'show' => 1, 'condition' => 'd.quantity'],
                ['base' => 0, 'type' => 2, 'id' => 'price', 'name' => '出库单价', 'unit' => '', 'show' => 1, 'condition' => 'd.price'],
                ['base' => 0, 'type' => 2, 'id' => 'total_money', 'name' => '出库总额', 'unit' => '元', 'show' => 1, 'condition' => 'd.total_money'],
                ['base' => 0, 'type' => 1, 'id' => 'notice_code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base' => 0, 'type' => 1, 'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 0, 'type' => 1, 'id' => 'product_code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base' => 0, 'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base' => 0, 'type' => 1, 'id' => 'bom_name', 'name' => '工艺名称', 'unit' => '', 'show' => 1, 'condition' => 't5.name'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 21, 'show' => 1, 'condition' => 'a.ext_val']
            ]
        ],
        22 => [
            'page_name' => '工资统计',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'report_date','name' => '生产日期', 'unit' => '', 'show' => 1, 'condition' => 't1.report_date'],
                ['base'=> 1,'type' => 1,'id' => 'staff_name','name' => '员工姓名', 'unit' => '', 'show' => 1, 'condition' => 't1.staff_name'],
                ['base'=> 1,'type' => 2,'id' => 'cost','name' => '时薪', 'unit' => '元/H', 'show' => 1, 'condition' => 't1.cost'],
                ['base'=> 1,'type' => 2,'id' => 'day_money','name' => '日工工资', 'unit' => '元', 'show' => 1, 'condition' => 't1.day_money'],
                ['base'=> 1,'type' => 2,'id' => 'jj_money','name' => '计件工资', 'unit' => '元', 'show' => 1, 'condition' => 't1.jj_money'],
                ['base'=> 1,'type' => 2,'id' => 'other_money','name' => '其他工作', 'unit' => '元', 'show' => 1, 'condition' => 't1.other_money'],
                ['base'=> 1,'type' => 2,'id' => 'day_bz_money','name' => '补助', 'unit' => '元', 'show' => 1, 'condition' => 't1.day_bz_money'],
                ['base'=> 1,'type' => 2,'id' => 'sum_money','name' => '工资', 'unit' => '元', 'show' => 1, 'condition' => 't1.sum_money']
            ]
        ],
        23 => [
            'page_name' => '供应商管理',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '编号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 1,'id' => 'name', 'name' => '名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 1,'id' => 'name_as', 'name' => '简称', 'unit' => '', 'show' => 1, 'condition' => 't1.name_as'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 23,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        24 => [
            'page_name' => '采购订单管理',
            'header_data' => self::PAGE_COMMON_COLUMN['purchase_order']
        ],
        25 => [
            'page_name' => '采购入库管理',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '入库单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'instock_date','name' => '入库日期', 'unit' => '', 'show' => 1, 'condition' => 't1.instock_date'],
                ['base'=> 0,'type' => 1,'id' => 'order_code','name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't2.order_code'],
                ['base'=> 0,'type' => 1,'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1,'type' => 2,'id' => 'total_money', 'name' => '入库未税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money'],
                ['base'=> 1,'type' => 2,'id' => 'total_money_hs', 'name' => '入库含税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money_hs'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 25,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        26 => [
            'page_name' => '领料出库管理',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '领料单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'outstock_date', 'name' => '领料日期', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_date'],
                ['base'=> 1,'type' => 1,'id' => 'outstock_user', 'name' => '领料人', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_user'],
                ['base'=> 1,'type' => 1,'id' => 'notice_code','name' => '生产批次', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 26,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        27 => [
            'page_name' => '其他入库管理',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '入库单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'other_date', 'name' => '入库日期', 'unit' => '', 'show' => 1, 'condition' => 't1.other_date'],
                ['base'=> 1,'type' => 2,'id' => 'total_quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't1.total_quantity'],
                ['base'=> 1,'type' => 2,'id' => 'total_money', 'name' => '入库未税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money'],
                ['base'=> 1,'type' => 2,'id' => 'total_money_hs', 'name' => '入库含税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money_hs'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 27,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        28 => [
            'page_name' => '盘点管理',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '盘点单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'inventory_date', 'name' => '盘点日期', 'unit' => '', 'show' => 1, 'condition' => 't1.inventory_date'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 28,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        29 => [
            'page_name' => '采购订单查询',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'order_code','name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't1.order_code'],
                ['base'=> 1,'type' => 6,'id' => 'order_date', 'name' => '订单日期', 'unit' => '', 'show' => 1, 'condition' => 't1.order_date'],
                ['base'=> 1,'type' => 6,'id' => 'finish_date', 'name' => '订单完结日期', 'unit' => '', 'show' => 1, 'condition' => 'substring(t1.finish_date, 1, 10)'],
                ['base'=> 1, 'type' => 2, 'id' => 'order_total_weight', 'name' => '订单总重量', 'unit' => '', 'show' => 1, 'condition' => 't1.total_weight'],
                ['base'=> 1, 'type' => 2, 'id' => 'order_total_money', 'name' => '订单未税总金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.total_money'],
                ['base'=> 1, 'type' => 2, 'id' => 'order_total_money_hs', 'name' => '订单含税总金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.total_money_hs'],
                ['base'=> 1,'type' => 1,'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1,'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 't3.goods_code'],
                ['base'=> 1,'type' => 1,'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 't3.goods_name'],
                ['base'=> 1,'type' => 1,'id' => 'goods_model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 't3.goods_model'],
                ['base'=> 1,'type' => 2,'id' => 'purchase_quantity', 'name' => '采购数量', 'unit' => '', 'show' => 1, 'condition' => 't3.purchase_quantity'],
                ['base'=> 1,'type' => 1,'id' => 'goods_unit', 'name' => '采购单位', 'unit' => '', 'show' => 1, 'condition' => 't3.goods_unit'],
                ['base'=> 1,'type' => 2,'id' => 'quantity', 'name' => '库存需求数量', 'unit' => '', 'show' => 1, 'condition' => 't3.quantity'],
                ['base'=> 1,'type' => 1,'id' => 'goods_deputy_unit', 'name' => '库存单位', 'unit' => '', 'show' => 1, 'condition' => 't3.goods_deputy_unit'],
                ['base'=> 1,'type' => 2,'id' => 'weight', 'name' => '物资单重', 'unit' => '', 'show' => 1, 'condition' => 't3.weight'],
                ['base'=> 1,'type' => 2,'id' => 'total_weight', 'name' => '物资总重', 'unit' => '', 'show' => 1, 'condition' => 't3.total_weight'],
                ['base'=> 1,'type' => 2,'id' => 'price', 'name' => '物资未税单价', 'unit' => '', 'show' => 1, 'condition' => 't3.price'],
                ['base'=> 1,'type' => 2,'id' => 'total_money', 'name' => '物资未税金额', 'unit' => '元', 'show' => 1, 'condition' => 't3.total_money'],
                ['base'=> 1,'type' => 2,'id' => 'tax_rate', 'name' => '物资税率', 'unit' => '%', 'show' => 1, 'condition' => 't3.tax_rate * 100'],
                ['base'=> 1,'type' => 2,'id' => 'price_hs', 'name' => '物资含税单价', 'unit' => '', 'show' => 1, 'condition' => 't3.price_hs'],
                ['base'=> 1,'type' => 2,'id' => 'total_money_hs', 'name' => '物资含税金额', 'unit' => '元', 'show' => 1, 'condition' => 't3.total_money_hs'],
                ['base'=> 1,'type' => 2,'id' => 'instock_quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't4.quantity'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 29,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        30 => [
            'page_name' => '采购入库查询',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'detail_uid','name' => 'detail_uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'check_status','name' => 'check_status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '入库单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'instock_date','name' => '入库日期', 'unit' => '', 'show' => 1, 'condition' => 't1.instock_date'],
                ['base'=> 1,'type' => 1,'id' => 'order_code','name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't2.order_code'],
                ['base'=> 1,'type' => 1,'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1,'type' => 2,'id' => 'total_quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't1.total_quantity'],
                ['base'=> 1,'type' => 2,'id' => 'total_money', 'name' => '入库未税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money'],
                ['base'=> 1,'type' => 2,'id' => 'total_money_hs', 'name' => '入库含税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money_hs'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1,'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_code'],
                ['base'=> 1,'type' => 1,'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_name'],
                ['base'=> 1,'type' => 1,'id' => 'goods_spec', 'name' => '规格', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_spec'],
                ['base'=> 1,'type' => 1,'id' => 'goods_model', 'name' => '型号', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_model'],
                ['base'=> 1,'type' => 2,'id' => 'quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity'],
                ['base'=> 1,'type' => 1,'id' => 'goods_unit', 'name' => '数量单位', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_unit'],
                ['base'=> 1,'type' => 2,'id' => 'price', 'name' => '未税单价', 'unit' => '', 'show' => 1, 'condition' => 't99.price'],
                ['base'=> 1,'type' => 2,'id' => 'price_hs', 'name' => '含税单价', 'unit' => '', 'show' => 1, 'condition' => 't99.price_hs'],
                ['base'=> 1,'type' => 2,'id' => 'total_money', 'name' => '未税金额', 'unit' => '元', 'show' => 1, 'condition' => 't99.total_money'],
                ['base'=> 1,'type' => 2,'id' => 'total_money_hs', 'name' => '含税金额', 'unit' => '元', 'show' => 1, 'condition' => 't99.total_money_hs'],
                ['base'=> 1,'type' => 9,'id' => 'check_flag', 'name' => '是否检验', 'unit' => '', 'show' => 1, 'condition' => '','data_values' => [1 => ['c'=>'success','v'=>'是'] , 0 => ['c'=>'default','v'=>'否']]],
                ['base'=> 1,'type' => 9,'id' => 'check_result_flag', 'name' => '检验结果', 'unit' => '', 'show' => 1, 'condition' => '','data_values' => [0 => ['c'=>'success','v'=>'OK'] , 1 => ['c'=>'danger','v'=>'否']]],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 30,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        31 => [
            'page_name' => '领料出库查询',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '领料单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'outstock_date', 'name' => '领料日期', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_date'],
                ['base'=> 1,'type' => 1,'id' => 'outstock_user', 'name' => '领料人', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_user'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1,'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_code'],
                ['base'=> 1,'type' => 1,'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_name'],
                ['base'=> 1,'type' => 1,'id' => 'goods_spec', 'name' => '规格', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_spec'],
                ['base'=> 1,'type' => 1,'id' => 'goods_model', 'name' => '型号', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_model'],
                ['base'=> 1,'type' => 2,'id' => 'input_val', 'name' => '输入内容', 'unit' => '', 'show' => 1, 'condition' => 't99.input_val'],
                ['base'=> 1,'type' => 2,'id' => 'quantity', 'name' => '出库数量', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity'],
                ['base'=> 1,'type' => 1,'id' => 'goods_unit', 'name' => '数量单位', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_unit'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 31,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        32 => [
            'page_name' => '其他入库查询',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '入库单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'other_date', 'name' => '入库日期', 'unit' => '', 'show' => 1, 'condition' => 't1.other_date'],
                ['base'=> 1,'type' => 2,'id' => 'total_quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't1.total_quantity'],
                ['base'=> 1,'type' => 2,'id' => 'total_money', 'name' => '入库未税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money'],
                ['base'=> 1,'type' => 2,'id' => 'total_money_hs', 'name' => '入库含税金额', 'unit' => '', 'show' => 1, 'condition' => 't1.total_money_hs'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1,'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_code'],
                ['base'=> 1,'type' => 1,'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_name'],
                ['base'=> 1,'type' => 1,'id' => 'goods_spec', 'name' => '规格', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_spec'],
                ['base'=> 1,'type' => 1,'id' => 'goods_model', 'name' => '型号', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_model'],
                ['base'=> 1,'type' => 2,'id' => 'quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity'],
                ['base'=> 1,'type' => 1,'id' => 'goods_unit', 'name' => '数量单位', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_unit'],
                ['base'=> 1,'type' => 2,'id' => 'price', 'name' => '未税单价', 'unit' => '', 'show' => 1, 'condition' => 't99.price'],
                ['base'=> 1,'type' => 2,'id' => 'price_hs', 'name' => '含税单价', 'unit' => '', 'show' => 1, 'condition' => 't99.price_hs'],
                ['base'=> 1,'type' => 2,'id' => 'total_money', 'name' => '未税金额', 'unit' => '元', 'show' => 1, 'condition' => 't99.total_money'],
                ['base'=> 1,'type' => 2,'id' => 'total_money_hs', 'name' => '含税金额', 'unit' => '元', 'show' => 1, 'condition' => 't99.total_money_hs'],
                ['base'=> 0,'type' => 1,'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 32,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        33 => [
            'page_name' => '盘点查询',
            'header_data' => [
                ['base'=> 0,'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
                ['base'=> 0,'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1,'id' => 'code','name' => '盘点单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6,'id' => 'inventory_date', 'name' => '盘点日期', 'unit' => '', 'show' => 1, 'condition' => 't1.inventory_date'],
                ['base'=> 1,'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1,'type' => 1,'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_code'],
                ['base'=> 1,'type' => 1,'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_name'],
                ['base'=> 1,'type' => 1,'id' => 'goods_spec', 'name' => '规格', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_spec'],
                ['base'=> 1,'type' => 1,'id' => 'goods_model', 'name' => '型号', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_model'],
                ['base'=> 1,'type' => 2,'id' => 'quantity_before', 'name' => '盘点前库存', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity_before'],
                ['base'=> 1,'type' => 2,'id' => 'quantity', 'name' => '盘点数量', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity'],
                ['base'=> 1,'type' => 2,'id' => 'quantity_after', 'name' => '盘点后库存', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity_after'],
                ['base'=> 1,'type' => 1,'id' => 'goods_unit', 'name' => '数量单位', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_unit'],
                ['base'=> 1,'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 33,'show' => 1,'condition' => 't1.ext_val']
            ]
        ],
        34 => [
            'page_name' => '销售收款管理',
            'header_data' => [
                ['base'=> 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1, 'type' => 1, 'id' => 'receive_no', 'name' => '收款单号', 'unit' => '', 'show' => 1, 'condition' => 't1.receive_no'],
                ['base'=> 1, 'type' => 6, 'id' => 'receive_date', 'name' => '收款日期', 'unit' => '', 'show' => 1, 'condition' => 't1.receive_date'],
                ['base'=> 1, 'type' => 2, 'id' => 'receive_money', 'name' => '收款金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.receive_money'],
                ['base'=> 0, 'type' => 1, 'id' => 'order_code', 'name' => '项目号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 0, 'type' => 1, 'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 34, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        35 => [
            'page_name' => '销售开票管理',
            'header_data' => [
                ['base'=> 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1, 'type' => 1, 'id' => 'invoice_no', 'name' => '开票单号', 'unit' => '', 'show' => 1, 'condition' => 't1.invoice_no'],
                ['base'=> 1, 'type' => 6, 'id' => 'invoice_date', 'name' => '开票日期', 'unit' => '', 'show' => 1, 'condition' => 't1.invoice_date'],
                ['base'=> 1, 'type' => 2, 'id' => 'invoice_money', 'name' => '开票金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.invoice_money'],
                ['base'=> 0, 'type' => 1, 'id' => 'order_code', 'name' => '项目号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base'=> 0, 'type' => 1, 'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base'=> 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base'=> 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 35, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        36 => [
            'page_name' => '采购开票管理',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'invoice_no', 'name' => '开票单号', 'unit' => '', 'show' => 1, 'condition' => 't1.invoice_no'],
                ['base' => 1, 'type' => 6, 'id' => 'invoice_date', 'name' => '开票日期', 'unit' => '', 'show' => 1, 'condition' => 't1.invoice_date'],
                ['base' => 1, 'type' => 2, 'id' => 'invoice_money', 'name' => '开票金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.invoice_money'],
                ['base' => 0, 'type' => 1, 'id' => 'order_code', 'name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 36, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        37 => [
            'page_name' => '采购开票查询',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'invoice_no', 'name' => '开票单号', 'unit' => '', 'show' => 1, 'condition' => 't1.invoice_no'],
                ['base' => 1, 'type' => 6, 'id' => 'invoice_date', 'name' => '开票日期', 'unit' => '', 'show' => 1, 'condition' => 't1.invoice_date'],
                ['base' => 1, 'type' => 1, 'id' => 'order_code', 'name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't2.order_code'],
                ['base' => 1, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_code'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_name'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_spec', 'name' => '规格', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_spec'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_model', 'name' => '型号', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_model'],
                ['base' => 0, 'type' => 2, 'id' => 'quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_unit', 'name' => '数量单位', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_unit'],
                ['base' => 0, 'type' => 2, 'id' => 'price', 'name' => '单价', 'unit' => '', 'show' => 1, 'condition' => 't99.price'],
                ['base' => 0, 'type' => 2, 'id' => 'total_money', 'name' => '总额', 'unit' => '元', 'show' => 1, 'condition' => 't99.total_money'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 37, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        38 => [
            'page_name' => '采购付款管理',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'pay_no', 'name' => '付款单号', 'unit' => '', 'show' => 1, 'condition' => 't1.pay_no'],
                ['base' => 1, 'type' => 6, 'id' => 'pay_date', 'name' => '付款日期', 'unit' => '', 'show' => 1, 'condition' => 't1.pay_date'],
                ['base' => 1, 'type' => 2, 'id' => 'pay_money', 'name' => '付款金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.pay_money'],
                ['base' => 0, 'type' => 1, 'id' => 'order_code', 'name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 38, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        39 => [
            'page_name' => '采购付款查询',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'pay_no', 'name' => '付款单号', 'unit' => '', 'show' => 1, 'condition' => 't1.pay_no'],
                ['base' => 1, 'type' => 6, 'id' => 'pay_date', 'name' => '付款日期', 'unit' => '', 'show' => 1, 'condition' => 't1.pay_date'],
                ['base' => 0, 'type' => 1, 'id' => 'order_code', 'name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't2.order_code'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base' => 0, 'type' => 1, 'id' => 'invoice_no', 'name' => '开票单号', 'unit' => '', 'show' => 1, 'condition' => 't5.invoice_no'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_code'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_name'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_spec', 'name' => '规格', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_spec'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_model', 'name' => '型号', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_model'],
                ['base' => 0, 'type' => 2, 'id' => 'quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 't99.quantity'],
                ['base' => 0, 'type' => 1, 'id' => 'goods_unit', 'name' => '数量单位', 'unit' => '', 'show' => 1, 'condition' => 't99.goods_unit'],
                ['base' => 0, 'type' => 2, 'id' => 'price', 'name' => '单价', 'unit' => '', 'show' => 1, 'condition' => 't99.price'],
                ['base' => 0, 'type' => 2, 'id' => 'total_money', 'name' => '总额', 'unit' => '元', 'show' => 1, 'condition' => 't99.total_money'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 39, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        40 => [
            'page_name' => '外委入库管理',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'code', 'name' => '入库单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base' => 1, 'type' => 6, 'id' => 'instock_date', 'name' => '入库日期', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_date'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 40, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        41 => [
            'page_name' => '外委入库查询',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'code', 'name' => '入库单号', 'unit' => '', 'show' => 1, 'condition' => 'a.code'],
                ['base' => 1, 'type' => 6, 'id' => 'instock_date', 'name' => '入库日期', 'unit' => '', 'show' => 1, 'condition' => 'a.outstock_date'],
                ['base' => 0, 'type' => 1, 'id' => 'order_code', 'name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 'o.order_code'],
                ['base' => 0, 'type' => 6, 'id' => 'order_date', 'name' => '订单日期', 'unit' => '', 'show' => 1, 'condition' => 'o.order_date'],
                ['base' => 0, 'type' => 1, 'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 's.name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 'a.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 'a.remarks'],
                ['base' => 0, 'type' => 2, 'id' => 'quantity', 'name' => '入库数量', 'unit' => '', 'show' => 1, 'condition' => 'd.quantity'],
                ['base' => 0, 'type' => 2, 'id' => 'price', 'name' => '入库单价', 'unit' => '', 'show' => 1, 'condition' => 'd.price'],
                ['base' => 0, 'type' => 2, 'id' => 'total_money', 'name' => '入库总额', 'unit' => '元', 'show' => 1, 'condition' => 'd.total_money'],
                ['base' => 0, 'type' => 1, 'id' => 'notice_code', 'name' => '生产批次号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
                ['base' => 0, 'type' => 1, 'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 't3.name'],
                ['base' => 0, 'type' => 1, 'id' => 'product_code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
                ['base' => 0, 'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base' => 0, 'type' => 1, 'id' => 'bom_name', 'name' => '工艺名称', 'unit' => '', 'show' => 1, 'condition' => 't5.name'],
                ['base' => 1, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 40, 'show' => 1, 'condition' => 'a.ext_val']
            ]
        ],
        42 => [
            'page_name' => '生产图纸打印',
            'header_data' => self::PAGE_COMMON_COLUMN['mes_task_wait']
        ],
        43 => [
            'page_name' => '技术派工',
            'header_data' => self::PAGE_COMMON_COLUMN['trade_order']
        ],
        44 => [
            'page_name' => '采购申请管理',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'apply_code', 'name' => '申请单号', 'unit' => '', 'show' => 1, 'condition' => 't1.apply_code'],
                ['base' => 1, 'type' => 6, 'id' => 'apply_date', 'name' => '申请日期', 'unit' => '', 'show' => 1, 'condition' => 't1.apply_date'],
                ['base' => 1, 'type' => 2, 'id' => 'goods_cnt', 'name' => '物资数量', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_cnt'],
                ['base' => 1, 'type' => 1, 'id' => 'goods_name', 'name' => '物资', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_name'],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base' => 1, 'type' => 1, 'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
                ['base' => 0, 'type' => 1, 'id' => 'apply_type_name', 'name' => '申请类型', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base' => 0, 'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 44, 'show' => 1, 'condition' => 't1.ext_val']
            ]
        ],
        45 => [
            'page_name' => '采购申请查询',
            'header_data' => [
                ['base' => 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 0, 'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'goods_code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 'a.goods_code'],
                ['base' => 1, 'type' => 1, 'id' => 'goods_name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 'a.goods_name'],
                ['base' => 1, 'type' => 1, 'id' => 'goods_model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 'a.goods_model'],
                ['base' => 1, 'type' => 2, 'id' => 'quantity', 'name' => '需求数量', 'unit' => '', 'show' => 1, 'condition' => 'a.quantity'],
                ['base' => 1, 'type' => 1, 'id' => 'goods_deputy_unit', 'name' => '需求单位', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base' => 1, 'type' => 2, 'id' => 'purchase_quantity', 'name' => '采购数量', 'unit' => '', 'show' => 1, 'condition' => 'a.purchase_quantity'],
                ['base' => 1, 'type' => 1, 'id' => 'goods_unit', 'name' => '采购单位', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base' => 1, 'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 'a.status_name'],
                ['base' => 0, 'type' => 1, 'id' => 'apply_code', 'name' => '申请单号', 'unit' => '', 'show' => 1, 'condition' => 'r.apply_code'],
                ['base' => 0, 'type' => 6, 'id' => 'apply_date', 'name' => '申请日期', 'unit' => '', 'show' => 1, 'condition' => 'r.apply_date'],
                ['base' => 0, 'type' => 1, 'id' => 'purchase_code', 'name' => '采购订单', 'unit' => '', 'show' => 1, 'condition' => 'p.order_code'],
                ['base' => 0, 'type' => 1, 'id' => 'product_code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 'pt.code'],
                ['base' => 0, 'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 'pt.name'],
                ['base' => 0, 'type' => 1, 'id' => 'order_code', 'name' => '项目号', 'unit' => '', 'show' => 1, 'condition' => 'o.code'],
                ['base' => 0, 'type' => 1, 'id' => 'customer_name', 'name' => '客户', 'unit' => '', 'show' => 1, 'condition' => 'c.name'],
                ['base' => 0, 'type' => 1, 'id' => 'apply_type_name', 'name' => '申请类型', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base' => 0, 'type' => 99, 'id' => 'ext_val', 'name' => '采购申请管理', 'page_id' => 44, 'show' => 1, 'condition' => 'r.ext_val']
            ]
        ],
        46 => [
            'page_name' => '设备管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'name', 'name' => '设备名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '设备编号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 1, 'id' => 'type_name', 'name' => '设备型号', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '设备状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '设备管理', 'page_id' => 46, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        47 => [
            'page_name' => '设备型号管理',
            'header_data' => [
                ['base'=> 0, 'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0, 'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1, 'type' => 1, 'id' => 'name', 'name' => '型号名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1, 'type' => 1, 'id' => 'ship_type_name', 'name' => '工艺类型', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
                ['base'=> 1, 'type' => 2, 'id' => 'tonnage', 'name' => '吨位', 'unit' => '', 'show' => 1, 'condition' => 't1.tonnage'],
                ['base'=> 1, 'type' => 99, 'id' => 'ext_val', 'name' => '设备型号管理', 'page_id' => 47, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        48 => [
            'page_name' => '外协供应商管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'name', 'name' => '供应商名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '外协供应商管理', 'page_id' => 48, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        49 => [
            'page_name' => '外委工序价格管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_code', 'name' => '委外商编码', 'unit' => '', 'show' => 1, 'condition' => 't1.supplier_code'],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_name', 'name' => '委外商名称', 'unit' => '', 'show' => 1, 'condition' => 't1.supplier_name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_code', 'name' => '物料编码', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_code'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_name', 'name' => '物料名称', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_model'],
                ['base'=> 1,'type' => 1, 'id' => 'ship_type_id', 'name' => '工序', 'unit' => '', 'show' => 1, 'condition' => 't1.ship_type_id'],
                ['base'=> 1,'type' => 1, 'id' => 'ship_type_name', 'name' => '工序说明', 'unit' => '', 'show' => 1, 'condition' => 't1.ship_type_name'],
                ['base'=> 1,'type' => 1, 'id' => 'measurement_unit', 'name' => '主计量', 'unit' => '', 'show' => 1, 'condition' => 't1.measurement_unit'],
                ['base'=> 1,'type' => 1, 'id' => 'price_unit', 'name' => '计价单位', 'unit' => '', 'show' => 1, 'condition' => 't1.price_unit'],
                ['base'=> 1,'type' => 2, 'id' => 'unit_price', 'name' => '加工费单价', 'unit' => '', 'show' => 1, 'condition' => 't1.unit_price'],
                ['base'=> 1,'type' => 2, 'id' => 'conversion_rate', 'name' => '换算率', 'unit' => '', 'show' => 1, 'condition' => 't1.conversion_rate'],
                ['base'=> 1,'type' => 1, 'id' => 'currency', 'name' => '币种', 'unit' => '', 'show' => 1, 'condition' => 't1.currency'],
                ['base'=> 1,'type' => 2, 'id' => 'tax_rate', 'name' => '税率(%)', 'unit' => '', 'show' => 1, 'condition' => 't1.tax_rate'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '外委工序价格管理', 'page_id' => 49, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        50 => [
            'page_name' => '采购到货单管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => '状态', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'receipt_code', 'name' => '单据号', 'unit' => '', 'show' => 1, 'condition' => 't1.receipt_code'],
                // ['base'=> 1,'type' => 1, 'id' => 'business_type_name', 'name' => '业务类型', 'unit' => '', 'show' => 1, 'condition' => 't1.business_type_name'],
                // ['base'=> 1,'type' => 1, 'id' => 'purchase_type_name', 'name' => '采购类型', 'unit' => '', 'show' => 1, 'condition' => 't1.purchase_type_name'],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_code', 'name' => '供应商编码', 'unit' => '', 'show' => 1, 'condition' => 't2.supplier_code'],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_name', 'name' => '供应商名称', 'unit' => '', 'show' => 1, 'condition' => 't1.supplier_name'],
                ['base'=> 1,'type' => 1, 'id' => 'currency', 'name' => '币种', 'unit' => '', 'show' => 1, 'condition' => 't1.currency'],
                ['base'=> 1,'type' => 2, 'id' => 'exchange_rate', 'name' => '汇率', 'unit' => '', 'show' => 1, 'condition' => 't1.exchange_rate'],
                ['base'=> 1,'type' => 6, 'id' => 'receipt_date', 'name' => '到货日期', 'unit' => '', 'show' => 1, 'condition' => 't1.receipt_date'],
                ['base'=> 1,'type' => 1, 'id' => 'department_name', 'name' => '部门', 'unit' => '', 'show' => 1, 'condition' => 't1.department_name'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1, 'id' => 'remark', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remark'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '采购到货单管理', 'page_id' => 50, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        51 => [
            'page_name' => '来料报检单',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'inspection_code', 'name' => '单据编号', 'unit' => '', 'show' => 1, 'condition' => 't1.inspection_code'],
                ['base'=> 1,'type' => 6, 'id' => 'inspection_day', 'name' => '报检日期', 'unit' => '', 'show' => 1, 'condition' => 't1.inspection_day'],
                ['base'=> 1,'type' => 6, 'id' => 'inspection_date', 'name' => '报检时间', 'unit' => '', 'show' => 1, 'condition' => 't1.inspection_date'],
                ['base'=> 1,'type' => 1, 'id' => 'inspection_department', 'name' => '报检部门', 'unit' => '', 'show' => 1, 'condition' => 't1.inspection_department'],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_name', 'name' => '供应商名称', 'unit' => '', 'show' => 1, 'condition' => 't1.supplier_name'],
                ['base'=> 1,'type' => 1, 'id' => 'receipt_code', 'name' => '到货单据', 'unit' => '', 'show' => 1, 'condition' => 't1.receipt_code'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '来料报检单', 'page_id' => 51, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        52 => [
            'page_name' => '来料检验单',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'check_code', 'name' => '检验单号', 'unit' => '', 'show' => 1, 'condition' => 't1.check_code'],
                ['base'=> 1,'type' => 6, 'id' => 'check_time', 'name' => '检验时间', 'unit' => '', 'show' => 1, 'condition' => 't1.check_time'],
                ['base'=> 1,'type' => 1, 'id' => 'inspection_code', 'name' => '报检单号', 'unit' => '', 'show' => 1, 'condition' => 't2.inspection_code'],
                ['base'=> 1,'type' => 1, 'id' => 'receipt_code', 'name' => '到货单号', 'unit' => '', 'show' => 1, 'condition' => 't2.receipt_code'],
                ['base'=> 1,'type' => 1, 'id' => 'department_name', 'name' => '采购部门', 'unit' => '', 'show' => 1, 'condition' => 't2.department_name'],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_name', 'name' => '供应商名称', 'unit' => '', 'show' => 1, 'condition' => 't2.supplier_name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_name', 'name' => '存货名称', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_model'],
                ['base'=> 1,'type' => 2, 'id' => 'quantity', 'name' => '报检数量', 'unit' => '', 'show' => 1, 'condition' => 't1.quantity'],
                ['base'=> 1,'type' => 1, 'id' => 'check_result', 'name' => '质检合格', 'unit' => '', 'show' => 1, 'condition' => 't1.check_result_flag'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '来料报检单', 'page_id' => 52, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        53 => [
            'page_name' => '发货汇总',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'outstock_code', 'name' => '发货单号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6, 'id' => 'outstock_date', 'name' => '发货日期', 'unit' => '', 'show' => 1, 'condition' => 't1.outstock_date'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_code', 'name' => '客户号', 'unit' => '', 'show' => 1, 'condition' => 't6.code'],
                ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名称', 'unit' => '', 'show' => 1, 'condition' => 't6.name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_code', 'name' => '存货编码', 'unit' => '', 'show' => 1, 'condition' => 't8.goods_code'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't5.name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 't5.code'],
                ['base'=> 1,'type' => 1, 'id' => 'inventory_unit', 'name' => '主计量单位', 'unit' => '', 'show' => 1, 'condition' => 't5.inventory_unit'],
                ['base'=> 1,'type' => 1, 'id' => 'shipped_quantity', 'name' => '发货数量', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'total_amount', 'name' => '发货价税合计', 'unit' => '', 'show' => 1, 'condition' => ''],

            ]
        ],
        54 => [
            'page_name' => '外委到货单管理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => '状态', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'receipt_code', 'name' => '单据号', 'unit' => '', 'show' => 1, 'condition' => 't1.receipt_code'],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_code', 'name' => '供应商编码', 'unit' => '', 'show' => 1, 'condition' => 't2.supplier_code'],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_name', 'name' => '供应商名称', 'unit' => '', 'show' => 1, 'condition' => 't1.supplier_name'],
                ['base'=> 1,'type' => 1, 'id' => 'currency', 'name' => '币种', 'unit' => '', 'show' => 1, 'condition' => 't1.currency'],
                ['base'=> 1,'type' => 2, 'id' => 'exchange_rate', 'name' => '汇率', 'unit' => '', 'show' => 1, 'condition' => 't1.exchange_rate'],
                ['base'=> 1,'type' => 6, 'id' => 'receipt_date', 'name' => '到货日期', 'unit' => '', 'show' => 1, 'condition' => 't1.receipt_date'],
                ['base'=> 1,'type' => 1, 'id' => 'department_name', 'name' => '部门', 'unit' => '', 'show' => 1, 'condition' => 't1.department_name'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
                ['base'=> 1,'type' => 1, 'id' => 'remark', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remark'],
                ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '采购到货单管理', 'page_id' => 50, 'show' => 1, 'condition' => 't1.ext_val'],
            ]
        ],
        55 => [
            'page_name' => '库存查询',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '物资编码', 'unit' => '', 'show' => 1, 'condition' => 'a.code'],
                ['base'=> 1,'type' => 1, 'id' => 'name', 'name' => '物资名称', 'unit' => '', 'show' => 1, 'condition' => 'a.name'],
                ['base'=> 1,'type' => 1, 'id' => 'type_name', 'name' => '物资类型', 'unit' => '', 'show' => 1, 'condition' => 'g.type_name'],
                ['base'=> 1,'type' => 1, 'id' => 'model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 'a.model'],
                ['base'=> 1,'type' => 2, 'id' => 'quantity', 'name' => '当前库存', 'unit' => '', 'show' => 1, 'condition' => 't.quantity'],
                ['base'=> 1,'type' => 1, 'id' => 'deputy_unit', 'name' => '数量单位', 'unit' => '', 'show' => 1, 'condition' => 'a.deputy_unit']
            ]
        ],
        56 => [
            'page_name' => '来料不良品处理',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => '状态', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '单据号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6, 'id' => 'handling_date', 'name' => '处理日期', 'unit' => '', 'show' => 1, 'condition' => 't1.handling_date'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_name', 'name' => '物料名称', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_model'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_unit', 'name' => '计量单位', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'defect_reasons_display', 'name' => '不良原因', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'handling_method_name', 'name' => '处理方式', 'unit' => '', 'show' => 1, 'condition' => 't1.handling_method_name'],
                ['base'=> 1,'type' => 2, 'id' => 'available_qty', 'name' => '可入库数量', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 2, 'id' => 'rejected_qty', 'name' => '不可入库数量', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_name', 'name' => '供应商名称', 'unit' => '', 'show' => 1, 'condition' => 't1.supplier_name'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name']
            ]
        ],
        57 => [
            'page_name' => '到货拒收单',
            'header_data' => [
                ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => '主键ID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'UUID', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => '状态', 'unit' => '', 'show' => 0, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '单据号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
                ['base'=> 1,'type' => 6, 'id' => 'rejection_date', 'name' => '拒收日期', 'unit' => '', 'show' => 1, 'condition' => 't1.rejection_date'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_name', 'name' => '物料名称', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_name'],
                ['base'=> 1,'type' => 1, 'id' => 'goods_model', 'name' => '规格型号', 'unit' => '', 'show' => 1, 'condition' => 't1.goods_model'],
                ['base'=> 1,'type' => 1, 'id' => 'rejection_reasons_dis', 'name' => '拒收原因', 'unit' => '', 'show' => 1, 'condition' => 't1.rejection_reasons_dis'],
                ['base'=> 1,'type' => 2, 'id' => 'rejection_quantity', 'name' => '拒收数量', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'goods_unit', 'name' => '计量单位', 'unit' => '', 'show' => 1, 'condition' => ''],
                ['base'=> 1,'type' => 1, 'id' => 'supplier_name', 'name' => '供应商名称', 'unit' => '', 'show' => 1, 'condition' => 't1.supplier_name'],
                ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name']
            ]
        ],
    ];

    const PAGE_COMMON_COLUMN = [
        'purchase_order' => [
            ['base'=> 0, 'type' => 1,'id' => 'id','name' => 'id','unit' => '','show' => 0,'condition' => ''],
            ['base'=> 0, 'type' => 1,'id' => 'uid','name' => 'uid','unit' => '','show' => 0,'condition' => ''],
            ['base'=> 0, 'type' => 1,'id' => 'status','name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 1, 'type' => 1,'id' => 'order_code','name' => '订单号', 'unit' => '', 'show' => 1, 'condition' => 't1.order_code'],
            ['base'=> 1, 'type' => 6,'id' => 'order_date', 'name' => '订单日期', 'unit' => '', 'show' => 1, 'condition' => 't1.order_date'],
            ['base'=> 1, 'type' => 6,'id' => 'finish_date', 'name' => '订单完结日期', 'unit' => '', 'show' => 1, 'condition' => 'substring(t1.finish_date, 1, 10)'],
            ['base'=> 1, 'type' => 1,'id' => 'supplier_name', 'name' => '供应商', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
            ['base'=> 1, 'type' => 1,'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
            ['base'=> 1, 'type' => 2, 'id' => 'total_weight', 'name' => '总重量', 'unit' => '', 'show' => 1, 'condition' => 't1.total_weight'],
            ['base'=> 1, 'type' => 2, 'id' => 'total_money', 'name' => '未税金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.total_money'],
            ['base'=> 1, 'type' => 2, 'id' => 'total_money_hs', 'name' => '含税金额', 'unit' => '元', 'show' => 1, 'condition' => 't1.total_money_hs'],
            ['base'=> 1, 'type' => 1,'id' => 'remarks', 'name' => '备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
            ['base'=> 0, 'type' => 99,'id' => 'ext_val','name' => '','page_id'=> 24,'show' => 1,'condition' => 't1.ext_val']
        ],
        'mes_task_wait' => [
            ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 0,'type' => 1, 'id' => 'drawing_path', 'name' => 'drawing_path', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 0,'type' => 1, 'id' => 'drawing_status', 'name' => 'drawing_status', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 1,'type' => 1, 'id' => 'order_code', 'name' => '项目号', 'unit' => '', 'show' => 1, 'condition' => 't4.code'],
            ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名称', 'unit' => '', 'show' => 1, 'condition' => 't7.name'],
            ['base'=> 1,'type' => 1, 'id' => 'product_code', 'name' => '产品编号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
            ['base'=> 1,'type' => 1, 'id' => 'product_name', 'name' => '产品名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
            ['base'=> 1,'type' => 6, 'id' => 'deliver_date', 'name' => '交付日期', 'unit' => '', 'show' => 1, 'condition' => 't1.deliver_date'],
            ['base'=> 1,'type' => 1, 'id' => 'group_name', 'name' => '生产车间', 'unit' => '', 'show' => 1, 'condition' => 't9.name'],
            ['base'=> 1,'type' => 1, 'id' => 'input_type_name', 'name' => '报工类型', 'unit' => '', 'show' => 1, 'condition' => 't.input_type_name'],
            ['base'=> 1,'type' => 1, 'id' => 'product_user_name', 'name' => '技术负责人', 'unit' => '', 'show' => 1, 'condition' => 't5.real_name'],
            ['base'=> 1,'type' => 1, 'id' => 'model_name', 'name' => '型号名称', 'unit' => '', 'show' => 1, 'condition' => 't8.name'],
            ['base'=> 1,'type' => 1, 'id' => 'name', 'name' => '工艺名称', 'unit' => '', 'show' => 1, 'condition' => 't1.name'],
            ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '工艺说明', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
            ['base'=> 1,'type' => 2, 'id' => 'plan_cnt', 'name' => '订单数量', 'unit' => '个', 'show' => 1, 'condition' => 't1.cnt'],
            ['base'=> 1,'type' => 2, 'id' => 'wait_cnt', 'name' => '待产数量', 'unit' => '个', 'show' => 1, 'condition' => 't1.wait_cnt'],
            ['base'=> 1,'type' => 2, 'id' => 'finish_cnt', 'name' => '完成数量', 'unit' => '个', 'show' => 1, 'condition' => 't6.cnt'],
            ['base'=> 1,'type' => 1, 'id' => 'product_remarks', 'name' => '产品备注', 'unit' => '', 'show' => 1, 'condition' => 't2.remarks'],
            ['base'=> 1,'type' => 99, 'id' => 'product_ext_val', 'name' => '产品', 'page_id' => 4, 'show' => 1, 'condition' => 't2.ext_val'],
            ['base'=> 1,'type' => 99, 'id' => 'order_detail_ext_val', 'name' => '订单', 'page_id' => 3, 'show' => 1, 'condition' => 't3.ext_val']
        ],
        'trade_order' => [
            ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 0,'type' => 1, 'id' => 'status', 'name' => 'status', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '订单编号', 'unit' => '', 'show' => 1, 'condition' => 't1.code'],
            ['base'=> 1,'type' => 1, 'id' => 'customer_name', 'name' => '客户名称', 'unit' => '', 'show' => 1, 'condition' => 't2.name'],
            ['base'=> 1,'type' => 1, 'id' => 'order_type_name', 'name' => '订单类型', 'unit' => '', 'show' => 1, 'condition' => 't1.order_type_name'],
            ['base'=> 1,'type' => 6, 'id' => 'sign_date', 'name' => '下单日期', 'unit' => '', 'show' => 1, 'condition' => 't1.sign_date'],
            ['base'=> 1,'type' => 1, 'id' => 'remarks', 'name' => '订单备注', 'unit' => '', 'show' => 1, 'condition' => 't1.remarks'],
            ['base'=> 1,'type' => 1, 'id' => 'status_name', 'name' => '状态', 'unit' => '', 'show' => 1, 'condition' => 't1.status_name'],
            ['base'=> 1,'type' => 99, 'id' => 'ext_val', 'name' => '', 'page_id' => 2, 'show' => 1, 'condition' => 't1.ext_val'],
            ['base'=> 1,'type' => 99, 'id' => 'customer_ext_val', 'name' => '客户管理', 'page_id' => 1, 'show' => 1, 'condition' => 't2.ext_val'],
        ]
    ];
}