<?php

namespace Envsan\Modules\Equ\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class EquFaultHistory extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $fault_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $equ_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $equ_code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $fault_status;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=false)
     */
    public $fault_describe;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'equ_fault_history';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquFaultHistory[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquFaultHistory
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
