<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInspection;
use Envsan\Modules\Purchase\Model\PurchaseInspectionDetail;
use Envsan\Modules\Purchase\Model\PurchaseReceipt;
use Envsan\Modules\Purchase\Model\PurchaseReceiptDetail;
use Envsan\Modules\Purchase\Util\Constant;

class PurchaseReceiptService extends BaseService
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseReceipt', 't1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseReceipt();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        return $this->executeInTransaction(function() use ($row, $act) {

            // save:1,commit:
            $type = $this->request->getPost('type', 'tstring');
            // 业务类型
//            $business_type = $this->request->getPost('business_type', 'tstring');
            // 业务类型
//            $business_type_name = $this->request->getPost('business_type_name', 'tstring');
            // 单据号前缀
            $re_code = $this->request->getPost('re_code', 'tstring');
            // 单据号采番
            $code = $this->request->getPost('code', 'tstring');
            // 到货日期
            $receipt_date = $this->request->getPost('receipt_date', 'tstring');
            // 采购订单ID
            $order_id = $this->request->getPost('order_id', 'tstring');
            // 采购订单code
            $order_code = $this->request->getPost('order_code', 'tstring');
            // 采购订单ID
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');
            // 采购订单code
            $supplier_code = $this->request->getPost('supplier_code', 'tstring');
            // 采购订单ID
            $supplier_name = $this->request->getPost('supplier_name', 'tstring');
            // 部门id
            $department_id = $this->request->getPost('department_id', 'tstring');
            // 部门名字
            $department_name = $this->request->getPost('department_name', 'tstring');
            // 币种
            $currency = $this->request->getPost('currency', 'tstring');
            // 汇率
            $exchange_rate = $this->request->getPost('exchange_rate', 'tstring');
            // 采购单明细
            $detail_data = $this->request->getPost('detail_data');
            // 备注
            $remarks = $this->request->getPost('remarks', 'tstring');

            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));

            // 判断必须输入项目
            if (empty($code) || empty($receipt_date) ||
                empty($detail_data) || empty($currency) || empty($exchange_rate) || CheckUtil::is_empty($order_id)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }

            if (!CheckUtil::isDecimalCommon($exchange_rate, 6)) {
                return $this->error('无效的汇率');
            }

            foreach ($detail_data as $detail)
            {
                if (!CheckUtil::isDecimal($detail['quantity'])) {
                    return $this->error('无效的数量');
                }
            }
            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);

//            $row->business_type = $business_type;
//            $row->business_type_name = $business_type_name;
            $row->receipt_code = $re_code . $code;
            $row->receipt_date = $receipt_date;
            $row->order_id = CvtUtil::blankToNull($order_id);
            $row->order_code = $order_code;
            $row->supplier_id = CvtUtil::blankToNull($supplier_id);
            $row->supplier_code = $supplier_code;
            $row->supplier_name = $supplier_name;
            $row->department_id = $department_id;
            $row->department_name = $department_name;
            $row->currency = $currency;
            $row->exchange_rate = $exchange_rate;
            $row->detail_data = CvtUtil::arrayToNull($detail_data);
            $row->remarks = $remarks;
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;

            // 新增,并且是save的场合
            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->status = 10;
                $row->status_name = Constant::$purchase_receipt_status[$row->status];
                $row->owner = $user->owner;
            }
            // 提交审批
            if ($type == 2) {
                $row->status = 20;
                $row->status_name = Constant::$purchase_receipt_status[$row->status];
            }
            $row->save();

            if ($type == 2) {
                foreach ($detail_data as $detail) {
                    $purchaseReceiptDetail = new PurchaseReceiptDetail();
                    // 基础信息
                    $purchaseReceiptDetail->uid = UUID::make();
                    $purchaseReceiptDetail->receipt_id = $row->id;
                    // 物料信息
                    $purchaseReceiptDetail->goods_id = $detail['goods_id'] ?? null;
                    $purchaseReceiptDetail->goods_code = $detail['goods_code'] ?? null;
                    $purchaseReceiptDetail->goods_name = $detail['goods_name'] ?? null;
                    $purchaseReceiptDetail->goods_model = $detail['goods_model'] ?? null;
                    $purchaseReceiptDetail->goods_unit = $detail['goods_unit'] ?? null;
                    $purchaseReceiptDetail->goods_deputy_unit = $detail['goods_deputy_unit'] ?? null;
                    // 数量和价格信息
                    $purchaseReceiptDetail->quantity = CvtUtil::blankToNull($detail['quantity']);
                    $purchaseReceiptDetail->purchase_quantity = CvtUtil::blankToNull($detail['purchase_quantity']);
                    $purchaseReceiptDetail->unit_conversion_rate = CvtUtil::blankToNull($detail['unit_conversion_rate']);
                    $purchaseReceiptDetail->price = CvtUtil::blankToNull($detail['price']);
                    $purchaseReceiptDetail->price_hs = CvtUtil::blankToNull($detail['price_hs']);
                    $purchaseReceiptDetail->total_amount = CvtUtil::blankToNull($detail['total_money']);
                    $purchaseReceiptDetail->tax_rate = CvtUtil::blankToNull($detail['tax_rate']);
                    $purchaseReceiptDetail->tax_amount = CvtUtil::blankToNull($detail['tax_money']);
                    $purchaseReceiptDetail->total_amount_hs = CvtUtil::blankToNull($detail['total_money_hs']);
                    // 检验信息
                    $purchaseReceiptDetail->check_status = $detail['check_status'] ?? null;
                    $purchaseReceiptDetail->check_flag = $detail['check_flag'] ?? 0;
                    $purchaseReceiptDetail->quality_template_id = CvtUtil::blankToNull($detail['quality_template_id']);
                    // 来源信息
                    $purchaseReceiptDetail->order_detail_id = $detail['id'] ?? null;
                    $purchaseReceiptDetail->apply_id = $detail['apply_id'] ?? null;
                    $purchaseReceiptDetail->apply_code = $detail['apply_code'] ?? null;
                    // 标准字段
                    $purchaseReceiptDetail->del_flag = 0;
                    $purchaseReceiptDetail->group_id = $user->group_id;
                    $purchaseReceiptDetail->owner = $user->owner;
                    $purchaseReceiptDetail->update_date = $now;
                    $purchaseReceiptDetail->update_by = $user->id;
                    // 状态信息
                    $purchaseReceiptDetail->status = 10; // 初始状态
                    $purchaseReceiptDetail->status_name = Constant::$purchase_receipt_status[10];
                    // 保存明细
                    $purchaseReceiptDetail->save();
                }
            }
        });
    }


    /**
     * 审核通过
     * @return array
     */
    public function approval()
    {
        return $this->executeInTransaction(function () {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $purchaseReceipt = $this->selectByUid($uid);

            if (empty($purchaseReceipt)) {
                return $this->error('单据不存在');
            }

            $now = DateUtil::now();
            $user = SessionData::user();
            $purchaseReceipt->comment = $comment;
            $purchaseReceipt->approved_by = $user->id;
            $purchaseReceipt->update_date = $now;
            $purchaseReceipt->update_by = $user->id;
            $purchaseReceipt->status = 30;
            $purchaseReceipt->status_name = Constant::$purchase_receipt_status[$purchaseReceipt->status];
            $purchaseReceipt->save();

            // 获取不需要报检的明细
            $receiptDetailNoCheck = PurchaseReceiptDetail::find(['receipt_id = ?1 and del_flag = 0', 'bind' => [1 => $purchaseReceipt->id]]);
            foreach ($receiptDetailNoCheck as $i => $detail) {
                $detail->update_date = $now;
                $detail->update_by = $user->id;
                $detail->status = 30;
                $detail->status_name = Constant::$purchase_receipt_status[$detail->status];
                $detail->save();
            }

            // 获取需要报检的明细
            $receiptDetailCheck = PurchaseReceiptDetail::find(['receipt_id = ?1 and del_flag = 0 and check_flag = 1', 'bind' => [1 => $purchaseReceipt->id]]);
            // 来料报检单号
            $inspection_type_code = 'LLBT' . date('ym');
            // 拿到当前最大号
            // 返回的是下一个可用号，比如 '0005'
            $inspection_max_code = $this->getInspectionCode($inspection_type_code);
            $inspection_code = $inspection_type_code . $inspection_max_code;

            // 来料检验单号
            $check_type_code = 'LLTY' . date('ym');
            // 拿到当前最大号
            // 返回的是下一个可用号，比如 '0005'
            $check_max_code = $this->getCheckCode($check_type_code);
            $max_num = intval($check_max_code);

            if ($receiptDetailCheck->count() > 0) {
                // 插入报检单
                $inspection_id = $this->insertOrUpdateInspection($inspection_code, $now, $purchaseReceipt, $user);
                foreach ($receiptDetailCheck as $i => $detail) {
                    // 插入报检单明细
                    $this->insertOrUpdateInspectionDetail($detail, $now, $user, $inspection_id, $max_num, $check_type_code);
                    $max_num++;
                }
            }
        });
    }

    /**
     * 审核驳回
     * @return array
     */
    public function reject()
    {
        return $this->executeInTransaction(function() {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $purchaseReceipt = $this->selectByUid($uid);
            if (empty($purchaseReceipt)) {
                return $this->error('单据不存在');
            }

            $now = DateUtil::now();
            $user = SessionData::user();
            $purchaseReceipt->comment = $comment;
            $purchaseReceipt->approved_by = $user->id;
            $purchaseReceipt->update_date = $now;
            $purchaseReceipt->update_by = $user->id;
            $purchaseReceipt->status = 10;
            $purchaseReceipt->status_name = '以驳回';
            $purchaseReceipt->save();

            $purchaseReceiptDetails = PurchaseReceiptDetail::find(['receipt_id = ?1 and del_flag = 0', 'bind' => [1 => $purchaseReceipt->id]]);
            foreach ($purchaseReceiptDetails as $i => $detail) {
                $detail->delete();
            }
        });
    }

    /**
     * 更新或者插入一条报检单
     * @param $max_num
     * @param $i
     * @param $type_code
     * @param $now
     * @param $purchaseReceipt
     * @return int
     */
    private function insertOrUpdateInspection($inspection_code, $now, $purchaseReceipt, $user): int
    {
        $purchaseInspection = new PurchaseInspection();

        $purchaseInspection->uid = UUID::make();
        // 编码
        $purchaseInspection->inspection_code = $inspection_code;
        // 报检日期
        $purchaseInspection->inspection_day = DateUtil::today();
        // 报检时间
        $purchaseInspection->inspection_date = $now;
        // 报检部门
        $purchaseInspection->inspection_department = $purchaseReceipt->department_name;
        // TODO
        $purchaseInspection->check_type = '采购检验';
        $purchaseInspection->department_id = $purchaseReceipt->department_id;
        $purchaseInspection->department_name = $purchaseReceipt->department_name;
        // 到货信息
        $purchaseInspection->receipt_id = $purchaseReceipt->id;
        $purchaseInspection->receipt_code = $purchaseReceipt->receipt_code;
        $purchaseInspection->receipt_date = $purchaseReceipt->receipt_date;
        // 供应商信息
        $purchaseInspection->supplier_id = $purchaseReceipt->supplier_id;
        $purchaseInspection->supplier_code = $purchaseReceipt->supplier_code;
        $purchaseInspection->supplier_name = $purchaseReceipt->supplier_name;
        // 状态信息
        $purchaseInspection->status = 30;
        $purchaseInspection->status_name = Constant::$purchase_inspection_status[$purchaseInspection->status];
        // 标准字段
        $purchaseInspection->del_flag = 0;
        $purchaseInspection->group_id = $user->group_id;
        $purchaseInspection->owner = $user->owner;
        $purchaseInspection->update_date = $now;
        $purchaseInspection->update_by = $user->id;
        // 保存
        $purchaseInspection->save();
        return $purchaseInspection->id;
    }

    /**
     * 更新或者插入一条报检单明细
     * @param $max_num
     * @param $i
     * @param $type_code
     * @param $now
     * @param $purchaseReceipt
     * @return void
     */
    private function insertOrUpdateInspectionDetail($detail, $now, $user, $inspection_id, $max_num ,$check_max_code)
    {
        $inspectionDetail = new PurchaseInspectionDetail();
        // 检验单号
        $check_code = $check_max_code .str_pad($max_num, 4, '0', STR_PAD_LEFT);

        $inspectionDetail->uid = UUID::make();
        // 到货单ID
        $inspectionDetail->inspection_id = $inspection_id;
        // 到货单明细ID
        $inspectionDetail->receipt_detail_id = $detail->id;
        // 物料信息
        $inspectionDetail->goods_id = $detail->goods_id;
        $inspectionDetail->goods_code = $detail->goods_code;
        $inspectionDetail->goods_name = $detail->goods_name;
        $inspectionDetail->goods_model = $detail->goods_model;
        $inspectionDetail->goods_unit = $detail->goods_unit;
        $inspectionDetail->purchase_quantity = $detail->purchase_quantity;
        $inspectionDetail->goods_deputy_unit = $detail->goods_deputy_unit;
        $inspectionDetail->quantity = $detail->quantity;

        // 来料检验内容初始化
        $inspectionDetail->check_status = 10;
        $inspectionDetail->check_images = null;
        $inspectionDetail->check_code = $check_code;
        $inspectionDetail->quality_template_id = CvtUtil::blankToNull($detail->quality_template_id);

        // 状态信息
        $inspectionDetail->status = 10;
        $inspectionDetail->status_name = Constant::$purchase_inspection_status[$inspectionDetail->status];
        // 标准字段
        $inspectionDetail->del_flag = 0;
        $inspectionDetail->group_id = $user->group_id;
        $inspectionDetail->owner = $user->owner;
        $inspectionDetail->update_date = $now;
        $inspectionDetail->update_by = $user->id;
        // 保存
        $inspectionDetail->save();
    }

    public function getOrders()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.order_code,
                t2.id as supplier_id,
                t2.code as supplier_code,
                t2.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            // 先处理订单状态是采购的，不是外委的
            ->where('t1.del_flag = 0 and t1.order_type = 1 and t1.owner = ?1 and t1.status < 40', [1 => SessionData::ownerId()])
            ->orderBy('t1.status asc, t1.order_code desc');
        return $builder->getQuery()->execute()->toArray();

    }

    /**
     * 判断是否重复
     * @param $name
     * @param $id
     * @return bool
     */
    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseReceipt')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return PurchaseReceipt::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseReceipt::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    /**
     * 取得当前供应商所有没完成的采购单的采购单明细
     * @param $supplier_id
     * @param $ids
     */
    public function selectPurchaseOrderDetail($order_id, $ids)
    {
        // 物料编码
        $goods_code = $this->request->get('goods_code', 'tstring');
        // 物料名称
        $goods_name = $this->request->get('goods_name', 'tstring');
        // 规格型号
        $goods_model = $this->request->get('goods_model', 'tstring');
        // 采购订单号
        $order_code = $this->request->get('order_code', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                p1.id,
                p1.order_id,
                p1.price,
                p1.price_hs,
                p1.quantity,
                p1.purchase_quantity,
                p1.tax_rate,
                p2.order_code,
                p4.id as goods_id,
                p4.code as goods_code,
                p4.name as goods_name,
                p4.model as oods_model,
                p4.unit as goods_unit,
                p4.deputy_unit as goods_deputy_unit,
                p4.unit_conversion_rate,
                p1.total_money,
                p1.total_money_hs,
                p1.total_money_hs - p1.total_money as tax_money,
                p1.check_flag,
                IF(p1.check_flag = 1, \'是\', \'否\') as check_status,
                0 as sel,
                p5.quantity as int_cnt,
                p5.purchase_quantity as int_purchase_cnt,
                p4.quality_template_id
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrderDetail', 'p1')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 'p1.order_id = p2.id and p2.del_flag = 0', 'p2')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseGoods', 'p4.id = p1.goods_id and p4.del_flag = 0', 'p4')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewOrderInstock', 'p2.id = p5.order_id and p1.goods_id = p5.goods_id', 'p5')
            ->where('p1.del_flag = 0 and p1.owner = ?1 and p2.id = ?2 and p2.status < ?3', [1 => SessionData::user()->owner, 2 => $order_id, 3 => '40']);

        if (!empty($ids)) {
            $builder->notInWhere('p1.id', explode(',', $ids));
        }

        if (!empty($goods_code)) {
            $builder->andWhere("p1.goods_code = ?3", [3 => $goods_code]);
        }

        if (!empty($goods_name)) {
            $builder->andWhere("p1.goods_name = ?4", [4 => $goods_name]);
        }

        if (!empty($goods_model)) {
            $builder->andWhere("p1.goods_model = ?5", [5 => $goods_model]);
        }

        if (!empty($order_code)) {
            $builder->andWhere("p2.order_code = ?6", [6 => $order_code]);
        }
        return $builder;
    }

    /**
     * 最大的到货编号
     * @param $type_code
     * @return string
     */
    public function getReceiptCode($type_code): string
    {
        $receipt_row = PurchaseReceipt::findFirst([
            'receipt_code like ?1 and length(receipt_code) = ?2',
            'bind' => [1 => "$type_code%", 2 => strlen($type_code) + 4],
            'order' => 'receipt_code desc'
        ]);
        if (empty($receipt_row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($receipt_row->receipt_code, strlen($type_code))) + 1;
        }
        return str_pad($cnt, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 最大的报检单编号
     * @param $type_code
     * @return string
     */
    public function getInspectionCode($type_code): string
    {
        $inspection_row = PurchaseInspection::findFirst([
            'inspection_code like ?1 and length(inspection_code) = ?2',
            'bind' => [1 => "$type_code%", 2 => strlen($type_code) + 4],
            'order' => 'inspection_code desc'
        ]);
        if (empty($inspection_row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($inspection_row->inspection_code, strlen($type_code))) + 1;
        }
        return str_pad($cnt, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 最大的报检单编号
     * @param $type_code
     * @return string
     */
    public function getCheckCode($type_code): string
    {
        $check_row = PurchaseInspectionDetail::findFirst([
            'check_code like ?1 and length(check_code) = ?2',
            'bind' => [1 => "$type_code%", 2 => strlen($type_code) + 4],
            'order' => 'check_code desc'
        ]);
        if (empty($check_row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($check_row->check_code, strlen($type_code))) + 1;
        }
        return str_pad($cnt, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 到货单中取得所有可以入库的详细
     * @param $id
     * @return mixed
     */
    public function getAllReceipts()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.receipt_code,
                t1.supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseReceipt', 't1')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseReceiptDetail', 't1.id = t2.receipt_id and t2.del_flag = 0 and t2.status != 40', 't2')
            ->where('t1.del_flag = 0 and t1.status = 30 and  t1.owner = ?1', [1 => SessionData::user()->owner])
            ->groupBy('t1.id, t1.receipt_code, t1.supplier_name');
        return $builder->getQuery()->execute();
    }
}