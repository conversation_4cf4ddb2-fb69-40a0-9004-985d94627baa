<?php
namespace Envsan\Modules\Equ\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\StringUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Equ\Model\EquFaultHistory;
use Envsan\Modules\Equ\Model\EquItem;
use Envsan\Modules\Equ\Model\EquItemShip;
use Envsan\Modules\Mes\Service\ProductService;
use Phalcon\Mvc\User\Component;

class EquItemService extends Component
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.name,
                t1.code,
                t1.status_name,
                t1.ext_val,
                t2.name as type_name
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItem', 't1')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItemType', 't1.type_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new EquItem();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = $this->request->getPost('name', 'tstring');
        $code = $this->request->getPost('code', 'tstring');
        $type_id = $this->request->getPost('type_id', 'tstring');
        $status_name = $this->request->getPost('status_name', 'tstring');
        $wages_ratio = $this->request->getPost('wages_ratio', 'tstring');
        $ship_list1 = $this->request->getPost('ship_list1');
        $ship_list2 = $this->request->getPost('ship_list2');
        $ship_list3 = $this->request->getPost('ship_list3');
        $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
        if (empty($name) || empty($code) || empty($type_id) || empty($status_name) || empty($wages_ratio))
            return ErrorHelper::WRONG_INPUT;

        if ($this->isRepeat($code, $row->id)) {
            return '编码重复';
        }
        $ts = new EquItemTypeService();
        $type_row = $ts->selectById($type_id);
        if (empty($type_row)) {
            return '设备类型不存在';
        }
        $ps = new ProductService();
        $ship_list = $ps->getShipTypes();
        $this->db->begin();
        try {
            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);
            $row->name = $name;
            $row->code = $code;
            $row->type_id = $type_id;
            $row->status_name = $status_name;
            $row->wages_ratio = CvtUtil::emptyToDouble($wages_ratio);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = 'equ' . mb_substr(UUID::make(),0,15);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("EquItem表更新失败");
            }
            $phql = 'DELETE FROM Envsan\Modules\Equ\Model\EquItemShip WHERE equ_id = ?1';
            $result = $this->modelsManager->executeQuery($phql, [1 => $row->id]);
            if (!$result->success()) {
                throw new \Exception('EquItemShip删除失败！');
            }
            foreach ($ship_list1 as $ship_id){
                foreach ($ship_list as $ship){
                    if ($ship_id == $ship['id']){
                        $item =  new EquItemShip();
                        $item->uid = UUID::make();
                        $item->equ_id = $row->id;
                        $item->ship_level = 1;
                        $item->ship_type_id = $ship['id'];
                        $item->ship_type_name = $ship['label'];
                        $item->update_date = $now;
                        $item->update_by = $user->id;
                        $item->del_flag = 0;
                        $item->owner = $user->owner;
                        if (!$item->save()) {
                            throw new \Exception("EquItemShip表更新失败");
                        }
                        break;
                    }
                }
            }
            if (!empty($ship_list2)){
                foreach ($ship_list2 as $ship_id){
                    foreach ($ship_list as $ship){
                        if ($ship_id == $ship['id']){
                            $item =  new EquItemShip();
                            $item->uid = UUID::make();
                            $item->equ_id = $row->id;
                            $item->ship_level = 2;
                            $item->ship_type_id = $ship['id'];
                            $item->ship_type_name = $ship['label'];
                            $item->update_date = $now;
                            $item->update_by = $user->id;
                            $item->del_flag = 0;
                            $item->owner = $user->owner;
                            if (!$item->save()) {
                                throw new \Exception("EquItemShip表更新失败");
                            }
                            break;
                        }
                    }
                }
            }
            if (!empty($ship_list3)){
                foreach ($ship_list3 as $ship_id){
                    foreach ($ship_list as $ship){
                        if ($ship_id == $ship['id']){
                            $item =  new EquItemShip();
                            $item->uid = UUID::make();
                            $item->equ_id = $row->id;
                            $item->ship_level = 3;
                            $item->ship_type_id = $ship['id'];
                            $item->ship_type_name = $ship['label'];
                            $item->update_date = $now;
                            $item->update_by = $user->id;
                            $item->del_flag = 0;
                            $item->owner = $user->owner;
                            if (!$item->save()) {
                                throw new \Exception("EquItemShip表更新失败");
                            }
                            break;
                        }
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    private function isRepeat($code, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Equ\Model\EquItem')
            ->where('del_flag = 0 and code = ?1 and owner = ?2', [1 => $code, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return EquItem::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return EquItem::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getViewData($uid)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.name,
                t1.code,
                t1.mac,
                t1.status_name,
                t1.qrcode_url,
                t1.tonnage,
                t1.ext_data,
                t2.name as type_name
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItem', 't1')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItemType', 't1.type_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid]);
        $rows = $builder->getQuery()->execute();
        return count($rows) > 0 ? $rows[0] : null;
    }

    public function getEquStatusList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.code,
                f.fault_level
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItem', 'a')
            ->innerJoin('Envsan\Modules\Equ\Model\EquItemType', 'a.type_id = t.id', 't')
            ->leftJoin('Envsan\Modules\Equ\Model\EquFault', 'a.fault_id = f.id', 'f')
            ->where('a.del_flag = 0 and a.status_name = \'在用\' and a.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('a.code');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            if (CheckUtil::is_empty($row['fault_level'])) {
                $row['status_class'] = 'green';
            } else if ($row['fault_level'] == 1) {
                $row['status_class'] = 'red';
            } else {
                $row['status_class'] = 'yellow';
            }
        }
        return $rows;
    }

    public function getEquFaultList()
    {
        $list = [];
        $rows = EquFaultHistory::find(['del_flag = 0', 'order' => 'create_date desc', 'limit' => 5])->toArray();
        foreach ($rows as &$row)
        {
            if ($row['fault_status'] == 10) {
                $str = '创建于';
            } else if ($row['fault_status'] == 20) {
                $str = '创建修理计划于';
            } else if ($row['fault_status'] == 30) {
                $str = '录入修理费用于';
            } else {
                $str = '解除于';
            }
            array_push($list, $row['create_date'].' - 设备 '.$row['equ_code'].' 故障'.$str.$row['create_date'].'：'.$row['fault_describe']);
        }
        return $list;
    }

    public function getList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.code
            ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItem', 't1')
            ->where('t1.del_flag = 0 and t1.status_name = \'正常\' and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.code');
        return $builder->getQuery()->execute();
    }

    public function getEquShips($equ_id){
       return EquItemShip::find(['del_flag = 0 and equ_id = ?1','bind'=>[1=>$equ_id]]);
    }
}