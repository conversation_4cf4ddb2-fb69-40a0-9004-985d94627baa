<?php

return [
    'header' => '采购中心',
    'id' => 'g-purchase',
    'icon' => ' icon-handbag',
    'module' => 'purchase',
    'active' => false,
    'items' => [
        ['name' => '供应商管理','controller'=>'supplier', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '供应商管理', 'url' => 'purchase/supplier/list', 'identity' => 'purchase:supplier:list']
        ]],
        ['name' => '物资管理','controller'=>'goodstype', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '物资管理', 'url' => 'purchase/goodstype/list', 'identity' => 'purchase:goodstype:list']
        ]],
        ['name' => '采购用料计划', 'controller' => 'plan', 'icon' => 'fa fa-fw fa-shopping-cart', 'items' => [
            ['name' => '采购用料计划', 'url' => 'purchase/plan/list', 'identity' => 'purchase:plan:list']
        ]],
        ['name' => '外委管理', 'controller'=>'orderww', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '外委计划', 'url' => 'purchase/orderww/plan', 'identity' => 'purchase:orderww:plan'],
            ['name' => '外委加工单管理', 'url' => 'purchase/orderww/list', 'identity' => 'purchase:orderww:list'],
            ['name' => '外委加工单查询', 'url' => 'purchase/orderww/search', 'identity' => 'purchase:orderww:search'],
            ['name' => '外委出库管理', 'url' => 'purchase/wwoutstock/list', 'identity' => 'purchase:wwoutstock:list'],
            ['name' => '外委出库查询', 'url' => 'purchase/wwoutstock/search', 'identity' => 'purchase:wwoutstock:search'],
            ['name' => '外委到货管理', 'url' => 'purchase/wwreceipt/list', 'identity' => 'purchase:wwreceipt:list'],
            ['name' => '外委入库管理', 'url' => 'purchase/wwinstock/list', 'identity' => 'purchase:wwinstock:list'],
            ['name' => '外委入库查询', 'url' => 'purchase/wwinstock/search', 'identity' => 'purchase:wwinstock:search'],
            ['name' => '外委工序价格管理', 'url' => 'purchase/wwprocess/list', 'identity' => 'purchase:wwprocess:list'],
        ]],
        ['name' => '采购申请', 'controller' => 'request', 'icon' => 'fa fa-fw fa-shopping-cart', 'items' => [
            ['name' => '采购申请管理', 'url' => 'purchase/request/list', 'identity' => 'purchase:request:list'],
            ['name' => '采购申请查询', 'url' => 'purchase/request/search', 'identity' => 'purchase:request:search']
        ]],
        ['name' => '采购订单','controller'=>'order', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '待采购查询', 'url' => 'purchase/order/wait', 'identity' => 'purchase:order:wait'],
            ['name' => '采购订单管理', 'url' => 'purchase/order/list', 'identity' => 'purchase:order:list'],
            ['name' => '采购订单查询', 'url' => 'purchase/order/search', 'identity' => 'purchase:order:search']
        ]],
        ['name' => '采购到货','controller'=>'order', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '采购到货管理', 'url' => 'purchase/receipt/list', 'identity' => 'purchase:receipt:list']
        ]],
        ['name' => '来料检验','controller'=>'order', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '来料报检单', 'url' => 'purchase/inspection/list', 'identity' => 'purchase:inspection:list'],
            ['name' => '原材料质检', 'url' => 'common/ext/materialcheck', 'identity' => 'purchase:inspection:list'],
            ['name' => '来料检验单', 'url' => 'purchase/inspection/checklist', 'identity' => 'purchase:inspection:checklist'],
            ['name' => '来料不良品处理单', 'url' => 'purchase/defecthandling/list', 'identity' => 'purchase:defecthandling:list'],
            ['name' => '到货拒收单', 'url' => 'purchase/arrivalrejection/list', 'identity' => 'purchase:arrivalrejection:list']
        ]],
        ['name' => '采购入库','controller'=>'instock', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '采购入库管理', 'url' => 'purchase/instock/list', 'identity' => 'purchase:instock:list'],
            ['name' => '采购入库查询', 'url' => 'purchase/instock/search', 'identity' => 'purchase:instock:search']
        ]],
        ['name' => '其他入库','controller'=>'other', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '其他入库管理', 'url' => 'purchase/other/list', 'identity' => 'purchase:other:list'],
            ['name' => '其他入库查询', 'url' => 'purchase/other/search', 'identity' => 'purchase:other:search']
        ]],
        ['name' => '领料出库','controller'=>'outstock', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '领料出库管理', 'url' => 'purchase/outstock/list', 'identity' => 'purchase:outstock:list'],
            ['name' => '领料出库查询', 'url' => 'purchase/outstock/search', 'identity' => 'purchase:outstock:search']
        ]],
        ['name' => '盘点管理','controller'=>'inventory', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '盘点管理', 'url' => 'purchase/inventory/list', 'identity' => 'purchase:inventory:list'],
            ['name' => '盘点查询', 'url' => 'purchase/inventory/search', 'identity' => 'purchase:inventory:search']
        ]],
        ['name' => '库存查询','controller'=>'stock', 'icon' => 'fa fa-fw fa-cubes', 'items' => [
            ['name' => '实时库存查询', 'url' => 'purchase/stock/list', 'identity' => 'purchase:stock:list'],
            ['name' => '库存履历查询', 'url' => 'purchase/stock/search', 'identity' => 'purchase:stock:search']
        ]],
        ['name' => '开票管理','controller'=>'invoice', 'icon' => 'fa fa-fw fa-list-alt', 'items' => [
            ['name' => '开票管理', 'url' => 'purchase/invoice/list', 'identity' => 'purchase:invoice:list'],
            ['name' => '开票查询', 'url' => 'purchase/invoice/search', 'identity' => 'purchase:invoice:search']
        ]],
        ['name' => '付款管理','controller' => 'pay', 'icon' => 'fa fa-fw fa-cny', 'items' => [
            ['name' => '付款管理', 'url' => 'purchase/pay/list', 'identity' => 'purchase:pay:list'],
            ['name' => '付款查询', 'url' => 'purchase/pay/search', 'identity' => 'purchase:pay:search']
        ]],
    ]
];