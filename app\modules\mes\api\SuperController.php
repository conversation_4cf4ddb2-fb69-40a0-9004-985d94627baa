<?php

namespace Envsan\Modules\Mes\Api;

use Envsan\Common\Base\ApiController;
use Envsan\Common\Data\SessionData;

class SuperController extends ApiController
{
    public function beforeExecuteRoute(\Phalcon\Mvc\Dispatcher $dispatcher)
    {
        // 不使用cookie
       // ini_set('session.use_cookies', '0');

        // 允许跨域
        header('Access-Control-Allow-Origin:*');

        // 设置所有的输出json格式
        $this->setJsonResponse();

        // 允许客户端上传SID作为session id
        header('Access-Control-Allow-Headers:SID, AUTH, Content-Type');

        // http会发送options查询各种支持的协议，die即可
        if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'OPTIONS')
            die();

//        SessionData::$_USER_KEY = 'paduser';
//        SessionData::$_OWNER_KEY = 'padowner';

        // 处理json体请求
        $this->handleRequestPayload();

        $controllerClass = $dispatcher->getControllerClass();
        // class级别的noacl
        $collection = $this->annotations->get($controllerClass)->getClassAnnotations();
        if ($collection != false && $collection->has('noacl'))
            return true;

        $annotations = $this->annotations->getMethod($controllerClass, $dispatcher->getActiveMethod());
        if ($annotations->has('noacl'))
            return true;

        if ($this->session->has(SessionData::$_USER_KEY))
            return true;

        $this->make4011();
        return false;
    }
}