<?php

namespace Envsan\Modules\Work\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class WorkDesign extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $doc_type;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $design_anchor;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $design_connects;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_list;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $form_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $data_status;


    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $flow_type;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_ids;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_error_ids;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $flow_more_ids;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $notify_flow_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $abstrakt_keys;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $hidden_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $check_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $money_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'work_design';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return WorkDesign[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return WorkDesign
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
