"use strict";
(self["webpackChunkrrts_manager"] = self["webpackChunkrrts_manager"] || []).push([["src_view_repair_edit_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/repair/edit.vue?vue&type=script&lang=js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/repair/edit.vue?vue&type=script&lang=js ***!
  \************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var vant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! vant */ "./node_modules/vant/es/dialog/index.js");

/* harmony default export */ __webpack_exports__["default"] = ({
  name: "repairEdit",
  data() {
    return {
      loading: false,
      uid: '',
      equ_code: '',
      begin_describe: '',
      repair_dt: '',
      repair_describe: '',
      repair_money: '',
      repair_money_describe: ''
    };
  },
  created() {
    this.uid = this.$route.params.uid || '';
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      this.$http.post_only('work/fault/repairmoneyinit', {
        uid: this.uid
      }).then(rs => {
        this.loading = false;
        if (rs.status == 'ok') {
          let data = rs.data;
          this.equ_code = data.equ_code;
          this.begin_describe = data.begin_describe;
          this.repair_dt = data.repair_dt;
          this.repair_describe = data.repair_describe;
          this.repair_money = data.repair_money;
          this.repair_money_describe = data.repair_money_describe;
        } else {
          vant__WEBPACK_IMPORTED_MODULE_0__["default"].alert({
            title: '提示',
            message: rs.message,
            confirmButtonText: '返回上一页'
          }).then(() => {
            this.$router.back();
          });
        }
      });
    },
    doSubmit() {
      if (!this.repair_money) {
        this.$toast.fail('请输入修理费用金额');
        return;
      }
      if (!this.repair_money_describe) {
        this.$toast.fail('请输入修理费用描述');
        return;
      }
      vant__WEBPACK_IMPORTED_MODULE_0__["default"].confirm({
        title: '提交',
        message: '确定提交吗？'
      }).then(() => {
        this.$http.post('work/fault/repairmoney', {
          uid: this.uid,
          repair_money: this.repair_money,
          repair_money_describe: this.repair_money_describe
        }).then(rs => {
          if (rs.status === 'ok') {
            this.$toast.success('提交成功！');
            this.$router.go(-1);
          } else {
            this.$toast.fail(rs.message);
          }
        }).catch(e => {
          this.$toast.fail('提交失败');
        });
      }).catch(() => {});
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/repair/edit.vue?vue&type=template&id=2ada848a":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/repair/edit.vue?vue&type=template&id=2ada848a ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "main"
  }, [_c('m-header', {
    attrs: {
      "name": "录入修理费用",
      "is_back": "1"
    }
  }), _c('m-body', [_vm.loading ? _c('div', {
    staticStyle: {
      "height": "100%",
      "display": "flex",
      "align-items": "center",
      "justify-content": "center"
    }
  }, [_c('van-loading', {
    attrs: {
      "size": "36px",
      "text-size": "16px",
      "vertical": ""
    }
  }, [_vm._v("加载中...")])], 1) : _c('div', {
    staticStyle: {
      "height": "100%",
      "display": "flex",
      "flex-direction": "column"
    }
  }, [_c('van-form', {
    staticStyle: {
      "flex": "1",
      "overflow-y": "auto"
    }
  }, [_c('van-field', {
    attrs: {
      "label": "设备",
      "type": "text",
      "readonly": ""
    },
    model: {
      value: _vm.equ_code,
      callback: function ($$v) {
        _vm.equ_code = $$v;
      },
      expression: "equ_code"
    }
  }), _c('van-field', {
    attrs: {
      "label": "故障现象",
      "type": "textarea",
      "readonly": ""
    },
    model: {
      value: _vm.begin_describe,
      callback: function ($$v) {
        _vm.begin_describe = $$v;
      },
      expression: "begin_describe"
    }
  }), _c('van-field', {
    attrs: {
      "label": "计划解除时间",
      "type": "text",
      "readonly": ""
    },
    model: {
      value: _vm.repair_dt,
      callback: function ($$v) {
        _vm.repair_dt = $$v;
      },
      expression: "repair_dt"
    }
  }), _c('van-field', {
    attrs: {
      "label": "解除计划",
      "type": "textarea",
      "readonly": ""
    },
    model: {
      value: _vm.repair_describe,
      callback: function ($$v) {
        _vm.repair_describe = $$v;
      },
      expression: "repair_describe"
    }
  }), _c('van-field', {
    attrs: {
      "label": "修理费用金额",
      "type": "number",
      "placeholder": "请输入修理费用金额",
      "maxlength": "10",
      "input-align": "right",
      "required": ""
    },
    scopedSlots: _vm._u([{
      key: "right-icon",
      fn: function () {
        return [_c('span', [_vm._v("元")])];
      },
      proxy: true
    }]),
    model: {
      value: _vm.repair_money,
      callback: function ($$v) {
        _vm.repair_money = $$v;
      },
      expression: "repair_money"
    }
  }), _c('van-field', {
    attrs: {
      "rows": "2",
      "autosize": "",
      "label": "修理费用描述",
      "type": "textarea",
      "maxlength": "200",
      "placeholder": "请输入修理费用描述",
      "input-align": "right",
      "show-word-limit": "",
      "required": ""
    },
    model: {
      value: _vm.repair_money_describe,
      callback: function ($$v) {
        _vm.repair_money_describe = $$v;
      },
      expression: "repair_money_describe"
    }
  })], 1), _c('div', [_c('van-button', {
    attrs: {
      "type": "warning",
      "icon": "success",
      "block": "",
      "size": "large"
    },
    on: {
      "click": _vm.doSubmit
    }
  }, [_vm._v("提交")])], 1)], 1)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./src/view/repair/edit.vue":
/*!**********************************!*\
  !*** ./src/view/repair/edit.vue ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _edit_vue_vue_type_template_id_2ada848a__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit.vue?vue&type=template&id=2ada848a */ "./src/view/repair/edit.vue?vue&type=template&id=2ada848a");
/* harmony import */ var _edit_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit.vue?vue&type=script&lang=js */ "./src/view/repair/edit.vue?vue&type=script&lang=js");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _edit_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _edit_vue_vue_type_template_id_2ada848a__WEBPACK_IMPORTED_MODULE_0__.render,
  _edit_vue_vue_type_template_id_2ada848a__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/view/repair/edit.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/view/repair/edit.vue?vue&type=script&lang=js":
/*!**********************************************************!*\
  !*** ./src/view/repair/edit.vue?vue&type=script&lang=js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_edit_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/repair/edit.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_edit_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/view/repair/edit.vue?vue&type=template&id=2ada848a":
/*!****************************************************************!*\
  !*** ./src/view/repair/edit.vue?vue&type=template&id=2ada848a ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_edit_vue_vue_type_template_id_2ada848a__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_edit_vue_vue_type_template_id_2ada848a__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_edit_vue_vue_type_template_id_2ada848a__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit.vue?vue&type=template&id=2ada848a */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/repair/edit.vue?vue&type=template&id=2ada848a");


/***/ })

}]);
//# sourceMappingURL=src_view_repair_edit_vue.js.map