<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseApply;
use Envsan\Modules\Purchase\Model\PurchaseRequest;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class PurchaseRequestService extends BaseService
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
            't1.status',
            't1.apply_code',
            't1.apply_date',
            't1.apply_type',
            't1.goods_cnt',
            't1.goods_name',
            't1.status_name',
            't1.remarks',
            't1.ext_val',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseRequest', 't1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        ModelUtil::limitGroup('t1.group_id', $builder);
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function setDetail($rows)
    {
        foreach ($rows as &$row)
        {
            $row['apply_type_name'] = Constant::$purchase_request_type_arr[$row['apply_type']];
        }
        return $rows;
    }

    public function create()
    {
        $row = new PurchaseRequest();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($row, $act) {
            $type = $this->request->getPost('type', 'tstring');
            $apply_date = $this->request->getPost('apply_date', 'tstring');
            // 采购类型：生产采购，库存采购
            $purchase_type = $this->request->getPost('purchase_type', 'tstring');
            $purchase_type_name = $this->request->getPost('purchase_type_name', 'tstring');
            // 生产批次id
            $notice_id = $this->request->getPost('notice_id', 'tstring');
            $product_id = $this->request->getPost('product_id', 'tstring');
            $notice_code = $this->request->getPost('notice_code', 'tstring');
            $remarks = $this->request->getPost('remarks', 'tstring');
            $files = $this->request->getPost('files');
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
            $detail_data = $this->request->getPost('detail_data');

            if (empty($apply_date) || empty($detail_data) || empty($purchase_type))
                return $this->error(ErrorHelper::WRONG_INPUT);

            if ($purchase_type == '1' && (empty($notice_id) || empty($product_id)))
                return $this->error(ErrorHelper::WRONG_INPUT);


            $goods_names = [];
            foreach ($detail_data as $detail) {
                if (!CheckUtil::isDecimal($detail['quantity'])) {
                    return  $this->error('无效的数量');
                }
                $goods_names[] = $detail['name'];
            }
            $goods_names = array_unique($goods_names);

            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);

            $row->apply_date = $apply_date;
            $row->purchase_type = $purchase_type;
            $row->purchase_type_name = $purchase_type_name;
            $row->notice_id = CvtUtil::blankToNull($notice_id);
            $row->product_id = CvtUtil::blankToNull($product_id);
            $row->notice_code = CvtUtil::blankToNull($notice_code);
            $row->goods_cnt = count($detail_data);
            $row->goods_name = implode(',', $goods_names);
            $row->detail_data = CvtUtil::arrayToNull($detail_data);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = CvtUtil::arrayToNull($table->getFormDataValue($ext_data));
            $row->files = CvtUtil::arrayToNull($files);
            $row->status = $type == 1 ? 10 : 20;
            $row->status_name = Constant::$purchase_request_status[$row->status];
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->apply_code = $ss->useSequence(7);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            $row->save();

            if ($type == 2) {
                foreach ($detail_data as $goods_item) {
                    $apply_row = new PurchaseApply();
                    $apply_row->uid = UUID::make();
                    $apply_row->request_id = $row->id;
                    $apply_row->goods_id = $goods_item['id'];
                    $apply_row->goods_uid = $goods_item['uid'];
                    $apply_row->goods_code = $goods_item['code'];
                    $apply_row->goods_name = $goods_item['name'];
                    $apply_row->goods_spec = $goods_item['spec'];
                    $apply_row->goods_model = $goods_item['model'];
                    $apply_row->supplier_name = $goods_item['supplier_name'];
                    $apply_row->goods_unit = $goods_item['unit'];
                    $apply_row->goods_deputy_unit = $goods_item['deputy_unit'];
                    $apply_row->quantity = $goods_item['quantity'];
                    $apply_row->purchase_quantity = $goods_item['purchase_quantity'];
                    $apply_row->status = 10;
                    $apply_row->status_name = Constant::$purchase_apply_status[$apply_row->status];
                    $apply_row->update_date = $now;
                    $apply_row->update_by = $user->id;
                    $apply_row->del_flag = 0;
                    $apply_row->owner = $user->owner;
                    $apply_row->save();
                }
            }
        });
    }

    public function selectById($id)
    {
        return PurchaseRequest::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseRequest::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return $this->error('数据不存在或者已删除');
        } else if ($row->status != 10) {
            return $this->error('采购申请已提交');
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        $row->save();
    }

    public function searchAll()
    {
        $column_arr = [
            'a.id',
            'a.uid',
            'a.goods_code',
            'a.goods_name',
            'a.goods_spec',
            'a.goods_model',
            'a.goods_unit',
            'a.goods_deputy_unit',
            'a.quantity',
            'a.purchase_quantity',
            'a.status',
            'a.status_name',
            'r.apply_code',
            'r.apply_date',
            'r.apply_type',
            'r.ext_val',
            'p.order_code as purchase_code',
            'pt.code as product_code',
            'pt.name as product_name',
            'o.code as order_code',
            'c.name as customer_name',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApply', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseRequest', 'a.request_id = r.id', 'r')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 'a.purchase_order_id = p.id', 'p')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 'r.product_id = pt.id', 'pt')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder', 'r.order_id = o.id', 'o')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 'r.customer_id = c.id', 'c')
            ->where('a.del_flag = 0 and a.request_id is not null and a.owner = '.SessionData::ownerId())
            ->orderBy('a.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function setSearchDetail($rows)
    {
        foreach ($rows as &$row)
        {
            $row['apply_type_name'] = Constant::$purchase_request_type_arr[$row['apply_type']];
        }
        return $rows;
    }

    public function getRequestData($uid)
    {
        $column_arr = [
            't1.apply_code',
            't1.apply_date',
            't1.apply_type',
            't1.goods_cnt',
            't1.goods_name',
            't1.purchase_type_name',
            't1.purchase_type',
            't1.notice_code',
            't1.detail_data',
            't1.remarks',
            't1.ext_data',
            't1.files',
            't2.code as product_code',
            't2.name as product_name',
            't3.code as order_code',
            't4.name as customer_name',

        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseRequest', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't1.product_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder', 't1.order_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't1.customer_id = t4.id', 't4')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid]);
        $rows = $builder->getQuery()->execute();
        return count($rows) > 0 ? $rows[0] : null;
    }

    public function getPurchaseGoods($notice_id, $product_id, $ids)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id as notice_id,
                t.code as notice_code,
                t2.id as product_id,
                t6.id,
                t6.uid,
                t6.deputy_unit,
                t6.unit,
                t6.price,
                t6.price_hs,
                round(t6.unit_conversion_rate,4) as unit_conversion_rate,
                t6.tax_rate,
                t6.code,
                t6.name,
                t6.model,
                t6.spec,
                t4.supplier_name,
                round(ifnull(t5.quantity, 0),4) as stock_cnt,
                round(sum(ifnull(t1.quantity, 0) * ifnull(t4.quantity, 0)),0) as notice_quantity
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNotice', 't')
            ->innerJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.id = t1.notice_id and t1.del_flag = 0', 't1')
            ->innerJoin('Envsan\Modules\Mes\Model\MesProduct', 't1.product_id = t2.id and t2.del_flag = 0', 't2')
            ->innerJoin('Envsan\Modules\Mes\Model\MesProductBom', 't2.id = t3.product_id and t3.del_flag = 0', 't3')
            ->innerJoin('Envsan\Modules\Mes\Model\MesProductGoods', 't3.id = t4.product_bom_id and t4.del_flag = 0', 't4')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewStock', 't4.goods_id = t5.goods_id', 't5')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseGoods', 't6.id = t4.goods_id', 't6')
            ->where('t.del_flag = 0 and t.owner = ?1 and t.id = ?2 and t.status = ?3 and t2.id = ?4',
                [1 => SessionData::user()->owner, 2 => $notice_id, 3 => '30', 4 => $product_id])
            ->groupBy('
                t.id,
                t.code,
                t2.id,
                t6.id,
                t6.deputy_unit,
                t6.unit,
                t6.price,
                t6.price_hs,
                t6.unit_conversion_rate,
                t6.tax_rate,
                t6.code,
                t6.name,
                t6.model,
                t4.supplier_name,
                t5.quantity
            ');

        if (!empty($ids)) {
            $builder->notInWhere('t4.goods_id', explode(',', $ids));
        }
        return $builder;
    }

    public function getProductList($notice_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduct', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1.id = t2.product_id and t2.del_flag = 0', 't2')
            ->where('t1.del_flag = 0 and t2.notice_id = ?1 and t2.status = ?2',  [1 => $notice_id, 2 => '10'])
            ->orderBy('t1.id asc');
        return $builder->getQuery()->execute()->toArray();
    }


    public function getRequestAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t.apply_code
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseRequest', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't.notice_id = t1.id and t1.del_flag = 0', 't1')
            ->where('t.del_flag = 0 and t.status = ?1 and (t1.id is null or t1.status = ?2)',  [1 => '20', 2 => '30'])
            ->orderBy('t.id asc');
        return $builder->getQuery()->execute();
    }
}