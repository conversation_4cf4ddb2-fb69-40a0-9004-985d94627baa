<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Mes\Model\MesShipType;
use Envsan\Modules\Mes\Model\MesShipField;
use Envsan\Modules\Mes\Util\Constant;
use Envsan\Modules\Sys\Model\SysDict;
use Envsan\Modules\Sys\Service\DictService;
use Phalcon\Mvc\User\Component;

class ShipService extends Component
{
    public function selectAllField(){
        $name = $this->request->get('name', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.*
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesShipField', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("t1.name like ?2", [2 => "%$name%"]);
        }
        return $builder;
    }

    public function selectAll(){
        $name = $this->request->get('name', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.plan_type,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesShipType', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("t1.name like ?2", [2 => "%$name%"]);
        }
        return $builder;
    }

    public function createField()
    {
        $row = new MesShipField();
        return $this->buildField('create', $row);
    }

    public function updateField($row)
    {
        return $this->buildField('update', $row);
    }

    public function buildField($act, $row)
    {
        $name = $this->request->getPost('name', 'tstring');
        $type = $this->request->getPost('type', 'tstring');
        $unit = $this->request->getPost('unit', 'tstring');
        $explain = $this->request->getPost('explain', 'tstring');
        $list = $this->request->getPost('list');
        if (empty($name) || empty($type)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        if ($act == 'create') {
            $field_row = MesShipField::findFirst(['del_flag = 0 and name = ?1 and owner = ?2', 'bind' => [1 => $name , 2 => $user->owner]]);
            if (!empty($field_row)){
                return '名称重复';
            }
        } else {
            $field_row = MesShipField::findFirst(['del_flag = 0 and name = ?1 and owner = ?2 and id <> ?3', 'bind' => [1 => $name , 2 => $user->owner ,3 => $row->id]]);
            if (!empty($field_row)){
                return '名称重复';
            }
        }
        $row->name = $name;
        $row->type = $type;
        $row->type_name = ConstantUtil::$input_types[$type];
        if ($type == 2){
            $row->unit = $unit;
        } else {
            $row->unit = null;
        }
        if ($type == 3 || $type == 4 || $type == 5){
            $row->list = json_encode($list, JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
        } else {
            $row->list = null;
        }
        $row->explain = CvtUtil::blankToNull($explain);
        $row->update_date = $now;
        $row->update_by = $user->id;
        if ($act == 'create') {
            $row->uid =  'f'.mb_substr(UUID::make(),0,10);
            $row->del_flag = 0;
            $row->owner = $user->owner;
        }
        if (!$row->save()) {
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function deleteFieldById(){
        $id = $this->request->getPost('id', 'tstring');
        if (empty($id)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = MesShipField::findFirst(['id = ?1', 'bind' => [1 => $id]]);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if (!$row->save()) {
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function selectById($id)
    {
        return MesShipType::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return MesShipType::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function selectFieldById($id)
    {
        return MesShipField::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function create()
    {
        $name = trim($this->request->getPost('name', 'string'));
        $plan_type = trim($this->request->getPost('plan_type', 'string'));
        if (empty($name) || empty($plan_type))
            return ErrorHelper::WRONG_INPUT;
        $user = SessionData::user();
        $check_row = MesShipType::findFirst(['del_flag = 0 and name = ?1 and owner = ?2', 'bind' => [1 => $name , 2 => $user->owner]]);
        if (!empty($check_row)){
            return '名称重复';
        }
        $row = new MesShipType();
        $row->uid = UUID::make();
        $row->name = $name;
        $row->plan_type = $plan_type;
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        $row->del_flag = 0;
        $row->owner = $user->owner;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function update($row){
        $data = urldecode($this->request->getPost('data', 'string'));
        $name = $this->request->getPost('name', 'string');
        $plan_type = trim($this->request->getPost('plan_type', 'string'));
        if (empty($name)){
            return ErrorHelper::WRONG_INPUT;
        }
        $data = json_decode($data,true);
        $user = SessionData::user();
        $row->name = $name;
        $row->plan_type = $plan_type;
        $row->form_data = json_encode($data,JSON_UNESCAPED_UNICODE);
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function delete(){
        $uid = $this->request->getPost('uid');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function selectFieldList(){
        $user = SessionData::user();
        $rows = MesShipField::find(['del_flag = 0 and owner = ?1', 'bind' => [1 => $user->owner]]);
        $rows = $rows->toArray();
        foreach ($rows as &$row){
            $row['list'] = CvtUtil::emptyToArray($row['list']);
        }
        return $rows;
    }

    public function getList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesShipType', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id');
        return $builder->getQuery()->execute();
    }


}