<div id="app" class="page-content">
    <div class="portlet light bordered">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-social-dribbble font-green"></i>
                <span class="caption-subject font-green bold">付款信息</span>
            </div>
        </div>
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body" style="min-height: 625px">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">付款单号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="pay_no" v-model="pay_no" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">采购订单</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="order_code" v-model="order_code" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">供应商</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">付款日期</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="pay_date" v-model="pay_date" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">开票金额</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="pay_money" v-model="pay_money" readonly/>
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ partial('view') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" class="form-control" v-model="remarks" rows="3" readonly></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                <thead>
                                <tr>
                                    <th>编码</th>
                                    <th>名称</th>
                                    <th>规格</th>
                                    <th>型号</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    <th>总价</th>
                                    <th>入库时间</th>
                                    <th>入库单号</th>
                                    <th>开票单号</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="row, index in detail_list">
                                    <td><span v-text="row.goods_code"></span></td>
                                    <td><span v-text="row.goods_name"></span></td>
                                    <td><span v-text="row.goods_spec"></span></td>
                                    <td><span v-text="row.goods_model"></span></td>
                                    <td><span v-text="row.quantity + '('+row.goods_unit+')' "></span></td>
                                    <td><span v-text="row.price + '(元/'+row.goods_unit+')' "></span></td>
                                    <td><span v-text="row.total_money + '(元)' "></span></td>
                                    <td><span v-text="row.instock_date"></span></td>
                                    <td><span v-text="row.instock_code"></span></td>
                                    <td><span v-text="row.invoice_no"></span></td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="row"v-if="detail_list.length == 0" style="text-align: center;margin-top: 15px">
                                <span>没有数据</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonPurchasePay }},
    });
</script>