<?php

namespace Envsan\Modules\Common\Controller;

/**
 * @skipacl
 */
class ExtController extends SuperController
{
    private $page_path = '/ext_1_0_3/';

    public function documentAction()
    {
        $version = $this->getVersion();
        $uid = $this->request->get('uid', 'tstring');
        $this->redirect($this->page_path."?v=$version#/document?uid=".$uid);
    }

    public function printAction()
    {
        $version = $this->getVersion();
        $uid = $this->request->get('uid', 'tstring');
        $doc_id = $this->request->get('doc_id', 'tstring');
        $this->redirect($this->page_path."?v=$version#/print?uid=".$uid . "&doc_id=" . $doc_id);
    }

    public function planAction()
    {
        $version = $this->getVersion();
        $uid = $this->request->get('uid', 'tstring');
        $this->redirect($this->page_path."?v=$version#/mingjingplan?uid=".$uid);
    }

    public function planviewAction()
    {
        $version = $this->getVersion();

        $this->redirect($this->page_path . "?v=$version#/mingjingplanview");
    }



    public function materialcheckAction()
    {
        $version = $this->getVersion();
        $this->redirect($this->page_path."?v=$version#/mingjingchecklist");
    }

    public function productcheckAction()
    {
        $version = $this->getVersion();
        $this->redirect($this->page_path. "?v=$version#/mingjingproducedetail");
    }

    private function getVersion()
    {
        $versionFile = $this->page_path . 'version.json';
        $version = time(); // 默认用时间戳

        if (file_exists($versionFile)) {
            $versionData = json_decode(file_get_contents($versionFile), true);
            $version = $versionData['hash']; // 使用构建hash：8491e955e542ee2e
        }

        return $version;
    }
}