<?php

namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Sys\Model\GroupConfig;
use Envsan\Modules\Sys\Service\GconfigService;
use Envsan\Modules\Sys\Service\GroupService;

/**
 * @name('组织数据')
 */
class GconfigController extends SuperController
{
    /**
     * @name('条目一览')
     */
    public function listAction()
    {
        $s = new GconfigService();
        $builder = $s->selectAll($this->request->get('content', ['string', 'trim']));
        $this->view->items = $builder->getQuery()->execute();

        // 默认设置
        $id = intval($this->request->get('id', 'int'));
        $gconfig = GroupConfig::findFirst('id=' . $id);
        $this->view->gconfig = $gconfig;

        // 角色所属组织的该项设定
        $this->view->ritems = $s->selectAllByGroupIds($gconfig->section, SessionData::groupIds());

        $gs = new GroupService();
        $this->view->group = $gs->selectById(SessionData::groupId());
    }

    /**
     * @admin
     * 创建默认设置
     */
    public function createAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new GconfigService();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }


        $jrow = (new GroupConfig())->toArray();
        $jrow['type'] = '';

        $this->view->jsonGroupConfig = json_encode($jrow);
    }

    /**
     * @name('条目一览')
     */
    public function editAction($id)
    {
        // 默认设置
        $id = intval($id);
        $defConfig = GroupConfig::findFirst('id=' . $id);
        if ($defConfig == null)
            die(ErrorHelper::WRONG_ID);

        $this->view->defConfig = $defConfig;

        // 该组织的设定
        $s = new GconfigService();
        $row = $s->get($defConfig->section, SessionData::groupId());

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->saveCustomizeData(SessionData::groupId(), $defConfig, $row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        if ($row != null)
            $this->view->val = $row->val;
        else
            $this->view->val = $defConfig->val;

    }

    /**
     * @admin
     * 修改默认的数据
     */
    public function editdefAction($id)
    {
        // 默认设置
        $id = intval($id);
        $row = GroupConfig::findFirst('id=' . $id);
        if ($row == null || $row->group_id != 0)
            die(ErrorHelper::WRONG_ID);

        // 该组织的设定
        $s = new GconfigService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->saveDefault($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $this->view->defConfig = $row;
        $this->view->val = $row->val;
        $this->view->pick('gconfig/edit');
    }

    /**
     * @name('删除条目')
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new GconfigService();
            $ret = new JsonData();
            $ret->message = $rs->deleteById(intval($this->request->getPost('id', 'int')));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

}