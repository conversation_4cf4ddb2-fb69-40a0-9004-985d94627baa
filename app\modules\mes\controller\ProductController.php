<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Service\ProductService;
use Envsan\Modules\Mes\Util\Constant;
use Envsan\Modules\Purchase\Service\GoodsService;
use Envsan\Modules\Purchase\Service\GoodsTypeService;

/**
 * @name("产品")
 */
class ProductController extends SuperController
{
    private $page_id = 4;

    /**
     * @name("查询")
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ProductService();
            $builder = $s->selectAll('search');
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }


    /**
     * @name("列表")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new ProductService();
            $builder = $s->selectAll('list');
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("工艺管理")
     */
    function createAction($uid)
    {
        $rs = new ProductService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->saveData($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $order_row = $rs->getOrderData($row->order_detail_id);
        if (empty($order_row)){
            die(ErrorHelper::WRONG_ID);
        }
        $oss_util = new FileService();
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);

        // 产品信息
        $jrow = $row->toArray();
        // 设定产品档案信息
        if ($row->goods_type_code) {
            $gts = new GoodsTypeService();
            $jrow['goods_type_text'] = $row->goods_type_code . ' ' . $gts->selectByCode($row->goods_type_code)->name;
        } else {
            $jrow['goods_type_text'] = '';
        }

        $jrow['tab_id'] = 1;
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['drawing_list'] = $rs->getDrawingList($row->id);
        //order_start
        $jrow['order_name'] = $order_row->name;
        $jrow['order_cnt'] = $order_row->cnt;
        $jrow['order_ext_data'] = CvtUtil::emptyToArray($order_row->ext_data);
        $jrow['order_files'] = CvtUtil::emptyToArray($order_row->files);
        $jrow['order_remarks'] = $order_row->remarks;
        $jrow['order_code'] = $order_row->order_code;
        $jrow['customer_name'] = $order_row->customer_name;
        //bom_start
        $jrow['bom_show'] = 0;
        $jrow['bom_tab_id'] = 1;
        $jrow['bom_data'] = (new MesProductBom())->toArray();
        $jrow['bom_ext_data'] = [];
        $jrow['select_drawing_id'] = '';
        $jrow['drawing_images'] = [];
        $jrow['select_card_id'] = '';
        $jrow['select_check_id'] = '';

        $gs = new GoodsTypeService();
        $this->view->tree_list = $gs->selectTree();
        $this->view->status = 10;
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
        $this->view->extDataFormName = 'bom_ext_data';
        $this->view->extDataFormCnt = 12;
        $this->view->extDataViewName = 'order_ext_data';
        $this->view->extDataViewCnt = 12;
        $this->view->shipTypes = $rs->getShipTypes();
        $this->view->checkList =$rs->getCheckList();
        $this->view->workTypes = Constant::$bom_work_types;
        $this->view->flowData = $jrow['flow_data'];
    }

    /**
     * @acl({'link':'mes:product:create'})
     */
    public function flowsaveAction($uid){
        $rs = new ProductService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->saveFlowData($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'mes:product:create'})
     */
    public function drawingdeleteAction(){
        $s = new ProductService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->drawingDelete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'mes:product:create'})
     */
    public function savebomAction()
    {
        if($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $s = new ProductService();
            $rtn = $s->saveBom();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function getbomAction($uid){
        $rs = new ProductService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $rtn = $rs->getBomData($row);
            $ret->message = $rtn->message;
            if (empty($rtn->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function getbomviewAction($uid){
        $rs = new ProductService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $rtn = $rs->getBomData($row,false);
            $ret->message = $rtn->message;
            if (empty($rtn->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function drawinglistAction($uid){
        $rs = new ProductService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            return json_encode($rs->getDrawingList($row->id));
        }
    }

    /**
     * @acl({'link':'mes:product:create'})
     */
    public function deletebomAction()
    {
        if($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $s = new ProductService();
            $ret->message = $s->deleteBom();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $rs = new ProductService();
        // 产品信息
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        // 订单明细信息
        $order_row = $rs->getOrderData($row->order_detail_id);
        if (empty($order_row)){
            die(ErrorHelper::WRONG_ID);
        }
        $oss_util = new FileService();
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        // 产品信息
        $jrow = $row->toArray();
        // 设定产品档案信息
        if ($row->goods_type_code) {
            $gts = new GoodsTypeService();
            $jrow['goods_type_text'] = $row->goods_type_code . ' ' . $gts->selectByCode($row->goods_type_code)->name;
        } else {
            $jrow['goods_type_text'] = '';
        }
        // tab也切换
        $jrow['tab_id'] = 1;
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        // 图纸
        $jrow['drawing_list'] =$rs->getDrawingList($row->id);
        // 订单明细信息
        $jrow['order_name'] = $order_row->name;
        $jrow['order_cnt'] = $order_row->cnt;
        $jrow['order_deliver_date'] = $order_row->deliver_date;
        $jrow['order_ext_data'] = CvtUtil::emptyToArray($order_row->ext_data);
        $jrow['order_files'] = CvtUtil::emptyToArray($order_row->files);
        $jrow['order_remarks'] = $order_row->remarks;
        $jrow['order_code'] = $order_row->order_code;
        $jrow['customer_name'] = $order_row->customer_name;
        // bom信息
        $jrow['bom_show'] = 0;
        $jrow['bom_tab_id'] = 1;
        $jrow['bom_data'] = (new MesProductBom())->toArray();
        $jrow['bom_ext_data'] = [];
        $gs = new GoodsTypeService();
        $this->view->tree_list = $gs->selectTree();
        $this->view->status = 10;
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $row->uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
        $this->view->extDataViewName = 'order_ext_data';
        $this->view->extDataViewCnt = 12;
        $this->view->extDataView3Name = 'bom_ext_data';
        $this->view->extDataView3Cnt = 12;
        $this->view->shipTypes = $rs->getShipTypes();
        $this->view->workTypes = Constant::$bom_work_types;
        $this->view->flowData = $jrow['flow_data'];
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new ProductService();
        $builder = $s->selectAll('list');
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     */
    public function exportsearchAction()
    {
        $s = new ProductService();
        $builder = $s->selectAll('search');
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }
}