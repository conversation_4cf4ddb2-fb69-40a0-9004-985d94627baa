<?php
namespace Envsan\Common\Base;

use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Package;
use Envsan\Modules\Sys\Model\Res;

class InstallerBase extends ControllerBase
{
    public function makePackage($shortName, $name, $version, $comment='')
    {
        $row = Package::findFirst('short_name = \''.$shortName.'\'');
        if ($row){
            $row->name = $name;
            $row->update_date = DateUtil::now();
            $row->comment = $comment;
            $row->version = $version;
        } else {
            $row = new Package();
            $row->short_name = $shortName;
            $row->name = $name;
            $row->update_date = DateUtil::now();
            $row->comment = $comment;
            $row->version = $version;
        }
        $row->save();
        return $row;
    }

    public function makeModule($identity, $name)
    {
        $row = Res::findFirst('identity =  \''.$identity.'\'');
        if ($row){
            $row->name = $name;
            $row->update_date = DateUtil::now();
        } else {
            $row = new Res();
            $row->uid = UUID::make();
            $row->pid = 0;
            $row->type = 'module';
            $row->name = $name;
            $row->identity = $identity;
            $row->update_date = DateUtil::now();
        }
        $row->save();
        return $row;
    }

    public function makeController($module, $identity, $name)
    {
        $row = Res::findFirst('identity =  \''.$identity.'\'');
        if ($row){
            $row->pid = $module->id;
            $row->name = $name;
            $row->update_date = DateUtil::now();
        } else {
            $row = new Res();
            $row->uid = UUID::make();
            $row->pid = $module->id;
            $row->type = 'controller';
            $row->name = $name;
            $row->identity = $identity;
            $row->update_date = DateUtil::now();
        }
        $row->save();
        return $row;
    }

    public function makeAct($controller, $identity, $name, $comment)
    {
        $row = Res::findFirst('identity =  \''.$identity.'\'');
        if ($row){
            $row->pid = $controller->id;
            $row->name = $name;
            $row->comment = $comment;
            $row->update_date = DateUtil::now();
        } else {
            $row = new Res();
            $row->uid = UUID::make();
            $row->pid = $controller->id;
            $row->type = 'action';
            $row->name = $name;
            $row->comment = $comment;
            $row->identity = $identity;
            $row->update_date = DateUtil::now();
        }
        $row->save();
        return $row;
    }

    public function moduleExistThenDie($name)
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>$name]]);
        if ($row != null)
            return '该模块已安装';
        return '';
    }
}
