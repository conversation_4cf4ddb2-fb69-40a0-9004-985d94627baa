{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('css').addCss('static/global/plugins/viewer/viewer.css') %}
{% do assets.collection('js').addJs('static/global/plugins/viewer/viewer.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-table.css') %}
{% do assets.collection('css').addCss('static/pages/css/whms/ticket-view.css') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.worker.js') %}
<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-md-6">
            <div class="portlet light" style="padding-bottom: 1px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-blue"></i>
                        <span class="caption-subject font-blue bold">图号：{{ code }}</span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center">
                        <div class="btn yellow" style="width: 100px;" @click="addVersion"><i class="fa fa-plus"></i>&nbsp;添加版本</div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="table-list" style="min-height: 77vh">
                        <div style="display: flex;flex-direction: column;justify-content: center">
                            <div class="zh-table">
                                <div class="zh-table-header">
                                    <div class="zh-col" style="width: 8%">版本号</div>
                                    <div class="zh-col" style="width: 10%">状态</div>
                                    <div class="zh-col" style="width: 20%">文件名</div>
                                    <div class="zh-col" style="width: 24%">版本说明</div>
                                    <div class="zh-col" style="width: 19%">上传时间</div>
                                    <div class="zh-col" style="width: 8%">上传人</div>
                                    <div class="zh-col" style="width: 11%">操作</div>
                                </div>
                                <div v-show="version_list.length > 0" class="zh-table-body">
                                    <div v-for="(t, index) in version_list" class="zh-row" style="border-bottom: 1px solid #ddd;">
                                        <div class="zh-col" style="width: 8%;">
                                            <div v-text="t.code" :class="t.use_status == 1 ? 'font-green-meadow' : ''"
                                                 style="font-weight: bold" :title="t.use_status == 1 ? '使用中' : ''"></div>
                                        </div>
                                        <div class="zh-col" style="width: 10%;">
                                            <span v-if="t.status == 30" class="font-green-meadow">通过审核</span>
                                            <span v-if="t.status == 20" class="font-yellow">审核中</span>
                                            <span v-if="t.status == 10 && t.reject_status == 0" class="font-blue">待提交</span>
                                            <span v-if="t.status == 10 && t.reject_status == 1" class="font-red">驳回</span>
                                        </div>
                                        <div class="zh-col" style="width: 20%" v-text="t.drawing_name"></div>
                                        <div class="zh-col" style="width: 24%" v-text="t.remarks"></div>
                                        <div class="zh-col" style="width: 19%" v-text="t.upload_time"></div>
                                        <div class="zh-col" style="width: 8%" v-text="t.upload_user"></div>
                                        <div class="zh-col" style="width: 11%">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                                    操作 <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu" role="menu">
                                                    <li v-if="t.status == 10"><a href="javascript:" @click="editVersion(t)"><i class="fa fa-fw fa-edit"></i>&nbsp;编辑</a></li>
                                                    <li><a href="javascript:" @click="show_pic(t)"><i class="fa fa-fw fa-eye"></i>&nbsp;查看图纸</a></li>
                                                    <li><a href="javascript:" @click="showReview(t)"><i class="fa fa-fw fa-anchor"></i>&nbsp;查看审批</a></li>
                                                    <li v-if="t.status == 10"><a href="javascript:" @click="deleteVersion(t)"><i class="fa fa-fw fa-times"></i>&nbsp;删除</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div  v-show="version_list.length == 0"  class="empty-data">
                                    <span class="caption-helper">暂无数据</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="pdf-viewer">
                <div class="toolbar">
                    <button @click="prevPage" :disabled="pageNumber <= 1">上一页</button>
                    <span>第 ${pageNumber} 页 / 共 ${numPages} 页</span>
                    <button @click="nextPage" :disabled="pageNumber >= numPages">下一页</button>
                    <button @click="fontChange(1)">放大</button>
                    <button @click="fontChange(2)">缩小</button>
                </div>
                <div class="pdf-container">
                    <canvas id="pdfCanvas"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var viewer = '';
    var app = new Vue({
        el: '#app',
        data: {
            version_list: {{ version_list }},
            base_path: '{{ base_path }}',
            pdfDoc: null,
            pageNumber: 1,
            numPages: 0,
            scale: 1,
            renderTask: null,
            pageRendering: false,
            pageNumPending: null
        },
        methods: {
            show_pic: function (item) {
                let src = app.base_path + item.drawing_url;
                pdfjsLib.getDocument(src).promise.then((pdfDoc_) => {
                    this.pdfDoc = pdfDoc_;
                    this.numPages = this.pdfDoc.numPages;
                    this.renderPage();
                });
            },
            fontChange(type){
                if (type == 1){
                    if (this.scale >= 3){
                        return;
                    }
                    this.scale += 0.25
                } else {
                    if (this.scale <= 0.5){
                        return;
                    }
                    this.scale -= 0.25
                }
                this.renderPage();
            },
            renderPage() {
                if (!this.pdfDoc) return;
                this.pageRendering = true;
                if (this.renderTask) {
                    this.renderTask.cancel();
                }
                this.pdfDoc.getPage(this.pageNumber).then((page) => {
                    const viewport = page.getViewport({ scale: this.scale });
                    const canvas = document.getElementById('pdfCanvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
                    this.renderTask = page.render({
                        canvasContext: context,
                        viewport: viewport
                    });
                    this.renderTask.promise.then(() => {
                        this.pageRendering = false;
                        // 如果有等待中的页面渲染请求
                        if (this.pageNumPending !== null) {
                            this.pageNumber = this.pageNumPending;
                            this.pageNumPending = null;
                            this.renderPage();
                        }
                    });
                });
            },
            queueRenderPage(num) {
                if (this.pageRendering) {
                    this.pageNumPending = num;
                } else {
                    this.pageNumber = num;
                    this.renderPage();
                }
            },
            prevPage() {
                if (this.pageNumber <= 1) return;
                this.queueRenderPage(this.pageNumber - 1);
            },
            nextPage() {
                if (this.pageNumber >= this.numPages) return;
                this.queueRenderPage(this.pageNumber + 1);
            },
            addVersion:function () {
                top.window.layer_result2 = '';
                top.layer.open({
                    title: '上传版本',
                    type: 2,
                    area: ['50em', '80%'],
                    content: '{{ url('mes/drawing/upload/' ~ uid) }}',
                    end: function() {
                        if (top.window.layer_result2 == 'ok') {
                            top.window.layer_result = 'ok'
                            app.getList();
                        }
                    }
                });
            },
            editVersion(item) {
                top.window.layer_result2 = '';
                top.layer.open({
                    title: '编辑版本',
                    type: 2,
                    area: ['50em', '60%'],
                    content: '{{ url('mes/drawing/uploadedit/' ~ uid) }}' + '/' + item.uid,
                    end: function() {
                        if (top.window.layer_result2 == 'ok') {
                            top.window.layer_result = 'ok'
                            app.getList();
                        }
                    }
                });
            },
            getList: function() {
                showSpin();
                var url= '{{ url('mes/drawing/version/') ~ uid}}';
                $.post(url, {}, function (rs) {
                    closeSpin(null);
                    app.version_list = rs;
                })
            },
            deleteVersion: function (item) {
                var dlg = top.layer.confirm('确认删除吗?', function() {
                    top.layer.close(dlg);
                    showSpin();
                    $.post("{{ url('mes/drawing/deleteversion/') ~ uid}}", {version_id: item.id}, function(rs) {
                        closeSpin(dlg);
                        if (rs.status == 'ok') {
                            app.getList();
                        } else {
                            toastr.error('操作失败!');
                        }
                    })
                });
            },
            showReview(item) {
                top.layer.open({
                    title: '审批流程',
                    type: 2,
                    area: ['100%', '100%'],
                    content: "{{ url('work/flow/view/3/') }}" + item.id,
                });
            }
        }
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });

</script>
<style>
    .empty-data {
        text-align: center;
        font-size: 25px;
        padding: 5px;
        color: #a2a2a2;
    }

    .factory-table {
        margin-bottom: 0;
        width: 100%;
        margin-top: 10px;

    }

    .factory-table > thead > tr > th, .factory-table > tbody > tr > td {
        border: 3px solid #F2F2F2;
        height: 35px;
        padding: 5px;
        background-color: #ffffff;
    }

    .factory-table > thead > tr > th {
        font-weight: normal;
        background-color: #3598DC;
    }

    .sort-type div {
        display: flex;
        align-items: center;
        margin-left: 15px;
    }

    .sort-type span {
        margin-left: 5px;
    }

    .sort-type i {
        font-size: 30px;
    }

    .row {
        margin-bottom: 10px;
    }

    .img-panel {
        height: 100%;
        background: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 30px;
        color: #BFBFBF;
        padding: 20px;
        position: relative;
    }

    .zh-table .zh-col {
        align-items: flex-start;
    }

    .icon-status {
        width: 8px;
        height: 8px;
        border-radius: 100%;
        background-color: #26C281;
        margin-left: 5px;
    }

    .pdf-viewer {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .toolbar {
        padding: 10px;
        background: #f0f0f0;
        display: flex;
        gap: 10px;
        align-items: center;
        height: 5vh;;
    }

    .pdf-container {
        width: 100%;
        height: 85vh;
        overflow: auto;
        border: 1px solid #ddd;
        background-color: #fff;
    }

    canvas {
        display: block;
        margin: 0 auto;
    }
</style>