<script>
    function isInt(num) {
        return /^\d+$/.test(num);
    }

    function isDecimal(num) {
        return /^\d+(\.\d+)?$/.test(num);
    }

    function isPrice(num, maxlength) {
        if (!maxlength) {
            maxlength = 8;
        }

        var maxval = '1';
        for (var i = 0; i < maxlength - 3; i++) {
            maxval += '0';
        }

        return !(!num || !isDecimal(num) || Number(num) >= Number(maxval));
    }

    function getPriceMsg(title, maxlength) {
        if (!maxlength) {
            maxlength = 8;
        }

        var maxval = '1';
        for (var i = 0; i < maxlength - 3; i++) {
            maxval += '0';
        }
        return "请输入有效的" + title + "：<br>大于0且小于" + maxval + "，保留2位小数";
    }

    function isWeight(num, maxlength) {
        if (!maxlength) {
            maxlength = 8;
        }

        var maxval = '1';
        for (var i = 0; i < maxlength - 4; i++) {
            maxval += '0';
        }

        return !(!num || !isDecimal(num) || Number(num) >= Number(maxval));
    }

    function isDecimalLimit(num, maxlength, point_length) {
        if (!maxlength) {
            maxlength = 8;
        }

        if (!point_length) {
            point_length = 2;
        }

        var maxval = '1';
        for (var i = 0; i < maxlength - (point_length + 1); i++) {
            maxval += '0';
        }

        return !(!num || !isDecimal(num) || Number(num) >= Number(maxval));
    }

    function getDecimalLimitMsg(title, maxlength, point_length) {
        if (!maxlength) {
            maxlength = 8;
        }

        if (!point_length) {
            point_length = 2;
        }

        var maxval = '1';
        for (var i = 0; i < maxlength - (point_length + 1); i++) {
            maxval += '0';
        }
        return "请输入有效的<b class=\"font-blue\">" + title + "</b>："
            + "<br>① 大于0且小于<b class=\"font-red\">" + parseMoney(maxval, point_length) + "</b>；"
            + "<br>② 保留<b class=\"font-red\">" + point_length + "</b>位小数。";
    }

    function parseMoney(money, n) {
        if (money == 'undefined' || money == null || money == '0' || money == undefined || money == "" || parseFloat(money) == 0) {
            return '0.00';
        } else {
            if (money > 0) { //金额为大于0
                n = n >= 0 && n <= 20 ? n : 2;
                money = parseFloat((money + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
                var l = money.split(".")[0].split("").reverse();
                var r = '';
                if (n > 0){
                    r = money.split(".")[1];
                }
                var t = "";
                for (i = 0; i < l.length; i++) {
                    t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
                }
                if (r == ''){
                    return t.split("").reverse().join("");
                } else {
                    return t.split("").reverse().join("") + "." + r;
                }
            } else { //金额小于0
                n = n >= 0 && n <= 20 ? n : 2;
                money = parseFloat((money + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
                var l = money.split(".")[0].split("").reverse();
                l.pop();
                var r = '';
                if (n > 0){
                    r = money.split(".")[1];
                    if (r == undefined){
                        r = '00';
                    }
                }
                var t = "";
                for (i = 0; i < l.length; i++) {
                    t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
                }
                if (r == ''){
                    return '-' + t.split("").reverse().join("");
                } else {
                    return '-' + t.split("").reverse().join("") + "." + r;
                }
            }
        }
    }

    function nullToBlank(val){
        if (val == 'undefined' || val == null || val == undefined || val == "") {
            return '';
        }else{
            return val;
        }
    }
</script>