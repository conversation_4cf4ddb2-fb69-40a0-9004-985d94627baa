<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Printing\Service\TemplateService;
use Envsan\Modules\Purchase\Model\PurchaseOutstock;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\OutstockService;

/**
 * @name('领料出库')
 */
class OutstockController extends SuperController
{
    private $page_id = 26;
    private $search_page_id = 31;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList($this->page_id),JSON_UNESCAPED_UNICODE);
    }

    /**
     * @name('mes列表')
     */
    public function meslistAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList($this->page_id),JSON_UNESCAPED_UNICODE);
        $this->view->pick('outstock/list');
    }

    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @acl({'link':'purchase:outstock:list,purchase:outstock:meslist'})
     */
    public function createAction($from)
    {
        $rs = new OutstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->create($from));
            return json_encode($ret);
        } else {
            $table = new TableService();
            $jrow = (new PurchaseOutstock())->toArray();
            $jrow['outstock_date'] = DateUtil::today();
            $jrow['outstock_user'] = SessionData::user()->real_name;
            $jrow['ext_data'] = $table->getFormData($this->page_id);
            $jrow['detail_data'] = [];
            $jrow['files'] = [];
            // 工艺对应选择的物料
            $jrow['goods_list'] = [];
            // 物料筛选使用
            $jrow['goods'] = '';
            // 供应商筛选使用
            $jrow['supplier'] = '';
            $jrow['type_uid'] = '';
            $jrow['type_pid'] = '';
            $jrow['bom_key'] = '';
            $jrow['order_code'] = '';
            $jrow['product_name'] = '';
            $jrow['bom_name'] = '';
            $jrow['productList'] = [];
            $this->view->noticeList = $rs->getNotices();
            $gs = new GoodsTypeService();
            $this->view->jsonTree = $gs->selectTree();
            $this->view->jsonOutstock = json_encode($jrow);
            $this->view->extDataName = 'ext_data';
            $this->view->extDataCnt = 6;
            $this->view->page_id = $this->page_id;
            $this->view->from = $from;
        }

    }

    /**
     * @skipacl
     */
    public function bomGoodsAction()
    {
        $this->setJsonResponse();
        $rs = new OutstockService();
        $jsonData = new JsonData();
        $jsonData->handleResult($rs->getGoodsList());
        return json_encode($jsonData);
    }

    /**
     * @acl({'link':'purchase:outstock:list,purchase:outstock:meslist'})
     */
    function editAction($from, $uid)
    {
        $rs = new OutstockService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($row->status > 10)
            die('已提交入库');
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->update($row, $from));
            return json_encode($ret);
        }

        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['goods_list'] = [];
        $jrow['goods'] = '';
        $jrow['supplier'] = '';
        $jrow['type_uid'] = '';
        $jrow['type_pid'] = '';
        $jrow['bom_key'] = '';
        $jrow['order_code'] = '';
        $jrow['product_name'] = '';
        $jrow['customer_name'] = '';
        $this->view->noticeList = $rs->getNotices();
        $jrow['productList'] = $rs->getProductList($jrow['notice_id']);
        if (!empty($jrow['order_bom_id'])){
            $bom_row = $rs->getBomDataByID($jrow['order_bom_id']);
            if (!empty($bom_row)){
                $jrow['order_code'] = $bom_row->order_code;
                $jrow['product_name'] = $bom_row->product_name;
                $jrow['customer_name'] = $bom_row->customer_name;
                $jrow['bom_name'] = $bom_row->bom_name;
            }
        }
        $gs = new GoodsTypeService();
        $this->view->jsonTree = $gs->selectTree();
        $this->view->jsonOutstock = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
        $this->view->from = $from;
        $this->view->pick('outstock/create');
    }

    /**
     * @skipacl
     */
    public function getbomAction(){
        if($this->request->isPost()){
            $rs = new OutstockService();
            $this->setJsonResponse();
            $ret = new JsonData();
            $rtn = $rs->getBomData();
            $ret->message = $rtn->message;
            if (empty($rtn->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:outstock:list,purchase:outstock:meslist'})
     */
    public function deleteAction(){
        $s = new OutstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:outstock:list,purchase:outstock:meslist'})
     */
    public function cancelAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OutstockService();
            $ret = new JsonData();
            $ret->handleResult($s->cancelReview());
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $rs = new OutstockService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        $jrow = $row->toArray();
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = $rs->getDetail($jrow['id']);
        $jrow['order_code'] = '';
        $jrow['product_name'] = '';
        $jrow['customer_name'] = '';
        $jrow['bom_name'] = '';
        if (!empty($jrow['order_bom_id'])){
            $bom_row = $rs->getBomDataByID($jrow['order_bom_id']);
            if (!empty($bom_row)){
                $jrow['order_code'] = $bom_row->order_code;
                $jrow['product_name'] = $bom_row->product_name;
                $jrow['customer_name'] = $bom_row->customer_name;
                $jrow['bom_name'] = $bom_row->bom_name;
            }
        }
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonOutstock = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new OutstockService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new OutstockService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }

//    /**
//     * @acl({'link':'purchase:outstock:list,purchase:outstock:meslist'})
//     * 取得产品对应的工艺
//     * @return false|string|void
//     */
//    public function changeAction()
//    {
//        $rs = new OutstockService();
//        if($this->request->isPost()){
//            $product_id = $this->request->getPost('product_id', 'int');
//            $this->setJsonResponse();
//            $ret = new JsonData();
//            $ret->handleResult($rs->changeProduct($product_id));
//            return json_encode($ret);
//        }
//    }

    /**
     * @skipacl
     * 取得产品对应的工艺
     * @return false|string|void
     */
    public function changenoticeAction()
    {
        $rs = new OutstockService();
        if ($this->request->isPost()) {
            $notice_id = $this->request->getPost('notice_id', 'int');
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->getProductList($notice_id));
            return json_encode($ret);
        }
    }

}