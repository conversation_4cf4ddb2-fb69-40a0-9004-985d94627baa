<?php
/**
 * Created by PhpStorm.
 * User: zhuh
 * Date: 2017/9/13
 * Time: 8:58
 */

namespace Envsan\Common\Util;


class CvtUtil
{
    public static function toChsMoney($num) {
        $c1 = "零壹贰叁肆伍陆柒捌玖";
        $c2 = "分角元拾佰仟万拾佰仟亿";
        //精确到分后面就不要了，所以只留两个小数位
        $num = round($num, 2);
        //将数字转化为整数
        $num = $num * 100;

        $i = 0;
        $c = "";
        while (1) {
            if ($i == 0) {
                //获取最后一位数字
                $n = substr($num, strlen($num)-1, 1);
            } else {
                $n = $num % 10;
            }
            //每次将最后一位数字转化为中文
            $p1 = substr($c1, 3 * $n, 3);
            $p2 = substr($c2, 3 * $i, 3);
            if ($n != '0' || ($n == '0' && ($p2 == '亿' || $p2 == '万' || $p2 == '元'))) {
                $c = $p1 . $p2 . $c;
            } else {
                $c = $p1 . $c;
            }
            $i = $i + 1;
            //去掉数字最后一位了
            $num = $num / 10;
            $num = (int)$num;
            //结束循环
            if ($num == 0) {
                break;
            }
        }
        $j = 0;
        $slen = strlen($c);
        while ($j < $slen) {
            //utf8一个汉字相当3个字符
            $m = substr($c, $j, 6);
            //处理数字中很多0的情况,每次循环去掉一个汉字“零”
            if ($m == '零元' || $m == '零万' || $m == '零亿' || $m == '零零') {
                $left = substr($c, 0, $j);
                $right = substr($c, $j + 3);
                $c = $left . $right;
                $j = $j-3;
                $slen = $slen-3;
            }
            $j = $j + 3;
        }
        //这个是为了去掉类似23.0中最后一个“零”字
        if (substr($c, strlen($c)-3, 3) == '零') {
            $c = substr($c, 0, strlen($c)-3);
        }
        //将处理的汉字加上“整”
        if (empty($c)) {
            return "零元整";
        }else{
            return $c . "整";
        }
    }

    public static function toChsNum($num) {
        if (empty($num)) {
            return "零";
        } else {
            $arr_num = explode(".", $num);
            $num_chs = str_replace("元整", "", CvtUtil::toChsMoney($arr_num[0]));
            if (count($arr_num) > 1 && intval($arr_num[1]) != 0) {
                $arr_chs_num = array("零","壹","贰","叁","肆","伍","陆","柒","捌","玖");
                $decimal = str_replace(array_keys($arr_chs_num), $arr_chs_num, $arr_num[1]);
                $num_chs = $num_chs.'点'.$decimal;
            }
            return $num_chs;
        }
    }

    public static function nullToBlank($str) {
        if ($str === null) {
            return '';
        } else {
            return $str;
        }
    }

    public static function emptyToZero($str) {
        if (empty($str)) {
            return 0;
        } else {
            return $str;
        }
    }

    public static function emptyToStr($str) {
        if (empty($str)) {
            return '';
        } else {
            return strval($str);
        }
    }

    public static function emptyToDouble($str) {
        if (empty($str)) {
            return 0;
        } else {
            return doubleval($str);
        }
    }

    public static function emptyToInt($str) {
        if (empty($str)) {
            return 0;
        } else {
            return intval($str);
        }
    }

    public static function emptyToArray($str)
    {
        if (empty($str)) {
            return [];
        } else {
            $arr = json_decode($str, true);
            return empty($arr) ? [] : $arr;
        }
    }

    public static function arrayToNull($arr)
    {
        if (count($arr) == 0) {
            return null;
        } else {
            return json_encode($arr, JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
        }
    }


    public static function strToArray($str)
    {
        if (empty($str)) {
            return [];
        } else {
            return preg_split("/,/",$str);
        }
    }

    public static function blankToNull($str) {
        if ($str === '') {
            return null;
        } else {
            return $str;
        }
    }

    public static function zeroToNull($str) {
        if ($str == 0) {
            return null;
        } else {
            return $str;
        }
    }

    public static function toFixed($num, $prec)
    {
        return sprintf('%.'.$prec.'f', round($num, $prec));
    }

    public static function toMoney($num, $prec = 0)
    {
        return number_format($num, $prec, '.', ',');
    }

    public static function percentToDecimal($num)
    {
        if (CheckUtil::is_empty($num)) {
            return null;
        }
        return $num / 100;
    }

    public static function formatDt($dt)
    {
        $date = substr($dt, 0, 10);
        $time = substr($dt, 11, 5);

        if ($date == DateUtil::today()) {
            $dt_new = '今天 '.$time;
        } else {
            $dt_new = $date;
        }
        return $dt_new;
    }

    public static function formatBankNo($bank_no) {
        if (empty($bank_no)) {
            return $bank_no;
        } else {
            $bank_no = str_replace(' ', '', $bank_no);
            $idx = 0;
            $bank_no_new = '';
            while ($idx < strlen($bank_no)) {
                $bank_no_temp = substr($bank_no, $idx, 4);
                $bank_no_new .= $bank_no_temp.' ';
                $idx += 4;
            }
            return trim($bank_no_new, ' ');
        }
    }

    public static function readExcelDouble($val, $prec = 0)
    {
        return round(CvtUtil::emptyToDouble(trim($val)), $prec);
    }

    /**
     * 将Excel时间数字转换为PHP日期时间
     * @param mixed $excelDate Excel时间数字或字符串
     * @param string $format 输出格式，默认为 'Y-m-d H:i:s'
     * @return string|null 转换后的日期时间字符串，如果转换失败返回null
     */
    public static function excelToDateTime($excelDate, $format = 'Y-m-d H:i:s')
    {
        // 如果为空，返回null
        if (empty($excelDate)) {
            return null;
        }

        // 如果已经是日期时间字符串，直接返回
        if (is_string($excelDate) && preg_match('/^\d{4}-\d{2}-\d{2}/', $excelDate)) {
            return $excelDate;
        }

        // 如果是数字，进行Excel时间转换
        if (is_numeric($excelDate)) {
            $excelDate = floatval($excelDate);
            
            // Excel的日期系统从1900年1月1日开始计算天数
            // 但是Excel错误地认为1900年是闰年，所以需要减去2天
            $unixDate = ($excelDate - 25569) * 86400;
            
            // 检查是否是有效的时间戳
            if ($unixDate < 0) {
                return null;
            }
            
            return date($format, $unixDate);
        }

        return null;
    }

    /**
     * 将Excel时间数字转换为PHP日期（仅日期部分）
     * @param mixed $excelDate Excel时间数字或字符串
     * @param string $format 输出格式，默认为 'Y-m-d'
     * @return string|null 转换后的日期字符串，如果转换失败返回null
     */
    public static function excelToDate($excelDate, $format = 'Y-m-d')
    {
        return self::excelToDateTime($excelDate, $format);
    }

    /**
     * 将Excel时间数字转换为PHP时间（仅时间部分）
     * @param mixed $excelTime Excel时间数字或字符串
     * @param string $format 输出格式，默认为 'H:i:s'
     * @return string|null 转换后的时间字符串，如果转换失败返回null
     */
    public static function excelToTime($excelTime, $format = 'H:i:s')
    {
        // 如果为空，返回null
        if (empty($excelTime)) {
            return null;
        }

        // 如果已经是时间字符串，直接返回
        if (is_string($excelTime) && preg_match('/^\d{2}:\d{2}/', $excelTime)) {
            return $excelTime;
        }

        // 如果是数字，进行Excel时间转换
        if (is_numeric($excelTime)) {
            $excelTime = floatval($excelTime);
            
            // Excel时间是以天为单位的小数部分
            // 例如：0.5 = 12:00:00, 0.25 = 06:00:00
            $seconds = round($excelTime * 86400);
            
            // 确保时间在有效范围内
            if ($seconds < 0 || $seconds >= 86400) {
                return null;
            }
            
            return date($format, $seconds);
        }

        return null;
    }

    public static function formatMobile($mobile){
        if (empty($mobile) || strlen($mobile) != 11) {
            return $mobile;
        }
        return substr($mobile, 0, 3).'****'.substr($mobile,7, 4);
    }

    public static function toBinary($num,$length)
    {
        $char = decbin($num);
        $char = str_pad($char,$length,"0",STR_PAD_LEFT);
        $char = strrev($char);
        return $char;
    }

    public static function toDecimalism($num,$index)
    {
        return $num+pow(2,$index-1);
    }
}