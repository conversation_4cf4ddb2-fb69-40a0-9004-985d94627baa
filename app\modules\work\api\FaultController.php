<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\FaultService;

class FaultController extends SuperController
{
    public function listAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->selectAll());
        }
    }

    public function addAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->getAddIndexData());
        }
    }

    public function equlistAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->getEquList($this->request->getPost('equ_type', 'tstring')));
        }
    }

    public function submitAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            $ret = new JsonData();
            $ret->message = $s->submit();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function endinitAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->getEndIndexData());
        }
    }

    public function endAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            $ret = new JsonData();
            $ret->message = $s->end();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function repairinitAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->getRepairIndexData());
        }
    }

    public function repairAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            $ret = new JsonData();
            $ret->message = $s->repairSubmit();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function detailAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->getDetailIndexData());
        }
    }

    public function repairlistAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->selectRepairAll());
        }
    }

    public function repairmoneyinitAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            return json_encode($s->getRepairMoneyIndexData());
        }
    }

    public function repairmoneyAction()
    {
        if ($this->request->isPost()) {
            $s = new FaultService();
            $ret = new JsonData();
            $ret->message = $s->repairMoneySubmit();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}