{"version": 3, "file": "js/src_view_work_index_vue.js", "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACgQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACthBA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/work/index.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/work/index.vue", "webpack://rrts-manager/./src/view/work/index.vue?0098", "webpack://rrts-manager/./src/view/work/index.vue?c3ad", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/work/index.vue?85b0", "webpack://rrts-manager/./src/view/work/index.vue?abea", "webpack://rrts-manager/./src/view/work/index.vue?c16e", "webpack://rrts-manager/./src/view/work/index.vue?f7b7"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div>\r\n        <div v-if=\"type == 2\" style=\"position: absolute;top:60px;right: 10px;z-index: 999\">\r\n            <van-button @click=\"doBack\" style=\"width: 40px;height: 40px;border-radius: 25px;font-size: 20px;padding-top: 5px\" type=\"info\">\r\n                <van-icon name=\"arrow-left\"/>\r\n            </van-button>\r\n        </div>\r\n        <div v-if=\"step==0\">\r\n        </div>\r\n        <div v-if=\"step==1\">\r\n            <van-empty image=\"error\" :description=\"message\" />\r\n        </div>\r\n        <div v-if=\"step==2\" class=\"main\">\r\n            <van-steps v-if=\"work_list.length > 1\" :active=\"active\" @click-step=\"stepClick\" >\r\n                <van-step v-for=\"(item,i) in work_list\" >{{item.name}}</van-step>\r\n            </van-steps>\r\n            <div :style=\"{overflow: 'auto',height: data.auth == 0 ? '100vh' : 'calc(100vh - 56px)',paddingBottom:'60px'}\" @scroll=\"scroll\" ref=\"scroll\">\r\n                <div class=\"header\">\r\n                    <div class=\"title\" v-text=\"data.title\"></div>\r\n                    <div class=\"title2\" v-text=\"data.group\"></div>\r\n                    <div class=\"title3\">\r\n                        <span v-text=\"data.anchor\"></span> &nbsp;&nbsp;&nbsp;&nbsp;\r\n                        <van-tag v-if=\"data.status == 15\" type=\"primary\" size=\"large\">审批中</van-tag>\r\n                        <van-tag v-if=\"data.handle_status == 1\" type=\"success\" size=\"large\">通过</van-tag>\r\n                        <van-tag v-if=\"data.handle_status == 2\" type=\"danger\" size=\"large\">拒绝</van-tag>\r\n                        <van-tag v-if=\"data.handle_status == 3\" type=\"danger\" size=\"large\">撤销</van-tag>\r\n                    </div>\r\n                </div>\r\n                <div class=\"content\" style=\"padding: 0;overflow: hidden\">\r\n                    <div>\r\n                        <van-cell-group>\r\n                            <van-cell title=\"业务单号\" :value=\"data.code\" />\r\n                            <van-cell title=\"业务名称\" :value=\"data.type_name\" />\r\n                            <van-cell v-if=\"data.review_type == 1\" title=\"业务类型\">\r\n                                <template #extra>\r\n                                    <span style=\"color: red\">撤销审批</span>\r\n                                </template>\r\n                            </van-cell>\r\n                            <van-cell title=\"申请人\" :value=\"data.create_user\" />\r\n                            <van-cell title=\"申请时间\" :value=\"data.create_date\" />\r\n                            <template v-for=\"(item,i) in data.form_list\">\r\n                                <template v-if=\"item.type == 99\">\r\n                                    <template v-if=\"item.sum_list.length > 0 || item.data_list.length > 0\">\r\n                                        <van-cell :title=\"item.name\" icon=\"star\" @click=\"detail_drict == 'row' ? detail_drict = 'column' :  detail_drict = 'row'\">\r\n                                            <van-icon size=\"24\" slot=\"right-icon\" :name=\"detail_drict == 'row' ? 'arrow' : 'arrow-down'\"/>\r\n                                        </van-cell>\r\n                                        <van-cell v-for=\"(sum,j) in item.sum_list\" :title=\"sum.name\" :value=\"sum.value + sum.unit\" />\r\n                                        <template v-if=\"detail_drict == 'row'\">\r\n                                            <div style=\"width: 100vw;overflow-x:auto;padding-bottom: 20px;padding-right: 32px \">\r\n                                                <table :style=\"{width:item.show_cnt*200 + 'px',borderSpacing:0}\" >\r\n                                                    <thead>\r\n                                                    <tr>\r\n                                                        <th style=\"width: 200px;border-color:#E2E2E2;border-style: solid;border-width: 1px 1px 1px 0;font-size: 14px;color: #555;padding: 5px 0;\" v-for=\"(v,l) in item.data_list[0]\"  v-if=\"v.show == 1\"  v-text=\"v.name\"></th>\r\n                                                    </tr>\r\n                                                    </thead>\r\n                                                    <tbody>\r\n                                                        <tr v-for=\"(d,k) in item.data_list\">\r\n                                                            <td v-for=\"(v,l) in d\"  v-if=\"v.show == 1\" style=\"border-color:#E2E2E2;border-style: solid;border-width: 0 1px 1px 0;font-size: 14px;padding: 5px;text-align: center\">\r\n                                                                <van-button  v-if=\"v.type == 100\" plain type=\"info\" size=\"small\" @click=\"viewDetail(v)\">查看详情</van-button>\r\n                                                                <template v-else-if=\"v.type == 999\">\r\n                                                                    <div v-if=\"v.value.length > 0\">\r\n                                                                        <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;\">\r\n                                                                            <div v-for=\"(file, i) in v.value\" @click=\"previewImg(v.value, i)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                                                                <van-image :src=\"base_path + file\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </template>\r\n                                                                <template v-else>\r\n                                                                    <span v-text=\"v.value + v.unit\" ></span>\r\n                                                                </template>\r\n                                                            </td>\r\n                                                        </tr>\r\n                                                    </tbody>\r\n                                                </table>\r\n                                            </div>\r\n                                        </template>\r\n                                        <template v-else>\r\n                                            <div v-for=\"(d,k) in item.data_list\" class=\"content\" style=\"padding: 0;overflow: hidden\">\r\n                                                <van-cell-group inset>\r\n                                                    <template v-for=\"(v,l) in d\">\r\n                                                        <van-cell v-if=\"v.type == 100\" title=\"查看详情\" is-link @click=\"viewDetail(v)\"/>\r\n                                                        <template v-else-if=\"v.type == 999\">\r\n                                                            <van-cell v-if=\"v.value.length > 0\" title=\"图片\">\r\n                                                                <template #extra>\r\n                                                                    <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;width: 79%\">\r\n                                                                        <div v-for=\"(file, i) in v.value\" @click=\"previewImg(v.value, i)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                                                            <van-image :src=\"base_path + file\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </template>\r\n                                                            </van-cell>\r\n                                                        </template>\r\n                                                        <template v-else>\r\n                                                            <van-cell v-if=\"v.show == 1\" :title=\"v.name\" :value=\"v.value + v.unit\" />\r\n                                                        </template>\r\n                                                    </template>\r\n                                                </van-cell-group>\r\n                                            </div>\r\n                                        </template>\r\n                                    </template>\r\n                                </template>\r\n                                <van-cell v-else-if=\"item.type == 999\" title=\"图片\">\r\n                                    <template #extra>\r\n                                        <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;width: 79%\">\r\n                                            <div v-for=\"(file,i) in item.value\" @click=\"previewImg(item.value, i)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                                <van-image :src=\"base_path+file\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                            </div>\r\n                                        </div>\r\n                                    </template>\r\n                                </van-cell>\r\n                                <van-cell v-else-if=\"item.type == 101\" :title=\"item.name\" is-link @click=\"viewDetail(item)\"/>\r\n                                <template v-else>\r\n                                    <van-cell v-if=\"item.show == 1\" :title=\"item.name\" :value=\"item.value + item.unit\" />\r\n                                </template>\r\n                            </template>\r\n                            <van-cell v-if=\"data.remarks != null\" title=\"说明\" :value=\"data.remarks\" />\r\n                            <van-cell v-if=\"data.files.length > 0\" title=\"文件\">\r\n                                <template #extra>\r\n                                    <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;width: 79%\">\r\n                                        <div v-for=\"(file,i) in data.files\" @click=\"previewImg(data.files, i)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                            <van-image :src=\"base_path+file\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                        </div>\r\n                                    </div>\r\n                                </template>\r\n                            </van-cell>\r\n                            <van-cell v-if=\"data.more_flag == 1\" title=\"查看子流程\" is-link @click=\"viewMore\"/>\r\n                        </van-cell-group>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"flow_list.length > 0\" class=\"content\">\r\n                    <div>\r\n                        <div v-for=\"(item,index) in flow_list\" class=\"item\">\r\n                            <div class=\"title-border\">\r\n                                <div v-if=\"item.type == 4\" class=\"title\" style=\"background-color: #3296FB\">\r\n                                    <van-icon name=\"wap-home-o\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                                </div>\r\n                                <div v-else-if=\"item.type == 5\" class=\"title\" style=\"background-color: #FF2B2B\">\r\n                                    <van-icon name=\"stop\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                                </div>\r\n                                <div v-else-if=\"item.type == 6\" class=\"title\" style=\"background-color: #FF2B2B\">\r\n                                    <van-icon name=\"revoke\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                                </div>\r\n                                <div v-else class=\"title\" style=\"background-color: #3296FB\">\r\n                                    {{item.icon}}\r\n                                    <div v-if=\"item.type == 1\" class=\"border\" style=\"color:#4AB37E\">\r\n                                        <van-icon name=\"checked\"/>\r\n                                    </div>\r\n                                    <div v-else-if=\"item.type == 3\" class=\"border\" style=\"color:#4AB37E\">\r\n                                        <van-icon name=\"volume\"/>\r\n                                    </div>\r\n                                    <div v-else class=\"border\" style=\"color: #3296FB\">\r\n                                        <van-icon name=\"thumb-circle\" />\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"item-content\">\r\n                                <div class=\"top\">\r\n                                    <div :style=\"{flex:'1',color: item.status == 0 ? '#A2A2A2' : '#000000'}\">\r\n                                        <span>{{item.name}}</span>\r\n                                        <span> {{item.val}}</span>\r\n                                    </div>\r\n                                    <div style=\"width: 80px;margin-left: 5px\">\r\n                                        <span style=\"font-size: 13px;color: #898989;margin-top: -3px\">{{item.time}}</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"bottom\" :style=\"{borderLeft:flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'}\">\r\n                                    <div v-if=\"item.text != ''\" class=\"bottom-content\">\r\n                                        {{item.text}}\r\n                                    </div>\r\n                                    <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;margin-left: 35px;margin-top: 5px\">\r\n                                        <div v-for=\"(file,i) in item.files\" @click=\"previewImg(item.files, i)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                            <van-image :src=\"base_path+file\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div v-if=\"item.send != ''\" class=\"bottom-send\">\r\n                                        {{item.send}}\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div>\r\n                        <div v-for=\"(item,idx) in anchor_list\" :key=\"idx\">\r\n                            <div style=\"display: flex;flex-direction: row;min-height: 60px;\">\r\n                                <div style=\"width: 35%;display: flex;flex-direction: row;\">\r\n                                    <div style=\"height: 100%;position: relative\">\r\n                                        <div style=\"height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;\">\r\n                                        </div>\r\n                                        <div v-if=\"idx == 0\" style=\"height: 18px;position: absolute;top: 0;width: 12px;background-color: #FFF\">\r\n                                        </div>\r\n                                        <div v-if=\"idx+1 == anchor_list.length\" style=\"height: calc(100% - 22px);position: absolute;top: 22px;width: 12px;background-color: #FFF\">\r\n                                        </div>\r\n                                        <div style=\"position: absolute;top: 15px;width: 15px;height: 15px;background-color: #aaaaaa;border-radius: 15px\">\r\n                                        </div>\r\n                                    </div>\r\n                                    <div style=\"padding-top:5px;padding-left: 15px\">\r\n                                        <span style=\"font-weight: 400\" v-text=\"item.name\"></span>\r\n                                        <div>\r\n                                            <span style=\"font-size: 12px;color: #898989\" v-if=\"item.type==1\">会签</span>\r\n                                            <span style=\"font-size: 12px;color: #898989\" v-if=\"item.type==2\">或签</span>\r\n                                            <span style=\"font-size: 12px;color: #898989\" v-if=\"item.type==3\">终签</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                                <div style=\"width: 65%;display: flex;flex-direction: row;flex-wrap: wrap;justify-content: flex-end;padding-right: 10px;padding-top: 5px\">\r\n                                    <van-tag v-for=\"(user,i) in item.list\" :key=\"user.id\" :type=\"item.type == 1 ? 'primary' : 'success'\" size=\"large\" style=\"height: 20px;margin: 2px\">{{user.name}}</van-tag>\r\n                                </div>\r\n                            </div>\r\n                            <div v-if=\"item.nlist.length > 0\" style=\"display: flex;flex-direction: row;min-height: 60px;\">\r\n                                <div style=\"width: 35%;display: flex;flex-direction: row;\">\r\n                                    <div style=\"height: 100%;position: relative\">\r\n                                        <div v-if=\"idx+1 != anchor_list.length\" style=\"height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;\">\r\n                                        </div>\r\n                                        <div v-else style=\"height: 100%;width: 8px;\">\r\n                                        </div>\r\n                                    </div>\r\n                                    <div style=\"padding-top:5px;padding-left: 15px\">\r\n                                        <span style=\"font-weight: 400;color: #898989\">抄送人</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div style=\"width: 65%;display: flex;flex-direction: row;flex-wrap: wrap;justify-content: flex-end;padding-right: 10px;padding-top: 5px\">\r\n                                    <van-tag v-for=\"(user,i) in item.nlist\" :key=\"user.id\" type=\"default\" size=\"large\" style=\"height: 20px;margin: 2px\">{{user.name}}</van-tag>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div  v-if=\"next_list.length > 0\" class=\"content\">\r\n                    <div style=\"margin-bottom: 15px;font-weight: 600\">\r\n                        发起申请\r\n                    </div>\r\n                    <van-radio-group v-model=\"next_id\">\r\n                        <van-cell-group inset>\r\n                            <van-cell v-for=\"(item,index) in next_list\" :title=\"item.name\" clickable @click=\"selectItem(index)\">\r\n                                <template #right-icon>\r\n                                    <van-radio checked-color=\"#008000\" :name=\"item.id\" />\r\n                                </template>\r\n                            </van-cell>\r\n                        </van-cell-group>\r\n                    </van-radio-group>\r\n                </div>\r\n            </div>\r\n            <div class=\"footer\" v-if=\"data.auth > 0\">\r\n                <van-button type=\"default\" class=\"btn-comment\" @click=\"addComment\">\r\n                    <div class=\"icon\">\r\n                        <van-icon name=\"chat-o\" />\r\n                    </div>\r\n                    <div class=\"text\">添加评论</div>\r\n                </van-button>\r\n                <van-button v-if=\"data.auth == 2\" plain type=\"info\" class=\"btn-left\" @click=\"rejectOpen\">拒绝</van-button>\r\n                <van-button v-if=\"data.auth == 2\" type=\"info\" class=\"btn-right\" @click=\"passShow\">通过</van-button>\r\n                <van-button v-if=\"data.auth == 3\" type=\"info\" class=\"btn-right-left\" @click=\"readSave\">已知晓</van-button>\r\n                <van-button v-if=\"data.auth == 4\" type=\"danger\" class=\"btn-left\" @click=\"pressingShow\">催办</van-button>\r\n                <van-button v-if=\"data.auth == 4\" type=\"danger\" class=\"btn-right\" @click=\"cancelShow\">撤回</van-button>\r\n\r\n            </div>\r\n            <van-popup\r\n                    v-model=\"cancel_show_flag\"\r\n                    position=\"bottom\"\r\n                    :style=\"{ height: '30%' }\"\r\n            >\r\n                <div>\r\n                    <van-field\r\n                            type=\"textarea\"\r\n                            :autosize=\"{ minHeight: 100 }\"\r\n                            v-model=\"cancel_value\"\r\n                            placeholder=\"撤回原因(必填)\"\r\n                            class=\"input-text\"\r\n                            maxlength = '200'\r\n                    />\r\n                </div>\r\n                <div class=\"btn-footer\">\r\n                    <van-button class=\"van-button\" round type=\"danger\" @click=\"cancelSave\">提交撤回</van-button>\r\n                    <van-button class=\"van-button\" plain round type=\"danger\" @click=\"cancelHide\" style=\"margin-left: 30px\">关闭</van-button>\r\n                </div>\r\n            </van-popup>\r\n            <van-popup\r\n                    v-model=\"pressing_show_flag\"\r\n                    position=\"bottom\"\r\n                    :style=\"{ height: '30%' }\"\r\n            >\r\n                <div>\r\n                    <van-field\r\n                            type=\"textarea\"\r\n                            :autosize=\"{ minHeight: 100 }\"\r\n                            v-model=\"cancel_value\"\r\n                            placeholder=\"催办原因(必填)\"\r\n                            class=\"input-text\"\r\n                            maxlength = '200'\r\n                    />\r\n                </div>\r\n                <div class=\"btn-footer\">\r\n                    <van-button class=\"van-button\" round type=\"danger\" @click=\"pressingSave\">提交催办</van-button>\r\n                    <van-button class=\"van-button\" plain round type=\"danger\" @click=\"pressingHide\" style=\"margin-left: 30px\">关闭</van-button>\r\n                </div>\r\n            </van-popup>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { ImagePreview } from 'vant';\r\n    import base from '../../components/base';\r\n    import { Dialog } from 'vant';\r\n    import Config from \"@/config\";\r\n    export default {\r\n        extends: base,\r\n        name: \"work\",\r\n        data () {\r\n            return {\r\n                active:0,\r\n                uid:'',\r\n                pos:0,\r\n                step : 0,\r\n                message:'',\r\n                type:1,\r\n                src:2,\r\n                pressing_show_flag:false,\r\n                cancel_show_flag:false,\r\n                cancel_value:'',\r\n                base_path:'',\r\n                flow_list:[],\r\n                anchor_list:[],\r\n                user_list:[],\r\n                next_id : '',\r\n                next_list:[],\r\n                work_list:[],\r\n                main_id:'',\r\n                pid:'',\r\n                data:{},\r\n                detail_drict : 'row',\r\n            };\r\n        },\r\n        components: {ImagePreview},\r\n        created(){\r\n            this.$hub.$on('refresh', (uid = null) => {\r\n                this.refresh(uid);\r\n            });\r\n        },\r\n        methods:{\r\n            onLoad(){\r\n                let user = this.$store.state.user;\r\n                this.base_path = user.imgdir;\r\n                this.pos = 0;\r\n                this.uid = this.$route.params.uid;\r\n                this.step = 0;\r\n                this.message = '';\r\n                this.type = this.$route.params.type;\r\n                this.src = this.$route.params.src;\r\n                this.pressing_show_flag = false;\r\n                this.cancel_show_flag = false;\r\n                this.cancel_value = '';\r\n                this.next_id  = '';\r\n                this.$http.post('work/work/view', {uid: this.uid,src:this.src}).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.step = 2;\r\n                        this.main_id = rs.data.data.main_id;\r\n                        this.pid = rs.data.data.pid;\r\n                        this.flow_list = rs.data.flow_list;\r\n                        this.anchor_list = rs.data.anchor_list;\r\n                        this.user_list = rs.data.user_list;\r\n                        this.next_list = rs.data.next_list;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.data = rs.data.data;\r\n                        for(let i = 0 ; i < this.work_list.length ; i++){\r\n                            if (this.work_list[i].uid == this.uid){\r\n                                this.active = i;\r\n                                break;\r\n                            }\r\n                        }\r\n                    } else {\r\n                        this.step = 1;\r\n                        this.message = rs.message;\r\n                    }\r\n                });\r\n            },\r\n            onShow(){\r\n                this.$refs['scroll'].scrollTop = this.pos;\r\n            },\r\n            refresh(uid){\r\n                if (uid !=null){\r\n                    this.uid = uid;\r\n                    this.$route.params.uid = uid;\r\n                }\r\n                this.$http.post('work/work/view', {uid: this.uid,src:this.src,main_id:this.main_id,pid:this.pid}).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.flow_list = rs.data.flow_list;\r\n                        this.anchor_list = rs.data.anchor_list;\r\n                        this.user_list = rs.data.user_list;\r\n                        this.next_list = rs.data.next_list;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.data = rs.data.data;\r\n                        for(let i = 0 ; i < this.work_list.length ; i++){\r\n                            if (this.work_list[i].uid == this.uid){\r\n                                this.active = i;\r\n                                break;\r\n                            }\r\n                        }\r\n                    } else {\r\n                        this.$toast.fail(rs.message);\r\n                    }\r\n                });\r\n            },\r\n            previewImg(imgs, idx) {\r\n                let images = [];\r\n                for (let i = 0; i < imgs.length; i++) {\r\n                    images.push(this.base_path + imgs[i]);\r\n                }\r\n                ImagePreview({\r\n                    images: images,\r\n                    startPosition: idx,\r\n                });\r\n            },\r\n            openFile(path) {\r\n                if (/\\.pdf$/.test(path)) {\r\n                    this.$router.push({name: 'preview/pdf', params: { path: this.base_path + path }});\r\n                } else {\r\n                    window.open(this.base_path + path);\r\n                }\r\n            },\r\n            addComment(){\r\n                this.$router.push({ name: 'work/comment', params: {uid:this.data.uid,list:this.user_list,type:1}})\r\n            },\r\n            passShow(){\r\n                this.$router.push({ name: 'work/comment', params: {uid:this.data.uid,list:[],type:2,pass_type:this.data.pass_type}})\r\n            },\r\n            rejectOpen(){\r\n                this.$router.push({ name: 'work/reject', params: {uid:this.data.uid}})\r\n            },\r\n            pressingShow(){\r\n                this.cancel_value = '';\r\n                this.pressing_show_flag = true;\r\n            },\r\n            pressingHide(){\r\n                this.pressing_show_flag = false;\r\n            },\r\n            pressingSave(){\r\n                if (this.cancel_value == ''){\r\n                    this.$toast.fail('请填写催办原因');\r\n                    return;\r\n                }\r\n                Dialog.confirm({\r\n                    title: '催办',\r\n                    message: '确定要提交催办吗？',\r\n                })\r\n                .then(() => {\r\n                    this.$http.post('work/work/pressing', {uid: this.data.uid,remarks:this.cancel_value}).then((rs) => {\r\n                        if (rs.status === 'ok') {\r\n                            this.$toast.success('催办成功');\r\n                            this.$hub.$emit('refreshlist');\r\n                            this.$router.back();\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                        }\r\n                    }).catch((e) => {\r\n                        this.$toast.fail('提交失败');\r\n                    });\r\n                })\r\n                .catch(() => {});\r\n            },\r\n            cancelShow(){\r\n                this.cancel_value = '';\r\n                this.cancel_show_flag = true;\r\n            },\r\n            cancelHide(){\r\n                this.cancel_show_flag = false;\r\n            },\r\n            cancelSave(){\r\n                if (this.cancel_value == ''){\r\n                    this.$toast.fail('请填写撤回原因');\r\n                    return;\r\n                }\r\n                Dialog.confirm({\r\n                    title: '撤回',\r\n                    message: '确定要提交撤回吗？',\r\n                })\r\n                    .then(() => {\r\n                        this.$http.post('work/work/cancel', {uid: this.data.uid,remarks:this.cancel_value}).then((rs) => {\r\n                            if (rs.status === 'ok') {\r\n                                this.$toast.success('撤销成功');\r\n                                this.$hub.$emit('refreshlist');\r\n                                this.$router.back();\r\n                            } else {\r\n                                this.$toast.fail(rs.message);\r\n                            }\r\n                        }).catch((e) => {\r\n                            this.$toast.fail('提交失败');\r\n                        });\r\n                    })\r\n                    .catch(() => {});\r\n            },\r\n            readSave(){\r\n                Dialog.confirm({\r\n                    title: '已知晓',\r\n                    message: '确定要已知晓吗？',\r\n                })\r\n                .then(() => {\r\n                    this.$http.post('work/work/readsave', {uid: this.data.uid}).then((rs) => {\r\n                        if (rs.status === 'ok') {\r\n                            this.$hub.$emit('refresh',rs.uid);\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                        }\r\n                    }).catch((e) => {\r\n                        this.$toast.fail('提交失败');\r\n                    });\r\n                });\r\n            },\r\n            viewDetail(v){\r\n                if (v.sort == 1 || v.sort == 4 || v.sort == 5){\r\n                    this.$router.push({name: 'product/view',params: { id: v.id}});\r\n                }\r\n            },\r\n            viewMore(){\r\n                this.$router.push({name: 'work/more',params: { uid: this.data.uid}});\r\n            },\r\n            doBack() {\r\n                this.$router.back();\r\n            },\r\n            scroll(event) {\r\n                this.pos = event.target.scrollTop;\r\n            },\r\n            selectItem(idx){\r\n                this.next_id = this.next_list[idx].id;\r\n            },\r\n            stepClick(idx){\r\n                if (this.work_list[idx].uid == this.uid){\r\n                    return;\r\n                }\r\n                this.uid = this.work_list[idx].uid;\r\n                this.refresh();\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    .main{\r\n        background-color: #F2F2F2;\r\n        width: 100%;\r\n        padding-bottom: 10px;\r\n        height: 100vh;\r\n        overflow: hidden;\r\n    }\r\n    .header{\r\n        width: 100%;\r\n        min-height: 100px;\r\n        background-color: #FFFFFF;\r\n        padding: 15px;\r\n        box-sizing: border-box;\r\n    }\r\n    .header .title{\r\n        font-size: 24px;\r\n        letter-spacing: 1px;\r\n    }\r\n\r\n    .header .title2{\r\n        font-size: 14px;\r\n        color: #898989;\r\n        margin-top: 10px;\r\n    }\r\n\r\n    .header .title3{\r\n        font-size: 16px;\r\n        margin-top: 10px;\r\n        color: #CAA36D;\r\n        font-weight: bold;\r\n        letter-spacing: 1px;\r\n    }\r\n\r\n    .content{\r\n        min-height: 100px;\r\n        background-color: #FFFFFF;\r\n        margin: 15px;\r\n        padding: 15px;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 10px;\r\n    }\r\n    .item{\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-start;\r\n        margin-top: 4px;\r\n    }\r\n    .item .title{\r\n        width: 45px;\r\n        height: 45px;\r\n        line-height: 45px;\r\n        vertical-align: middle;\r\n        background-color: #3296FB;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 5px;\r\n        color: #FFFFFF;\r\n        text-align: center;\r\n        position: relative;\r\n        font-size: 16px;\r\n    }\r\n    .item .title .border{\r\n        position: absolute;\r\n        top: 31px;\r\n        left: 31px;\r\n        width: 18px;\r\n        height: 18px;\r\n        line-height: 18px;\r\n        border-radius: 9px;\r\n        background-color: #FFFFFF;\r\n        text-align: center;\r\n        padding-top: 1px;\r\n    }\r\n\r\n    .item .item-content{\r\n        width: 100%;\r\n        margin-left: 15px;\r\n        color: #888888;\r\n    }\r\n\r\n    .item .item-content .top {\r\n        min-height: 20px;\r\n        line-height: 20px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: space-between;\r\n        font-size: 16px;\r\n        margin-top: 2px;\r\n        vertical-align: top;\r\n    }\r\n\r\n    .item .item-content .bottom {\r\n        margin-left: -38px;\r\n        border-left: 4px #D2D2D2 solid;\r\n        padding-bottom: 10px;\r\n        min-height: 55px;\r\n    }\r\n\r\n    .item .bottom-content{\r\n        margin-left: 35px;\r\n        margin-top:5px;\r\n        min-height: 40px;\r\n        background-color: #F2F2F2;\r\n        border-radius: 5px;\r\n        padding: 10px;\r\n        color: #000000;\r\n    }\r\n\r\n    .item .bottom-send{\r\n        margin-left: 35px;\r\n        margin-top:5px;\r\n        font-size: 14px;\r\n    }\r\n\r\n    .item .title-border{\r\n        background-color: #FFFFFF;\r\n        width: 45px;\r\n        height: 52px;\r\n        z-index: 999;\r\n    }\r\n\r\n    .flow-title{\r\n        font-size: 20px;\r\n        margin-top: 10px;\r\n        margin-bottom: 15px;\r\n        font-weight: bold;\r\n        letter-spacing: 2px;\r\n    }\r\n\r\n    .footer{\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 56px;\r\n        border-top: 1px #E2E2E2 solid;\r\n        background-color: #FFFFFF;\r\n        z-index: 1000;\r\n        box-sizing: border-box;\r\n        padding-top: 5px;\r\n        padding-right: 15px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-end;\r\n    }\r\n\r\n    .footer .btn-left{\r\n        border-radius: 0;\r\n        border-top-left-radius:22px;\r\n        border-bottom-left-radius:22px;\r\n        font-size: 18px;\r\n        width: 90px;\r\n        margin-right: 1px;\r\n    }\r\n\r\n    .footer .btn-right{\r\n        border-radius: 0;\r\n        border-top-right-radius:22px;\r\n        border-bottom-right-radius:22px;\r\n        font-size: 18px;\r\n        width: 90px;\r\n    }\r\n    .footer .btn-right-left{\r\n        border-radius: 0;\r\n        border-top-left-radius:22px;\r\n        border-bottom-left-radius:22px;\r\n        border-top-right-radius:22px;\r\n        border-bottom-right-radius:22px;\r\n        font-size: 18px;\r\n        width: 90px;\r\n    }\r\n    .footer .btn-comment{\r\n        border:0;\r\n        margin-right: 10px;\r\n    }\r\n    .footer .btn-comment .icon{\r\n        font-size: 26px;\r\n        color: #929292;\r\n    }\r\n    .footer .btn-comment .text{\r\n        font-size: 14px;\r\n        color: #929292;\r\n        margin-top: -5px;\r\n    }\r\n    .input-text {\r\n        font-size: 18px;\r\n    }\r\n    .van-button{\r\n        width: 120px;\r\n        font-size: 17px;\r\n    }\r\n    .btn-footer{\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        background-color: #FFFFFF;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n        padding-bottom: 10px;\r\n    }\r\n\r\n    .file-box {\r\n        width: 79%;\r\n        display: flex;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .file-item {\r\n        margin-bottom: 20px;\r\n        font-size: 18px;\r\n        font-style: italic;\r\n        color: #3a94ec;\r\n    }\r\n\r\n    .file-box > .file-item:last-child {\r\n        margin-bottom: 0;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.type == 2)?_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"60px\",\"right\":\"10px\",\"z-index\":\"999\"}},[_c('van-button',{staticStyle:{\"width\":\"40px\",\"height\":\"40px\",\"border-radius\":\"25px\",\"font-size\":\"20px\",\"padding-top\":\"5px\"},attrs:{\"type\":\"info\"},on:{\"click\":_vm.doBack}},[_c('van-icon',{attrs:{\"name\":\"arrow-left\"}})],1)],1):_vm._e(),(_vm.step==0)?_c('div'):_vm._e(),(_vm.step==1)?_c('div',[_c('van-empty',{attrs:{\"image\":\"error\",\"description\":_vm.message}})],1):_vm._e(),(_vm.step==2)?_c('div',{staticClass:\"main\"},[(_vm.work_list.length > 1)?_c('van-steps',{attrs:{\"active\":_vm.active},on:{\"click-step\":_vm.stepClick}},_vm._l((_vm.work_list),function(item,i){return _c('van-step',[_vm._v(_vm._s(item.name))])}),1):_vm._e(),_c('div',{ref:\"scroll\",style:({overflow: 'auto',height: _vm.data.auth == 0 ? '100vh' : 'calc(100vh - 56px)',paddingBottom:'60px'}),on:{\"scroll\":_vm.scroll}},[_c('div',{staticClass:\"header\"},[_c('div',{staticClass:\"title\",domProps:{\"textContent\":_vm._s(_vm.data.title)}}),_c('div',{staticClass:\"title2\",domProps:{\"textContent\":_vm._s(_vm.data.group)}}),_c('div',{staticClass:\"title3\"},[_c('span',{domProps:{\"textContent\":_vm._s(_vm.data.anchor)}}),_vm._v(\"      \"),(_vm.data.status == 15)?_c('van-tag',{attrs:{\"type\":\"primary\",\"size\":\"large\"}},[_vm._v(\"审批中\")]):_vm._e(),(_vm.data.handle_status == 1)?_c('van-tag',{attrs:{\"type\":\"success\",\"size\":\"large\"}},[_vm._v(\"通过\")]):_vm._e(),(_vm.data.handle_status == 2)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"拒绝\")]):_vm._e(),(_vm.data.handle_status == 3)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"撤销\")]):_vm._e()],1)]),_c('div',{staticClass:\"content\",staticStyle:{\"padding\":\"0\",\"overflow\":\"hidden\"}},[_c('div',[_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"业务单号\",\"value\":_vm.data.code}}),_c('van-cell',{attrs:{\"title\":\"业务名称\",\"value\":_vm.data.type_name}}),(_vm.data.review_type == 1)?_c('van-cell',{attrs:{\"title\":\"业务类型\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(\"撤销审批\")])]},proxy:true}],null,false,688513901)}):_vm._e(),_c('van-cell',{attrs:{\"title\":\"申请人\",\"value\":_vm.data.create_user}}),_c('van-cell',{attrs:{\"title\":\"申请时间\",\"value\":_vm.data.create_date}}),_vm._l((_vm.data.form_list),function(item,i){return [(item.type == 99)?[(item.sum_list.length > 0 || item.data_list.length > 0)?[_c('van-cell',{attrs:{\"title\":item.name,\"icon\":\"star\"},on:{\"click\":function($event){_vm.detail_drict == 'row' ? _vm.detail_drict = 'column' :  _vm.detail_drict = 'row'}}},[_c('van-icon',{attrs:{\"slot\":\"right-icon\",\"size\":\"24\",\"name\":_vm.detail_drict == 'row' ? 'arrow' : 'arrow-down'},slot:\"right-icon\"})],1),_vm._l((item.sum_list),function(sum,j){return _c('van-cell',{attrs:{\"title\":sum.name,\"value\":sum.value + sum.unit}})}),(_vm.detail_drict == 'row')?[_c('div',{staticStyle:{\"width\":\"100vw\",\"overflow-x\":\"auto\",\"padding-bottom\":\"20px\",\"padding-right\":\"32px\"}},[_c('table',{style:({width:item.show_cnt*200 + 'px',borderSpacing:0})},[_c('thead',[_c('tr',_vm._l((item.data_list[0]),function(v,l){return (v.show == 1)?_c('th',{staticStyle:{\"width\":\"200px\",\"border-color\":\"#E2E2E2\",\"border-style\":\"solid\",\"border-width\":\"1px 1px 1px 0\",\"font-size\":\"14px\",\"color\":\"#555\",\"padding\":\"5px 0\"},domProps:{\"textContent\":_vm._s(v.name)}}):_vm._e()}),0)]),_c('tbody',_vm._l((item.data_list),function(d,k){return _c('tr',_vm._l((d),function(v,l){return (v.show == 1)?_c('td',{staticStyle:{\"border-color\":\"#E2E2E2\",\"border-style\":\"solid\",\"border-width\":\"0 1px 1px 0\",\"font-size\":\"14px\",\"padding\":\"5px\",\"text-align\":\"center\"}},[(v.type == 100)?_c('van-button',{attrs:{\"plain\":\"\",\"type\":\"info\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDetail(v)}}},[_vm._v(\"查看详情\")]):(v.type == 999)?[(v.value.length > 0)?_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\"}},_vm._l((v.value),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(v.value, i)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path + file,\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0)]):_vm._e()]:[_c('span',{domProps:{\"textContent\":_vm._s(v.value + v.unit)}})]],2):_vm._e()}),0)}),0)])])]:_vm._l((item.data_list),function(d,k){return _c('div',{staticClass:\"content\",staticStyle:{\"padding\":\"0\",\"overflow\":\"hidden\"}},[_c('van-cell-group',{attrs:{\"inset\":\"\"}},[_vm._l((d),function(v,l){return [(v.type == 100)?_c('van-cell',{attrs:{\"title\":\"查看详情\",\"is-link\":\"\"},on:{\"click\":function($event){return _vm.viewDetail(v)}}}):(v.type == 999)?[(v.value.length > 0)?_c('van-cell',{attrs:{\"title\":\"图片\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"width\":\"79%\"}},_vm._l((v.value),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(v.value, i)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path + file,\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0)]},proxy:true}],null,true)}):_vm._e()]:[(v.show == 1)?_c('van-cell',{attrs:{\"title\":v.name,\"value\":v.value + v.unit}}):_vm._e()]]})],2)],1)})]:_vm._e()]:(item.type == 999)?_c('van-cell',{attrs:{\"title\":\"图片\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"width\":\"79%\"}},_vm._l((item.value),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(item.value, i)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path+file,\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0)]},proxy:true}],null,true)}):(item.type == 101)?_c('van-cell',{attrs:{\"title\":item.name,\"is-link\":\"\"},on:{\"click\":function($event){return _vm.viewDetail(item)}}}):[(item.show == 1)?_c('van-cell',{attrs:{\"title\":item.name,\"value\":item.value + item.unit}}):_vm._e()]]}),(_vm.data.remarks != null)?_c('van-cell',{attrs:{\"title\":\"说明\",\"value\":_vm.data.remarks}}):_vm._e(),(_vm.data.files.length > 0)?_c('van-cell',{attrs:{\"title\":\"文件\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"width\":\"79%\"}},_vm._l((_vm.data.files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(_vm.data.files, i)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path+file,\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0)]},proxy:true}],null,false,200490463)}):_vm._e(),(_vm.data.more_flag == 1)?_c('van-cell',{attrs:{\"title\":\"查看子流程\",\"is-link\":\"\"},on:{\"click\":_vm.viewMore}}):_vm._e()],2)],1)]),(_vm.flow_list.length > 0)?_c('div',{staticClass:\"content\"},[_c('div',_vm._l((_vm.flow_list),function(item,index){return _c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title-border\"},[(item.type == 4)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#3296FB\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"wap-home-o\",\"size\":\"30\"}})],1):(item.type == 5)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#FF2B2B\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"stop\",\"size\":\"30\"}})],1):(item.type == 6)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#FF2B2B\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"revoke\",\"size\":\"30\"}})],1):_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#3296FB\"}},[_vm._v(\" \"+_vm._s(item.icon)+\" \"),(item.type == 1)?_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#4AB37E\"}},[_c('van-icon',{attrs:{\"name\":\"checked\"}})],1):(item.type == 3)?_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#4AB37E\"}},[_c('van-icon',{attrs:{\"name\":\"volume\"}})],1):_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#3296FB\"}},[_c('van-icon',{attrs:{\"name\":\"thumb-circle\"}})],1)])]),_c('div',{staticClass:\"item-content\"},[_c('div',{staticClass:\"top\"},[_c('div',{style:({flex:'1',color: item.status == 0 ? '#A2A2A2' : '#000000'})},[_c('span',[_vm._v(_vm._s(item.name))]),_c('span',[_vm._v(\" \"+_vm._s(item.val))])]),_c('div',{staticStyle:{\"width\":\"80px\",\"margin-left\":\"5px\"}},[_c('span',{staticStyle:{\"font-size\":\"13px\",\"color\":\"#898989\",\"margin-top\":\"-3px\"}},[_vm._v(_vm._s(item.time))])])]),_c('div',{staticClass:\"bottom\",style:({borderLeft:_vm.flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'})},[(item.text != '')?_c('div',{staticClass:\"bottom-content\"},[_vm._v(\" \"+_vm._s(item.text)+\" \")]):_vm._e(),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"margin-left\":\"35px\",\"margin-top\":\"5px\"}},_vm._l((item.files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(item.files, i)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path+file,\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0),(item.send != '')?_c('div',{staticClass:\"bottom-send\"},[_vm._v(\" \"+_vm._s(item.send)+\" \")]):_vm._e()])])])}),0),_c('div',_vm._l((_vm.anchor_list),function(item,idx){return _c('div',{key:idx},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"min-height\":\"60px\"}},[_c('div',{staticStyle:{\"width\":\"35%\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"position\":\"relative\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"border-right\":\"1px #D2D2D2 solid\",\"width\":\"8px\"}}),(idx == 0)?_c('div',{staticStyle:{\"height\":\"18px\",\"position\":\"absolute\",\"top\":\"0\",\"width\":\"12px\",\"background-color\":\"#FFF\"}}):_vm._e(),(idx+1 == _vm.anchor_list.length)?_c('div',{staticStyle:{\"height\":\"calc(100% - 22px)\",\"position\":\"absolute\",\"top\":\"22px\",\"width\":\"12px\",\"background-color\":\"#FFF\"}}):_vm._e(),_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"15px\",\"width\":\"15px\",\"height\":\"15px\",\"background-color\":\"#aaaaaa\",\"border-radius\":\"15px\"}})]),_c('div',{staticStyle:{\"padding-top\":\"5px\",\"padding-left\":\"15px\"}},[_c('span',{staticStyle:{\"font-weight\":\"400\"},domProps:{\"textContent\":_vm._s(item.name)}}),_c('div',[(item.type==1)?_c('span',{staticStyle:{\"font-size\":\"12px\",\"color\":\"#898989\"}},[_vm._v(\"会签\")]):_vm._e(),(item.type==2)?_c('span',{staticStyle:{\"font-size\":\"12px\",\"color\":\"#898989\"}},[_vm._v(\"或签\")]):_vm._e(),(item.type==3)?_c('span',{staticStyle:{\"font-size\":\"12px\",\"color\":\"#898989\"}},[_vm._v(\"终签\")]):_vm._e()])])]),_c('div',{staticStyle:{\"width\":\"65%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"justify-content\":\"flex-end\",\"padding-right\":\"10px\",\"padding-top\":\"5px\"}},_vm._l((item.list),function(user,i){return _c('van-tag',{key:user.id,staticStyle:{\"height\":\"20px\",\"margin\":\"2px\"},attrs:{\"type\":item.type == 1 ? 'primary' : 'success',\"size\":\"large\"}},[_vm._v(_vm._s(user.name))])}),1)]),(item.nlist.length > 0)?_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"min-height\":\"60px\"}},[_c('div',{staticStyle:{\"width\":\"35%\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"position\":\"relative\"}},[(idx+1 != _vm.anchor_list.length)?_c('div',{staticStyle:{\"height\":\"100%\",\"border-right\":\"1px #D2D2D2 solid\",\"width\":\"8px\"}}):_c('div',{staticStyle:{\"height\":\"100%\",\"width\":\"8px\"}})]),_vm._m(0,true)]),_c('div',{staticStyle:{\"width\":\"65%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"justify-content\":\"flex-end\",\"padding-right\":\"10px\",\"padding-top\":\"5px\"}},_vm._l((item.nlist),function(user,i){return _c('van-tag',{key:user.id,staticStyle:{\"height\":\"20px\",\"margin\":\"2px\"},attrs:{\"type\":\"default\",\"size\":\"large\"}},[_vm._v(_vm._s(user.name))])}),1)]):_vm._e()])}),0)]):_vm._e(),(_vm.next_list.length > 0)?_c('div',{staticClass:\"content\"},[_c('div',{staticStyle:{\"margin-bottom\":\"15px\",\"font-weight\":\"600\"}},[_vm._v(\" 发起申请 \")]),_c('van-radio-group',{model:{value:(_vm.next_id),callback:function ($$v) {_vm.next_id=$$v},expression:\"next_id\"}},[_c('van-cell-group',{attrs:{\"inset\":\"\"}},_vm._l((_vm.next_list),function(item,index){return _c('van-cell',{attrs:{\"title\":item.name,\"clickable\":\"\"},on:{\"click\":function($event){return _vm.selectItem(index)}},scopedSlots:_vm._u([{key:\"right-icon\",fn:function(){return [_c('van-radio',{attrs:{\"checked-color\":\"#008000\",\"name\":item.id}})]},proxy:true}],null,true)})}),1)],1)],1):_vm._e()]),(_vm.data.auth > 0)?_c('div',{staticClass:\"footer\"},[_c('van-button',{staticClass:\"btn-comment\",attrs:{\"type\":\"default\"},on:{\"click\":_vm.addComment}},[_c('div',{staticClass:\"icon\"},[_c('van-icon',{attrs:{\"name\":\"chat-o\"}})],1),_c('div',{staticClass:\"text\"},[_vm._v(\"添加评论\")])]),(_vm.data.auth == 2)?_c('van-button',{staticClass:\"btn-left\",attrs:{\"plain\":\"\",\"type\":\"info\"},on:{\"click\":_vm.rejectOpen}},[_vm._v(\"拒绝\")]):_vm._e(),(_vm.data.auth == 2)?_c('van-button',{staticClass:\"btn-right\",attrs:{\"type\":\"info\"},on:{\"click\":_vm.passShow}},[_vm._v(\"通过\")]):_vm._e(),(_vm.data.auth == 3)?_c('van-button',{staticClass:\"btn-right-left\",attrs:{\"type\":\"info\"},on:{\"click\":_vm.readSave}},[_vm._v(\"已知晓\")]):_vm._e(),(_vm.data.auth == 4)?_c('van-button',{staticClass:\"btn-left\",attrs:{\"type\":\"danger\"},on:{\"click\":_vm.pressingShow}},[_vm._v(\"催办\")]):_vm._e(),(_vm.data.auth == 4)?_c('van-button',{staticClass:\"btn-right\",attrs:{\"type\":\"danger\"},on:{\"click\":_vm.cancelShow}},[_vm._v(\"撤回\")]):_vm._e()],1):_vm._e(),_c('van-popup',{style:({ height: '30%' }),attrs:{\"position\":\"bottom\"},model:{value:(_vm.cancel_show_flag),callback:function ($$v) {_vm.cancel_show_flag=$$v},expression:\"cancel_show_flag\"}},[_c('div',[_c('van-field',{staticClass:\"input-text\",attrs:{\"type\":\"textarea\",\"autosize\":{ minHeight: 100 },\"placeholder\":\"撤回原因(必填)\",\"maxlength\":\"200\"},model:{value:(_vm.cancel_value),callback:function ($$v) {_vm.cancel_value=$$v},expression:\"cancel_value\"}})],1),_c('div',{staticClass:\"btn-footer\"},[_c('van-button',{staticClass:\"van-button\",attrs:{\"round\":\"\",\"type\":\"danger\"},on:{\"click\":_vm.cancelSave}},[_vm._v(\"提交撤回\")]),_c('van-button',{staticClass:\"van-button\",staticStyle:{\"margin-left\":\"30px\"},attrs:{\"plain\":\"\",\"round\":\"\",\"type\":\"danger\"},on:{\"click\":_vm.cancelHide}},[_vm._v(\"关闭\")])],1)]),_c('van-popup',{style:({ height: '30%' }),attrs:{\"position\":\"bottom\"},model:{value:(_vm.pressing_show_flag),callback:function ($$v) {_vm.pressing_show_flag=$$v},expression:\"pressing_show_flag\"}},[_c('div',[_c('van-field',{staticClass:\"input-text\",attrs:{\"type\":\"textarea\",\"autosize\":{ minHeight: 100 },\"placeholder\":\"催办原因(必填)\",\"maxlength\":\"200\"},model:{value:(_vm.cancel_value),callback:function ($$v) {_vm.cancel_value=$$v},expression:\"cancel_value\"}})],1),_c('div',{staticClass:\"btn-footer\"},[_c('van-button',{staticClass:\"van-button\",attrs:{\"round\":\"\",\"type\":\"danger\"},on:{\"click\":_vm.pressingSave}},[_vm._v(\"提交催办\")]),_c('van-button',{staticClass:\"van-button\",staticStyle:{\"margin-left\":\"30px\"},attrs:{\"plain\":\"\",\"round\":\"\",\"type\":\"danger\"},on:{\"click\":_vm.pressingHide}},[_vm._v(\"关闭\")])],1)])],1):_vm._e()])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding-top\":\"5px\",\"padding-left\":\"15px\"}},[_c('span',{staticStyle:{\"font-weight\":\"400\",\"color\":\"#898989\"}},[_vm._v(\"抄送人\")])])\n}]\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.main[data-v-fc871796]{\\n    background-color: #F2F2F2;\\n    width: 100%;\\n    padding-bottom: 10px;\\n    height: 100vh;\\n    overflow: hidden;\\n}\\n.header[data-v-fc871796]{\\n    width: 100%;\\n    min-height: 100px;\\n    background-color: #FFFFFF;\\n    padding: 15px;\\n    box-sizing: border-box;\\n}\\n.header .title[data-v-fc871796]{\\n    font-size: 24px;\\n    letter-spacing: 1px;\\n}\\n.header .title2[data-v-fc871796]{\\n    font-size: 14px;\\n    color: #898989;\\n    margin-top: 10px;\\n}\\n.header .title3[data-v-fc871796]{\\n    font-size: 16px;\\n    margin-top: 10px;\\n    color: #CAA36D;\\n    font-weight: bold;\\n    letter-spacing: 1px;\\n}\\n.content[data-v-fc871796]{\\n    min-height: 100px;\\n    background-color: #FFFFFF;\\n    margin: 15px;\\n    padding: 15px;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 10px;\\n}\\n.item[data-v-fc871796]{\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-start;\\n    margin-top: 4px;\\n}\\n.item .title[data-v-fc871796]{\\n    width: 45px;\\n    height: 45px;\\n    line-height: 45px;\\n    vertical-align: middle;\\n    background-color: #3296FB;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 5px;\\n    color: #FFFFFF;\\n    text-align: center;\\n    position: relative;\\n    font-size: 16px;\\n}\\n.item .title .border[data-v-fc871796]{\\n    position: absolute;\\n    top: 31px;\\n    left: 31px;\\n    width: 18px;\\n    height: 18px;\\n    line-height: 18px;\\n    border-radius: 9px;\\n    background-color: #FFFFFF;\\n    text-align: center;\\n    padding-top: 1px;\\n}\\n.item .item-content[data-v-fc871796]{\\n    width: 100%;\\n    margin-left: 15px;\\n    color: #888888;\\n}\\n.item .item-content .top[data-v-fc871796] {\\n    min-height: 20px;\\n    line-height: 20px;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: space-between;\\n    font-size: 16px;\\n    margin-top: 2px;\\n    vertical-align: top;\\n}\\n.item .item-content .bottom[data-v-fc871796] {\\n    margin-left: -38px;\\n    border-left: 4px #D2D2D2 solid;\\n    padding-bottom: 10px;\\n    min-height: 55px;\\n}\\n.item .bottom-content[data-v-fc871796]{\\n    margin-left: 35px;\\n    margin-top:5px;\\n    min-height: 40px;\\n    background-color: #F2F2F2;\\n    border-radius: 5px;\\n    padding: 10px;\\n    color: #000000;\\n}\\n.item .bottom-send[data-v-fc871796]{\\n    margin-left: 35px;\\n    margin-top:5px;\\n    font-size: 14px;\\n}\\n.item .title-border[data-v-fc871796]{\\n    background-color: #FFFFFF;\\n    width: 45px;\\n    height: 52px;\\n    z-index: 999;\\n}\\n.flow-title[data-v-fc871796]{\\n    font-size: 20px;\\n    margin-top: 10px;\\n    margin-bottom: 15px;\\n    font-weight: bold;\\n    letter-spacing: 2px;\\n}\\n.footer[data-v-fc871796]{\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 56px;\\n    border-top: 1px #E2E2E2 solid;\\n    background-color: #FFFFFF;\\n    z-index: 1000;\\n    box-sizing: border-box;\\n    padding-top: 5px;\\n    padding-right: 15px;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-end;\\n}\\n.footer .btn-left[data-v-fc871796]{\\n    border-radius: 0;\\n    border-top-left-radius:22px;\\n    border-bottom-left-radius:22px;\\n    font-size: 18px;\\n    width: 90px;\\n    margin-right: 1px;\\n}\\n.footer .btn-right[data-v-fc871796]{\\n    border-radius: 0;\\n    border-top-right-radius:22px;\\n    border-bottom-right-radius:22px;\\n    font-size: 18px;\\n    width: 90px;\\n}\\n.footer .btn-right-left[data-v-fc871796]{\\n    border-radius: 0;\\n    border-top-left-radius:22px;\\n    border-bottom-left-radius:22px;\\n    border-top-right-radius:22px;\\n    border-bottom-right-radius:22px;\\n    font-size: 18px;\\n    width: 90px;\\n}\\n.footer .btn-comment[data-v-fc871796]{\\n    border:0;\\n    margin-right: 10px;\\n}\\n.footer .btn-comment .icon[data-v-fc871796]{\\n    font-size: 26px;\\n    color: #929292;\\n}\\n.footer .btn-comment .text[data-v-fc871796]{\\n    font-size: 14px;\\n    color: #929292;\\n    margin-top: -5px;\\n}\\n.input-text[data-v-fc871796] {\\n    font-size: 18px;\\n}\\n.van-button[data-v-fc871796]{\\n    width: 120px;\\n    font-size: 17px;\\n}\\n.btn-footer[data-v-fc871796]{\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    background-color: #FFFFFF;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: center;\\n    padding-bottom: 10px;\\n}\\n.file-box[data-v-fc871796] {\\n    width: 79%;\\n    display: flex;\\n    flex-direction: column;\\n}\\n.file-item[data-v-fc871796] {\\n    margin-bottom: 20px;\\n    font-size: 18px;\\n    font-style: italic;\\n    color: #3a94ec;\\n}\\n.file-box > .file-item[data-v-fc871796]:last-child {\\n    margin-bottom: 0;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=fc871796&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"5c9599b3\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=fc871796&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=fc871796&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=fc871796&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=fc871796&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fc871796\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('fc871796')) {\n      api.createRecord('fc871796', component.options)\n    } else {\n      api.reload('fc871796', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=fc871796&scoped=true\", function () {\n      api.rerender('fc871796', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/work/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=fc871796&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=fc871796&scoped=true\""], "names": [], "sourceRoot": ""}