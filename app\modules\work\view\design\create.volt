{% do assets.collection('css').addCss('static/global/plugins/jsplumb/css/jsplumbtoolkit-defaults.css') %}
{% do assets.collection('css').addCss('static/global/plugins/jsplumb/css/main.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jsplumb/js/jsplumb.js') %}
{% do assets.collection('css').addCss('static/global/plugins/sweetalert/sweetalert.css') %}
{% do assets.collection('js').addJs('static/global/plugins/sweetalert/sweetalert.min.js') %}
{{ assets.outputJs('validate') }}
<!-- Main content -->
<div id="app" data-demo-id="flowchart" class="wrapper wrapper-content page-content" style="margin-bottom: 30px">
    <div class="jtk-demo-canvas canvas-wide flowchart-demo jtk-surface" id="canvas">
        <div><i @click="create" class="fa fa fa-plus-square" style="font-size: 50px;margin-top:25px;margin-left: 15px;cursor:pointer"></i></div>
        <div><i @click="save" class="fa fa-save" style="font-size: 50px;margin-top:25px;margin-left: 15px;cursor:pointer"></i></div>

        <div class="window jtk-node" v-for="(d,index) in anchor" :id="d.id" :style="{top:d.y,left:d.x}">
            <div v-if="d.fixed == 0" class="portlet light bordered" style="width: 100%;height: 100%;margin: 0;padding: 0">
                <div class="portlet-title" style="margin-bottom: 0;padding-left: 10px;padding-right: 10px;padding-top:10px">
                    <div class="caption">
                        <i class="icon-share font-blue"></i>
                        <span class="caption-subject font-blue bold uppercase">${d.name}</span>
                        <span v-if="d.type == 2" class="caption-subject font-blue bold uppercase">(或签)</span>
                        <span v-if="d.type == 1" class="caption-subject font-blue bold uppercase">(会签)</span>
                        <span v-if="d.type == 3" class="caption-subject font-blue bold uppercase">(终签)</span>
                    </div>
                    <div class="actions">
                        <div class="btn-group">
                            <a class="btn green btn-outline btn-circle btn-sm" href="javascript:;" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" aria-expanded="false">
                                <i class="fa fa-angle-down"></i>
                            </a>
                            <ul class="dropdown-menu pull-right">
                                <li>
                                    <a data-toggle="modal" @click="editItem(index)" href="#edit_user"><i class="fa fa-fw fa-pencil"></i> 编辑</a>
                                </li>
                                <li>
                                    <a href="javascript:;" @click="delItem(index)"><i class="fa fa-fw fa-times"></i> 删除</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="portlet-body" style="height: 175px;overflow: auto">
                    <table class="table table-hover table-light">
                        <tbody>
                        <tr v-for="(item,idx) in d.list">
                            <td align="left">
                                <div style="display: flex;flex-direction: row;justify-content: space-between">
                                    <a href="javascript:;" class="primary-link"><i class="fa fa-fw fa-user-o"></i> ${item.name}</a>
                                    <a href="javascript:;" @click="notifyClick(index,idx)" v-if="item.notify == 1" class="primary-link"><i class="fa fa-fw fa-bell" style="color: red"></i></a>
                                    <a href="javascript:;" @click="notifyClick(index,idx)" v-else class="primary-link"><i class="fa fa-fw fa-bell-o"></i></a>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div v-else class="portlet light bordered" style="width: 100%;height: 100%;margin: 0;padding: 0">
                <div class="portlet-title" style="margin-bottom: 0;padding-left: 10px;padding-right: 10px;padding-top:10px">
                    <div class="caption">
                        <i class="icon-share font-green"></i>
                        <span class="caption-subject font-blue bold uppercase">${d.name}</span>
                    </div>
                    <div class="actions">
                        <div class="btn-group">
                            <a class="btn green btn-outline btn-circle btn-sm" href="javascript:;" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" aria-expanded="false">
                                <i class="fa fa-angle-down"></i>
                            </a>
                            <ul class="dropdown-menu pull-right">
                                <li>
                                    <a data-toggle="modal" @click="editItem(index)" href="#edit_user"><i class="fa fa-fw fa-pencil"></i> 编辑</a>
                                </li>
                                <li>
                                    <a href="javascript:;" @click="delItem(index)"><i class="fa fa-fw fa-times"></i> 删除</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="portlet-body" style="height: 175px;overflow: auto">
                    <table class="table table-hover table-light">
                        <tbody>
                        <tr v-for="(item,idx) in d.list">
                            <td align="left">
                                <div style="display: flex;flex-direction: row;justify-content: space-between">
                                    <a href="javascript:;" class="primary-link"><i class="fa fa-fw fa-user-o"></i> ${item.name}</a>
                                    <a href="javascript:;" v-if="item.notify == 1" class="primary-link"><i class="fa fa-fw fa-bell" style="color: red"></i></a>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div id="edit_user" class="modal fade" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">编辑</h4>
                </div>
                <div class="modal-body" style="height: 500px;overflow: auto">
                    <div class="mt-element-ribbon bg-grey-steel" style="padding-top: 10px;padding-bottom: 10px">
                        <div class="ribbon ribbon-vertical-left ribbon-color-primary uppercase">
                            <i class="fa fa-star"></i>
                        </div>
                        <div class="row">
                            <div class="col-md-7">
                                <label class="col-xs-4 control-label" style="text-align: right;margin-top:8px">名称：</label>
                                <div class="col-xs-8">
                                    <input type="text" class="form-control" v-model="flow_name" placeholder="请输入节点名称" maxlength="10">
                                </div>
                            </div>
                            <div class="col-md-5">
                                <label class="col-xs-5 control-label" style="text-align: right;margin-top:8px">类型：</label>
                                <div class="col-xs-7">
                                    <select name="flow_type" class="form-control bs-select" v-model="flow_type">
                                        <option value="1">会签</option>
                                        <option value="2">或签</option>
                                        <option value="3">终签</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-element-ribbon bg-grey-steel" style="padding-top: 10px;padding-bottom: 10px">
                        <div class="row">
                            <div class="col-md-8 to-right">
                                <label class="col-xs-4 control-label" style="text-align: right;margin-top:8px">姓名：</label>
                                <div class="col-xs-8 input-group" style="margin-left: 100px">
                                    <input type="text" class="form-control" v-model="select_name" placeholder="请输入人员姓名" maxlength="10">
                                </div>
                            </div>
                            <div class="col-md-4 to-right">
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default btn-flat" @click="selectUser"><i class="fa fa-search"></i></button>
                                </span>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default btn-flat" @click="finishUser"><i class="fa fa-repeat"></i></button>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-element-ribbon bg-grey-steel" v-for="(group,index) in users" v-if="select_group == '' || select_group == group.id" style="padding-top: 60px;padding-bottom: 10px">
                        <div class="ribbon ribbon-border-hor ribbon-clip ribbon-color-danger uppercase">
                            <div class="ribbon-sub ribbon-clip"></div> ${group.name} </div>
                        <div class="row">
                            <div v-for="(user,idx) in group.list" v-if="select_user == '' || select_user == user.id" class="col-md-3">
                                <div @click="userSelect(index,idx)" v-if="user.checked == 0" style="width: 115px;margin-bottom: 8px;height: 30px;text-align: center;background-color: #ffffff;line-height: 30px;vertical-align: middle;cursor:pointer">
                                    ${user.name}
                                </div>
                                <div @click="userSelect(index,idx)" v-else style="width: 115px;margin-bottom: 8px;height: 30px;text-align: center;background-color: #3598DC;line-height: 30px;vertical-align: middle;cursor:pointer;color: #FFFFFF">
                                    ${user.name}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn dark btn-outline">关闭</button>
                    <button type="button" data-dismiss="modal" class="btn green" @click="saveItem">保存</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var instance = null;
    var connects = {{ connects }};
    var source_id = null;
    var app = new Vue({
        el: '#app',
        data:{
            id:'{{ id }}',
            name:'{{ name }}',
            users:{{ users }},
            flow_idx:'',
            flow_name:'',
            flow_type:1,
            anchor:{{ anchor }},
            select_name:'',
            select_user:'',
            select_group:''
        },
        created:function (instance) {
        },
        methods: {
            create:function () {
                var uuid = getUuid();
                var item = {id:uuid,name:'业务审批',fixed:0,type:1,x:'10px',y:'70px',target:'',list:[],flag:0};
                this.anchor.push(item);
                setTimeout(function () {
                    _addEndpoints(instance,item);
                },100);
            },
            createFixed:function (idx){
                var a = this.fixedAnchors[idx];
                for(let i of this.anchor){
                    if (i.id == a.key && i.flag == 0){
                        toastr.error('业务节点不能重复添加');
                        return;
                    }
                }
                var item = {id:a.key,name:a.name,fixed:1,type:1,x:'10px',y:'70px',target:'',list:[],flag:0};
                this.anchor.push(item);
                setTimeout(function () {
                    _addEndpoints(instance,item);
                },100);
            },
            editItem:function (index) {
                this.flow_idx = index;
                this.flow_name = this.anchor[index].name;
                this.flow_type = parseInt(this.anchor[index].type);
                var flow_list = this.anchor[index].list;
                for(var i = 0; i < this.users.length; i++){
                    var list = this.users[i].list;
                    for(var j = 0; j < list.length; j++){
                        list[j].checked = 0;
                        for(var k = 0; k < flow_list.length; k++){
                            if (list[j].id == flow_list[k].id){
                                list[j].checked = 1;
                                break;
                            }
                        }
                    }
                }
            },
            delItem:function (index) {
                var flow_id = this.anchor[index].id;
                _delEndpoints(flow_id);
                this.anchor[index].flag = 1;
                var left_point = flow_id+left_middle;
                var right_point = flow_id+right_middle;
                for(var key in connects){
                    if (key == left_point || key == right_point){
                        delete connects[key];
                    } else if (connects[key] == left_point || connects[key] == right_point){
                        delete connects[key];
                    }
                }
            },
            saveItem:function () {
                var item = this.anchor[this.flow_idx];
                item.name = this.flow_name;
                item.type = this.flow_type;
                var item_list = item.list;
                var user_list = [];
                for(var i = 0; i < this.users.length; i++){
                    var list = this.users[i].list;
                    for(var j = 0; j < list.length; j++){
                        var notify = 0;
                        if (list[j].checked == 1){
                            if (item.fixed == 0){
                                for(var l of item_list){
                                    if (l.id == list[j].id){
                                        notify = l.notify
                                        break;
                                    }
                                }
                            } else {
                                notify = 1;
                            }
                            user_list.push({
                                id:list[j].id,
                                name:list[j].name,
                                notify:notify
                            });
                        }
                    }
                }
                item.list = user_list;
            },
            userSelect:function (index,idx) {
                if (this.users[index].list[idx].checked == 0){
                    this.users[index].list[idx].checked = 1;
                } else {
                    this.users[index].list[idx].checked = 0;
                }
            },
            notifyClick:function (index,idx) {
                if (this.anchor[index].list[idx].notify){
                    this.anchor[index].list[idx].notify = 0;
                } else {
                    this.anchor[index].list[idx].notify = 1;
                }
            },
            save:function () {
                var ret_data = this.getFlowData();
                if (ret_data.msg != ''){
                    alertWarning(ret_data.msg);
                    return;
                }
                swal({
                    title: "确认要保存吗？",
                    type: "input",
                    showCancelButton: true,
                    inputPlaceholder: "请输入流程名称",
                    inputValue: app.name,
                    confirmButtonText: "确定",
                    cancelButtonText: "取消"
                }, function (isConfirm) {
                    if (!(isConfirm === false)) {
                        showSpin();
                        $.post('{{ url('work/design/save') }}',{id : app.id ,name: isConfirm ,flow:encodeURI(JSON.stringify(ret_data.list)),connects:encodeURI(JSON.stringify(connects)) }, function (rs) {
                            closeSpin();
                            if (rs.status == 'ok') {
                                toastr.success('保存成功！');
//                                top.window.layer_result = 'ok';
//                                top.layer.close(top.layer.getFrameIndex(window.name));
                            }
                            else {
                                toastr.error('操作失败！' + rs.message);
                            }
                        })
                    }
                });
            },
            getFlowData:function () {
                var flow_list_1 = [];
                var target_empty_cnt = 0;
                var i =0;
                var anchor = null;
                for (i = 0; i<this.anchor.length; i++){
                    if (this.anchor[i].flag == 0){
                        if (this.anchor[i].fixed == 0){
                            if (this.anchor[i].list.length == 0){
                                return {msg : this.anchor[i].name+',请添加审批人'};
                            }
                            var notify = 1;
                            for(var l of this.anchor[i].list){
                                if (l.notify == 0){
                                    notify = 0;
                                    break;
                                }
                            }
                            if (notify == 1){
                                if (this.anchor.length > 1){
                                    return {msg:this.anchor[i].name+',请添加审批人'};
                                }
                            }
                        }
                        flow_list_1.push(this.anchor[i]);
                    }
                }
                if (flow_list_1.length == 0){
                    return {msg:'请添加节点'};
                }
                var flow_list = [];
                if (flow_list_1.length == 1){
                    flow_list_1[0].x = $('#'+flow_list_1[0].id).css('left');
                    flow_list_1[0].y = $('#'+flow_list_1[0].id).css('top');
                    flow_list = flow_list_1;
                } else {
                    for (i = 0; i< flow_list_1.length; i++){
                        anchor = flow_list_1[i];
                        var left_point = anchor.id+left_middle;
                        var right_point = anchor.id+right_middle;
                        anchor.target = '';
                        var flag = true;
                        var srouce_cnt = 0;
                        if (connects[left_point] && connects[right_point]){
                            return {msg : anchor.name+',请正确连接节点'};
                        }
                        for (var key in connects){
                            if (connects[key] == left_point || connects[key] == right_point){
                                srouce_cnt ++;
                                var c = false;
                                if (connects[key] == left_point){
                                    if (connects[right_point] === undefined){
                                        c = true;
                                    }
                                } else {
                                    if (connects[left_point] === undefined){
                                        c = true;
                                    }
                                }
                                if (c){
                                    target_empty_cnt++;
                                }
                            }
                        }
                        if (srouce_cnt > 1){
                            return {msg : anchor.name+',请正确连接节点'};
                        }
                        for (var key in connects){
                            if (key == left_point || key == right_point){
                                flag = false;
                                anchor.target = connects[key].replace(left_middle,'').replace(right_middle,'');
                                flow_list.push(anchor);
                                break;
                            }
                        }
                        if (flag){
                            for (var key in connects){
                                if (connects[key] == left_point || connects[key] == right_point){
                                    flow_list.push(anchor);
                                    flag = false;
                                    break;
                                }
                            }
                        }
                        if (flag){
                            return {msg : anchor.name+',请正确连接节点'};
                        }
                    }
                    if (flow_list.length == 0){
                        return {msg:'请连接节点'};
                    }
                    if (target_empty_cnt == 0){
                        return {msg:'流程不能是闭环'};
                    }
                    if (target_empty_cnt > 1){
                        return {msg:'流程只能有一条流'};
                    }
                    var flow_list_sort = [];
                    for (i = 0; i< flow_list.length; i++){
                        anchor = flow_list[i];
                        if (anchor.target == ''){
                            anchor.x = $('#'+anchor.id).css('left');
                            anchor.y = $('#'+anchor.id).css('top');
                            flow_list_sort.push(anchor);
                            flow_list.splice(i,1);
                            break;
                        }
                    }
                    for (i = 0; i< 1000; i++){
                        if (flow_list.length == 0){
                            break;
                        }
                        for (var j = 0; j< flow_list.length; j++){
                            anchor = flow_list[j];
                            if (flow_list_sort[0].id == anchor.target){
                                anchor.x = $('#'+anchor.id).css('left');
                                anchor.y = $('#'+anchor.id).css('top');
                                flow_list_sort.splice(0,0,anchor);
                                flow_list.splice(j,1);
                                break;
                            }
                        }
                    }
                    flow_list = flow_list_sort;
                    // var fixed_id = '';
                    // for(var item of flow_list){
                    //     if (item.fixed == 1){
                    //         if (fixed_id == ''){
                    //             fixed_id = item.id;
                    //         } else {
                    //             if (fixed_id > item.id){
                    //                 return {msg:'请按正确顺序添加业务节点'};
                    //             } else {
                    //                 fixed_id = item.id;
                    //             }
                    //         }
                    //     }
                    // }
                }
                return {msg:'',list:flow_list};
            },
            selectUser:function (){
                if(this.select_name != ''){
                    $.post('{{ url('work/design/selectuser') }}',  {
                        select_name: this.select_name
                    }, function(rs) {
                        if(rs != ''){
                            rs = JSON.parse(rs)
                            app.$data.select_user = rs.id;
                            app.$data.select_group = rs.group_id;
                        }else{
                            toastr.warning('输入不正确');
                            return'';
                        }
                    });
                }
            },
            finishUser:function (){
                app.$data.select_user = '';
                app.$data.select_name = '';
                app.$data.select_group = '';
            }
        }
    });

    function getUuid() {
        var s = [];
        var hexDigits = "0123456789abcdef";
        for (var i = 0; i < 10; i++) {
            s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
        }
        return s.join("");
    }

    $(function() {
        initSize();
        flowInit(app.anchor,connects,function (ins) {
            instance = ins;
            instance.bind("click", function (conn, originalEvent) {
                //console.log(1);
            });
            instance.bind("connectionDrag", function (conn) {
                var anchor = conn.endpoints[0].anchor;
                source_id = anchor.elementId + anchor.type;
            });
            instance.bind("connectionDragStop", function (conn) {
                if (conn.endpoints != null){
                    var anchor = conn.endpoints[1].anchor;
                    if (anchor.elementId){
                        connects[source_id] = anchor.elementId + anchor.type;
                    }
                } else {
                    if (connects[source_id]){
                        delete connects[source_id];
                    }
                }
            });
            instance.bind("connectionMoved", function (params) {
                //console.log(4);
            });
        })
    });

    function initSize() {
        var h = $(window).height() - 60;
        $(".jtk-demo-canvas").height(h);
    }


</script>
{{ partial('flow') }}
{{ partial('css') }}
