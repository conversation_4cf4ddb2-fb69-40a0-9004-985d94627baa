<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}


<section class="content">
    <div class="box">
        <div class="box-body">
            <div id="tree"></div>
        </div>
        <div class="box-footer" id="app">
            <div class="btn-bar">
                <button class="btn btn-primary" @click="confirm"><i class="fa fa-check"></i> 确定</button>
            </div>
        </div>
    </div>
</section>

<script>
    var app = new Vue({
        el:'#app',
        data:{
            id:'',uid:'',name:''
        },
        methods:{
            confirm:function(){
                var ids= $tree.jstree('get_selected');
                var group_name = "";
                var groups = $tree.jstree().get_selected(true);
                for (var index in groups) {
                    if (index == groups.length-1){
                        group_name += groups[index].text;
                    } else {
                        group_name += groups[index].text + ",";
                    }
                }
                parent.window.result_ids = ids.toString();
                parent.window.result_names = group_name;
                parent.layer.close(parent.layer.getFrameIndex(window.name));
            }
        }
    });

    var $tree = $('#tree');

    $(document).ready(function () {
        $tree.jstree({
            'plugins':['wholerow', 'checkbox'],
            'core': {'data': {{ jsonTree }}}
        });

//        $tree.on('select_node.jstree', function (e, data) {
//            app.id = data.node.id;
//            app.uid = data.node.original.uid;
//            app.name = data.node.text;
//        });
//
//        $tree.on('dblclick.jstree', function (e) {
//            parent.window.layer_result = app.$data;
//            parent.layer.close(parent.layer.getFrameIndex(window.name));
//        });
    });
</script>
