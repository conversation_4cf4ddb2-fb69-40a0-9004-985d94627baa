{% do assets.collection('js').addJs('static/global/plugins/echarts/echarts.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('js').addJs('static/global/js/input-loading.js') %}
{% do assets.collection('css').addCss('static/global/css/input-loading.css') %}
{% do assets.collection('css').addCss('static/pages/css/home.css') %}

<!-- BEGIN CONTENT -->
<div id="app" class="page-content-wrapper">
    <div class="page-content">
        <div style="display: flex;">
            <div class="home-left" style="width:60%">
                <div class="home-box todo-box">
                    <div class="home-box-header">
                        <div class="home-box-title">待办</div>
                    </div>
                    <div class="home-box-body">
                        <div class="todo-group">
                            <div id="myCarousel" class="carousel slide" data-ride="carousel">
                                <div class="carousel-inner">
                                    <div v-for="(tz_list, idx) in tz_page_list" class="item" :class="idx == 0 ? 'active' : ''">
                                        <div class="todo-row">
                                            <div v-for="row in tz_list" class="todo-item-content">
                                                <div @click="openPage(row.url)">
                                                    <div class="todo-icon-group" style="width: 100px;display: flex;justify-content: center">
                                                        <div style="position: absolute;right: 0;top: 0;background-color: #EE5A0B;width: 30px;height: 30px;line-height: 30px;text-align: center;color: #fff;border-radius: 15px;font-size: 15px;font-weight: bold" v-text="row.cnt" v-if="row.cnt > 0"></div>
                                                        <img :src="'/static/pages/img/home/<USER>' + row.color + '.png'">
                                                        <div class="todo-icon"><i :class="row.icon"></i></div>
                                                    </div>
                                                    <div class="todo-body" style="text-align: center;margin-top: 10px">
                                                        <div class="todo-title" v-text="row.name"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="carousel-control left" href="#myCarousel" data-slide="prev">
                                    <div class="todo-arrow">
                                        <i class="fa fa-angle-left"></i>
                                    </div>
                                </a>
                                <a class="carousel-control right" href="#myCarousel" data-slide="next">
                                    <div class="todo-arrow">
                                        <i class="fa fa-angle-right"></i>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-right" style="width:40%;margin-left: 20px">
                <div class="home-box todo-box">
                    <div class="home-box-header">
                        <div class="home-box-title">审批</div>
                        <button type="button" class="btn btn-link" @click="openSearch">审批查询</button>
                    </div>
                    <div class="home-box-body">
                        <div class="todo-group" style="padding-left: 20px;padding-right: 20px">
                            <div class="todo-item-content" style="width: 25%">
                                <div @click="openReview(1)" style="cursor: pointer;">
                                    <div class="todo-icon-group" style="width: 100px;display: flex;justify-content: center">
                                        <div style="position: absolute;right: 0;top: 0;background-color: #EE5A0B;width: 30px;height: 30px;line-height: 30px;text-align: center;color: #fff;border-radius: 15px;font-size: 15px;font-weight: bold" v-text="data_cnt._1" v-if="data_cnt._1 > 0"></div>
                                        <img src="/static/pages/img/home/<USER>">
                                        <div class="todo-icon"><i class="fa fa-list"></i></div>
                                    </div>
                                    <div class="todo-body" style="text-align: center;margin-top: 10px">
                                        <div class="todo-title">待审批</div>
                                    </div>
                                </div>
                            </div>
                            <div class="todo-item-content" style="width: 25%">
                                <div @click="openReview(2)" style="cursor: pointer;">
                                    <div class="todo-icon-group" style="width: 100px;display: flex;justify-content: center">
                                        <div style="position: absolute;right: 0;top: 0;background-color: #EE5A0B;width: 30px;height: 30px;line-height: 30px;text-align: center;color: #fff;border-radius: 15px;font-size: 15px;font-weight: bold" v-text="data_cnt._2" v-if="data_cnt._2 > 0"></div>
                                        <img src="/static/pages/img/home/<USER>">
                                        <div class="todo-icon"><i class="fa fa-check"></i></div>
                                    </div>
                                    <div class="todo-body" style="text-align: center;margin-top: 10px">
                                        <div class="todo-title">已审批</div>
                                    </div>
                                </div>
                            </div>
                            <div class="todo-item-content" style="width: 25%">
                                <div @click="openReview(3)" style="cursor: pointer;">
                                    <div class="todo-icon-group" style="width: 100px;display: flex;justify-content: center">
                                        <div style="position: absolute;right: 0;top: 0;background-color: #EE5A0B;width: 30px;height: 30px;line-height: 30px;text-align: center;color: #fff;border-radius: 15px;font-size: 15px;font-weight: bold" v-text="data_cnt._3" v-if="data_cnt._3 > 0"></div>
                                        <img src="/static/pages/img/home/<USER>">
                                        <div class="todo-icon"><i class="fa fa-star"></i></div>
                                    </div>
                                    <div class="todo-body" style="text-align: center;margin-top: 10px">
                                        <div class="todo-title">已发起</div>
                                    </div>
                                </div>
                            </div>
                            <div class="todo-item-content" style="width: 25%">
                                <div @click="openReview(4)" style="cursor: pointer;">
                                    <div class="todo-icon-group" style="width: 100px;display: flex;justify-content: center">
                                        <div style="position: absolute;right: 0;top: 0;background-color: #EE5A0B;width: 30px;height: 30px;line-height: 30px;text-align: center;color: #fff;border-radius: 15px;font-size: 15px;font-weight: bold" v-text="data_cnt._4" v-if="data_cnt._4 > 0"></div>
                                        <img src="/static/pages/img/home/<USER>">
                                        <div class="todo-icon"><i class="fa fa-bullhorn"></i></div>
                                    </div>
                                    <div class="todo-body" style="text-align: center;margin-top: 10px">
                                        <div class="todo-title">抄送于我</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div  class="home-main" style="height: 72vh">
            <div class="home-box chart-box">
                <div class="home-box-header home-tab">
                    <div class="home-tab-group">
                        <div class="home-tab-item" :class="param.tab_idx == 0 ? 'active' : ''" @click="changeTab(0)">营销</div>
                        <div class="home-tab-item" :class="param.tab_idx == 1 ? 'active' : ''" @click="changeTab(1)">生产运营</div>
                        <div class="home-tab-item" :class="param.tab_idx == 2 ? 'active' : ''" @click="changeTab(2)">技术</div>
                        <div class="home-tab-item" :class="param.tab_idx == 3 ? 'active' : ''" @click="changeTab(3)">质量</div>
                        <div class="home-tab-item" :class="param.tab_idx == 4 ? 'active' : ''" @click="changeTab(4)">供应</div>
                    </div>
                </div>
                <div class="home-box-body">
                    <div class="chart-box-group">
                        <div v-show="chart_loading" class="chart-loading-layer">
                            <div class="input-loading" style="height: 50px;">
                                <div class="loading"></div>
                            </div>
                        </div>
                        <div class="chart-tool-bar">
                            <div class="chart-type-group">
                                <div v-for="(name, type_idx) in type_list" class="chart-type-item" :class="param.type_idx == type_idx ? 'active' : ''" @click="changeType(type_idx)" v-text="name"></div>
                            </div>
                            <div class="input-group" style="width: 300px;">
                                <input type="text" class="date dtpicker-start form-control" name="date_begin" v-model="param.date_begin">
                                <span class="input-group-addon no-border-lr">至</span>
                                <input type="text" class="date dtpicker-end form-control" name="date_end" v-model="param.date_end">
                                <span class="input-group-btn">
                                    <button type="button" class="btn blue" @click="search"><i class="fa fa-search"></i></button>
                                </span>
                            </div>
                        </div>
                        <div class="chart-sum-bar" v-if="sum_list.length > 0">
                            <div class="chart-sum-item" v-for="row in sum_list" :class="row.color">
                                <img :src="'/static/pages/img/home/<USER>' + row.color + '.png'">
                                <div class="chart-sum-type" v-if="row.type" v-text="row.type"></div>
                                <div class="chart-sum-content">
                                    <div class="chart-sum-title" v-text="row.title"></div>
                                    <div class="chart-sum-val" v-text="row.val"></div>
                                    <div class="chart-sum-unit" v-text="row.unit"></div>
                                </div>
                            </div>
                        </div>
                        <div class="chart-content">
                            <div id="chart_line" style="width: 100%;height: 100%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let chartLine = null;
    let app = new Vue({
        el: '#app',
        data: {
            tz_page_list: {{ data['tz_page_list'] | json_encode }},
            data_cnt: {{ data['data_cnt'] | json_encode }},
            param: {
                date_begin: '{{ date_begin }}',
                date_end: '{{ date_end }}',
                tab_idx: 0,
                type_idx: 0
            },
            chart_loading: true,
            type_list: [],
            sum_list: []
        },
        mounted() {
            this.$nextTick(function() {
                setTimeout(function() {
                    app.search();
                }, 1000);
            });
        },
        methods: {
            search: function() {
                if (!this.param.date_begin || !this.param.date_end) {
                    alertWarning('请选择统计日期');
                    return;
                } else if (this.param.date_begin > this.param.date_end) {
                    alertWarning('统计开始日期不能晚于统计结束日期');
                    return;
                } else {
                    let date1 = new Date(this.param.date_begin);
                    let date2 = new Date(this.param.date_end);
                    let timeDiff = (date2.getTime() - date1.getTime()) / 1000;
                    if (timeDiff / (24 * 3600) > 30) {
                        alertWarning('统计天数不能超过29天');
                        return;
                    }
                }

                this.chart_loading = true;
                $.post('{{ url('sys/home/<USER>') }}', this.param, function(rs) {
                    app.chart_loading = false;
                    if (rs.status == 'ok') {
                        app.type_list = rs.data.type_list;
                        app.sum_list = rs.data.sum_list;
                        app.$nextTick(function() {
                            if (app.param.tab_idx == 1) {
                                updateScOption(rs.data.chart_data);
                            } else if (app.param.tab_idx == 2) {
                                updateJsOption(rs.data.chart_data);
                            } else if (app.param.tab_idx == 3) {
                                updateZlOption(rs.data.chart_data);
                            } else if (app.param.tab_idx == 4) {
                                updateGyOption(rs.data.chart_data);
                            } else {
                                updateLineOption(rs.data.chart_data);
                            }
                        });
                    } else {
                        toastr.error(rs.message);
                    }
                });
            },
            refresh: function() {
                showSpin();
                $.post("{{ url('sys/home/<USER>') }}", function (rs) {
                    closeSpin();
                    app.data_cnt = rs.data_cnt;
                })
            },
            openDetail: function(row, type) {
                top.window.layer_result = '';
                top.layer.open({
                    title: '业务详情',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/view/') }}' + row.uid + '/' + type,
                    end: function() {
                        if (top.window.layer_result == 'ok') {
                            toastr.success('操作完毕');
                            app.refresh();
                        }
                    }
                });
            },
            openReview: function(type) {
                top.window.layer_result = '';
                top.layer.open({
                    title: '审批',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/list/open/') }}' + type,
                    end: function() {
                        app.refresh();
                    }
                });
            },
            openSearch: function() {
                top.window.layer_result = '';
                top.layer.open({
                    title: '审批查询',
                    type: 2,
                    resize: false,
                    area: ['100%', '100%'],
                    content: '{{ url('work/work/search') }}',
                    end: function() {
                        app.refresh();
                    }
                });
            },
            changeTab: function(tab_idx) {
                this.param.tab_idx = tab_idx;
                this.param.type_idx = 0;
                this.search();
            },
            changeType: function(type_idx) {
                this.param.type_idx = type_idx;
                this.search();
            },
            openPage: function(url) {
                location.href = url;
            }
        }
    });

    parent.window.refreshTopData();

    function getChartOption(rs) {
        if (!chartLine) {
            chartLine = echarts.init(document.getElementById('chart_line'));
        }
        return {
            color: ['#3598F6', '#F67A35'],
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['国内', '外销'],
                icon: 'rect',
                itemWidth: 30,
                itemHeight: 4,
                itemGap: 20,
                bottom: 10,
                textStyle: {
                    color: '#000000',
                    fontSize: 14
                }
            },
            grid: {
                top: 30,
                left: 20,
                right: 50,
                bottom: 50,
                containLabel: true
            },
            xAxis: {
                type: 'category',
                name: '日期',
                nameTextStyle: {
                    color: '#101011'
                },
                boundaryGap: false,
                axisLine: {
                    lineStyle: {
                        color: '#E7E7E7'
                    }
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#8E8E8E',
                    formatter: function(value) {
                        return value.substring(5);
                    }
                },
                data: rs.xAxis_data
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#8E8E8E'
                },
                splitLine: {
                    lineStyle: {
                        color: '#E7E7E7'
                    }
                }
            },
            series: []
        };
    }

    function updateLineOption(rs) {
        let option = getChartOption(rs);
        option.yAxis.name = '单位：吨';
        option.series = [
            {
                type: 'line',
                name: '国内',
                symbol: 'circle',
                symbolSize: 10,
                smooth: true,
                itemStyle: {
                    borderColor: '#FFFFFF',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(53, 152, 246, 0.3)' // 0% 处的颜色
                        }, {
                            offset: 1, color: 'rgba(53, 152, 246, 0.1)' // 100% 处的颜色
                        }],
                        global: false // 缺省为 false
                    }
                },
                data: rs.data
            },
            {
                type: 'line',
                name: '外销',
                symbol: 'circle',
                symbolSize: 10,
                smooth: true,
                itemStyle: {
                    borderColor: '#FFFFFF',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(246, 122, 53, 0.3)' // 0% 处的颜色
                        }, {
                            offset: 1, color: 'rgba(246, 122, 53, 0.1)' // 100% 处的颜色
                        }],
                        global: false // 缺省为 false
                    }
                },
                data: rs.data2
            }
        ];
        chartLine.clear();
        chartLine.setOption(option);
    }

    function updateScOption(rs) {
        let option = getChartOption(rs);
        option.legend.data = ['支数', '重量'];
        option.xAxis.boundaryGap = true;
        option.series = [
            {
                type: 'line',
                name: '支数',
                symbol: 'circle',
                symbolSize: 10,
                smooth: true,
                itemStyle: {
                    borderColor: '#FFFFFF',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(53, 152, 246, 0.3)' // 0% 处的颜色
                        }, {
                            offset: 1, color: 'rgba(53, 152, 246, 0.1)' // 100% 处的颜色
                        }],
                        global: false // 缺省为 false
                    }
                },
                data: rs.data
            },
            {
                type: 'bar',
                name: '重量',
                barWidth: 20,
                data: rs.data2
            }
        ];
        chartLine.clear();
        chartLine.setOption(option);
    }

    function updateJsOption(rs) {
        let option = getChartOption(rs);
        option.legend.data = rs.legend;

        let seriesDatas = [];
        seriesDatas.push({
            type: 'line',
            name: rs.legend[0],
            symbol: 'circle',
            symbolSize: 10,
            smooth: true,
            itemStyle: {
                borderColor: '#FFFFFF',
                borderWidth: 2
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(53, 152, 246, 0.3)' // 0% 处的颜色
                    }, {
                        offset: 1, color: 'rgba(53, 152, 246, 0.1)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                }
            },
            data: rs.data
        });
        if (rs.data2.length > 0) {
            seriesDatas.push({
                type: 'bar',
                name: rs.legend[1],
                barWidth: 20,
                data: rs.data2
            });
        }
        option.series = seriesDatas;
        chartLine.clear();
        chartLine.setOption(option);
    }

    function updateZlOption(rs) {
        let option = getChartOption(rs);
        option.color = ['#3598F6', '#F67A35', '#22313F'];
        option.legend.data = ['检验', '缺陷', '废品'];
        option.xAxis.boundaryGap = true;
        option.yAxis.name = '单位：支';
        option.series = [
            {
                type: 'line',
                name: '检验',
                symbol: 'circle',
                symbolSize: 10,
                smooth: true,
                itemStyle: {
                    borderColor: '#FFFFFF',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(53, 152, 246, 0.3)' // 0% 处的颜色
                        }, {
                            offset: 1, color: 'rgba(53, 152, 246, 0.1)' // 100% 处的颜色
                        }],
                        global: false // 缺省为 false
                    }
                },
                data: rs.data
            },
            {
                type: 'bar',
                name: '缺陷',
                barWidth: 12,
                data: rs.data2
            },
            {
                type: 'bar',
                name: '废品',
                barWidth: 12,
                data: rs.data3
            }
        ];
        chartLine.clear();
        chartLine.setOption(option);
    }

    function updateGyOption(rs) {
        let option = getChartOption(rs);
        option.color = ['#3598F6', '#F67A35', '#22313F'];
        option.legend.data = ['合同数', '合同金额', '付款金额'];
        option.xAxis.boundaryGap = true;
        option.yAxis = [
            {
                type: 'value',
                name: '单位：万元',
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#8E8E8E'
                },
                splitLine: {
                    lineStyle: {
                        color: '#E7E7E7'
                    }
                }
            },
            {
                type: 'value',
                name: '单位：份数',
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#8E8E8E'
                },
                splitLine: {
                    show: false
                }
            }
        ];
        option.series = [
            {
                type: 'line',
                name: '合同数',
                yAxisIndex: 1,
                symbol: 'circle',
                symbolSize: 10,
                smooth: true,
                itemStyle: {
                    borderColor: '#FFFFFF',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(53, 152, 246, 0.3)' // 0% 处的颜色
                        }, {
                            offset: 1, color: 'rgba(53, 152, 246, 0.1)' // 100% 处的颜色
                        }],
                        global: false // 缺省为 false
                    }
                },
                data: rs.data
            },
            {
                type: 'bar',
                name: '合同金额',
                barWidth: 12,
                data: rs.data2
            },
            {
                type: 'bar',
                name: '付款金额',
                barWidth: 12,
                data: rs.data3
            }
        ];
        chartLine.clear();
        chartLine.setOption(option);
    }

    let dtpickerOption = {
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: true,
        autoclose: true,
        format: 'yyyy-mm-dd',
        pickerPosition: 'bottom-left'
    };

    $('.dtpicker-start').datetimepicker(dtpickerOption).on('changeDate', function() {
        let val = $(this).val();
        app.param[$(this).attr('name')] = $(this).val();

        let date = new Date(val);
        date.setDate(date.getDate() + 29);
        let date_end = getYmd(date);
        if (app.param.date_end > date_end || app.param.date_end < val) {
            app.param.date_end = date_end;
        }
    });

    $('.dtpicker-end').datetimepicker(dtpickerOption).on('changeDate', function() {
        let val = $(this).val();
        app.param[$(this).attr('name')] = $(this).val();

        let date = new Date(val);
        date.setDate(date.getDate() - 29);
        let date_start = getYmd(date);
        if (app.param.date_begin < date_start || app.param.date_begin > val) {
            app.param.date_begin = date_start;
        }
    });

    function getYmd(date) {
        return date.getFullYear() + '-' + ('0' + (date.getMonth() + 1)).substr(-2) + '-' + ('0' + date.getDate()).substr(-2);
    }
</script>