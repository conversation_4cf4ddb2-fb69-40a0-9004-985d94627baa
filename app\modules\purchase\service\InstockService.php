<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseInstock;
use Envsan\Modules\Purchase\Model\PurchaseInstockDetail;
use Envsan\Modules\Purchase\Model\PurchaseReceiptDetail;
use Envsan\Modules\Purchase\Model\PurchaseStockLogs;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;

class InstockService extends BaseService
{
    private $page_id = 25;

    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.instock_date,
                t1.remarks,
                t1.ext_val,
                t2.order_code,
                t3.name as supplier_name,
                t1.total_money,
                t1.total_money_hs
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInstock', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t99.uid as detail_uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.instock_date,
                t1.remarks,
                t1.ext_val,
                t2.order_code,
                t3.name as supplier_name,
                t99.goods_code,
                t99.goods_name,
                t99.goods_spec,
                t99.goods_model,
                t99.goods_unit,
                t99.price,
                t99.quantity,
                t99.total_money,
                t99.tax_rate,
                t99.total_money_hs,
                t99.check_flag,
                t99.check_result_flag,
                t99.check_status
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInstock', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 't1.id = t99.instock_id', 't99')
            ->where('t1.del_flag = 0 and t99.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectById($id)
    {
        return PurchaseInstock::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseInstock::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function selectDetailByUid($uid)
    {
        return PurchaseInstockDetail::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function create()
    {
        $row = new PurchaseInstock();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($row, $act) {
            $type = urldecode($this->request->getPost('type', ['string', 'trim']));
            // 入库日期
            $instock_date = $this->request->getPost('instock_date', 'tstring');
            // 到货单ID
            $receipt_id = $this->request->getPost('receipt_id', 'tstring');
            // 订单ID
            $order_id = $this->request->getPost('order_id', 'tstring');
            // 供应商ID
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');
            // 备注
            $remarks = $this->request->getPost('remarks', 'tstring');
            // 文件
            $files = urldecode($this->request->getPost('files', ['string', 'trim']));
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
            $detail = str_replace('%2B', '+', urldecode($this->request->getPost('detail', ['string', 'trim'])));
            if (empty($type) || empty($instock_date) || empty($receipt_id) || empty($detail)) {
                return $this->error(ErrorHelper::WRONG_INPUT) ;
            }
            $files = CvtUtil::emptyToArray($files);
            $ext_data = CvtUtil::emptyToArray($ext_data);
            $detail = CvtUtil::emptyToArray($detail);
            $now = DateUtil::now();
            $user = SessionData::user();
            $total_money = 0;
            $total_money_hs = 0;
            foreach ($detail as &$d_item) {
                // 不含税总金额
                $money = CvtUtil::emptyToDouble($d_item['total_amount']);
                // 含税总金额
                $money_hs = CvtUtil::emptyToDouble($d_item['total_amount_hs']);
                // 入库订单的不含税总金额
                $total_money += $money;
                // 入库订单的含税总金额
                $total_money_hs += $money_hs;
            }
            $row->total_money = round($total_money, 4);
            $row->total_money_hs = round($total_money_hs, 4);
            $row->instock_date = $instock_date;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->detail_data = CvtUtil::arrayToNull($detail);;
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            // Save
            if ($type == 1) {
                $row->status = 10;
                // 提交
            } else {
                $row->status = 20;
            }
            $row->status_name = Constant::$purchase_instock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            // 创建入库
            if ($act == 'create') {
                $row->uid = UUID::make();
                $ss = new SequenceService();
                $row->code = $ss->useSequence(13);
                $row->order_id = $order_id;
                $row->supplier_id = $supplier_id;
                $row->receipt_id = $receipt_id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            $row->save();

            if ($row->status == 20) {
                foreach ($detail as $item) {
                    $tax_rate = CvtUtil::emptyToDouble($d_item['tax_rate']);
                    $detail_row = new PurchaseInstockDetail();
                    $detail_row->uid = UUID::make();
                    $receipt_detail_id = $item['id'];
                    $detail_row->instock_id = $row->id;
                    $detail_row->receipt_detail_id = $receipt_detail_id;
                    $detail_row->goods_id = $item['goods_id'];
                    $detail_row->goods_code = $item['goods_code'];
                    $detail_row->goods_name = $item['goods_name'];
                    $detail_row->goods_model = $item['goods_model'];
                    $detail_row->goods_unit = $item['goods_unit'];
                    $detail_row->goods_deputy_unit = $item['goods_deputy_unit'];
                    $detail_row->check_flag = $item['check_flag'];
                    $detail_row->check_images = $item['check_images'];
                    $detail_row->check_status = 10;
                    $detail_row->price = $item['price'];
                    $detail_row->price_hs = $item['price_hs'];
                    $detail_row->quantity = $item['order_quantity'];
                    $detail_row->purchase_quantity = $item['order_purchase_quantity'];
                    $detail_row->total_money = $item['total_amount'];
                    $detail_row->tax_rate = CvtUtil::zeroToNull($tax_rate);
                    $detail_row->total_money_hs = $item['total_amount_hs'];
                    $detail_row->update_date = $now;
                    $detail_row->update_by = $user->id;
                    $detail_row->del_flag = 0;
                    $detail_row->owner = $user->owner;
                    $detail_row->save();

                    $receiptDetail = PurchaseReceiptDetail::findFirst( ['del_flag = 0 and id = ?1', 'bind' => [1 => $receipt_detail_id]]);
                    $receiptDetail->status = 40;
                    $receiptDetail->status_name = '以入库';
                    $receiptDetail->update_date = $now;
                    $receiptDetail->update_by = $user->id;
                    $receiptDetail->save();

                    $logs = new PurchaseStockLogs();
                    $logs->batch_no = $row->code;
                    $logs->data_type = 1;
                    $logs->data_id = $row->id;
                    $logs->sign = 1;
                    $logs->goods_id = $detail_row->goods_id;
                    $logs->goods_name = $detail_row->goods_name;
                    $logs->goods_unit = $detail_row->goods_unit;
                    // 库存计量数量
                    $logs->quantity = $detail_row->quantity;
                    $logs->money = $detail_row->total_money;
                    $logs->money_hs = $detail_row->total_money_hs;
                    $logs->update_date = $now;
                    $logs->update_user = $user->real_name;
                    $logs->del_flag = 0;
                    $logs->owner = $user->owner;
                    $logs->save();
                }
            }
        });
    }

    public function delete(){
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status != 10){
            return '状态不正确';
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->del_flag = 1;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    /**
     * 取消入库
     * @return array
     */
    public function cancel(){
        return $this->executeInTransaction(function () {
            $uid = $this->request->getPost('uid', 'string');
            if (empty($uid)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $row = $this->selectByUid($uid);
            if (empty($row)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            if ($row->status != 20) {
                return $this->error('状态不正确');
            }
            $now = DateUtil::now();
            $user = SessionData::user();
            $instock_rows = PurchaseInstockDetail::find(['del_flag = 0 and instock_id = ?1', 'bind' => [1 => $row->id]]);
            foreach ($instock_rows as $instock_row) {
                if ($instock_row->check_status != 10) {
                    return $this->error('已经检验不能取消');
                }
                $receipt_detail = PurchaseReceiptDetail::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $instock_row->receipt_detail_id]]);
                $receipt_detail->status = 30;
                $receipt_detail->status_name = Constant::$purchase_receipt_status[$receipt_detail->status];
                $receipt_detail->update_date = $now;
                $receipt_detail->update_by = $user->id;
                $receipt_detail->save();
            }
            $instock_rows->delete();
            $log_rows = PurchaseStockLogs::find(['del_flag = 0 and data_type = 1 and data_id = ?1', 'bind' => [1 => $row->id]]);
            $log_rows->delete();
            $row->status = 10;
            $row->status_name = Constant::$purchase_instock_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->save();
        });

    }

    public function getDetailList($receipt_id)
    {
        // 查询收货单头部信息
        $headerBuilder = $this->modelsManager->createBuilder()
            ->columns('
                id,
                order_code,
                order_id,
                supplier_name,
                supplier_id,
                receipt_code
            ')
            ->from('Envsan\Modules\Purchase\Model\PurchaseReceipt')
            ->where('id = ?1 AND del_flag = 0', [1 => $receipt_id]);

        $headerInfo = $headerBuilder->getQuery()->execute()->getFirst();

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.goods_id,
                t1.goods_code,
                t1.goods_name,
                t1.goods_model,
                t1.goods_unit,
                t1.goods_deputy_unit,
                t4.order_code,
                ifnull(t1.tax_rate,0) as tax_rate,
                t1.price_hs,
                t1.price,
                t1.unit_conversion_rate,
                round(if (t6.id is not null, ifnull(t6.available_qty, 0), ifnull(t1.quantity, 0)), 4) as order_quantity,
                round(if (t6.id is not null, ifnull(t6.available_qty, 0) * ifnull(t1.unit_conversion_rate, 1), ifnull(t1.quantity, 0) * ifnull(t1.unit_conversion_rate, 1)), 4) as order_purchase_quantity,
                round(ifnull(t2.quantity,0), 4) as instock_quantity,
                round(if (t6.id is not null, ifnull(t6.available_qty, 0) * ifnull(t1.price, 0), ifnull(t1.quantity, 0) * ifnull(t1.price, 0)), 2) as total_amount,
                round(if (t6.id is not null, ifnull(t6.available_qty, 0) * ifnull(t1.price_hs, 0), ifnull(t1.quantity, 0) * ifnull(t1.price_hs, 0)), 2) as total_amount_hs,
                t1.tax_rate,
                t1.tax_amount,
                t1.check_flag,
                1 as show,
                t4.supplier_name,
                t4.receipt_code,
                t4.receipt_date,
                t5.id as inspection_id,
                t5.check_images,
                t5.check_result_flag,
                t5.check_user_id,
                t6.id as defect_id,
                t6.rejected_qty,
                t6.available_qty,
                t6.handling_method,
                t6.handling_method_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseReceiptDetail', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseReceipt', 't1.receipt_id = t4.id', 't4')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewOrderInstock', 't4.order_id = t2.order_id and t2.goods_id = t1.goods_id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInspectionDetail', 't1.id = t5.receipt_detail_id and t5.del_flag = 0', 't5')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseDefectHandling', 't5.id = t6.inspection_id and t6.del_flag = 0 and t6.status = 30', 't6')
            ->where('
                t1.del_flag = 0 
                and t1.receipt_id = ?1  
                and (
                    t5.id is null or 
                    (t5.check_status = 20 and t5.check_result_flag = 0) or
                    (t5.check_status = 20 and t5.check_result_flag = 1 and t6.id is not null and t6.handling_method != ?2)
                )
                ', [1 => $receipt_id, 2 => 'all_reject'])
            ->orderBy('t1.id asc');
        $rows = $builder->getQuery()->execute();
        return $this->success([
            'rows' => $rows,
            'headerInfo' => $headerInfo
        ]);
    }

    public function getDetail($instock_id)
    {
        $rows = PurchaseInstockDetail::find(['del_flag = 0 and instock_id = ?1','bind'=>[1=>$instock_id]])->toArray();
        foreach ($rows as &$row)
        {
            $row['check_images'] = CvtUtil::emptyToArray($row['check_images']);
        }
        return $rows;
    }

    public function getInstockDetailList($id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.goods_code,
                a.goods_name,
                a.goods_spec,
                a.goods_model,
                a.goods_unit,
                a.price,
                a.quantity,
                a.total_money,
                a.tax_rate,
                a.total_money_hs
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 'a')
            ->where('a.del_flag = 0 and a.instock_id = ?1', [1 => $id])
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }

    public function selectInStockByUid($uid)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.code,
                t1.instock_date,
                t1.order_id,
                t1.supplier_id,
                t1.receipt_id,
                t1.remarks,
                t1.files,
                t1.ext_data,
                t1.detail_data,
                t1.ext_val,
                t1.total_money,
                t1.total_money_hs,
                t1.update_date,
                t1.update_by,
                t1.del_flag,
                t1.owner,
                t2.name as supplier_name,
                t3.order_code,
                t4.receipt_code
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInstock', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseReceipt', 't1.receipt_id = t4.id', 't4')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid]);
        $result = $builder->getQuery()->execute()->getFirst();

        if ($result != null) {
            return $result->toArray();
        }
        return [];
    }
}