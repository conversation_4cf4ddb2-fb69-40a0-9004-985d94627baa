{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">数据字典管理</h3>
    <div id="app" class="search-page">
        <div class="search-table">
            <table id="table" class="table table-bordered table-striped table-hover table-big">
                <thead class="bg-blue">
                <tr>
                    <th>序号</th>
                    <th>名称</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,idx) in list">
                    <td><span v-text="idx+1"></span></td>
                    <td><span v-text="item.name"></span></td>
                    <td>
                        <button type="button" class="btn blue btn-outline" @click="edit(item)">编辑</button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            list: {{ dictList }}
        },
        methods: {
            edit:function (item) {
                top.window.layer_result = '';
                top.layer.open({
                    title: item.name,
                    type: 2,
                    area: ['100%', '100%'],
                    content: '{{ url('sys/dict/edit/') }}' + item.type,
                    end: function () {
                        if (top.window.layer_result == 'ok') {
                            toastr.success('操作完毕');
                        }
                    }
                });
            }
        }
    });
</script>
