<?php

namespace Envsan\Modules\Sys\Model;
use Envsan\Common\Model\BaseModel;
class ExtendColumn extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $page_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $page_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $form_data;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $update_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_extend_column';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return ExtendColumn[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return ExtendColumn
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
