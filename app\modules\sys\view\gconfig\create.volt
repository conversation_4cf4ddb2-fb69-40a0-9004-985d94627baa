{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    键<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="section" v-model="section" required>                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    类型<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <select class="form-control" name="type" v-model="type" required>
                                        <option value="">请选择</option>
                                        <option value="list">数组</option>
                                        <option value="string">字符串</option>
                                        <option value="json">对象</option>
                                        <option value="int">整数</option>
                                        <option value="float">浮点数</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    值<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <textarea class="form-control" name="val" v-model="val" required></textarea>                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    说明<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="comment" v-model="comment" required>                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    用户是否可见
                                </label>
                                <div class="col-sm-8">
                                    <input type="checkbox" name="visible" v-model="visible">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <p class="text-orange"><i class="fa fa-exclamation-circle"></i> 创建之后不能删除，也不能修改除了值之外的任何字段!</p>
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonGroupConfig }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                var url= '{{ url('sys/gconfig/create') }}';
                $.post(url, app.$data, function (rs) {
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });

    app.visible = 1;
</script>
