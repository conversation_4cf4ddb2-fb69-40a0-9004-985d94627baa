<?php
namespace Envsan\Modules\Equ\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Equ\Model\EquItemMaintainLogs;
use Envsan\Modules\Equ\Service\EquMaintainService;
use Envsan\Modules\Work\Service\MaintainService;


/**
 * @name('保养计划')
 */
class MaintainController extends SuperController
{

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new EquMaintainService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setDetail($page->rows->toArray());
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'equ:maintain:list'})
     */
    public function createAction()
    {
        $rs = new EquMaintainService();
        $cs = new MaintainService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new EquItemMaintainLogs())->toArray();
        $this->view->equ_list = $cs->getEquList();
        $this->view->form_list = $cs->getFormList();
        $this->view->jsonLog = json_encode($jrow);
    }

    /**
     * @acl({'link':'equ:maintain:list'})
     */
    function editAction($id='')
    {
        $rs = new EquMaintainService();
        $cs = new MaintainService();
        $row = $rs->selectById($id);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        $this->view->id = $id;
        $this->view->jsonLog = json_encode($jrow);
        $this->view->equ_list = $cs->getEquList();
        $this->view->form_list = $cs->getFormList();
        $this->view->pick('maintain/create');
    }

    /**
     * @acl({'link':'equ:maintain:list'})
     */
    function viewAction($id='')
    {
        $rs = new EquMaintainService();
        $row = $rs->selectById($id);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
        $this->view->r_id = $id;
        $this->view->jsonLog = json_encode($jrow);
    }

    /**
     * @acl({'link':'equ:maintain:list'})
     */
    public function deleteAction(){
        $s = new EquMaintainService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'equ:maintain:list'})
     */
    public function exportlistAction()
    {
        $s = new EquMaintainService();
        $s->exportListExcel();
    }

    /**
     * @acl({'link':'equ:maintain:list'})
     */
    public function exportAction($id)
    {
        $s = new EquMaintainService();
        $s->exportExcel($id);
    }
}