{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    模板名称<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" placeholder="请输入名称" name="template_name" v-model="template_name" required maxlength="100"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    打印页面<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    {% if dispatcher.getActionName() == 'edit' %}
                                        <input type="text" class="form-control" placeholder="请输入名称" name="page_name" v-model="page_name" readonly/>
                                    {% else %}
                                        <select class="bs-select form-control" name="page_id" v-model="page_id" data-size="8" data-live-search="true" required>
                                            <option value="">请选择</option>
                                            {% for key,val in page_list %}
                                                <option value="{{ key }}">{{ val }}</option>
                                            {% endfor %}
                                        </select>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">默认使用</label>
                                <div class="col-sm-8">
                                    <a href="javascript:;" @click="default_flag == 1 ? default_flag = 0 : default_flag = 1" :style="{color:default_flag == 1 ? '#00FF00' : '#C0C0C0',fontSize:'20px'}" >
                                        <i class="fa fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">状态</label>
                                <div class="col-sm-8">
                                    <select name="status" class="bs-select form-control" v-model="status" required>
                                        <option value="1">启用</option>
                                        <option value="0">停用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonForm }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{url('printing/template/edit/' ~ uid)}}';
                {% else %}
                var url= '{{ url('printing/template/create') }}';
                {% endif %}
                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>
