<div class="btn-select-file" style="display: none;"></div>
<div id="btn_select_file" class="btn-select-file" style="display: none;">选择文件</div>
<script>
    var uploader = null;
    function initUpLoaderPdf(folder_name) {
        uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/main/lib/uploader/uploader.swf') }}',
            server: '{{ upyun.host ~ '/' ~ upyun.bucket }}',
            pick: '.btn-select-file',
            resize: false,
            fileSingleSizeLimit: 200 * 1024 * 1024,
            fileNumLimit: 1,
            duplicate: true
        });

        uploader.on('error', function(type) {
            if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，文件大小不能超过200M');
            }
        });

        uploader.on('beforeFileQueued', function(file) {
            uploader.reset();
        });

        uploader.on('fileQueued', function(file) {
            let ext = file.name.split('.').pop().toLowerCase();
            let now = new Date();
            let date = now.toGMTString();
            let year = now.getFullYear();
            let month = ('0' + (now.getMonth() + 1)).slice(-2);
            let day = ('0' + now.getDate()).slice(-2);
            let remote_path = '{{ upyun.baseDir }}{{ owner.id }}/' + folder_name + '/' + year + '/' + month + '/' + day + '/';
            let file_name = remote_path + now.getTime() + '.' + ext;

            let opts = {
                'save-key': file_name,
                'bucket': '{{ upyun.bucket }}',
                'expiration': Math.round(now.getTime() / 1000) + 3600,
                'date': date,
            };
            let policy = btoa(JSON.stringify(opts));
            let data = [ 'POST', '/{{ upyun.bucket }}', date, policy ].join('&');
            showSpin();
            $.post('{{ url('common/file/sign') }}', {data: data}, function(rs) {
                uploader.options.formData = {
                    authorization: 'UPYUN {{ upyun.user }}:' + rs.data,
                    policy: policy
                };
                uploader.upload();
            });
        });

        uploader.on('uploadSuccess', function(file, rs) {
            try {
                if (rs.message == 'ok') {
                    uploadSuccess({
                        status: 'ok',
                        file_name: file.name,
                        file_url: rs.url
                    });
                } else {
                    closeSpin();
                    toastr.error('上传失败！' + rs.message);
                }
            } catch (e) {
                console.log(e);
                closeSpin();
                toastr.error('数据解析失败！');
            }
        });

        uploader.on('uploadError', function(file) {
            closeSpin();
            toastr.error('操作失败！');
        });
    }

    function uploadPdf() {
        $("#btn_select_file").find("label").click();
    }
</script>