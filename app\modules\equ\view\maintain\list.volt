{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">保养计划管理</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">设备</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="code" v-model="code"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">计划日期</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control date dtpicker" name="date_begin" v-model="date_begin"/>
                                    <span class="input-group-addon no-border-lr">至</span>
                                    <input type="text" class="form-control date dtpicker" name="date_end" v-model="date_end"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">保养日期</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control date dtpicker" name="date_begin_b" v-model="date_begin_b"/>
                                    <span class="input-group-addon no-border-lr">至</span>
                                    <input type="text" class="form-control date dtpicker" name="date_end_b" v-model="date_end_b"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   data-mobile-responsive="true"
                   data-query-params="app.params"
                   data-pagination="true"
                   data-url="{{ url('equ/maintain/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server"
                   class="table table-striped table-condensed">
                <thead class="bg-blue">
                <tr>
                    <th data-field="code">设备</th>
                    <th data-field="plan_date">计划日期</th>
                    <th data-field="check_name">模板</th>
                    <th data-field="maintain_date">保养日期</th>
                    <th data-field="crete_user">保养人</th>
                    <th data-field="status_name">状态</th>
                    <th data-field="remarks">备注</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-pencil"></i> 编辑</a></li>
            <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-times"></i> 删除</a></li>
        </ul>
    </div>
</div>

<div id="act1" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a href="javascript:" onclick="view('@id@')"><i class="fa fa-eye"></i> 查看</a></li>
        </ul>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            code:'',
            date_begin:'',
            date_end:'',
            date_begin_b:'',
            date_end_b:'',
        },
        methods: {
            search: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params : function (p) {
                p.code = this.code;
                p.date_begin = this.date_begin;
                p.date_end = this.date_end;
                p.date_begin_b = this.date_begin_b;
                p.date_end_b = this.date_end_b;
                return p;
            },
            reset: function () {
                this.code = '';
                this.date_begin = '';
                this.date_end = '';
                this.date_begin_b = '';
                this.date_end_b = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            getParams() {
                let params = this.$data;
                let str = '';
                for (let col in params) {
                    str += col + '=' + params[col] + '&';
                }

                if (str) {
                    str = '?' + str.substr(0, str.length - 1);
                }
                return str;
            },
            excel() {
                window.open("{{ url('equ/maintain/exportlist') }}" + this.getParams());
            }
        }
    });

    var $table = $('#table');

    $table.bootstrapTable();
    var actHtml = $('#act').html();
    var actHtml1 = $('#act1').html();
    function actionFormatter(v, row, idx) {
        if (row.status == 10 && row.maintain_by == null){
            return actHtml.replace(/@id@/g, row.id);
        }else{
            return actHtml1.replace(/@id@/g, row.id);
        }

    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新建',
            type: 2,
            area: ['40em', '50em'],
            content: "{{ url('equ/maintain/create') }}",
            end: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        });
    }

    function edit(id) {
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['40em', '50em'],
            content: '{{ url('equ/maintain/edit/') }}' + id,
            end:function(){
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        });
    }

    function view(id) {
        top.layer.open({
            title:'保养数据',
            type: 2,
            area: ['50em', '60em'],
            content: '{{ url('equ/maintain/view/') }}' + id
        });
    }

    function del(id) {
        var dlg = layer.confirm('确认删除吗?', function () {
            layer.close(dlg);
            showSpin();
            $.post("{{ url('equ/maintain/delete') }}", { id: id }, function (rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作成功!');
                    $table.bootstrapTable('refreshOptions', {pageNumber: 1});
                }
                else {
                    toastr.error('操作失败!');
                }
            })
        });
    }

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>