{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jqzoom-master/css/jquery.jqzoom.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jqzoom-master/js/jquery.jqzoom-core.js') %}
{% do assets.collection('css').addCss('static/pages/css/work/work-view.css') %}


<div id="app" class="page-content">
    <div class="tabbable-line boxless tabbable-reversed" style="border-bottom: 1px #36C6D3 SOLID;">
        <ul class="nav nav-tabs">
            <li :class="row.uid == data.data.uid ? 'active' : ''" v-for="row,idx in tabs">
                <a style="font-size: 18px;min-width: 100px;text-align: center" @click="tabClick(idx)" data-toggle="tab" :aria-expanded="row.uid == data.data.uid ? true : false" v-text="row.name"></a>
            </li>
        </ul>
    </div>
    <div style="margin-top:20px">
        <div class="row">
            <div class="col-md-4">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">业务信息</span>
                        </div>
                    </div>
                    <div class="portlet-body" style="height: 773px;overflow: auto;">
                        <div class="data-row">
                            <div class="data-col">
                                <div class="data-title">业务状态：</div>
                                <div class="data-content">
                                    <span v-text="data.data.anchor"></span>
                                    <span v-if="data.data.handle_status == 1" class="label label-success" style="margin-left: 5px;">通过</span>
                                    <span v-if="data.data.handle_status == 2" class="label label-danger" style="margin-left: 5px;">拒绝</span>
                                    <span v-if="data.data.handle_status == 3" class="label label-danger" style="margin-left: 5px;">撤销</span>
                                </div>
                            </div>
                            <div class="data-col">
                                <div class="data-title">提交部门：</div>
                                <div class="data-content" v-text="data.data.group"></div>
                            </div>
                            <div class="data-col">
                                <div class="data-title">业务单号：</div>
                                <div class="data-content" v-text="data.data.code"></div>
                            </div>
                            <div class="data-col">
                                <div class="data-title">业务名称：</div>
                                <div class="data-content">
                                    <span v-text="data.data.type_name"></span>
                                    <span v-if="data.data.review_type == 1" class="label label-danger">(撤销审批)</span>
                                </div>
                            </div>
                        </div>
                        <div class="data-row" v-for="(item,i) in data.data.form_list">
                            <div v-if="item.type == 99" style="width: 100%;padding-right: 20px">
                                <div class="data-col">
                                    <div class="data-title"><i class="fa fa-star"></i>${ item.name }</div>
                                    <div class="data-content"></div>
                                </div>
                                <div v-for="(sum,j) in item.sum_list">
                                    <div class="data-col">
                                        <div class="data-title">${ sum.name }</div>
                                        <div class="data-content">${ sum.value } ${ sum.unit }</div>
                                    </div>
                                </div>
                                <div class="inner-row" v-for="(d,k) in item.data_list">
                                    <div v-for="(v,l) in d">
                                        <div class="data-col">
                                            <div class="data-title">${ v.name }</div>
                                            <div class="data-content">${ v.value } ${ v.unit }</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div class="data-col">
                                    <div class="data-title">${ item.name }</div>
                                    <div class="data-content">${ item.value } ${ item.unit }</div>
                                </div>
                            </div>
                        </div>
                        <div class="data-row">
                            <div class="data-col">
                                <div class="data-title">说明：</div>
                                <div class="data-content"  v-text="data.data.remarks"></div>
                            </div>
                        </div>
                        <div class="data-row" v-if="data.data.files != null && data.data.files.length != 0">
                            <div style="color: #000;font-weight: 600;width: 100%;margin-bottom: 15px">相关文件：</div>
                            <div style="display: flex;flex-direction: row;flex-wrap: wrap;margin-left: 8%">
                                <div id="uploader" class="uploader-one"v-for="file,index in data.data.files" style="margin:5px 15px">
                                    <div class="img-body">
                                        <div class="file-item thumbnail">
                                            <a :href="img_path + file" data-lightbox="datafiles" title="查看图片">
                                                <img :src="img_path + file + '!small'" style="height: 100%;width: 100%">
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="data-row" v-if="sub_list.length > 0">
                            <div class="data-col">子流程：</div>
                            <div class="data-col search-table">
                                <table class="table table-striped table-condensed">
                                    <thead class="bg-blue">
                                    <tr>
                                        <th>业务类型</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="row in sub_list">
                                        <td v-text="row.name"></td>
                                        <td>
                                            <button type="button" class="btn blue" @click="openDetail(row.uid)">详情</button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4" style="padding-left: 0;">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-blue"></i>
                            <span class="caption-subject font-blue bold uppercase">流程信息</span>
                        </div>
                    </div>
                    <div class="portlet-body" style="height: 773px;overflow: auto;">
                        <div class="work-line" >
                            <div class="work-line-content">
                                <div v-for="(item,index) in data.flow_list" class="item">
                                    <div class="title-border">
                                        <div v-if="item.type == 4" class="title" style="background-color: #3296FB">
                                            <i class="fa fa-home" style="margin-top: 6px"></i>
                                        </div>
                                        <div v-if="item.type == 5" class="title" style="background-color: #FF2B2B">
                                            <i class="fa fa-stop" style="margin-top: 6px"></i>
                                        </div>
                                        <div v-if="item.type == 6" class="title" style="background-color: #FF2B2B">
                                            <i class="fa fa-reply" style="margin-top: 6px"></i>
                                        </div>
                                        <div style="display: inline-block" v-if="item.type!=4 && item.type!=5 && item.type!=6" class="title" style="background-color: #3296FB">
                                            ${item.icon}
                                            <div v-if="item.type == 1" class="border" style="color:#4AB37E">
                                                <i class="fa fa-check"></i>
                                            </div>
                                            <div v-if="item.type == 3" class="border" style="color:#4AB37E">
                                                <i class="fa fa-volume-up"></i>
                                            </div>
                                            <div v-if="item.type != 1 && item.type != 3" class="border" style="color: #3296FB">
                                                <i class="fa fa-thumbs-o-up"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item-content">
                                        <div class="top">
                                            <div v-if="item.status == 0" style="color: #A2A2A2;">
                                                <span>${item.name}</span>
                                                <span>${item.val}</span>
                                            </div>
                                            <div v-if="item.status != 0" style="color: #000000;">
                                                <span>${item.name}</span>
                                                <span>${item.val}</span>
                                            </div>
                                            <span style="font-size: 15px;color: #898989;margin-top: -3px">${item.time}</span>
                                        </div>
                                        <div class="bottom" :style="{borderLeft:data.flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'}">
                                            <div v-if="item.text != ''" class="bottom-content">
                                                ${item.text}
                                            </div>
                                            <div style="display: flex;flex-direction: row;flex-wrap: wrap;margin-left: 35px;margin-top: 5px">
                                                <div v-for="(file,i) in item.files" style="width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px">
                                                    <a :href="img_path + file" data-lightbox="flowitem" title="查看图片">
                                                        <img :src="img_path+file" width="80px" height="80px" class="img-view"/>
                                                    </a>
                                                </div>
                                            </div>
                                            <div v-if="item.send != ''" class="bottom-send">
                                                ${item.send}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="work-line-content">
                                <div v-for="(item,idx) in data.anchor_list">
                                    <div style="display: flex;flex-direction: row;min-height: 60px;">
                                        <div style="width: 35%;display: flex;flex-direction: row;">
                                            <div style="height: 100%;position: relative">
                                                <div style="height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;">
                                                </div>
                                                <div v-if="idx == 0" style="height: 18px;position: absolute;top: 0;width: 12px;background-color: #FFF">
                                                </div>
                                                <div v-if="idx+1 == data.anchor_list.length" style="height: calc(100% - 22px);position: absolute;top: 22px;width: 12px;background-color: #FFF">
                                                </div>
                                                <div style="position: absolute;top: 15px;width: 15px;height: 15px;background-color: #aaaaaa;border-radius: 15px !important;">
                                                </div>
                                            </div>
                                            <div style="padding-top:5px;padding-left: 15px">
                                                <span style="font-weight: 400" v-text="item.name"></span>
                                                <div>
                                                    <span style="font-size: 12px;color: #898989" v-if="item.type==1">会签</span>
                                                    <span style="font-size: 12px;color: #898989" v-if="item.type==2">或签</span>
                                                    <span style="font-size: 12px;color: #898989" v-if="item.type==3">终签</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="user-row">
                                            <div v-for="(user,i) in item.list">
                                                <div v-if="item.type == 1" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #3598dc;color: #ffffff">${user.name}</div>
                                                <div v-if="item.type != 1" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #00adbc;color: #ffffff">${user.name}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="item.nlist.length > 0" style="display: flex;flex-direction: row;min-height: 60px;">
                                        <div style="width: 35%;display: flex;flex-direction: row;">
                                            <div style="height: 100%;position: relative">
                                                <div v-if="idx+1 != data.anchor_list.length" style="height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;">
                                                </div>
                                                <div v-if="idx+1 == data.anchor_list.length" style="height: 100%;width: 8px;">
                                                </div>
                                            </div>
                                            <div style="padding-top:5px;padding-left: 15px">
                                                <span style="font-weight: 400;color: #898989">抄送人</span>
                                            </div>
                                        </div>
                                        <div class="user-row">
                                            <div v-for="(user,i) in item.nlist" style="height: 20px;margin: 2px;padding: 0 2px;background-color: #3598dc;color: #ffffff">${user.name}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{ partial('uploader_preview') }}

<script>
    initImgWidth();
    var app = new Vue({
        data: {
            tabs: {{ jsonTabs }},
            data: {{ jsonData }},
            img_path: '{{ base_path }}',
            sub_list: {{ jsonSubList }}
        },
        el:'#app',
        mounted: function() {
            this.$nextTick(function() {
                vueMounted();
            });
        },
        methods:{
            view: function (uid) {
                top.layer.open({
                    title: '查看',
                    type: 2,
                    area: ['100%', '100%'],
                    content: "{{ url('work/weight/view/') }}" + uid,
                });
            },
            tabClick:function (idx) {
                this.doc_type = this.tabs[idx].uid;
                if (this.tabs[idx].uid == this.data.data.uid){
                    return;
                }
                var url = '{{ url('work/data/view/') }}' + this.tabs[idx].uid;
                showSpin();
                $.post(url, {}, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        app.data = rs.data;
                        app.sub_list = rs.sub_list;
                        app.$forceUpdate();
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            openDetail: function(uid) {
                window.open("{{ url('work/data/view/') }}" + uid);
            }
        }
    });

    function vueMounted() {
        $('.jqzoom').jqzoom({
            zoomType: 'standard',
            lens: true,
            preloadImages: false,
            alwaysOn: false,
            position: 'left',
            title: false,
            zoomWidth: 200,
            zoomHeight: 200
        });
    }

    $(function() {
        initSize();
    });

    function initImgWidth() {
        $(".img-panel img").css({
            'max-width': $(".img-panel").width() * 0.8
        });
    }

    function initSize() {
        $(".weighing-pic-box").height($(".portlet-ticket .ticket-box .review-inner").height());

        var h = $(window).height() - 35;
        $(".form-panel").height(h);
        $(".img-panel").height(h);
        $(".img-panel img").css({
            'max-height': h * 0.8
        });
    }
</script>
<style>
    .inv-div{
        margin-top: 5px;
        min-height: 350px;
        overflow: auto
    }
    .detail_table tr td:nth-child(1){
        width: 15%;
        text-align: right;
    }
    .detail_table tr td:nth-child(2){
        text-align: left;
    }
    .detail_table tr td:nth-child(3){
        width: 15%;
        text-align: right;
    }
    .detail_table tr td:nth-child(4){
        text-align: left;
    }
    .work-line{
        width: 100%;
        display: flex;
        flex-direction: column;
        min-height: 50px;
    }
    .work-line-content{
        width: 100%;
        min-height: 50px;
        display: flex;
        flex-direction: column;
    }
    .work-line-content .item-content{
        width: auto;
        margin-left: 15px;
        color: #888888;
    }

    .work-line-content .item-content .top {
        height: 20px;
        line-height: 20px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 18px;
        margin-top: 2px;
        vertical-align: top;
    }

    .work-line-content .item-content .bottom {
        margin-left: -38px;
        border-left: 4px #D2D2D2 solid;
        padding-bottom: 10px;
        min-height: 55px;
    }

    .work-line-content .bottom-content{
        margin-left: 35px;
        margin-top:5px;
        min-height: 40px;
        background-color: #F2F2F2;
        border-radius: 5px !important;
        padding: 10px;
        color: #000000;
    }

    .work-line-content .bottom-send{
        margin-left: 35px;
        margin-top:5px;
        font-size: 14px;
    }

    .item{
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        margin-top: 4px;
    }
    .item .title{
        width: 45px;
        height: 45px;
        line-height: 45px;
        vertical-align: middle;
        background-color: #3296FB;
        border: 1px #E2E2E2 solid;
        border-radius: 5px !important;
        color: #FFFFFF;
        text-align: center;
        position: relative;
        font-size: 16px;
    }
    .item .title .border{
        position: absolute;
        top: 31px;
        left: 31px;
        width: 18px;
        height: 18px;
        line-height: 18px;
        border-radius: 9px !important;
        background-color: #FFFFFF;
        border: 0;
        text-align: center;
        padding-top: 1px;
    }

    .item .item-content{
        width: 100%;
        margin-left: 15px;
        color: #888888;
    }

    .item .item-content .top {
        height: 20px;
        line-height: 20px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 18px;
        margin-top: 2px;
        vertical-align: top;
    }

    .item .item-content .bottom {
        margin-left: -38px;
        border-left: 4px #D2D2D2 solid;
        padding-bottom: 10px;
        min-height: 55px;
    }

    .item .bottom-content{
        margin-left: 35px;
        margin-top:5px;
        min-height: 40px;
        background-color: #F2F2F2;
        border-radius: 5px !important;
        padding: 10px;
        color: #000000;
    }

    .item .bottom-send{
        margin-left: 35px;
        margin-top:5px;
        font-size: 14px;
    }

    .item .title-border{
        background-color: #FFFFFF;
        width: 45px;
        height: 52px;
        z-index: 999;
    }

    .data-row {
        display: flex;
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
    }

    .inner-row {
        padding: 10px;
        margin-bottom: 20px;
        width: 100%;
        background-color: #f5f5f5;
        border-radius: 10px !important;
    }

    .button-row{
        padding: 6px 0;
        display: flex;
        flex-direction: row;
        justify-content: left;
        border-bottom: 1px solid #eee;
    }

    .data-row .data-col {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 20px;
        padding: 0 15px;
        width: 100%;
    }

    .data-col.time-line {
        width: 100%;
    }

    .data-col.pic {
        height: 95px;
        overflow: hidden;
    }

    .data-row.last {
        margin-bottom: 0;
    }

    .data-title {
        min-width: 80px;
    }

    .data-content {
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .weight-col {
        overflow-y: auto;
        height: 860px;
    }

    .weight-col>div:last-child {
        margin-bottom: 0;
    }

    .user-row {
        width: 65%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-end;
        padding-right: 10px;
        padding-top: 5px;
    }
</style>
