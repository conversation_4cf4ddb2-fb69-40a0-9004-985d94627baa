{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('css').addCss('static/global/plugins/jqzoom-master/css/jquery.jqzoom.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jqzoom-master/js/jquery.jqzoom-core.js') %}
{% do assets.collection('css').addCss('static/pages/css/review.css') %}

<div id="app" class="page-content">
    <div class="row">
        <div class="col-md-7">
            <div class="form-panel">
                <div class="portlet light" style="margin-bottom: 0">
                    <div class="portlet-body form">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-badge">
                                    <div class="timeline-icon">
                                        <i class="icon-note font-green-haze"></i>
                                    </div>
                                </div>
                                <div class="timeline-body">
                                    <div class="timeline-body-arrow"> </div>
                                    <div class="timeline-body-head">
                                        <div class="timeline-body-head-caption">
                                            <span class="timeline-body-alerttitle font-blue-madison">故障提交信息</span>
                                        </div>
                                    </div>
                                    <div class="timeline-body-content">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>故障单号</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="fault_no" v-model="fault_row.fault_no" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>设备编号</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="equ_code" v-model="fault_row.equ_code" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>影响级别</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="fault_level_name" v-model="fault_row.fault_level_name" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>故障发生时间</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="begin_dt" v-model="fault_row.begin_dt" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="form-group">
                                                    <label>故障现象</label>
                                                    <div>
                                                        <textarea class="form-control" name="begin_describe" v-model="fault_row.begin_describe" style="resize: none" readonly></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="form-group">
                                                    <label>故障照片</label>
                                                    <div class="portlet-goods-img">
                                                        <div class="img-box">
                                                            <div v-for="img, index in fault_row.begin_files" class="img-item">
                                                                <div id="uploader" class="uploader-one">
                                                                    <div class="img-body">
                                                                        <div class="file-item thumbnail">
                                                                            <img :src="img + '!small'">
                                                                        </div>
                                                                        <div class="uploader-wrap">
                                                                            <div class="uploader-btn-row">
                                                                                <a class="btn btn-default btn-circle" href="javascript:void(0);" :data-url="img" data-title="故障照片" title="查看图片" onclick="previewImg(this)">
                                                                                    <i class="fa fa-search"></i>
                                                                                </a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="fault_row.repair_dt" class="timeline-item">
                                <div class="timeline-badge">
                                    <div class="timeline-icon">
                                        <i class="icon-wrench font-green-haze"></i>
                                    </div>
                                </div>
                                <div class="timeline-body">
                                    <div class="timeline-body-arrow"> </div>
                                    <div class="timeline-body-head">
                                        <div class="timeline-body-head-caption">
                                            <span class="timeline-body-alerttitle font-blue-madison">外协修理信息</span>
                                        </div>
                                    </div>
                                    <div class="timeline-body-content">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>责任人</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="repair_user_name" v-model="fault_row.repair_user_name" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>计划解除时间</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="repair_dt" v-model="fault_row.repair_dt" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>外协供应商</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="repair_company" v-model="fault_row.repair_company" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="form-group">
                                                    <label>解除计划</label>
                                                    <div>
                                                        <textarea class="form-control" name="repair_describe" v-model="fault_row.repair_describe" style="resize: none" readonly></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="fault_row.repair_money" class="timeline-item">
                                <div class="timeline-badge">
                                    <div class="timeline-icon">
                                        <i class="icon-wallet font-green-haze"></i>
                                    </div>
                                </div>
                                <div class="timeline-body">
                                    <div class="timeline-body-arrow"> </div>
                                    <div class="timeline-body-head">
                                        <div class="timeline-body-head-caption">
                                            <span class="timeline-body-alerttitle font-blue-madison">外协修理费用信息</span>
                                        </div>
                                    </div>
                                    <div class="timeline-body-content">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>修理费用金额</label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="repair_money" v-model="fault_row.repair_money" readonly>
                                                        <span class="input-group-addon">元</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="form-group">
                                                    <label>修理费用描述</label>
                                                    <div>
                                                        <textarea class="form-control" name="repair_money_describe" v-model="fault_row.repair_money_describe" style="resize: none" readonly></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="fault_row.status == 40" class="timeline-item">
                                <div class="timeline-badge">
                                    <div class="timeline-icon">
                                        <i class="icon-check font-green-haze"></i>
                                    </div>
                                </div>
                                <div class="timeline-body">
                                    <div class="timeline-body-arrow"> </div>
                                    <div class="timeline-body-head">
                                        <div class="timeline-body-head-caption">
                                            <span class="timeline-body-alerttitle font-blue-madison">故障解除信息</span>
                                        </div>
                                    </div>
                                    <div class="timeline-body-content">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <label>故障解除时间</label>
                                                    <div>
                                                        <input type="text" class="form-control" name="end_dt" v-model="fault_row.end_dt" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="form-group">
                                                    <label>原因及解除对策</label>
                                                    <div>
                                                        <textarea class="form-control" name="end_describe" v-model="fault_row.end_describe" style="resize: none" readonly></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="form-group">
                                                    <label>解除故障照片</label>
                                                    <div class="portlet-goods-img">
                                                        <div class="img-box">
                                                            <div v-for="img, index in fault_row.end_files" class="img-item">
                                                                <div id="uploader" class="uploader-one">
                                                                    <div class="img-body">
                                                                        <div class="file-item thumbnail">
                                                                            <img :src="img + '!small'">
                                                                        </div>
                                                                        <div class="uploader-wrap">
                                                                            <div class="uploader-btn-row">
                                                                                <a class="btn btn-default btn-circle" href="javascript:void(0);" :data-url="img" data-title="故障照片" title="查看图片" onclick="previewImg(this)">
                                                                                    <i class="fa fa-search"></i>
                                                                                </a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-5">
            <div class="img-panel">
                <span v-if="preview_img == ''">图片预览区</span>
                <div class="img-box" :class="preview_img == '' ? 'img-hide' : 'img-show'">
                    <a :href="preview_img" class="jqzoom" rel='preview_img'>
                        <img :src="preview_img">
                    </a>
                </div>
                <a id="a_zoom" style="display: none;" href='javascript:void(0);' :rel="zoom_rel"><img :src="preview_img"></a>
            </div>
        </div>
    </div>
</div>

{{ partial('uploader_preview') }}
<script>
    initImgWidth();

    var app = new Vue({
        el:'#app',
        data: {
            fault_row: {{ data }},
            img_path: '{{ base_path }}',
            zoom_rel: "{gallery: 'preview_img', smallimage: '', largeimage: ''}",
            preview_img: ''
        },
        mounted: function() {
            this.$nextTick(function() {
                vueMounted();
            });
        }
    });

    function previewImg(obj) {
        app.preview_img = $(obj).attr("data-url");
        app.zoom_rel = "{gallery: 'preview_img', smallimage: '" + app.preview_img + "', largeimage: '" + app.preview_img + "'}";
        app.$nextTick(function() {
            $("#a_zoom").click();
        });
    }

    function vueMounted() {
        $('.jqzoom').jqzoom({
            zoomType: 'standard',
            lens: true,
            preloadImages: false,
            alwaysOn: false,
            position: 'left',
            title: false,
            zoomWidth: 200,
            zoomHeight: 200
        });
    }

    $(function() {
        initSize();
    });

    function initImgWidth() {
        $(".img-panel img").css({
            'max-width': $(".img-panel").width() * 0.8
        });
    }

    function initSize() {
        $(".weighing-pic-box").height($(".portlet-ticket .ticket-box .review-inner").height());

        var h = $(window).height() - 35;
        $(".form-panel").height(h);
        $(".img-panel").height(h);
        $(".img-panel img").css({
            'max-height': h * 0.8
        });
    }
</script>