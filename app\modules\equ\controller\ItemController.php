<?php
namespace Envsan\Modules\Equ\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Equ\Model\EquItem;
use Envsan\Modules\Equ\Service\EquItemService;
use Envsan\Modules\Equ\Service\EquItemTypeService;
use Envsan\Modules\Equ\Util\Constant;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Service\ProductService;


/**
 * @name('设备')
 */
class ItemController extends SuperController
{
    private $page_id = 46;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new EquItemService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new EquItemService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $ts = new EquItemTypeService();

        $jrow = (new EquItem())->toArray();
        $jrow['wages_ratio'] = 1;
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['ship_list1'] = [];
        $jrow['ship_list2'] = [];
        $jrow['ship_list3'] = [];
        $this->view->jsonEquItem = json_encode($jrow);
        $this->view->status_list = Constant::$equ_status_arr;
        $this->view->type_list = $ts->getList();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $ps = new ProductService();
        $this->view->ship_list = $ps->getShipTypes();
    }

    /**
     * @acl({'link':'equ:item:create'})
     */
    public function editAction($uid = '')
    {
        $s = new EquItemService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $ts = new EquItemTypeService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $ships = $s->getEquShips($row->id);
        $jrow['ship_list1'] = [];
        $jrow['ship_list2'] = [];
        $jrow['ship_list3'] = [];
        foreach ($ships as $ship){
            if ($ship->ship_level == 1){
                $jrow['ship_list1'][] = $ship->ship_type_id;
            } else if ($ship->ship_level == 2){
                $jrow['ship_list2'][] = $ship->ship_type_id;
            } else if ($ship->ship_level == 3){
                $jrow['ship_list3'][] = $ship->ship_type_id;
            }
        }
        $this->view->jsonEquItem = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->status_list = Constant::$equ_status_arr;
        $this->view->type_list = $ts->getList();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $ps = new ProductService();
        $this->view->ship_list = $ps->getShipTypes();
        $this->view->pick('item/create');
    }

    /**
     * @acl({'link':'equ:item:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new EquItemService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new EquItemService();
        $row = $s->getViewData($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $fs = new FileService();

        $jrow = $row->toArray();
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonEquItem = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new EquItemService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }



    /**
     * @name('状态一览')
     */
    public function panelAction()
    {
        $s = new EquItemService();
        $this->view->equ_list = json_encode($s->getEquStatusList());
        $this->view->fault_history = json_encode($s->getEquFaultList());
    }
}