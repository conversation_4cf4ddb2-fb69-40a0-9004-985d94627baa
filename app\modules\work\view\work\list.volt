{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                    <a class="dashboard-stat dashboard-stat-v2 blue" :class="type == 1 ? 'blue' : 'grey'" href="#" @click="search(1)">
                        <div class="visual">
                            <i class="fa fa-list" :style="{color: type == 1 ? '#FFFFFF' : '#0C8BD8'}"></i>
                        </div>
                        <div class="details">
                            <div class="number">
                                <span style="font-size: 50px;font-weight:bold" data-counter="counterup" :data-value="cnt_data._1" v-text="cnt_data._1"></span>
                            </div>
                            <div v-if="type == 1" class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#FFF">待审批</div>
                            <div v-else class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#666">待审批</div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                    <a class="dashboard-stat dashboard-stat-v2 blue" :class="type == 2 ? 'blue' : 'grey'" href="#" @click="search(2)">
                        <div class="visual">
                            <i class="fa fa-check" :style="{color: type == 2 ? '#FFFFFF' : '#0C8BD8'}"></i>
                        </div>
                        <div class="details">
                            <div class="number">
                                <span style="font-size: 50px;font-weight:bold" data-counter="counterup" :data-value="cnt_data._2" v-text="cnt_data._2"></span>
                            </div>
                            <div v-if="type == 2" class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#FFF">已审批</div>
                            <div v-else class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#666">已审批</div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                    <a class="dashboard-stat dashboard-stat-v2 blue" :class="type == 3 ? 'blue' : 'grey'" href="#" @click="search(3)">
                        <div class="visual">
                            <i class="fa fa-star" :style="{color: type == 3 ? '#FFFFFF' : '#0C8BD8'}"></i>
                        </div>
                        <div class="details">
                            <div class="number">
                                <span style="font-size: 50px;font-weight:bold" data-counter="counterup" :data-value="cnt_data._3" v-text="cnt_data._3"></span>
                            </div>
                            <div v-if="type == 3" class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#FFF">已发起</div>
                            <div v-else class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#666">已发起</div>
                        </div>
                    </a>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                    <a class="dashboard-stat dashboard-stat-v2 blue" :class="type == 4 ? 'blue' : 'grey'" href="#" @click="search(4)">
                        <div class="visual">
                            <i class="fa fa-bullhorn"  :style="{color: type == 4 ? '#FFFFFF' : '#0C8BD8'}"></i>
                        </div>
                        <div class="details">
                            <div class="number">
                                <span style="font-size: 50px;font-weight:bold" data-counter="counterup" :data-value="cnt_data._4" v-text="cnt_data._4"></span>
                            </div>
                            <div v-if="type == 4" class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#FFF">抄送于我</div>
                            <div v-else class="desc" style="margin-top: 10px;font-size: 20px;font-weight:bold;color:#666">抄送于我</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <div class="search-table" style="height: 70vh;overflow: auto">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('work/work/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="status" data-formatter="statusFormatter">状态</th>
                    <th data-field="type_name">审批类型</th>
                    <th data-field="group_name">提交部门</th>
                    <th data-field="create_name">提交人</th>
                    <th data-field="create_date">提交时间</th>
                    <th data-field="abstrakt_data" data-formatter="abstraktFormatter">审批摘要</th>
                    <th data-field="remarks">备注</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="list-table">
        <button type="button" onclick="view('@uid@')" class="btn blue">详情</button>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            cnt_data:{_1:0,_2:0,_3:0,_4:0},
            type: '{{ type }}'
        },
        methods: {
            search: function(type) {
                this.type = type;
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.type = this.type;
                return p;
            },
            reset: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable({
        onLoadSuccess: function (data) {
            app.cnt_data = data.cnt_data;
        }
    });

    function actionFormatter(v, row, idx) {
        return $('#act').html().replace(/@uid@/g, row.uid);
    }

    function statusFormatter(v, row) {
        let str = '';
        if (row.status == 15){
            str += '<span class="label label-info">审批中</span>&nbsp;';
        }
        if (row.handle_status == 1){
            str += '<span class="label label-success">通过</span>&nbsp;';
        }
        if (row.handle_status == 2){
            str += '<span class="label label-danger">拒绝</span>&nbsp;';
        }
        if (row.handle_status == 3){
            str += '<span class="label label-danger">撤回</span>&nbsp;';
        }
        if (row.pressing_flag > 0){
            str += '<span class="label label-danger">催办(+'+row.pressing_flag+')</span>&nbsp;';
        }
        if (row.review_type == 1){
            str += '<span class="label label-danger">撤销审批</span>&nbsp;';
        }
        if (row.read_flag == 0){
            str += '<span class="label label-danger">未读</span>&nbsp;';
        }
        return str;
    }

    function abstraktFormatter(list){
        if (list == null){
            return '-';
        }
        let str = '';
        for(let item of list){
            str += '<div style="display: flex;padding: 3px" ><div style="min-width: 100px;"><span>'+item.name+'</span>:</div><div><span>'+item.value+'</span></div></div>'
        }
        return  str;
    }

    function view(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '查看详情',
            type: 2,
            resize: false,
            area: ['100%', '100%'],
            content: '{{ url('work/work/view/') }}' + uid + '/1',
            end: function() {
                $table.bootstrapTable('refresh');
            }
        });
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

    let bsOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsOption);
    $('.bs-select-read').selectpicker(bsOption);
</script>