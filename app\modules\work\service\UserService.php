<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\GroupService;
use Phalcon\Mvc\User\Component;

class UserService extends Component
{
    public function getUser()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.uid,
                a.login_name,
                a.real_name,
                a.mobile,
                a.sign,
                g.name as group_name,
                r.name as role_name
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User', 'a')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'a.group_id = g.id', 'g')
            ->leftJoin('Envsan\Modules\Sys\Model\Role', 'a.role_id = r.id', 'r')
            ->where('a.id = '.SessionData::user()->id);
        $rows = $builder->getQuery()->execute();
        if (count($rows) > 0) {
            $row = $rows[0]->toArray();
            return $row;
        } else {
            return null;
        }
    }

    public function signSave(){
        $rtn = new \stdClass();
        $data = $this->request->getPost('data', ['string', 'trim']);
        if (empty($data)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $user = User::findFirst([
            ' del_flag = 0 and id = ?1',
            'bind' => [1 => SessionData::user()->id]
        ]);
        if (empty($user)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $now = DateUtil::now();
        $user->sign = $data;
        $user->update_date = $now;
        $user->update_by = $user->id;
        if (!$user->save()) {
            $rtn->message = ErrorHelper::UNKOWN;
            return $rtn;
        }
        $rtn->message = ErrorHelper::NOERROR;
        return $rtn;
    }
}