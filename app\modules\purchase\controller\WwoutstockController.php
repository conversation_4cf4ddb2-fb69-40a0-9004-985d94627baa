<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseWwoutstock;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\OrderService;
use Envsan\Modules\Purchase\Service\PurchaseWwoutstockService;

/**
 * @name('外委出库')
 */
class WwoutstockController extends SuperController
{
    private $page_id = 20;
    private $search_page_id = 21;

    /**
     * @name('管理')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseWwoutstockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseWwoutstockService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new PurchaseWwoutstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }

        $common = new CommonService();
        $table = new TableService();
        $fs = new FileService();

        $jrow = (new PurchaseWwoutstock())->toArray();
        $jrow['outstock_date'] = DateUtil::today();
        $jrow['order_id'] = '';
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['detail_data'] = [];
        $jrow['files'] = [];
        $jrow['bom_list'] = [];
        $jrow['notice_code'] = '';
        $jrow['bom_msg'] = '没有找到匹配的记录';
        $this->view->jsonOutstock = json_encode($jrow);

        $this->view->orderList = $common->getWworderList();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'purchase:wwoutstock:create'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseWwoutstockService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $common = new CommonService();
        $fs = new FileService();
        $table = new TableService();
        $ext_data = CvtUtil::emptyToArray($row->ext_data);

        $order_code = '';
        $supplier_name = '';
        $order_row = $s->getOrderData($row->order_id);
        if (!empty($order_row)) {
            $order_code = $order_row->order_code;
            $supplier_name = $order_row->supplier_name;
        }

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($row->files);
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['detail_data'] = CvtUtil::emptyToArray($row->detail_data);
        $jrow['order_code'] = $order_code;
        $jrow['supplier_name'] = $supplier_name;
        $jrow['bom_list'] = $s->getBomList($row->order_id);
        $jrow['notice_code'] = '';
        $jrow['bom_msg'] = '';
        $this->view->jsonOutstock = json_encode($jrow);

        $this->view->orderList = $common->getWworderList();
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('wwoutstock/create');
    }

    /**
     * @acl({'link':'purchase:wwoutstock:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwoutstockService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }


    /**
     * @skipacl
     */
    public function orderchangeAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwoutstockService();
            $ret = new JsonData();
            $ret->handleResult($s->getBomList($this->request->getPost('order_id', 'tstring')));
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:wwoutstock:create'})
     */
    public function cancelAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwoutstockService();
            $ret = new JsonData();
            $ret->handleResult($s->cancel($this->request->getPost('uid', 'tstring')));
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid, $type = 'view')
    {
        $s = new PurchaseWwoutstockService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $fs = new FileService();
        $table = new TableService();
        $ext_data = CvtUtil::emptyToArray($row->ext_data);

        $order_code = '';
        $supplier_name = '';
        $order_row = $s->getOrderData($row->order_id);
        if (!empty($order_row)) {
            $order_code = $order_row->order_code;
            $supplier_name = $order_row->supplier_name;
        }

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($row->files);
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['detail_data'] = CvtUtil::emptyToArray($row->detail_data);
        $jrow['order_code'] = $order_code;
        $jrow['supplier_name'] = $supplier_name;
        $this->view->jsonOutstock = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->view_type = $type;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseWwoutstockService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function searchexportAction()
    {
        $s = new PurchaseWwoutstockService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id, $builder);
    }


    /**
     * @name('审核')
     */
    public function approvalAction() {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwoutstockService();
            $ret = new JsonData();
            $ret->handleResult($s->approval());
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:receipt:approval'})
     */
    public function rejectAction() {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwoutstockService();
            $ret = new JsonData();
            $ret->handleResult($s->reject());
            return json_encode($ret);
        }
    }

}