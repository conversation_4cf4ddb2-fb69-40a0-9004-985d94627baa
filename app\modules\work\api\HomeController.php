<?php
namespace Envsan\Modules\Work\Api;

use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Work\Service\HomeService;

class HomeController extends SuperController
{
    public function indexAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new HomeService();
            return json_encode($s->init(), JSON_UNESCAPED_UNICODE);
        }
    }

    public function reviewAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $s = new HomeService();
            $ret->message = '';
            $ret->list = $s->getReviewList();
            $user = SessionData::user();
            $ret->user = ['real_name' => $user->real_name, 'mobile' => $user->mobile];
            $ret->emptyIsOk();
            return json_encode($ret,JSON_UNESCAPED_UNICODE);
        }
    }

    public function groupAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $s = new HomeService();
            $ret->message = $s->changeGroup();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}