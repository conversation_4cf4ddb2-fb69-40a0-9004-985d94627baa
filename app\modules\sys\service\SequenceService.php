<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Sys\Model\Sequence;
use Envsan\Modules\Sys\Model\SequenceDetail;
use Phalcon\Mvc\User\Component;

class SequenceService extends Component
{
    public function useSequence($type = '', $owner = '')
    {
        if (CheckUtil::is_empty($type)) {
            throw new \Exception("类型不能为空");
        }

        if (CheckUtil::is_empty($owner)) {
            $user = SessionData::user();
            if ($user != null) {
                $owner = $user->owner;
            }
        }
        if (CheckUtil::is_empty($owner)) {
            throw new \Exception("owner不能为空");
        }

        $arr = $this->selectSequenceRow($type, $owner);
        if ($arr == false) {
            throw new \Exception("编号序列查询失败");
        }

        $row_seq = $this->updateSequence($arr);
        if ($row_seq == false) {
            throw new \Exception("编号序列更新失败");
        }

        return $this->getCode($row_seq);
    }

    public function selectSequenceRow($type = '', $owner = '')
    {
        if (empty($owner)) {
            $owner = 1;
        }

        $seq = Sequence::findFirst(['type = ?1', 'bind' => [1 => $type]]);
        if ($seq == false) {
            return false;
        }

        $row = [];
        $row['id'] = '';
        $row['type'] = $seq->type;
        $row['header'] = $seq->header;
        $row['body'] = $seq->body;
        $row['seq_date'] = date($seq->seq_fmt);
        $row['num_length'] = $seq->num_length;
        $row['no_count'] = 0;
        $row['owner'] = $owner;

        $sql = 'SELECT *';
        $sql .= ' FROM Envsan\Modules\Sys\Model\SequenceDetail';
        $sql .= ' WHERE type = \''.$type.'\' AND owner = \''.$owner.'\'';
        $sql .= ' FOR UPDATE';
        $rows = $this->modelsManager->executeQuery($sql);
        if ($rows != false && count($rows) > 0) {
            $detail = $rows[0];
            $row['id'] = $detail->id;
            $row['no_count'] = $detail->no_count;

            if ($row['seq_date'] != $detail->seq_date) {
                $row['no_count'] = 0;
            }
        }
        return json_decode(json_encode($row));
    }

    public function updateSequence($arr)
    {
        $arr->no_count = intval($arr->no_count) + 1;

        $row = new SequenceDetail();
        $row->id = $arr->id;
        $row->type = $arr->type;
        $row->seq_date = $arr->seq_date;
        $row->no_count = $arr->no_count;
        $row->owner = $arr->owner;
        if ($row->save()) {
            return $arr;
        } else {
            return false;
        }
    }

    public function getCode($row)
    {
        $header = CvtUtil::nullToBlank($row->header);
        $body = empty($row->body) ? '' : date($row->body);
        $footer = substr(str_repeat('0', $row->num_length).$row->no_count, -$row->num_length);
        return $header.$body.$footer;
    }
}