{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="row" style="padding-left: 15px">
        <div class="col-sm-3" style="padding-left: 0">
            <div style="background-color: #FFFFFF;padding-top:10px;overflow-y:auto;overflow-x: hidden" id="left-info">
                <div class="search-page">
                    <div id="app" class="search-bar">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <h3 style="margin-top: 6px;">物资类型管理</h3>
                                    </div>
                                    <div  class="col-sm-6">
                                        <button type="button" class="btn btn yellow" onclick="create()" title="新建">
                                            <i class="fa fa-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn green" onclick="edit()" title="编辑">
                                            <i class="fa fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn red" onclick="del()" title="删除">
                                            <i class="fa fa-trash-o"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div id="tree"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-9" style="padding-left: 0">
            <div class="search-page">
                <div id="app_goods" class="search-bar bordered">
                    <form class="form-horizontal" autocomplete="off">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">物料</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control" name="name" v-model="name"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8 to-right">
                                <button type="button" class="btn green" @click="search">
                                    <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                                </button>
                                <button type="button" class="btn blue" @click="reset">
                                    <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                                </button>
                                <button type="button" class="btn yellow" onclick="creategoods()">
                                    <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="search-table">
                    <div style="max-height: 700px; overflow-y: auto; width: 100%;">
                    <table id="table"
                 
                           data-mobile-responsive="true"
                           data-pagination="true"
                           data-query-params="app_goods.params"
                           data-url="{{ url('purchase/goods/list/json') }}"
                           data-page-size="10"
                           data-side-pagination="server">
                        <thead class="bg-blue" style="position: sticky; top: 0; z-index: 1;">
                        <tr>
                            <th data-field="code">编码</th>
                            <th data-field="name">名称</th>
                            <th data-field="spec">规格</th>
                            <th data-field="model">型号</th>
                            <th data-field="inventory_code">存货代码</th>
                            <th data-field="as_name">存货简称</th>
                            <th data-field="type_name">存货分类</th>
                            <th data-field="unit">采购单位</th>
                            <th data-field="deputy_unit">库存单位</th>
                            <th data-field="supplier_name">供应商</th>
                            <th data-field="taxation">税目</th>
                            <th data-field="price">单价</th>
                            <th data-field="tax_rate" data-formatter="taxFormatter">税率</th>
                            <th data-field="formula_val">公式</th>
                            <th data-field="check_flag" data-formatter="checkFormatter">检验</th>
                            <th data-field="warning_flag" data-formatter="statusFormatter">预警</th>
                            <th data-field="warning_quantity">预警值</th>
                            <th data-field="unit_conversion_rate">单位换算率</th>
                            <th data-formatter="actionFormatter">操作</th>
                        </tr>
                        </thead>
                    </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="act" style="display: none;">
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                操作 <span class="caret"></span>
            </button>
            <ul class="dropdown-menu pull-right" role="menu">
                <li><a href="javascript:" onclick="edit_goods('@id@')"><i class="fa fa-fw fa-edit"></i> 编辑</a></li>
                <li>
                    <a href="javascript:" onclick="del_goods('@id@')">
                        <i class="fa fa-close fa-fw"></i> 删除
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app_goods = new Vue({
        el: '#app_goods',
        data: {
            uid: '',
            name: '',
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.uid = this.uid;
                p.name = this.name;
                return p;
            },
            reset: function() {
                this.name = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
        }
    });

    $table.bootstrapTable();
    function actionFormatter(v, row, idx) {
        return $('#act').html().replace(/@id@/g, row.id);
    }

    function taxFormatter(v, row) {
        return (Number(v) * 100).toFixed(0) + '%';
    }

    function statusFormatter(v, row, idx) {
        let rtn_html = '';
        if (v == 1) {
            rtn_html = '<span class="label label-warning">预警</span>';
        }
        return rtn_html;
    }

    function checkFormatter(v, row, idx) {
        let rtn_html = '';
        if (v == 1) {
            rtn_html = '<span class="label label-danger">检验</span>';
        }
        return rtn_html;
    }

    function batchFormatter(v, row, idx) {
        return v == 1 ? '是': '-';
    }

    var $tree = $('#tree');

    function refreshTree() {
        $.post('{{ url('purchase/goodstype/list') }}', function (rs) {
            $tree.jstree(true).settings.core.data = rs;
            $tree.jstree('refresh');

            if (rs.length > 0) {
                app_goods.uid = rs[0].uid;
                app_goods.search();
            }
        });
    }

    function create() {
        top.window.layer_result='';
        top.layer.open({
            title:'新建',
            type: 2,
            area: ['50em', '80%'],
            content: '{{ url('purchase/goodstype/create') }}',
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    refreshTree();
                }
            }
        });
    }

    function edit() {
        if (!app_goods.uid) {
            layer.alert('请先选择商品类型!');
            return;
        }

        top.window.layer_result = '';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: makeArea('40em', '80%'),
            content: '{{ url('purchase/goodstype/edit/') }}' + app_goods.uid,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    refreshTree();
                }
            }
        });
    }

    function del() {
        if (!app_goods.uid) {
            alertWarning('请先选择商品类型!');
            return;
        }

        var dlg = top.layer.confirm('确认删除吗?', function(){
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('purchase/goodstype/delete') }}", { uid: app_goods.uid }, function (rs) {
                closeSpin();
                if(rs.status=='ok'){
                    toastr.success('操作成功!');
                    refreshTree();
                }
                else {
                    toastr.error('操作失败!'+rs.message);
                }
            })
        });
    }

    function creategoods() {
        if (!app_goods.uid) {
            alertWarning('请先选择商品类型!');
            return;
        }
        top.window.layer_result='';
        top.layer.open({
            title:'新建',
            type: 2,
            area: ['65%', '85%'],
            content: '{{ url('purchase/goods/create/') }}' + app_goods.uid,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refreshOptions', {pageNumber: 1});
                }
            }
        });
    }

    function edit_goods(id) {
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['65%', '85%'],
            content: '{{ url('purchase/goods/edit/') }}' + id,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refreshOptions', {pageNumber: 1});

                }
            }
        });
    }

    function del_goods(id) {
        var dlg = top.layer.confirm('确认删除吗？', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('purchase/goods/delete') }}", {id: id}, function (rs) {
                closeSpin();
                if (rs.status == 'ok') {
                    toastr.success('操作成功！');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }

    function adjustMap() {
        var h = $(window).height();
        $('#left-info').height(h-50);
    }

    $(function() {
        $tree.jstree({
            "core": {
                "data": {{ tree_list | json_encode }}
            }
        });

        $tree.on("select_node.jstree", function (e, data) {
            app_goods.uid = data.node.original.uid;
            app_goods.search();
        });

        adjustMap();
    });

    $(window).resize(function () {
        adjustMap();
    });
</script>
