{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="form-group">
                        <label>上级部门</label>
                        <div>
                            <input type="text" class="form-control" name="pname" v-model="pname" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>分类编码</label>
                        <div>
                            <input type="text" class="form-control" name="code" v-model="code" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>名称<span class="required"> * </span></label>
                        <div>
                            <input type="text" class="form-control" name="name" v-model="name" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>缩写<span class="required"> * </span></label>
                        <div>
                            <input type="text" class="form-control" name="short_name" v-model="short_name" required>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonGoodsType }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                showSpin();
                $.post('{{ url('purchase/goodstype/edit/' ~ goodstype.uid) }}', app.$data, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            }
        }
    });
</script>
