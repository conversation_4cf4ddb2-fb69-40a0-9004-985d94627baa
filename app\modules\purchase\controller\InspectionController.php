<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInspection;
use Envsan\Modules\Purchase\Service\PurchaseInspectionService;


/**
 * @name('采购质检')
 */
class InspectionController extends SuperController
{
    private $page_id = 51;

    private $check_page_id = 52;

    /**
     * @name('报检单列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseInspectionService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @acl({'link':'purchase:inspection:list'})
     */
    public function checklistAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseInspectionService();
            $builder = $s->selectCheckAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->check_page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->check_page_id;
    }

    /**
     * @acl({'link':'purchase:inspection:list'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseInspectionService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, CvtUtil::emptyToArray($row->ext_data));
        $this->view->jsonPurchaseInspection = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('inspection/create');
    }

    /**
     * @acl({'link':'purchase:inspection:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseInspectionService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new PurchaseInspectionService();
        // 取得报检单信息
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        // 取得报检单物料明细信息
        $details = $s->selectInspectionDetail($row->id);
        $jrow['inspection_list'] = $details ? $details->toArray() : [];

        $this->view->jsonPurchaseInspection = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    function checkAction($uid)
    {
        $s = new PurchaseInspectionService();
        // 取得报检单信息
        $row = $s->selectDetailByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        // 取得报检单物料明细信息
        $details = $s->selectInspectionDetail4one($uid);
        $jrow = $details->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $fs = new FileService();
        $jrow['base_path'] =  $fs->getImagePath();
        $jrow['check_data'] = CvtUtil::emptyToArray($jrow['check_data']);
        $jrow['check_images'] = CvtUtil::emptyToArray($jrow['check_images']);

        $this->view->jsonData = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseInspectionService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}