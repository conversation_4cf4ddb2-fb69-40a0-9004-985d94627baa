<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Printing\Service\TemplateService;
use Envsan\Modules\Purchase\Model\PurchaseInstock;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\InstockService;
use Envsan\Modules\Purchase\Service\PurchaseReceiptService;

/**
 * @name('采购入库')
 */
class InstockController extends SuperController
{
    private $page_id = 25;
    private $search_page_id = 30;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new InstockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList($this->page_id));
    }

    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new InstockService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $rs = new InstockService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->create());
            return json_encode($ret);
        }
        $table = new TableService();
        $fs = new FileService();
        $jrow = (new PurchaseInstock())->toArray();
        $jrow['instock_date'] = DateUtil::today();
        $jrow['order_code'] = '';
        $jrow['receipt_code'] = '';
        $jrow['supplier_name'] = '';
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['detail_data'] = [];
        $jrow['files'] = [];
        $jrow['base_path'] = $fs->getImagePath();
        $prs = new PurchaseReceiptService();
        // 采购到货单列表
        $this->view->receiptList = $prs->getAllReceipts();
        $this->view->jsonInstock = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->page_id = $this->page_id;
    }

    /**
     * @acl({'link':'purchase:instock:create'})
     */
    function editAction($uid)
    {
        $rs = new InstockService();
        if ($this->request->isPost()) {
            $row = $rs->selectInStockByUid($uid);
        } else {
            $row = $rs->selectByUid($uid);
        }

        if (empty($row))
            die(ErrorHelper::WRONG_ID);
        if ($row->status > 10)
            die('已提交入库');

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($rs->update($row));
            return json_encode($ret);
        } else {
            $table = new TableService();
            $fs = new FileService();
            $ext_data = CvtUtil::emptyToArray($row->ext_data);

            $jrow = $row->toArray();
            $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
            $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
            $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
            $jrow['base_path'] = $fs->getImagePath();
            $this->view->jsonInstock = json_encode($jrow);
            $this->view->uid = $uid;
            $this->view->extDataName = 'ext_data';
            $this->view->extDataCnt = 6;
            $this->view->page_id = $this->page_id;
            $this->view->pick('instock/create');
        }

    }

    /**
     * @acl({'link':'purchase:instock:create'})
     */
    public function deleteAction(){
        $s = new InstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:instock:create'})
     */
    public function cancelAction(){
        $s = new InstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->cancel());
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $rs = new InstockService();
        $row = $rs->selectInStockByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row;
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = $rs->getDetail($jrow['id']);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonInstock = json_encode($jrow);
    }

    /**
     * 取得
     * @skipacl
     */
    public function receiptAction($receipt_id)
    {
        $s = new InstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->getDetailList($receipt_id));
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function checkAction($uid)
    {
        $s = new InstockService();
        $row = $s->selectDetailByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);
        $jrow = $row->toArray();
        $fs = new FileService();
        $jrow['base_path'] =  $fs->getImagePath();
        $jrow['check_data'] = CvtUtil::emptyToArray($jrow['check_data']);
        $jrow['check_images'] = CvtUtil::emptyToArray($jrow['check_images']);
        $this->view->jsonData = json_encode($jrow,JSON_UNESCAPED_UNICODE);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new InstockService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new InstockService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }
}