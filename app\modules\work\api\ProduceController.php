<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\ProduceService;

class ProduceController extends SuperController
{
    public function dataAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->getData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function initAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->getData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function otherinitAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->getOtherData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function produceAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $ret->handleResult($s->saveProduce());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function otherAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->saveOther();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function listAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->getReportList();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->deleteReport();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function deleteotherAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->deleteOther();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function editAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->editReport();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function editSaveAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->editReportSave();
            $ret->message = $rtn->message;
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    public function viewAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new ProduceService();
            $ret = new JsonData();
            $rtn = $s->getViewData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }
}
