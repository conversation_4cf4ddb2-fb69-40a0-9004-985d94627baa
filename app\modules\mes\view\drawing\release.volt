{{ assets.outputJs('validate') }}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.worker.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vue.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/Sortable.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vuedraggable.umd.min.js') %}
<!-- Main content -->
<div id="app" style="width: 100vw;height: 100vh;background-color: #f2f2f2">
    <div style="display: flex">
        <div style="width: 25vw;background-color: #fff;border-right: 1px solid #c2c2c2;text-align: center;padding: 15px;height: 100vh;overflow: auto;">
            <div style="background-color: #f2f2f2;padding: 10px">
                <button type="button" class="btn blue btn-outline" @click="submit" style="width: 180px;"><i class="fa fa-check"></i> 发布 ( 版本号：<span v-text="code"></span>)</button>
            </div>
            <draggable tag="ul" :list="drawing_images"
                       :group="{ name: 'bind_data', pull: 'clone', put: false }"
                       :move="onMove"
                       class="list-group"
                       handle=".handle">
                <li
                        class="list-group-item"
                        v-for="drawing_item,drawing_index in drawing_images"
                        :key="drawing_index"
                >
                    <div style="display: flex">
                        <div style="display: flex;flex-direction: column;justify-content: center">
                            <i class="fa fa-align-justify handle" style="margin-right: 10px"></i>
                        </div>
                        <div>
                            <img style="width: 18vw" :src="drawing_item.base64" class="lightbox-image" />
                            <div>
                                <span v-text="drawing_item.url_name"></span>
                            </div>
                        </div>
                    </div>
                </li>
            </draggable>
        </div>
        <div style="width: 25vw;background-color: #fff;text-align: center;padding: 15px;height: 100vh;overflow: auto;border-right: 1px solid #c2c2c2" v-for="(bom_data,bom_idx)  in bom_list">
            <div style="background-color: #f2f2f2;padding: 10px">
                型号/工艺：<span v-text="bom_data.model_name + '/' + bom_data.name"></span>
            </div>
            <draggable tag="ul" :list="bom_data.drawing_data"
                       :group="{ name: 'bind_data', pull: false }"
                       class="list-group"
                       handle=".handle">
                <li
                        class="list-group-item"
                        v-for="drawing_item,drawing_index in bom_data.drawing_data"
                        :key="drawing_index"
                >
                    <div style="display: flex">
                        <div style="display: flex;flex-direction: column;justify-content: center">
                            <i class="fa fa-align-justify handle" style="margin-right: 10px"></i>
                        </div>
                        <div>
                            <a v-if="drawing_item.url != ''" class="lightbox-a"
                               :href="base_path + drawing_item.url" data-lightbox="goods-pic" :data-title="drawing_item.url_name">
                                <img style="width: 20vw" :src="base_path + drawing_item.url" class="lightbox-image" />
                            </a>
                            <a v-else class="lightbox-a" data-lightbox="goods-pic" :data-title="drawing_item.url_name">
                                <img style="width: 18vw" :src="drawing_item.base64" class="lightbox-image" />
                            </a>
                            <div>
                                <a style="color: red;font-size: 20px" v-if="drawing_item.status == 0" @click="bomDrawingDelete(bom_idx,drawing_index)"><i class="fa fa-times"></i></a>
                                <span v-text="drawing_item.url_name"></span>
                            </div>
                        </div>
                    </div>
                </li>
            </draggable>
        </div>
    </div>
</div>

{{ partial('upyun_base64') }}
<script>
    var pdfDoc = null;
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        components: {
            draggable: window.vuedraggable
        },
        methods: {
            initDrawing(){
                showSpin();
                pdfjsLib.getDocument(app.base_path + app.drawing_url).promise.then((pdfDoc_) => {
                    pdfDoc = pdfDoc_;
                    let page_cnt =  pdfDoc.numPages;
                    app.renderPage(1,page_cnt,app.drawing_name,app.uid,app.drawing_url);
                })
            },
            bomDrawingDelete(bom_idx,drawing_index){
                app.bom_list[bom_idx].drawing_data.splice(drawing_index,1);
            },
            renderPage(num,page_cnt,name,uid,drawing_url) {
                if (num > page_cnt){
                    closeSpin();
                    return;
                }
                pdfDoc.getPage(num).then((page)=> {
                    const viewport = page.getViewport({ scale: 1 });
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
                    ctx.imageSmoothingEnabled = true;
                    const renderContext = {
                        canvasContext: ctx,
                        viewport: viewport
                    };
                    const renderTask = page.render(renderContext);
                    renderTask.promise.then(()=> {
                        let base64 =  canvas.toDataURL('image/jpeg',0.95);
                        app.drawing_images.push({
                            drawing_uid : uid,
                            page_num : num,
                            base64 : base64,
                            url_name :num + '_'+ name,
                            url : '',
                            drawing_url:drawing_url,
                            status : 0
                        });
                        num++;
                        app.renderPage(num,page_cnt,name,uid,drawing_url);
                    });
                });
            },
            drawingSelect(drawing_item){
                if (app.bom_show == 1 && app.bom_tab_id == 1){
                    for(let item of app.bom_data.drawing_data){
                        if (item.drawing_uid == drawing_item.drawing_uid && item.page_num == drawing_item.page_num){
                            toastr.error('不能重复选择！');
                            return;
                        }
                    }
                    app.bom_data.drawing_data.push(JSON.parse(JSON.stringify(drawing_item)));
                }
            },
            onMove({ relatedContext, draggedContext }) {
                const relatedElement = relatedContext.element;
                const draggedElement = draggedContext.element;
                for (let item of relatedContext.list){
                    if (item.drawing_uid == draggedElement.drawing_uid && item.page_num == draggedElement.page_num){
                        return false;
                    }
                }
                return true;
            },
            submit(){
                var dlg = top.layer.confirm('确认发布吗？', () => {
                    top.layer.close(dlg);
                    showSpin();
                    app.uploadBase64(0,0,()=>{
                        $.post('{{ url('mes/drawing/release/' ~ uid) }}', {
                            uid : app.uid,
                            bom_list : encodeURI(JSON.stringify(app.bom_list)),
                        } , (rs) => {
                            closeSpin(null);
                            if (rs.status == 'ok') {
                                toastr.success('操作成功');
                                top.window.layer_result = 'ok';
                                top.layer.close(top.layer.getFrameIndex(window.name));
                            } else {
                                toastr.error(rs.message);
                            }
                        })
                    });
                });
            },
            uploadBase64(bom_idx,idx,cb){
                if (bom_idx >= app.bom_list.length){
                    cb();
                    return;
                }
                if (app.bom_list[bom_idx].drawing_data[idx].url == ''){
                    upyunUploadBase64('product', app.bom_list[bom_idx].drawing_data[idx].base64).then((url) => {
                        let item = app.bom_list[bom_idx].drawing_data[idx]
                        for (let bom_item of app.bom_list){
                            for (let drawing_item of bom_item.drawing_data){
                                if (drawing_item.drawing_uid == item.drawing_uid && drawing_item.page_num == item.page_num){
                                    drawing_item.base64 = '';
                                    drawing_item.url = url;
                                }
                            }
                        }
                        idx++;
                        if (idx >= app.bom_list[bom_idx].drawing_data.length){
                            idx = 0;
                            bom_idx ++;
                        }
                        app.uploadBase64(bom_idx,idx, cb);
                    }).catch((e) => {
                        toastr.error('上传失败！' + e);
                    });
                } else {
                    idx++;
                    if (idx >= app.bom_list[bom_idx].drawing_data.length){
                        idx = 0;
                        bom_idx ++;
                    }
                    this.uploadBase64(bom_idx,idx,cb);
                }
            },
        }
    });

    $(function (){
        app.initDrawing();
    })
</script>
<style>

</style>