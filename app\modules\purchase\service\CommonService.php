<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Data\SessionData;
use Envsan\Modules\Purchase\Model\PurchaseGoodsType;
use Phalcon\Mvc\User\Component;

class CommonService extends Component
{
    public function getSupplierList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.id,a.code, a.name, a.name_as')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a')
            ->where('a.del_flag = 0 and a.owner = ?1',[1 => SessionData::ownerId()]);
        return $builder->getQuery()->execute();
    }

    public function getOrderList($id = '')
    {
        return $this->getOrderListCommon(1, $id);
    }

    public function getWworderList($id = '')
    {
        return $this->getOrderListCommon(2, $id);
    }

    private function getOrderListCommon($order_type, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.order_code,
                t2.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.order_type = ?2 and t1.owner = ?1', [1 => SessionData::ownerId(), 2 => $order_type])
            ->orderBy('t1.id desc');
        if (empty($id)) {
            $builder->andWhere('t1.status = 30');
        } else {
            $builder->andWhere('t1.id = ?3', [3 => $id]);
        }
        return $builder->getQuery()->execute();
    }

    public function getGoodsList($uid)
    {
        $type_ids = [-1];
        if (!empty($uid)) {
            $gts = new GoodsTypeService();
            $row = $gts->selectByUid($uid);
            if (!empty($row)) {
                $type_ids = $gts->getTypeIds($row->id);
            }
        }

        $columns = '
            a.id,
            a.uid,
            a.name,
            a.code,
            a.spec,
            a.check_flag,
            a.model,
            a.unit,
            a.deputy_unit,
            a.price,
            a.price_hs,
            round(a.unit_conversion_rate,4) as unit_conversion_rate,
            a.is_batch_managed,
            a.tax_rate,
            ifnull(s.name_as,\'\') as supplier_name,
            round(ifnull(t.quantity,0),4) as stock_cnt,
            1 as show
        ';

        $formula_flag = $this->request->getPost('formula_flag', 'tstring');
        if ($formula_flag == 1) {
            $columns .= '
                ,round(a.cd, 2) as cd,
                round(a.kd, 2) as kd,
                round(a.hd, 2) as hd,
                round(a.weight, 3) as weight,
                a.formula_list
            ';
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns($columns)
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a.supplier_id = s.id', 's')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewStock', 'a.id = t.goods_id', 't')
            ->where('a.del_flag = 0 and a.owner = :owner:', ['owner'=>SessionData::ownerId()])
            ->inWhere('a.type_id', $type_ids)
            ->orderBy('a.code asc');
        return $builder->getQuery()->execute();
    }

    public function getGoodsBuilder($goods_name, $cols, $builder)
    {
        $names = explode(' ', $goods_name);
        for ($i = 0; $i < count($names); $i++)
        {
            $idx = $i + 100;
            $where = '';
            $params = [];
            foreach ($cols as $col)
            {
                $where .= ' or '.$col.' like ?'.$idx;
            }

            $params[$idx] = "%$names[$i]%";
            $builder->andWhere(trim($where, ' or '), $params);
        }
        return $builder;
    }
}