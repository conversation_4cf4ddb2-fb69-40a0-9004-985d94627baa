<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\SmsUtil;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Sys\Service\UserService;
use Phalcon\Mvc\User\Component;

class LoginService extends Component
{
    public function getMobileCode()
    {
        $mobile = $this->request->getPost('mobile', 'tstring');
        $user = User::findFirst(['del_flag = 0 and mobile = ?1', 'bind' => [1 => $mobile]]);
        if ($user == false) {
            return '该手机号未注册';
        }

        $code = rand(100000, 999999);
        $ret = SmsUtil::sendCodeSms($mobile, $code);
        if ($ret == 'ok') {
            $this->session->set('sms_code', $code.$mobile);
            return '';
        } else {
            return '验证码发送失败';
        }
    }

    public function login()
    {
        $ret = [];
        $ret['status'] = 'error';
        $username = $this->request->getPost('username', 'tstring');
        $pwd = $this->request->getPost('pwd', 'tstring');

        if (empty($username) || empty($pwd)) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }
        $user = User::findFirstDirect([
            'login_name=?1 and del_flag = 0',
            'bind'=>[1=>$username]
        ]);
        if ($user == false) {
            $ret['message'] = '用户名不存在';
            return $ret;
        }
        if ($user->password != $pwd){
            $ret['message'] = '密码不正确';
            return $ret;
        }
        $user->session_id = $this->session->getId();
        $registration = $this->session->get('app_registration_id');
        if (empty($registration)){
            $user->app_registration_id = null;
        }  else {
            $user->app_registration_id = $registration;
        }
        $this->session->set(SessionData::$_USER_KEY, $user);
        if (!$user->save()) {
            $this->session->remove(SessionData::$_USER_KEY);
            $ret['message'] = '账号绑定失败';
            return $ret;
        }
        $gs = new GroupService();
        if (!empty($user->auth_data_ids)){
            $arr = explode(',', $user->auth_data_ids);
        } else {
            $arr = $gs->selectGroupIds($user->group_id);
        }
        $this->session->set('group_ids', $arr);
        $this->session->set(SessionData::$_USER_KEY, $user);

        $us = new UserService();
        $this->session->set('acl', $us->buildAcl($user));

        $cs = new CommonService();
        $ret['status'] = 'ok';
        $ret['user'] = $cs->getUserData();
        $ret['sid'] = $this->session->getId();
        return $ret;
    }

    public function logout()
    {
        $ret = [];
        $ret['status'] = 'error';

        if (empty($this->session->get('is_weapp'))) {
            $ret['is_weapp'] = 0;
        } else {
            $ret['is_weapp'] = $this->session->get('is_weapp');
        }
        $ret['app_registration_id'] = $this->session->get('app_registration_id');
        $us = new UserService();
        $row = $us->selectById(SessionData::user()->id);
        if ($row == false) {
            $ret['status'] = 'ok';
            return $ret;
        }
        $row->manage_open_id = null;
        $row->managewx_open_id = null;
        $row->app_registration_id = null;
        $row->session_id = null;
        if ($row->save()) {
            $this->session->remove(SessionData::$_USER_KEY);
            $this->session->remove(SessionData::$_OWNER_KEY);
            $this->session->remove('group_ids');
            $ret['status'] = 'ok';
            return $ret;
        }
        $ret['message'] = ErrorHelper::UNKOWN;
        return $ret;
    }
}