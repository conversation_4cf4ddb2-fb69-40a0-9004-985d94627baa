<?php
namespace Envsan\Common\Util\Excel;

use PHPExcel_IOFactory;
use PHPExcel_Reader_Excel2007;
use PHPExcel_Reader_HTML;
use ReflectionClass;

class ReadWrite
{
    public static function read($path, $class_name, $annotations)
    {
        $excel_reader = new PHPExcel_Reader_Excel2007();
        $excel = $excel_reader->load($path);
        $current_sheet = $excel->getSheet(0);
        $comments = $current_sheet->getComments();
        //读取execl注释
        $col_attr_arr = array();
        foreach ($comments as $pCellCoordinate => $comment) {
            $cell = $current_sheet->getCell($pCellCoordinate);
            if ($cell->getRow() == 1) {
                $name = trim($comment->getText()->getPlainText());
                if ($name != '') {
                    $sub = new \stdClass();
                    $sub->index = $cell->getColumn();
                    $sub->col = $name;
                    $sub->name = $cell->getValue();
                    array_push($col_attr_arr, $sub);
                }
            }
        }
        //读取类注释
        $class = new ReflectionClass($class_name);
        $class_properties = $class->getProperties();
        $col_annotation = array();
        foreach ($class_properties as $property) {
            $property_name = $property->getName();
            $sub_annotations = $annotations->getProperty($class_name, $property_name);
            foreach ($sub_annotations as $annotation) {
                if ($annotation->getName() == 'Column') {
                    $argu_cnt = $annotation->numberArguments();
                    if ($argu_cnt == 3) {
                        $col_annotation[$property_name] = array(
                            'type' => $annotation->getArguments()['type'],
                            'length' => $annotation->getArguments()['length'],
                            'nullable' => $annotation->getArguments()['nullable']
                        );
                    }
                }
            }
        }
        //excel导入内容读入
        $read_arr = array();
        $read_err_arr = array();
        $err_code_arr = array();
        $err_cnt = 0;
        for ($i = 2; $i <= 2000; $i++) {
            $row_empry = '';
            $flg = true;
            $instance = $class->newInstanceArgs();
            foreach ($col_attr_arr as $col_attr) {
                if (array_key_exists($col_attr->col, $col_annotation)) {
                    $col_attr_col = $col_annotation[$col_attr->col];
                    $val = $current_sheet->getCell($col_attr->index . $i)->getValue();
                    $class->getProperty($col_attr->col)->setValue($instance, $val);
                    $row_empry = $row_empry . $val;
                    $err_code = array();
                    if (!Check::typeCheck($col_attr_col['type'], $val)) {
                        array_push($err_code, 1);
                        $flg = false;
                    }
                    if (!Check::lengthCheck($col_attr_col['length'], $val)) {
                        array_push($err_code, 2);
                        $flg = false;
                    }
                    if (!Check::emptyCheck($col_attr_col['nullable'], $val)) {
                        array_push($err_code, 3);
                        $flg = false;
                    }
                    if (count($err_code) > 0) {
                        $err_code_arr[$col_attr->col . $err_cnt] = $err_code;
                    }
                }
            }
            if (trim($row_empry) == '') {
                break;
            }
            if ($flg) {
                array_push($read_arr, $instance);
            } else {
                array_push($read_err_arr, $instance);
                $err_cnt++;
            }
        }
        $execl_obj = new \stdClass();
        $execl_obj->succ_arr = $read_arr;
        $execl_obj->err_arr = $read_err_arr;
        $execl_obj->err_code_arr = $err_code_arr;
        return $execl_obj;
    }

    public static function write($tpl_path, $data_arr)
    {
        $excel_reader = new PHPExcel_Reader_Excel2007();
        $excel = $excel_reader->load($tpl_path);
        $current_sheet = $excel->getSheet(0);
        $comments = $current_sheet->getComments();
        //读取execl注释
        $col_attr_arr = array();
        foreach ($comments as $pCellCoordinate => $comment) {
            $cell = $current_sheet->getCell($pCellCoordinate);
            if ($cell->getRow() == 1) {
                $name = trim($comment->getText()->getPlainText());
                if ($name != '') {
                    $sub = new \stdClass();
                    $sub->index = $cell->getColumn();
                    $sub->col = $name;
                    array_push($col_attr_arr, $sub);
                }
            }
        }
        $class_name = get_class($data_arr[0]);
        $class = new ReflectionClass($class_name);
        $i = 2;
        foreach ($data_arr as $row) {
            foreach ($col_attr_arr as $col_attr) {
                $val = $class->getProperty($col_attr->col)->getValue($row);
                $current_sheet->getCell($col_attr->index . $i)->setValue($val);
            }
            $i++;
        }
        $objWriter = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
        $objWriter->save('php://output');
        return '';
    }

    public static function writeError($tpl_path, $save_path, $data_arr, $err_code_arr)
    {
        $excel_reader = new PHPExcel_Reader_Excel2007();
        $excel = $excel_reader->load($tpl_path);
        $current_sheet = $excel->getSheet(0);
        $comments = $current_sheet->getComments();
        //读取execl注释
        $col_attr_arr = array();
        foreach ($comments as $pCellCoordinate => $comment) {
            $cell = $current_sheet->getCell($pCellCoordinate);
            if ($cell->getRow() == 1) {
                $name = trim($comment->getText()->getPlainText());
                if ($name != '') {
                    $sub = new \stdClass();
                    $sub->index = $cell->getColumn();
                    $sub->col = $name;
                    array_push($col_attr_arr, $sub);
                }
            }
        }
        $class_name = get_class($data_arr[0]);
        $class = new ReflectionClass($class_name);
        $i = 2;
        foreach ($data_arr as $row) {
            foreach ($col_attr_arr as $col_attr) {
                $val = $class->getProperty($col_attr->col)->getValue($row);
                $current_sheet->getCell($col_attr->index . $i)->setValue($val);
                if (array_key_exists($col_attr->col . ($i - 2), $err_code_arr)) {
                    $current_sheet->getComment($col_attr->index . $i)
                        ->getText()->createTextRun(Check::checkMsg($err_code_arr[$col_attr->col . ($i - 2)]));
                }
            }
            $i++;
        }
        $objWriter = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
        $objWriter->save($save_path);
        return '';
    }

    public static function writeArray($title, $data, $hasHeader, $version = '2003')
    {
        $body = '<table>';
        $startIdx = 0;
        if ($hasHeader && count($data) > 0) {
            $body .= '<thead><tr>';
            $columns = $data[0];

            foreach ((array)$columns as $column) {
                $body .= "<th>$column</th>";
            }
            $body .= '</tr></thead>' . PHP_EOL;
            $startIdx++;
        }

        $body .= '<tbody>';
        for ($i = $startIdx; $i < count($data); $i++) {
            $body .= '<tr>';
            $columns = $data[$i];
            foreach ((array)$columns as $column) {
                $body .= "<td>$column</td>";
            }
            $body .= '</tr>' . PHP_EOL;
        }
        $body .= '</tbody>';

        $html = file_get_contents(__DIR__ . '/html.tpl');
        $html = str_replace('{title}', $title, $html);
        $html = str_replace('{body}', $body, $html);

        $tmpfile = tempnam(null, 'ex_');
        file_put_contents($tmpfile, $html);
        $reader = new PHPExcel_Reader_HTML;
        $content = $reader->load($tmpfile);
        if ($version == '2003') {
            $objWriter = PHPExcel_IOFactory::createWriter($content, 'Excel5');
        } else {
            $objWriter = PHPExcel_IOFactory::createWriter($content, 'Excel2007');
        }
        $objWriter->save('php://output');
        unlink($tmpfile);
    }
}