<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
class PurchaseInventoryDetail extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $inventory_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_spec;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_model;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $quantity_before;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $quantity_after;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_inventory_detail';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseInventoryDetail[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseInventoryDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
