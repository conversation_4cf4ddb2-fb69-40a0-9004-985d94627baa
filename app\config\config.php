<?php
/*
 * Modified: preppend directory path of current file, because of this file own different ENV under between Apache and command line.
 * NOTE: please remove this comment.
 */
defined('BASE_PATH') || define('BASE_PATH', getenv('BASE_PATH') ?: realpath(dirname(__FILE__) . '/../..'));
defined('APP_PATH') || define('APP_PATH', BASE_PATH . '/app');

return new \Phalcon\Config([
    'version' => '1.0',

    'database' => [
        'adapter'  => 'Mysql',
        'host'     => 'rm-f8z6p121g2h4j6jw2zo.mysql.rds.aliyuncs.com',
        'username' => 'rrts_test',
        'password' => 'bingdundun@BeiJing_2022',
        'dbname'   => 'mes_mingjing_test',
        'charset'  => 'utf8',
    ],

    'application' => [
        'appDir' => APP_PATH . '/',
        'modelsDir' => APP_PATH . '/common/model/',
        'cacheDir' => BASE_PATH . '/cache/',
        'baseUri' => '/',
        'logDir' => APP_PATH . '/log/',
        'menuDir' => APP_PATH . '/modules/menu/',
        'staticVersion' => '1.0.0'
    ],

    'redis' => [
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => 'zaq12wsx',
        'sessionIdx' => 1,
        'cacheIdx' => 2,
        'metaIdx' => 3
    ],

    'site' => [
        'homeUrl' => 'sys/home/<USER>', //登录成功之后的跳转页面
    ],

    'api' => [
        'authKey' => '0496cc01440f5787', //用于加密用户名密码并保存在手机客户端的密匙
    ],

    'pda_version' => [
        'version' => '1.0.0',
        'version_show' => '1.0.0',
        'download' => 'http://upyun.ftmespro.com/1/apk/pda.apk'
    ],

    'upload' => [
        'uploadDir' => BASE_PATH . '/public/upload/',
        'templateDir' => BASE_PATH . '/public/static/template/',
        'errorDir' => BASE_PATH . '/public/static/error_excel/',
    ],

    'jpush' => [
        'key' =>'7cdea427da1be3e9d8606d07',
        'secret' => '7fbaacb2b08330c2d42aafd1'
    ],

    'upyun' => [
        'host'      => 'https://upymj.ftmespro.com',
        'bucket'    => 'mingjing',
        'user'      => 'ftmes',
        'password'  => 'ewZGccM7L56TjUKdmzNYUQrxDkgIbjlf',
        'uploadDir' => BASE_PATH . '/upload/',
        'baseDir' => '/mingxing/',
        'goods_template_dir' => 'template/原料导入模板v1.0.xlsx'
    ],

    // 当给单租户使用时，当前的ownerID是多少
    'ownerId' => 1,

    // 是否显示数据库查询的各种语句
    'showDbLog' => true,

    'printNewLine' => true,

    // 是否是开发版，发布版必须设置为false
    'developMode' => true,

    // 开发期间模拟的owner
    'developOwner' => 1,
]);
