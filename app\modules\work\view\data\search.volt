{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">审批查询</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">审批类型</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="type_name" v-model="type_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">审批内容</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="content" v-model="content"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">发起日期</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control date dtpicker" name="date_start" v-model="date_start"/>
                                    <span class="input-group-addon no-border-lr">至</span>
                                    <input type="text" class="form-control date dtpicker" name="date_end" v-model="date_end"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">审批状态</label>
                            <div class="col-md-9">
                                <select class="bs-select form-control" name="status" v-model="status">
                                    <option value="">全部</option>
                                    <option value="1">进行中</option>
                                    <option value="2">通过</option>
                                    <option value="3">拒绝</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">提交人</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="user_name" v-model="user_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">提交部门</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="group_name" v-model="group_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">

                    </div>
                    <div class="col-md-3 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table" >
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('work/data/search/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="status" data-formatter="statusFormatter">状态</th>
                    <th data-field="type_name">审批类型</th>
                    <th data-field="group_name">提交部门</th>
                    <th data-field="create_name">提交人</th>
                    <th data-field="create_date">提交时间</th>
                    <th data-field="abstrakt_data" data-formatter="abstraktFormatter">审批摘要</th>
                    <th data-field="remarks">备注</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="list-table">
        <button type="button" onclick="view('@uid@')" class="btn blue">详情</button>
    </div>
</div>

<div id="act_1" style="display: none;">
    <div class="list-table">
        <button type="button" onclick="view('@uid@')" class="btn blue">详情</button>
        <button type="button" onclick="print('@uid@')" class="btn green">打印</button>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            content: '',
            type_name: '',
            date_start: '',
            date_end: '',
            status: '',
            user_name:'',
            group_name:''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.content = this.content;
                p.type_name = this.type_name;
                p.date_start = this.date_start;
                p.date_end = this.date_end;
                p.status = this.status;
                p.user_name = this.user_name;
                p.group_name = this.group_name;
                return p;
            },
            reset: function() {
                this.content = '';
                this.type_name = '';
                this.date_start = '';
                this.date_end = '';
                this.user_name = '';
                this.group_name = '';
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable();
    function actionFormatter(v, row, idx) {
        if (row.handle_status == 1) {
            return $('#act_1').html().replace(/@uid@/g, row.uid);
        } else {
            return $('#act').html().replace(/@uid@/g, row.uid);
        }
    }

    function statusFormatter(v, row) {
        let str = '';
        if (row.status == 15){
            str += '<span class="label label-info">审批中</span>&nbsp;';
        }
        if (row.handle_status == 1){
            str += '<span class="label label-success">通过</span>&nbsp;';
        }
        if (row.handle_status == 2){
            str += '<span class="label label-danger">拒绝</span>&nbsp;';
        }
        if (row.handle_status == 3){
            str += '<span class="label label-danger">撤回</span>&nbsp;';
        }
        if (row.pressing_flag > 0){
            str += '<span class="label label-danger">催办(+'+row.pressing_flag+')</span>&nbsp;';
        }
        if (row.review_type == 1){
            str += '<span class="label label-danger">撤销审批</span>&nbsp;';
        }
        return str;
    }

    function abstraktFormatter(list){
        if (list == null){
            return '-';
        }
        list = JSON.parse(list);
        let str = '';
        for(let item of list){
            str += '<div style="display: flex;padding: 3px" ><div style="min-width: 100px;"><span>'+item.name+'</span>:</div><div><span>'+item.value+'</span></div></div>'
        }
        return  str;
    }

    function view(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '查看详情',
            type: 2,
            resize: false,
            area: ['100%', '100%'],
            content: '{{ url('work/work/view/') }}' + uid + '/1',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function print(uid){
        top.window.layer_result = '';
        top.layer.open({
            title: '打印',
            type: 2,
            resize: false,
            area: ['100%', '100%'],
            content: '{{ url('work/work/print/') }}' + uid ,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

    let bsOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsOption);
    $('.bs-select-read').selectpicker(bsOption);
</script>