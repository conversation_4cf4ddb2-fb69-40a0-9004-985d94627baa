{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>仓库编码</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>仓库名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" v-model="name" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">仓库管理员</label>
                                <div class="col-sm-8">
                                    <select class="bs-select form-control" name="manager_id" v-model="manager_id">
                                        <option value="">请选择管理员</option>
                                        {% for row in user_list %}
                                            <option value="{{ row['id'] }}">{{ row['real_name'] }}</option>
                                        {% endfor %}
                                        </select>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">仓库位置</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="location" v-model="location" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonPurchaseWarehouse }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('purchase/warehouse/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('purchase/warehouse/create') }}';
                {% endif %}

                let param = JSON.parse(JSON.stringify(this.$data));

                commonAjaxRequest(url, param, function (rs) {
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                })
            }
        },
        watch: {
            manager_id: function(val) {
                if (val == '') {
                    this.manager_name = '';
                } else {
                    const user = this.user_list.find(item => item.id == val);
                    if (user) {
                        console.log(user.real_name);
                        this.manager_name = user.real_name;
                    } else {
                        this.manager_name = '';
                    }
                }
            }
        }
    });
    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);
</script>
{{ partial('form_script') }}