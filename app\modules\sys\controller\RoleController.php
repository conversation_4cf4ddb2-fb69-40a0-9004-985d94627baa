<?php
namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Modules\Sys\Model\Role;
use Envsan\Modules\Sys\Service\RoleService;

/**
 * @name('岗位管理')
 */
class RoleController extends SuperController
{

    /**
     * @name('角色一览')
     */
    public function listAction($type='')
    {
        if($type=='json') {
            $this->setJsonResponse();
            $rs = new RoleService();
            $find = trim($this->request->get('name', 'string'));
            $rows = $rs->selectAll($find);
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
        $this->view->jsonPageList = json_encode(ConstantUtil::$home_page_list);
    }

    /**
     * @acl({'link':'sys:role:list'})
     */
    public function createAction()
    {
        if($this->request->isPost()){
            $this->setJsonResponse();
            $rs = new RoleService();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $jrow = (new Role())->toArray();
        $jrow['group_uid'] = '';
        $jrow['group_name'] = '';
        $jrow['home_page_id'] = '';
        $jrow['scope'] = '1';
        $this->view->jsonRole = json_encode($jrow);
        $this->view->pageList = ConstantUtil::$home_page_list;
    }

    /**
     * @acl({'link':'sys:role:list'})
     */
    function editAction($uid='')
    {
        $rs = new RoleService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($row->identity=='admin')
            die('无法对管理员角色进行该操作');

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $group = $row->group;
        $jrow = $row->toArray();
        $jrow['group_uid'] = $group?$group->uid:'';
        $jrow['group_name'] = $group?$group->name:'';

        $this->view->jsonRole = json_encode($jrow);
        $this->view->role = $row;
        $this->view->pageList = ConstantUtil::$home_page_list;
        $this->view->pick('role/create');
    }

    /**
     * @acl({'link':'sys:role:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new RoleService();
            $ret = new JsonData();
            $ret->message = $rs->deleteByUid($this->request->getPost('uid', 'string'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'sys:role:list'})
     */
    // 为角色分配权限
    public function assignAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();

            $uid = $this->request->getPost('uid', 'string');
            $ids = $this->request->getPost('ids', 'string');

            $ret = new JsonData();
            $rs = new RoleService();
            $ret->message = $rs->assign($uid, $ids);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}