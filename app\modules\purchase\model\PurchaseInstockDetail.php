<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseInstockDetail extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $instock_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $receipt_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_spec;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_model;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_deputy_unit;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=true)
     */
    public $check_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_images;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=true)
     */
    public $check_status;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=true)
     */
    public $check_result_flag;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $check_val;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $check_data;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=true)
     */
    public $check_user_id;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $check_user_name;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $check_time;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $check_remarks;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $quality_template_id;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $price;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $price_hs;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $purchase_quantity;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $total_money;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $tax_rate;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $total_money_hs;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_instock_detail';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseInstockDetail[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseInstockDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
