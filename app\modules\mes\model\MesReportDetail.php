<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesReportDetail extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $report_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $report_date;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $report_month;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $staff_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $staff_name;

    /**
     *
     * @var double
     * @Column(type="double", nullable=true)
     */
    public $cost;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $day_money;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $day_hour;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $other_day_hour;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $jj_money;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $other_money;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $day_bz_money;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $sum_money;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $produce_list;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $other_list;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_report_detail';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesReportDetail[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesReportDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
