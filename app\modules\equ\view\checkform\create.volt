{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    模板名称<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="name" class="form-control" v-model="name" required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    类型<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <select  name="type" class="form-control bs-select" v-model="type" required>
                                        <option value="">请选择</option>
                                        {% for key,value in types %}
                                            <option value="{{ key }}">{{ value }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    文件番号
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" name="code" class="form-control" v-model="code"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    备注
                                </label>
                                <div class="col-sm-8">
                                    <textarea class="form-control" name="remarks" v-model="remarks" maxlength="200" style="resize: none;"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonEquCheckForm }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName()=='edit' %}
                var url= '{{ url('equ/checkform/edit/'~form.uid) }}';
                {% else %}
                var url= '{{ url('equ/checkform/create') }}';
                {% endif %}

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });
    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>
