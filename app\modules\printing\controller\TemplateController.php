<?php
namespace Envsan\Modules\Printing\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Printing\Model\PrintingTemplate;
use Envsan\Modules\Printing\Service\TemplateService;
use Envsan\Modules\Printing\Util\Constant;

/**
 * @name('打印模版')
 */
class TemplateController extends SuperController
{

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new TemplateService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'printing:template:list'})
     */
    function createAction()
    {
        $rs = new TemplateService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new PrintingTemplate())->toArray();
        $jrow['page_id'] = '';
        $jrow['default_flag'] = 0;
        $jrow['status'] = 0;
        $this->view->page_list = $rs->getPageList();
        $this->view->jsonForm = json_encode($jrow);
    }

    /**
     * @acl({'link':'printing:template:list'})
     */
    function editAction($uid)
    {
        $rs = new TemplateService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        $this->view->uid = $uid;
        $this->view->jsonForm = json_encode($jrow);
        $this->view->pick('template/create');
    }

    /**
     * @acl({'link':'printing:template:list'})
     */
    public function deleteAction(){
        $s = new TemplateService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}