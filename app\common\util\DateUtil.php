<?php
namespace Envsan\Common\Util;

class DateUtil
{
    public static function now()
    {
        return date('Y-m-d H:i:s');
    }

    public static function time()
    {
        return date('H:i:s');
    }

    public static function today()
    {
        return date('Y-m-d');
    }

    public static function yesterday()
    {
        return date('Y-m-d', strtotime("-1 days"));
    }

    public static function tomorrow()
    {
        return date('Y-m-d', strtotime("+1 days"));
    }

    public static function ymd()
    {
        return date('Ymd');
    }

    public static function nowYmdHi()
    {
        return date('Y-m-d H:i');
    }

    public static function getDiffSeconds($date_start, $date_end)
    {
        return round(strtotime($date_end) - strtotime($date_start));
    }

    public static function getDiffHours($date_start, $date_end)
    {
        $seconds = strtotime($date_end) - strtotime($date_start);
        return round($seconds / (60 * 60), 2);
    }

    public static function formatTime($time)
    {
        $hour = floor($time / (60 * 60));
        $minute = floor(($time - $hour * 3600) / 60);
        $second = $time - $hour * 3600 - $minute * 60;

        $str = '';
        if ($hour > 0) {
            $str .= $hour.'时';
        }

        if ($minute > 0) {
            $str .= $minute.'分';
        }

        return $str.$second.'秒';
    }
}
