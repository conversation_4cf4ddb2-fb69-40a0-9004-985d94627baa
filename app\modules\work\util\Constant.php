<?php
namespace Envsan\Modules\Work\Util;

class Constant
{
    public static $review_field_template = ['id'=>'','type'=>'1','name'=>'', 'value' => '','unit'=>''];

    public static $input_types = [
        1 => '文本',
        2 => '数字',
        3 => '单选',
        4 => '多选',
        5 => '下拉列表',
        6 => '日期',
        7 => '时间',
        8 => '多行文本',
        9 => '月份',
        99 => '明细'
    ];

    public static $service_input_types = [
        ['id' => 'weight','type' => 2,'name' =>'结算重量','unit'=>'吨','list'=>[]],
        ['id' => 'cprice','type' => 2,'name' =>'结算单价','unit'=>'元/吨','list'=>[]],
        ['id' => 'total_price','type' => 2,'name' =>'结算金额','unit'=>'元','list'=>[]],
        ['id' => 'customer','type' => 5,'name' =>'客户','unit'=>'','list'=>[]]
    ];

    public static $form_data_template = [
        't' => 1,
        'id' => '',
        'type' => '',
        'name' => '',
        'value' => '',
        'values' => [],
        'title' => '',
        'required' => '0',
        'is_sum' => '0',
        'max' => '',
        'unit' => '',
        'explain' => '',
        'list' => []
    ];

    public static $review_type = [
        1 => '会签',
        2 => '或签',
        3 => '终签'
    ];

    //数据状态
    public static $data_status = [
        10 => '提交请求',
        15 => '待审批',
        20 => '完成'
    ];

    //status 1 通过 2 拒绝 3 终止流程
    public static $data_template = [
        'id' => '',
        'di' => '',
        'dt' => '',
        'name' => '',
        'type' => 1,
        'val' => '',
        'status' => 0,
        'time' => '',
        'text' => '',
        'files' => [],
        'send' => []
    ];

    public static  $extend_column = [
        'page_name' => '审批报表',
        'header_data' => [
            ['base'=> 0,'type' => 1, 'id' => 'id', 'name' => 'id', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 0,'type' => 1, 'id' => 'uid', 'name' => 'uid', 'unit' => '', 'show' => 0, 'condition' => ''],
            ['base'=> 1,'type' => 1, 'id' => 'code', 'name' => '单号', 'unit' => '', 'show' => 1, 'condition' => 't2.code'],
            ['base'=> 1,'type' => 1, 'id' => 'create_name', 'name' => '提交人', 'unit' => '', 'show' => 1, 'condition' => 't4.real_name'],
            ['base'=> 1,'type' => 1, 'id' => 'create_date', 'name' => '提交时间', 'unit' => '', 'show' => 1, 'condition' => 't2.create_date'],
            ['base'=> 1,'type' => 99, 'id' => 'json_data', 'name' => '', 'page_id' => 1, 'show' => 1, 'condition' => 't1.json_data'],
        ]
    ];
}