<?php
namespace Envsan\Modules\Quality\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Quality\Model\QualityTemplate;
use Phalcon\Mvc\User\Component;

class TemplateService extends Component
{
    public function selectAll()
    {
        $name = $this->request->get('name', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.type,
                t1.name,
                t1.remarks
            ')
            ->addFrom('Envsan\Modules\Quality\Model\QualityTemplate','t1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("t1.name like ?1", [1 => "%$name%"]);
        }
        return $builder;
    }

    public function getTempList(){
        return  QualityTemplate::find('del_flag = 0 ');
    }

    public function create()
    {
        $row = new QualityTemplate();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = $this->request->getPost('name', 'tstring');
        $type = $this->request->getPost('type', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $form_data = str_replace('%2B', '+', urldecode($this->request->getPost('form_data', 'tstring')));
        if (empty($name) || empty($type)){
            return ErrorHelper::WRONG_INPUT;
        }
        $form_data = json_decode($form_data,true);
        $user = SessionData::user();
        $row->name = $name;
        $row->type = $type;
        $row->remarks =CvtUtil::blankToNull($remarks);
        $row->form_data = json_encode($form_data,JSON_UNESCAPED_UNICODE);
        $row->update_date = DateUtil::now();
        $row->owner = $user->owner;
        $row->update_by = $user->id;
        if ($act == 'create'){
            $row->uid = UUID::make();
            $row->del_flag = 0;
            $row->owner = $user->owner;
        }
        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return QualityTemplate::findFirst(['del_flag = 0 and id=?1', 'bind'=>[1=>$id]]);
    }

    public function selectByUid($uid)
    {
        return QualityTemplate::findFirst(['del_flag = 0 and uid = ?1', 'bind'=>[1=>$uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = QualityTemplate::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
        if ($row != null){
            $user = SessionData::user();
            $now = DateUtil::now();
            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save())
                return ErrorHelper::UNKOWN;
        }
        return '';
    }
}