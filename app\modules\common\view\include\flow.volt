<script>
    const left_middle = 'LeftMiddle';
    const right_middle = 'RightMiddle';
    function flowInit(anchor,connects,callback) {
        jsPlumb.ready(function () {
            var instance = window.jsp = jsPlumb.getInstance({
                DragOptions: { cursor: 'pointer', zIndex: 2000 },
                ConnectionOverlays: [
                    [ "Arrow", {
                        location: 1,
                        visible:true,
                        width:11,
                        length:11,
                        id:"ARROW",
                        events:{
                            click:function() { alert("you clicked on the arrow overlay")}
                        }
                    } ]
                ],
                Container: "canvas"
            });

            // suspend drawing and initialise.
            instance.batch(function () {
                for (var i = 0; i < anchor.length; i++){
                     _initEndpoints(instance,anchor[i]);
                }
                instance.bind("connection", function (connInfo, originalEvent) {
                    init(connInfo.connection);
                });
                instance.draggable(jsPlumb.getSelector(".flowchart-demo .window"), { grid: [20, 20] });
                for (var key in connects){
                    instance.connect({uuids: [key, connects[key]]});
                }
            });
            jsPlumb.fire("jsPlumbDemoLoaded", instance);
            callback(instance);
        });
    }
    var connectorPaintStyle = {
            strokeWidth: 2,
            stroke: "#61B7CF",
            joinstyle: "round",
            outlineStroke: "white",
            outlineWidth: 2
        },
        // .. and this is the hover style.
        connectorHoverStyle = {
            strokeWidth: 3,
            stroke: "#216477",
            outlineWidth: 5,
            outlineStroke: "white"
        },
        endpointHoverStyle = {
            fill: "#216477",
            stroke: "#216477"
        },
        sourceEndpoint = {
            endpoint: "Dot",
            paintStyle: {
                stroke: "#7AB02C",
                fill: "transparent",
                radius: 7,
                strokeWidth: 1
            },
            isSource: true,
            connector: [ "Flowchart", { stub: [40, 60], gap: 10, cornerRadius: 5, alwaysRespectStubs: true } ],
            connectorStyle: connectorPaintStyle,
            hoverPaintStyle: endpointHoverStyle,
            connectorHoverStyle: connectorHoverStyle,
            isTarget: true,
            dragOptions: {},
            overlays: []
        },
        init = function (connection) {
            //connection.getOverlay("label").setLabel(connection.sourceId.substring(15) + "-" + connection.targetId.substring(15));
        };

    function _initEndpoints (instance,item) {
        var connect_list = [];
        var toId = item.id;
        var source_left_id = item.id + left_middle;
        var source_right_id = item.id + right_middle;
        var target_left_id = '';
        var target_right_id = '';
        instance.addEndpoint(toId, sourceEndpoint, {
            anchor: left_middle, uuid: source_left_id
        });
        instance.addEndpoint(toId, sourceEndpoint, {
            anchor: right_middle, uuid: source_right_id
        });
    }

    function _addEndpoints (instance,item) {
        var toId = item.id;
        var source_left_id = item.id + left_middle;
        var source_right_id = item.id + right_middle;
        instance.addEndpoint(toId, sourceEndpoint, {
            anchor: left_middle, uuid: source_left_id
        });
        instance.addEndpoint(toId, sourceEndpoint, {
            anchor: right_middle, uuid: source_right_id
        });
        instance.draggable(jsPlumb.getSelector(".flowchart-demo .window"), { grid: [20, 20] });
    }
    
    function _delEndpoints(id) {
        instance.remove(id);
    }

</script>