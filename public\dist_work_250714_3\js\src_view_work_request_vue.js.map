{"version": 3, "file": "js/src_view_work_request_vue.js", "mappings": ";;;;;;;;;;;AA2BA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;ACzDA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACwEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AClOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACn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iBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AGAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AGAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AEAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AEAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACxCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AIAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://rrts-manager/src/components/dest_field.vue", "webpack://rrts-manager/src/components/detail_field.vue", "webpack://rrts-manager/src/components/dynamic_field.vue", "webpack://rrts-manager/src/components/edit_goods.vue", "webpack://rrts-manager/src/components/plate.vue", "webpack://rrts-manager/src/view/work/request.vue", "webpack://rrts-manager/./src/components/dest_field.vue", "webpack://rrts-manager/./src/components/detail_field.vue", "webpack://rrts-manager/./src/components/dynamic_field.vue", "webpack://rrts-manager/./src/components/edit_goods.vue", "webpack://rrts-manager/./src/components/plate.vue", "webpack://rrts-manager/./src/view/work/request.vue", "webpack://rrts-manager/./src/components/dest_field.vue?81b4", "webpack://rrts-manager/./src/components/detail_field.vue?102e", "webpack://rrts-manager/./src/components/plate.vue?645d", "webpack://rrts-manager/./src/components/plate.vue?3e1f", "webpack://rrts-manager/./src/view/work/request.vue?0d6b", "webpack://rrts-manager/./src/resource/css/iconfont/iconfont.css", "webpack://rrts-manager/./src/components/dest_field.vue?08e5", "webpack://rrts-manager/./src/components/detail_field.vue?7a1d", "webpack://rrts-manager/./src/components/plate.vue?4954", "webpack://rrts-manager/./src/components/plate.vue?5ad2", "webpack://rrts-manager/./src/view/work/request.vue?7b79", "webpack://rrts-manager/./src/components/dest_field.vue?f64a", "webpack://rrts-manager/./src/components/dest_field.vue?6d92", "webpack://rrts-manager/./src/components/dest_field.vue?8247", "webpack://rrts-manager/./src/components/dest_field.vue?3087", "webpack://rrts-manager/./src/components/detail_field.vue?8f02", "webpack://rrts-manager/./src/components/detail_field.vue?f7c5", "webpack://rrts-manager/./src/components/detail_field.vue?1378", "webpack://rrts-manager/./src/components/detail_field.vue?880a", "webpack://rrts-manager/./src/components/dynamic_field.vue?e976", "webpack://rrts-manager/./src/components/dynamic_field.vue?0f6d", "webpack://rrts-manager/./src/components/dynamic_field.vue?872a", "webpack://rrts-manager/./src/components/edit_goods.vue?6431", "webpack://rrts-manager/./src/components/edit_goods.vue?1be8", "webpack://rrts-manager/./src/components/edit_goods.vue?43dc", "webpack://rrts-manager/./src/components/plate.vue?0516", "webpack://rrts-manager/./src/components/plate.vue?548d", "webpack://rrts-manager/./src/components/plate.vue?9792", "webpack://rrts-manager/./src/components/plate.vue?54f3", "webpack://rrts-manager/./src/components/plate.vue?69f0", "webpack://rrts-manager/./src/resource/css/iconfont/iconfont.css?6013", "webpack://rrts-manager/./src/view/work/request.vue?773c", "webpack://rrts-manager/./src/view/work/request.vue?3206", "webpack://rrts-manager/./src/view/work/request.vue?b0aa", "webpack://rrts-manager/./src/view/work/request.vue?d474"], "sourcesContent": ["<template>\r\n    <div>\r\n        <van-popup v-model=\"onshow\" position=\"right\"  class=\"main\">\r\n            <van-button style=\"position: absolute;background-color: #0081FF;margin-top: 8px\" color=\"#0081FF\" icon=\"cross\" @click=\"closeAdd\"> </van-button>\r\n            <div class=\"header\">添加明细 </div>\r\n            <van-search\r\n                v-model=\"name\"\r\n                show-action\r\n                label=\"名称\"\r\n                placeholder=\"请输入搜索关键词\"\r\n                input-align=\"center\"\r\n            >\r\n              <template #action>\r\n                <van-button @click=\"onSave\" plain type=\"default\" size=\"small\"\r\n                            style=\"width: 60px\">新增\r\n                </van-button>\r\n              </template>\r\n            </van-search>\r\n            <van-cell v-for=\"(item, idx) in list\" :title=\"item.text\" v-if=\"item.is_show == 1\">\r\n              <template #right-icon>\r\n                <van-button type=\"info\" plain round size=\"small\" @click=\"saveAdd(item)\">确认</van-button>\r\n              </template>\r\n            </van-cell>\r\n        </van-popup>\r\n    </div>\r\n</template>\r\n<script>\r\n    import {Dialog} from \"vant\";\r\n\r\n    export default {\r\n        name: \"dest-field\",\r\n        props: {\r\n            onshow: Boolean\r\n        },\r\n        data() {\r\n            return {\r\n                name:'',\r\n                list:[]\r\n            }\r\n        },\r\n        created() {\r\n            this.name = '';\r\n            this.getList();\r\n        },\r\n        methods:{\r\n            saveAdd(item){\r\n                this.$emit('changeDest', item)\r\n            },\r\n            closeAdd(){\r\n                this.$emit('changeDest', null)\r\n            },\r\n            getList() {\r\n                let me = this;\r\n                this.$http.post('work/dest/dest',{name:this.name}).then((rs) => {\r\n                    me.list = rs;\r\n                }).catch(() => {\r\n                    this.$router.replace({name: 'error'});\r\n                });\r\n            },\r\n            onSave(){\r\n                Dialog.confirm({\r\n                  title: '提交',\r\n                  message: '确定要提交吗？',\r\n                })\r\n                .then(() => {\r\n                    this.$http.post('work/dest/adddest',{name:this.name}).then((rs) => {\r\n                        if (rs.status == 'ok'){\r\n                            this.$toast.success('提交成功！');\r\n                            this.saveAdd(rs.data)\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                        }\r\n                    }).catch((e) => {\r\n                        this.$toast.fail('提交失败！');\r\n                    });\r\n                }).catch(() => {});\r\n            },\r\n        },\r\n        watch: {\r\n            name(val) {\r\n                for(let i = 0; i < this.list.length; i++){\r\n                    if(this.list[i].text.indexOf(val) !== -1){\r\n                      this.list[i].is_show = 1\r\n                    }else {\r\n                      this.list[i].is_show = 0\r\n                    }\r\n                }\r\n            },\r\n            onshow(val) {\r\n                if (val){\r\n                    this.name = '';\r\n                    this.getList();\r\n                }\r\n            }\r\n        },\r\n    }\r\n</script>\r\n<style scoped>\r\n    .main {\r\n        background-color: #FAFAFA;\r\n        width: 100vw;\r\n        height: 100vh;\r\n    }\r\n    .header {\r\n        text-align: center;\r\n        height: 54px;\r\n        line-height: 54px;\r\n        background-color: #0081FF;\r\n        color: #FFFFFF;\r\n    }\r\n    .content{\r\n        min-height: 100px;\r\n        background-color: #FFFFFF;\r\n        margin: 15px;\r\n        padding: 15px;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 10px;\r\n    }\r\n</style>", "<template>\r\n    <div>\r\n        <van-cell :title=\"title\" icon=\"star\" :required=\"required == '1' ? true:false\">\r\n            <template #right-icon>\r\n                <van-button type=\"info\" plain round size=\"small\" @click=\"showAdd\">添加</van-button>\r\n            </template>\r\n            <template #label>\r\n                <span style=\"color: red\" v-text=\"explain\"></span>\r\n            </template>\r\n        </van-cell>\r\n        <van-cell v-for=\"(item,idx) in sum_list\" :title=\"item.title\" :value=\"item.value + '('+item.unit+')'\"/>\r\n        <div v-for=\"(list, index) in valueList\" :key=\"index\" class=\"content\" style=\"padding: 0;overflow: hidden\">\r\n            <van-cell-group>\r\n                <template v-for=\"(item, idx) in list\">\r\n                    <van-cell v-if=\"item.type == 4\" :title=\"item.title\">\r\n                        <template #extra>\r\n                            <span style=\"margin-left: 5px\" v-for=\"(d,i) in item.values\" v-text=\"d\"></span>\r\n                        </template>\r\n                    </van-cell>\r\n                    <van-cell v-else :title=\"item.title\" :value=\"item.value + item.unit\" />\r\n                </template>\r\n            </van-cell-group>\r\n            <div style=\"padding: 10px;display: flex;flex-direction: row;justify-content: flex-end\">\r\n                <van-button type=\"danger\" plain round size=\"small\" @click=\"delData(index)\" style=\"margin-left: 10px\">删除</van-button>\r\n                <van-button type=\"primary\" plain round size=\"small\" @click=\"editData(index)\" style=\"margin-left: 10px\">编辑</van-button>\r\n            </div>\r\n        </div>\r\n        <van-popup v-model=\"show\" position=\"right\" closeable close-icon-position=\"top-left\" class=\"main\" @click-close-icon=\"closeAdd\">\r\n            <div class=\"header\">添加明细</div>\r\n            <dynamic-field v-for=\"(item, idx) in form_list\" :data=\"item\" @change=\"changeValue\"></dynamic-field>\r\n            <div class=\"footer\" style=\"background-color: #FFFFFF;padding: 10px;text-align: center;position: absolute;left: 0;bottom:0;width: 100%\">\r\n                <van-button class=\"van-button\" plain round type=\"info\" @click=\"closeAdd\">返回</van-button>\r\n                <van-button class=\"van-button\" round type=\"info\" @click=\"addSave\" style=\"margin-left: 30px\">添加</van-button>\r\n            </div>\r\n        </van-popup>\r\n    </div>\r\n</template>\r\n<script>\r\n    import dynamicField from './dynamic_field';\r\n    export default {\r\n        name: \"detail-field\",\r\n        components: {dynamicField},\r\n        props: {\r\n            title: String,\r\n            required: String,\r\n            explain : String,\r\n            formList : Array,\r\n            valueList : Array\r\n        },\r\n        data() {\r\n            return {\r\n                form_list:[],\r\n                sum_list:[],\r\n                show:false,\r\n                edit_idx : null\r\n            }\r\n        },\r\n        created() {\r\n            this.form_list = this.formList;\r\n        },\r\n        methods:{\r\n            showAdd(){\r\n                this.edit_idx = null;\r\n                this.show = true;\r\n            },\r\n            closeAdd(){\r\n                this.show = false\r\n            },\r\n            addSave(){\r\n                for(let item of this.form_list){\r\n                    if (item.required == 1){\r\n                        if (item.type == 4){\r\n                            if (item.values.length == 0){\r\n                                this.$toast.fail('请选择'+item.title);\r\n                                return;\r\n                            }\r\n                        } else if (item.type == 2){\r\n                            if (item.value == '' || parseFloat(item.value) == 0){\r\n                                this.$toast.fail('请输入'+item.title);\r\n                                return;\r\n                            }\r\n                        } else {\r\n                            if (item.value == ''){\r\n                              this.$toast.fail('请输入'+item.title);\r\n                              return;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (this.edit_idx == null){\r\n                    this.valueList.push(this.cloneObj(this.form_list));\r\n                } else {\r\n                    this.valueList[this.edit_idx] = this.cloneObj(this.form_list);\r\n                }\r\n                this.sumData();\r\n                this.clearForm();\r\n                this.closeAdd();\r\n            },\r\n            editData(idx){\r\n                this.edit_idx = idx;\r\n                this.show = true;\r\n                this.form_list = this.cloneObj(this.valueList[idx]);\r\n            },\r\n            delData(idx){\r\n                this.valueList.splice(idx,1);\r\n                this.sumData();\r\n            },\r\n            cloneObj(obj){\r\n                return JSON.parse(JSON.stringify(obj))\r\n            },\r\n            clearForm(){\r\n                for(let item of this.form_list){\r\n                    item.value = '';\r\n                    item.values = [];\r\n                }\r\n            },\r\n            sumData(){\r\n                let data = {};\r\n                for(let value of this.valueList){\r\n                    for (let item of value){\r\n                        if (item.type == 2 && item.is_sum == 1){\r\n                            if (item.value != ''){\r\n                                if (data[item.id]){\r\n                                    data[item.id].value += parseFloat(item.value);\r\n                                } else {\r\n                                    data[item.id] = {title:item.title+'(合计)',value:parseFloat(item.value),unit:item.unit}\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                let list = [];\r\n                for(let key in data){\r\n                    list.push(data[key]);\r\n                }\r\n                this.sum_list = list;\r\n            },\r\n            changeValue(item){\r\n                if (item.type == 10){\r\n                    let data = this.form_list.find(object => object.type === 11);\r\n                    if (data){\r\n                        data.value = item.price;\r\n                    }\r\n                } else if (item.type == 12){\r\n                    let data = this.form_data.find(object => object.id === 'bank_name');\r\n                    if (data){\r\n                        data.value = item.bank_name;\r\n                    }\r\n                    data = this.form_data.find(object => object.id === 'bank_no');\r\n                    if (data){\r\n                        data.value = item.bank_no;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>\r\n<style scoped>\r\n    .main {\r\n        background-color: #FAFAFA;\r\n        width: 100vw;\r\n        height: 100vh;\r\n        overflow: hidden;\r\n    }\r\n    .header {\r\n        text-align: center;\r\n        height: 54px;\r\n        line-height: 54px;\r\n        background-color: #0081FF;\r\n        color: #FFFFFF;\r\n    }\r\n    .content{\r\n        min-height: 100px;\r\n        background-color: #FFFFFF;\r\n        margin: 15px;\r\n        padding: 15px;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 10px;\r\n    }\r\n</style>", "<template>\r\n    <div>\r\n        <van-field v-if=\"data.type == 1\" :maxlength=\"data.max\" v-model=\"data.value\" :label=\"data.title\" :placeholder=\"'请输入'+data.title\" :required=\"data.required == 1 ? true:false\"  :error-message=\"data.explain\"/>\r\n        <van-field\r\n                v-if=\"data.type == 2 || data.type == 13\"\r\n                required\r\n                @click-input=\"number_show = true\"\r\n                type=\"number\"\r\n                :label=\"data.title\"\r\n                :is-link=\"true\"\r\n                v-model=\"data.value\"\r\n                :placeholder=\"'请输入'+data.title\"\r\n                :required=\"data.required == 1 ? true:false\"\r\n                :maxlength=\"data.max\"\r\n                :error-message=\"data.explain\"\r\n                readonly\r\n        >\r\n            <template #button>\r\n                <span v-if=\"data.unit != ''\" style=\"color: #000\" v-text=\"'（'+data.unit+'）'\"></span>\r\n            </template>\r\n        </van-field>\r\n        <van-field  v-if=\"data.type == 3\" :label=\"data.title\" :required=\"data.required == 1 ? true:false\" :error-message=\"data.explain\">\r\n            <template #input>\r\n                <van-radio-group v-model=\"data.value\" direction=\"horizontal\">\r\n                    <van-radio v-for=\"(item,idx) of data.list\" :name=\"item\">{{item}}</van-radio>\r\n                </van-radio-group>\r\n            </template>\r\n        </van-field>\r\n        <van-field v-if=\"data.type == 4\" :label=\"data.title\" :required=\"data.required == 1 ? true:false\"  :error-message=\"data.explain\">\r\n            <template #input>\r\n                <van-checkbox-group v-model=\"data.values\" direction=\"horizontal\">\r\n                    <van-checkbox o v-for=\"(item,idx) of data.list\"  :name=\"item\" shape=\"square\" style=\"margin-bottom: 2px\">{{item}}</van-checkbox>\r\n                </van-checkbox-group>\r\n            </template>\r\n        </van-field>\r\n        <van-field\r\n                v-if=\"data.type == 5 \"\r\n                @click-input=\"select_show = true\"\r\n                :required=\"data.required == 1 ? true:false\"\r\n                :label=\"data.title\"\r\n                :is-link=\"true\"\r\n                v-model=\"data.value\"\r\n                :placeholder=\"'选择' + data.title\"\r\n                :error-message=\"data.explain\"\r\n                readonly\r\n        />\r\n        <van-field\r\n                v-if=\"data.type == 6\"\r\n                :required=\"data.required == 1 ? true:false\"\r\n                @click-input=\"date_show = true\"\r\n                :label=\"data.title\"\r\n                :is-link=\"true\"\r\n                v-model=\"data.value\"\r\n                :placeholder=\"'选择' + data.title\"\r\n                :error-message=\"data.explain\"\r\n                readonly\r\n        />\r\n        <van-field\r\n                v-if=\"data.type == 7\"\r\n                :required=\"data.required == 1 ? true:false\"\r\n                @click-input=\"datetime_show = true\"\r\n                :label=\"data.title\"\r\n                :is-link=\"true\"\r\n                v-model=\"data.value\"\r\n                :placeholder=\"'选择' + data.title\"\r\n                :error-message=\"data.explain\"\r\n                readonly\r\n        />\r\n        <van-field\r\n                v-if=\"data.type == 8\"\r\n                :is-link=\"true\"\r\n                v-model=\"data.value\"\r\n                rows=\"2\"\r\n                autosize\r\n                type=\"textarea\"\r\n                :label=\"data.title\"\r\n                :placeholder=\"'请输入' + data.title\"\r\n                :maxlength=\"data.max\"\r\n                :error-message=\"data.explain\"\r\n        />\r\n        <van-field\r\n            v-if=\"data.type == 9\"\r\n            :required=\"data.required == 1 ? true:false\"\r\n            @click-input=\"date_month_show = true\"\r\n            :label=\"data.title\"\r\n            :is-link=\"true\"\r\n            v-model=\"data.value\"\r\n            :placeholder=\"'选择' + data.title\"\r\n            :error-message=\"data.explain\"\r\n            readonly\r\n        />\r\n        <van-field\r\n                v-if=\"data.type == 10\"\r\n                @click-input=\"select_goods = true\"\r\n                :required=\"data.required == 1 ? true:false\"\r\n                :label=\"data.title\"\r\n                :is-link=\"true\"\r\n                 v-model=\"data.value\"\r\n                :placeholder=\"'选择' + data.title\"\r\n                :error-message=\"data.explain\"\r\n                readonly\r\n        />\r\n        <van-field\r\n                v-if=\"data.type == 11\"\r\n                :label=\"data.title\"\r\n                v-model=\"data.value\"\r\n                readonly\r\n        >\r\n            <template #button>\r\n                <span v-if=\"data.unit != ''\" style=\"color: #000\" v-text=\"'（'+data.unit+'）'\"></span>\r\n            </template>\r\n        </van-field>\r\n        <van-popup\r\n            v-model=\"select_goods\"\r\n            position=\"bottom\"\r\n            style=\"height: 402px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n          <van-picker\r\n              :title=\"'选择' + data.title\"\r\n              show-toolbar\r\n              :columns=\"data.list\"\r\n              @cancel= \"select_goods = false\"\r\n              @confirm=\"selectGoods\"\r\n          >\r\n              <template #columns-bottom>\r\n                  <div style=\"padding-top: 10px\">\r\n                      <van-button round block type=\"default\" @click=\"selectGoods(null)\">取消选择</van-button>\r\n                  </div>\r\n              </template>\r\n          </van-picker>\r\n        </van-popup>\r\n        <template v-if=\"data.type == 12\">\r\n          <van-field\r\n              :required=\"data.required == 1 ? true:false\"\r\n              @click-input=\"dest_show = true\"\r\n              :label=\"data.title\"\r\n              :is-link=\"true\"\r\n              v-model=\"data.value\"\r\n              :placeholder=\"'选择' + data.title\"\r\n              :error-message=\"data.explain\"\r\n              readonly />\r\n          <dest-field :onshow=\"dest_show\" @changeDest=\"changeDest\"></dest-field>\r\n        </template>\r\n        <van-popup v-model=\"datetime_show\" position=\"bottom\" style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n            <van-datetime-picker\r\n                    type=\"datetime\"\r\n                    :title=\"'选择' + data.title\"\r\n                    @cancel= \"datetime_show = false\"\r\n                    @confirm=\"datetimeChange\"\r\n                    :value=\"date_date\"\r\n                    :min-date=\"min_date\"\r\n                    :max-date=\"max_date\"\r\n                    :formatter=\"formatter\"\r\n            >\r\n                <template #columns-bottom>\r\n                    <div style=\"padding-top: 10px\">\r\n                        <van-button round block type=\"default\" @click=\"dateChange(null)\">取消选择</van-button>\r\n                    </div>\r\n                </template>\r\n            </van-datetime-picker>\r\n        </van-popup>\r\n        <van-popup v-model=\"date_show\" position=\"bottom\" style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n            <van-datetime-picker\r\n                    type=\"date\"\r\n                    :title=\"'选择' + data.title\"\r\n                    @cancel= \"date_show = false\"\r\n                    @confirm=\"dateChange\"\r\n                    :value=\"date_date\"\r\n                    :min-date=\"min_date\"\r\n                    :max-date=\"max_date\"\r\n                    :formatter=\"formatter\"\r\n            >\r\n                <template #columns-bottom>\r\n                    <div style=\"padding-top: 10px\">\r\n                        <van-button round block type=\"default\" @click=\"dateChange(null)\">取消选择</van-button>\r\n                    </div>\r\n                </template>\r\n            </van-datetime-picker>\r\n        </van-popup>\r\n        <van-popup v-model=\"date_month_show\" position=\"bottom\" style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n          <van-datetime-picker\r\n              type=\"year-month\"\r\n              :title=\"'选择' + data.title\"\r\n              @cancel= \"date_month_show = false\"\r\n              @confirm=\"datemonthChange\"\r\n              :value=\"date_date\"\r\n              :min-date=\"min_date\"\r\n              :max-date=\"max_date\"\r\n              :formatter=\"formatter\"\r\n          >\r\n            <template #columns-bottom>\r\n              <div style=\"padding-top: 10px\">\r\n                <van-button round block type=\"default\" @click=\"datemonthChange(null)\">取消选择</van-button>\r\n              </div>\r\n            </template>\r\n          </van-datetime-picker>\r\n        </van-popup>\r\n        <van-popup\r\n                v-model=\"select_show\"\r\n                position=\"bottom\"\r\n                style=\"height: 402px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n            <van-picker\r\n                    :title=\"'选择' + data.title\"\r\n                    show-toolbar\r\n                    :columns=\"data.list\"\r\n                    @cancel= \"select_show = false\"\r\n                    @confirm=\"selectValue\"\r\n            >\r\n                <template #columns-bottom>\r\n                    <div style=\"padding-top: 10px\">\r\n                        <van-button round block type=\"default\" @click=\"selectValue('')\">取消选择</van-button>\r\n                    </div>\r\n                </template>\r\n            </van-picker>\r\n        </van-popup>\r\n        <van-number-keyboard\r\n                :show=\"number_show\"\r\n                theme=\"custom\"\r\n                extra-key=\".\"\r\n                z-index=\"1050\"\r\n                close-button-text=\"完成\"\r\n                @blur=\"number_show = false\"\r\n                @input=\"onNumberInput\"\r\n                @delete=\"onNumberDelete\"\r\n        />\r\n    </div>\r\n</template>\r\n<script>\r\n    import destField from './dest_field.vue';\r\n    export default {\r\n        name: \"dynamic-field\",\r\n        components: {destField},\r\n        props: {\r\n            data : Object\r\n        },\r\n        data() {\r\n            return {\r\n                min_date: new Date(2022,0,1),\r\n                max_date: new Date(2099, 1, 1),\r\n                number_show:false,\r\n                select_show:false,\r\n                select_goods:false,\r\n                date_show:false,\r\n                dest_show:false,\r\n                date_month_show:false,\r\n                datetime_show:false\r\n            }\r\n        },\r\n        methods:{\r\n            changeDest(item){\r\n                if (item != null){\r\n                    this.data.value = item.text;\r\n                    this.data.values = item.id;\r\n                    this.$emit('change', {...this.data,bank_name:item.bank_name,bank_no:item.bank_no});\r\n                }\r\n                this.dest_show = false;\r\n            },\r\n            onNumberInput(v){\r\n                if (this.data.value.length >= this.data.max){\r\n                    return;\r\n                }\r\n                this.data.value = this.data.value + '' + v;\r\n            },\r\n            onNumberDelete(v){\r\n                if (this.data.value == ''){\r\n                    return;\r\n                }\r\n                this.data.value = this.data.value.substring(0,this.data.value.length-1);\r\n            },\r\n            selectValue(val){\r\n                if (this.data.value == val){\r\n                    this.select_show = false;\r\n                    return;\r\n                }\r\n                this.data.value = val;\r\n                this.select_show = false;\r\n            },\r\n            getGoodsObj(arr){\r\n                if (arr == null){\r\n                    return null;\r\n                }\r\n                for(let it of this.data.list){\r\n                    if (arr[0] == it.text){\r\n                        for(let c of it.children){\r\n                            if (arr[1] == c.text){\r\n                                return c;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                return null\r\n            },\r\n            selectGoods(arr){\r\n              let obj = this.getGoodsObj(arr);\r\n              if (obj == null){\r\n                  this.select_goods = false;\r\n                  this.data.value = '';\r\n                  this.$emit('change', {...this.data,price:''});\r\n                  return;\r\n              }\r\n              if (this.data.value == obj.text){\r\n                    this.select_goods = false;\r\n                    return;\r\n              }\r\n              this.data.value = obj.text;\r\n              this.$emit('change', {...this.data,price:obj.price});\r\n              this.select_goods = false;\r\n            },\r\n            dateChange(value){\r\n                this.data.value = this.$cjs.formatDate(value);\r\n                this.date_show = false;\r\n            },\r\n            datetimeChange(value){\r\n                this.data.value = this.$cjs.formatDateTime(value);\r\n                this.datetime_show = false;\r\n            },\r\n            datemonthChange(value){\r\n                this.data.value = this.$cjs.formatMonth(value);\r\n                this.date_month_show = false;\r\n            },\r\n            formatter(type, val) {\r\n                if (type === 'year') {\r\n                    return val + '年';\r\n                }\r\n                if (type === 'month') {\r\n                    return val + '月';\r\n                }\r\n                if (type === 'day') {\r\n                    return val + '日';\r\n                }\r\n                if (type === 'hour') {\r\n                    return val + '时';\r\n                }\r\n                if (type === 'minute') {\r\n                    return val + '分';\r\n                }\r\n                return val;\r\n            },\r\n        },\r\n        computed:{\r\n            date_date(){\r\n                let date = new Date();\r\n                if (this.data.value == null || this.data.value == ''){\r\n                    return date;\r\n                }\r\n                try {\r\n                    date =  new Date(this.data.value);\r\n                }catch (e){}\r\n                return date;\r\n            },\r\n        }\r\n    }\r\n</script>\r\n<style scoped>\r\n</style>", "<template>\r\n    <van-popup v-model=\"show\" position=\"right\" closeable close-icon-position=\"top-left\"  @click-close-icon=\"closeEdit\" style=\"width: 100vw;height: 100vh;\">\r\n        <div class=\"main\" style=\"background-color: #fff\">\r\n            <m-header name=\"编辑数据\"></m-header>\r\n            <m-body  padding=\"false\">\r\n                <van-form>\r\n                    <van-field\r\n                        required\r\n                        @click-input=\"cc_show_flag = true\"\r\n                        name=\"厂区\"\r\n                        label=\"厂区\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"data.cc_name\"\r\n                        placeholder=\"选择厂区\"\r\n                        readonly\r\n                    />\r\n                    <van-popup\r\n                        v-model=\"cc_show_flag\"\r\n                        position=\"bottom\"\r\n                        style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <van-picker\r\n                            title=\"选择厂区\"\r\n                            show-toolbar\r\n                            :columns=\"cc_list\"\r\n                            @cancel= \"cc_show_flag = false\"\r\n                            @confirm=\"selectCC\"\r\n                        >\r\n                            <template #columns-bottom>\r\n                                <div style=\"padding-top: 10px\">\r\n                                    <van-button round block type=\"default\" @click=\"selectCC(null)\">取消选择</van-button>\r\n                                </div>\r\n                            </template>\r\n                        </van-picker>\r\n                    </van-popup>\r\n                    <van-field\r\n                        required\r\n                        @click-input=\"ck_show_flag = true\"\r\n                        name=\"仓库\"\r\n                        label=\"仓库\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"data.ck_name\"\r\n                        placeholder=\"选择仓库\"\r\n                        readonly\r\n                    />\r\n                    <van-popup\r\n                        v-model=\"ck_show_flag\"\r\n                        position=\"bottom\"\r\n                        style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <van-picker\r\n                            title=\"选择货物\"\r\n                            show-toolbar\r\n                            :columns=\"ck_list\"\r\n                            @cancel= \"ck_show_flag = false\"\r\n                            @confirm=\"selectCK\"\r\n                        >\r\n                            <template #columns-bottom>\r\n                                <div style=\"padding-top: 10px\">\r\n                                    <van-button round block type=\"default\" @click=\"selectCK(null)\">取消选择</van-button>\r\n                                </div>\r\n                            </template>\r\n                        </van-picker>\r\n                    </van-popup>\r\n                    <van-field\r\n                        required\r\n                        @click-input=\"goods_show_flag = true\"\r\n                        name=\"货物\"\r\n                        label=\"货物\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"data.goods_name\"\r\n                        placeholder=\"选择货物\"\r\n                        readonly\r\n                    />\r\n                    <van-popup\r\n                        v-model=\"goods_show_flag\"\r\n                        position=\"bottom\"\r\n                        style=\"height: 400px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <van-picker\r\n                            title=\"选择货物\"\r\n                            show-toolbar\r\n                            :columns=\"goods_list\"\r\n                            @cancel= \"goods_show_flag = false\"\r\n                            @confirm=\"selectGoods\"\r\n                        >\r\n                            <template #columns-bottom>\r\n                                <div style=\"padding-top: 10px\">\r\n                                    <van-button round block type=\"default\" @click=\"selectGoods(null)\">取消选择</van-button>\r\n                                </div>\r\n                            </template>\r\n                        </van-picker>\r\n                    </van-popup>\r\n                    <van-field\r\n                        required\r\n                        @click-input=\"cnt_show = true\"\r\n                        type=\"number\"\r\n                        name=\"数量\"\r\n                        label=\"数量\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"data.cnt\"\r\n                        placeholder=\"请输入数量\"\r\n                        readonly\r\n                    >\r\n                        <template #button>\r\n                            <span v-if=\"data.unit != ''\" style=\"color: #000\" v-text=\"'（'+data.unit+'）'\"></span>\r\n                        </template>\r\n                    </van-field>\r\n                    <van-number-keyboard\r\n                        :show=\"cnt_show\"\r\n                        theme=\"custom\"\r\n                        extra-key=\".\"\r\n                        close-button-text=\"完成\"\r\n                        @blur=\"cnt_show = false\"\r\n                        @input=\"onCntInput\"\r\n                        @delete=\"onCntDelete\"\r\n                    />\r\n                    <div style=\"position: absolute;bottom:0;width: 100%;padding: 10px;border-top: 1px #F2F2F2 solid;background-color: #FFFFFF;z-index: 99;\">\r\n                        <van-button  round block type=\"info\" @click=\"onSubmit\">提交</van-button>\r\n                    </div>\r\n                </van-form>\r\n            </m-body>\r\n        </div>\r\n    </van-popup>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"goodsEdit\",\r\n    props: {\r\n        inShow: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        dataGroup : {\r\n            type : String,\r\n            default: null\r\n        },\r\n        dataValue:{\r\n            type : Object,\r\n            default: null\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            data:{\r\n                cc_id:'',\r\n                cc_name:'',\r\n                ck_id:'',\r\n                ck_name:'',\r\n                goods_id:'',\r\n                goods_name:'',\r\n                cnt : '',\r\n                unit : ''\r\n            },\r\n            cc_list:[],\r\n            ck_list:[],\r\n            goods_list:[],\r\n            show : false,\r\n            goods_show_flag : false,\r\n            cc_show_flag : false,\r\n            ck_show_flag : false,\r\n            cnt_show:false\r\n        }\r\n    },\r\n    created() {\r\n    },\r\n    methods: {\r\n        onSubmit(){\r\n            if (this.data.goods_id == '' || this.data.goods_id == null){\r\n                this.$toast({\r\n                    message: '请选择货物',\r\n                    position: 'top'\r\n                });\r\n                return;\r\n            }\r\n            if (this.data.cnt == '' || this.data.cnt == null){\r\n                this.$toast({\r\n                    message: '请输入数量',\r\n                    position: 'top'\r\n                });\r\n                return;\r\n            }\r\n            if (parseFloat(this.data.cnt) <= 0){\r\n                this.$toast({\r\n                    message: '请输入正确数量',\r\n                    position: 'top'\r\n                });\r\n                return;\r\n            }\r\n            this.$emit('change', {...this.data});\r\n        },\r\n        selectCC(obj){\r\n            if (obj == null){\r\n                this.data.cc_id = '';\r\n                this.data.cc_name = '';\r\n                this.cc_show_flag = false;\r\n                return;\r\n            }\r\n            if (this.cc_id == obj.id){\r\n                this.cc_show_flag = false;\r\n                return;\r\n            }\r\n            this.data.cc_id = obj.id;\r\n            this.data.cc_name = obj.text;\r\n            this.cc_show_flag = false;\r\n            this.data.ck_id = '';\r\n            this.data.ck_name = '';\r\n            this.data.goods_id = '';\r\n            this.data.goods_name = '';\r\n            this.getGoods();\r\n        },\r\n        selectCK(obj){\r\n            if (obj == null){\r\n                this.data.ck_id = '';\r\n                this.data.ck_name = '';\r\n                this.ck_show_flag = false;\r\n                return;\r\n            }\r\n            if (this.data.ck_id == obj.id){\r\n                this.ck_show_flag = false;\r\n                return;\r\n            }\r\n            this.data.ck_id = obj.id;\r\n            this.data.ck_name = obj.text;\r\n            this.data.goods_id = '';\r\n            this.data.goods_name = '';\r\n            this.ck_show_flag = false;\r\n            this.getGoods();\r\n        },\r\n        getGoodsObj(arr){\r\n            if (arr == null){\r\n                return null;\r\n            }\r\n            for(let it of this.goods_list){\r\n                if (arr[0] == it.text){\r\n                    for(let c of it.children){\r\n                        if (arr[1] == c.text){\r\n                            return c;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            return null\r\n        },\r\n        selectGoods(arr){\r\n            let obj = this.getGoodsObj(arr);\r\n            if (obj == null){\r\n                this.data.goods_id = '';\r\n                this.data.goods_name = '';\r\n                this.goods_show_flag = false;\r\n                return;\r\n            }\r\n            if (this.data.goods_id == obj.id){\r\n                this.goods_show_flag = false;\r\n                return;\r\n            }\r\n            this.data.goods_id = obj.id;\r\n            this.data.goods_name = obj.text;\r\n            this.data.unit = obj.unit;\r\n            this.goods_show_flag = false;\r\n        },\r\n        onCntInput(v){\r\n            if (this.data.cnt.length >= 10){\r\n                return;\r\n            }\r\n            this.data.cnt = this.data.cnt + '' + v;\r\n        },\r\n        onCntDelete(v){\r\n            if (this.data.cnt == ''){\r\n                return;\r\n            }\r\n            this.data.cnt = this.data.cnt.substring(0,this.data.cnt.length-1);\r\n        },\r\n        closeEdit() {\r\n            this.$emit('close');\r\n        },\r\n        getGoods(){\r\n            this.$http.post('work/work/goods', {...this.data,group_id:this.dataGroup}).then((rs) => {\r\n                if (rs.status == 'ok'){\r\n                    this.cc_list = rs.data.cc_list;\r\n                    this.ck_list = rs.data.ck_list;\r\n                    this.goods_list = rs.data.goods_list;\r\n                } else {\r\n                    this.cc_list = [];\r\n                    this.ck_list = [];\r\n                    this.goods_list =[];\r\n                    this.$toast.fail(rs.message);\r\n                }\r\n            }).catch(() => {\r\n                this.$router.replace({name: 'error'});\r\n            });\r\n        }\r\n    },\r\n    watch: {\r\n        inShow: function (val) {\r\n            this.show = val;\r\n            if (val){\r\n                if (this.dataValue == null){\r\n                    for(let key in this.data){\r\n                        this.data[key] = '';\r\n                    }\r\n                } else {\r\n                    for(let key in this.data){\r\n                        this.data[key] = this.dataValue[key];\r\n                    }\r\n                }\r\n                this.getGoods();\r\n            }\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "<template>\r\n    <van-popup v-model=\"show\" position=\"right\" closeable close-icon-position=\"top-left\" class=\"plate-main\" @click-close-icon=\"closePlate\" @open=\"showPlate\">\r\n        <div class=\"plate-header\">车牌号码录入</div>\r\n        <div class=\"plate-panel\">\r\n            <div class=\"plate-panel-header\">\r\n                <div class=\"plate-panel-title\">车牌号码</div>\r\n            </div>\r\n            <div class=\"plate-panel-body\">\r\n                <div class=\"plate-row\">\r\n                    <div v-for=\"(plate, idx) in plate_list\" class=\"plate-box\" :class=\"box_idx == idx ? 'selected' : ''\" @click=\"touchBox(idx)\" v-text=\"plate\"></div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"padding\">\r\n            <van-button type=\"info\" block @click=\"doSubmit\">确定</van-button>\r\n        </div>\r\n\r\n        <van-popup v-model=\"keyboard_show\" position=\"bottom\" :overlay=\"false\">\r\n            <div class=\"pop-box\">\r\n                <div v-if=\"box_idx == 0\" class=\"keyboard-box header-box\">\r\n                    <div v-for=\"(plate_header, idx) in plate_header_list\" :key=\"idx\" class=\"keyboard-btn\" :class=\"header_idx == idx ? 'selected' : ''\"\r\n                         @click=\"selectHeader(idx)\">\r\n                        <div class=\"keyboard-text\" v-text=\"plate_header\"></div>\r\n                    </div>\r\n                </div>\r\n                <div v-else class=\"keyboard-box\">\r\n                    <div class=\"keyboard-row num-row\">\r\n                        <div v-for=\"(num, idx) in num_list\" :key=\"idx\" class=\"keyboard-btn\" @click=\"selectLetter(num)\">\r\n                            <div class=\"keyboard-text\" v-text=\"num\"></div>\r\n                        </div>\r\n                    </div>\r\n                    <div v-for=\"(letter_row, idx) in letter_list\" :key=\"idx\" class=\"keyboard-row\" :class=\"idx == letter_list.length - 1 ? 'last' : ''\">\r\n                        <div v-for=\"(letter, letter_idx) in letter_row\" :key=\"letter_idx\" class=\"keyboard-btn\" @click=\"selectLetter(letter)\">\r\n                            <div class=\"keyboard-text\" v-text=\"letter\"></div>\r\n                        </div>\r\n                        <div v-if=\"idx == letter_list.length - 1\" class=\"keyboard-btn btn-del\" @click=\"doDel\">\r\n                            <div class=\"keyboard-text\">\r\n                                <div class=\"iconfont icon-tuige\"></div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </van-popup>\r\n    </van-popup>\r\n</template>\r\n\r\n<script>\r\n    import '../resource/css/iconfont/iconfont.css';\r\n\r\n    export default {\r\n        name: \"plate\",\r\n\r\n        props: {\r\n            inShow: {\r\n                type: Boolean,\r\n                default: false\r\n            },\r\n\r\n            inPlate: {\r\n                type: String,\r\n                default: ''\r\n            }\r\n        },\r\n\r\n        data() {\r\n            return {\r\n                box_idx: 0,\r\n                last_box_idx: 6,\r\n                plate_list: ['', '', '', '', '', '', ''],\r\n                plate_header_list: [],\r\n                letter_list: [],\r\n                num_list: [],\r\n                header_idx: -1,\r\n                vehicle_list: [],\r\n                keyboard_show: true,\r\n                show: false,\r\n                plate_no: ''\r\n            };\r\n        },\r\n\r\n        created() {\r\n            this.init();\r\n        },\r\n\r\n        methods: {\r\n            init() {\r\n                this.plate_header_list = [\r\n                    \"京\", \"津\", \"冀\", \"晋\", \"蒙\", \"辽\", \"吉\", \"黑\", \"沪\", \"苏\",\r\n                    \"浙\", \"皖\", \"闽\", \"赣\", \"鲁\", \"豫\", \"鄂\", \"湘\", \"粤\", \"桂\",\r\n                    \"琼\", \"渝\", \"川\", \"贵\", \"云\", \"藏\", \"陕\", \"甘\", \"青\", \"宁\",\r\n                    \"新\", \"港\", \"澳\", \"台\", \"临\"\r\n                ]\r\n                this.letter_list =[\r\n                    ['Q','W','E','R','T','Y','U','P'],\r\n                    ['A','S','D','F','G','H','J','K','L'],\r\n                    ['Z','X','C','V','B','N','M']\r\n                ];\r\n                this.num_list =  ['1','2','3','4','5','6','7','8','9','0'];\r\n                this.vehicle_list = [];\r\n                this.$nextTick(() => {\r\n                    let plate_no = this.plate_no || '';\r\n                    if (plate_no) {\r\n                        this.setPlate(plate_no);\r\n                    }\r\n                });\r\n            },\r\n\r\n            touchBox(idx) {\r\n                this.keyboard_show = true;\r\n                this.box_idx = idx;\r\n            },\r\n\r\n            selectPlate(plate) {\r\n                this.setPlate(plate);\r\n            },\r\n\r\n            setPlate(plate) {\r\n                let plate_list = ['', '', '', '', '', '', ''];\r\n                let header_idx = -1;\r\n                for (let i = 0; i < plate.length; i++) {\r\n                    if (i >= plate_list.length) {\r\n                        break;\r\n                    }\r\n                    plate_list[i] = plate.substr(i, 1);\r\n\r\n                    if (i == 0) {\r\n                        header_idx = this.plate_header_list.indexOf(plate_list[i]);\r\n                    }\r\n                }\r\n                this.plate_list = plate_list;\r\n                this.box_idx = this.last_box_idx;\r\n                this.header_idx = header_idx;\r\n            },\r\n\r\n            selectHeader(idx) {\r\n                let plate_list = this.plate_list;\r\n                plate_list[0] = this.plate_header_list[idx];\r\n\r\n                this.plate_list = plate_list;\r\n                this.header_idx = idx;\r\n                this.box_idx = 1;\r\n            },\r\n\r\n            selectLetter(letter) {\r\n                this.$set(this.plate_list, this.box_idx, letter);\r\n                if (this.box_idx < this.last_box_idx) {\r\n                    this.box_idx++;\r\n                }\r\n            },\r\n\r\n            doDel() {\r\n                if (this.box_idx == 0) {\r\n                    return false;\r\n                }\r\n\r\n                if (this.box_idx < this.last_box_idx || !this.plate_list[this.box_idx]) {\r\n                    this.box_idx--;\r\n                }\r\n                this.$set(this.plate_list, this.box_idx, '');\r\n\r\n                if (this.box_idx == 0) {\r\n                    this.header_idx = -1;\r\n                }\r\n            },\r\n\r\n            doSubmit() {\r\n                let empty_flag = true;\r\n                for (let item of this.plate_list){\r\n                    if (item != ''){\r\n                        empty_flag = false;\r\n                        break\r\n                    }\r\n                }\r\n                if (empty_flag){\r\n                    this.$emit('change', plate);\r\n                    return;\r\n                }\r\n                let plate = '';\r\n                let letter = '';\r\n                for (let i = 0; i < this.plate_list.length; i++) {\r\n                    letter = this.plate_list[i];\r\n                    if (!letter && i < this.last_box_idx) {\r\n                        this.$toast({\r\n                            message: '请录入完整的车牌号码',\r\n                            type: 'fail'\r\n                        });\r\n                        return false;\r\n                    }\r\n\r\n                    plate += letter;\r\n                }\r\n\r\n                if (!checkPlate(plate)){\r\n                  this.$toast({\r\n                    message: '请正确的车牌号码',\r\n                    type: 'fail'\r\n                  });\r\n                  return false;\r\n                }\r\n\r\n                this.plate_list = ['', '', '', '', '', '', ''];\r\n                this.box_idx = 0;\r\n                this.header_idx = -1;\r\n                this.$emit('change', plate);\r\n            },\r\n\r\n            showPlate() {\r\n                this.keyboard_show = true;\r\n            },\r\n\r\n            closePlate() {\r\n                this.$emit('close');\r\n            }\r\n        },\r\n\r\n        watch: {\r\n            inShow: function(val) {\r\n                this.show = val;\r\n                if (val) {\r\n                    let plate_no = this.inPlate || '';\r\n                    if (plate_no) {\r\n                        this.setPlate(plate_no);\r\n                    }\r\n                }\r\n            },\r\n\r\n            inPlate: function(val) {\r\n                this.plate_no = val;\r\n            }\r\n        }\r\n    }\r\n\r\n    function checkPlate(plate) {\r\n      return /^[临京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领a-zA-Z]{1}[a-zA-Z]{1}[a-zA-Z0-9]{4}[a-zA-Z0-9挂学警港澳]{1}$/.test(plate);\r\n    }\r\n</script>\r\n\r\n<style>\r\n    .plate-main .van-icon-cross::before {\r\n        color: #FFFFFF;\r\n    }\r\n</style>\r\n\r\n<style scoped>\r\n    .plate-main {\r\n        background-color: #FAFAFA;\r\n        width: 100vw;\r\n        height: 100vh;\r\n        overflow: hidden;\r\n    }\r\n\r\n    .plate-header {\r\n        text-align: center;\r\n        height: 54px;\r\n        line-height: 54px;\r\n        background-color: #0081FF;\r\n        color: #FFFFFF;\r\n    }\r\n\r\n    .plate-panel {\r\n        background-color: #FFFFFF;\r\n        display: flex;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .plate-panel-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        font-size: 16px;\r\n        padding: 15px 15px 15px 5px;\r\n        border-bottom: 1px solid rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .plate-panel-title {\r\n        display: flex;\r\n        align-items: center;\r\n    }\r\n\r\n    .plate-panel-title::before {\r\n        content: '';\r\n        display: block;\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 100% !important;\r\n        background-color: #236eeb;\r\n        margin: 0 10px;\r\n    }\r\n\r\n    .plate-panel-body {\r\n        padding: 15px;\r\n    }\r\n\r\n    .plate-row {\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n        align-items: center;\r\n        font-size: 30px;\r\n        color: #333;\r\n        margin: 15px 0 25px;\r\n    }\r\n\r\n    .plate-box {\r\n        border: 1px solid #ddd;\r\n        height: 45px;\r\n        width: 35px;\r\n        margin: 0 5px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .plate-box.selected {\r\n        border: 2px solid #3197f3;\r\n    }\r\n\r\n    .vehicle-box {\r\n        overflow-y: auto;\r\n        max-height: 192px;\r\n        margin-left: -5px;\r\n        margin-right: -5px;\r\n    }\r\n\r\n    .vehicle-box .item-plate {\r\n        float: left;\r\n        padding: 0 5px;\r\n        font-size: 17px;\r\n        width: 33.333333%;\r\n        box-sizing: border-box;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .item-plate .plate-no {\r\n        background-color: #eee;\r\n        box-sizing: border-box;\r\n        height: 38px;\r\n        color: #333;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n    }\r\n\r\n    .plate-footer {\r\n        padding: 8px 5px 5px;\r\n        position: absolute;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        z-index: 1;\r\n        transition: all 400ms;\r\n    }\r\n\r\n    .plate-footer.hide {\r\n        bottom: -50px;\r\n    }\r\n\r\n    .van-popup {\r\n        box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .pop-box {\r\n        padding: 8px 5px 5px;\r\n        background-color: #f2f2f2;\r\n    }\r\n\r\n    .keyboard-box {\r\n        overflow: hidden;\r\n    }\r\n\r\n    .header-box .keyboard-btn {\r\n        float: left;\r\n        box-sizing: border-box;\r\n        width: 14.285714%;\r\n        padding: 5px;\r\n    }\r\n\r\n    .header-box .keyboard-btn.selected .keyboard-text {\r\n        background-color: #3197f3;\r\n        color: #FFFFFF;\r\n    }\r\n\r\n    .keyboard-text {\r\n        box-sizing: border-box;\r\n        background-color: #FFFFFF;\r\n        text-align: center;\r\n        padding: 4px 0;\r\n        border-radius: 5px;\r\n        box-shadow: 0 2px 0 #aaaaaa;\r\n    }\r\n\r\n    .keyboard-row {\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .keyboard-row.last {\r\n        margin-bottom: 0;\r\n    }\r\n\r\n    .keyboard-row .keyboard-text {\r\n        width: 1.8em;\r\n        height: 35px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    .keyboard-btn {\r\n        padding: 5px;\r\n    }\r\n\r\n    .btn-del .keyboard-text {\r\n        width: 50px;\r\n        background-color: #99b4cc;\r\n        color: #FFFFFF;\r\n    }\r\n\r\n    .num-row .keyboard-btn {\r\n        padding: 5px 4px;\r\n    }\r\n\r\n    .pop-footer {\r\n        padding-top: 8px;\r\n    }\r\n</style>", "<template>\r\n    <div  class=\"main\" style=\"background-color: #fff\">\r\n        <m-header name=\"发起申请\" is_back=\"1\"></m-header>\r\n        <m-body  padding=\"false\">\r\n            <van-form>\r\n                <van-field\r\n                        name=\"所属部门\"\r\n                        label=\"所属部门\"\r\n                        v-model=\"group_name\"\r\n                        placeholder=\"\"\r\n                        readonly\r\n                />\r\n                <van-field\r\n                        @click-input=\"type_show_flag = true\"\r\n                        required\r\n                        name=\"业务流程\"\r\n                        label=\"业务流程\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"type_name\"\r\n                        placeholder=\"选择业务流程\"\r\n                        readonly\r\n                />\r\n                <van-popup\r\n                        v-model=\"type_show_flag\"\r\n                        position=\"bottom\"\r\n                        style=\"height: 350px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                    <van-picker\r\n                            title=\"选择业务流程\"\r\n                            show-toolbar\r\n                            :columns=\"type_list\"\r\n                            @cancel= \"type_show_flag = false\"\r\n                            @confirm=\"selectType\"\r\n                    />\r\n                </van-popup>\r\n                <template v-if=\"form_data_show\" v-for=\"(item,idx) in form_data\">\r\n                    <detail-field v-if=\"item.type == 99\" :required=\"item.required\" :title=\"item.title\" :explain=\"item.explain\" :form-list=\"item.list\" :value-list=\"item.values\"></detail-field>\r\n                    <dynamic-field v-else :data=\"item\"  @change=\"changeValue\"></dynamic-field>\r\n                </template>\r\n                <van-field\r\n                        :is-link=\"true\"\r\n                        v-model=\"remarks\"\r\n                        rows=\"2\"\r\n                        autosize\r\n                        type=\"textarea\"\r\n                        name=\"说明\"\r\n                        label=\"说明\"\r\n                        placeholder=\"请输入相关说明\"\r\n                />\r\n                <van-field name=\"uploader\" label=\"文件上传\">\r\n                    <template #input>\r\n                        <input\r\n                                ref=\"fileInput\"\r\n                                type=\"file\"\r\n                                multiple\r\n                                accept=\"image/*\"\r\n                                hidden\r\n                                @change=\"handleFileChange\"\r\n                        >\r\n                        <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;\">\r\n                            <div v-for=\"(file,i) in files\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                <div @click=\"delPhoto(i)\" style=\"position: absolute;top: -5px;right: -5px;width: 20px;height: 20px;background-color: red;border-radius: 20px;z-index: 999;text-align: center;display: flex;flex-direction: column;justify-content: center\">\r\n                                    <van-icon name=\"cross\" size=\"16\" color=\"#FFFFFF\"/>\r\n                                </div>\r\n                                <van-image :src=\"file\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                            </div>\r\n                            <div v-if=\"files.length < 5\" @click=\"takePhoto\" style=\"width: 80px;height: 80px;background-color: #f2f2f2;text-align: center;display: flex;flex-direction: column; justify-content: center;\">\r\n                                <van-icon name=\"photograph\" color=\"#bbbbbb\" size=\"25\"/>\r\n                            </div>\r\n                        </div>\r\n                    </template>\r\n                </van-field>\r\n                <div class=\"btn-footer\">\r\n                    <van-button v-if=\"uid!=''\" round block type=\"danger\" @click=\"deleteSave\" style=\"margin-right: 20px\">删除</van-button>\r\n                    <van-button  round block type=\"info\" @click=\"onSubmit\" style=\"margin-right: 20px\">提交</van-button>\r\n                </div>\r\n            </van-form>\r\n            <div v-if=\"flow_data.length > 0\" class=\"content\" style=\"margin-bottom: 80px\">\r\n                <div>\r\n                    <div v-for=\"(item,index) in flow_list\" class=\"item\">\r\n                        <div class=\"title-border\">\r\n                            <div v-if=\"item.type == 4\" class=\"title\" style=\"background-color: #3296FB\">\r\n                                <van-icon name=\"wap-home-o\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                            </div>\r\n                            <div v-else-if=\"item.type == 5\" class=\"title\" style=\"background-color: #FF2B2B\">\r\n                                <van-icon name=\"stop\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                            </div>\r\n                            <div v-else-if=\"item.type == 6\" class=\"title\" style=\"background-color: #FF2B2B\">\r\n                                <van-icon name=\"revoke\" size=\"30\" style=\"margin-top: 6px\"/>\r\n                            </div>\r\n                            <div v-else class=\"title\" style=\"background-color: #3296FB\">\r\n                                {{item.icon}}\r\n                                <div v-if=\"item.type == 1\" class=\"border\" style=\"color:#4AB37E\">\r\n                                    <van-icon name=\"checked\"/>\r\n                                </div>\r\n                                <div v-else-if=\"item.type == 3\" class=\"border\" style=\"color:#4AB37E\">\r\n                                    <van-icon name=\"volume\"/>\r\n                                </div>\r\n                                <div v-else class=\"border\" style=\"color: #3296FB\">\r\n                                    <van-icon name=\"thumb-circle\" />\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"item-content\">\r\n                            <div class=\"top\">\r\n                                <div :style=\"{flex:'1',color: item.status == 0 ? '#A2A2A2' : '#000000'}\">\r\n                                    <span>{{item.name}}</span>\r\n                                    <span> {{item.val}}</span>\r\n                                </div>\r\n                                <div style=\"width: 80px;margin-left: 5px\">\r\n                                    <span style=\"font-size: 13px;color: #898989;margin-top: -3px\">{{item.time}}</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bottom\" :style=\"{borderLeft:flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'}\">\r\n                                <div v-if=\"item.text != ''\" class=\"bottom-content\">\r\n                                    {{item.text}}\r\n                                </div>\r\n                                <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;margin-left: 35px;margin-top: 5px\">\r\n                                    <div v-for=\"(file,i) in item.files\" @click=\"previewImg(file)\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                        <van-image :src=\"base_path+file + '!small'\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                    </div>\r\n                                </div>\r\n                                <div v-if=\"item.send != ''\" class=\"bottom-send\">\r\n                                    {{item.send}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div v-for=\"(item,idx) in flow_data\" :key=\"idx\">\r\n                    <div style=\"display: flex;flex-direction: row;min-height: 60px;\">\r\n                        <div style=\"width: 35%;display: flex;flex-direction: row;\">\r\n                            <div style=\"height: 100%;position: relative\">\r\n                                <div style=\"height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;\">\r\n                                </div>\r\n                                <div v-if=\"idx == 0\" style=\"height: 18px;position: absolute;top: 0;width: 12px;background-color: #FFF\">\r\n                                </div>\r\n                                <div v-if=\"idx+1 == flow_data.length\" style=\"height: calc(100% - 22px);position: absolute;top: 22px;width: 12px;background-color: #FFF\">\r\n                                </div>\r\n                                <div style=\"position: absolute;top: 15px;width: 15px;height: 15px;background-color: #aaaaaa;border-radius: 15px\">\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"padding-top:5px;padding-left: 15px\">\r\n                                <span style=\"font-weight: 400\" v-text=\"item.name\"></span>\r\n                                <div>\r\n                                    <span style=\"font-size: 12px;color: #898989\" v-text=\"item.type_name\"></span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"width: 65%;display: flex;flex-direction: row;flex-wrap: wrap;justify-content: flex-end;padding-right: 10px;padding-top: 5px\">\r\n                            <van-tag v-for=\"(user,i) in item.list\" :key=\"user.id\" :type=\"item.type == 1 ? 'primary' : 'success'\" size=\"large\" style=\"height: 20px;margin: 2px\">{{user.name}}</van-tag>\r\n                        </div>\r\n                    </div>\r\n                    <div v-if=\"item.nlist.length > 0\" style=\"display: flex;flex-direction: row;min-height: 60px;\">\r\n                        <div style=\"width: 35%;display: flex;flex-direction: row;\">\r\n                            <div style=\"height: 100%;position: relative\">\r\n                                <div v-if=\"idx+1 != flow_data.length\" style=\"height: 100%;border-right: 1px #D2D2D2 solid;width: 8px;\">\r\n                                </div>\r\n                                <div v-else style=\"height: 100%;width: 8px;\">\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"padding-top:5px;padding-left: 15px\">\r\n                                <span style=\"font-weight: 400;color: #898989\">抄送人</span>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"width: 65%;display: flex;flex-direction: row;flex-wrap: wrap;justify-content: flex-end;padding-right: 10px;padding-top: 5px\">\r\n                            <van-tag v-for=\"(user,i) in item.nlist\" :key=\"user.id\" type=\"default\" size=\"large\" style=\"height: 20px;margin: 2px\">{{user.name}}</van-tag>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import base from '../../components/base';\r\n    import plate from '../../components/plate';\r\n    import dynamicField from '../../components/dynamic_field';\r\n    import detailField from '../../components/detail_field';\r\n    import editGoods from '../../components/edit_goods';\r\n    import destField from '../../components/dest_field';\r\n    import { Dialog } from 'vant';\r\n    var default_data = {};\r\n    export default {\r\n        name: \"workRequest\",\r\n        components: { plate , dynamicField, detailField, editGoods, destField},\r\n        extends: base,\r\n        data() {\r\n            return {\r\n                uid:'',\r\n                p_uid:'',\r\n                main_uid : '',\r\n                group_id:'',\r\n                group_name:'',\r\n                type_id:'',\r\n                type_name:'',\r\n                files:[],\r\n                remarks:'',\r\n                flow_data:[],\r\n                flow_list:[],\r\n                form_data:[],\r\n                type_list : [],\r\n                type_show_flag : false,\r\n                form_data_show:true,\r\n                min_date: new Date(2022,0,1),\r\n                max_date: new Date(2099, 1, 1),\r\n                base_path:''\r\n            }\r\n        },\r\n        created() {\r\n            default_data = {...this.$data};\r\n        },\r\n        methods: {\r\n            onLoad() {\r\n                for(let key in default_data){\r\n                    this.$data[key] = default_data[key];\r\n                }\r\n                let user = this.$store.state.user;\r\n                this.base_path = user.imgdir;\r\n                this.files = [];\r\n                this.goods_data = [];\r\n                this.flow_list = [];\r\n                this.init();\r\n            },\r\n            onShow() {\r\n                this.onLoad();\r\n            },\r\n            changeValue(item){\r\n\r\n            },\r\n            init() {\r\n                this.uid = '';\r\n                this.p_uid = '';\r\n                this.main_uid = '';\r\n                if (this.$route.params.uid){\r\n                    this.uid = this.$route.params.uid;\r\n                    this.$http.post('work/work/edit', {uid: this.uid}).then((rs) => {\r\n                        if (rs.status == 'ok'){\r\n                            let data = rs.data;\r\n                            for(let key in data){\r\n                                this.$data[key] = data[key];\r\n                            }\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                            return;\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$router.replace({name: 'error'});\r\n                    });\r\n                } else if (this.$route.params.main_uid){\r\n                    this.main_uid = this.$route.params.main_uid;\r\n                    this.p_uid = this.$route.params.p_uid;\r\n                    this.group_name =  this.$route.params.group_name;\r\n                    this.selectType({id:this.$route.params.type_id,text:this.$route.params.type_name})\r\n                } else {\r\n                    this.$http.post('work/work/init', {id: 0}).then((rs) => {\r\n                        if (rs.status == 'ok'){\r\n                            this.group_name = rs.group_name;\r\n                            this.type_list = rs.type_list;\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                            return;\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$router.replace({name: 'error'});\r\n                    });\r\n                }\r\n            },\r\n            onSubmit(){\r\n                if (this.type_id === ''){\r\n                    this.$toast.fail('请选择类型');\r\n                    return;\r\n                }\r\n                if (this.flow_data.length === 0){\r\n                    this.$toast.fail('选择的类型未配置业务流程');\r\n                    return;\r\n                }\r\n                let data_list = [];\r\n                for(let item of this.form_data){\r\n                    if (item.required == 1){\r\n                        if (item.type == 4 || item.type == 99){\r\n                            if (item.values.length == 0){\r\n                                this.$toast.fail('请选择'+item.title);\r\n                                return;\r\n                            }\r\n                        } else if (item.type == 2){\r\n                            if (item.value == '' || parseFloat(item.value) == 0){\r\n                                this.$toast.fail('请输入'+item.title);\r\n                                return;\r\n                            }\r\n                        } else {\r\n                            if (item.value == ''){\r\n                                this.$toast.fail('请输入'+item.title);\r\n                                return;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (item.type == 4) {\r\n                        if (item.values.length > 0){\r\n                            data_list.push({\r\n                                id:item.id,\r\n                                type:item.type,\r\n                                name:item.title,\r\n                                value:item.values.toString(),\r\n                                unit:item.unit\r\n                            })\r\n                        }\r\n                    } else if(item.type == 99){\r\n                        if (item.values.length > 0){\r\n                            data_list.push({\r\n                                id:item.id,\r\n                                type:item.type,\r\n                                name:item.title,\r\n                                sum_list:this.getSumData(item.values),\r\n                                data_list:this.getDataList(item.values)\r\n                            })\r\n                        }\r\n                    } else {\r\n                        if (item.value != ''){\r\n                            let push_obj = {\r\n                                id:item.id,\r\n                                type:item.type,\r\n                                name:item.title,\r\n                                value:item.value,\r\n                                unit:item.unit\r\n                            };\r\n                            if (item.type == 12){\r\n                                push_obj.key = item.values;\r\n                            }\r\n                            data_list.push(push_obj);\r\n                        }\r\n                    }\r\n                }\r\n                Dialog.confirm({\r\n                    title: '提交',\r\n                    message: '确定要提交吗？',\r\n                })\r\n                    .then(() => {\r\n                        this.$cjs.showLoading('文件上传中');\r\n                        this.upload(this.files,[],0,(upload_rs)=>{\r\n                            this.$cjs.hideLoading();\r\n                            if (upload_rs.status == 'ok'){\r\n                                this.$cjs.showLoading('数据提交中');\r\n                                this.$http.post_only('work/work/create', {\r\n                                    uid:this.uid,\r\n                                    p_uid:this.p_uid,\r\n                                    main_uid:this.main_uid,\r\n                                    type_id:this.type_id,\r\n                                    type_name:this.type_name,\r\n                                    data_list : encodeURI(JSON.stringify(data_list)),\r\n                                    files : encodeURI(JSON.stringify(upload_rs.list)),\r\n                                    remarks:this.remarks\r\n                                }).then((rs) => {\r\n                                    this.$cjs.hideLoading();\r\n                                    if (rs.status === 'ok'){\r\n                                        this.$toast.success('提交成功！');\r\n                                        if (this.main_uid !== '' || this.uid !== ''){\r\n                                            this.$hub.$emit('refresh');\r\n                                        }\r\n                                        if (this.main_uid !== ''){\r\n                                            this.$router.go(-2);\r\n                                        } else {\r\n                                            this.$router.back();\r\n                                        }\r\n                                    } else {\r\n                                        this.$toast.fail(rs.message);\r\n                                    }\r\n                                }).catch((e) => {\r\n                                    this.$cjs.hideLoading();\r\n                                    this.$toast.fail('提交失败！');\r\n                                });\r\n                            } else {\r\n                                this.$toast.fail('文件上传失败！');\r\n                            }\r\n                        });\r\n                    })\r\n                    .catch(() => {});\r\n            },\r\n            deleteSave(){\r\n                Dialog.confirm({\r\n                    title: '删除',\r\n                    message: '确定要删除吗？',\r\n                })\r\n                .then(() => {\r\n                    this.$http.post('work/work/delete', {uid: this.uid}).then((rs) => {\r\n                        if (rs.status === 'ok') {\r\n                            this.$toast.success('删除成功');\r\n                            this.$hub.$emit('refreshlist');\r\n                            this.$router.back();\r\n                        } else {\r\n                            this.$toast.fail(rs.message);\r\n                        }\r\n                    }).catch((e) => {\r\n                        this.$toast.fail('提交失败');\r\n                    });\r\n                })\r\n                .catch(() => {});\r\n            },\r\n            getSumData(valueList){\r\n                let data = {};\r\n                for(let value of valueList){\r\n                    for (let item of value){\r\n                        if (item.type == 2 && item.is_sum == 1){\r\n                            if (item.value != ''){\r\n                                if (data[item.id]){\r\n                                    data[item.id].value += parseFloat(item.value);\r\n                                } else {\r\n                                    data[item.id] = {id:item.id, name:item.title+'(合计)',value:parseFloat(item.value),unit:item.unit}\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                let list = [];\r\n                for(let key in data){\r\n                    list.push(data[key]);\r\n                }\r\n                return list;\r\n            },\r\n            getDataList(valueList){\r\n                let list = [];\r\n                for(let value of valueList){\r\n                    let l = [];\r\n                    for (let item of value){\r\n                        if (item.type == 4) {\r\n                            l.push({\r\n                                id:item.id,\r\n                                type:item.type,\r\n                                name:item.title,\r\n                                value:item.values.toString(),\r\n                                unit:item.unit\r\n                            })\r\n                        } else {\r\n                            l.push({\r\n                                id:item.id,\r\n                                type:item.type,\r\n                                name:item.title,\r\n                                value:item.value,\r\n                                unit:item.unit\r\n                            })\r\n                        }\r\n                    }\r\n                    list.push(l);\r\n                }\r\n                return list;\r\n            },\r\n            selectType(obj){\r\n                if (this.type_id == obj.id){\r\n                    this.type_show_flag = false;\r\n                    return;\r\n                }\r\n                this.type_id = obj.id;\r\n                this.type_name = obj.text;\r\n                this.type_show_flag = false;\r\n                this.form_data_show = false;\r\n                this.$http.post('work/work/getflow', {id: obj.id,main_uid:this.main_uid}).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        let data = rs.data;\r\n                        this.form_data_show = true;\r\n                        this.form_data = data.form_data;\r\n                        this.flow_data = data.list;\r\n                    } else {\r\n                        this.flow_data = [];\r\n                        this.form_data = [];\r\n                        this.$toast.fail(rs.message);\r\n                        return;\r\n                    }\r\n                }).catch(() => {\r\n                    this.$router.replace({name: 'error'});\r\n                });\r\n            },\r\n            formatter(type, val) {\r\n                if (type === 'year') {\r\n                    return val + '年';\r\n                }\r\n                if (type === 'month') {\r\n                    return val + '月';\r\n                }\r\n                if (type === 'day') {\r\n                    return val + '日';\r\n                }\r\n                return val;\r\n            },\r\n            takePhoto() {\r\n                this.$refs.fileInput.click();\r\n            },\r\n            async handleFileChange(e){\r\n                const selectedFiles = Array.from(e.target.files);\r\n                if (!selectedFiles) return;\r\n                // 逐个处理文件\r\n                for (const file of selectedFiles) {\r\n                    // 验证文件类型\r\n                    if (!file.type.startsWith('image/')) {\r\n                        this.errorMessage = '仅支持图片格式'\r\n                        continue\r\n                    }\r\n                    // 验证文件大小\r\n                    if (file.size > 10 * 1024 * 1024) {\r\n                        this.errorMessage = `文件大小不能超过10MB`\r\n                        continue\r\n                    }\r\n                    const preview = await this.readFileAsDataURL(file)\r\n                    this.files.push(preview)\r\n                }\r\n            },\r\n            readFileAsDataURL(file) {\r\n                return new Promise((resolve, reject) => {\r\n                    const reader = new FileReader()\r\n                    reader.onload = () => resolve(reader.result)\r\n                    reader.onerror = reject\r\n                    reader.readAsDataURL(file)\r\n                })\r\n            },\r\n            delPhoto(i){\r\n                this.files.splice(i,1);\r\n            },\r\n            upload(flies,new_flies,i,cb){\r\n                if (flies.length == i){\r\n                    cb({\r\n                        status : 'ok',\r\n                        list : new_flies\r\n                    });\r\n                    return;\r\n                }\r\n                this.fileUpload(flies[i],(data)=>{\r\n                    if (data.status == 'ok'){\r\n                        new_flies.push(data.path);\r\n                        i++;\r\n                        this.upload(flies,new_flies,i,cb);\r\n                    } else {\r\n                        cb(data);\r\n                    }\r\n                });\r\n            },\r\n            fileUpload(base64Data,cb){\r\n                let user = this.$store.state.user;\r\n                this.$http.fileUpload(user,'review',base64Data).then((rs) => {\r\n                    cb({\r\n                        status:'ok',\r\n                        path: rs\r\n                    });\r\n                }).catch((e) => {\r\n                    console.error(e);\r\n                    cb({status:'error'});\r\n                });\r\n            }\r\n        },\r\n        computed:{\r\n\r\n        },\r\n        watch:{\r\n\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    .content{\r\n        min-height: 100px;\r\n        background-color: #FFFFFF;\r\n        margin: 15px;\r\n        padding: 15px;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 10px;\r\n    }\r\n    .item{\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-start;\r\n        margin-top: 4px;\r\n    }\r\n    .item .title{\r\n        width: 45px;\r\n        height: 45px;\r\n        line-height: 45px;\r\n        vertical-align: middle;\r\n        background-color: #3296FB;\r\n        border: 1px #E2E2E2 solid;\r\n        border-radius: 5px;\r\n        color: #FFFFFF;\r\n        text-align: center;\r\n        position: relative;\r\n        font-size: 16px;\r\n    }\r\n    .item .title .border{\r\n        position: absolute;\r\n        top: 31px;\r\n        left: 31px;\r\n        width: 18px;\r\n        height: 18px;\r\n        line-height: 18px;\r\n        border-radius: 9px;\r\n        background-color: #FFFFFF;\r\n        text-align: center;\r\n        padding-top: 1px;\r\n    }\r\n\r\n    .item .item-content{\r\n        width: 100%;\r\n        margin-left: 15px;\r\n        color: #888888;\r\n    }\r\n\r\n    .item .item-content .top {\r\n        min-height: 20px;\r\n        line-height: 20px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: space-between;\r\n        font-size: 16px;\r\n        margin-top: 2px;\r\n        vertical-align: top;\r\n    }\r\n\r\n    .item .item-content .bottom {\r\n        margin-left: -38px;\r\n        border-left: 4px #D2D2D2 solid;\r\n        padding-bottom: 10px;\r\n        min-height: 55px;\r\n    }\r\n\r\n    .item .bottom-content{\r\n        margin-left: 35px;\r\n        margin-top:5px;\r\n        min-height: 40px;\r\n        background-color: #F2F2F2;\r\n        border-radius: 5px;\r\n        padding: 10px;\r\n        color: #000000;\r\n    }\r\n\r\n    .item .bottom-send{\r\n        margin-left: 35px;\r\n        margin-top:5px;\r\n        font-size: 14px;\r\n    }\r\n\r\n    .item .title-border{\r\n        background-color: #FFFFFF;\r\n        width: 45px;\r\n        height: 52px;\r\n        z-index: 999;\r\n    }\r\n\r\n    .btn-footer{\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        background-color: #FFFFFF;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n        padding-top: 10px;\r\n        padding-bottom: 10px;\r\n        padding-left: 20px;\r\n        z-index: 1000;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('van-popup',{staticClass:\"main\",attrs:{\"position\":\"right\"},model:{value:(_vm.onshow),callback:function ($$v) {_vm.onshow=$$v},expression:\"onshow\"}},[_c('van-button',{staticStyle:{\"position\":\"absolute\",\"background-color\":\"#0081FF\",\"margin-top\":\"8px\"},attrs:{\"color\":\"#0081FF\",\"icon\":\"cross\"},on:{\"click\":_vm.closeAdd}}),_c('div',{staticClass:\"header\"},[_vm._v(\"添加明细 \")]),_c('van-search',{attrs:{\"show-action\":\"\",\"label\":\"名称\",\"placeholder\":\"请输入搜索关键词\",\"input-align\":\"center\"},scopedSlots:_vm._u([{key:\"action\",fn:function(){return [_c('van-button',{staticStyle:{\"width\":\"60px\"},attrs:{\"plain\":\"\",\"type\":\"default\",\"size\":\"small\"},on:{\"click\":_vm.onSave}},[_vm._v(\"新增 \")])]},proxy:true}]),model:{value:(_vm.name),callback:function ($$v) {_vm.name=$$v},expression:\"name\"}}),_vm._l((_vm.list),function(item,idx){return (item.is_show == 1)?_c('van-cell',{attrs:{\"title\":item.text},scopedSlots:_vm._u([{key:\"right-icon\",fn:function(){return [_c('van-button',{attrs:{\"type\":\"info\",\"plain\":\"\",\"round\":\"\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.saveAdd(item)}}},[_vm._v(\"确认\")])]},proxy:true}],null,true)}):_vm._e()})],2)],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('van-cell',{attrs:{\"title\":_vm.title,\"icon\":\"star\",\"required\":_vm.required == '1' ? true:false},scopedSlots:_vm._u([{key:\"right-icon\",fn:function(){return [_c('van-button',{attrs:{\"type\":\"info\",\"plain\":\"\",\"round\":\"\",\"size\":\"small\"},on:{\"click\":_vm.showAdd}},[_vm._v(\"添加\")])]},proxy:true},{key:\"label\",fn:function(){return [_c('span',{staticStyle:{\"color\":\"red\"},domProps:{\"textContent\":_vm._s(_vm.explain)}})]},proxy:true}])}),_vm._l((_vm.sum_list),function(item,idx){return _c('van-cell',{attrs:{\"title\":item.title,\"value\":item.value + '('+item.unit+')'}})}),_vm._l((_vm.valueList),function(list,index){return _c('div',{key:index,staticClass:\"content\",staticStyle:{\"padding\":\"0\",\"overflow\":\"hidden\"}},[_c('van-cell-group',[_vm._l((list),function(item,idx){return [(item.type == 4)?_c('van-cell',{attrs:{\"title\":item.title},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return _vm._l((item.values),function(d,i){return _c('span',{staticStyle:{\"margin-left\":\"5px\"},domProps:{\"textContent\":_vm._s(d)}})})},proxy:true}],null,true)}):_c('van-cell',{attrs:{\"title\":item.title,\"value\":item.value + item.unit}})]})],2),_c('div',{staticStyle:{\"padding\":\"10px\",\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"flex-end\"}},[_c('van-button',{staticStyle:{\"margin-left\":\"10px\"},attrs:{\"type\":\"danger\",\"plain\":\"\",\"round\":\"\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.delData(index)}}},[_vm._v(\"删除\")]),_c('van-button',{staticStyle:{\"margin-left\":\"10px\"},attrs:{\"type\":\"primary\",\"plain\":\"\",\"round\":\"\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(index)}}},[_vm._v(\"编辑\")])],1)],1)}),_c('van-popup',{staticClass:\"main\",attrs:{\"position\":\"right\",\"closeable\":\"\",\"close-icon-position\":\"top-left\"},on:{\"click-close-icon\":_vm.closeAdd},model:{value:(_vm.show),callback:function ($$v) {_vm.show=$$v},expression:\"show\"}},[_c('div',{staticClass:\"header\"},[_vm._v(\"添加明细\")]),_vm._l((_vm.form_list),function(item,idx){return _c('dynamic-field',{attrs:{\"data\":item},on:{\"change\":_vm.changeValue}})}),_c('div',{staticClass:\"footer\",staticStyle:{\"background-color\":\"#FFFFFF\",\"padding\":\"10px\",\"text-align\":\"center\",\"position\":\"absolute\",\"left\":\"0\",\"bottom\":\"0\",\"width\":\"100%\"}},[_c('van-button',{staticClass:\"van-button\",attrs:{\"plain\":\"\",\"round\":\"\",\"type\":\"info\"},on:{\"click\":_vm.closeAdd}},[_vm._v(\"返回\")]),_c('van-button',{staticClass:\"van-button\",staticStyle:{\"margin-left\":\"30px\"},attrs:{\"round\":\"\",\"type\":\"info\"},on:{\"click\":_vm.addSave}},[_vm._v(\"添加\")])],1)],2)],2)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.data.type == 1)?_c('van-field',{attrs:{\"maxlength\":_vm.data.max,\"label\":_vm.data.title,\"placeholder\":'请输入'+_vm.data.title,\"required\":_vm.data.required == 1 ? true:false,\"error-message\":_vm.data.explain},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 2 || _vm.data.type == 13)?_c('van-field',{attrs:{\"required\":\"\",\"type\":\"number\",\"label\":_vm.data.title,\"is-link\":true,\"placeholder\":'请输入'+_vm.data.title,\"required\":_vm.data.required == 1 ? true:false,\"maxlength\":_vm.data.max,\"error-message\":_vm.data.explain,\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.number_show = true}},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [(_vm.data.unit != '')?_c('span',{staticStyle:{\"color\":\"#000\"},domProps:{\"textContent\":_vm._s('（'+_vm.data.unit+'）')}}):_vm._e()]},proxy:true}],null,false,3363291398),model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 3)?_c('van-field',{attrs:{\"label\":_vm.data.title,\"required\":_vm.data.required == 1 ? true:false,\"error-message\":_vm.data.explain},scopedSlots:_vm._u([{key:\"input\",fn:function(){return [_c('van-radio-group',{attrs:{\"direction\":\"horizontal\"},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}},_vm._l((_vm.data.list),function(item,idx){return _c('van-radio',{attrs:{\"name\":item}},[_vm._v(_vm._s(item))])}),1)]},proxy:true}],null,false,1124482878)}):_vm._e(),(_vm.data.type == 4)?_c('van-field',{attrs:{\"label\":_vm.data.title,\"required\":_vm.data.required == 1 ? true:false,\"error-message\":_vm.data.explain},scopedSlots:_vm._u([{key:\"input\",fn:function(){return [_c('van-checkbox-group',{attrs:{\"direction\":\"horizontal\"},model:{value:(_vm.data.values),callback:function ($$v) {_vm.$set(_vm.data, \"values\", $$v)},expression:\"data.values\"}},_vm._l((_vm.data.list),function(item,idx){return _c('van-checkbox',{staticStyle:{\"margin-bottom\":\"2px\"},attrs:{\"o\":\"\",\"name\":item,\"shape\":\"square\"}},[_vm._v(_vm._s(item))])}),1)]},proxy:true}],null,false,1932094511)}):_vm._e(),(_vm.data.type == 5 )?_c('van-field',{attrs:{\"required\":_vm.data.required == 1 ? true:false,\"label\":_vm.data.title,\"is-link\":true,\"placeholder\":'选择' + _vm.data.title,\"error-message\":_vm.data.explain,\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.select_show = true}},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 6)?_c('van-field',{attrs:{\"required\":_vm.data.required == 1 ? true:false,\"label\":_vm.data.title,\"is-link\":true,\"placeholder\":'选择' + _vm.data.title,\"error-message\":_vm.data.explain,\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.date_show = true}},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 7)?_c('van-field',{attrs:{\"required\":_vm.data.required == 1 ? true:false,\"label\":_vm.data.title,\"is-link\":true,\"placeholder\":'选择' + _vm.data.title,\"error-message\":_vm.data.explain,\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.datetime_show = true}},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 8)?_c('van-field',{attrs:{\"is-link\":true,\"rows\":\"2\",\"autosize\":\"\",\"type\":\"textarea\",\"label\":_vm.data.title,\"placeholder\":'请输入' + _vm.data.title,\"maxlength\":_vm.data.max,\"error-message\":_vm.data.explain},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 9)?_c('van-field',{attrs:{\"required\":_vm.data.required == 1 ? true:false,\"label\":_vm.data.title,\"is-link\":true,\"placeholder\":'选择' + _vm.data.title,\"error-message\":_vm.data.explain,\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.date_month_show = true}},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 10)?_c('van-field',{attrs:{\"required\":_vm.data.required == 1 ? true:false,\"label\":_vm.data.title,\"is-link\":true,\"placeholder\":'选择' + _vm.data.title,\"error-message\":_vm.data.explain,\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.select_goods = true}},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),(_vm.data.type == 11)?_c('van-field',{attrs:{\"label\":_vm.data.title,\"readonly\":\"\"},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [(_vm.data.unit != '')?_c('span',{staticStyle:{\"color\":\"#000\"},domProps:{\"textContent\":_vm._s('（'+_vm.data.unit+'）')}}):_vm._e()]},proxy:true}],null,false,3363291398),model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}):_vm._e(),_c('van-popup',{staticStyle:{\"height\":\"402px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.select_goods),callback:function ($$v) {_vm.select_goods=$$v},expression:\"select_goods\"}},[_c('van-picker',{attrs:{\"title\":'选择' + _vm.data.title,\"show-toolbar\":\"\",\"columns\":_vm.data.list},on:{\"cancel\":function($event){_vm.select_goods = false},\"confirm\":_vm.selectGoods},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.selectGoods(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),(_vm.data.type == 12)?[_c('van-field',{attrs:{\"required\":_vm.data.required == 1 ? true:false,\"label\":_vm.data.title,\"is-link\":true,\"placeholder\":'选择' + _vm.data.title,\"error-message\":_vm.data.explain,\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.dest_show = true}},model:{value:(_vm.data.value),callback:function ($$v) {_vm.$set(_vm.data, \"value\", $$v)},expression:\"data.value\"}}),_c('dest-field',{attrs:{\"onshow\":_vm.dest_show},on:{\"changeDest\":_vm.changeDest}})]:_vm._e(),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.datetime_show),callback:function ($$v) {_vm.datetime_show=$$v},expression:\"datetime_show\"}},[_c('van-datetime-picker',{attrs:{\"type\":\"datetime\",\"title\":'选择' + _vm.data.title,\"value\":_vm.date_date,\"min-date\":_vm.min_date,\"max-date\":_vm.max_date,\"formatter\":_vm.formatter},on:{\"cancel\":function($event){_vm.datetime_show = false},\"confirm\":_vm.datetimeChange},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.dateChange(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.date_show),callback:function ($$v) {_vm.date_show=$$v},expression:\"date_show\"}},[_c('van-datetime-picker',{attrs:{\"type\":\"date\",\"title\":'选择' + _vm.data.title,\"value\":_vm.date_date,\"min-date\":_vm.min_date,\"max-date\":_vm.max_date,\"formatter\":_vm.formatter},on:{\"cancel\":function($event){_vm.date_show = false},\"confirm\":_vm.dateChange},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.dateChange(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.date_month_show),callback:function ($$v) {_vm.date_month_show=$$v},expression:\"date_month_show\"}},[_c('van-datetime-picker',{attrs:{\"type\":\"year-month\",\"title\":'选择' + _vm.data.title,\"value\":_vm.date_date,\"min-date\":_vm.min_date,\"max-date\":_vm.max_date,\"formatter\":_vm.formatter},on:{\"cancel\":function($event){_vm.date_month_show = false},\"confirm\":_vm.datemonthChange},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.datemonthChange(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),_c('van-popup',{staticStyle:{\"height\":\"402px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.select_show),callback:function ($$v) {_vm.select_show=$$v},expression:\"select_show\"}},[_c('van-picker',{attrs:{\"title\":'选择' + _vm.data.title,\"show-toolbar\":\"\",\"columns\":_vm.data.list},on:{\"cancel\":function($event){_vm.select_show = false},\"confirm\":_vm.selectValue},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.selectValue('')}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),_c('van-number-keyboard',{attrs:{\"show\":_vm.number_show,\"theme\":\"custom\",\"extra-key\":\".\",\"z-index\":\"1050\",\"close-button-text\":\"完成\"},on:{\"blur\":function($event){_vm.number_show = false},\"input\":_vm.onNumberInput,\"delete\":_vm.onNumberDelete}})],2)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('van-popup',{staticStyle:{\"width\":\"100vw\",\"height\":\"100vh\"},attrs:{\"position\":\"right\",\"closeable\":\"\",\"close-icon-position\":\"top-left\"},on:{\"click-close-icon\":_vm.closeEdit},model:{value:(_vm.show),callback:function ($$v) {_vm.show=$$v},expression:\"show\"}},[_c('div',{staticClass:\"main\",staticStyle:{\"background-color\":\"#fff\"}},[_c('m-header',{attrs:{\"name\":\"编辑数据\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[_c('van-form',[_c('van-field',{attrs:{\"required\":\"\",\"name\":\"厂区\",\"label\":\"厂区\",\"is-link\":true,\"placeholder\":\"选择厂区\",\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.cc_show_flag = true}},model:{value:(_vm.data.cc_name),callback:function ($$v) {_vm.$set(_vm.data, \"cc_name\", $$v)},expression:\"data.cc_name\"}}),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.cc_show_flag),callback:function ($$v) {_vm.cc_show_flag=$$v},expression:\"cc_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择厂区\",\"show-toolbar\":\"\",\"columns\":_vm.cc_list},on:{\"cancel\":function($event){_vm.cc_show_flag = false},\"confirm\":_vm.selectCC},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.selectCC(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),_c('van-field',{attrs:{\"required\":\"\",\"name\":\"仓库\",\"label\":\"仓库\",\"is-link\":true,\"placeholder\":\"选择仓库\",\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.ck_show_flag = true}},model:{value:(_vm.data.ck_name),callback:function ($$v) {_vm.$set(_vm.data, \"ck_name\", $$v)},expression:\"data.ck_name\"}}),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.ck_show_flag),callback:function ($$v) {_vm.ck_show_flag=$$v},expression:\"ck_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择货物\",\"show-toolbar\":\"\",\"columns\":_vm.ck_list},on:{\"cancel\":function($event){_vm.ck_show_flag = false},\"confirm\":_vm.selectCK},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.selectCK(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),_c('van-field',{attrs:{\"required\":\"\",\"name\":\"货物\",\"label\":\"货物\",\"is-link\":true,\"placeholder\":\"选择货物\",\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.goods_show_flag = true}},model:{value:(_vm.data.goods_name),callback:function ($$v) {_vm.$set(_vm.data, \"goods_name\", $$v)},expression:\"data.goods_name\"}}),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.goods_show_flag),callback:function ($$v) {_vm.goods_show_flag=$$v},expression:\"goods_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择货物\",\"show-toolbar\":\"\",\"columns\":_vm.goods_list},on:{\"cancel\":function($event){_vm.goods_show_flag = false},\"confirm\":_vm.selectGoods},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.selectGoods(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1),_c('van-field',{attrs:{\"required\":\"\",\"type\":\"number\",\"name\":\"数量\",\"label\":\"数量\",\"is-link\":true,\"placeholder\":\"请输入数量\",\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.cnt_show = true}},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [(_vm.data.unit != '')?_c('span',{staticStyle:{\"color\":\"#000\"},domProps:{\"textContent\":_vm._s('（'+_vm.data.unit+'）')}}):_vm._e()]},proxy:true}]),model:{value:(_vm.data.cnt),callback:function ($$v) {_vm.$set(_vm.data, \"cnt\", $$v)},expression:\"data.cnt\"}}),_c('van-number-keyboard',{attrs:{\"show\":_vm.cnt_show,\"theme\":\"custom\",\"extra-key\":\".\",\"close-button-text\":\"完成\"},on:{\"blur\":function($event){_vm.cnt_show = false},\"input\":_vm.onCntInput,\"delete\":_vm.onCntDelete}}),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"width\":\"100%\",\"padding\":\"10px\",\"border-top\":\"1px #F2F2F2 solid\",\"background-color\":\"#FFFFFF\",\"z-index\":\"99\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"info\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"提交\")])],1)],1)],1)],1)])\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('van-popup',{staticClass:\"plate-main\",attrs:{\"position\":\"right\",\"closeable\":\"\",\"close-icon-position\":\"top-left\"},on:{\"click-close-icon\":_vm.closePlate,\"open\":_vm.showPlate},model:{value:(_vm.show),callback:function ($$v) {_vm.show=$$v},expression:\"show\"}},[_c('div',{staticClass:\"plate-header\"},[_vm._v(\"车牌号码录入\")]),_c('div',{staticClass:\"plate-panel\"},[_c('div',{staticClass:\"plate-panel-header\"},[_c('div',{staticClass:\"plate-panel-title\"},[_vm._v(\"车牌号码\")])]),_c('div',{staticClass:\"plate-panel-body\"},[_c('div',{staticClass:\"plate-row\"},_vm._l((_vm.plate_list),function(plate,idx){return _c('div',{staticClass:\"plate-box\",class:_vm.box_idx == idx ? 'selected' : '',domProps:{\"textContent\":_vm._s(plate)},on:{\"click\":function($event){return _vm.touchBox(idx)}}})}),0)])]),_c('div',{staticClass:\"padding\"},[_c('van-button',{attrs:{\"type\":\"info\",\"block\":\"\"},on:{\"click\":_vm.doSubmit}},[_vm._v(\"确定\")])],1),_c('van-popup',{attrs:{\"position\":\"bottom\",\"overlay\":false},model:{value:(_vm.keyboard_show),callback:function ($$v) {_vm.keyboard_show=$$v},expression:\"keyboard_show\"}},[_c('div',{staticClass:\"pop-box\"},[(_vm.box_idx == 0)?_c('div',{staticClass:\"keyboard-box header-box\"},_vm._l((_vm.plate_header_list),function(plate_header,idx){return _c('div',{key:idx,staticClass:\"keyboard-btn\",class:_vm.header_idx == idx ? 'selected' : '',on:{\"click\":function($event){return _vm.selectHeader(idx)}}},[_c('div',{staticClass:\"keyboard-text\",domProps:{\"textContent\":_vm._s(plate_header)}})])}),0):_c('div',{staticClass:\"keyboard-box\"},[_c('div',{staticClass:\"keyboard-row num-row\"},_vm._l((_vm.num_list),function(num,idx){return _c('div',{key:idx,staticClass:\"keyboard-btn\",on:{\"click\":function($event){return _vm.selectLetter(num)}}},[_c('div',{staticClass:\"keyboard-text\",domProps:{\"textContent\":_vm._s(num)}})])}),0),_vm._l((_vm.letter_list),function(letter_row,idx){return _c('div',{key:idx,staticClass:\"keyboard-row\",class:idx == _vm.letter_list.length - 1 ? 'last' : ''},[_vm._l((letter_row),function(letter,letter_idx){return _c('div',{key:letter_idx,staticClass:\"keyboard-btn\",on:{\"click\":function($event){return _vm.selectLetter(letter)}}},[_c('div',{staticClass:\"keyboard-text\",domProps:{\"textContent\":_vm._s(letter)}})])}),(idx == _vm.letter_list.length - 1)?_c('div',{staticClass:\"keyboard-btn btn-del\",on:{\"click\":_vm.doDel}},[_c('div',{staticClass:\"keyboard-text\"},[_c('div',{staticClass:\"iconfont icon-tuige\"})])]):_vm._e()],2)})],2)])])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\",staticStyle:{\"background-color\":\"#fff\"}},[_c('m-header',{attrs:{\"name\":\"发起申请\",\"is_back\":\"1\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[_c('van-form',[_c('van-field',{attrs:{\"name\":\"所属部门\",\"label\":\"所属部门\",\"placeholder\":\"\",\"readonly\":\"\"},model:{value:(_vm.group_name),callback:function ($$v) {_vm.group_name=$$v},expression:\"group_name\"}}),_c('van-field',{attrs:{\"required\":\"\",\"name\":\"业务流程\",\"label\":\"业务流程\",\"is-link\":true,\"placeholder\":\"选择业务流程\",\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.type_show_flag = true}},model:{value:(_vm.type_name),callback:function ($$v) {_vm.type_name=$$v},expression:\"type_name\"}}),_c('van-popup',{staticStyle:{\"height\":\"350px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.type_show_flag),callback:function ($$v) {_vm.type_show_flag=$$v},expression:\"type_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择业务流程\",\"show-toolbar\":\"\",\"columns\":_vm.type_list},on:{\"cancel\":function($event){_vm.type_show_flag = false},\"confirm\":_vm.selectType}})],1),_vm._l((_vm.form_data),function(item,idx){return (_vm.form_data_show)?[(item.type == 99)?_c('detail-field',{attrs:{\"required\":item.required,\"title\":item.title,\"explain\":item.explain,\"form-list\":item.list,\"value-list\":item.values}}):_c('dynamic-field',{attrs:{\"data\":item},on:{\"change\":_vm.changeValue}})]:_vm._e()}),_c('van-field',{attrs:{\"is-link\":true,\"rows\":\"2\",\"autosize\":\"\",\"type\":\"textarea\",\"name\":\"说明\",\"label\":\"说明\",\"placeholder\":\"请输入相关说明\"},model:{value:(_vm.remarks),callback:function ($$v) {_vm.remarks=$$v},expression:\"remarks\"}}),_c('van-field',{attrs:{\"name\":\"uploader\",\"label\":\"文件上传\"},scopedSlots:_vm._u([{key:\"input\",fn:function(){return [_c('input',{ref:\"fileInput\",attrs:{\"type\":\"file\",\"multiple\":\"\",\"accept\":\"image/*\",\"hidden\":\"\"},on:{\"change\":_vm.handleFileChange}}),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\"}},[_vm._l((_vm.files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"}},[_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"-5px\",\"right\":\"-5px\",\"width\":\"20px\",\"height\":\"20px\",\"background-color\":\"red\",\"border-radius\":\"20px\",\"z-index\":\"999\",\"text-align\":\"center\",\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\"},on:{\"click\":function($event){return _vm.delPhoto(i)}}},[_c('van-icon',{attrs:{\"name\":\"cross\",\"size\":\"16\",\"color\":\"#FFFFFF\"}})],1),_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":file,\"width\":\"80px\",\"height\":\"80px\"}})],1)}),(_vm.files.length < 5)?_c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"background-color\":\"#f2f2f2\",\"text-align\":\"center\",\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\"},on:{\"click\":_vm.takePhoto}},[_c('van-icon',{attrs:{\"name\":\"photograph\",\"color\":\"#bbbbbb\",\"size\":\"25\"}})],1):_vm._e()],2)]},proxy:true}])}),_c('div',{staticClass:\"btn-footer\"},[(_vm.uid!='')?_c('van-button',{staticStyle:{\"margin-right\":\"20px\"},attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"danger\"},on:{\"click\":_vm.deleteSave}},[_vm._v(\"删除\")]):_vm._e(),_c('van-button',{staticStyle:{\"margin-right\":\"20px\"},attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"info\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"提交\")])],1)],2),(_vm.flow_data.length > 0)?_c('div',{staticClass:\"content\",staticStyle:{\"margin-bottom\":\"80px\"}},[_c('div',_vm._l((_vm.flow_list),function(item,index){return _c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title-border\"},[(item.type == 4)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#3296FB\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"wap-home-o\",\"size\":\"30\"}})],1):(item.type == 5)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#FF2B2B\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"stop\",\"size\":\"30\"}})],1):(item.type == 6)?_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#FF2B2B\"}},[_c('van-icon',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"name\":\"revoke\",\"size\":\"30\"}})],1):_c('div',{staticClass:\"title\",staticStyle:{\"background-color\":\"#3296FB\"}},[_vm._v(\" \"+_vm._s(item.icon)+\" \"),(item.type == 1)?_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#4AB37E\"}},[_c('van-icon',{attrs:{\"name\":\"checked\"}})],1):(item.type == 3)?_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#4AB37E\"}},[_c('van-icon',{attrs:{\"name\":\"volume\"}})],1):_c('div',{staticClass:\"border\",staticStyle:{\"color\":\"#3296FB\"}},[_c('van-icon',{attrs:{\"name\":\"thumb-circle\"}})],1)])]),_c('div',{staticClass:\"item-content\"},[_c('div',{staticClass:\"top\"},[_c('div',{style:({flex:'1',color: item.status == 0 ? '#A2A2A2' : '#000000'})},[_c('span',[_vm._v(_vm._s(item.name))]),_c('span',[_vm._v(\" \"+_vm._s(item.val))])]),_c('div',{staticStyle:{\"width\":\"80px\",\"margin-left\":\"5px\"}},[_c('span',{staticStyle:{\"font-size\":\"13px\",\"color\":\"#898989\",\"margin-top\":\"-3px\"}},[_vm._v(_vm._s(item.time))])])]),_c('div',{staticClass:\"bottom\",style:({borderLeft:_vm.flow_list.length -1 == index ? 0:'4px #D2D2D2 solid'})},[(item.text != '')?_c('div',{staticClass:\"bottom-content\"},[_vm._v(\" \"+_vm._s(item.text)+\" \")]):_vm._e(),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"margin-left\":\"35px\",\"margin-top\":\"5px\"}},_vm._l((item.files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"},on:{\"click\":function($event){return _vm.previewImg(file)}}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":_vm.base_path+file + '!small',\"width\":\"80px\",\"height\":\"80px\"}})],1)}),0),(item.send != '')?_c('div',{staticClass:\"bottom-send\"},[_vm._v(\" \"+_vm._s(item.send)+\" \")]):_vm._e()])])])}),0),_vm._l((_vm.flow_data),function(item,idx){return _c('div',{key:idx},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"min-height\":\"60px\"}},[_c('div',{staticStyle:{\"width\":\"35%\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"position\":\"relative\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"border-right\":\"1px #D2D2D2 solid\",\"width\":\"8px\"}}),(idx == 0)?_c('div',{staticStyle:{\"height\":\"18px\",\"position\":\"absolute\",\"top\":\"0\",\"width\":\"12px\",\"background-color\":\"#FFF\"}}):_vm._e(),(idx+1 == _vm.flow_data.length)?_c('div',{staticStyle:{\"height\":\"calc(100% - 22px)\",\"position\":\"absolute\",\"top\":\"22px\",\"width\":\"12px\",\"background-color\":\"#FFF\"}}):_vm._e(),_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"15px\",\"width\":\"15px\",\"height\":\"15px\",\"background-color\":\"#aaaaaa\",\"border-radius\":\"15px\"}})]),_c('div',{staticStyle:{\"padding-top\":\"5px\",\"padding-left\":\"15px\"}},[_c('span',{staticStyle:{\"font-weight\":\"400\"},domProps:{\"textContent\":_vm._s(item.name)}}),_c('div',[_c('span',{staticStyle:{\"font-size\":\"12px\",\"color\":\"#898989\"},domProps:{\"textContent\":_vm._s(item.type_name)}})])])]),_c('div',{staticStyle:{\"width\":\"65%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"justify-content\":\"flex-end\",\"padding-right\":\"10px\",\"padding-top\":\"5px\"}},_vm._l((item.list),function(user,i){return _c('van-tag',{key:user.id,staticStyle:{\"height\":\"20px\",\"margin\":\"2px\"},attrs:{\"type\":item.type == 1 ? 'primary' : 'success',\"size\":\"large\"}},[_vm._v(_vm._s(user.name))])}),1)]),(item.nlist.length > 0)?_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"min-height\":\"60px\"}},[_c('div',{staticStyle:{\"width\":\"35%\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"height\":\"100%\",\"position\":\"relative\"}},[(idx+1 != _vm.flow_data.length)?_c('div',{staticStyle:{\"height\":\"100%\",\"border-right\":\"1px #D2D2D2 solid\",\"width\":\"8px\"}}):_c('div',{staticStyle:{\"height\":\"100%\",\"width\":\"8px\"}})]),_c('div',{staticStyle:{\"padding-top\":\"5px\",\"padding-left\":\"15px\"}},[_c('span',{staticStyle:{\"font-weight\":\"400\",\"color\":\"#898989\"}},[_vm._v(\"抄送人\")])])]),_c('div',{staticStyle:{\"width\":\"65%\",\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\",\"justify-content\":\"flex-end\",\"padding-right\":\"10px\",\"padding-top\":\"5px\"}},_vm._l((item.nlist),function(user,i){return _c('van-tag',{key:user.id,staticStyle:{\"height\":\"20px\",\"margin\":\"2px\"},attrs:{\"type\":\"default\",\"size\":\"large\"}},[_vm._v(_vm._s(user.name))])}),1)]):_vm._e()])})],2):_vm._e()],1)],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.main[data-v-245178ad] {\\n    background-color: #FAFAFA;\\n    width: 100vw;\\n    height: 100vh;\\n}\\n.header[data-v-245178ad] {\\n    text-align: center;\\n    height: 54px;\\n    line-height: 54px;\\n    background-color: #0081FF;\\n    color: #FFFFFF;\\n}\\n.content[data-v-245178ad]{\\n    min-height: 100px;\\n    background-color: #FFFFFF;\\n    margin: 15px;\\n    padding: 15px;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 10px;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.main[data-v-84c2f4c8] {\\n    background-color: #FAFAFA;\\n    width: 100vw;\\n    height: 100vh;\\n    overflow: hidden;\\n}\\n.header[data-v-84c2f4c8] {\\n    text-align: center;\\n    height: 54px;\\n    line-height: 54px;\\n    background-color: #0081FF;\\n    color: #FFFFFF;\\n}\\n.content[data-v-84c2f4c8]{\\n    min-height: 100px;\\n    background-color: #FFFFFF;\\n    margin: 15px;\\n    padding: 15px;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 10px;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.plate-main .van-icon-cross::before {\\n    color: #FFFFFF;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.plate-main[data-v-01ada794] {\\n    background-color: #FAFAFA;\\n    width: 100vw;\\n    height: 100vh;\\n    overflow: hidden;\\n}\\n.plate-header[data-v-01ada794] {\\n    text-align: center;\\n    height: 54px;\\n    line-height: 54px;\\n    background-color: #0081FF;\\n    color: #FFFFFF;\\n}\\n.plate-panel[data-v-01ada794] {\\n    background-color: #FFFFFF;\\n    display: flex;\\n    flex-direction: column;\\n}\\n.plate-panel-header[data-v-01ada794] {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    font-size: 16px;\\n    padding: 15px 15px 15px 5px;\\n    border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n.plate-panel-title[data-v-01ada794] {\\n    display: flex;\\n    align-items: center;\\n}\\n.plate-panel-title[data-v-01ada794]::before {\\n    content: '';\\n    display: block;\\n    width: 8px;\\n    height: 8px;\\n    border-radius: 100% !important;\\n    background-color: #236eeb;\\n    margin: 0 10px;\\n}\\n.plate-panel-body[data-v-01ada794] {\\n    padding: 15px;\\n}\\n.plate-row[data-v-01ada794] {\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: center;\\n    align-items: center;\\n    font-size: 30px;\\n    color: #333;\\n    margin: 15px 0 25px;\\n}\\n.plate-box[data-v-01ada794] {\\n    border: 1px solid #ddd;\\n    height: 45px;\\n    width: 35px;\\n    margin: 0 5px;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    box-sizing: border-box;\\n}\\n.plate-box.selected[data-v-01ada794] {\\n    border: 2px solid #3197f3;\\n}\\n.vehicle-box[data-v-01ada794] {\\n    overflow-y: auto;\\n    max-height: 192px;\\n    margin-left: -5px;\\n    margin-right: -5px;\\n}\\n.vehicle-box .item-plate[data-v-01ada794] {\\n    float: left;\\n    padding: 0 5px;\\n    font-size: 17px;\\n    width: 33.333333%;\\n    box-sizing: border-box;\\n    margin-bottom: 10px;\\n}\\n.item-plate .plate-no[data-v-01ada794] {\\n    background-color: #eee;\\n    box-sizing: border-box;\\n    height: 38px;\\n    color: #333;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n}\\n.plate-footer[data-v-01ada794] {\\n    padding: 8px 5px 5px;\\n    position: absolute;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    z-index: 1;\\n    transition: all 400ms;\\n}\\n.plate-footer.hide[data-v-01ada794] {\\n    bottom: -50px;\\n}\\n.van-popup[data-v-01ada794] {\\n    box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.1);\\n}\\n.pop-box[data-v-01ada794] {\\n    padding: 8px 5px 5px;\\n    background-color: #f2f2f2;\\n}\\n.keyboard-box[data-v-01ada794] {\\n    overflow: hidden;\\n}\\n.header-box .keyboard-btn[data-v-01ada794] {\\n    float: left;\\n    box-sizing: border-box;\\n    width: 14.285714%;\\n    padding: 5px;\\n}\\n.header-box .keyboard-btn.selected .keyboard-text[data-v-01ada794] {\\n    background-color: #3197f3;\\n    color: #FFFFFF;\\n}\\n.keyboard-text[data-v-01ada794] {\\n    box-sizing: border-box;\\n    background-color: #FFFFFF;\\n    text-align: center;\\n    padding: 4px 0;\\n    border-radius: 5px;\\n    box-shadow: 0 2px 0 #aaaaaa;\\n}\\n.keyboard-row[data-v-01ada794] {\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: center;\\n    margin-bottom: 10px;\\n}\\n.keyboard-row.last[data-v-01ada794] {\\n    margin-bottom: 0;\\n}\\n.keyboard-row .keyboard-text[data-v-01ada794] {\\n    width: 1.8em;\\n    height: 35px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n}\\n.keyboard-btn[data-v-01ada794] {\\n    padding: 5px;\\n}\\n.btn-del .keyboard-text[data-v-01ada794] {\\n    width: 50px;\\n    background-color: #99b4cc;\\n    color: #FFFFFF;\\n}\\n.num-row .keyboard-btn[data-v-01ada794] {\\n    padding: 5px 4px;\\n}\\n.pop-footer[data-v-01ada794] {\\n    padding-top: 8px;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.content[data-v-37ddd0b2]{\\n    min-height: 100px;\\n    background-color: #FFFFFF;\\n    margin: 15px;\\n    padding: 15px;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 10px;\\n}\\n.item[data-v-37ddd0b2]{\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-start;\\n    margin-top: 4px;\\n}\\n.item .title[data-v-37ddd0b2]{\\n    width: 45px;\\n    height: 45px;\\n    line-height: 45px;\\n    vertical-align: middle;\\n    background-color: #3296FB;\\n    border: 1px #E2E2E2 solid;\\n    border-radius: 5px;\\n    color: #FFFFFF;\\n    text-align: center;\\n    position: relative;\\n    font-size: 16px;\\n}\\n.item .title .border[data-v-37ddd0b2]{\\n    position: absolute;\\n    top: 31px;\\n    left: 31px;\\n    width: 18px;\\n    height: 18px;\\n    line-height: 18px;\\n    border-radius: 9px;\\n    background-color: #FFFFFF;\\n    text-align: center;\\n    padding-top: 1px;\\n}\\n.item .item-content[data-v-37ddd0b2]{\\n    width: 100%;\\n    margin-left: 15px;\\n    color: #888888;\\n}\\n.item .item-content .top[data-v-37ddd0b2] {\\n    min-height: 20px;\\n    line-height: 20px;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: space-between;\\n    font-size: 16px;\\n    margin-top: 2px;\\n    vertical-align: top;\\n}\\n.item .item-content .bottom[data-v-37ddd0b2] {\\n    margin-left: -38px;\\n    border-left: 4px #D2D2D2 solid;\\n    padding-bottom: 10px;\\n    min-height: 55px;\\n}\\n.item .bottom-content[data-v-37ddd0b2]{\\n    margin-left: 35px;\\n    margin-top:5px;\\n    min-height: 40px;\\n    background-color: #F2F2F2;\\n    border-radius: 5px;\\n    padding: 10px;\\n    color: #000000;\\n}\\n.item .bottom-send[data-v-37ddd0b2]{\\n    margin-left: 35px;\\n    margin-top:5px;\\n    font-size: 14px;\\n}\\n.item .title-border[data-v-37ddd0b2]{\\n    background-color: #FFFFFF;\\n    width: 45px;\\n    height: 52px;\\n    z-index: 999;\\n}\\n.btn-footer[data-v-37ddd0b2]{\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    background-color: #FFFFFF;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: center;\\n    padding-top: 10px;\\n    padding-bottom: 10px;\\n    padding-left: 20px;\\n    z-index: 1000;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../node_modules/css-loader/dist/runtime/api.js\";\nimport ___CSS_LOADER_GET_URL_IMPORT___ from \"../../../../node_modules/css-loader/dist/runtime/getUrl.js\";\nvar ___CSS_LOADER_URL_IMPORT_0___ = new URL(\"\", import.meta.url);\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___, { hash: \"?#iefix\" });\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@font-face {\\r\\n    font-family: 'iconfont';  /* project id 3162763 */\\r\\n    src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \") format('embedded-opentype'),\\r\\n    url('//at.alicdn.com/t/font_3162763_wiihobta1tq.woff2') format('woff2'),\\r\\n    url('//at.alicdn.com/t/font_3162763_wiihobta1tq.woff') format('woff'),\\r\\n    url('//at.alicdn.com/t/font_3162763_wiihobta1tq.ttf') format('truetype'),\\r\\n    url('#iconfont') format('svg');\\r\\n}\\r\\n\\r\\n.iconfont {\\r\\n  font-family: \\\"iconfont\\\" !important;\\r\\n  font-size: 16px;\\r\\n  font-style: normal;\\r\\n  -webkit-font-smoothing: antialiased;\\r\\n  -moz-osx-font-smoothing: grayscale;\\r\\n}\\r\\n\\r\\n.icon-tuige:before {\\r\\n  content: \\\"\\\\e640\\\";\\r\\n}\\r\\n\\r\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dest_field.vue?vue&type=style&index=0&id=245178ad&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"82fadede\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dest_field.vue?vue&type=style&index=0&id=245178ad&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dest_field.vue?vue&type=style&index=0&id=245178ad&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail_field.vue?vue&type=style&index=0&id=84c2f4c8&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"3cf4c3e3\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail_field.vue?vue&type=style&index=0&id=84c2f4c8&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail_field.vue?vue&type=style&index=0&id=84c2f4c8&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=0&id=01ada794&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"735f3af7\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=0&id=01ada794&lang=css\", function() {\n     var newContent = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=0&id=01ada794&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=1&id=01ada794&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"617b344d\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=1&id=01ada794&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=1&id=01ada794&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./request.vue?vue&type=style&index=0&id=37ddd0b2&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"d5d01be8\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./request.vue?vue&type=style&index=0&id=37ddd0b2&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./request.vue?vue&type=style&index=0&id=37ddd0b2&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./dest_field.vue?vue&type=template&id=245178ad&scoped=true\"\nimport script from \"./dest_field.vue?vue&type=script&lang=js\"\nexport * from \"./dest_field.vue?vue&type=script&lang=js\"\nimport style0 from \"./dest_field.vue?vue&type=style&index=0&id=245178ad&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"245178ad\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('245178ad')) {\n      api.createRecord('245178ad', component.options)\n    } else {\n      api.reload('245178ad', component.options)\n    }\n    module.hot.accept(\"./dest_field.vue?vue&type=template&id=245178ad&scoped=true\", function () {\n      api.rerender('245178ad', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/dest_field.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dest_field.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dest_field.vue?vue&type=script&lang=js\"", "export * from \"-!../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dest_field.vue?vue&type=style&index=0&id=245178ad&scoped=true&lang=css\"", "export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dest_field.vue?vue&type=template&id=245178ad&scoped=true\"", "import { render, staticRenderFns } from \"./detail_field.vue?vue&type=template&id=84c2f4c8&scoped=true\"\nimport script from \"./detail_field.vue?vue&type=script&lang=js\"\nexport * from \"./detail_field.vue?vue&type=script&lang=js\"\nimport style0 from \"./detail_field.vue?vue&type=style&index=0&id=84c2f4c8&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"84c2f4c8\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('84c2f4c8')) {\n      api.createRecord('84c2f4c8', component.options)\n    } else {\n      api.reload('84c2f4c8', component.options)\n    }\n    module.hot.accept(\"./detail_field.vue?vue&type=template&id=84c2f4c8&scoped=true\", function () {\n      api.rerender('84c2f4c8', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/detail_field.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail_field.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail_field.vue?vue&type=script&lang=js\"", "export * from \"-!../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail_field.vue?vue&type=style&index=0&id=84c2f4c8&scoped=true&lang=css\"", "export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail_field.vue?vue&type=template&id=84c2f4c8&scoped=true\"", "import { render, staticRenderFns } from \"./dynamic_field.vue?vue&type=template&id=43e5b01a&scoped=true\"\nimport script from \"./dynamic_field.vue?vue&type=script&lang=js\"\nexport * from \"./dynamic_field.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43e5b01a\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('43e5b01a')) {\n      api.createRecord('43e5b01a', component.options)\n    } else {\n      api.reload('43e5b01a', component.options)\n    }\n    module.hot.accept(\"./dynamic_field.vue?vue&type=template&id=43e5b01a&scoped=true\", function () {\n      api.rerender('43e5b01a', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/dynamic_field.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dynamic_field.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dynamic_field.vue?vue&type=script&lang=js\"", "export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./dynamic_field.vue?vue&type=template&id=43e5b01a&scoped=true\"", "import { render, staticRenderFns } from \"./edit_goods.vue?vue&type=template&id=5b1ba31e&scoped=true\"\nimport script from \"./edit_goods.vue?vue&type=script&lang=js\"\nexport * from \"./edit_goods.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b1ba31e\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('5b1ba31e')) {\n      api.createRecord('5b1ba31e', component.options)\n    } else {\n      api.reload('5b1ba31e', component.options)\n    }\n    module.hot.accept(\"./edit_goods.vue?vue&type=template&id=5b1ba31e&scoped=true\", function () {\n      api.rerender('5b1ba31e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/edit_goods.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit_goods.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit_goods.vue?vue&type=script&lang=js\"", "export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./edit_goods.vue?vue&type=template&id=5b1ba31e&scoped=true\"", "import { render, staticRenderFns } from \"./plate.vue?vue&type=template&id=01ada794&scoped=true\"\nimport script from \"./plate.vue?vue&type=script&lang=js\"\nexport * from \"./plate.vue?vue&type=script&lang=js\"\nimport style0 from \"./plate.vue?vue&type=style&index=0&id=01ada794&lang=css\"\nimport style1 from \"./plate.vue?vue&type=style&index=1&id=01ada794&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"01ada794\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('01ada794')) {\n      api.createRecord('01ada794', component.options)\n    } else {\n      api.reload('01ada794', component.options)\n    }\n    module.hot.accept(\"./plate.vue?vue&type=template&id=01ada794&scoped=true\", function () {\n      api.rerender('01ada794', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/plate.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=script&lang=js\"", "export * from \"-!../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=0&id=01ada794&lang=css\"", "export * from \"-!../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=style&index=1&id=01ada794&scoped=true&lang=css\"", "export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./plate.vue?vue&type=template&id=01ada794&scoped=true\"", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-14.use[1]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-14.use[2]!./iconfont.css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"362e08f9\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-14.use[1]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-14.use[2]!./iconfont.css\", function() {\n     var newContent = require(\"!!../../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-14.use[1]!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-14.use[2]!./iconfont.css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./request.vue?vue&type=template&id=37ddd0b2&scoped=true\"\nimport script from \"./request.vue?vue&type=script&lang=js\"\nexport * from \"./request.vue?vue&type=script&lang=js\"\nimport style0 from \"./request.vue?vue&type=style&index=0&id=37ddd0b2&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"37ddd0b2\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('37ddd0b2')) {\n      api.createRecord('37ddd0b2', component.options)\n    } else {\n      api.reload('37ddd0b2', component.options)\n    }\n    module.hot.accept(\"./request.vue?vue&type=template&id=37ddd0b2&scoped=true\", function () {\n      api.rerender('37ddd0b2', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/work/request.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./request.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./request.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./request.vue?vue&type=style&index=0&id=37ddd0b2&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./request.vue?vue&type=template&id=37ddd0b2&scoped=true\""], "names": [], "sourceRoot": ""}