{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-4">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">基本信息</span>
                    </div>
                </div>
                <div class="portlet-body form" >
                    <form id="form" class="form-horizontal">
                        <div class="form-body"  style="height: 73vh;overflow-y: auto;overflow-x: hidden">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">生产批次号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">客户</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="customer_name" v-model="customer_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品名称</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_name" v-model="product_name" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">产品规格</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="product_code" v-model="product_code" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">计划开工日</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="plan_begin_date" v-model="plan_begin_date" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">计划完工日</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="plan_end_date" v-model="plan_end_date" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" readonly placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-8">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">生产明细</span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center"></div>
                </div>
                <div class="portlet-body" style="height: 80vh;overflow-y: auto">
                    <div class="col-sm-12">
                        <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                            <thead>
                            <tr>
                                <th>工艺名称</th>
                                <th>工序号</th>
                                <th>开工日期</th>
                                <th>完工日期</th>
                                <th>计划生产数量</th>
                                <th>排产数量</th>
                                <th>生产数量</th>
                                <th>不良数量</th>
                                <th>不良率</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="row, index in bom_list">
                                <td>
                                    <span v-text="row.bom_name"></span>
                                    <span v-if="row.entrust_flag == 1" class="label label-warning">外委</span>
                                </td>
                                <td>
                                    <span v-text="row.bom_code"></span>
                                </td>
                                <td>
                                    <span v-text="row.start_date"></span>
                                </td>
                                <td>
                                    <span v-text="row.end_date"></span>
                                </td>
                                <td>
                                    <span v-text="row.quantity"></span>
                                </td>
                                <td>
                                    <span v-text="row.plan_cnt"></span>
                                </td>
                                <td>
                                    <span v-text="row.produce_cnt"></span>
                                </td>
                                <td>
                                    <span v-text="row.error_cnt"></span>
                                </td>
                                <td>
                                    <span v-text="row.error_rate + '%'"></span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="row" v-if="bom_list.length == 0" style="text-align: center;margin-top: 15px">
                            <span>没有数据</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {

        }
    });

</script>
<style>
    .tb-left {
        position: sticky;
        left: 0;
        z-index: 1;
    }

    .left-1 {
        border-left: 0 !important;
    }
    .left-2 {
        left: 220px;
    }
    .left-3 {
        left: 380px;
    }
    .left-4 {
        left: 480px;
    }
    .left-5 {
        left: 580px;
    }

    thead .tb-left {
        background-color: #3598DC !important;
    }

    .zh-table-box th, .zh-table-box td {
        text-align: left;
    }

    .zh-table-box table tbody .tb-left {
        border: 0 !important;
        padding: 0 !important;
    }

    .zh-table-box table tbody td {
        background-color: #ffffff;
    }

    .zh-table-box table tbody .tb-left > div {
        padding: 8px;
        border-right: 1px solid #e7ecf1;
        border-bottom: 1px solid #e7ecf1;
        line-height: 36px;
        height: 54px;
    }

    .zh-table-box table tbody .left-1 > div {
        border-left: 1px solid #e7ecf1;
    }

    .btn-del {
        width: 18px;
        height: 18px;
        font-size: 13px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 100%;
        margin-left: 5px;
        background-color: #e7505a;
        color: #FFFFFF;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .btn-del:hover {
        background-color: #e12330;
    }

    .btn-del:active {
        background-color: #c51b26;
    }
</style>