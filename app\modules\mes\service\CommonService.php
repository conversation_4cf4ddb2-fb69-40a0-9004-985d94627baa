<?php

namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Phalcon\Mvc\User\Component;

class CommonService extends Component
{
    public function getWorkMonth($date){
        return date('Y-m', strtotime($date));
    }

    public function getWorkDate(){
        $start_hour = 7;
        $hour = CvtUtil::emptyToInt(date('H'));
        if ($hour >= 0 && $hour < $start_hour){
            return DateUtil::yesterday();
        } else {
            return DateUtil::today();
        }
    }

    public function getShiftType(){
        $hour = CvtUtil::emptyToInt(date('H'));
        if ($hour < 7 || $hour > 17){
            return 2;
        }
        return 1;
    }

    public function getCheckDataValue($form_data){
        $form_data_val = [];
        foreach ($form_data as $form_item){
            if ($form_item['type'] == 1 || $form_item['type'] == 3  || $form_item['type'] == 5) {
                $form_data_val[$form_item['id']] = $form_item['value'];
            } else if ($form_item['type'] == 2 || $form_item['type'] == 7 || $form_item['type'] == 8) {
                $values = '';
                foreach ($form_item['values'] as $value){
                    if ($value != ''){
                        $values .=  CvtUtil::emptyToDouble($value) . ',';
                    }
                }
                $form_data_val[$form_item['id']] = rtrim($values,',');
            } else if ($form_item['type'] == 6) {
                $values = '';
                foreach ($form_item['values'] as $value) {
                    $values .=  $form_item['list'][CvtUtil::emptyToInt($value)] . ',';
                }
                $form_data_val[$form_item['id']] = rtrim($values,',');
            } else if ($form_item['type'] == 4) {
                $values = '';
                foreach ($form_item['values'] as $value) {
                    $values .=  $value . ',';
                }
                $form_data_val[$form_item['id']] = rtrim($values,',');
            }
        }
        return $form_data_val;
    }
}