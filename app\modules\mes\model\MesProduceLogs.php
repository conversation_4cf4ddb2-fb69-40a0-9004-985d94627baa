<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesProduceLogs extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $work_month;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $work_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $notice_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $bom_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $equ_id;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $cnt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $error_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $error_type;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $error_remarks;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $hour;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $cost;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $one_cost;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $error_money;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $shift_type;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $work_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $ww_instock_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $staff_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $staff_name;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $staff_cost;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $wages_ratio;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_time;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_produce_logs';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProduceLogs[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProduceLogs
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
