{"version": 3, "file": "js/src_view_produce_report_vue.js", "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACvYA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/produce/report.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/produce/report.vue", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/produce/report.vue?7319", "webpack://rrts-manager/./src/view/produce/report.vue?cccd", "webpack://rrts-manager/./src/view/produce/report.vue?d09b"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"生产报工\" is_back=\"1\"></m-header>\r\n        <m-body padding=\"false\">\r\n            <div v-if=\"code_type == ''\" style=\"padding-top: 200px;text-align: center\">\r\n                <van-loading type=\"spinner\" color=\"#1989fa\" />\r\n            </div>\r\n            <van-cell-group v-if=\"code_type == 1\">\r\n                <van-cell title=\"生产批次号\" :value=\"notice_data.notice_code\" />\r\n                <van-cell title=\"产品名称\" :value=\"notice_data.product_name\" />\r\n                <van-cell title=\"规格型号\" :value=\"notice_data.product_code\" />\r\n                <van-field\r\n                        @click-input=\"bom_show_flag = true\"\r\n                        required\r\n                        name=\"生产工艺\"\r\n                        label=\"生产工艺\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"notice_data.bom_name\"\r\n                        placeholder=\"选择生产工艺\"\r\n                        readonly\r\n                        input-align=\"right\"\r\n                />\r\n                <van-popup\r\n                        v-model=\"bom_show_flag\"\r\n                        position=\"bottom\"\r\n                        style=\"height: 350px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                    <van-picker\r\n                            title=\"选择生产工艺\"\r\n                            show-toolbar\r\n                            value-key=\"name\"\r\n                            :columns=\"notice_data.bom_list\"\r\n                            @cancel= \"bom_show_flag = false\"\r\n                            @confirm=\"selectBom\"\r\n                    />\r\n                </van-popup>\r\n            </van-cell-group>\r\n            <van-cell-group v-if=\"code_type == 2\">\r\n                <van-cell title=\"设备编号\" :value=\"plan_data.que_code\" />\r\n                <van-cell title=\"设备名称\" :value=\"plan_data.que_name\" />\r\n                <van-cell title=\"设备类型\" :value=\"plan_data.que_type_name\" />\r\n                <van-field\r\n                    @click-input=\"plan_show_flag = true\"\r\n                    required\r\n                    name=\"生产计划\"\r\n                    label=\"生产计划\"\r\n                    :is-link=\"true\"\r\n                    placeholder=\"选择生产计划\"\r\n                    readonly\r\n                    input-align=\"right\"\r\n                />\r\n                <van-popup\r\n                        v-model=\"plan_show_flag\"\r\n                        position=\"bottom\"\r\n                        style=\"height: 350px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                    <van-picker\r\n                        title=\"选择生产计划\"\r\n                        show-toolbar\r\n                        value-key=\"name\"\r\n                        :columns=\"plan_data.plan_list\"\r\n                        @cancel= \"plan_show_flag = false\"\r\n                        @confirm=\"selectPlan\"/>\r\n                </van-popup>\r\n                <van-cell title=\"生产批次号\" :value=\"plan_data.notice_code\"/>\r\n                <van-cell title=\"产品名称\" :value=\"plan_data.product_name\"/>\r\n                <van-cell title=\"规格型号\" :value=\"plan_data.product_code\"/>\r\n                <van-cell title=\"生产工艺\" :value=\"plan_data.bom_name\"/>\r\n            </van-cell-group>\r\n            <div v-if=\"code_type != ''\">\r\n                <van-cell-group>\r\n                    <van-field\r\n                        required\r\n                        @click-input=\"cnt_show = true\"\r\n                        type=\"number\"\r\n                        name=\"合格数量\"\r\n                        label=\"合格数量\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"produce_cnt\"\r\n                        placeholder=\"请输入合格数量\"\r\n                        readonly\r\n                        input-align=\"right\"\r\n                    >\r\n                        <template #button>\r\n                            <span>(件)</span>\r\n                        </template>\r\n                    </van-field>\r\n                    <van-number-keyboard\r\n                        :show=\"cnt_show\"\r\n                        theme=\"custom\"\r\n                        extra-key=\".\"\r\n                        close-button-text=\"完成\"\r\n                        @blur=\"cnt_show = false\"\r\n                        @input=\"onCntInput\"\r\n                        @delete=\"onCntDelete\"\r\n                    />\r\n                    <van-field\r\n                        required\r\n                        @click-input=\"hour_show = true\"\r\n                        type=\"number\"\r\n                        name=\"生产时长\"\r\n                        label=\"生产时长\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"produce_hour\"\r\n                        placeholder=\"请输入生产时长\"\r\n                        readonly\r\n                        input-align=\"right\"\r\n                    >\r\n                        <template #button>\r\n                            <span>(小时)</span>\r\n                        </template>\r\n                    </van-field>\r\n                    <van-number-keyboard\r\n                            :show=\"hour_show\"\r\n                            theme=\"custom\"\r\n                            extra-key=\".\"\r\n                            close-button-text=\"完成\"\r\n                            @blur=\"hour_show = false\"\r\n                            @input=\"onHourInput\"\r\n                            @delete=\"onHourDelete\"\r\n                    />\r\n\r\n                    <van-field\r\n                            required\r\n                            @click-input=\"error_show = true\"\r\n                            type=\"number\"\r\n                            name=\"不合格数量\"\r\n                            label=\"不合格数量\"\r\n                            :is-link=\"true\"\r\n                            v-model=\"error_cnt\"\r\n                            placeholder=\"请输入不合格数量\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                            style=\"color: red\"\r\n                    >\r\n                        <template #button>\r\n                            <span>(件)</span>\r\n                        </template>\r\n                    </van-field>\r\n                    <van-number-keyboard\r\n                            :show=\"error_show\"\r\n                            theme=\"custom\"\r\n                            extra-key=\".\"\r\n                            close-button-text=\"完成\"\r\n                            @blur=\"error_show = false\"\r\n                            @input=\"onErrorInput\"\r\n                            @delete=\"onErrorDelete\"\r\n                    />\r\n                    <van-field\r\n                            @click-input=\"type_show_flag = true\"\r\n                            name=\"不合格类型\"\r\n                            label=\"不合格类型\"\r\n                            :is-link=\"true\"\r\n                            placeholder=\"选择不合格类型\"\r\n                            v-model=\"error_type\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                    />\r\n                    <van-popup\r\n                            v-model=\"type_show_flag\"\r\n                            position=\"bottom\"\r\n                            style=\"height: 350px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <van-picker\r\n                                title=\"选择不合格类型\"\r\n                                show-toolbar\r\n                                value-key=\"name\"\r\n                                :columns=\"error_types\"\r\n                                @cancel= \"type_show_flag = false\"\r\n                                @confirm=\"selectType\"/>\r\n                    </van-popup>\r\n                    <van-field\r\n                            :is-link=\"true\"\r\n                            v-model=\"error_remarks\"\r\n                            rows=\"2\"\r\n                            autosize\r\n                            type=\"textarea\"\r\n                            name=\"不合格原因\"\r\n                            label=\"不合格原因\"\r\n                            input-align=\"right\"\r\n                            placeholder=\"请输入不合格原因\"\r\n                    />\r\n                </van-cell-group>\r\n                <div style=\"position: absolute;bottom:0;width: 100%;padding: 10px;border-top: 1px #F2F2F2 solid;background-color: #FFFFFF;z-index: 99;\">\r\n                    <van-button  round block type=\"info\" @click=\"onSubmit\">提交</van-button>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {Dialog} from \"vant\";\r\n    import base from '../../components/base';\r\n    export default {\r\n        name: \"produceReport\",\r\n        extends: base,\r\n        components: {},\r\n        data() {\r\n            return {\r\n                code:'',\r\n                loading: true,\r\n                notice_data: {},\r\n                plan_data: {},\r\n                code_type : '',\r\n                produce_cnt: '',\r\n                produce_hour: '',\r\n                error_cnt:'',\r\n                type_show_flag:false,\r\n                bom_show_flag:false,\r\n                plan_show_flag:false,\r\n                hour_show:false,\r\n                cnt_show:false,\r\n                error_show:false,\r\n                error_types : [],\r\n                error_type : '',\r\n                error_remarks : ''\r\n            }\r\n        },\r\n        methods: {\r\n            onLoad() {\r\n                this.code_type = '';\r\n                this.code = this.$route.params.code;\r\n                this.init();\r\n            },\r\n            onShow() {\r\n\r\n            },\r\n            init(){\r\n                this.$http.post('/work/produce/init',{code:this.code}).then((rs) => {\r\n                    if (rs.status === 'ok') {\r\n                        if (rs.data.notice_data != null){\r\n                            this.code_type = 1;\r\n                            this.notice_data = rs.data.notice_data;\r\n                        } else if (rs.data.plan_data != null){\r\n                            this.code_type = 2;\r\n                            this.plan_data = rs.data.plan_data;\r\n                        }\r\n                        this.error_types  =  rs.data.error_types;\r\n                        this.error_type = '';\r\n                        this.produce_cnt = '';\r\n                        this.produce_hour = '';\r\n                        this.error_cnt = '';\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '异常消息',\r\n                            message: rs.message,\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                }).catch(() => {\r\n                    Dialog.alert({\r\n                        title: '网络异常',\r\n                        message: '网络异常',\r\n                    }).then(() => {\r\n                        this.$router.back();\r\n                    });\r\n                });\r\n            },\r\n            onSubmit(){\r\n                if (this.code_type == ''){\r\n                    return;\r\n                }\r\n                if (this.code_type == 1){\r\n                    if (this.notice_data.bom_id == ''){\r\n                        this.$toast.fail('请选择工艺');\r\n                        return;\r\n                    }\r\n                } else{\r\n                    if (this.plan_data.bom_id == ''){\r\n                        this.$toast.fail('请选择生产计划');\r\n                        return;\r\n                    }\r\n                }\r\n                if (this.produce_cnt == ''){\r\n                    this.$toast.fail('请输入合格数量');\r\n                    return;\r\n                }\r\n                if (this.produce_hour == ''){\r\n                    this.$toast.fail('请输入生产时长');\r\n                    return;\r\n                }\r\n                if (this.error_cnt == ''){\r\n                    this.$toast.fail('请输入不合格数量');\r\n                    return;\r\n                } else if (this.error_cnt != 0 ) {\r\n                    if (this.error_type == ''){\r\n                       this.$toast.fail('请选择不合格类型');\r\n                       return;\r\n                   }\r\n                   if (this.error_remarks == ''){\r\n                       this.$toast.fail('请输入不合格原因');\r\n                       return;\r\n                   }\r\n                }\r\n\r\n                this.$http.post('work/produce/produce', {\r\n                    notice_detail_id: this.code_type == 1 ? this.notice_data.notice_detail_id : this.plan_data.notice_detail_id,\r\n                    bom_id: this.code_type == 1 ? this.notice_data.bom_id : this.plan_data.bom_id,\r\n                    produce_cnt:this.produce_cnt,\r\n                    produce_hour:this.produce_hour,\r\n                    error_cnt:this.error_cnt,\r\n                    error_type:this.error_type,\r\n                    error_remarks:this.error_remarks\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.$toast.success('提交成功');\r\n                        this.$router.back();\r\n                    } else {\r\n                        this.$toast.fail(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$toast.fail('网络异常');\r\n                });\r\n            },\r\n            selectBom(obj){\r\n                if (this.notice_data.bom_id == obj.id){\r\n                    this.bom_show_flag = false;\r\n                    return;\r\n                }\r\n                this.notice_data.bom_id = obj.id;\r\n                this.notice_data.bom_name = obj.name;\r\n                this.bom_show_flag = false;\r\n            },\r\n            selectPlan(obj){\r\n                if (this.plan_data.plan_id == obj.id){\r\n                    this.plan_show_flag = false;\r\n                    return;\r\n                }\r\n                this.plan_data.plan_id = obj.id;\r\n                this.plan_data.notice_detail_id = obj.notice_detail_id;\r\n                this.plan_data.notice_code = obj.notice_code;\r\n                this.plan_data.product_code = obj.product_code;\r\n                this.plan_data.product_name = obj.product_name;\r\n                this.plan_data.bom_id = obj.bom_id;\r\n                this.plan_data.bom_name = obj.bom_name;\r\n                this.plan_show_flag = false;\r\n            },\r\n            selectType(obj){\r\n                if (this.error_type == obj.name){\r\n                    this.type_show_flag = false;\r\n                    return;\r\n                }\r\n                this.error_type = obj.name;\r\n                this.type_show_flag = false;\r\n            },\r\n            onCntInput(v){\r\n                try{\r\n                    if (parseInt(this.produce_cnt) > 9999){\r\n                        return;\r\n                    }\r\n                    this.produce_cnt = this.produce_cnt + '' + v;\r\n                }catch (e){}\r\n            },\r\n            onCntDelete(v){\r\n                if (this.produce_cnt == ''){\r\n                    return;\r\n                }\r\n                this.produce_cnt = this.produce_cnt.substring(0,this.produce_cnt.length-1);\r\n            },\r\n            onHourInput(v){\r\n                try{\r\n                    if (parseFloat(this.produce_hour) > 99){\r\n                        return;\r\n                    }\r\n                    this.produce_hour = this.produce_hour + '' + v;\r\n                }catch (e){}\r\n            },\r\n            onHourDelete(v){\r\n                if (this.produce_hour == ''){\r\n                    return;\r\n                }\r\n                this.produce_hour = this.produce_hour.substring(0,this.produce_hour.length-1);\r\n            },\r\n            onErrorInput(v){\r\n                try{\r\n                    if (parseFloat(this.error_cnt) > 99){\r\n                        return;\r\n                    }\r\n                    this.error_cnt = this.error_cnt + '' + v;\r\n                }catch (e){}\r\n            },\r\n            onErrorDelete(v){\r\n                if (this.error_cnt == ''){\r\n                    return;\r\n                }\r\n                this.error_cnt = this.error_cnt.substring(0,this.error_cnt.length-1);\r\n                if (this.error_cnt == ''){\r\n                    this.error_type = '';\r\n                    this.error_remarks = '';\r\n                }\r\n            },\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"生产报工\",\"is_back\":\"1\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[(_vm.code_type == '')?_c('div',{staticStyle:{\"padding-top\":\"200px\",\"text-align\":\"center\"}},[_c('van-loading',{attrs:{\"type\":\"spinner\",\"color\":\"#1989fa\"}})],1):_vm._e(),(_vm.code_type == 1)?_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"生产批次号\",\"value\":_vm.notice_data.notice_code}}),_c('van-cell',{attrs:{\"title\":\"产品名称\",\"value\":_vm.notice_data.product_name}}),_c('van-cell',{attrs:{\"title\":\"规格型号\",\"value\":_vm.notice_data.product_code}}),_c('van-field',{attrs:{\"required\":\"\",\"name\":\"生产工艺\",\"label\":\"生产工艺\",\"is-link\":true,\"placeholder\":\"选择生产工艺\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.bom_show_flag = true}},model:{value:(_vm.notice_data.bom_name),callback:function ($$v) {_vm.$set(_vm.notice_data, \"bom_name\", $$v)},expression:\"notice_data.bom_name\"}}),_c('van-popup',{staticStyle:{\"height\":\"350px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.bom_show_flag),callback:function ($$v) {_vm.bom_show_flag=$$v},expression:\"bom_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择生产工艺\",\"show-toolbar\":\"\",\"value-key\":\"name\",\"columns\":_vm.notice_data.bom_list},on:{\"cancel\":function($event){_vm.bom_show_flag = false},\"confirm\":_vm.selectBom}})],1)],1):_vm._e(),(_vm.code_type == 2)?_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"设备编号\",\"value\":_vm.plan_data.que_code}}),_c('van-cell',{attrs:{\"title\":\"设备名称\",\"value\":_vm.plan_data.que_name}}),_c('van-cell',{attrs:{\"title\":\"设备类型\",\"value\":_vm.plan_data.que_type_name}}),_c('van-field',{attrs:{\"required\":\"\",\"name\":\"生产计划\",\"label\":\"生产计划\",\"is-link\":true,\"placeholder\":\"选择生产计划\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.plan_show_flag = true}}}),_c('van-popup',{staticStyle:{\"height\":\"350px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.plan_show_flag),callback:function ($$v) {_vm.plan_show_flag=$$v},expression:\"plan_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择生产计划\",\"show-toolbar\":\"\",\"value-key\":\"name\",\"columns\":_vm.plan_data.plan_list},on:{\"cancel\":function($event){_vm.plan_show_flag = false},\"confirm\":_vm.selectPlan}})],1),_c('van-cell',{attrs:{\"title\":\"生产批次号\",\"value\":_vm.plan_data.notice_code}}),_c('van-cell',{attrs:{\"title\":\"产品名称\",\"value\":_vm.plan_data.product_name}}),_c('van-cell',{attrs:{\"title\":\"规格型号\",\"value\":_vm.plan_data.product_code}}),_c('van-cell',{attrs:{\"title\":\"生产工艺\",\"value\":_vm.plan_data.bom_name}})],1):_vm._e(),(_vm.code_type != '')?_c('div',[_c('van-cell-group',[_c('van-field',{attrs:{\"required\":\"\",\"type\":\"number\",\"name\":\"合格数量\",\"label\":\"合格数量\",\"is-link\":true,\"placeholder\":\"请输入合格数量\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.cnt_show = true}},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [_c('span',[_vm._v(\"(件)\")])]},proxy:true}],null,false,3021629863),model:{value:(_vm.produce_cnt),callback:function ($$v) {_vm.produce_cnt=$$v},expression:\"produce_cnt\"}}),_c('van-number-keyboard',{attrs:{\"show\":_vm.cnt_show,\"theme\":\"custom\",\"extra-key\":\".\",\"close-button-text\":\"完成\"},on:{\"blur\":function($event){_vm.cnt_show = false},\"input\":_vm.onCntInput,\"delete\":_vm.onCntDelete}}),_c('van-field',{attrs:{\"required\":\"\",\"type\":\"number\",\"name\":\"生产时长\",\"label\":\"生产时长\",\"is-link\":true,\"placeholder\":\"请输入生产时长\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.hour_show = true}},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [_c('span',[_vm._v(\"(小时)\")])]},proxy:true}],null,false,266040680),model:{value:(_vm.produce_hour),callback:function ($$v) {_vm.produce_hour=$$v},expression:\"produce_hour\"}}),_c('van-number-keyboard',{attrs:{\"show\":_vm.hour_show,\"theme\":\"custom\",\"extra-key\":\".\",\"close-button-text\":\"完成\"},on:{\"blur\":function($event){_vm.hour_show = false},\"input\":_vm.onHourInput,\"delete\":_vm.onHourDelete}}),_c('van-field',{staticStyle:{\"color\":\"red\"},attrs:{\"required\":\"\",\"type\":\"number\",\"name\":\"不合格数量\",\"label\":\"不合格数量\",\"is-link\":true,\"placeholder\":\"请输入不合格数量\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.error_show = true}},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [_c('span',[_vm._v(\"(件)\")])]},proxy:true}],null,false,3021629863),model:{value:(_vm.error_cnt),callback:function ($$v) {_vm.error_cnt=$$v},expression:\"error_cnt\"}}),_c('van-number-keyboard',{attrs:{\"show\":_vm.error_show,\"theme\":\"custom\",\"extra-key\":\".\",\"close-button-text\":\"完成\"},on:{\"blur\":function($event){_vm.error_show = false},\"input\":_vm.onErrorInput,\"delete\":_vm.onErrorDelete}}),_c('van-field',{attrs:{\"name\":\"不合格类型\",\"label\":\"不合格类型\",\"is-link\":true,\"placeholder\":\"选择不合格类型\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.type_show_flag = true}},model:{value:(_vm.error_type),callback:function ($$v) {_vm.error_type=$$v},expression:\"error_type\"}}),_c('van-popup',{staticStyle:{\"height\":\"350px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.type_show_flag),callback:function ($$v) {_vm.type_show_flag=$$v},expression:\"type_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择不合格类型\",\"show-toolbar\":\"\",\"value-key\":\"name\",\"columns\":_vm.error_types},on:{\"cancel\":function($event){_vm.type_show_flag = false},\"confirm\":_vm.selectType}})],1),_c('van-field',{attrs:{\"is-link\":true,\"rows\":\"2\",\"autosize\":\"\",\"type\":\"textarea\",\"name\":\"不合格原因\",\"label\":\"不合格原因\",\"input-align\":\"right\",\"placeholder\":\"请输入不合格原因\"},model:{value:(_vm.error_remarks),callback:function ($$v) {_vm.error_remarks=$$v},expression:\"error_remarks\"}})],1),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"width\":\"100%\",\"padding\":\"10px\",\"border-top\":\"1px #F2F2F2 solid\",\"background-color\":\"#FFFFFF\",\"z-index\":\"99\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"info\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"提交\")])],1)],1):_vm._e()],1)],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./report.vue?vue&type=template&id=26713db8&scoped=true\"\nimport script from \"./report.vue?vue&type=script&lang=js\"\nexport * from \"./report.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26713db8\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('26713db8')) {\n      api.createRecord('26713db8', component.options)\n    } else {\n      api.reload('26713db8', component.options)\n    }\n    module.hot.accept(\"./report.vue?vue&type=template&id=26713db8&scoped=true\", function () {\n      api.rerender('26713db8', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/produce/report.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./report.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./report.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./report.vue?vue&type=template&id=26713db8&scoped=true\""], "names": [], "sourceRoot": ""}