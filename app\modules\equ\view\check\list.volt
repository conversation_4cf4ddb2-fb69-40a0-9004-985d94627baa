{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">点检履历查询</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">设备</label>
                            <div class="col-md-9">
                                <select name="equ_id" v-model="equ_id" class="bs-select form-control" data-size="8" data-live-search="true">
                                    <option value="">全部</option>
                                    {% for row in equ_list %}
                                        <option value="{{ row.id }}">{{ row.code }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">点检日期</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control date dtpicker" name="date_begin" v-model="date_begin"/>
                                    <span class="input-group-addon no-border-lr">至</span>
                                    <input type="text" class="form-control date dtpicker" name="date_end" v-model="date_end"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">模板</label>
                            <div class="col-md-9">
                                <select name="form_id" v-model="form_id" class="bs-select form-control">
                                    <option value="">全部</option>
                                    {% for row in form_list %}
                                    <option value="{{ row.id }}">{{ row.name ~ ' / ' ~ row.code }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   data-mobile-responsive="true"
                   data-query-params="app.params"
                   data-pagination="true"
                   data-url="{{ url('equ/check/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server"
                   class="table table-striped table-condensed">
                <thead class="bg-blue">
                <tr>
                    <th data-field="equ_code">设备</th>
                    <th data-field="check_date">点检日期</th>
                    <th data-field="form_name">模板名称</th>
                    <th data-field="form_code">模板编号</th>
                    <th data-field="status_name">状态</th>
                    <th data-field="remarks">备注</th>
                    <th data-formatter="actionFormatter">操作</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="btn-group">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            操作 <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a href="javascript:" onclick="view('@id@')"><i class="fa fa-eye"></i> 查看</a></li>
        </ul>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            equ_id: '',
            date_begin: '',
            date_end: '',
            form_id: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.equ_id = this.equ_id;
                p.date_begin = this.date_begin;
                p.date_end = this.date_end;
                p.form_id = this.form_id;
                return p;
            },
            reset: function() {
                this.equ_id = '';
                this.date_begin = '';
                this.date_end = '';
                this.form_id = '';
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            getParams() {
                let params = this.$data;
                let str = '';
                for (let col in params) {
                    str += col + '=' + params[col] + '&';
                }

                if (str) {
                    str = '?' + str.substr(0, str.length - 1);
                }
                return str;
            },
            excel() {
                if (!this.equ_id) {
                    alertWarning('请选择设备');
                    return false;
                }

                if (!this.date_begin || !this.date_end) {
                    alertWarning('请选择点检日期');
                    return false;
                }

                if (!this.form_id) {
                    alertWarning('请选择模板');
                    return false;
                }
                window.open("{{ url('equ/check/export') }}" + this.getParams());
            }
        }
    });

    var $table = $('#table');

    $table.bootstrapTable();
    function actionFormatter(v, row, idx) {
        return $('#act').html().replace(/@id@/g, row.id);
    }

    function view(id) {
        top.layer.open({
            title: '查看详情',
            type: 2,
            area: ['50em', '60em'],
            content: '{{ url('equ/check/view/') }}' + id
        });
    }

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>