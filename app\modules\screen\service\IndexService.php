<?php

namespace Envsan\Modules\Screen\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Equ\Util\Constant;

use Phalcon\Mvc\User\Component;

class IndexService extends Component
{
    public function getPageData()
    {
        $et_ret = $this->getEquipmentTypeCnt();
        return [
            'top_list' => $this->getHourTopData(),
            'et_total_cnt' => $et_ret['total_cnt'],
            'et_data' => $et_ret['data'],
            'equ_error_data' => $this->getEquErrorData(),
//            'bj_task_list' => $this->getTaskList(3),
//            'zp_task_list' => $this->getTaskList(4),
            'order_list' => $this->getOrderList()
        ];
    }

    private function getHourTopData()
    {
        $year = date('Y');
        $builder = $this->modelsManager->createBuilder()
            ->columns("
                a.staff_id,
                a.staff_name as name,
                round(sum(ifnull(a.hour,0)),2) as hours
            ")
            ->addFrom('Envsan\Modules\Mes\Model\MesProduceLogs', 'a')
            ->where('a.del_flag = 0 and a.hour is not null and a.work_date like ?1', [1 => "$year%"])
            ->groupBy('a.staff_id')
            ->orderBy('hours desc')
            ->limit(10);
        $top_rows = $builder->getQuery()->execute();

        $rtn = [];

        $max_hour = 1;
        foreach ($top_rows as $idx => $top_row)
        {
            if ($idx == 0){
                $max_hour =  $top_row->hours;
            }

            $rtn_row = new \stdClass();
            $rtn_row->name = $top_row->name;
            if ($max_hour == 0) {
                $rtn_row->percent = 0;
            } else {
                $rtn_row->percent = round($top_row->hours / $max_hour, 2) * 100 ;
            }
            $rtn_row->value = $top_row->hours;

            $rtn[] = $rtn_row;
        }
        return $rtn;
    }

    private function getEquipmentTypeCnt()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns("
                t.type_name as name,
                count(a.id) as cnt
            ")
            ->addFrom('Envsan\Modules\Equ\Model\EquItem', 'a')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItemType', 'a.type_id = t.id', 't')
            ->where('a.del_flag = 0 and a.type_id is not null')
            ->groupBy('t.type_name')
            ->orderBy('cnt desc');
        $rows = $builder->getQuery()->execute();

        $total_cnt = 0;
        $other_cnt = 0;
        $data = [];
        foreach ($rows as $idx => $row)
        {
            if ($idx < 6) {
                $data[] = [
                    'value' => $row->cnt,
                    'name' => $row->name
                ];
            } else {
                $other_cnt += $row->cnt;
            }
            $total_cnt += $row->cnt;
        }

        $data[] = [
            'value' => $other_cnt,
            'name' => '其它'
        ];

        $ret = [];
        $ret['data'] = $data;
        $ret['total_cnt'] = $total_cnt;
        return $ret;
    }

    private function getEquErrorData()
    {
        $x_values = Constant::$equ_type_arr;
        $map = [];
        foreach ($x_values as $item)
        {
            $map[$item] = 0;
        }

        $begin_date = date('Y-m-d', strtotime("-30 days"));
        $builder = $this->modelsManager->createBuilder()
            ->columns("
                t1.equ_type_name,
                count(t1.id) as cnt
            ")
            ->addFrom('Envsan\Modules\Equ\Model\EquFault', 't1')
            ->where('t1.del_flag = 0 and t1.fault_date >= ?1', [1 => $begin_date])
            ->groupBy('t1.equ_type_name');

        $total_cnt = 0;
        $equ_rows = $builder->getQuery()->execute();
        foreach ($equ_rows as $equ_row)
        {
            if (isset($map[$equ_row->equ_type_name])) {
                $map[$equ_row->equ_type_name] = $equ_row->cnt;
                $total_cnt += $equ_row->cnt;
            }
        }

        $ret = [];
        $ret['x_values'] = $x_values;
        $ret['data'] = array_values($map);
        Logger::error('异常', $total_cnt);
        $ret['total_cnt'] = $total_cnt;
        return $ret;
    }

    private function getTaskList($group_id)
    {
//        $builder = $this->modelsManager->createBuilder()
//            ->columns('
//                substr(a.begin_time, 3) as begin_time,
//                REPLACE(JSON_EXTRACT(a.bom_data, \'$[0].order_code\'), \'"\', \'\') as code
//            ')
//            ->addFrom('Envsan\Modules\Mes\Model\MesTaskDetail', 'a')
//            ->leftJoin('Envsan\Modules\Hr\Model\HrStaff', 'a.staff_id = s.id', 's')
//            ->where('a.del_flag = 0 and a.work_date = ?3 and a.owner = ?1', [1 => SessionData::ownerId(), 3 => '2025-05-19'])
//            ->andWhere('s.group_id = ?2', [2 => $group_id])
//            ->orderBy('a.id desc')
//            ->limit(8);
//        return $builder->getQuery()->execute();
    }

    private function getOrderList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.remarks,
                t1.quantity,
                t1.status,
                t1.status_name,
                t1.product_id,
                t2.code as product_code,
                t2.name as product_name,
                t2.ext_val as product_ext_val,
                t3.name as customer_name,
                t4.code,
                t4.plan_begin_date,
                t4.plan_end_date,
                ROUND(IFNULL(t7.instock_cnt, 0), 4) as instock_cnt,
                ifnull(t5.error_cnt,0) as error_cnt,
                round((ifnull(t5.produce_cnt,0)/(t1.quantity * t6.bom_cnt)) * 100 ,2) as produce_rate
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewProduceBomCnt','t1.id = t5.notice_detail_id','t5')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewProductBomCnt','t1.product_id = t6.product_id','t6')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewInstockCnt','t1.id = t7.notice_detail_id','t7')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t4.code desc');

        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            $limit_date = date('Y-m-d', strtotime("-2 days", strtotime($row['plan_begin_date'])));
            if (DateUtil::today() >= $limit_date && $row['produce_rate'] < 100) {
                $row['danger_flag'] = 1;
            } else {
                $row['danger_flag'] = 0;
            }
        }
        return $rows;
    }
}