<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class MesProductBom extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $ship_type_id;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $pids;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $pid;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $pid_val;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $nid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $cnt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     * 是否外委
     */
    public $is_outsourcing;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $produce_cnt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $produce_cost;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $work_type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $one_cost;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $goods_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $drawing_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $data_sort;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_product_bom';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProductBom[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProductBom
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
