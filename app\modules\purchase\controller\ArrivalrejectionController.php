<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseArrivalRejection;
use Envsan\Modules\Purchase\Service\PurchaseArrivalRejectionService;


/**
 * @name('拒收单')
 */
class ArrivalrejectionController extends SuperController
{
    private $page_id = 57;

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseArrivalRejectionService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }


    /**
     * @skipacl
     */
    public function createAction()
    {
        $s = new PurchaseArrivalRejectionService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = (new PurchaseArrivalRejection())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $this->view->jsonPurchaseArrivalRejection = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseArrivalRejectionService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, CvtUtil::emptyToArray($row->ext_data));
        $this->view->jsonPurchaseArrivalRejection = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('arrivalrejection/create');
    }

    /**
     * @skipacl
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseArrivalRejectionService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new PurchaseArrivalRejectionService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->jsonPurchaseArrivalRejection = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseArrivalRejectionService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}