{"version": 3, "file": "js/src_view_produce_other_vue.js", "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC/KA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/produce/other.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/produce/other.vue", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/produce/other.vue?f47e", "webpack://rrts-manager/./src/view/produce/other.vue?e65c", "webpack://rrts-manager/./src/view/produce/other.vue?71d0"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"其他报工\" is_back=\"1\"></m-header>\r\n        <m-body padding=\"false\">\r\n            <div>\r\n                <van-cell-group>\r\n                    <van-field\r\n                            required\r\n                            @click-input=\"hour_show = true\"\r\n                            type=\"number\"\r\n                            name=\"生产时长\"\r\n                            label=\"生产时长\"\r\n                            :is-link=\"true\"\r\n                            v-model=\"produce_hour\"\r\n                            placeholder=\"请输入生产时长\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                    >\r\n                        <template #button>\r\n                            <span>(小时)</span>\r\n                        </template>\r\n                    </van-field>\r\n                    <van-number-keyboard\r\n                            :show=\"hour_show\"\r\n                            theme=\"custom\"\r\n                            extra-key=\".\"\r\n                            close-button-text=\"完成\"\r\n                            @blur=\"hour_show = false\"\r\n                            @input=\"onHourInput\"\r\n                            @delete=\"onHourDelete\"\r\n                    />\r\n                    <van-field\r\n                            @click-input=\"type_show_flag = true\"\r\n                            name=\"工作类型\"\r\n                            label=\"工作类型\"\r\n                            :is-link=\"true\"\r\n                            placeholder=\"选择工作类型\"\r\n                            v-model=\"work_type\"\r\n                            readonly\r\n                            input-align=\"right\"\r\n                    />\r\n                    <van-popup\r\n                            v-model=\"type_show_flag\"\r\n                            position=\"bottom\"\r\n                            style=\"height: 350px;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n                        <van-picker\r\n                                title=\"选择工作类型\"\r\n                                show-toolbar\r\n                                value-key=\"name\"\r\n                                :columns=\"work_types\"\r\n                                @cancel= \"type_show_flag = false\"\r\n                                @confirm=\"selectType\"/>\r\n                    </van-popup>\r\n                    <van-field\r\n                            :is-link=\"true\"\r\n                            v-model=\"remarks\"\r\n                            rows=\"2\"\r\n                            autosize\r\n                            type=\"textarea\"\r\n                            name=\"工作说明\"\r\n                            label=\"工作说明\"\r\n                            input-align=\"right\"\r\n                            placeholder=\"请输入工作说明\"\r\n                    />\r\n                </van-cell-group>\r\n                <div style=\"position: absolute;bottom:0;width: 100%;padding: 10px;border-top: 1px #F2F2F2 solid;background-color: #FFFFFF;z-index: 99;\">\r\n                    <van-button  round block type=\"info\" @click=\"onSubmit\">提交</van-button>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {Dialog} from \"vant\";\r\n    import base from '../../components/base';\r\n    export default {\r\n        name: \"produceOther\",\r\n        extends: base,\r\n        components: {},\r\n        data() {\r\n            return {\r\n                loading: true,\r\n                uid:'',\r\n                produce_hour: '',\r\n                type_show_flag:false,\r\n                hour_show:false,\r\n                cnt_show:false,\r\n                error_show:false,\r\n                work_types : [],\r\n                work_type : '',\r\n                remarks : ''\r\n            }\r\n        },\r\n        methods: {\r\n            onLoad() {\r\n                this.uid = this.$route.params.uid || '';\r\n                this.init();\r\n            },\r\n            onShow() {},\r\n            init(){\r\n                this.$http.post('/work/produce/otherinit',{uid:this.uid}).then((rs) => {\r\n                    if (rs.status === 'ok') {\r\n                        this.work_types  =  rs.data.work_types;\r\n                        this.work_type = rs.data.work_type;\r\n                        this.remarks = rs.data.remarks;\r\n                        this.produce_hour = rs.data.produce_hour;\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '异常消息',\r\n                            message: rs.message,\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                }).catch(() => {\r\n                    Dialog.alert({\r\n                        title: '网络异常',\r\n                        message: '网络异常',\r\n                    }).then(() => {\r\n                        this.$router.back();\r\n                    });\r\n                });\r\n            },\r\n            onSubmit(){\r\n                if (this.produce_hour == ''){\r\n                    this.$toast.fail('请输入工作时长');\r\n                    return;\r\n                }\r\n                if (this.work_type == ''){\r\n                    this.$toast.fail('请选择工作类型');\r\n                    return;\r\n                }\r\n                this.$http.post('work/produce/other', {\r\n                    uid : this.uid,\r\n                    produce_hour : this.produce_hour,\r\n                    work_type : this.work_type,\r\n                    remarks : this.remarks\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok'){\r\n                        this.$toast.success('提交成功');\r\n                        if (this.uid != ''){\r\n                            this.$route.params.cb();\r\n                        }\r\n                        this.$router.back();\r\n                    } else {\r\n                        this.$toast.fail(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$toast.fail('网络异常');\r\n                });\r\n            },\r\n            selectType(obj){\r\n                if (this.work_type == obj.name){\r\n                    this.type_show_flag = false;\r\n                    return;\r\n                }\r\n                this.work_type = obj.name;\r\n                this.type_show_flag = false;\r\n            },\r\n            onHourInput(v){\r\n                try{\r\n                    if (parseFloat(this.produce_hour) > 99){\r\n                        return;\r\n                    }\r\n                    this.produce_hour = this.produce_hour + '' + v;\r\n                }catch (e){}\r\n            },\r\n            onHourDelete(v){\r\n                if (this.produce_hour == ''){\r\n                    return;\r\n                }\r\n                this.produce_hour = this.produce_hour.substring(0,this.produce_hour.length-1);\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"其他报工\",\"is_back\":\"1\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[_c('div',[_c('van-cell-group',[_c('van-field',{attrs:{\"required\":\"\",\"type\":\"number\",\"name\":\"生产时长\",\"label\":\"生产时长\",\"is-link\":true,\"placeholder\":\"请输入生产时长\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.hour_show = true}},scopedSlots:_vm._u([{key:\"button\",fn:function(){return [_c('span',[_vm._v(\"(小时)\")])]},proxy:true}]),model:{value:(_vm.produce_hour),callback:function ($$v) {_vm.produce_hour=$$v},expression:\"produce_hour\"}}),_c('van-number-keyboard',{attrs:{\"show\":_vm.hour_show,\"theme\":\"custom\",\"extra-key\":\".\",\"close-button-text\":\"完成\"},on:{\"blur\":function($event){_vm.hour_show = false},\"input\":_vm.onHourInput,\"delete\":_vm.onHourDelete}}),_c('van-field',{attrs:{\"name\":\"工作类型\",\"label\":\"工作类型\",\"is-link\":true,\"placeholder\":\"选择工作类型\",\"readonly\":\"\",\"input-align\":\"right\"},on:{\"click-input\":function($event){_vm.type_show_flag = true}},model:{value:(_vm.work_type),callback:function ($$v) {_vm.work_type=$$v},expression:\"work_type\"}}),_c('van-popup',{staticStyle:{\"height\":\"350px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.type_show_flag),callback:function ($$v) {_vm.type_show_flag=$$v},expression:\"type_show_flag\"}},[_c('van-picker',{attrs:{\"title\":\"选择工作类型\",\"show-toolbar\":\"\",\"value-key\":\"name\",\"columns\":_vm.work_types},on:{\"cancel\":function($event){_vm.type_show_flag = false},\"confirm\":_vm.selectType}})],1),_c('van-field',{attrs:{\"is-link\":true,\"rows\":\"2\",\"autosize\":\"\",\"type\":\"textarea\",\"name\":\"工作说明\",\"label\":\"工作说明\",\"input-align\":\"right\",\"placeholder\":\"请输入工作说明\"},model:{value:(_vm.remarks),callback:function ($$v) {_vm.remarks=$$v},expression:\"remarks\"}})],1),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"width\":\"100%\",\"padding\":\"10px\",\"border-top\":\"1px #F2F2F2 solid\",\"background-color\":\"#FFFFFF\",\"z-index\":\"99\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"info\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"提交\")])],1)],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./other.vue?vue&type=template&id=037d4b50&scoped=true\"\nimport script from \"./other.vue?vue&type=script&lang=js\"\nexport * from \"./other.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"037d4b50\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('037d4b50')) {\n      api.createRecord('037d4b50', component.options)\n    } else {\n      api.reload('037d4b50', component.options)\n    }\n    module.hot.accept(\"./other.vue?vue&type=template&id=037d4b50&scoped=true\", function () {\n      api.rerender('037d4b50', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/produce/other.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./other.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./other.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./other.vue?vue&type=template&id=037d4b50&scoped=true\""], "names": [], "sourceRoot": ""}