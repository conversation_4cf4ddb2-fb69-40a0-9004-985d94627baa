{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <div style="width: 100%;">
            <div class="list-box">
                <div class="portlet light portlet-detail" style="margin-bottom: 0;">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-social-dribbble font-green"></i>
                            <span class="caption-subject font-green bold uppercase">订单明细{% if order_code %}({{ order_code }}){% endif %}</span>
                        </div>
                        <div class="actions" style="display: flex;align-items: center;justify-content: flex-end">
                            <div class="sum-item">
                                <div>总数量：</div>
                                <div class="sum-val" v-text="total_quantity"></div>
                            </div>
                            <div class="sum-item">
                                <div>未税总金额：</div>
                                <div class="sum-val" v-text="total_money"></div>
                                <div style="margin-left: 5px;">元</div>
                            </div>
                            <div class="sum-item">
                                <div>含税总金额：</div>
                                <div class="sum-val" v-text="total_money_hs"></div>
                                <div style="margin-left: 5px;">元</div>
                            </div>
                            <button type="button" class="btn purple" @click="openGoodsSelector" style="margin-right: 20px;"><i class="fa fa-plus"></i> 选择明细</button>
                            <button type="button" class="btn blue" @click="save" style="margin: 0 20px;"><i class="fa fa-save"></i> 保存</button>
                            <button type="button" class="btn green"  @click="submit"><i class="fa fa-check"></i> 提交</button>
                        </div>
                    </div>
                    {% if reject_status == 1 %}
                        <div class="alert alert-danger" role="alert" style="margin: 15px;">
                            <strong>驳回理由：</strong>{{ reject_remarks }}
                        </div>
                    {% endif %}
                    <div class="portlet-body">
                        <div class="table-search-bar row">
                            <div class="col-sm-4">
                                <div class="input-group">
                                    <span class="input-group-addon">供应商</span>
                                    <select class="bs-select form-control" name="supplier_id" v-model="supplier_id" data-live-search="true" data-size="8" required>
                                        <option value="">请选择供应商</option>
                                        {% for item in supplierList %}
                                            <option value="{{ item.id }}">{{ item.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="input-group">
                                    <span class="input-group-addon">采购请求</span>
                                    <select class="bs-select form-control" name="request_id" 
                                        @change="changeRequest"
                                        v-model="request_id" data-live-search="true" data-size="8" required>
                                        <option value="">请选择采购请求</option>
                                        {% for item in requestList %}
                                            <option value="{{ item.id }}">{{ item.apply_code }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="input-group date dtpicker">
                                    <span class="input-group-addon"><span class="required">*</span>订单日期</span>
                                    <input type="text" class="form-control" name="order_date" v-model="order_date">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="zh-table-box">
                            <div class="zh-table-box-content">
                                <table class="table table-bordered table-big">
                                    <thead>
                                    <tr>
                                        <th>编码</th>
                                        <th>名称</th>
                                        <th>规格型号</th>
                                        <th style="width: 170px;">库存计量数量</th>
                                        <th>采购计量数量</th>
                                        <th style="width: 190px;">采购单价(未税)</th>
                                        <th style="width: 190px;">采购单价(含税)</th>
                                        <th>总价(未税)</th>
                                        <th>总价(含税)</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-if="list.length == 0">
                                        <td colspan="10" style="text-align: center;">没有数据</td>
                                    </tr>
                                    <tr v-for="row, index in list">
                                        <td v-text="row.code"></td>
                                        <td v-text="row.name"></td>
                                        <td v-text="row.model"></td>
                                        <td>
                                            <div class="input-group">
                                                <input  type="number" class="form-control" :name="'inventory_quantity' + index" v-model="row.inventory_quantity" @keyup="sumMoney" @input="calcPurchaseQuantity(row)" placeholder="数量" number="true" maxlength="10" required>
                                                <span class="input-group-addon" v-text="row.inventory_unit"></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input  type="number" class="form-control" :name="'purchase_quantity' + index" v-model="row.purchase_quantity" @keyup="sumMoney" @input="calcPurchaseQuantity(row)" placeholder="数量" number="true" maxlength="10" required>
                                                <span class="input-group-addon" v-text="row.purchase_unit"></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input  type="number" class="form-control" :name="'price' + index" v-model="row.price" @keyup="sumMoney" @input="onPriceChange(row)" placeholder="未税单价" number="true"  maxlength="10" required>
                                                <span class="input-group-addon" v-text="'元/' + row.purchase_unit"></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group">
                                                <input  type="number" class="form-control" :name="'price_hs' + index" v-model="row.price_hs" @keyup="sumMoney" @input="onPriceHsChange(row)" placeholder="含税单价" number="true"  maxlength="10" required>
                                                <span class="input-group-addon" v-text="'元/' + row.purchase_unit"></span>
                                            </div>
                                        </td>
                                        <td v-text="row.total_money"></td>
                                        <td v-text="row.total_money_hs"></td>
                                        <td>
                                            <a href="javascript:;" @click='delList(index)' style="color: red;font-size: 20px">
                                                <i class="fa fa-times"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
{{ partial('check') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            order_date: '{{ order_date }}',
            supplier_id: '{{ supplier_id }}',
            request_id: '{{ request_id }}',
            list: {{ detail }},
            apply_list:{{ apply_list }},
        },
        created() {
            if (!this.request_id) {
                this.request_id = '';
            }
        },
        computed: {
            total_quantity() {
                return this.list.reduce((sum, item) => {
                    return sum + parseFloat(item.inventory_quantity || 0);
                }, 0).toFixed(2);
            },
            total_money() {
                return this.list.reduce((sum, item) => {
                    return sum + parseFloat(item.total_money || 0);
                }, 0).toFixed(2);
            },
            total_money_hs() {
                return this.list.reduce((sum, item) => {
                    return sum + parseFloat(item.total_money_hs || 0);
                }, 0).toFixed(2);
            },
        },
        methods:{
            changeRequest() {
                this.list = [];
            },
            openGoodsSelector() {
                if (this.request_id == '' ) {
                    toastr.error('需要选择采购请求！');
                    return;
                }

                ids = [];
                for (let item of app.list) {
                    ids.push(item.id);
                }
                top.window.layer_result2 = '';
                top.layer.open({
                    title: '选择采购明细',
                    type: 2,
                    area: ['90%', '80%'],
                    content: '{{url("purchase/order/addapply/0/")}}' + app.request_id + '/' + ids,
                    end: function() {
                        if (top.window.layer_result2 == 'ok') {
                            app.addDetailList(top.window.layer_data2);
                        }
                    }
                });   
            },
            onPriceChange(row) {
                if (row.price !== '' && row.price !== null) {
                    const priceValue = Number(row.price) || 0;
                    const rateValue = Number(row.tax_rate) || 0;
                    row.price_hs = (priceValue * (1 + rateValue)).toFixed(2);
                }
            },
            onPriceHsChange(row) {
                if (row.price_hs !== '' && row.price_hs !== null) {
                    const priceHsValue = Number(row.price_hs) || 0;
                    const rateValue = Number(row.tax_rate) || 0;
                    if (rateValue === 0) {
                        this.price = priceHsValue.toFixed(2);
                    } else {
                        row.price = (priceHsValue / (1 + rateValue)).toFixed(2);
                    }
                }
            },
            save: function (e) {
                e.preventDefault();
                this.saveData(1);
            },
            submit: function (e) {
                e.preventDefault();

                if (!this.supplier_id) {
                    toastr.error('请选择供应商');
                    return;
                }

                this.saveData(2);
            },
            saveData(type) {
                if (!app.order_date) {
                    toastr.error('请选择订单日期');
                    return;
                }

                if (this.list.length == 0){
                    toastr.error('请添加订单明细');
                    return;
                }

                for (let i = 0; i < this.list.length; i++) {
                    let row = this.list[i];
                    if (!isDecimal(row.inventory_quantity) || parseFloat(row.inventory_quantity) <= 0) {
                        toastr.error('请输入有效的数量，大于0');
                        return;
                    }

                    if (!isDecimal(row.price)) {
                        toastr.error('请输入有效的未税单价');
                        return;
                    }

                    if (!isDecimal(row.price_hs)) {
                        toastr.error('请输入有效的含税单价');
                        return;
                    }
                }

                let apply_ids = []
                for (let i = 0; i < this.apply_list.length; i++) {
                    apply_ids.push(this.apply_list[i].id)
                }

                let param = {
                    type: type,
                    request_id: app.request_id,
                    supplier_id: app.supplier_id,
                    order_date: app.order_date,
                    detail_list: app.list,
                    apply_ids: apply_ids
                };

                showSpin();
                $.post('{{ url('purchase/order/detail/' ~ uid) }}', param, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },
            delList: function(idx) {
                this.list.splice(idx, 1);
                this.sumMoney();
            },
            delApply: function() {
                let apply_list = [], goods_obj = {};
                for (let item of this.apply_list) {
                    if (!goods_obj.hasOwnProperty(item.goods_id)) {
                        goods_obj[item.goods_id] = 0;
                    }

                    if (item.sel == 0) {
                        apply_list.push(item);
                        goods_obj[item.goods_id]++;
                    }
                }
                if (apply_list.length === this.apply_list.length) {
                    toastr.error('请选择删除记录！');
                    return;
                }
                this.apply_list = apply_list;

                let list = [];
                for (let item of this.list) {
                    if (!goods_obj.hasOwnProperty(item.id) || goods_obj[item.id] > 0) {
                        list.push(item);
                    }
                }
                this.list = list;
                this.sumMoney();
            },
            sumMoney() {
                for (let item of this.list) {
                    let total_money = '';        // 不含税总价
                    let total_money_hs = '';     // 含税总价
                    
                    // 获取并验证数量、单价
                    const quantity = item.purchase_quantity;
                    const price = item.price;           // 不含税单价
                    const tax_rate = item.tax_rate;     // 税率（如：17表示17%）
                    
                    // 检查必要字段是否有效
                    if (isDecimal(quantity) && isDecimal(price)) {
                        const qty = Number(quantity);
                        const unitPrice = Number(price);
                        
                        // 计算不含税总价
                        total_money = Number((qty * unitPrice).toFixed(4));
                        
                        // 处理税率：空值当作0处理
                        let taxRateValue = 0;
                        if (isDecimal(tax_rate)) {
                            taxRateValue = Number(tax_rate);
                        }
                        // 如果tax_rate为空、null、undefined、''等，taxRateValue保持为0
                        
                        // 计算含税总价
                        total_money_hs = Number((qty * unitPrice * (1 + taxRateValue)).toFixed(4));
                    }
                    // 如果数量或单价无效，则保持空字符串
                    
                    // 设置计算结果
                    this.$set(item, 'total_money', total_money);
                    this.$set(item, 'total_money_hs', total_money_hs);
                }
            },
            // 计算采购数量
            calcPurchaseQuantity(row) {
                if (!row.inventory_quantity || row.inventory_quantity === '' || isNaN(row.inventory_quantity)) {
                    this.$set(row, 'purchase_quantity', 0);
                    return;
                }
                
                let inventoryQty = Number(row.inventory_quantity);
                let conversionRate = Number(row.unit_conversion_rate || 1);
                
                if (inventoryQty <= 0 || conversionRate <= 0) {
                    this.$set(row, 'purchase_quantity', 0);
                    return;
                }
                
                let purchaseQty = Math.ceil(inventoryQty * conversionRate * 1000) / 1000;
                this.$set(row, 'purchase_quantity', purchaseQty);
            },
            // 计算采购数量
            calcQuantity(row) {
                if (!row.purchase_quantity || row.purchase_quantity === '' || isNaN(row.purchase_quantity)) {
                    this.$set(row, 'inventory_quantity', 0);
                    return;
                }
                
                let qty = Number(row.purchase_quantity);
                let conversionRate = Number(row.unit_conversion_rate || 1);
                
                if (qty <= 0 || conversionRate <= 0) {
                    this.$set(row, 'inventory_quantity', 0);
                    return;
                }
                
                let inventoryQty = Math.round(qty / conversionRate * 1000) / 1000;
                this.$set(row, 'inventory_quantity', inventoryQty);
            },
            // 获取采购数量显示文本
            getPurchaseQuantityDisplay(row) {
                let qty = row.purchase_quantity || 0;
                let unit = row.purchase_unit || '';
                return qty + '（' + unit + '）';
            },
            addDetailList(apply_list) {
                paramLoop: for (let i = 0; i < apply_list.length; i++) {
                    apply_row = apply_list[i];
                    for (let j = 0; j < app.list.length; j++) {
                        if (app.list[j].id == apply_row.id) {
                            continue paramLoop;
                        }
                    }
                    let newItem = {
                        id: apply_row.id,
                        goods_id: apply_row.goods_id,
                        uid: apply_row.goods_uid,
                        code: apply_row.goods_code,
                        name: apply_row.goods_name,
                        spec: apply_row.goods_spec,
                        inventory_unit: apply_row.inventory_unit,
                        purchase_unit: apply_row.purchase_unit,
                        model: apply_row.goods_model,
                        price: apply_row.price || '',
                        price_hs: apply_row.price_hs || '',
                        inventory_quantity: apply_row.inventory_quantity,
                        purchase_quantity: apply_row.purchase_quantity || '',
                        total_money: '',
                        total_money_hs: '',
                        apply_id: apply_row.id,
                        unit_conversion_rate: apply_row.unit_conversion_rate || 1,
                        tax_rate: apply_row.tax_rate,
                        check_flag: apply_row.check_flag,
                    };
                    
                    // 计算采购数量
                    app.calcPurchaseQuantity(newItem);
                    
                    // 处理价格：如果有未税单价但没有含税单价，则计算含税单价
                    if (newItem.price && newItem.price !== '' && (!newItem.price_hs || newItem.price_hs === '')) {
                        app.onPriceChange(newItem);
                    }
                    // 如果有含税单价但没有未税单价，则计算未税单价  
                    else if (newItem.price_hs && newItem.price_hs !== '' && (!newItem.price || newItem.price === '')) {
                        app.onPriceHsChange(newItem);
                    }
                    
                    app.list.push(newItem);
                }
                app.sumMoney();
            },
        },
    });

   
    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd',
        pickerPosition: 'bottom-left'
    }).on('changeDate', function (ev) {
        let obj = $(this).find('input');
        app[$(obj).attr('name')] = $(obj).val();
    });

    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);
</script>
<style>
    .tree-box {
        overflow-y: auto;
    }

    .table-search-bar {
        margin-bottom: 20px;
    }

    .list-box {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .list-box .portlet {
        height: 48%;
    }

    .zh-table-box table thead {
        z-index: 3;
    }

    .sum-item {
        display: flex;
        align-items: flex-end;
        margin: 0 10px;
    }

    .sum-val {
        font-size: 20px;
        line-height: 20px;
        font-weight: bold;
        color: #3598F6;
    }
</style>