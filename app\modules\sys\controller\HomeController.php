<?php

namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\Logger;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Sys\Service\HomeService;

/**
 * @skipacl
 */
class HomeController extends SuperController
{
    public function indexAction()
    {
        $s = new HomeService();
        $this->view->data = $s->getReviewData();

        $this->view->date_begin = date('Y-m-d', strtotime("-30 day"));
        $this->view->date_end = date('Y-m-d', strtotime("-1 day"));
        $this->view->today = DateUtil::today();
    }

    public function searchAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new HomeService();
            return json_encode($s->getChartData());
        }
    }

    public function refreshAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new HomeService();
            return json_encode($s->getReviewData());
        }
    }
}