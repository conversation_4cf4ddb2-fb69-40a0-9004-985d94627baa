<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Work\Model\WorkDataRead;
use Envsan\Modules\Work\Model\WorkDataReview;
use Phalcon\Mvc\User\Component;

class ReviewService extends Component
{
    public function selectAll(){
        $type = $this->request->get('type', ['string', 'trim']);
        $builder = $this->selectData($type);
        return $builder;
    }

    public function searchAll(){
        $type_name = $this->request->get('type_name', ['string', 'trim']);
        $content = $this->request->get('content', ['string', 'trim']);
        $status = $this->request->get('status', ['string', 'trim']);
        $date_start = $this->request->get('date_start', ['string', 'trim']);
        $date_end = $this->request->get('date_end', ['string', 'trim']);
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns( '
                t1.id,
                t1.uid,
                t1.code,
                t1.type_name,
                t1.review_type,
                ifnull(t1.remarks,\'\') as remarks,
                t1.create_date,
                t1.status,
                t1.pressing_flag,
                t1.handle_status,
                t1.abstrakt_data,
                t2.name as group_name,
                t3.real_name as create_name,
                ifnull(t21.read_flag,1) as read_flag
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.create_group_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Work\Model\WorkDataReview', 't1.id = t20.data_id', 't20')
            ->leftJoin('Envsan\Modules\Work\Model\WorkDataRead', 't1.id = t21.data_id', 't21')
            ->where('t1.del_flag = 0 and t1.owner = ?1',[1 => SessionData::ownerId()])
            ->andWhere('t1.anchor_users like ?2 or t1.create_by = ?3 or t20.user_id = ?3 or t21.user_id = ?3',[2 => "%|$user->id|%",3 => $user->id])
            ->orderBy('t1.pressing_flag desc,t1.status asc,t1.create_date asc')
            ->groupBy('t1.id');
        if (!CheckUtil::is_empty($type_name)) {
            $builder->andWhere("t1.type_name like ?5", [5 => "%$type_name%"]);
        }
        if (!CheckUtil::is_empty($content)) {
            $builder->andWhere("t1.form_data like ?6", [6 => "%$content%"]);
        }
        if (!empty($date_start)) {
            $builder->andWhere("t1.create_date >= ?7", [7 => $date_start.' 00:00:00']);
        }
        if (!empty($date_end)) {
            $builder->andWhere("t1.create_date <= ?8", [8 => $date_end.' 23:59:59']);
        }
        if (!empty($status)) {
            if ($status == 1){
                $builder->andWhere("t1.status = 15");
            } else if ($status == 2){
                $builder->andWhere("t1.status = 20 and t1.handle_status = 1");
            } else if ($status == 3){
                $builder->andWhere("t1.status = 20 and t1.handle_status > 1");
            }
        }
        return $builder;
    }

    public function selectData($type)
    {
        $user = SessionData::user();
        $columns = '
                t1.uid,
                t1.code,
                t1.type_name,
                t1.review_type,
                ifnull(t1.remarks,\'\') as remarks,
                t1.create_date,
                t1.status,
                t1.pressing_flag,
                t1.handle_status,
                t1.abstrakt_data,
                t2.name as group_name,
                t3.real_name as create_name
            ';
        if ($type == 4){
            $columns .= ',t21.read_flag';
        } else {
            $columns .= ',1 as read_flag';
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns($columns)
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.create_group_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = ?1',[1 => SessionData::ownerId()]);
        if ($type == 1){
            $builder->andWhere('t1.status = 15 and t1.anchor_users like ?2 ', [2 => "%|$user->id|%"]);
            $builder->orderBy('t1.pressing_flag desc,t1.status asc,t1.create_date asc,t1.id');
        } else if ($type == 2){
            $builder->leftJoin('Envsan\Modules\Work\Model\WorkDataReview', 't1.id = t20.data_id', 't20');
            $builder->andWhere('t20.user_id = ?3',[3 => $user->id]);
            $builder->orderBy('t1.create_date desc,t1.id');
        } else if ($type == 3){
            $builder->andWhere('t1.create_by = ?4 ', [4 => $user->id]);
            $builder->orderBy('t1.create_date desc,t1.id');
        } else if ($type == 4){
            $builder->leftJoin('Envsan\Modules\Work\Model\WorkDataRead', 't1.id = t21.data_id', 't21');
            $builder->andWhere('t21.user_id = ?5',[5 => $user->id]);
            $builder->orderBy('t21.read_flag asc,t1.create_date desc,t1.id');
        }
        return $builder;
    }

    public function getCntData(){
        $cnt_data = [];
        for ($i = 1; $i <= 4 ; $i ++){
            $sel_builder = $this->selectData($i);
            $cnt_data['_'.$i] = $this->getCount($i,$sel_builder,'');
        }
        return $cnt_data;
    }

    public function getCount($type,$builder,$search_type = ''){
        $builder->columns('count(0) as cnt');
        switch ($type)
        {
            case 2:
                if (empty($search_type)){
                    $today = DateUtil::today();
                    $builder->andWhere('t20.update_date like ?44',[44=>"$today%"]);
                }
                break;
            case 3:
                if (empty($search_type)){
                    $builder->andWhere('t1.status = 15');
                }
                break;
            case 4:
                if (empty($search_type)){
                    $builder->andWhere('t21.read_flag = 0');
                }
                break;
            default:
                break;
        }
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            return 0;
        }
        return CvtUtil::emptyToInt($rows[0]->cnt);
    }

    public function setDetail($rows){
        $rows = $rows->toArray();
        foreach ($rows as &$row){
            $row['abstrakt_data'] = CvtUtil::emptyToArray($row['abstrakt_data']);
            $row['create_date'] =  date('y/m/d H:i',strtotime($row['create_date']));;
        }
        return $rows;
    }

    public function getPrintData($work_row){
        $rtn = new \stdClass();
        $user = SessionData::user();
        $ws = new WorkService();
        $data = $ws->getWorkRow($work_row);
        $hidden_list = [];
        $read_row = WorkDataRead::findFirst(['del_flag = 0 and data_id = ?1 and user_id = ?2','bind'=>[1=>$work_row->id,2=>$user->id]]);
        if (!empty($read_row)){
            $hidden_list = CvtUtil::emptyToArray($read_row->hidden_keys);
        } else {
            $review_row = WorkDataReview::findFirst(['del_flag = 0 and data_id = ?1 and user_id = ?2','bind'=>[1=>$work_row->id,2=>$user->id],'order'=>'id desc']);
            if (!empty($review_row)){
                $hidden_list = CvtUtil::emptyToArray($review_row->hidden_keys);
            }
        }
        $form_list = [];
        $form_list[]= [
          'name' => '审批单号',
          'value' => $data->code,
          'unit' => ''
        ];
        $form_list[]= [
            'name' => '提交部门',
            'value' => $data->group,
            'unit' => ''
        ];
        $form_list[]= [
            'name' => '提交人',
            'value' => $data->create_user,
            'unit' => ''
        ];
        $form_list[]= [
            'name' => '提交时间',
            'value' => $data->create_date,
            'unit' => ''
        ];
        $detail_list = [];
        if (!empty($work_row->form_data)){
            $form = json_decode($work_row->form_data,true);
            foreach ($form as $from_item){
                if (!in_array($from_item['id'].'_1',$hidden_list) && CvtUtil::emptyToInt($from_item['type']) < 60){
                    $form_list[] = [
                        'name' => $from_item['name'],
                        'value' => $from_item['value'],
                        'unit' => $from_item['unit']
                    ];
                }
                if (CvtUtil::emptyToInt($from_item['type']) == 99){
                    foreach ($from_item['data_list'] as $data_row){
                        $detail_row = [];
                        foreach ($data_row as $data_item){
                            if (!in_array($data_item['id'].'_2',$hidden_list) && CvtUtil::emptyToInt($data_item['type']) < 60){
                                $detail_row[] = [
                                    'name' => $data_item['name'],
                                    'value' => $data_item['value'],
                                    'unit' => $data_item['unit']
                                ];
                            }
                        }
                        $detail_list[] = $detail_row;
                    }
                }
            }
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.anchor_name,
                t1.user_name,
                t1.text,
                t1.update_date,
                t2.sign
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkDataReview','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\User','t1.user_id = t2.id','t2')
            ->where('t1.del_flag = 0 and data_id = ?1',[1 => $work_row->id])
            ->orderBy('t1.update_date,t1.id');
        $pass_list = $builder->getQuery()->execute();
        $rtn_data =  new \stdClass();
        $rtn_data->type_name = $data->type_name;
        $rtn_data->pass_list = $pass_list;
        $rtn_data->form_list = $form_list;
        $rtn_data->detail_list = $detail_list;
        $rtn->message = '';
        $rtn->data = $rtn_data;
        return $rtn;
    }
}