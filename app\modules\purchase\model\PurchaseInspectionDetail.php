<?php

namespace Envsan\Modules\Purchase\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseInspectionDetail extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $inspection_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $receipt_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_model;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_deputy_unit;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $purchase_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", length=1, nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $owner;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $check_code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $check_status;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_images;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $check_result_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $check_user_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $check_user_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_time;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $check_remarks;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quality_template_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_inspection_detail';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseInspectionDetail[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseInspectionDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }

}
