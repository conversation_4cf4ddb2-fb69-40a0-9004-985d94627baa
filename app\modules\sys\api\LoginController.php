<?php

namespace Envsan\Modules\Sys\Api;

use Envsan\Common\Base\ApiController;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Sys\Model\Owner;
use Envsan\Modules\Sys\Service\LoginService;
use Phalcon\Crypt;

/**
 * @noacl
 */
class LoginController extends ApiController
{
    /**
     * @return string
     */
    public function indexAction()
    {
        if ($this->request->isPost()) {
            // todo!!
            $owner = Owner::findFirst();
            $ret = new JsonData();
            $ls = new LoginService();
            $user = $ls->login($owner);
            if ($user != null) {
                $ret->emptyIsOk();
                $crypt = new Crypt();
                $ret->auth = $crypt->encryptBase64($user->login_name . ',' . $user->password, $this->config->api->authKey);
                $ret->sid = $this->session->getId();
            }

            return json_encode($ret);
        }
    }

    public function logoutAction()
    {
        if ($this->request->isPost()) {
            $ls = new LoginService();
            $ls->logout();

            $ret = new JsonData();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function sessionAction()
    {
        $user = SessionData::user();
        $ret = array();
        $ret['logined'] = ($user != null ? true : false);
        $ret['sid'] = $this->session->getId();
        $ret['user'] = $user;
        return json_encode($ret);
    }
}