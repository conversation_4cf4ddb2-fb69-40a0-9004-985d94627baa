<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesNotice;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Service\NoticeService;
use Envsan\Modules\Printing\Service\TemplateService;
use Envsan\Modules\Trade\Model\TradeCustomer;
use Envsan\Modules\Trade\Model\TradeOrder;


/**
 * @name("消息通知")
 */
class NoticeController extends SuperController
{
    private $page_id = 5;
    private $search_page_id = 7;

    /**
     * @name("列表")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new NoticeService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("查询")
     * 生产完成情况统计
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new NoticeService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList(6),JSON_UNESCAPED_UNICODE);
    }

    /**
     * @name("新增")
     */
    public function createAction()
    {
        $s = new NoticeService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }

        $table = new TableService();
        $oss_util = new FileService();

        $jrow = (new MesNotice())->toArray();
        $jrow['customer_id'] = '';
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['files'] = [];
        $jrow['detail_data'] = [];
        $jrow['order_id'] = '';
        $jrow['product_id'] = '';
        $jrow['product_list'] = $s->getProductList();
        $jrow['order_list'] = [];
        $jrow['customerList'] = $s->getCustomerList();
        $this->view->event_type = 'create';
        $this->view->jsonNotice = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
    }

    /**
     * @acl({'link':'mes:notice:create'})
     * @name("编辑")
     */
    function editAction($type = '', $uid)
    {
        $s = new NoticeService();
        $row = $s->selectByUid($uid);
        if(empty($row) || $row->del_flag == 1) {
            die(ErrorHelper::WRONG_ID);
            // 变更数量的时候不进行这个判断
        } else if ($row->status != 10 && $type != 'change') {
            die('生产计划已提交，不能编辑');
        }

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $oss_util = new FileService();
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $jrow['product_id'] = '';
        $jrow['order_list'] = $s->getOrderList($jrow['customer_id']);
        $jrow['product_list'] = $s->getProductList($jrow['order_id'], $jrow['customer_id']);
        $jrow['customerList'] = $s->getCustomerList();
        $this->view->jsonNotice = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->event_type = $type;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
        $this->view->pick('notice/create');
    }

    /**
     * @acl({'link':'mes:notice:create'})
     * @name("删除")
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new NoticeService();
            $ret = new JsonData();
            $ret->handleResult($s->deleteByUid());
            return json_encode($ret);
        }
    }

    /**
     * @name("停产")
     */
    public function stopAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new NoticeService();
            $ret = new JsonData();
            $ret->handleResult($s->stop());
            return json_encode($ret);
        }
    }


    /**
     * @name("重新生产")
     */
    public function restartAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new NoticeService();
            $ret = new JsonData();
            $ret->handleResult($s->restart());
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     * @name("生产详情")
     * 和停产没有关系
     */
    function produceAction($uid)
    {
        $s = new NoticeService();
        $builder = $s->searchAll();
        $builder->andWhere('t1.uid = ?99',[99=>$uid]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0)
            die(ErrorHelper::WRONG_ID);
        $jrow = $rows[0]->toArray();
        $jrow['bom_list'] = $s->getProduceData($jrow['id'],$jrow['code']);
        $this->view->jsonData = json_encode($jrow,JSON_UNESCAPED_UNICODE);
    }

    /**
     * @skipacl
     * @name("质检")
     * 和停产没有关系
     */
    function qualityAction($uid)
    {
        $s = new NoticeService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new NoticeService();
            $ret = new JsonData();
            $rtn =  $s->getCheckData();
            $ret->message = $rtn->message;
            if (empty($ret->message)){
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $builder = $s->searchAll();
        $builder->andWhere('t1.uid = ?99',[99=>$uid]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0)
            die(ErrorHelper::WRONG_ID);
        $jrow = $rows[0]->toArray();
        $jrow['bom_list'] = $s->getQualityData($jrow['product_id']);
        $jrow['quality_list'] = [];
        $jrow['bom_item'] = [];
        if (count( $jrow['bom_list']) > 0){
            $jrow['bom_item'] = $jrow['bom_list'][0];
            $jrow['quality_list'] = $s->getCheckList($jrow['id'] , $jrow['bom_item']['bom_id'] ,  $jrow['bom_item']['id']);
        }
        $this->view->uid = $uid;
        $this->view->jsonData = json_encode($jrow,JSON_UNESCAPED_UNICODE);
    }


    /**
     * @skipacl
     * @name("查看")
     */
    function viewAction($uid)
    {
        $s = new NoticeService();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);
        $oss_util = new FileService();
        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['customer_name'] = '';
        $customer = TradeCustomer::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=> $jrow['customer_id']]]);
        if (!empty($customer)){
            $jrow['customer_name'] = $customer->name;
        }
        $jrow['data_sort'] = CvtUtil::emptyToInt($jrow['data_sort']);
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['detail_data'] = $s->getDetailData($jrow['id']);
        $this->view->jsonNotice = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 12;
    }

    /**
     * @skipacl
     * @name("导出")
     */
    public function exportAction()
    {
        $s = new NoticeService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     * @name("获取产品")
     */
    public function productAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new NoticeService();
            $jsonData = new JsonData();
            $order_id = $this->request->getPost('order_id', 'tstring');
            $customer_id = $this->request->getPost('customer_id', 'tstring');
            $jsonData->handleResult($s->getProductList($order_id, $customer_id));
            return json_encode($jsonData);
        }
    }


    /**
     * @skipacl
     * @name("取得订单")
     */
    public function orderAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new NoticeService();
            $jsonData = new JsonData();
            $customer_id = $this->request->getPost('customer_id', 'tstring');
            $orderList = $s->getOrderList($customer_id);
//            $productList = $s->getProductList('', $customer_id);
            $result = [
                'orderList' => $orderList
            ];
            $jsonData->handleResult($result);
            return json_encode($jsonData);
        }
    }

    /**
     * @skipacl
     * @name("获取BOM")
     */
    public function getbomAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new NoticeService();
            $jsonData = new JsonData();
            $jsonData->handleResult($s->getBomList($this->request->getPost('product_id', 'tstring')));
            return json_encode($jsonData);
        }
    }


    /**
     * @skipacl
     * @name("导出2")
     */
    public function export2Action()
    {
        $s = new NoticeService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }

    /**
     * 到货单关联的采购单
     * @acl({'link':'purchase:receipt:create'})
     */
    public function orderdetailAction($type, $product_id = null)
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new NoticeService();
            $builder = $s->selectOrderDetail($product_id);
            $page = $this->getPagination($builder);

            // 获取产品统计信息并添加到返回数据中
            $productStats = $s->getProductCount($product_id);
            $page->product_stats = $productStats;
            
            return json_encode($page);
        }
        $this->view->product_id = $product_id ;
    }
    
    /**
     * @skipacl
     * @name("获取原材料详情")
     */
    public function materialsdetailAction($type, $product_id = null)
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new NoticeService();
            return json_encode($s->getMaterialsByProductId($product_id));
        }
    }
}