<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesDrawing;
use Envsan\Modules\Mes\Model\MesDrawingVersion;
use Envsan\Modules\Mes\Model\MesOrderBom;
use Envsan\Modules\Mes\Service\DrawingService;
use Envsan\Modules\Mes\Service\ProductService;

/**
 * @name("图纸")
 */
class DrawingController extends SuperController
{
    private $page_id = 17;

    /**
     * @name("列表")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new DrawingService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @acl({'link':'mes:drawing:list'})
     * @name("版本")
     */
    public function versionAction($uid)
    {
        $rs = new DrawingService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($this->request->isPost()){
            $this->setJsonResponse();
            return json_encode($rs->getVersionList($row->id));
        }
        $this->view->uid = $uid;
        $this->view->code = $row->code;
        $this->view->remarks = $row->remarks;
        $oss_util = new FileService();
        $this->view->base_path = $oss_util->getImagePath();
        $this->view->version_list = json_encode($rs->getVersionList($row->id));
    }

    /**
     * @acl({'link':'mes:drawing:list'})
     * @name("上传")
     */
    public function uploadAction($uid)
    {
        $rs = new DrawingService();
        $row = $rs->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()) {
            $this->setJsonResponse();
            return json_encode($rs->upload($row));
        }

        $jrow = (new MesDrawingVersion())->toArray();
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['drawing_url'] = '';
        $this->view->uid = $uid;
        $this->view->jsonVersion = json_encode($jrow);
    }

    /**
     * @acl({'link':'mes:drawing:list'})
     */
    public function uploadeditAction($uid, $version_uid)
    {
        $rs = new DrawingService();
        $row = $rs->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $version_row = $rs->selectVersionByUid($version_uid);
        if (empty($version_row))
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()) {
            $this->setJsonResponse();
            return json_encode($rs->upload($row, $version_row));
        }

        $jrow = $version_row->toArray();
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $this->view->jsonVersion = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->version_uid = $version_uid;
        $this->view->pick('drawing/upload');
    }

    /**
     * @acl({'link':'mes:drawing:list'})
     */
    public function deleteversionAction($uid)
    {
        $rs = new DrawingService();
        $row = $rs->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->deleteVersion($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'mes:drawing:list'})
     */
    public function releaseAction($uid)
    {
        $s = new DrawingService();
        $row = $s->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            die(ErrorHelper::WRONG_ID);
        } else if ($row->status != 40) {
            die('数据状态不正确');
        }

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->updateBom($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $oss_util = new FileService();
        $version_row = $s->selectVersionById($row->version_id);
        if (empty($version_row) || $version_row->del_flag == 1) {
            die('版本数据不存在');
        } else if ($version_row->status != 30) {
            die('版本数据状态不正确');
        }
        $jrow = $version_row->toArray();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['bom_list'] = $s->getBomList($row);
        $jrow['drawing_images'] = [];
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
    }

    /**
     * @acl({'link':'mes:drawing:list'})
     */
    public function statusAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new DrawingService();
            $ret = new JsonData();
            $ret->message = $s->saveStatus();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function createAction($uid,$type='')
    {
        $ps = new ProductService();
        $row = $ps->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        $rs = new DrawingService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $rtn =  $rs->create($row);
            $ret->message = $rtn->message;
            if (empty($ret->message)){
                $ret->uid = $rtn->uid;
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new MesDrawing())->toArray();
        $oss_util = new FileService();
        $jrow['version_code'] = '1.0.0';
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['drawing_url'] = CvtUtil::nullToBlank($jrow['drawing_url']);
        $jrow['files'] = [];
        $this->view->uid = $uid;
        $this->view->jsonData = json_encode($jrow);
        if ($type == 2){
            $this->view->pick('drawing/multiple');
        }
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new DrawingService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function printAction($uid)
    {
        $s = new DrawingService();
        $row = $s->getPrintData($uid);
        if (empty($row)){
            die(ErrorHelper::WRONG_INPUT);
        }
        $oss_util = new FileService();
        $row['base_path'] = $oss_util->getImagePath();
        $row['width'] = 800;
        $row['page_width'] = 1200;
        $this->view->uid = $row['uid'];
        $this->view->jsonData = json_encode($row);
    }

    /**
     * @skipacl
     */
    public function uploadbase64Action()
    {
        $this->setJsonResponse();
        $s = new DrawingService();
        return json_encode($s->saveBase64());
    }
}