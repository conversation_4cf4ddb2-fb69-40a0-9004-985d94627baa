{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/bootstrap-select/css/bootstrap-select.min.css') %}
{% do assets.collection('js').addJs('static/global/plugins/bootstrap-select/js/bootstrap-select.min.js') %}
{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <!-- 基本信息 -->
                    <div class="row">
                        {% if dispatcher.getActionName() == 'edit' %}
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">单据号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" readonly>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>检验单号</label>
                                <div class="col-sm-8">
                                    <select class="form-control" name="inspection_code" v-model="inspection_code" @change="loadInspectionData" required>
                                        <option value="">请选择检验单号</option>
                                        <option v-for="item in inspectionOptions" :key="item.id" :value="item.check_code">
                                            ${ item.check_code }
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>处理日期</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control date dtpicker" placeholder="请输入处理日期" name="handling_date" v-model="handling_date" required/>
                                        <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-if="inspection_code != ''">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">供应商名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 物料信息 -->
                    <div class="row" v-if="inspection_code != ''">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">规格型号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="goods_model" v-model="goods_model" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">物料名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="goods_name" v-model="goods_name" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" v-if="inspection_code != ''">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">计量单位</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="goods_unit" v-model="goods_unit" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">到货数量</label>
                                <div class="col-sm-8">
                                    <input type="number" class="form-control" name="arrival_quantity" v-model="arrival_quantity" readonly>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- 数量信息 -->
                    <div class="row" v-if="inspection_code != ''">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">合格品数量</label>
                                <div class="col-sm-8">
                                    <input type="number" class="form-control" name="available_qty" :value="available_qty" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">不良品数量</label>
                                <div class="col-sm-8">
                                    <input type="number" class="form-control" name="rejected_qty" :value="rejected_qty" readonly>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- 不良品原因 -->
                    <div class="row" v-if="inspection_code != ''">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><span class="required">*</span>不良品原因</label>
                                <div class="col-sm-10">
                                    <select name="defect_reasons_dis" v-model="defect_reasons_dis" class="bs-select form-control" multiple data-size="8">
                                        <option v-for="(item,index) in defectReasonOptions" :value="item.id">${ item.name }</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 处理方式 -->
                    <div class="row" v-if="inspection_code != ''">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><span class="required">*</span>不良品处理方式</label>
                                <div class="col-sm-10">
                                    <div class="radio-list">
                                        <label class="radio-inline" v-for="(label, value) in handlingMethodOptions" :key="value">
                                            <input type="radio" name="handling_method" :value="value" v-model="handling_method" @change="calculateQuantities(1)">
                                            ${ label }
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 部分处理的数量输入 -->
                    <div class="row" v-if="showPartialInputs && inspection_code != ''">
                        <div class="col-sm-6" v-if="handling_method === 'partial_return'">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">退货数量</label>
                                <div class="col-sm-8">
                                    <input type="number" class="form-control" v-model="return_quantity" min="0" :max="arrival_quantity" @input="calculateQuantities(2)">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-if="handling_method === 'partial_scrap'">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">报废数量</label>
                                <div class="col-sm-8">
                                    <input type="number" class="form-control" v-model="scrap_quantity" min="0" :max="arrival_quantity" @input="calculateQuantities(3)">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" >
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                                                    

                    {{ partial('form') }}
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: Object.assign({}, {{ jsonData }}, {
            // 其他初始化数据
            handlingMethodOptions: [],
            return_quantity: 0,
            scrap_quantity: 0,
            defect_reasons_dis: {{defect_reasons_dis|json_encode}},
            defectReasonOptions: {{defectReasonOptions|json_encode}}
        }),
        methods: {
            // 加载检验单数据
            loadInspectionData: function() {
                if (!this.inspection_code) return;
                let self = this;
                commonAjaxRequest('{{ url('/purchase/defecthandling/getinfo') }}', {
                    inspection_code: this.inspection_code
                }, function(rs) {
                    // 成功回调
                    var data = rs.data[0];
                    self.inspection_id = data.inspection_id;
                    self.goods_id = data.goods_id;
                    self.goods_model = data.goods_model;
                    self.goods_name = data.goods_name;
                    self.goods_unit = data.goods_unit;
                    self.arrival_quantity = data.quantity;
                    self.supplier_name = data.supplier_name;
                    self.supplier_id = data.supplier_id;
                    self.calculateFinalQuantity();
                    self.$nextTick(() => {
                        var bsSelectOption = {
                            iconBase: 'fa',
                            tickIcon: 'fa-check',
                            noneSelectedText: '请选择'
                        };
                        $('.bs-select').selectpicker(bsSelectOption);
                    });
                });
            },


            // 根据处理方式计算合格品和不良品数量
            calculateQuantities: function(type) {
                var arrivalQty = this.arrival_quantity || 0;
                if (type == 1) {
                    this.return_quantity = 0;
                    this.scrap_quantity = 0;
                }

                switch (this.handling_method) {
                    case 'all_accept':
                        // 全部让步接收：全部都是合格品
                        this.available_qty = arrivalQty;
                        this.rejected_qty = (this.arrival_quantity || 0) - (this.available_qty || 0);
                        break;
                        
                    case 'partial_return':
                        // 部分退货：合格品 = 到货数量 - 退货数量
                        var returnQty = this.return_quantity || 0;
                        this.available_qty = arrivalQty - returnQty;
                        this.rejected_qty = (this.arrival_quantity || 0) - (this.available_qty || 0);
                        break;
                        
                    case 'partial_scrap':
                        // 部分报废：合格品 = 到货数量 - 报废数量
                        var scrapQty = this.scrap_quantity || 0;
                        this.available_qty = arrivalQty - scrapQty;
                        this.rejected_qty = (this.arrival_quantity || 0) - (this.available_qty || 0);
                        break;
                        
                    case 'all_reject':
                        // 全部拒收：没有合格品
                        this.available_qty = 0;
                        this.rejected_qty = (this.arrival_quantity || 0) - (this.available_qty || 0);
                        break;
                        
                    default:
                        // 默认：全部为合格品
                        this.available_qty = arrivalQty;
                        this.rejected_qty = (this.arrival_quantity || 0) - (this.available_qty || 0);
                }
            },

            // 计算最终入库数量（保持兼容性）
            calculateFinalQuantity: function() {
                this.calculateQuantities();
            },

            // 验证表单
            validateForm: function() {
                // 验证是否选择了不良品原因
                if (!this.defect_reasons_dis || this.defect_reasons_dis.length === 0) {
                    toastr.error('请选择不良品原因');
                    return false;
                }

                // 验证是否选择了处理方式
                if (!this.handling_method) {
                    toastr.error('请选择不良品处理方式');
                    return false;
                }

                // 验证部分处理的数量不能超过到货数量
                if (this.handling_method === 'partial_return' && (safeNumber(this.return_quantity) <= 0 || safeNumber(this.return_quantity) > safeNumber(this.arrival_quantity))) {
                    toastr.error('退货数量应该大于0，并且不能超过到货数量');
                    return false;
                }
                if (this.handling_method === 'partial_scrap' && (safeNumber(this.scrap_quantity) <= 0 || safeNumber(this.scrap_quantity) > safeNumber(this.arrival_quantity))) {
                    toastr.error('报废数量应该大于0，兵器不能超过到货数量');
                    return false;
                }

                return true;
            },

            submit: function (e) {
                e.preventDefault();
                if (!this.validateForm()) return;
                if (!$('#form').validate().form()) return;

                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('purchase/defecthandling/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('purchase/defecthandling/create') }}';
                {% endif %}

                var param = JSON.parse(JSON.stringify(this.$data));

                param.handling_method_name = this.handlingMethodOptions[this.handling_method] || '';
                param.ext_data = encodeURI(JSON.stringify(this.ext_data || {})).replace(/\+/g, '%2B');

                commonAjaxRequest(url, param, function(rs) {
                    // 成功回调
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                });
            }
        },
        computed: {
            showPartialInputs: function() {
                return ['partial_return', 'partial_scrap'].indexOf(this.handling_method) !== -1;
            }
        },
        mounted: function() {
            if (!Array.isArray(this.defect_reasons)) {
                this.defect_reasons = [];
            }

            if (!this.inspection_code) {
                this.inspection_code = '';
            }
            
            this.return_quantity = this.rejected_qty;
            this.scrap_quantity = this.rejected_qty;

            this.handlingMethodOptions = {
                'all_accept': '全部让步接收',
                'partial_return': '部分退货(其余接收)',
                'partial_scrap': '部分报废(其余接收)',
                'all_reject': '全部拒收',
            };
        
        }
    });

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

</script>


<style>
.defect-reasons-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background-color: #f9f9f9;
}

.reason-item {
    margin-bottom: 10px;
}

.reason-item:last-child {
    margin-bottom: 0;
}

.radio-list .radio-inline {
    margin-right: 20px;
}

.radio-list .radio-inline input[type="radio"] {
    margin-right: 5px;
}
</style>

{{ partial('form_script') }}