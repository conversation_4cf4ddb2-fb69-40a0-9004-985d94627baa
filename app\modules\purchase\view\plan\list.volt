{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div id="app" class="page-content">
    <h3 class="page-title">采购用料计划</h3>
    <div class="search-page">
        <div style="display: flex;flex-direction: row">
            <div>
                <div class="plan-header" style="width: 160px">
                </div>
                <div v-for="(goods_item,goods_key) in goods_data" :key="goods_key">
                    <div class="plan-item" style="background-color: #0683FF;;padding: 5px;width: 160px;color: #fff">
                        <div><span v-text="goods_item.code"></span>/<span v-text="goods_item.name"></span></div>
                        <div><span v-text="goods_item.model"></span>/<span v-text="goods_item.spec"></span></div>
                        <div>当前库存：<span v-text="goods_item.stock_cnt"></span>(<span v-text="goods_item.unit"></span>)</div>
                    </div>
                </div>
            </div>
            <div style="flex: 1;overflow-x: auto">
                <div style="display: flex;flex-direction: row">
                    <div v-for="(day_item,day_idx) in day_list" :key="day_idx" class="plan-header">
                        <span v-text="day_item.date_show + ' ' + day_item.week"></span>
                    </div>
                </div>
                <div style="display: flex;flex-direction: row" v-for="(goods_item,goods_key) in goods_data" :key="goods_key">
                    <div class="plan-item" v-for="(day_item,day_idx) in goods_item.day_list" :key="day_idx">
                       <div v-if="day_item.plan_cnt != ''">
                           <div>计划用量：<span v-text="day_item.plan_cnt"></span>(<span v-text="goods_item.unit"></span>)</div>
                           <div>期末库存：<span v-text="day_item.last_cnt"></span>(<span v-text="goods_item.unit"></span>)</div>
                           <div v-if="day_item.last_cnt <= 0" style="padding: 2px;background-color: red;width: 80px;color: white;text-align: center;">库存预警</div>
                       </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            goods_data: {{ goods_data }},
            day_list: {{ day_list }}
        },
        methods: {

        }
    });
</script>


<style scoped>
    .plan-header{
        width: 160px;
        height: 40px;
        line-height: 40px;
        background-color: #0683FF;
        border-width:  0 2px 2px 0;
        border-color: #F2F2F2;
        border-style: solid;
        text-align: center;
        flex-shrink: 0;
        color: #FFF;
    }

    .plan-item{
        width: 160px;
        height: 80px;
        background-color: #FFF;
        border-width:  0 2px 2px 0;
        border-color: #F2F2F2;
        border-style: solid;
        flex-shrink: 0;
        position: relative;
        padding: 5px;
    }

    .plan-item::selection {
        background: rgba(255,255,255,0);
    }

</style>