<?php
namespace Envsan\Modules\Trade\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Trade\Model\TradeInvoice;
use Envsan\Modules\Trade\Model\TradeReceive;
use Envsan\Modules\Trade\Service\TradeInvoiceService;

/**
 * @name("开票")
 */
class InvoiceController extends SuperController
{
    private $page_id = 35;

    /**
     * @name("管理")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new TradeInvoiceService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("新增")
     */
    public function createAction()
    {
        $s = new TradeInvoiceService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $oss_util = new FileService();

        $jrow = (new TradeInvoice())->toArray();
        $jrow['invoice_date'] = date('Y-m-d');
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['files'] = [];
        $this->view->jsonTradeInvoice = json_encode($jrow);

        $this->view->order_list = $s->getOrderList();
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'trade:invoice:create'})
     */
    public function editAction($uid = '')
    {
        $s = new TradeInvoiceService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $oss_util = new FileService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['base_path'] = $oss_util->getImagePath();
        $this->view->jsonTradeInvoice = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->order_list = $s->getOrderList();
        $this->view->pick('invoice/create');
    }

    /**
     * @acl({'link':'trade:invoice:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new TradeInvoiceService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new TradeInvoiceService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $table = new TableService();
        $oss_util = new FileService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $order_rows = $s->getOrderList($row->order_id);
        if (count($order_rows) > 0) {
            $order_code = $order_rows[0]->order_code;
            $customer_name = $order_rows[0]->customer_name;
        } else {
            $order_code = '';
            $customer_name = '';
        }

        $jrow = $row->toArray();
        $jrow['order_code'] = $order_code;
        $jrow['customer_name'] = $customer_name;
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $this->view->jsonTradeInvoice = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new TradeInvoiceService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}