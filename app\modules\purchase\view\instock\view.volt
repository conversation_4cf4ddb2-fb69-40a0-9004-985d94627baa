{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">入库单号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">采购订单</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">到货单号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">供应商</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">入库日</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="instock_date" v-model="instock_date" readonly/>
                                </div>
                            </div>
                        </div>
                        {{ partial('view') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" rows="3" readonly></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                <thead>
                                <tr>
                                    <th>编码</th>
                                    <th>名称</th>
                                    <th>规格型号</th>
                                    <th>质检状态</th>
                                    <th>库存计量数量</th>
                                    <th>采购计量数量</th>
                                    <th>未税单价</th>
                                    <th>含税单价</th>
                                    <th>未税总金额</th>
                                    <th>含税总金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="row, index in detail_data">
                                    <td><span v-text="row.goods_code"></span></td>
                                    <td><span v-text="row.goods_name"></span></td>
                                    <td><span v-text="row.goods_model"></span></td>
                                    <td>
                                        <div style="display: flex;align-items: center;">
                                            <div><span v-text="row.check_flag == 1 ? '已质检' : '无需质检'"> </span></div>
                                            <div style="display: flex;align-items: center;">
                                                <a v-for="url, img_idx in row.check_images" class="lightbox-a" :href="base_path + url" :data-lightbox="'check_' + index" :data-title="row.goods_name" style="margin-left: 10px;">
                                                    <img style="max-width: 50px;max-height: 34px;" :src="base_path + url" class="lightbox-image" />
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span v-text="row.quantity + '('+row.goods_deputy_unit+')'"></span></td>
                                    <td><span v-text="row.purchase_quantity + '('+row.goods_unit+')'"></span></td>
                                    <td><span v-text="row.price + '(元/'+row.goods_unit+')'"></span></td>
                                    <td><span v-text="row.price_hs + '(元/'+row.goods_unit+')'"></span></td>
                                    <td><span v-text="row.total_money + '(元)'"></span></td>
                                    <td><span v-text="row.total_money_hs + '(元)'"></span></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonInstock }},
        methods: {

        }
    });
</script>