{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>是否新品</label>
                                <div class="col-sm-8" style="padding-top: 8px">
                                    <a  href="javascript:void(0)" @click="new_flag == 1 ? new_flag = 0 : new_flag = 1">
                                        <span v-if="new_flag == 1" class="label label-warning">新品</span>
                                        <span v-else class="label label-default">新品</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-show="new_flag == 0">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>订单产品</label>
                                <div class="col-sm-8">
                                    <select class="bs-select form-control" name="product_id" @change="handleProductChange" v-model="product_id"  data-live-search="true" data-size="8" required>
                                        <option value="">请选择产品</option>
                                        {% for item in productList %}
                                            <option value="{{ item.id }}">{{ item.code }}({{ item.name }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-show="new_flag == 1">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>产品名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" v-model="name" required maxlength="100"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-show="new_flag == 1">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>规格型号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" required maxlength="100"/>
                                </div>
                            </div>
                        </div>
                       <div class="col-sm-6" v-show="new_flag == 1">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">存货代码</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="inventory_code" v-model="inventory_code" >
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-show="new_flag == 1">
                            <div class="form-group">
                                <label class="col-sm-4 control-label" >库存单位<span class="required"> * </span></label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="inventory_unit" v-model="inventory_unit" maxlength="10" required >
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-show="new_flag == 1">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">销售单位<span class="required"> * </span></label>
                                <div  class="col-sm-8">
                                    <input type="text" class="form-control" name="purchase_unit" v-model="purchase_unit" maxlength="10" required >
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">税率</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" @input="onTaxRateChange()" number="true" name="tax_rate" v-model="tax_rate" required maxlength="8">
                                        <span class="input-group-addon">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-sm-6" v-show="new_flag == 1">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">是否批次管理<span class="required"> * </span></label>
                                <div class="col-sm-8">
                                    <select class="form-control bs-select"  name="is_batch_managed" v-model="is_batch_managed" required>
                                        <option value="">请选择</option>
                                        <option value="1">是</option>
                                        <option value="0">否</option>
                                    </select>
                                </div>
                            </div>
                        </div> -->
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>订单数量</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" number="true" name="cnt" v-model="cnt" required maxlength="8">
                                        <span class="input-group-addon">个</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-show="new_flag != 1">
                          <div class="form-group">
                            <label class="col-sm-4 control-label">库存数量</label>
                            <div class="col-sm-8">
                              <div class="input-group">
                                  <input type="number" class="form-control" number="true" name="stock_cnt" v-model="stock_cnt" :disabled="true">
                                  <span class="input-group-addon">个</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>未税单价</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" @input="onPriceChange()" number="true" name="price" v-model="price" required maxlength="8">
                                        <span class="input-group-addon">元/个</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>含税单价</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" @input="onPriceHsChange()" number="true" name="price_hs" v-model="price_hs" required maxlength="8">
                                        <span class="input-group-addon">元/个</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-show="new_flag == 1">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">单重</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" number="true" name="weight" v-model="weight" maxlength="9">
                                        <span class="input-group-addon">KG</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ partial('form') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                        <div>
                                            <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                删除
                                            </a>
                                        </div>
                                    </div>
                                    <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" @click="submit" class="btn btn-primary">提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }},
            lastInputField: 'price' 
        },
        methods: {
            onPriceChange() {
                this.lastInputField = 'price'; // 记录最后输入的是不含税单价
                if (this.price !== '' && this.price !== null) {
                    const priceValue = Number(this.price) || 0;
                    const rateValue = Number(this.tax_rate) || 0;
                    this.price_hs = (priceValue * (1 + rateValue / 100)).toFixed(2);
                }
            },

            onPriceHsChange() {
                this.lastInputField = 'price_hs'; // 记录最后输入的是含税单价
                if (this.price_hs !== '' && this.price_hs !== null) {
                    const priceHsValue = Number(this.price_hs) || 0;
                    const rateValue = Number(this.tax_rate) || 0;
                    if (rateValue === 0) {
                        this.price = priceHsValue.toFixed(2);
                    } else {
                        this.price = (priceHsValue / (1 + rateValue / 100)).toFixed(2);
                    }
                }
            },

            onTaxRateChange() {
                // 根据最后输入的字段来决定计算哪个价格
                if (this.lastInputField === 'price') {
                    // 如果最后输入的是不含税单价，则重新计算含税单价
                    if (this.price !== '' && this.price !== null) {
                        const priceValue = Number(this.price) || 0;
                        const rateValue = Number(this.tax_rate) || 0;
                        this.price_hs = (priceValue * (1 + rateValue / 100)).toFixed(2);
                    }
                } else if (this.lastInputField === 'price_hs') {
                    // 如果最后输入的是含税单价，则重新计算不含税单价
                    if (this.price_hs !== '' && this.price_hs !== null) {
                        const priceHsValue = Number(this.price_hs) || 0;
                        const rateValue = Number(this.tax_rate) || 0;
                        if (rateValue === 0) {
                            this.price = priceHsValue.toFixed(4);
                        } else {
                            this.price = (priceHsValue / (1 + rateValue / 100)).toFixed(2);
                        }
                    }
                }
            },
            handleProductChange : function (e) {
                e.preventDefault();
                this.stock_cnt = 0 ;
                this.tax_rate = null;
                if (e.target.value == '') {
                    this.onTaxRateChange();
                    return;
                }
                let param = {}
                param.productId = e.target.value
                let stock_url = '{{ url('trade/orderdetail/stockinfo/json') }}';
                let that = this;
                $.get(stock_url, param, (rs) => {
                    that.stock_cnt = rs.stock_cnt;
                    that.tax_rate = rs.tax_rate;
                    that.onTaxRateChange();
                });
            },
            submit(e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('trade/orderdetail/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('trade/orderdetail/create/' ~ order_uid) }}';
                {% endif %}

                commonAjaxRequest(url,
                    {
                        product_id:app.product_id,
                        new_flag:app.new_flag,
                        name:app.name,
                        code:app.code,
                        cnt:app.cnt,
                        price:app.price,
                        price_hs:app.price_hs,
                        inventory_code:app.inventory_code,
                        inventory_unit:app.inventory_unit,
                        purchase_unit:app.purchase_unit,
                        // is_batch_managed:app.is_batch_managed,
                        tax_rate:app.tax_rate,
                        weight:app.weight,
                        remarks:app.remarks,
                        files:encodeURI(JSON.stringify(app.files)),
                        ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B')
                    },
                    function(rs) {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                );
            },
            delFile:function (index) {
                this.files.splice(index);
            }
        }
    });

    initUpLoaderPdf('trade_order_detail');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}