<?php
namespace Envsan\Modules\Equ\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Equ\Model\EquCheckForm;
use Envsan\Modules\Equ\Service\EquCheckFormService;
use Envsan\Modules\Equ\Util\Constant;


/**
 * @name('检查模版')
 */
class CheckformController extends SuperController
{
    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new EquCheckFormService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setDetail($page->rows->toArray());
            return json_encode($page);
        }
        $this->view->types = Constant::$check_form_type;
    }

    /**
     * @acl({'link':'equ:checkform:list'})
     */
    public function createAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new EquCheckFormService();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $jrow = (new EquCheckForm())->toArray();
        $this->view->jsonEquCheckForm = json_encode($jrow);
        $this->view->types = Constant::$check_form_type;
    }

    /**
     * @acl({'link':'equ:checkform:list'})
     */
    public function editAction($uid = '')
    {
        $s = new EquCheckFormService();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->save($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $jrow = $row->toArray();
        $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
        $jrow['data'] = Constant::$form_data_template;
        $jrow['row_no'] = 0;
        $this->view->jsonEquCheckForm = json_encode($jrow);

        $this->view->uid = $row->uid;
        $this->view->defaultForm = Constant::$form_data_template;
    }

    /**
     * @acl({'link':'equ:checkform:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new EquCheckFormService();
            $ret = new JsonData();
            if ($rs->deleteByUid($this->request->getPost('uid', 'tstring')))
                $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}