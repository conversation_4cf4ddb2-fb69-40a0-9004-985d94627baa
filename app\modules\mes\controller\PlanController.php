<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Service\PlanService;
use Envsan\Modules\Printing\Service\TemplateService;


/**
 * @name("排产")
 */
class PlanController extends SuperController
{
    private $page_id = 6;
    private $search_page_id = 15;

    /**
     * @name("列表")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PlanService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList($this->page_id),JSON_UNESCAPED_UNICODE);
    }


    /**
     * @name("查询")
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PlanService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList($this->page_id),JSON_UNESCAPED_UNICODE);
    }

    /**
     * @acl({'link':'mes:plan:plan'})
     */
    public function refreshAction($equ_type_id){
        if($this->request->isPost()){
            $this->setJsonResponse();
            $s = new PlanService();
            $data = $s->getPlanData($equ_type_id);
            return json_encode($data);
        }
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PlanService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new PlanService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }
}