<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
<section class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <div class="form-body">
                <div id="tree"></div>
            </div>
            <div class="form-actions right" id="app">
                <button type="button" class="btn green" @click="confirm"><i class="fa fa-check"></i> 确定</button>
                <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
            </div>
        </div>
    </div>
</section>

<script>
    var app = new Vue({
        el:'#app',
        data:{
            id:'',uid:'',name:''
        },
        methods:{
            confirm:function(){
                var ids= $tree.jstree('get_selected');
                var group_name = "";
                var groups = $tree.jstree().get_selected(true);
                for (var index in groups) {
                    if (index == groups.length-1){
                        group_name += groups[index].text;
                    } else {
                        group_name += groups[index].text + ",";
                    }
                }
                top.window.layer_result = 'ok';
                top.window.result_ids = ids.toString();
                top.window.result_names = group_name;
                top.layer.close(top.layer.getFrameIndex(window.name));
            }
        }
    });

    var $tree = $('#tree');

    $(document).ready(function () {
        $tree.jstree({
            'plugins':['wholerow', 'checkbox'],
            'core': {'data': {{ jsonTree }}},
            'checkbox':{
                "three_state":false
            }
        });

        $tree.on('check_node.jstree', function (e, data) {
            var check_ids = $tree.jstree('get_checked')
            console.log(check_ids)
        });
//        $tree.on('select_node.jstree', function (e, data) {
//            app.id = data.node.id;
//            app.uid = data.node.original.uid;
//            app.name = data.node.text;
//        });
//
//        $tree.on('dblclick.jstree', function (e) {
//            parent.window.layer_result = app.$data;
//            parent.layer.close(parent.layer.getFrameIndex(window.name));
//        });
    });
</script>
