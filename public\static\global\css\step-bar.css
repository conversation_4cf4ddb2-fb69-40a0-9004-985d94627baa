.step-bar {
    margin-bottom: 20px;
    font-size: 15px;
}

.title-row, .step-row, .time-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.title-row .title-item, .time-row .time-item {
    flex: 1;
    text-align: center;
}

.title-row .title-item.active {
    color: #18a689;
}

.step-row {
    margin: 10px 0;
}

.step-row .step-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.step-row .step-item:first-child .step-line:first-child,
.step-row .step-item:last-child .step-line:last-child {
    visibility: hidden;
}

.step-row .step-item .step-line {
    width: 42%;
    height: 1px;
    background-color: #B7B7B7;
}

.step-row .step-item.active .step-line,
.step-row .step-item.now .step-line:first-child {
    background-color: #18a689;
}

.step-row .step-item .step-point {
    width: 25px;
    height: 25px;
    border-radius: 25px;
    border: 1px solid #B7B7B7;
    display: flex;
    justify-content: center;
    align-items: center;
}

.step-row .step-item .step-point::before {
    content: ' ';
    width: 15px;
    height: 15px;
    border-radius: 15px;
    background-color: #B7B7B7;
    display: inline-block;
}

.step-row .step-item.active .step-point,
.step-row .step-item.now .step-point {
    border: 1px solid #18a689;
}

.step-row .step-item.active .step-point::before,
.step-row .step-item.now .step-point::before {
    background-color: #18a689;
}