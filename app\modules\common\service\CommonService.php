<?php
namespace Envsan\Modules\Common\Service;

use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\ModelUtil;
use Envsan\Modules\Sys\Model\SysDict;
use Phalcon\Mvc\User\Component;

class CommonService extends Component
{
    public function getDictList($dict_type,$owner_id = ''){
        if (empty($owner_id)){
            $owner_id = SessionData::ownerId();
        }
        return SysDict::find(['del_flag = 0 and dict_type = ?1 and owner = ?2','bind'=>[1 => $dict_type,2 => $owner_id],'order'=>'sort asc']);
    }

    public function getUserList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.real_name
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id asc');
        ModelUtil::limitGroup('t1.group_id', $builder);
        return $builder->getQuery()->execute();
    }
}