<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesNotice;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesPlan;
use Envsan\Modules\Purchase\Model\PurchaseApplyEntrust;
use Envsan\Modules\Purchase\Model\PurchaseOrder;
use Envsan\Modules\Purchase\Model\PurchaseOrderDetail;
use Envsan\Modules\Purchase\Model\PurchaseOrderwwDetail;
use <PERSON>vsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Work\Service\DataCommonService;

class OrderWwService extends BaseService
{
    public function selectPlanAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t.status,
                t.status_name,
                t.quantity,
                t.code as entrust_code,
                t1.remarks,
                t1.product_id,               
                t2.code as product_code,
                t2.name as product_name,
                t3.name as customer_name,
                t4.code as notice_code,
                t5.name as bom_name,
                t6.start_date,
                t6.end_date,
                round(ifnull(t7.finish_cnt,0) - ifnull(t9.quantity,0) ,2) as wait_out_cnt,
                ifnull(t9.quantity,0) as out_cnt,
                t9.outstock_date as out_date,
                t9.supplier_name,
                t8.finish_cnt as in_cnt,
                t8.work_date as in_date
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustPlan','t.id = t6.entrust_id','t6')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewBomFinishCnt','t.notice_detail_id = t1.id and t5.pid = t7.bom_id','t7')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewBomFinishCnt','t.notice_detail_id = t1.id and t.product_bom_id = t8.bom_id','t8')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustOut','t.id = t9.entrust_id','t9')
            ->where('t1.del_flag = 0 and t1.status < 20 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.status asc, t1.id asc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectByUid($uid)
    {
        return PurchaseOrder::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function selectWwPlanByUid($uid = null)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t.status,
                t.status_name,
                t.code as entrust_code,
                t2.code as product_code,
                t2.name as product_name,
                t3.name as customer_name,
                t4.code as notice_code,
                t5.name as bom_name
                
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwPlan', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t5.id','t5')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t.uid = ?2', [1 => SessionData::ownerId(), 2 => $uid])
            ->orderBy('t1.status asc, t1.id');

        return $builder->getQuery()->execute()->getFirst();
    }


//    public function selectWwPlanDropdown()
//    {
//        $builder = $this->modelsManager->createBuilder()
//            ->columns('
//                t.id,
//                t.uid,
//                t.status,
//                t.status_name,
//                t.code as entrust_code,
//                t2.code as product_code,
//                t2.name as product_name,
//                t3.name as customer_name,
//                t4.code as notice_code,
//                t5.name as bom_name
//
//            ')
//            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id','t1')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
//            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
//            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.product_bom_id = t5.id','t5')
//            ->where('t1.del_flag = 0 and t1.owner = ?1 and t.status < ?2', [1 => SessionData::ownerId(), 2 => '20'])
//            ->orderBy('t1.status asc, t1.id');
//
//        return $builder->getQuery()->execute()->toArray();
//    }

    public function passSave($id)
    {
        $os = new OrderWwService();
        $row = $os->selectById($id);
        if (empty($row) || $row->del_flag == 1) {
            throw new \Exception("记录已失效");
        } else if ($row->status != 20) {
            throw new \Exception("记录状态已变更");
        }

        $now = DateUtil::now();
        $user = SessionData::user();

        $row->status = 30;
        $row->status_name = Constant::$purchase_order_status[$row->status];
        $row->review_date = $now;
        $row->review_by = $user->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->save();

        $details = CvtUtil::emptyToArray($row->detail_data);

        foreach ($details as $item)
        {
            $detail_row = new PurchaseOrderDetail();
            $detail_row->uid = UUID::make();
            $detail_row->order_id = $row->id;
            $detail_row->apply_id = $item['id'];
            $detail_row->process_id = $item['process_id'];
            $detail_row->goods_id = $item['goods_id'];
            $detail_row->goods_code = $item['goods_code'];
            $detail_row->goods_name = $item['product_name'];
            $detail_row->goods_model = $item['product_model'];
            // 采购单位
            $detail_row->goods_unit = $item['price_unit'];
            // 是否质检 TODO
            $detail_row->check_flag = 0;
            // 库存单位
            $detail_row->goods_deputy_unit = $item['measurement_unit'];
            // 库存数量
            $detail_row->quantity = CvtUtil::blankToNull($item['quantity']);
            // 采购数量
            $detail_row->purchase_quantity = CvtUtil::blankToNull($item['pricing_quantity']);
            $detail_row->price = CvtUtil::blankToNull($item['price']);
            $detail_row->price_hs = CvtUtil::blankToNull($item['price_hs']);
            $detail_row->total_money = CvtUtil::blankToNull($item['total_money']);
            $detail_row->total_money_hs = CvtUtil::blankToNull($item['total_money_hs']);
            $detail_row->tax_rate = CvtUtil::blankToNull($item['tax_rate']);
            $detail_row->conversion_rate = CvtUtil::blankToNull($item['conversion_rate']);
            $detail_row->update_date = $now;
            $detail_row->update_by = $user->id;
            $detail_row->del_flag = 0;
            $detail_row->owner = $user->owner;
            $detail_row->save();
        }
    }

    public function rejectSave($id, $reject_remarks)
    {
        $os = new OrderService();
        $row = $os->selectById($id);
        if (empty($row) || $row->del_flag == 1) {
            throw new \Exception("记录已失效");
        } else if ($row->status != 20) {
            throw new \Exception("记录状态已变更");
        }

        $now = DateUtil::now();
        $user = SessionData::user();

        $row->status = 10;
        $row->status_name = Constant::$purchase_order_status[$row->status];
        $row->reject_status = 1;
        $row->reject_remarks = $reject_remarks;
        $row->review_date = $now;
        $row->review_by = $user->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->save();
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.order_code,
                a.order_date,
                a.remarks,
                a.status,
                a.status_name,
                a.total_money,
                a.ext_val,
                s.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a.supplier_id = s.id', 's')
            ->where('a.del_flag = 0 and a.order_type = 2 and a.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('a.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function createWwPlan()
    {
        return $this->build('create');
    }

    public function build($act)
    {
        return $this->executeInTransaction(function () use ($act) {

            $notice_detail_uid = $this->request->getPost('notice_detail_uid', 'tstring');
            $notice_detail = MesNoticeDetail::findFirst( ['uid = ?1 and del_flag = 0 and status = 10', 'bind' => [1 => $notice_detail_uid]]);
            if (empty($notice_detail)) {
                return $this->error('没有生产批次');
            }
            $notice_detail_id = $notice_detail->id;
            $product_id = $this->request->getPost('product_id', 'tstring');
            $product_bom_id = $this->request->getPost('bom_id', 'tstring');
            $product_bom_name = $this->request->getPost('bom_name', 'tstring');
            $notice_id = $this->request->getPost('notice_id', 'tstring');
            $product_quantity = $this->request->getPost('entrust_quantity', 'tstring');
            $remarks = $this->request->getPost('remarks', 'tstring');


            if (empty($notice_id)
                || CheckUtil::is_empty($notice_detail_id)
                || CheckUtil::is_empty($product_bom_id)
                || CheckUtil::is_empty($product_quantity)
            ) {
                return $this->error(ErrorHelper::WRONG_INPUT) ;
            }

            $mesPlans = MesPlan::find(['notice_detail_id = ?1 and del_flag = 0 and bom_id = ?2', 'bind' => [1 => $notice_detail_id, 2 => $product_bom_id]]);
            if ($mesPlans->count() > 0) {
                return $this->error('已经生产排产了，不可以外委，请先取消排产！');
            }

            $mesNotice = MesNotice::findFirst(['id = ?1 and del_flag = 0 and status = 30', 'bind' => [1 => $notice_id]]);

            if (empty($mesNotice)) {
                return $this->error('没有生产批次');
            }
            // 获取工序数据
            $detail_data = $mesNotice->detail_data;
            if (!empty($detail_data)) {
                $process_list = json_decode($detail_data, true);
                if (is_array($process_list)) {
                    // 更新MesNotice表
                    $this->updateNoticeProcessData($mesNotice , $process_list, $product_bom_name);
                }
            }

            $now = DateUtil::now();
            $user = SessionData::user();

            $row = new PurchaseApplyEntrust();
            $row->type = 2;
            $ss = new SequenceService();
            $row->code = $ss->useSequence(22);
            $row->del_flag = 0;
            $row->owner = $user->owner;
            $row->update_date = $now;
            $row->update_by = $user->id;
            // 产品的库存数量
            $row->quantity = CvtUtil::blankToNull($product_quantity);
            // 以下单数据
            $row->ordered_quantity = 0;
            // 剩余数量
            $row->remaining_quantity = CvtUtil::blankToNull($product_quantity);
            // 产品工序单位物料用量
            $row->notice_id = CvtUtil::blankToNull($notice_id);
            $row->product_id = CvtUtil::blankToNull($product_id);
            $row->notice_detail_id = CvtUtil::blankToNull($notice_detail_id);
            $row->product_bom_id = CvtUtil::blankToNull($product_bom_id);
            $row->product_bom_name = $product_bom_name;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->uid = UUID::make();
            $row->status = 10;
            $row->status_name = Constant::$purchase_apply_entrust_status[$row->status];

            // 保存外委计划
            $row->save();
        });
    }

    /**
     * 更新MesNotice表中的工序数据
     * @param MesNotice $mesNotice MesNotice对象
     * @param array $process_list 工序列表数据
     * @param string $bom_name 当前BOM名称
     */
    private function updateNoticeProcessData($mesNotice, $process_list, $bom_name)
    {
        // 遍历工序数据，找到匹配的BOM并更新sel状态
        foreach ($process_list as &$process_item) {
            if (isset($process_item['list']) && is_array($process_item['list'])) {
                foreach ($process_item['list'] as &$process) {
                    if (isset($process['name']) && $process['name'] === $bom_name) {
                        // 找到匹配的工序名称，设置为选中状态
                        $process['sel'] = '1';
                    } else {
                        // 其他工序设置为未选中状态（可选）
                        $process['sel'] = '0';
                    }
                }
                unset($process); // 解除引用
            }
        }
        unset($process_item); // 解除引用
        
        // 保存更新后的数据
        $mesNotice->detail_data = json_encode($process_list, JSON_UNESCAPED_UNICODE);
        $mesNotice->update_date = DateUtil::now();
        $mesNotice->update_by = SessionData::user()->id;
        $mesNotice->save();
    }

    public function selectPlanDetail($ids)
    {
        // 产品名称
        $product_name =  $this->request->get('product_name', 'tstring');
        // 产品规格型号
        $product_model =  $this->request->get('product_model', 'tstring');
        // 工序名称
        $bom_name =  $this->request->get('bom_name', 'tstring');
        // 委外计划号
        $ww_plan_code =  $this->request->get('ww_plan_code', 'tstring');
        // 生产批次号
        $notice_code =  $this->request->get('notice_code', 'tstring');
        $builder=  $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                0 as sel,
                null as process_id,
                t1.uid,
                t4.name as product_name,
                t4.code as product_model,
                t3.name as bom_name, 
                t.quantity,
                t.product_id,
                t.product_bom_id,
                t.notice_id,
                t.notice_detail_id,
                t.type,
                t.code as ww_plan_code,
                t.remarks,
                t.status,
                t.status_name,
                t3.ship_type_id,
                t2.code as notice_code,
                t5.code as order_code,
                t6.code as goods_code,
                t6.id as goods_id,
                if(t3.pid is null ,
                    round(ifnull(t7.plan_cnt, 0) - ifnull(t10.quantity, 0), 2),
                    round(ifnull(t8.finish_cnt, 0) - ifnull(t10.quantity, 0) ,2)
                ) as wait_out_cnt,
                ifnull(t10.quantity,0) as out_cnt,
                t9.finish_cnt as in_cnt
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id','t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't.notice_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom', 't.product_bom_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't4.id = t3.product_id', 't4')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods', 't4.goods_id = t6.id', 't6')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder', 't2.order_id = t5.id', 't5')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustPlan','t.id = t7.entrust_id','t7')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewBomFinishCnt','t.notice_detail_id = t1.id and t3.pid = t8.bom_id','t8')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewBomFinishCnt','t.notice_detail_id = t1.id and t.product_bom_id = t9.bom_id','t9')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustOut','t.id = t10.entrust_id','t10')
            ->where('t.del_flag = 0 and t.status < 20 and t.owner = ?1'
                , [1 => SessionData::ownerId()]);

        if (!empty($ids)) {
            $builder->notInWhere('t1.id', explode(',', $ids));
        }

        if (!empty($product_name)) {
            $builder->andWhere("t4.name like ?2", [2 => $product_name]);
        }

        if (!empty($product_model)) {
            $builder->andWhere("t4.code = ?4", [4 => $product_model]);
        }

        if (!empty($bom_name)) {
            $builder->andWhere("t3.name = ?5", [5 => $bom_name]);
        }

        if (!empty($ww_plan_code)) {
            $builder->andWhere("t1.code = ?6", [6 => $ww_plan_code]);
        }


        if (!empty($notice_code)) {
            $builder->andWhere("t2.code = ?7", [7 => $notice_code]);
        }


        return $builder;
    }

    public function detail($row)
    {
        return $this->executeInTransaction(function () use ($row) {
            $type = $this->request->getPost('type', 'tstring');
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');
            $order_date = $this->request->getPost('order_date', 'tstring');
            // 外委计划ID
            $detail_list = str_replace('%2B', '+', urldecode($this->request->getPost('detail_data', ['string', 'trim'])));
            $detail_data = json_decode($detail_list, true);
            if (empty($type) || empty($order_date) || empty($detail_data) || empty($supplier_id)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }

            $total_money = 0;
            $total_money_hs = 0;
            foreach ($detail_data as $item) {
                // 保存的时候没有金额出错
                if ($item['process_id']) {
                    $money  = CvtUtil::emptyToDouble($item['total_money']);
                    $money_hs  = CvtUtil::emptyToDouble($item['total_money_hs']);
                    // 总的采购金额（未税）
                    $total_money += $money;
                    // 总的采购金额（含税）
                    $total_money_hs += $money_hs;
                }
            }

            $now = DateUtil::now();
            $user = SessionData::user();
            $row->order_date = $order_date;
            $row->supplier_id = CvtUtil::blankToNull($supplier_id);
            $row->status = $type == 2 ? 20 : 10;
            $row->status_name = Constant::$purchase_order_status[$row->status];
            $row->total_money = round($total_money, 2);
            $row->total_money_hs = round($total_money_hs, 2);
            $row->detail_data = CvtUtil::arrayToNull($detail_data);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (empty($row->id)) {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                // 外委加工单
                $row->order_type = 2;
                $row->order_code = $ss->useSequence(4);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            $row->save();

            // 把之前绑定的解绑
            $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseApplyEntrust';
            $phql .= ' SET status = 10, status_name = ?4, purchase_order_id = null, update_date = ?2, update_by = ?3';
            $phql .= ' WHERE del_flag = 0 and purchase_order_id = ?1';

            $result = $this->modelsManager->executeQuery($phql, [
                1 => $row->id,
                2 => $now,
                3 => $user->id,
                4 => Constant::$purchase_apply_entrust_status[10]
            ]);
            if (!$result->success()) {
                return $this->error($result->getMessage());
            }

            // 绑定外委申请
            foreach ($detail_data as $detail) {
                $apply_row = PurchaseApplyEntrust::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $detail['id']]]);
                if (empty($apply_row)) {
                    return $this->error('外委申请已失效');
                } else if (!empty($apply_row->purchase_order_id)) {
                    if ($apply_row->purchase_order_id != $row->id) {
                        return $this->error('外委申请已绑定其他订单');
                    }
                } else {
                    $apply_row->purchase_order_id = $row->id;
                    $apply_row->status = 20;
                    $apply_row->status_name = Constant::$purchase_apply_entrust_status[$apply_row->status];
                    $apply_row->update_date = $now;
                    $apply_row->update_by = $user->id;
                    $apply_row->save();
                }
            }

            if ($type == 2) {
                $dcm = new DataCommonService();
                $dcm->submitDesign($row->id, 5, $user->group_id);
            }
        });
    }

//    public function changeBomDetail($bom_id)
//    {
//        $builder = $this->modelsManager->createBuilder()
//            ->columns('
//                t1.id,
//                t1.uid,
//                t1.goods_id,
//                t1.code as goods_code,
//                t1.name as goods_name,
//                t1.model as goods_model,
//                t1.quantity as bom_quantity,
//                t1.name
//            ')
//            ->addFrom('Envsan\Modules\Mes\Model\MesProductGoods', 't1')
//            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods','t1.goods_id = t2.id','t2')
//            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.product_bom_id = ?2',
//                [1 => SessionData::ownerId(), 2 => $bom_id]);
//        return $builder->getQuery()->execute()->toArray();
//    }


    public function changeNoticeDetail($notice_detail_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t3.id,
                t3.name,
                t1.quantity
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t2.id = t3.product_id','t3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust','t1.id = t4.notice_detail_id and t4.product_bom_id = t3.id and t4.del_flag = 0','t4')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.id = ?2 and t1.status = 10 and t4.id is null',
                [1 => SessionData::ownerId(), 2 => $notice_detail_id]);
        return $builder->getQuery()->execute()->toArray();
    }

    public function changeNotice($notice_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.product_id,
                t2.code as product_code,
                t2.name as product_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.notice_id = ?2',
                [1 => SessionData::ownerId(), 2 => $notice_id]);
        return $builder->getQuery()->execute()->toArray();
    }

    public function selectById($id)
    {
        return PurchaseOrder::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function delete()
    {
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)) {
            return $this->error(ErrorHelper::WRONG_INPUT);
        }
        $row = $this->selectByUid($uid);
        if (empty($row)) {
            return $this->error(ErrorHelper::WRONG_INPUT);
        } else if ($row->status != 10) {
            return $this->error('状态不正确');
        }

        $now = DateUtil::now();
        $row->del_flag = 1;
        $row->update_date = $now;
        $row->update_by = SessionData::user()->id;
        $row->save();

        $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseApplyEntrust';
        $phql .= ' SET status = 10, status_name = ?4, purchase_order_id = null, update_date = ?2, update_by = ?3';
        $phql .= ' WHERE del_flag = 0 and purchase_order_id = ?1';

        $result = $this->modelsManager->executeQuery($phql, [
            1 => $row->id,
            2 => $now,
            3 => SessionData::user()->id,
            4 => Constant::$purchase_apply_entrust_status[10]
        ]);
        if (!$result->success()) {
            return $this->error($result->getMessage());
        }
    }

    public function selectWwPlanDetail($uid)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t.status,
                t.status_name,
                t2.code as goods_code,
                t2.name as goods_name,
                t2.model as goods_model,
                t.quantity as goods_quantity
                
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductGoods', 't.bom_goods_id = t2.id','t2')
//            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrderDetail', 't2.product_id = t3.id','t3')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.uid = ?2', [1 => SessionData::ownerId(), 2 => $uid])
            ->orderBy('t.status asc, t.id');
        return $builder->getQuery()->execute();
    }
}