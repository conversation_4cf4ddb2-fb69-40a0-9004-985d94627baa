{"version": 3, "file": "js/src_view_ipc_index_vue.9f12f5d4.js", "mappings": ";;;;;;;;;;;;;;AAqcA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AC36BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/ipc/index.vue", "webpack://sfp_ext/./src/view/ipc/index.vue", "webpack://sfp_ext/./src/view/ipc/index.vue?25a9", "webpack://sfp_ext/./src/view/ipc/index.vue?6513", "webpack://sfp_ext/./src/view/ipc/index.vue?a8b0", "webpack://sfp_ext/./src/view/ipc/index.vue?2484", "webpack://sfp_ext/./src/view/ipc/index.vue?f4a2", "webpack://sfp_ext/./src/view/ipc/index.vue?96f0"], "sourcesContent": ["<template>\r\n    <div style=\"padding: 15px;background-color:#F2FEFF;height: 100vh\">\r\n        <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\" style=\"display: flex;flex-direction: row;\">\r\n                <div style=\"width: 50%\">\r\n                    <span style=\"font-size: 20px;color: #409eff;\" v-text=\"group_name\"></span>\r\n                </div>\r\n                <div  style=\"width: 50%;text-align: right\">\r\n                    <el-button v-if=\"group_uid != ''\" type=\"primary\" plain size=\"mini\" @click=\"tokenShow\">设置</el-button>\r\n                </div>\r\n            </div>\r\n            <div style=\"height: 85vh;overflow: auto;\">\r\n                <div v-if=\"step_type == 1\">\r\n                    <div style=\"display: flex;flex-direction: row;justify-content: center;padding-top: 100px\">\r\n                        <el-input style=\"width: 300px\" v-model=\"token_key\" placeholder=\"请输入TOKEN\"></el-input>\r\n                        <el-button type=\"primary\" @click=\"setToken\">设置</el-button>\r\n                    </div>\r\n                    <div style=\"text-align: center;margin-top: 60px\">\r\n                        <el-button type=\"info\" plain style=\"width: 200px;height: 120px\" @click=\"init\">\r\n                            <div><span style=\"font-size: 30px\">关闭</span></div>\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"step_type == 2\" style=\"padding-top: 50px\">\r\n                    <div style=\"width: 500px;height: 350px;margin: auto;border: 2px solid #B2B2B2;padding: 30px;border-radius: 10px;text-align: center\">\r\n                        <i class=\"el-icon-s-custom\" style=\"font-size: 150px\"></i>\r\n                        <div>\r\n                            <span style=\"font-size: 50px\">请扫描工卡</span>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;justify-content: center;margin-top: 10px\">\r\n                            <input id=\"user_key\" type=\"text\" style=\"ime-mode: disabled;width: 200px;\" @keypress=\"handleKeypress\" class=\"form-control\" placeholder=\"请扫描工卡\" v-model=\"user_key\" autoComplete=\"off\"/>\r\n                            <el-button type=\"primary\" plain size=\"small\" @click=\"setUserFocus\">扫码</el-button>\r\n                        </div>\r\n                        <div style=\"margin-top: 10px\">\r\n                            <span style=\"font-size: 16px;color: red\">如果扫码没反应，请点击上方（扫码）按钮后再扫码</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"step_type == 3\" >\r\n                    <div>\r\n                        <el-descriptions class=\"margin-top\"  :column=\"3\" border style=\"font-size: 20px\">\r\n                            <el-descriptions-item>\r\n                                <template slot=\"label\">\r\n                                    <i class=\"el-icon-user\"></i>\r\n                                    姓名\r\n                                </template>\r\n                                <span style=\"font-weight: bold;\" v-text=\"user_name\"></span>\r\n                            </el-descriptions-item>\r\n                            <el-descriptions-item>\r\n                                <template slot=\"label\">\r\n                                    <i class=\"el-icon-mobile-phone\"></i>\r\n                                    工号\r\n                                </template>\r\n                                <span v-text=\"user_code\"></span>\r\n                            </el-descriptions-item>\r\n                            <el-descriptions-item>\r\n                                <template slot=\"label\">\r\n                                    <i class=\"el-icon-time\"></i>\r\n                                    总工时\r\n                                </template>\r\n                                <span v-text=\"work_hour + '（H）'\"></span>\r\n                            </el-descriptions-item>\r\n                        </el-descriptions>\r\n                    </div>\r\n                    <div style=\"width: 100%;height: 62vh;padding: 15px 0;overflow: auto;\">\r\n                        <template>\r\n                            <el-table\r\n                                    :data=\"work_list\"\r\n                                    style=\"width: 100%;font-size: 16px;\">\r\n                                <el-table-column\r\n                                        width=\"140px\"\r\n                                        prop=\"work_name\"\r\n                                        label=\"工作\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"begin_time\"\r\n                                        label=\"工作时间\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <div v-for=\"(time,time_index) in JSON.parse(scope.row.time_data)\" :key=\"time_index\">\r\n                                           <span v-text=\"time.begin\"></span> ~ <span v-text=\"time.end\"></span>\r\n                                        </div>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"hour\"\r\n                                        label=\"工时(H)\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        width=\"450px\"\r\n                                        label=\"工作内容\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <div v-if=\"scope.row.work_type == 2 || scope.row.work_type == 4\" style=\"display: flex;\">\r\n                                            <div v-for=\"(work,work_index) in JSON.parse(scope.row.work_data)\" :key=\"work_index\">\r\n                                                <span v-text=\"work.ship_name\"></span>\r\n                                                <span v-if=\"work_index < JSON.parse(scope.row.work_data).length - 1\">,</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else>\r\n                                            <div v-for=\"(work,work_index) in JSON.parse(scope.row.work_data)\" :key=\"work_index\">\r\n                                                <span v-text=\"work.order_code\"></span>/\r\n                                                <span v-text=\"work.product_name\"></span>/\r\n                                                <span v-text=\"work.bom_name\"></span>/\r\n                                                <span v-text=\"work.ship_name\"></span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"remarks\"\r\n                                        label=\"备注\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"work_status\"\r\n                                        label=\"状态\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <div v-if=\"scope.row.status == 10\" style=\"display: flex;flex-direction: row;\">\r\n                                            <i class=\"el-icon-video-play\" style=\"font-size: 30px;color:#00DB00\"></i>\r\n                                            <div style=\"margin-left: 5px;height:30px;line-height: 30px;\">\r\n                                                <span style=\"font-size: 20px;\">工作中</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-if=\"scope.row.status == 20\" style=\"display: flex;flex-direction: row;\">\r\n                                            <i class=\"el-icon-video-pause\" style=\"font-size: 30px;color: red\"></i>\r\n                                            <div style=\"margin-left: 5px;height:30px;line-height: 30px;\">\r\n                                                <span style=\"font-size: 20px;\">暂停</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-if=\"scope.row.status == 30\" style=\"display: flex;flex-direction: row;\">\r\n                                            <i class=\"el-icon-circle-check\" style=\"font-size: 30px;color: #0080FF\"></i>\r\n                                            <div style=\"margin-left: 5px;height:30px;line-height: 30px;\">\r\n                                                <span style=\"font-size: 20px;\">完成</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                        </template>\r\n                    </div>\r\n                    <div style=\"display: flex;flex-direction: row;justify-content: center;border-top: 1px solid #D2D2D2;padding-top: 15px\">\r\n                        <el-button v-if=\"work_status == 20\" type=\"warning\" plain style=\"width: 200px;height: 120px\" @click=\"continueWork\">\r\n                            <div><span style=\"font-size: 30px\">继续工作</span></div>\r\n                            <div style=\"margin-top: 15px\"><span style=\"font-size: 20px\">(继续暂停工作)</span></div>\r\n                        </el-button>\r\n                        <el-button v-if=\"work_status == 0 || work_status == 20\" type=\"primary\" plain style=\"width: 200px;height: 120px\" @click=\"workBegin\">\r\n                            <div><span style=\"font-size: 30px\">开始工作</span></div>\r\n                        </el-button>\r\n                        <el-button v-if=\"work_status == 10\" type=\"danger\" plain style=\"width: 200px;height: 120px\" @click=\"stopWork\">\r\n                            <div><span style=\"font-size: 30px\">暂停工作</span></div>\r\n                            <div style=\"margin-top: 15px\"><span style=\"font-size: 20px\">(开始其他工作)</span></div>\r\n                        </el-button>\r\n                        <el-button v-if=\"work_status == 10 || work_status == 20 || work_status == 30\" type=\"success\" plain style=\"width: 200px;height: 120px\" @click=\"overWork\">\r\n                            <div><span style=\"font-size: 30px\">工作完成</span></div>\r\n                            <div style=\"margin-top: 15px\"><span style=\"font-size: 20px\">(填报工作)</span></div>\r\n                        </el-button>\r\n                        <el-button type=\"info\" plain style=\"width: 200px;height: 120px\" @click=\"step_type = 2\">\r\n                            <div><span style=\"font-size: 30px\">关闭</span></div>\r\n                            <div style=\"margin-top: 15px\"><span style=\"font-size: 20px\" v-text=\"'('+close_cnt+'S)'\"></span></div>\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-card>\r\n        <el-dialog title=\"确定开始其他工作吗？\" :visible.sync=\"other_work_show\">\r\n            <div>\r\n                <div style=\"text-align: center\">\r\n                    <div>\r\n                        <span style=\"font-size: 30px;color:#FF0000\">其他工作（无图纸）</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"otherWorkSave\">确 定</el-button>\r\n                <el-button @click=\"other_work_show = false\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <el-dialog title=\"确定开始工作吗？\" :visible.sync=\"begin_work_show\">\r\n            <div>\r\n                <div style=\"text-align: center\">\r\n                    <div>\r\n                        <span style=\"font-size: 30px;color: #3a8ee6\"></span>\r\n                    </div>\r\n                </div>\r\n                <div style=\"text-align: center\">\r\n                    <div>\r\n                        <span style=\"font-size: 30px\">请扫描图纸上方二维码</span>\r\n                    </div>\r\n                    <div style=\"display: flex;flex-direction: row;justify-content: center;margin-top: 10px\">\r\n                        <input id=\"bom_key\" type=\"text\" style=\"ime-mode: disabled;width: 200px;\" @keypress=\"handleKeypress\" class=\"form-control\" placeholder=\"请扫描图纸二维码\" v-model=\"bom_key\" autoComplete=\"off\"/>\r\n                        <el-button type=\"primary\" plain size=\"small\" @click=\"setBomFocus\">扫码</el-button>\r\n                    </div>\r\n                    <div style=\"margin-top: 10px\">\r\n                        <span style=\"font-size: 16px;color: red\">如果扫码没反应，请点击上方（扫码）按钮后再扫码</span>\r\n                    </div>\r\n                </div>\r\n                <div>\r\n                    <el-table\r\n                            :data=\"bom_list\"\r\n                            style=\"width: 100%;font-size: 16px;\">\r\n                        <el-table-column\r\n                                prop=\"order_code\"\r\n                                label=\"项目号\">\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                                prop=\"product_name\"\r\n                                label=\"产品\">\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                                prop=\"product_code\"\r\n                                label=\"产品号\">\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                                prop=\"model_name\"\r\n                                label=\"型号\">\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                                prop=\"bom_name\"\r\n                                label=\"工艺\">\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                                fixed=\"right\"\r\n                                label=\"操作\"\r\n                                width=\"120\">\r\n                            <template slot-scope=\"scope\">\r\n                                <el-button\r\n                                        @click.native.prevent=\"bom_list.splice(scope.$index,1)\"\r\n                                        type=\"danger\"\r\n                                        size=\"small\">\r\n                                    删除\r\n                                </el-button>\r\n                            </template>\r\n                        </el-table-column>\r\n                    </el-table>\r\n                </div>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"workBeginSave\">确 定</el-button>\r\n                <el-button @click=\"begin_work_show = false\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <el-dialog title=\"确定暂停工作吗？\" :visible.sync=\"stop_work_show\">\r\n            <div>\r\n                <el-descriptions class=\"margin-top\" :column=\"2\" border>\r\n                    <el-descriptions-item>\r\n                        <template slot=\"label\">\r\n                           工作\r\n                        </template>\r\n                        <span v-text=\"stop_data.work_name\"></span>\r\n                    </el-descriptions-item>\r\n                </el-descriptions>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"stopWorkSave\">确 定</el-button>\r\n                <el-button @click=\"stop_work_show = false\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <el-dialog title=\"确定继续工作吗？\" :visible.sync=\"continue_work_show\">\r\n            <div>\r\n                <el-descriptions class=\"margin-top\" :column=\"2\" border>\r\n                    <el-descriptions-item>\r\n                        <template slot=\"label\">\r\n                            工作\r\n                        </template>\r\n                        <span v-text=\"stop_data.work_name\"></span>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item>\r\n                        <template slot=\"label\">\r\n                            产品\r\n                        </template>\r\n                        <span v-text=\"stop_data.product_name\"></span>\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item>\r\n                        <template slot=\"label\">\r\n                            客户\r\n                        </template>\r\n                        <span v-text=\"stop_data.customer_name\"></span>\r\n                    </el-descriptions-item>\r\n                </el-descriptions>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"continueWorkSave\">确 定</el-button>\r\n                <el-button @click=\"continue_work_show = false\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <el-dialog title=\"确定完成工作吗？\" :visible.sync=\"over_work_show\" :fullscreen=\"true\">\r\n            <div style=\"display: flex;flex-direction: row;min-height: 80vh\">\r\n                <div style=\"width: 50%\">\r\n                    <el-descriptions class=\"margin-top\" :column=\"2\" border>\r\n                        <el-descriptions-item>\r\n                            <template slot=\"label\">\r\n                                工作\r\n                            </template>\r\n                            <span v-text=\"stop_data.work_name\"></span>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item>\r\n                            <template slot=\"label\">\r\n                                工作时长\r\n                            </template>\r\n                            <span v-text=\"stop_data.work_hour + ' 小时'\"></span>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item>\r\n                            <template slot=\"label\">\r\n                                异常说明\r\n                            </template>\r\n                            <el-select style=\"width: 100%\" v-model=\"stop_data_error\" placeholder=\"请选择工作异常说明\">\r\n                                <el-option\r\n                                        key=\"\"\r\n                                        label=\"无异常\"\r\n                                        value=\"\">\r\n                                </el-option>\r\n                                <el-option\r\n                                        v-for=\"item in errors\"\r\n                                        :key=\"item.name\"\r\n                                        :label=\"item.name\"\r\n                                        :value=\"item.name\">\r\n                                </el-option>\r\n                            </el-select>\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n                    <div>\r\n                        <div style=\"padding: 10px 0;font-size: 18px;\">\r\n                            <span>请选择工作内容</span>\r\n                        </div>\r\n                        <div>\r\n                            <div style=\"margin-top: 20px;display: flex\">\r\n                                <div @click=\"over_work_type = 1\" :style=\"{borderColor:over_work_type == 1 ? '#FF8000' : '#D2D2D2',width:'240px'}\" class=\"check-ship\">\r\n                                    <div>\r\n                                        <i class=\" el-icon-picture\" :style=\"{color:over_work_type == 1 ? '#FF8000' : '#D2D2D2'}\"></i>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span :style=\"{color:over_work_type == 1 ? '#FF8000' : '#898989'}\">图纸生产</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div v-if=\"stop_data.work_type == null\" @click=\"over_work_type = 2\" :style=\"{borderColor:over_work_type == 2 ? '#FF8000' : '#D2D2D2',width:'240px'}\" class=\"check-ship\">\r\n                                    <div>\r\n                                        <i class=\" el-icon-s-help\" :style=\"{color:over_work_type == 2 ? '#FF8000' : '#D2D2D2'}\"></i>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span :style=\"{color:over_work_type == 2 ? '#FF8000' : '#898989'}\">协同生产</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div v-if=\"stop_data.work_type == null\" @click=\"over_work_type = 3\" :style=\"{borderColor:over_work_type == 3 ? '#FF8000' : '#D2D2D2',width:'240px'}\" class=\"check-ship\">\r\n                                    <div>\r\n                                        <i class=\"el-icon-s-tools\" :style=\"{color:over_work_type == 3 ? '#FF8000' : '#D2D2D2'}\"></i>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span :style=\"{color:over_work_type == 3 ? '#FF8000' : '#898989'}\">返修</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div v-if=\"stop_data.work_type == null\" @click=\"over_work_type = 4\" :style=\"{borderColor:over_work_type == 4 ? '#FF8000' : '#D2D2D2',width:'240px'}\" class=\"check-ship\">\r\n                                    <div>\r\n                                        <i class=\"el-icon-warning\" :style=\"{color:over_work_type == 4 ? '#FF8000' : '#D2D2D2'}\"></i>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span :style=\"{color:over_work_type == 4 ? '#FF8000' : '#898989'}\">其他工作</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"margin-top: 20px;display: flex;flex-wrap: wrap;\" v-if=\"over_work_type == 1\">\r\n                                <div v-for=\"(ship,ship_idx) in ships\" @click=\"ship.sel == 1 ? ship.sel = 0 : ship.sel = 1\" :key=\"ship_idx\" :style=\"{borderColor:ship.sel == 1 ? '#0080FF' : '#D2D2D2'}\" class=\"check-ship\">\r\n                                    <div>\r\n                                        <i class=\" el-icon-circle-check\" :style=\"{color:ship.sel == 1 ? '#0080FF' : '#D2D2D2'}\"></i>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span v-text=\"ship.name\" :style=\"{color:ship.sel == 1 ? '#0080FF' : '#898989'}\"></span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"margin-top: 20px;display: flex;flex-wrap: wrap;\" v-if=\"over_work_type == 2\">\r\n                                <div v-for=\"(xt,xt_idx) in xt_list\" @click=\"selXt(xt_idx)\" :key=\"xt_idx\" :style=\"{borderColor:xt.sel == 1 ? '#0080FF' : '#D2D2D2'}\" class=\"check-ship\">\r\n                                    <span v-text=\"xt.staff_name + '（' + xt.begin_time + '-' + xt.end_time + '）'\" :style=\"{color:xt.sel == 1 ? '#0080FF' : '#898989'}\"></span>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"margin-top: 20px;display: flex;flex-wrap: wrap;\" v-if=\"over_work_type == 3\">\r\n                                <div v-for=\"(repair,repair_idx) in repair_list\" @click=\"repair.sel == 1 ? repair.sel = 0 : repair.sel = 1\" :key=\"repair_idx\" :style=\"{borderColor:repair.sel == 1 ? '#0080FF' : '#D2D2D2',flexDirection:'column'}\" class=\"check-ship\">\r\n                                    <span v-text=\"repair.error_code + '/' + repair.product_name + '/' + repair.bom_name\" :style=\"{color:repair.sel == 1 ? '#0080FF' : '#898989',fontSize:'20px'}\"></span>\r\n                                    <span v-text=\"'贴签号:' + repair.error_code\" :style=\"{color:repair.sel == 1 ? '#0080FF' : '#898989',fontSize:'20px'}\"></span>\r\n                                </div>\r\n                            </div>\r\n                            <div style=\"margin-top: 20px;display: flex;flex-wrap: wrap;\" v-if=\"over_work_type == 4\">\r\n                                <div v-for=\"(t,t_idx) in types\" @click=\"t.sel == 1 ? t.sel = 0 : t.sel = 1\" :key=\"t_idx\" :style=\"{borderColor:t.sel == 1 ? '#0080FF' : '#D2D2D2'}\" class=\"check-ship\">\r\n                                    <div>\r\n                                        <i class=\" el-icon-circle-check\" :style=\"{color:t.sel == 1 ? '#0080FF' : '#D2D2D2'}\"></i>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span v-text=\"t.name\" :style=\"{color:t.sel == 1 ? '#0080FF' : '#898989'}\"></span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div style=\"width: 50%\" v-if=\"over_work_type == 1\">\r\n                    <div style=\"text-align: center\">\r\n                        <div>\r\n                            <span style=\"font-size: 30px\">请扫描图纸上方二维码</span>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;justify-content: center;margin-top: 10px\">\r\n                            <input id=\"over_bom_key\" type=\"text\" style=\"ime-mode: disabled;width: 200px;\" @keypress=\"handleKeypress\" class=\"form-control\" placeholder=\"请扫描图纸二维码\" v-model=\"bom_key\" autoComplete=\"off\"/>\r\n                            <el-button type=\"primary\" plain size=\"small\" @click=\"setOverBomFocus\">点击扫码</el-button>\r\n                        </div>\r\n                        <div style=\"margin-top: 10px\">\r\n                            <span style=\"font-size: 16px;color: red\">如果扫码没反应，请点击上方（扫码）按钮后再扫码</span>\r\n                        </div>\r\n                    </div>\r\n                    <div>\r\n                        <el-table\r\n                                :data=\"boms\"\r\n                                style=\"width: 100%;font-size: 16px;\">\r\n                            <el-table-column\r\n                                    prop=\"order_code\"\r\n                                    label=\"项目号\">\r\n                            </el-table-column>\r\n                            <el-table-column\r\n                                    prop=\"product_name\"\r\n                                    label=\"产品\">\r\n                            </el-table-column>\r\n                            <el-table-column\r\n                                    prop=\"product_code\"\r\n                                    label=\"产品号\">\r\n                            </el-table-column>\r\n                            <el-table-column\r\n                                    prop=\"model_name\"\r\n                                    label=\"型号\">\r\n                            </el-table-column>\r\n                            <el-table-column\r\n                                    prop=\"bom_name\"\r\n                                    label=\"工艺\">\r\n                            </el-table-column>\r\n                            <el-table-column\r\n                                    fixed=\"right\"\r\n                                    label=\"操作\"\r\n                                    width=\"120\">\r\n                                <template slot-scope=\"scope\">\r\n                                    <el-button\r\n                                            @click.native.prevent=\"boms.splice(scope.$index,1)\"\r\n                                            type=\"danger\"\r\n                                            size=\"small\">\r\n                                        删除\r\n                                    </el-button>\r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"overWorkSave\">确 定</el-button>\r\n                <el-button @click=\"over_work_show = false\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\n    import Global from '../../js/global';\r\n    export default {\r\n        name: \"plan\",\r\n        components: {\r\n        },\r\n        data() {\r\n            return {\r\n                key_list:['1','2','3','4','5','6','7','8','9','0','·','删除'],\r\n                step_type : 0,\r\n                group_uid : '',\r\n                group_name : 'MES工控机',\r\n                token_key:'',\r\n                user_key:'',\r\n                user_uid:'',\r\n                user_name : '',\r\n                user_code : '',\r\n                work_hour : 0,\r\n                errors:[],\r\n                ships : [],\r\n                xt_list:[],\r\n                repair_list:[],\r\n                over_work_type:1,\r\n                types:[],\r\n                boms:[],\r\n                bom_list:[],\r\n                work_list: [],\r\n                begin_work_show:false,\r\n                other_work_show:false,\r\n                bom_key:'',\r\n                work_status: 0, // 0 未开始 10 工作中 20 暂停\r\n                over_work_show:false,\r\n                stop_work_show:false,\r\n                continue_work_show:false,\r\n                stop_data:{\r\n                    uid:''\r\n                },\r\n                sel_ship_id : '',\r\n                stop_data_error:'',\r\n                stop_data_type:'',\r\n                close_cnt:30\r\n            };\r\n        },\r\n        created() {\r\n            this.init();\r\n            this.setFocus();\r\n        },\r\n        methods: {\r\n            selXt(idx){\r\n                for (let i = 0; i < this.xt_list.length; i++) {\r\n                    this.xt_list[i].sel = 0;\r\n                }\r\n                this.xt_list[idx].sel = 1;\r\n            },\r\n            init(){\r\n                this.group_uid = Global.getItem('token');\r\n                this.group_name = Global.getItem('name');\r\n                if (this.group_uid == '' || this.group_uid == null){\r\n                    this.step_type = 1;\r\n                }else{\r\n                    this.step_type = 2;\r\n                }\r\n            },\r\n            setUserFocus(){\r\n                this.user_key = '';\r\n                // eslint-disable-next-line no-undef\r\n                $('#user_key').focus();\r\n            },\r\n            setBomFocus(){\r\n                this.bom_key = '';\r\n                // eslint-disable-next-line no-undef\r\n                $('#bom_key').focus();\r\n            },\r\n            setOverBomFocus(){\r\n                this.bom_key = '';\r\n                // eslint-disable-next-line no-undef\r\n                $('#over_bom_key').focus();\r\n            },\r\n            setFocus(){\r\n                if (this.step_type == 2){\r\n                    // eslint-disable-next-line no-undef\r\n                    $('#user_key').focus();\r\n                } else if(this.step_type == 3){\r\n                    if (!(this.begin_work_show ||\r\n                        this.other_work_show ||\r\n                        this.over_work_show ||\r\n                        this.stop_work_show ||\r\n                        this.continue_work_show)\r\n                    ){\r\n                        if (this.close_cnt <= 1){\r\n                            this.step_type = 2;\r\n                        } else {\r\n                            this.close_cnt --;\r\n                        }\r\n                    } else {\r\n                        this.close_cnt = 30;\r\n                    }\r\n                }\r\n                setTimeout(() => {\r\n                    this.setFocus();\r\n                },1000);\r\n            },\r\n            setToken(){\r\n                if (this.token_key == ''){\r\n                    this.$message.error('请输入TOKEN');\r\n                    return;\r\n                }\r\n                if (this.token_key.length != 32){\r\n                    this.$message.error('TOKEN长度不正确');\r\n                    return;\r\n                }\r\n                this.$http.post('ipc/index/token', {uid:this.token_key}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        Global.setItem('token', rs.data.uid);\r\n                        Global.setItem('name', rs.data.name);\r\n                        this.$message.success('设置成功');\r\n                        this.token_key = '';\r\n                        this.init();\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            handleKeypress(e){\r\n                if (e.code == 'Enter'){\r\n                    if (this.step_type == 2){\r\n                        if (this.user_key.length != 10){\r\n                            this.user_key = '';\r\n                            this.$message.error('二维码格式不正确');\r\n                            return;\r\n                        }\r\n                        this.getUserData(this.user_key);\r\n                        this.user_key = '';\r\n                    } else if (this.step_type == 3){\r\n                        if (this.bom_key.length != 15){\r\n                            this.bom_key = '';\r\n                            this.$message.error('二维码格式不正确');\r\n                            return;\r\n                        }\r\n                        if(this.begin_work_show){\r\n                            this.getBomData(this.bom_key,'');\r\n                        } else if (this.over_work_show){\r\n                            this.getBomData(this.bom_key,this.stop_data.uid);\r\n                        }\r\n                        this.bom_key = '';\r\n                    }\r\n                }\r\n            },\r\n            getBomData(bom_key,detail_uid){\r\n                this.$http.post('ipc/index/get-bom', {uid:bom_key,detail_uid:detail_uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let data = rs.data;\r\n                        if (detail_uid == ''){\r\n                            for (let bom of this.bom_list){\r\n                                if (bom.uid == data.uid){\r\n                                    this.$message.error('不能重复扫码');\r\n                                    return;\r\n                                }\r\n                            }\r\n                            this.bom_list.push(data);\r\n                        } else {\r\n                            for (let bom of this.boms){\r\n                                if (bom.uid == data.uid){\r\n                                    this.$message.error('不能重复扫码');\r\n                                    return;\r\n                                }\r\n                            }\r\n                            this.boms.push(data);\r\n                        }\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            getUserData(user_key){\r\n                this.$http.post('ipc/index/user', {uid:user_key,group_uid:this.group_uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        let work_status = 0;\r\n                        for(let item of rs.data.work_list){\r\n                            if (item.status <= 20){\r\n                                work_status += parseInt(item.status);\r\n                            }\r\n                        }\r\n                        this.step_type = 3;\r\n                        this.user_uid = rs.data.user_uid;\r\n                        this.user_name = rs.data.user_name;\r\n                        this.user_code = rs.data.user_code;\r\n                        this.work_status = work_status;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.work_hour = rs.data.work_hour;\r\n                        this.close_cnt = 30;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            workBegin(){\r\n                this.begin_work_show = true;\r\n                this.bom_list = [];\r\n                setTimeout(()=>{\r\n                    this.setBomFocus();\r\n                },100);\r\n            },\r\n            stopWork(){\r\n                for(let item of this.work_list){\r\n                    if (item.status == 20){\r\n                        this.$message.error('已存在暂停工作');\r\n                        return;\r\n                    }\r\n                }\r\n                for(let item of this.work_list){\r\n                    if (item.status == 10){\r\n                        this.stop_work_show = true;\r\n                        this.stop_data = item;\r\n                        return;\r\n                    }\r\n                }\r\n                this.$message.error('没有工作中内容');\r\n            },\r\n            stopWorkSave(){\r\n                if (!this.stop_data.uid){\r\n                    this.$message.error('数据异常');\r\n                    return;\r\n                }\r\n                this.$http.post('ipc/index/stop-work', {uid:this.stop_data.uid,user_uid:this.user_uid,group_uid:this.group_uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.stop_work_show = false;\r\n                        let work_status = 0;\r\n                        for(let item of rs.data.work_list){\r\n                            if (item.status <= 20){\r\n                                work_status += parseInt(item.status);\r\n                            }\r\n                        }\r\n                        this.work_status = work_status;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.work_hour = rs.data.work_hour;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            workBeginSave(){\r\n                this.$http.post('ipc/index/work-begin', {\r\n                    user_uid:this.user_uid,\r\n                    group_uid:this.group_uid,\r\n                    bom_data: encodeURIComponent(JSON.stringify(this.bom_list))\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.begin_work_show = false;\r\n                        let work_status = 0;\r\n                        for(let item of rs.data.work_list){\r\n                            if (item.status <= 20){\r\n                                work_status += parseInt(item.status);\r\n                            }\r\n                        }\r\n                        this.work_status = work_status;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.work_hour = rs.data.work_hour;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            continueWork(){\r\n                for(let item of this.work_list){\r\n                    if (item.status == 20){\r\n                        this.continue_work_show = true;\r\n                        this.stop_data = item;\r\n                        return;\r\n                    }\r\n                }\r\n                this.$message.error('没有暂停工作');\r\n            },\r\n            continueWorkSave(){\r\n                if (!this.stop_data.uid){\r\n                    this.$message.error('数据异常');\r\n                    return;\r\n                }\r\n                this.$http.post('ipc/index/continue-work', {uid:this.stop_data.uid,user_uid:this.user_uid,group_uid:this.group_uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.continue_work_show = false;\r\n                        let work_status = 0;\r\n                        for(let item of rs.data.work_list){\r\n                            if (item.status <= 20){\r\n                                work_status += parseInt(item.status);\r\n                            }\r\n                        }\r\n                        this.work_status = work_status;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.work_hour = rs.data.work_hour;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            overWork(){\r\n                for(let item of this.work_list){\r\n                    if (item.status == 10){\r\n                        this.getOverWork(item);\r\n                        return;\r\n                    }\r\n                }\r\n                for(let item of this.work_list){\r\n                    if (item.status == 20){\r\n                        this.getOverWork(item);\r\n                        return;\r\n                    }\r\n                }\r\n                this.$message.error('没有工作中');\r\n            },\r\n            getOverWork(item){\r\n                this.stop_data = item;\r\n                this.$http.post('ipc/index/over-data', {uid:item.uid,group_uid:this.group_uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.over_work_type = 1;\r\n                        this.ships = rs.data.ships;\r\n                        this.errors = rs.data.errors;\r\n                        this.types = rs.data.types;\r\n                        this.xt_list = rs.data.xt_list;\r\n                        this.repair_list = rs.data.repair_list;\r\n                        this.boms = rs.data.bom_data;\r\n                        this.stop_data.work_hour = rs.data.work_hour;\r\n                        this.stop_data.work_type = rs.data.work_type;\r\n                        this.sel_ship_id = '';\r\n                        this.stop_data_error = '';\r\n                        this.stop_data_type = '';\r\n                        this.over_work_show = true;\r\n                        setTimeout(() => {\r\n                            this.setOverBomFocus();\r\n                        },500);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            numKeypress(key){\r\n                if (this.sel_ship_id == ''){\r\n                    this.$message.error('请选择填写项目');\r\n                    return;\r\n                }\r\n                for(let bom of this.boms){\r\n                    for(let item of bom.ships){\r\n                        if (item.id == this.sel_ship_id){\r\n                            if (key == '删除'){\r\n                                item.cnt = '';\r\n                            } else {\r\n                                if (key == '·'){\r\n                                    if (item.cnt == ''){\r\n                                        return;\r\n                                    }\r\n                                    if (item.cnt.indexOf('.') > -1){\r\n                                        return;\r\n                                    }\r\n                                    item.cnt += '.';\r\n                                } else {\r\n                                    item.cnt += key + '';\r\n                                }\r\n                            }\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            overWorkSave(){\r\n                if (!this.stop_data.uid){\r\n                    this.$message.error('数据异常');\r\n                    return;\r\n                }\r\n                let datas = [];\r\n                let ship_list = [];\r\n                if (this.over_work_type == 1){\r\n                    if(this.boms.length == 0){\r\n                        this.$message.error('请扫图纸二位码');\r\n                        return;\r\n                    }\r\n                    datas = this.boms;\r\n                    for(let ship of this.ships){\r\n                        if (ship.sel == 1){\r\n                            ship_list.push(ship);\r\n                        }\r\n                    }\r\n                    if (ship_list.length == 0){\r\n                        this.$message.error('请选择工作内容');\r\n                        return;\r\n                    }\r\n                } else if (this.over_work_type == 2){\r\n                    for(let item of this.xt_list){\r\n                       if (item.sel == 1){\r\n                           datas.push(item);\r\n                           break;\r\n                       }\r\n                    }\r\n                    if (datas.length == 0){\r\n                        this.$message.error('请选择协同工作项目');\r\n                        return;\r\n                    }\r\n                } else if (this.over_work_type == 3){\r\n                    for(let item of this.repair_list){\r\n                        if (item.sel == 1){\r\n                            datas.push(item);\r\n                        }\r\n                    }\r\n                    if (datas.length == 0){\r\n                        this.$message.error('请选择返修项目');\r\n                        return;\r\n                    }\r\n                } else if (this.over_work_type == 4){\r\n                    for(let item of this.types){\r\n                        if (item.sel == 1){\r\n                            datas.push(item);\r\n                        }\r\n                    }\r\n                    if (datas.length == 0){\r\n                        this.$message.error('请选择其他工作内容');\r\n                        return;\r\n                    }\r\n                } else {\r\n                    this.$message.error('请选择工作类型');\r\n                    return;\r\n                }\r\n                this.$http.post('ipc/index/over-work', {\r\n                    work_type:this.over_work_type,\r\n                    uid:this.stop_data.uid,\r\n                    group_uid:this.group_uid,\r\n                    stop_data_error:this.stop_data_error,\r\n                    ships:encodeURIComponent(JSON.stringify(ship_list)),\r\n                    datas:encodeURIComponent(JSON.stringify(datas))\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.over_work_show = false;\r\n                        let work_status = 0;\r\n                        for(let item of rs.data.work_list){\r\n                            if (item.status <= 20){\r\n                                work_status += parseInt(item.status);\r\n                            }\r\n                        }\r\n                        this.work_status = work_status;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.work_hour = rs.data.work_hour;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            otherWorkSave(){\r\n                this.$http.post('ipc/index/other-work', {\r\n                    user_uid:this.user_uid,group_uid:this.group_uid\r\n                }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.other_work_show = false;\r\n                        let work_status = 0;\r\n                        for(let item of rs.data.work_list){\r\n                            if (item.status <= 20){\r\n                                work_status += parseInt(item.status);\r\n                            }\r\n                        }\r\n                        this.work_status = work_status;\r\n                        this.work_list = rs.data.work_list;\r\n                        this.work_hour = rs.data.work_hour;\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            tokenShow(){\r\n                this.step_type = 1;\r\n                this.token_key = '';\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style scoped>\r\n    .check-ship{\r\n        padding: 10px;\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        border: 2px solid #D2D2D2;\r\n        min-width: 200px;\r\n        max-width: 300px;\r\n        border-radius: 10px;\r\n        display: flex;\r\n        justify-content: space-around;\r\n        margin-right: 30px;\r\n        cursor: pointer;\r\n        margin-bottom: 30px;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding\":\"15px\",\"background-color\":\"#F2FEFF\",\"height\":\"100vh\"}},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"},attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\",\"color\":\"#409eff\"},domProps:{\"textContent\":_vm._s(_vm.group_name)}})]),_c('div',{staticStyle:{\"width\":\"50%\",\"text-align\":\"right\"}},[(_vm.group_uid != '')?_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\",\"size\":\"mini\"},on:{\"click\":_vm.tokenShow}},[_vm._v(\"设置\")]):_vm._e()],1)]),_c('div',{staticStyle:{\"height\":\"85vh\",\"overflow\":\"auto\"}},[(_vm.step_type == 1)?_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\",\"padding-top\":\"100px\"}},[_c('el-input',{staticStyle:{\"width\":\"300px\"},attrs:{\"placeholder\":\"请输入TOKEN\"},model:{value:(_vm.token_key),callback:function ($$v) {_vm.token_key=$$v},expression:\"token_key\"}}),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.setToken}},[_vm._v(\"设置\")])],1),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"60px\"}},[_c('el-button',{staticStyle:{\"width\":\"200px\",\"height\":\"120px\"},attrs:{\"type\":\"info\",\"plain\":\"\"},on:{\"click\":_vm.init}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"关闭\")])])])],1)]):_vm._e(),(_vm.step_type == 2)?_c('div',{staticStyle:{\"padding-top\":\"50px\"}},[_c('div',{staticStyle:{\"width\":\"500px\",\"height\":\"350px\",\"margin\":\"auto\",\"border\":\"2px solid #B2B2B2\",\"padding\":\"30px\",\"border-radius\":\"10px\",\"text-align\":\"center\"}},[_c('i',{staticClass:\"el-icon-s-custom\",staticStyle:{\"font-size\":\"150px\"}}),_c('div',[_c('span',{staticStyle:{\"font-size\":\"50px\"}},[_vm._v(\"请扫描工卡\")])]),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\",\"margin-top\":\"10px\"}},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.user_key),expression:\"user_key\"}],staticClass:\"form-control\",staticStyle:{\"ime-mode\":\"disabled\",\"width\":\"200px\"},attrs:{\"id\":\"user_key\",\"type\":\"text\",\"placeholder\":\"请扫描工卡\",\"autoComplete\":\"off\"},domProps:{\"value\":(_vm.user_key)},on:{\"keypress\":_vm.handleKeypress,\"input\":function($event){if($event.target.composing)return;_vm.user_key=$event.target.value}}}),_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\",\"size\":\"small\"},on:{\"click\":_vm.setUserFocus}},[_vm._v(\"扫码\")])],1),_c('div',{staticStyle:{\"margin-top\":\"10px\"}},[_c('span',{staticStyle:{\"font-size\":\"16px\",\"color\":\"red\"}},[_vm._v(\"如果扫码没反应，请点击上方（扫码）按钮后再扫码\")])])])]):_vm._e(),(_vm.step_type == 3)?_c('div',[_c('div',[_c('el-descriptions',{staticClass:\"margin-top\",staticStyle:{\"font-size\":\"20px\"},attrs:{\"column\":3,\"border\":\"\"}},[_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 姓名 \")]),_c('span',{staticStyle:{\"font-weight\":\"bold\"},domProps:{\"textContent\":_vm._s(_vm.user_name)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_c('i',{staticClass:\"el-icon-mobile-phone\"}),_vm._v(\" 工号 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.user_code)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" 总工时 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.work_hour + '（H）')}})],2)],1)],1),_c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"62vh\",\"padding\":\"15px 0\",\"overflow\":\"auto\"}},[[_c('el-table',{staticStyle:{\"width\":\"100%\",\"font-size\":\"16px\"},attrs:{\"data\":_vm.work_list}},[_c('el-table-column',{attrs:{\"width\":\"140px\",\"prop\":\"work_name\",\"label\":\"工作\"}}),_c('el-table-column',{attrs:{\"prop\":\"begin_time\",\"label\":\"工作时间\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return _vm._l((JSON.parse(scope.row.time_data)),function(time,time_index){return _c('div',{key:time_index},[_c('span',{domProps:{\"textContent\":_vm._s(time.begin)}}),_vm._v(\" ~ \"),_c('span',{domProps:{\"textContent\":_vm._s(time.end)}})])})}}],null,false,2710230586)}),_c('el-table-column',{attrs:{\"prop\":\"hour\",\"label\":\"工时(H)\"}}),_c('el-table-column',{attrs:{\"width\":\"450px\",\"label\":\"工作内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.work_type == 2 || scope.row.work_type == 4)?_c('div',{staticStyle:{\"display\":\"flex\"}},_vm._l((JSON.parse(scope.row.work_data)),function(work,work_index){return _c('div',{key:work_index},[_c('span',{domProps:{\"textContent\":_vm._s(work.ship_name)}}),(work_index < JSON.parse(scope.row.work_data).length - 1)?_c('span',[_vm._v(\",\")]):_vm._e()])}),0):_c('div',_vm._l((JSON.parse(scope.row.work_data)),function(work,work_index){return _c('div',{key:work_index},[_c('span',{domProps:{\"textContent\":_vm._s(work.order_code)}}),_vm._v(\"/ \"),_c('span',{domProps:{\"textContent\":_vm._s(work.product_name)}}),_vm._v(\"/ \"),_c('span',{domProps:{\"textContent\":_vm._s(work.bom_name)}}),_vm._v(\"/ \"),_c('span',{domProps:{\"textContent\":_vm._s(work.ship_name)}})])}),0)]}}],null,false,3347347189)}),_c('el-table-column',{attrs:{\"prop\":\"remarks\",\"label\":\"备注\"}}),_c('el-table-column',{attrs:{\"prop\":\"work_status\",\"label\":\"状态\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.status == 10)?_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('i',{staticClass:\"el-icon-video-play\",staticStyle:{\"font-size\":\"30px\",\"color\":\"#00DB00\"}}),_c('div',{staticStyle:{\"margin-left\":\"5px\",\"height\":\"30px\",\"line-height\":\"30px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\"工作中\")])])]):_vm._e(),(scope.row.status == 20)?_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('i',{staticClass:\"el-icon-video-pause\",staticStyle:{\"font-size\":\"30px\",\"color\":\"red\"}}),_c('div',{staticStyle:{\"margin-left\":\"5px\",\"height\":\"30px\",\"line-height\":\"30px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\"暂停\")])])]):_vm._e(),(scope.row.status == 30)?_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('i',{staticClass:\"el-icon-circle-check\",staticStyle:{\"font-size\":\"30px\",\"color\":\"#0080FF\"}}),_c('div',{staticStyle:{\"margin-left\":\"5px\",\"height\":\"30px\",\"line-height\":\"30px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\"完成\")])])]):_vm._e()]}}],null,false,4064544140)})],1)]],2),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\",\"border-top\":\"1px solid #D2D2D2\",\"padding-top\":\"15px\"}},[(_vm.work_status == 20)?_c('el-button',{staticStyle:{\"width\":\"200px\",\"height\":\"120px\"},attrs:{\"type\":\"warning\",\"plain\":\"\"},on:{\"click\":_vm.continueWork}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"继续工作\")])]),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\"(继续暂停工作)\")])])]):_vm._e(),(_vm.work_status == 0 || _vm.work_status == 20)?_c('el-button',{staticStyle:{\"width\":\"200px\",\"height\":\"120px\"},attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":_vm.workBegin}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"开始工作\")])])]):_vm._e(),(_vm.work_status == 10)?_c('el-button',{staticStyle:{\"width\":\"200px\",\"height\":\"120px\"},attrs:{\"type\":\"danger\",\"plain\":\"\"},on:{\"click\":_vm.stopWork}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"暂停工作\")])]),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\"(开始其他工作)\")])])]):_vm._e(),(_vm.work_status == 10 || _vm.work_status == 20 || _vm.work_status == 30)?_c('el-button',{staticStyle:{\"width\":\"200px\",\"height\":\"120px\"},attrs:{\"type\":\"success\",\"plain\":\"\"},on:{\"click\":_vm.overWork}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"工作完成\")])]),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\"(填报工作)\")])])]):_vm._e(),_c('el-button',{staticStyle:{\"width\":\"200px\",\"height\":\"120px\"},attrs:{\"type\":\"info\",\"plain\":\"\"},on:{\"click\":function($event){_vm.step_type = 2}}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"关闭\")])]),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"},domProps:{\"textContent\":_vm._s('('+_vm.close_cnt+'S)')}})])])],1)]):_vm._e()])]),_c('el-dialog',{attrs:{\"title\":\"确定开始其他工作吗？\",\"visible\":_vm.other_work_show},on:{\"update:visible\":function($event){_vm.other_work_show=$event}}},[_c('div',[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\",\"color\":\"#FF0000\"}},[_vm._v(\"其他工作（无图纸）\")])])])]),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.otherWorkSave}},[_vm._v(\"确 定\")]),_c('el-button',{on:{\"click\":function($event){_vm.other_work_show = false}}},[_vm._v(\"取 消\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"确定开始工作吗？\",\"visible\":_vm.begin_work_show},on:{\"update:visible\":function($event){_vm.begin_work_show=$event}}},[_c('div',[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\",\"color\":\"#3a8ee6\"}})])]),_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"请扫描图纸上方二维码\")])]),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\",\"margin-top\":\"10px\"}},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.bom_key),expression:\"bom_key\"}],staticClass:\"form-control\",staticStyle:{\"ime-mode\":\"disabled\",\"width\":\"200px\"},attrs:{\"id\":\"bom_key\",\"type\":\"text\",\"placeholder\":\"请扫描图纸二维码\",\"autoComplete\":\"off\"},domProps:{\"value\":(_vm.bom_key)},on:{\"keypress\":_vm.handleKeypress,\"input\":function($event){if($event.target.composing)return;_vm.bom_key=$event.target.value}}}),_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\",\"size\":\"small\"},on:{\"click\":_vm.setBomFocus}},[_vm._v(\"扫码\")])],1),_c('div',{staticStyle:{\"margin-top\":\"10px\"}},[_c('span',{staticStyle:{\"font-size\":\"16px\",\"color\":\"red\"}},[_vm._v(\"如果扫码没反应，请点击上方（扫码）按钮后再扫码\")])])]),_c('div',[_c('el-table',{staticStyle:{\"width\":\"100%\",\"font-size\":\"16px\"},attrs:{\"data\":_vm.bom_list}},[_c('el-table-column',{attrs:{\"prop\":\"order_code\",\"label\":\"项目号\"}}),_c('el-table-column',{attrs:{\"prop\":\"product_name\",\"label\":\"产品\"}}),_c('el-table-column',{attrs:{\"prop\":\"product_code\",\"label\":\"产品号\"}}),_c('el-table-column',{attrs:{\"prop\":\"model_name\",\"label\":\"型号\"}}),_c('el-table-column',{attrs:{\"prop\":\"bom_name\",\"label\":\"工艺\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\"},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.bom_list.splice(scope.$index,1)}}},[_vm._v(\" 删除 \")])]}}])})],1)],1)]),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.workBeginSave}},[_vm._v(\"确 定\")]),_c('el-button',{on:{\"click\":function($event){_vm.begin_work_show = false}}},[_vm._v(\"取 消\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"确定暂停工作吗？\",\"visible\":_vm.stop_work_show},on:{\"update:visible\":function($event){_vm.stop_work_show=$event}}},[_c('div',[_c('el-descriptions',{staticClass:\"margin-top\",attrs:{\"column\":2,\"border\":\"\"}},[_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 工作 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.stop_data.work_name)}})],2)],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.stopWorkSave}},[_vm._v(\"确 定\")]),_c('el-button',{on:{\"click\":function($event){_vm.stop_work_show = false}}},[_vm._v(\"取 消\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"确定继续工作吗？\",\"visible\":_vm.continue_work_show},on:{\"update:visible\":function($event){_vm.continue_work_show=$event}}},[_c('div',[_c('el-descriptions',{staticClass:\"margin-top\",attrs:{\"column\":2,\"border\":\"\"}},[_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 工作 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.stop_data.work_name)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 产品 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.stop_data.product_name)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 客户 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.stop_data.customer_name)}})],2)],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.continueWorkSave}},[_vm._v(\"确 定\")]),_c('el-button',{on:{\"click\":function($event){_vm.continue_work_show = false}}},[_vm._v(\"取 消\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"确定完成工作吗？\",\"visible\":_vm.over_work_show,\"fullscreen\":true},on:{\"update:visible\":function($event){_vm.over_work_show=$event}}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"min-height\":\"80vh\"}},[_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('el-descriptions',{staticClass:\"margin-top\",attrs:{\"column\":2,\"border\":\"\"}},[_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 工作 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.stop_data.work_name)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 工作时长 \")]),_c('span',{domProps:{\"textContent\":_vm._s(_vm.stop_data.work_hour + ' 小时')}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 异常说明 \")]),_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择工作异常说明\"},model:{value:(_vm.stop_data_error),callback:function ($$v) {_vm.stop_data_error=$$v},expression:\"stop_data_error\"}},[_c('el-option',{key:\"\",attrs:{\"label\":\"无异常\",\"value\":\"\"}}),_vm._l((_vm.errors),function(item){return _c('el-option',{key:item.name,attrs:{\"label\":item.name,\"value\":item.name}})})],2)],2)],1),_c('div',[_c('div',{staticStyle:{\"padding\":\"10px 0\",\"font-size\":\"18px\"}},[_c('span',[_vm._v(\"请选择工作内容\")])]),_c('div',[_c('div',{staticStyle:{\"margin-top\":\"20px\",\"display\":\"flex\"}},[_c('div',{staticClass:\"check-ship\",style:({borderColor:_vm.over_work_type == 1 ? '#FF8000' : '#D2D2D2',width:'240px'}),on:{\"click\":function($event){_vm.over_work_type = 1}}},[_c('div',[_c('i',{staticClass:\"el-icon-picture\",style:({color:_vm.over_work_type == 1 ? '#FF8000' : '#D2D2D2'})})]),_c('div',[_c('span',{style:({color:_vm.over_work_type == 1 ? '#FF8000' : '#898989'})},[_vm._v(\"图纸生产\")])])]),(_vm.stop_data.work_type == null)?_c('div',{staticClass:\"check-ship\",style:({borderColor:_vm.over_work_type == 2 ? '#FF8000' : '#D2D2D2',width:'240px'}),on:{\"click\":function($event){_vm.over_work_type = 2}}},[_c('div',[_c('i',{staticClass:\"el-icon-s-help\",style:({color:_vm.over_work_type == 2 ? '#FF8000' : '#D2D2D2'})})]),_c('div',[_c('span',{style:({color:_vm.over_work_type == 2 ? '#FF8000' : '#898989'})},[_vm._v(\"协同生产\")])])]):_vm._e(),(_vm.stop_data.work_type == null)?_c('div',{staticClass:\"check-ship\",style:({borderColor:_vm.over_work_type == 3 ? '#FF8000' : '#D2D2D2',width:'240px'}),on:{\"click\":function($event){_vm.over_work_type = 3}}},[_c('div',[_c('i',{staticClass:\"el-icon-s-tools\",style:({color:_vm.over_work_type == 3 ? '#FF8000' : '#D2D2D2'})})]),_c('div',[_c('span',{style:({color:_vm.over_work_type == 3 ? '#FF8000' : '#898989'})},[_vm._v(\"返修\")])])]):_vm._e(),(_vm.stop_data.work_type == null)?_c('div',{staticClass:\"check-ship\",style:({borderColor:_vm.over_work_type == 4 ? '#FF8000' : '#D2D2D2',width:'240px'}),on:{\"click\":function($event){_vm.over_work_type = 4}}},[_c('div',[_c('i',{staticClass:\"el-icon-warning\",style:({color:_vm.over_work_type == 4 ? '#FF8000' : '#D2D2D2'})})]),_c('div',[_c('span',{style:({color:_vm.over_work_type == 4 ? '#FF8000' : '#898989'})},[_vm._v(\"其他工作\")])])]):_vm._e()]),(_vm.over_work_type == 1)?_c('div',{staticStyle:{\"margin-top\":\"20px\",\"display\":\"flex\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.ships),function(ship,ship_idx){return _c('div',{key:ship_idx,staticClass:\"check-ship\",style:({borderColor:ship.sel == 1 ? '#0080FF' : '#D2D2D2'}),on:{\"click\":function($event){ship.sel == 1 ? ship.sel = 0 : ship.sel = 1}}},[_c('div',[_c('i',{staticClass:\"el-icon-circle-check\",style:({color:ship.sel == 1 ? '#0080FF' : '#D2D2D2'})})]),_c('div',[_c('span',{style:({color:ship.sel == 1 ? '#0080FF' : '#898989'}),domProps:{\"textContent\":_vm._s(ship.name)}})])])}),0):_vm._e(),(_vm.over_work_type == 2)?_c('div',{staticStyle:{\"margin-top\":\"20px\",\"display\":\"flex\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.xt_list),function(xt,xt_idx){return _c('div',{key:xt_idx,staticClass:\"check-ship\",style:({borderColor:xt.sel == 1 ? '#0080FF' : '#D2D2D2'}),on:{\"click\":function($event){return _vm.selXt(xt_idx)}}},[_c('span',{style:({color:xt.sel == 1 ? '#0080FF' : '#898989'}),domProps:{\"textContent\":_vm._s(xt.staff_name + '（' + xt.begin_time + '-' + xt.end_time + '）')}})])}),0):_vm._e(),(_vm.over_work_type == 3)?_c('div',{staticStyle:{\"margin-top\":\"20px\",\"display\":\"flex\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.repair_list),function(repair,repair_idx){return _c('div',{key:repair_idx,staticClass:\"check-ship\",style:({borderColor:repair.sel == 1 ? '#0080FF' : '#D2D2D2',flexDirection:'column'}),on:{\"click\":function($event){repair.sel == 1 ? repair.sel = 0 : repair.sel = 1}}},[_c('span',{style:({color:repair.sel == 1 ? '#0080FF' : '#898989',fontSize:'20px'}),domProps:{\"textContent\":_vm._s(repair.error_code + '/' + repair.product_name + '/' + repair.bom_name)}}),_c('span',{style:({color:repair.sel == 1 ? '#0080FF' : '#898989',fontSize:'20px'}),domProps:{\"textContent\":_vm._s('贴签号:' + repair.error_code)}})])}),0):_vm._e(),(_vm.over_work_type == 4)?_c('div',{staticStyle:{\"margin-top\":\"20px\",\"display\":\"flex\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.types),function(t,t_idx){return _c('div',{key:t_idx,staticClass:\"check-ship\",style:({borderColor:t.sel == 1 ? '#0080FF' : '#D2D2D2'}),on:{\"click\":function($event){t.sel == 1 ? t.sel = 0 : t.sel = 1}}},[_c('div',[_c('i',{staticClass:\"el-icon-circle-check\",style:({color:t.sel == 1 ? '#0080FF' : '#D2D2D2'})})]),_c('div',[_c('span',{style:({color:t.sel == 1 ? '#0080FF' : '#898989'}),domProps:{\"textContent\":_vm._s(t.name)}})])])}),0):_vm._e()])])],1),(_vm.over_work_type == 1)?_c('div',{staticStyle:{\"width\":\"50%\"}},[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"请扫描图纸上方二维码\")])]),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\",\"margin-top\":\"10px\"}},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.bom_key),expression:\"bom_key\"}],staticClass:\"form-control\",staticStyle:{\"ime-mode\":\"disabled\",\"width\":\"200px\"},attrs:{\"id\":\"over_bom_key\",\"type\":\"text\",\"placeholder\":\"请扫描图纸二维码\",\"autoComplete\":\"off\"},domProps:{\"value\":(_vm.bom_key)},on:{\"keypress\":_vm.handleKeypress,\"input\":function($event){if($event.target.composing)return;_vm.bom_key=$event.target.value}}}),_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\",\"size\":\"small\"},on:{\"click\":_vm.setOverBomFocus}},[_vm._v(\"点击扫码\")])],1),_c('div',{staticStyle:{\"margin-top\":\"10px\"}},[_c('span',{staticStyle:{\"font-size\":\"16px\",\"color\":\"red\"}},[_vm._v(\"如果扫码没反应，请点击上方（扫码）按钮后再扫码\")])])]),_c('div',[_c('el-table',{staticStyle:{\"width\":\"100%\",\"font-size\":\"16px\"},attrs:{\"data\":_vm.boms}},[_c('el-table-column',{attrs:{\"prop\":\"order_code\",\"label\":\"项目号\"}}),_c('el-table-column',{attrs:{\"prop\":\"product_name\",\"label\":\"产品\"}}),_c('el-table-column',{attrs:{\"prop\":\"product_code\",\"label\":\"产品号\"}}),_c('el-table-column',{attrs:{\"prop\":\"model_name\",\"label\":\"型号\"}}),_c('el-table-column',{attrs:{\"prop\":\"bom_name\",\"label\":\"工艺\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\"},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.boms.splice(scope.$index,1)}}},[_vm._v(\" 删除 \")])]}}],null,false,2107747319)})],1)],1)]):_vm._e()]),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.overWorkSave}},[_vm._v(\"确 定\")]),_c('el-button',{on:{\"click\":function($event){_vm.over_work_show = false}}},[_vm._v(\"取 消\")])],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.check-ship[data-v-5f1e5064]{\\n    padding: 10px;\\n    font-size: 24px;\\n    font-weight: 600;\\n    border: 2px solid #D2D2D2;\\n    min-width: 200px;\\n    max-width: 300px;\\n    border-radius: 10px;\\n    display: -webkit-box;\\n    display: -ms-flexbox;\\n    display: flex;\\n    -ms-flex-pack: distribute;\\n        justify-content: space-around;\\n    margin-right: 30px;\\n    cursor: pointer;\\n    margin-bottom: 30px;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"2676db8f\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=5f1e5064&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5f1e5064\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('5f1e5064')) {\n      api.createRecord('5f1e5064', component.options)\n    } else {\n      api.reload('5f1e5064', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=5f1e5064&scoped=true\", function () {\n      api.rerender('5f1e5064', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/ipc/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5f1e5064&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=5f1e5064&scoped=true\""], "names": [], "sourceRoot": ""}