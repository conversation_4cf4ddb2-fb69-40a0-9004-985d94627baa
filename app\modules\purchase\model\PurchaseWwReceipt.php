<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseWwReceipt extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $receipt_code;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $business_type;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $business_type_name;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $purchase_type;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $purchase_type_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $supplier_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $order_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $order_code;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $supplier_code;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $supplier_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $operator_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $operator_name;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $transport_mode;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $currency;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $exchange_rate;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $receipt_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $department_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $department_name;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $total_amount;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $tax_amount;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $total_amount_with_tax;

    /**
     *
     * @var string
     * @Column(type="string", length=500, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $comment;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $detail_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $approved_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_wwreceipt';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwReceipt[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwReceipt
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }

}
