<?php

namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Mes\Service\DailyReportService;

/**
 * @name("日报")
 */
class DailyreportController extends SuperController
{

    /**
     * @name("检索")
     */
    public function searchAction($type = '')
    {
        $s = new DailyReportService();
        if ($type == 'json') {
            $this->setJsonResponse();
            $jsonData = new JsonData();
            $jsonData->handleResult($s->searchAll());
            return json_encode($jsonData);
        }
    }
}