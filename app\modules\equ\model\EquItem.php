<?php

namespace Envsan\Modules\Equ\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class EquItem extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=false)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $type_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $tonnage;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $mac;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $ipc_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $model_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $model_plan_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $main_plan_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $run_status;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $stop_time;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $qrcode_url;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $ipc_print;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $serial;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $print_left;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $print_right;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $print_mac;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $fault_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $wages_ratio;


    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'equ_item';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquItem[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquItem
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
