<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Data\SessionData;
use Envsan\Modules\Common\Service\TableService;
use Phalcon\Mvc\User\Component;

class ProduceService extends Component
{
    public function selectAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.bom_id,
                t1.notice_detail_id,
                t2.quantity,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name,
                t5.name as bom_name,
                t6.name as customer_name,
                ifnull(t1.error_cnt,0) as error_cnt,
                ifnull(t1.error_money,0) as error_money,
                t1.error_type,
                t1.error_remarks,
                t1.work_date,
                t1.create_time,
                t1.staff_name,
                t1.shift_type,
                t1.work_type,
                t1.cost,
                t1.one_cost,
                t1.staff_cost,
                t1.cnt,
                t1.hour
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduceLogs', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t4.customer_id = t6.id','t6')
            ->where('t1.del_flag = 0 and t1.owner = ?1',[1 => SessionData::ownerId()])
            ->orderBy('t1.create_time desc,t1.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }


    public function selectOtherAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.produce_type,
                t1.remarks,
                t1.work_date,
                t1.create_time,
                t1.staff_name,
                t1.shift_type,
                t1.work_type,
                t1.hour
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesOtherLogs', 't1')
            ->where('t1.del_flag = 0 and t1.owner = ?1',[1 => SessionData::ownerId()])
            ->orderBy('t1.create_time desc,t1.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }
}