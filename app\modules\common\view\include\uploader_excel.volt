<div id="btn_select_excel" style="display: none;">选择文件</div>
<script>
    var uploader = null;
    function initUpLoaderExcel(url) {
        uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/main/lib/uploader/uploader.swf') }}',
            server: url,
            pick: '#btn_select_excel',
            accept: {
                title: 'Excel',
                extensions: 'xlsx',
                mimeTypes: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            fileSingleSizeLimit: 10 * 1024 * 1024,
            duplicate: true
        });

        uploader.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，只支持xlsx后缀的文件');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，文件大小不能超过10M');
            }
        });

        uploader.on('fileQueued', function(file) {
            fileQueued(file);
        });

        uploader.on('uploadSuccess', function(file, rs) {
            uploadSuccess(rs, file);
        });

        uploader.on('uploadError', function(file) {
            closeSpin();
            toastr.error('操作失败！');
        });
    }

    function uploadExcel() {
        $("#btn_select_excel").find("label").click();
    }
</script>