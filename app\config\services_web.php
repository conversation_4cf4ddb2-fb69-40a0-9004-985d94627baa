<?php

use Envsan\Common\Component\UrlResolver2;
use Phalcon\Cache\Backend\Redis as RedisCache;
use Phalcon\Cache\Frontend\Data as FrontData;
use Phalcon\Flash\Direct as Flash;
use Phalcon\Mvc\Dispatcher;
use Phalcon\Mvc\Router;
use Phalcon\Mvc\Url as UrlResolver;
use Phalcon\Mvc\View\Engine\Volt as VoltEngine;
use Phalcon\Security;
use Phalcon\Session\Adapter\Redis as SessionRedis;
use Phalcon\Text;

/**
 * Registering a router
 */
$di->setShared('router', function () {
    $router = new Router();

     $router->setDefaultModule('sys');
     $router->setDefaultNamespace('Envsan\Modules\Sys\Controller');

    return $router;
});

/**
 * The URL component is used to generate all kinds of URLs in the application
 */
$di->setShared('url', function () {
    // $config = $this->getConfig();

    $url = new UrlResolver2();
    // $url->setBaseUri($config->application->baseUri);
    // $url->setStaticBaseUri("http://static.mywebsite.com/");
    return $url;
});

/**
 * Starts the session the first time some component requests the session service
 */
$di->setShared('session', function () {
    $config = $this->getConfig();
    $session = new SessionRedis([
        // 'uniqueId'   => $config->redis->appId,
        'host'       => $config->redis->host,
        'port'       => $config->redis->port,
        'auth'       => $config->redis->password,
        'persistent' => false,
        'lifetime'   => 172800,
        'prefix'     => 'session_',
        'index'      => $config->redis->sessionIdx //使用第1个db
    ]);

    // session id放于HTTP_SID中的话，用这个id作为会话的id
    if (isset($_SERVER['HTTP_SID'])) {
        $sid = $_SERVER['HTTP_SID'];
        if (strlen($sid)>=20)
            $session->setId($sid);
    }

    session_name('SESSID');
    $session->start();
    return $session;
});

/**
 * Register the session flash service with the Twitter Bootstrap classes
 */
$di->set('flash', function () {
    return new Flash([
        'error' => 'alert alert-danger',
        'success' => 'alert alert-success',
        'notice' => 'alert alert-info',
        'warning' => 'alert alert-warning'
    ]);
});

/**
 * Set the default namespace for dispatcher
 */
$di->setShared('dispatcher', function () use ($di){
    $eventsManager = new Phalcon\Events\Manager();

    //捕获404
    // $eventsManager->attach('dispatch:beforeException', new Envsan\Modules\Sys\Plugins\NotFoundPlugin());

    $eventsManager->attach("dispatch:beforeDispatchLoop", function ($event, $dispatcher){
        $dispatcher->setActionName(
            lcfirst(Text::camelize($dispatcher->getActionName()))
        );
    });

    // ▼▼▼ 新增：API 调用日志 ▼▼▼
    $eventsManager->attach(
        "dispatch:beforeExecuteRoute",
        function ($event, $dispatcher) use ($di) {
            $request = $di->getRequest();

            // 获取原始请求的 URL（包含路径和查询参数）
            $requestUrl = $request->getURI();  // 包含路径和查询字符串（如 /mes/plan?uid=123）

            // 记录日志
            $logger = new Phalcon\Logger\Adapter\File(
                APP_PATH . '/log/api_' . date("Ymd") . '.log'
            );
            // 获取所有可能的参数来源
            $params = [
                '路由参数' => $dispatcher->getParams(),
                'GET参数' => $request->getQuery(),
                'POST参数' => $request->getPost(),
                '原始Body' => $request->getRawBody(),
                '域名端口' => $request->getScheme() . '://' . $request->getHttpHost()
            ];


            $logger->info(sprintf(
                "请求URL: %s - 参数: %s",
                $requestUrl,
                json_encode($params, JSON_UNESCAPED_UNICODE)
            ));

        }
    );

    $dispatcher = new Dispatcher();
    $dispatcher->setDefaultNamespace('Envsan\Modules\Sys\Controller');
    $dispatcher->setEventsManager($eventsManager);

    return $dispatcher;
});

/**
 * 使用redis作为缓存器，放在第2个db
 */
$di->setShared('cache', function () {
    $config = $this->getConfig();

    // Cache data for 2 days
    $frontCache = new FrontData([
        'lifetime' => 172800
    ]);

    // Create the Cache setting redis connection options
    $cache = new RedisCache($frontCache, [
        'host'        => $config->redis->host,
        'port'        => $config->redis->port,
        'auth'        => $config->redis->password,
        'prefix'      => 'cache_',
        'persistent'  => false,
        'index'       => $config->redis->cacheIdx
    ]);

    return $cache;
});

/**
 * redis服务,连接何时关闭?
 */
$di->setShared('redis', function () {
    $config = $this->getConfig();

    $redis = new \Redis();
    $redis->connect($config->redis->host, $config->redis->port);
    $redis->auth($config->redis->password);

    return $redis;
});


$di->setShared('acl', function () {
    return new \Envsan\Common\Component\Acl();
});


$di->setShared("security", function () {
    $security = new Security();
    $security->setWorkFactor(4);
    return $security;
});