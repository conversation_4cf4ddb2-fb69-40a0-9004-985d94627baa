{"version": 3, "sources": ["webpack:///./src/view/index/page1.vue", "webpack:///./src/components/tools_bar.vue", "webpack:///src/components/tools_bar.vue", "webpack:///./src/components/tools_bar.vue?74a8", "webpack:///./src/components/tools_bar.vue?00ae", "webpack:///src/view/index/page1.vue", "webpack:///./src/view/index/page1.vue?189b", "webpack:///./src/view/index/page1.vue?7860"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "_l", "top_list", "row", "domProps", "_s", "name", "style", "percent", "value", "order_list", "length", "staticStyle", "attrs", "_v", "_e", "code", "customer_name", "plan_begin_date", "plan_end_date", "instock_cnt", "error_cnt", "status_name", "class", "danger_flag", "produce_rate", "equipment_total_cnt", "equipment_error_cnt", "ref", "staticRenderFns", "current_date", "current_time", "data", "created", "that", "$store", "state", "intervalList", "push", "setInterval", "getNow", "methods", "now", "Date", "year", "getFullYear", "month", "getMonth", "substr", "date", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "component", "chartLxzb", "chartYctj", "components", "ToolsBar", "equipment_data", "bj_task_list", "zp_task_list", "mounted", "init", "initSize", "$http", "post", "then", "rs", "et_total_cnt", "et_data", "equ_error_data", "total_cnt", "createChartYctj", "createChartLxzb", "$", "height", "showLoading", "$refs", "loading", "showLoadingRainbow", "hideLoading", "hideLoa<PERSON><PERSON><PERSON><PERSON>", "echarts", "document", "getElementById", "setOption", "color", "legend", "bottom", "left", "textStyle", "series", "type", "radius", "center", "label", "show", "labelLine", "grid", "top", "right", "xAxis", "axisLabel", "splitLine", "lineStyle", "width", "yAxis", "inverse", "fontSize", "margin", "axisTick", "axisLine", "x_values", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "x", "y", "x2", "y2", "colorStops", "offset", "global", "position", "z", "barGap"], "mappings": "uHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,WAAWF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,aAAaA,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAAC<PERSON>,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,WAAWJ,EAAIM,GAAIN,EAAIO,UAAU,SAASC,GAAK,OAAON,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYK,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIG,SAAST,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAeF,EAAG,MAAM,CAACE,YAAY,UAAUQ,MAAO,UAAYJ,EAAIK,QAAU,QAASX,EAAG,MAAM,CAACE,YAAY,UAAUK,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIM,MAAQ,aAAY,SAASZ,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,QAAQ,CAACE,YAAY,kCAAkC,CAACJ,EAAIK,GAAG,GAAGH,EAAG,QAAQ,CAA2B,GAAzBF,EAAIe,WAAWC,OAAad,EAAG,KAAK,CAACA,EAAG,KAAK,CAACe,YAAY,CAAC,aAAa,UAAUC,MAAM,CAAC,QAAU,MAAM,CAAClB,EAAImB,GAAG,YAAYnB,EAAIoB,KAAKpB,EAAIM,GAAIN,EAAIe,YAAY,SAASP,GAAK,OAAON,EAAG,KAAK,CAACA,EAAG,KAAK,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIa,SAASnB,EAAG,KAAK,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIc,kBAAkBpB,EAAG,KAAK,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIe,oBAAoBrB,EAAG,KAAK,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIgB,kBAAkBtB,EAAG,KAAK,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIiB,gBAAgBvB,EAAG,KAAK,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIkB,cAAcxB,EAAG,KAAK,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAImB,gBAAgBzB,EAAG,KAAK,CAACA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBwB,MAAyB,GAAnBpB,EAAIqB,YAAmB,SAAW,IAAI,CAAC3B,EAAG,MAAM,CAACE,YAAY,qBAAqBQ,MAAO,UAAYJ,EAAIsB,aAAe,KAAM,CAAC5B,EAAG,MAAM,CAACE,YAAY,gBAAgBK,SAAS,CAAC,YAAcT,EAAIU,GAAGF,EAAIsB,aAAe,sBAAqB,eAAe5B,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAImB,GAAG,UAAUjB,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,OAAO,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGV,EAAI+B,oBAAsB,cAAc7B,EAAG,MAAM,CAACe,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQC,MAAM,CAAC,GAAK,wBAAwBhB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAImB,GAAG,WAAWjB,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,OAAO,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGV,EAAIgC,8BAA8BhC,EAAIK,GAAG,cAAc,GAAGH,EAAG,UAAU,CAAC+B,IAAI,aAAa,IAE18FC,EAAkB,CAAC,WAAY,IAAIlC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAImB,GAAG,kBAC/J,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAImB,GAAG,iBAC1I,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACe,YAAY,CAAC,MAAQ,UAAU,CAACjB,EAAImB,GAAG,WAAWjB,EAAG,KAAK,CAACe,YAAY,CAAC,MAAQ,UAAU,CAACjB,EAAImB,GAAG,UAAUjB,EAAG,KAAK,CAACe,YAAY,CAAC,MAAQ,UAAU,CAACjB,EAAImB,GAAG,WAAWjB,EAAG,KAAK,CAACe,YAAY,CAAC,MAAQ,UAAU,CAACjB,EAAImB,GAAG,WAAWjB,EAAG,KAAK,CAACe,YAAY,CAAC,MAAQ,UAAU,CAACjB,EAAImB,GAAG,UAAUjB,EAAG,KAAK,CAACe,YAAY,CAAC,MAAQ,UAAU,CAACjB,EAAImB,GAAG,WAAWjB,EAAG,KAAK,CAACe,YAAY,CAAC,MAAQ,SAAS,CAACjB,EAAImB,GAAG,QAAQjB,EAAG,KAAK,CAACF,EAAImB,GAAG,aAC5e,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAImB,GAAG,iBAC1I,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAImB,GAAG,iBAC1I,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACe,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQC,MAAM,CAAC,GAAK,oBCPrJnB,G,8BAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACO,SAAS,CAAC,YAAcT,EAAIU,GAAGV,EAAImC,iBAAiBjC,EAAG,MAAM,CAACE,YAAY,OAAOK,SAAS,CAAC,YAAcT,EAAIU,GAAGV,EAAIoC,uBAE9QF,EAAkB,GCQP,G,UAAA,CACfvB,KAAA,WAEA0B,OACA,OACAF,aAAA,GACAC,aAAA,KAIAE,UACA,IAAAC,EAAA,KACA,KAAAC,OAAAC,MAAAC,aAAAC,KACAC,aAAA,WACAL,EAAAM,WACA,OAIAC,QAAA,CACAD,SACA,IAAAE,EAAA,IAAAC,KAEAC,EAAAF,EAAAG,cACAC,GAAA,KAAAJ,EAAAK,WAAA,IAAAC,QAAA,GACAC,GAAA,IAAAP,EAAAQ,WAAAF,QAAA,GACA,KAAAlB,aAAAc,EAAA,IAAAE,EAAA,IAAAG,EAEA,IAAAE,GAAA,IAAAT,EAAAU,YAAAJ,QAAA,GACAK,GAAA,IAAAX,EAAAY,cAAAN,QAAA,GACAO,GAAA,IAAAb,EAAAc,cAAAR,QAAA,GACA,KAAAjB,aAAAoB,EAAA,IAAAE,EAAA,IAAAE,MCzCkV,I,YCO9UE,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,EAAAA,E,QC8Gf,IAAAC,EAAA,KACAC,EAAA,KAEA,OACArD,KAAA,QACAsD,WAAA,CACA,YAAAC,GAEA7B,OACA,OACA9B,SAAA,GACAwB,oBAAA,EACAoC,eAAA,GACAnC,oBAAA,EACAoC,aAAA,GACAC,aAAA,GACAtD,WAAA,KAGAuD,UACAP,EAAA,KACAC,EAAA,KAEA,KAAAO,QAEAzB,QAAA,CACAyB,OACA,KAAAC,WACA,KAAAC,MAAAC,KAAA,qBAAAC,KAAAC,IACA,KAAA7C,oBAAA6C,EAAAC,aACA,KAAAV,eAAAS,EAAAE,QACAF,EAAAG,iBACA,KAAA/C,oBAAA4C,EAAAG,eAAAC,UACA,KAAAC,gBAAAL,EAAAG,iBAGA,KAAAxE,SAAAqE,EAAArE,SACA,KAAAQ,WAAA6D,EAAA7D,WACA,KAAAmE,qBAIAV,WACAW,EAAA,yBAAAC,OAAAD,EAAA,iCAAAC,WAEAC,cACA,KAAAC,MAAAC,QAAAC,sBAEAC,cACA,KAAAH,MAAAC,QAAAG,sBAEAR,kBACAnB,IACAA,EAAA4B,QAAApB,KAAAqB,SAAAC,eAAA,gBAEA9B,EAAA+B,UAAA,CACAC,MAAA,sGACAC,OAAA,CACAC,OAAA,KACAC,KAAA,SACAC,UAAA,CACAJ,MAAA,YAGAK,OAAA,CACA,CACAC,KAAA,MACAC,OAAA,cACAC,OAAA,cACAC,MAAA,CACAC,MAAA,GAEAC,UAAA,CACAD,MAAA,GAEApE,KAAA,KAAA8B,oBAKAc,gBAAA5C,GACA2B,IACAA,EAAA2B,QAAApB,KAAAqB,SAAAC,eAAA,gBAGA7B,EAAA8B,UAAA,CACAa,KAAA,CACAC,IAAA,GACAC,MAAA,GACAX,KAAA,IACAD,OAAA,IAEAa,MAAA,CACAT,KAAA,QACAU,UAAA,CACAN,MAAA,GAEAO,UAAA,CACAC,UAAA,CACAlB,MAAA,0BACAmB,MAAA,KAIAC,MAAA,CACAd,KAAA,WACAe,SAAA,EACAL,UAAA,CACAM,SAAA,GACAtB,MAAA,UACAuB,OAAA,IAEAC,SAAA,CACAd,MAAA,GAEAe,SAAA,CACAP,UAAA,CACAlB,MAAA,UACAmB,MAAA,IAGA7E,OAAAoF,UAEArB,OAAA,CACA,CACAC,KAAA,MACAqB,SAAA,GACAC,UAAA,CACA5B,MAAA,CACAM,KAAA,SACAuB,EAAA,EACAC,EAAA,EACAC,GAAA,EACAC,GAAA,EACAC,WAAA,CACA,CAAAC,OAAA,EAAAlC,MAAA,WACA,CAAAkC,OAAA,IAAAlC,MAAA,WACA,CAAAkC,OAAA,EAAAlC,MAAA,YAEAmC,QAAA,IAGA1B,MAAA,CACAC,MAAA,EACA0B,SAAA,QACAd,SAAA,GACAtB,MAAA,WAEAqC,EAAA,EACA/F,aAEA,CACAgE,KAAA,MACAqB,SAAA,EACAW,OAAA,OACAV,UAAA,CACA5B,MAAA,2BAEAqC,EAAA,EACA/F,KAAA,iCC/R6V,ICOzV,EAAY,eACd,EACAtC,EACAmC,GACA,EACA,KACA,KACA,MAIa,e", "file": "js/chunk-f44e8b04.49cc28a0.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('div',{staticClass:\"header\"}),_c('div',{staticClass:\"body\"},[_c('tools-bar'),_c('div',{staticClass:\"box-groups\"},[_c('div',{staticClass:\"box-groups-content\"},[_c('div',{staticClass:\"box-group left\"},[_c('div',{staticClass:\"box-card cq\"},[_vm._m(0),_c('div',{staticClass:\"box-card-body\"},[_c('div',{staticClass:\"phb-box\"},_vm._l((_vm.top_list),function(row){return _c('div',{staticClass:\"phb-row\"},[_c('div',{staticClass:\"phb-title\",domProps:{\"textContent\":_vm._s(row.name)}}),_c('div',{staticClass:\"phb-bar-group\"},[_c('div',{staticClass:\"phb-bar-bg\"}),_c('div',{staticClass:\"phb-bar\",style:('width: ' + row.percent + '%')})]),_c('div',{staticClass:\"phb-val\",domProps:{\"textContent\":_vm._s(row.value + 'h')}})])}),0)])])]),_c('div',{staticClass:\"box-group center\"},[_c('div',{staticClass:\"box-card list\"},[_vm._m(1),_c('div',{staticClass:\"box-card-body\"},[_c('div',{staticClass:\"zh-table-box\"},[_c('div',{staticClass:\"zh-table-box-content\"},[_c('table',{staticClass:\"table table-bordered table-big\"},[_vm._m(2),_c('tbody',[(_vm.order_list.length == 0)?_c('tr',[_c('td',{staticStyle:{\"text-align\":\"center\"},attrs:{\"colspan\":\"8\"}},[_vm._v(\"没有数据\")])]):_vm._e(),_vm._l((_vm.order_list),function(row){return _c('tr',[_c('td',{domProps:{\"textContent\":_vm._s(row.code)}}),_c('td',{domProps:{\"textContent\":_vm._s(row.customer_name)}}),_c('td',{domProps:{\"textContent\":_vm._s(row.plan_begin_date)}}),_c('td',{domProps:{\"textContent\":_vm._s(row.plan_end_date)}}),_c('td',{domProps:{\"textContent\":_vm._s(row.instock_cnt)}}),_c('td',{domProps:{\"textContent\":_vm._s(row.error_cnt)}}),_c('td',{domProps:{\"textContent\":_vm._s(row.status_name)}}),_c('td',[_c('div',{staticClass:\"progress-group\"},[_c('div',{staticClass:\"progress-base\",class:row.danger_flag == 1 ? 'danger' : ''},[_c('div',{staticClass:\"order-progress-bar\",style:('width: ' + row.produce_rate + '%')},[_c('div',{staticClass:\"progress-text\",domProps:{\"textContent\":_vm._s(row.produce_rate + '%')}})])])])])])})],2)])])])])])]),_c('div',{staticClass:\"box-group right\"},[_c('div',{staticClass:\"box-card lxzb\"},[_vm._m(3),_c('div',{staticClass:\"box-card-body\"},[_c('div',{staticClass:\"bar-group\"},[_c('div',{staticClass:\"bar-body\"},[_c('div',{staticClass:\"chart-lxzb-container\"},[_c('div',{staticClass:\"chart-lxzb-core\"},[_c('div',{staticClass:\"lxzb-title\"},[_vm._v(\"设备总数\")]),_c('div',{staticClass:\"lxzb-val\"},[_c('span',{domProps:{\"textContent\":_vm._s(_vm.equipment_total_cnt + '台')}})])])]),_c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"id\":\"chart_lxzb\"}})])])])]),_c('div',{staticClass:\"box-card yctj\"},[_vm._m(4),_c('div',{staticClass:\"box-card-body\"},[_c('div',{staticClass:\"yctj-header\"},[_c('div',{staticClass:\"yctj-header-box\"},[_c('div',{staticClass:\"yctj-title\"},[_vm._v(\"总异常件数\")]),_c('div',{staticClass:\"yctj-val\"},[_c('span',{domProps:{\"textContent\":_vm._s(_vm.equipment_error_cnt)}})])])]),_vm._m(5)])])])])])],1),_c('loading',{ref:\"loading\"})],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"box-card-header\"},[_c('div',{staticClass:\"box-card-title\"},[_vm._v(\"个人工时绩效排行榜\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"box-card-header\"},[_c('div',{staticClass:\"box-card-title\"},[_vm._v(\"生产完成情况统计\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('thead',[_c('tr',[_c('th',{staticStyle:{\"width\":\"120px\"}},[_vm._v(\"生产批次号\")]),_c('th',{staticStyle:{\"width\":\"130px\"}},[_vm._v(\"客户名称\")]),_c('th',{staticStyle:{\"width\":\"100px\"}},[_vm._v(\"计划开工日\")]),_c('th',{staticStyle:{\"width\":\"100px\"}},[_vm._v(\"计划完工日\")]),_c('th',{staticStyle:{\"width\":\"130px\"}},[_vm._v(\"入库数量\")]),_c('th',{staticStyle:{\"width\":\"130px\"}},[_vm._v(\"不合格数量\")]),_c('th',{staticStyle:{\"width\":\"80px\"}},[_vm._v(\"状态\")]),_c('th',[_vm._v(\"进度\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"box-card-header\"},[_c('div',{staticClass:\"box-card-title\"},[_vm._v(\"设备类型数量占比\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"box-card-header\"},[_c('div',{staticClass:\"box-card-title\"},[_vm._v(\"设备异常情况统计\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"yctj-body\"},[_c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"id\":\"chart_yctj\"}})])\n}]\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"tools-bar\"},[_c('div',{staticClass:\"tools-box time-box\"},[_c('div',{domProps:{\"textContent\":_vm._s(_vm.current_date)}}),_c('div',{staticClass:\"time\",domProps:{\"textContent\":_vm._s(_vm.current_time)}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div class=\"tools-bar\">\r\n        <div class=\"tools-box time-box\">\r\n            <div v-text=\"current_date\"></div>\r\n            <div class=\"time\" v-text=\"current_time\"></div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"ToolsBar\",\r\n\r\n    data() {\r\n        return {\r\n            current_date: '',\r\n            current_time: '',\r\n        }\r\n    },\r\n\r\n    created() {\r\n        let that = this;\r\n        this.$store.state.intervalList.push(\r\n            setInterval(function() {\r\n                that.getNow();\r\n            }, 1000)\r\n        );\r\n    },\r\n\r\n    methods: {\r\n        getNow() {\r\n            let now = new Date();\r\n\r\n            let year = now.getFullYear();\r\n            let month = ('0' + (now.getMonth() + 1)).substr(-2);\r\n            let date = ('0' + now.getDate()).substr(-2);\r\n            this.current_date = year + '-' + month + '-' + date;\r\n\r\n            let hours = ('0' + now.getHours()).substr(-2);\r\n            let minutes = ('0' + now.getMinutes()).substr(-2);\r\n            let seconds = ('0' + now.getSeconds()).substr(-2);\r\n            this.current_time = hours + ':' + minutes + ':' + seconds;\r\n        },\r\n    }\r\n}\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./tools_bar.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./tools_bar.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./tools_bar.vue?vue&type=template&id=e5818362\"\nimport script from \"./tools_bar.vue?vue&type=script&lang=js\"\nexport * from \"./tools_bar.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n    <div class=\"main\">\r\n        <div class=\"header\"></div>\r\n        <div class=\"body\">\r\n            <tools-bar></tools-bar>\r\n            <div class=\"box-groups\">\r\n                <div class=\"box-groups-content\">\r\n                    <div class=\"box-group left\">\r\n                        \r\n                        <div class=\"box-card cq\">\r\n                            <div class=\"box-card-header\">\r\n                                <div class=\"box-card-title\">个人工时绩效排行榜</div>\r\n                            </div>\r\n                            <div class=\"box-card-body\">\r\n                                <div class=\"phb-box\">\r\n                                    <div v-for=\"row in top_list\" class=\"phb-row\">\r\n                                        <div class=\"phb-title\" v-text=\"row.name\"></div>\r\n                                        <div class=\"phb-bar-group\">\r\n                                            <div class=\"phb-bar-bg\"></div>\r\n                                            <div class=\"phb-bar\" :style=\"'width: ' + row.percent + '%'\"></div>\r\n                                        </div>\r\n                                        <div class=\"phb-val\" v-text=\"row.value + 'h'\"></div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"box-group center\">\r\n                        <div class=\"box-card list\">\r\n                            <div class=\"box-card-header\">\r\n                                <div class=\"box-card-title\">生产完成情况统计</div>\r\n                            </div>\r\n                            <div class=\"box-card-body\">\r\n                                <div class=\"zh-table-box\">\r\n                                    <div class=\"zh-table-box-content\">\r\n                                        <table class=\"table table-bordered table-big\">\r\n                                            <thead>\r\n                                            <tr>\r\n                                                <th style=\"width: 120px\">生产批次号</th>\r\n                                                <th style=\"width: 130px\">客户名称</th>\r\n                                                <th style=\"width: 100px\">计划开工日</th>\r\n                                                <th style=\"width: 100px\">计划完工日</th>\r\n                                                <th style=\"width: 130px\">入库数量</th>\r\n                                                <th style=\"width: 130px\">不合格数量</th>\r\n                                                <th style=\"width: 80px\">状态</th>\r\n                                                <th>进度</th>\r\n                                            </tr>\r\n                                            </thead>\r\n                                            <tbody>\r\n                                            <tr v-if=\"order_list.length == 0\">\r\n                                                <td colspan=\"8\" style=\"text-align: center;\">没有数据</td>\r\n                                            </tr>\r\n                                            <tr v-for=\"row in order_list\">\r\n                                                <td v-text=\"row.code\"></td>\r\n                                                <td v-text=\"row.customer_name\"></td>\r\n                                                <td v-text=\"row.plan_begin_date\"></td>\r\n                                                <td v-text=\"row.plan_end_date\"></td>\r\n                                                <td v-text=\"row.instock_cnt\"></td>\r\n                                                <td v-text=\"row.error_cnt\"></td>\r\n                                                <td v-text=\"row.status_name\"></td>\r\n                                                <td>\r\n                                                    <div class=\"progress-group\">\r\n                                                        <div class=\"progress-base\" :class=\"row.danger_flag == 1 ? 'danger' : ''\">\r\n                                                            <div class=\"order-progress-bar\" :style=\"'width: ' + row.produce_rate + '%'\">\r\n                                                                <div class=\"progress-text\" v-text=\"row.produce_rate + '%'\"></div>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </td>\r\n                                            </tr>\r\n                                            </tbody>\r\n                                        </table>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"box-group right\">\r\n                        <div class=\"box-card lxzb\">\r\n                            <div class=\"box-card-header\">\r\n                                <div class=\"box-card-title\">设备类型数量占比</div>\r\n                            </div>\r\n                            <div class=\"box-card-body\">\r\n                                <div class=\"bar-group\">\r\n                                    <div class=\"bar-body\">\r\n                                        <div class=\"chart-lxzb-container\">\r\n                                            <div class=\"chart-lxzb-core\">\r\n                                                <div class=\"lxzb-title\">设备总数</div>\r\n                                                <div class=\"lxzb-val\">\r\n                                                    <span v-text=\"equipment_total_cnt + '台'\"></span>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div id=\"chart_lxzb\" style=\"width: 100%;height: 100%;\"></div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"box-card yctj\">\r\n                            <div class=\"box-card-header\">\r\n                                <div class=\"box-card-title\">设备异常情况统计</div>\r\n                            </div>\r\n                            <div class=\"box-card-body\">\r\n                                <div class=\"yctj-header\">\r\n                                    <div class=\"yctj-header-box\">\r\n                                        <div class=\"yctj-title\">总异常件数</div>\r\n                                        <div class=\"yctj-val\"><span v-text=\"equipment_error_cnt\"></span></div>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"yctj-body\">\r\n                                    <div id=\"chart_yctj\" style=\"width: 100%;height: 100%;\"></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <loading ref=\"loading\"/>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import \"../../resource/lib/iconfont/iconfont.css\";\r\n    import \"../../resource/css/page.css\";\r\n    import \"../../resource/css/page1.css\";\r\n    import ToolsBar from '../../components/tools_bar';\r\n\r\n    let chartLxzb = null;\r\n    let chartYctj = null;\r\n\r\n    export default {\r\n        name: \"page1\",\r\n        components: {\r\n            'tools-bar': ToolsBar\r\n        },\r\n        data() {\r\n            return {\r\n                top_list: [],\r\n                equipment_total_cnt: 0,\r\n                equipment_data: [],\r\n                equipment_error_cnt: 0,\r\n                bj_task_list: [],\r\n                zp_task_list: [],\r\n                order_list: []\r\n            }\r\n        },\r\n        mounted() {\r\n            chartLxzb = null;\r\n            chartYctj = null;\r\n\r\n            this.init();\r\n        },\r\n        methods: {\r\n            init() {\r\n                this.initSize();\r\n                this.$http.post('screen/index/page').then((rs) => {\r\n                    this.equipment_total_cnt = rs.et_total_cnt;\r\n                    this.equipment_data = rs.et_data;\r\n                    if (rs.equ_error_data) {\r\n                        this.equipment_error_cnt = rs.equ_error_data.total_cnt;\r\n                        this.createChartYctj(rs.equ_error_data);\r\n                    }\r\n                    \r\n                    this.top_list = rs.top_list;\r\n                    this.order_list = rs.order_list;\r\n                    this.createChartLxzb();\r\n                    \r\n                });\r\n            },\r\n            initSize() {\r\n                $(\".zh-table-box-content\").height($(\".box-card.list .box-card-body\").height());\r\n            },\r\n            showLoading() {\r\n                this.$refs.loading.showLoadingRainbow();\r\n            },\r\n            hideLoading() {\r\n                this.$refs.loading.hideLoadingRainbow();\r\n            },\r\n            createChartLxzb() {\r\n                if (!chartLxzb) {\r\n                    chartLxzb = echarts.init(document.getElementById('chart_lxzb'));\r\n                }\r\n                chartLxzb.setOption({\r\n                    color: ['#be01ff', '#36d967', '#0600f1', '#c0ff01', '#e30cbd', '#ff7301', '#0ca7f7', '#ff3101', '#5b01ff', '#17f5ed'],\r\n                    legend: {\r\n                        bottom: '5%',\r\n                        left: 'center',\r\n                        textStyle: {\r\n                            color: '#FFFFFF'\r\n                        }\r\n                    },\r\n                    series: [\r\n                        {\r\n                            type: 'pie',\r\n                            radius: ['50%', '70%'],\r\n                            center: ['50%', '40%'],\r\n                            label: {\r\n                                show: false\r\n                            },\r\n                            labelLine: {\r\n                                show: false\r\n                            },\r\n                            data: this.equipment_data\r\n                        }\r\n                    ]\r\n                });\r\n            },\r\n            createChartYctj(data) {\r\n                if (!chartYctj) {\r\n                    chartYctj = echarts.init(document.getElementById('chart_yctj'));\r\n                }\r\n\r\n                chartYctj.setOption({\r\n                    grid: {\r\n                        top: 10,\r\n                        right: 20,\r\n                        left: 100,\r\n                        bottom: 30\r\n                    },\r\n                    xAxis: {\r\n                        type: 'value',\r\n                        axisLabel: {\r\n                            show: false\r\n                        },\r\n                        splitLine: {\r\n                            lineStyle: {\r\n                                color: 'rgba(38, 196, 255, 0.2)',\r\n                                width: 2\r\n                            }\r\n                        }\r\n                    },\r\n                    yAxis: {\r\n                        type: 'category',\r\n                        inverse: true,\r\n                        axisLabel: {\r\n                            fontSize: 14,\r\n                            color: '#bcdbff',\r\n                            margin: 15\r\n                        },\r\n                        axisTick: {\r\n                            show: false\r\n                        },\r\n                        axisLine: {\r\n                            lineStyle: {\r\n                                color: '#acc9ec',\r\n                                width: 2\r\n                            }\r\n                        },\r\n                        data: data.x_values\r\n                    },\r\n                    series: [\r\n                        {\r\n                            type: 'bar',\r\n                            barWidth: 13,\r\n                            itemStyle: {\r\n                                color: {\r\n                                    type: 'linear',\r\n                                    x: 0,\r\n                                    y: 0,\r\n                                    x2: 1,\r\n                                    y2: 0,\r\n                                    colorStops: [\r\n                                        {offset: 0, color: '#721F1A'},\r\n                                        {offset: 0.95, color: '#E01D0D'},\r\n                                        {offset: 1, color: '#FFC8C7'}\r\n                                    ],\r\n                                    global: false\r\n                                }\r\n                            },\r\n                            label: {\r\n                                show: true,\r\n                                position: 'right',\r\n                                fontSize: 15,\r\n                                color: '#bcdbff'\r\n                            },\r\n                            z: 2,\r\n                            data: data.data\r\n                        },\r\n                        {\r\n                            type: 'bar',\r\n                            barWidth: 9,\r\n                            barGap: '-90%',\r\n                            itemStyle: {\r\n                                color: 'rgba(53, 155, 235, 0.2)'\r\n                            },\r\n                            z: 1,\r\n                            data: [15, 15, 15, 15, 15, 15, 15, 15]\r\n                        }\r\n                    ]\r\n                });\r\n            },\r\n        }\r\n    }\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./page1.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./page1.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./page1.vue?vue&type=template&id=6d2cbfe6\"\nimport script from \"./page1.vue?vue&type=script&lang=js\"\nexport * from \"./page1.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}