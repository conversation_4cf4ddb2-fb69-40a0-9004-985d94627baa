{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div id="app" class="page-content">
    <h3 class="page-title">组织数据</h3>
    <div class="search-page">
        <div class="row">
            <div class="col-md-6">
                <div class="portlet light">
                {% if acl.isAdmin() %}
                    <div class="portlet-title">
                        <div class="actions">
                            <button type="button" class="btn yellow" onclick="create()">
                                <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                            </button>
                        </div>
                    </div>
                {% endif %}
                    <div class="portlet-body">
                        <div class="search-table">
                            <table class="table table-border table-striped table-condensed">
                                <thead class="bg-blue">
                                <tr>
                                    <th>键</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody style="border: 1px solid #ddd;border-top: 0;">
                                {% for item in items %}
                                    <tr>
                                        <td>{{ item.section }}</td>
                                        <td v-html="type('{{ item.type }}')"></td>
                                        <td>{{ item.comment }}</td>
                                        <td>
                                            <button type="button" onclick="showDetail({{ item.id }})" class="btn green">查看</button>
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        {% if gconfig != null %}
            <div class="col-md-6">
                <div class="portlet light">
                    <div class="portlet-title">
                        <div class="caption">{{ gconfig.section }} ({{ gconfig.comment }})</div>
                        <div class="actions">
                            <button class="btn red" onclick="edit({{ gconfig.id }})"><i class="fa fa-gear"></i> 自定义 (组织: {{ group.name }})</button>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="search-table">
                            <table class="table table-striped table-condensed">
                                <thead class="bg-blue">
                                    <tr>
                                        <th>级别</th>
                                        <th>值</th>
                                        {% if acl.isAdmin() %}
                                            <th>操作</th>
                                        {% endif %}
                                    </tr>
                                </thead>
                                <tbody style="border: 1px solid #ddd;border-top: 0;">
                                    <tr>
                                        <td>系统默认</td>
                                        <td>{{ gconfig.val }}</td>
                                    {% if acl.isAdmin() %}
                                        <td>
                                            <button onclick="editDef({{ gconfig.id }})" class="btn blue">编辑</button>
                                        </td>
                                    {% endif %}
                                    </tr>
                                {% for item in ritems %}
                                    <tr>
                                        {% set group = item.getGroup() %}
                                        <td>
                                            {% if group != null %}
                                                {{ group.name }}
                                            {% endif %}
                                        </td>
                                        <td>{{ item.val }}</td>
                                    {% if acl.isAdmin() %}
                                        <td>
                                            <button onclick="del({{ item.id }})" class="btn red">删除</button>
                                        </td>
                                    {% endif %}
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {},
        methods: {
            type: function(v) {
                return typeFormatter(v);
            }
        }
    });

    function typeFormatter(v) {
        var t = '';
        if (v == 'list')
            t = '数组';
        else if (v == 'json')
            t = '对象';
        else if (v == 'string')
            t = '字符串';
        else if (v == 'int')
            t = '整数';
        else if (v == 'float')
            t = '浮点数';
        return '<span class="label label-default">' + t + '</span>';
    }

    function create() {
        window.layer_result = '';
        layer.open({
            title: '新建',
            type: 2,
            area: ['40em', '55em'],
            content: '{{ url('sys/gconfig/create') }}',
            end: function () {
                if (window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    location.reload();
                }
            }
        });
    }

    function edit(id) {
        window.layer_result = '';
        layer.open({
            title: '自定义',
            type: 2,
            area: ['40em', '40em'],
            content: '{{ url('sys/gconfig/edit/') }}' + id,
            end: function () {
                if (window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    delayReload();
                }
            }
        });
    }

    function editDef(id) {
        window.layer_result = '';
        layer.open({
            title: '修改默认',
            type: 2,
            area: ['40em', '40em'],
            content: '{{ url('sys/gconfig/editdef/') }}' + id,
            end: function () {
                if (window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    delayReload();
                }
            }
        });
    }

    function del(id) {
        {% if acl.isAdmin() %}
        var dlg = layer.confirm('确认删除吗?', function () {
            $.post("{{ url('sys/gconfig/delete') }}", {id: id}, function (rs) {
                layer.close(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作成功!');
                    delayReload();
                }
                else {
                    toastr.error('操作失败!');
                }
            })
        });
        {% endif %}
    }

    function showDetail(id)
    {
        location.href = '{{ url('sys/gconfig/list') }}?id=' + id;
    }
</script>
