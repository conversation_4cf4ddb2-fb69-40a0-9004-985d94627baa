{"version": 3, "file": "js/src_view_check_detail_vue.js", "mappings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kBA;AACA;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/check/detail.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/check/detail.vue", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/check/detail.vue?587b", "webpack://rrts-manager/./src/view/check/detail.vue?5129", "webpack://rrts-manager/./src/view/check/detail.vue?cab3"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"生产质检\" is_back=\"1\"></m-header>\r\n        <m-body padding=\"false\">\r\n            <div v-if=\"loading\" style=\"padding-top: 200px;text-align: center\">\r\n                <van-loading type=\"spinner\" color=\"#1989fa\" />\r\n            </div>\r\n            <div v-else style=\"padding-bottom: 200px\">\r\n                <van-cell-group>\r\n                    <van-cell title=\"入库单号\" :value=\"data.code\" />\r\n                    <van-cell title=\"入库日\" :value=\"data.instock_date\" />\r\n                    <van-cell title=\"物资编码\" :value=\"data.goods_code\" />\r\n                    <van-cell title=\"物资名称\" :value=\"data.goods_name\" />\r\n                    <van-cell title=\"物资型号\" :value=\"data.goods_model\" />\r\n                    <van-cell title=\"入库数量\" :value=\"data.quantity + '(' + data.goods_unit + ')'\" />\r\n                </van-cell-group>\r\n                <div>\r\n                    <template v-for=\"(item,idx) in check_data\">\r\n                        <quality-field :data=\"item\" @change=\"checkResult\"></quality-field>\r\n                    </template>\r\n                </div>\r\n                <van-cell-group>\r\n                    <van-cell title=\"质检结果\">\r\n                        <template #right-icon>\r\n                            <van-tag v-if=\"data.check_result_flag == 0\" type=\"success\">OK</van-tag>\r\n                            <van-tag v-if=\"data.check_result_flag == 1\" type=\"danger\">NG</van-tag>\r\n                        </template>\r\n                    </van-cell>\r\n                    <van-field\r\n                            :is-link=\"true\"\r\n                            v-model=\"data.check_remarks\"\r\n                            rows=\"2\"\r\n                            autosize\r\n                            type=\"textarea\"\r\n                            name=\"质检说明\"\r\n                            label=\"质检说明\"\r\n                            input-align=\"right\"\r\n                            placeholder=\"请输入质检说明\"\r\n                    />\r\n                    <van-field name=\"uploader\" label=\"照片上传\">\r\n                        <template #input>\r\n                            <input\r\n                                    ref=\"fileInput\"\r\n                                    type=\"file\"\r\n                                    multiple\r\n                                    accept=\"image/*\"\r\n                                    hidden\r\n                                    @change=\"handleFileChange\"\r\n                            >\r\n                            <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;\">\r\n                                <div v-for=\"(file,i) in files\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                    <div @click=\"delPhoto(i)\" style=\"position: absolute;top: -5px;right: -5px;width: 20px;height: 20px;background-color: red;border-radius: 20px;z-index: 999;text-align: center;display: flex;flex-direction: column;justify-content: center\">\r\n                                        <van-icon name=\"cross\" size=\"16\" color=\"#FFFFFF\"/>\r\n                                    </div>\r\n                                    <van-image :src=\"file\" width=\"80px\" height=\"80px\" class=\"img-view\"></van-image>\r\n                                </div>\r\n                                <div v-if=\"files.length < 5\" @click=\"takePhoto\" style=\"width: 80px;height: 80px;background-color: #f2f2f2;text-align: center;display: flex;flex-direction: column; justify-content: center;\">\r\n                                    <van-icon name=\"photograph\" color=\"#bbbbbb\" size=\"25\"/>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </van-field>\r\n                </van-cell-group>\r\n                <div style=\"position: absolute;bottom:0;width: 100%;padding: 10px;border-top: 1px #F2F2F2 solid;background-color: #FFFFFF;z-index: 99;\">\r\n                    <van-button  round block type=\"info\" @click=\"onSubmit\">提交</van-button>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {Dialog} from \"vant\";\r\n    import base from '../../components/base';\r\n    import qualityField from '../../components/quality_field';\r\n    import { ImagePreview } from 'vant';\r\n\r\n    export default {\r\n        name: \"checkDetail\",\r\n        extends: base,\r\n        components: {qualityField,ImagePreview},\r\n        data() {\r\n            return {\r\n                uid:'',\r\n                base_path:'',\r\n                loading: true,\r\n                data:{},\r\n                check_data:[],\r\n                files:[],\r\n                error_result:0\r\n            }\r\n        },\r\n        methods: {\r\n            onLoad(){\r\n                let user = this.$store.state.user;\r\n                this.base_path = user.imgdir;\r\n                this.uid = this.$route.params.uid;\r\n                this.init();\r\n            },\r\n            onShow(){\r\n\r\n            },\r\n            init(){\r\n                this.$http.post('/work/check/init',{uid:this.uid}).then((rs) => {\r\n                    if (rs.status === 'ok') {\r\n                        this.loading = false;\r\n                        this.data = rs.data.data;\r\n                        this.files = [];\r\n                        this.check_data = rs.data.check_data;\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '异常消息',\r\n                            message: rs.message,\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                }).catch(() => {\r\n                    Dialog.alert({\r\n                        title: '网络异常',\r\n                        message: '网络异常',\r\n                    }).then(() => {\r\n                        this.$router.back();\r\n                    });\r\n                });\r\n            },\r\n            checkResult(cb){\r\n                this.data.check_result_flag = 0;\r\n                for(let check_item of this.check_data){\r\n                    check_item.result = 0;\r\n                    if (check_item.type == 6){\r\n                        for(let i = 0 ; i < check_item.values.length; i++){\r\n                            check_item.results[i] = 0;\r\n                            if (check_item.values[i] == 1){\r\n                                if (check_item.result == 0){\r\n                                    check_item.result = 1;\r\n                                }\r\n                                check_item.results[i] = 1;\r\n                            }\r\n                        }\r\n                    } else if (check_item.type == 7){\r\n                        check_item.result = 0;\r\n                        for(let i = 0 ; i < check_item.values.length; i++){\r\n                            check_item.results[i] = 0;\r\n                            let val = check_item.values[i];\r\n                            if (val != ''){\r\n                                try {\r\n                                    // let res = eval(val);\r\n                                    let num = Number(val);\r\n                                    num = Number(num.toFixed(4));\r\n                                    if (!(num >= parseFloat(check_item.standard_minus) && num <= parseFloat(check_item.standard_plus))){\r\n                                        check_item.results[i] = 1;\r\n                                    }\r\n                                } catch (e){\r\n                                    console.error(e);\r\n                                    check_item.results[i] = 1;\r\n                                }\r\n                                if (check_item.result == 0){\r\n                                    check_item.result = check_item.results[i];\r\n                                }\r\n                            }\r\n                        }\r\n                    } else if (check_item.type == 8){\r\n                        check_item.result = 0;\r\n                        let min = parseFloat(check_item.standard_val) - parseFloat(check_item.standard_minus);\r\n                        let max = parseFloat(check_item.standard_val) + parseFloat(check_item.standard_plus);\r\n                        for(let i = 0 ; i < check_item.values.length; i++){\r\n                            check_item.results[i] = 0;\r\n                            if (check_item.values[i] != ''){\r\n                                let res = parseFloat(check_item.values[i]);\r\n                                if (!(res >= min && res <= max)){\r\n                                    check_item.results[i] = 1;\r\n                                }\r\n                                if (check_item.result == 0){\r\n                                    check_item.result = check_item.results[i];\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    if (this.data.check_result_flag == 0){\r\n                        this.data.check_result_flag = check_item.result;\r\n                    }\r\n                }\r\n                cb();\r\n            },\r\n            onSubmit(){\r\n                console.log(this.check_data)\r\n                for(let item of this.check_data){\r\n                    if (item.required == 1){\r\n                        if (item.type == 1 || item.type == 3 || item.type == 5){\r\n                            if (item.value === '' || item.value === null || item.value === undefined){\r\n                                this.$toast.fail('请选择'+item.title);\r\n                                return;\r\n                            }\r\n                        } else if (item.type == 4){\r\n                            if (item.values.length == 0){\r\n                                this.$toast.fail('请选择'+item.title);\r\n                                return;\r\n                            }\r\n                        } else if (item.type == 2 || item.type == 6 || item.type == 7 || item.type == 8){\r\n                            for(let value of item.values){\r\n                                 if (value === '' || value === null || value === undefined){\r\n                                    this.$toast.fail('请输入'+item.title);\r\n                                    return;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                this.$cjs.showLoading('文件上传中');\r\n                this.upload(this.files,[],0,(upload_rs)=>{\r\n                    this.$cjs.hideLoading();\r\n                    if (upload_rs.status == 'ok'){\r\n                        this.$http.post('work/check/save', {\r\n                            uid: this.uid,\r\n                            quality_template_id:this.data.quality_template_id,\r\n                            check_result_flag: this.data.check_result_flag,\r\n                            check_remarks:this.data.check_remarks,\r\n                            check_data:encodeURI(JSON.stringify(this.check_data)),\r\n                            files : encodeURI(JSON.stringify(upload_rs.list))\r\n                        }).then((rs) => {\r\n                            if (rs.status == 'ok'){\r\n                                this.$toast.success('提交成功');\r\n                                this.$route.params.cb();\r\n                                this.$router.back();\r\n                            } else {\r\n                                this.$toast.fail(rs.message);\r\n                            }\r\n                        }).catch(() => {\r\n                            this.$toast.fail('网络异常');\r\n                        });\r\n                    } else {\r\n                        this.$toast.fail('文件上传失败！');\r\n                    }\r\n                });\r\n            },\r\n            takePhoto() {\r\n                this.$refs.fileInput.click();\r\n            },\r\n            async handleFileChange(e){\r\n                const selectedFiles = Array.from(e.target.files);\r\n                if (!selectedFiles) return;\r\n                // 逐个处理文件\r\n                for (const file of selectedFiles) {\r\n                    // 验证文件类型\r\n                    if (!file.type.startsWith('image/')) {\r\n                        this.errorMessage = '仅支持图片格式'\r\n                        continue\r\n                    }\r\n                    // 验证文件大小\r\n                    if (file.size > 10 * 1024 * 1024) {\r\n                        this.errorMessage = `文件大小不能超过10MB`\r\n                        continue\r\n                    }\r\n                    const preview = await this.readFileAsDataURL(file)\r\n                    this.files.push(preview)\r\n                }\r\n            },\r\n            readFileAsDataURL(file) {\r\n                return new Promise((resolve, reject) => {\r\n                    const reader = new FileReader()\r\n                    reader.onload = () => resolve(reader.result)\r\n                    reader.onerror = reject\r\n                    reader.readAsDataURL(file)\r\n                })\r\n            },\r\n            delPhoto(i){\r\n                this.files.splice(i,1);\r\n            },\r\n            upload(flies,new_flies,i,cb){\r\n                if (flies.length == i){\r\n                    cb({\r\n                        status : 'ok',\r\n                        list : new_flies\r\n                    });\r\n                    return;\r\n                }\r\n                this.fileUpload(flies[i],(data)=>{\r\n                    if (data.status == 'ok'){\r\n                        new_flies.push(data.path);\r\n                        i++;\r\n                        this.upload(flies,new_flies,i,cb);\r\n                    } else {\r\n                        cb(data);\r\n                    }\r\n                });\r\n            },\r\n            fileUpload(base64Data,cb){\r\n                let user = this.$store.state.user;\r\n                this.$http.fileUpload(user,'review',base64Data).then((rs) => {\r\n                    cb({\r\n                        status:'ok',\r\n                        path: rs\r\n                    });\r\n                }).catch((e) => {\r\n                    console.error(e);\r\n                    cb({status:'error'});\r\n                });\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"生产质检\",\"is_back\":\"1\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[(_vm.loading)?_c('div',{staticStyle:{\"padding-top\":\"200px\",\"text-align\":\"center\"}},[_c('van-loading',{attrs:{\"type\":\"spinner\",\"color\":\"#1989fa\"}})],1):_c('div',{staticStyle:{\"padding-bottom\":\"200px\"}},[_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"入库单号\",\"value\":_vm.data.code}}),_c('van-cell',{attrs:{\"title\":\"入库日\",\"value\":_vm.data.instock_date}}),_c('van-cell',{attrs:{\"title\":\"物资编码\",\"value\":_vm.data.goods_code}}),_c('van-cell',{attrs:{\"title\":\"物资名称\",\"value\":_vm.data.goods_name}}),_c('van-cell',{attrs:{\"title\":\"物资型号\",\"value\":_vm.data.goods_model}}),_c('van-cell',{attrs:{\"title\":\"入库数量\",\"value\":_vm.data.quantity + '(' + _vm.data.goods_unit + ')'}})],1),_c('div',[_vm._l((_vm.check_data),function(item,idx){return [_c('quality-field',{attrs:{\"data\":item},on:{\"change\":_vm.checkResult}})]})],2),_c('van-cell-group',[_c('van-cell',{attrs:{\"title\":\"质检结果\"},scopedSlots:_vm._u([{key:\"right-icon\",fn:function(){return [(_vm.data.check_result_flag == 0)?_c('van-tag',{attrs:{\"type\":\"success\"}},[_vm._v(\"OK\")]):_vm._e(),(_vm.data.check_result_flag == 1)?_c('van-tag',{attrs:{\"type\":\"danger\"}},[_vm._v(\"NG\")]):_vm._e()]},proxy:true}])}),_c('van-field',{attrs:{\"is-link\":true,\"rows\":\"2\",\"autosize\":\"\",\"type\":\"textarea\",\"name\":\"质检说明\",\"label\":\"质检说明\",\"input-align\":\"right\",\"placeholder\":\"请输入质检说明\"},model:{value:(_vm.data.check_remarks),callback:function ($$v) {_vm.$set(_vm.data, \"check_remarks\", $$v)},expression:\"data.check_remarks\"}}),_c('van-field',{attrs:{\"name\":\"uploader\",\"label\":\"照片上传\"},scopedSlots:_vm._u([{key:\"input\",fn:function(){return [_c('input',{ref:\"fileInput\",attrs:{\"type\":\"file\",\"multiple\":\"\",\"accept\":\"image/*\",\"hidden\":\"\"},on:{\"change\":_vm.handleFileChange}}),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\"}},[_vm._l((_vm.files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"}},[_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"-5px\",\"right\":\"-5px\",\"width\":\"20px\",\"height\":\"20px\",\"background-color\":\"red\",\"border-radius\":\"20px\",\"z-index\":\"999\",\"text-align\":\"center\",\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\"},on:{\"click\":function($event){return _vm.delPhoto(i)}}},[_c('van-icon',{attrs:{\"name\":\"cross\",\"size\":\"16\",\"color\":\"#FFFFFF\"}})],1),_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":file,\"width\":\"80px\",\"height\":\"80px\"}})],1)}),(_vm.files.length < 5)?_c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"background-color\":\"#f2f2f2\",\"text-align\":\"center\",\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\"},on:{\"click\":_vm.takePhoto}},[_c('van-icon',{attrs:{\"name\":\"photograph\",\"color\":\"#bbbbbb\",\"size\":\"25\"}})],1):_vm._e()],2)]},proxy:true}])})],1),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"width\":\"100%\",\"padding\":\"10px\",\"border-top\":\"1px #F2F2F2 solid\",\"background-color\":\"#FFFFFF\",\"z-index\":\"99\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"info\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"提交\")])],1)],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./detail.vue?vue&type=template&id=4f29aa4e&scoped=true\"\nimport script from \"./detail.vue?vue&type=script&lang=js\"\nexport * from \"./detail.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f29aa4e\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('4f29aa4e')) {\n      api.createRecord('4f29aa4e', component.options)\n    } else {\n      api.reload('4f29aa4e', component.options)\n    }\n    module.hot.accept(\"./detail.vue?vue&type=template&id=4f29aa4e&scoped=true\", function () {\n      api.rerender('4f29aa4e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/check/detail.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail.vue?vue&type=template&id=4f29aa4e&scoped=true\""], "names": [], "sourceRoot": ""}