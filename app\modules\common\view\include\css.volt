<style>
    .demo {
        /* for IE10+ touch devices */
        touch-action:none;
    }

    .flowchart-demo .window {
        border: 1px solid #346789;
        box-shadow: 2px 2px 19px #aaa;
        -o-box-shadow: 2px 2px 19px #aaa;
        -webkit-box-shadow: 2px 2px 19px #aaa;
        -moz-box-shadow: 2px 2px 19px #aaa;
        -moz-border-radius: 0.5em;
        border-radius: 0.5em;
        opacity: 0.8;
        width: 240px;
        height: 240px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        text-align: center;
        z-index: 20;
        position: absolute;
        background-color: #eeeeef;
        color: black;
        font-family: helvetica, sans-serif;
        font-size: 0.9em;
        -webkit-transition: -webkit-box-shadow 0.15s ease-in;
        -moz-transition: -moz-box-shadow 0.15s ease-in;
        -o-transition: -o-box-shadow 0.15s ease-in;
        transition: box-shadow 0.15s ease-in;
    }

    .flowchart-demo .window:hover {
        box-shadow: 2px 2px 19px #444;
        -o-box-shadow: 2px 2px 19px #444;
        -webkit-box-shadow: 2px 2px 19px #444;
        -moz-box-shadow: 2px 2px 19px #444;
        opacity: 0.6;
    }

    .flowchart-demo .active {
        border: 1px dotted green;
    }

    .flowchart-demo .hover {
        border: 1px dotted red;
    }

    .flowchart-demo .jtk-connector {
        z-index: 4;
    }

    .flowchart-demo .jtk-endpoint, .endpointTargetLabel, .endpointSourceLabel {
        z-index: 21;
        cursor: pointer;
    }

    .flowchart-demo .aLabel {
        background-color: white;
        padding: 0.4em;
        font: 12px sans-serif;
        color: #444;
        z-index: 21;
        border: 1px dotted gray;
        opacity: 0.8;
        cursor: pointer;
    }

    .flowchart-demo .aLabel.jtk-hover {
        background-color: #5C96BC;
        color: white;
        border: 1px solid white;
    }

    .window.jtk-connected {
        border: 1px solid green;
    }

    .jtk-drag {
        outline: 4px solid pink !important;
    }

    path, .jtk-endpoint {
        cursor: pointer;
    }

    .jtk-overlay {
        background-color:transparent;
    }


    .jtk-bootstrap {
        min-height:100vh;
        display:flex;
        flex-direction: column;
    }

    .jtk-bootstrap .jtk-page-container {
        display:flex;
        width:100vw;
        justify-content: center;
        flex:1;
    }

    .jtk-bootstrap .jtk-container {
        width: 60%;
        max-width:800px;
    }

    .jtk-bootstrap-wide .jtk-container {
        width: 80%;
        max-width:1187px;
    }

    .jtk-demo-main {
        position: relative;
        margin-top:98px;
    }

    .jtk-demo-main .description {
        font-size: 13px;
        margin-top: 25px;
        padding: 13px;
        margin-bottom: 22px;
        background-color: #f4f5ef;
    }

    .jtk-demo-main .description li {
        list-style-type: disc !important;
    }

    .jtk-demo-canvas {
        min-height:600px;
        border:1px solid #CCC;
        background-color:white;
        display: flex;
        position:relative;
    }

    .canvas-wide {
        margin-left:0;
    }

    .miniview {
        position: absolute;
        top: 25px;
        right: 25px;
        z-index: 100;
    }


    .jtk-demo-dataset {
        text-align: left;
        max-height: 600px;
        overflow: auto;
    }

    .demo-title {
        float:left;
        font-size:18px;
    }

    .controls {
        top: 25px;
        color: #FFF;
        margin-right: 10px;
        position: absolute;
        left: 25px;
        z-index: 1;
    }

    .controls i {
        background-color: #3E7E9C;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 0;
        padding: 4px;
    }

    li {
        list-style-type: none;
    }

    /* ------------------------ node palette -------------------- */

    .sidebar {
        margin:0;
        padding:0;
        padding-top: 7px;
        padding-right: 10px;
        width:150px;
        float:left;
        text-align:center;
        height: 550px;
        background-color: #84acb3;
    }

    .sidebar ul li {
        background-color: #234b5e;
        border-radius: 11px;
        color: #f7ebca;
        cursor: move;
        margin-bottom: 10px;
        margin-left: 6px;
        padding: 8px;
        width:128px;
    }

    .sidebar ul {
        width:100%;
        padding:0;
    }

    .sidebar ul li:hover {
        background-color: #577a8b;
    }

    .sidebar i {
        float:left;
    }

    @media (max-width: 600px) {
        .sidebar {
            float:none;
            height: 55px;
            width: 100%;
            padding-top:0;
        }

        .sidebar ul li {
            display:inline-block;
            margin-top: 7px;
            width:67px;
        }
        .jtk-demo-canvas {
            margin-left: 0;
            margin-top:10px;
            height:364px;
        }
    }

    /* ---------------------------------------------------------------------------------------------------- */
    /* --- jsPlumb setup ---------------------------------------------------------------------------------- */
    /* ---------------------------------------------------------------------------------------------------- */

    .jtk-connector {
        z-index:9;
    }

    .jtk-endpoint {
        z-index:12;
        opacity:0.8;
        cursor:pointer;
    }

    .jtk-overlay {
        background-color: white;
        color: #434343;
        font-weight: 400;
        padding: 4px;
        z-index:10;

    }

    .jtk-overlay.jtk-hover {
        color: #434343;
    }

    path {
        cursor:pointer;
    }

    .delete {
        padding: 2px;
        cursor: pointer;
        float: left;
        font-size: 10px;
        line-height: 20px;
    }

    .add, .edit {
        cursor: pointer;
        float:right;
        font-size: 10px;
        line-height: 20px;
        margin-right:2px;
        padding: 2px;
    }

    .edit:hover {
        color: #ff8000;
    }

    .selected-mode {
        color:#E4F013;
    }

    .connect {
        width:10px;
        height:10px;
        background-color:#f76258;
        position:absolute;
        bottom: 13px;
        right: 5px;
    }

    /* header styles */

    .demo-links {
        position: fixed;
        right: 0;
        top: 57px;
        font-size: 11px;
        background-color: white;
        opacity: 0.8;
        padding-right: 10px;
        padding-left: 5px;
        text-transform: uppercase;
        z-index:100001;
    }

    .demo-links div {
        display:inline;
        margin-right:7px;
        margin-left:7px;
    }

    .demo-links i {
        padding:4px;
    }
</style>