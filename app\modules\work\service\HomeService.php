<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Sys\Service\RoleService;
use Phalcon\Mvc\User\Component;

class HomeService extends Component
{
    public function init()
    {
        $ret = [];
        $ret['status'] = 'error';
        $user = SessionData::user();
        $ret['status'] = 'ok';
        $ret['user'] = ['real_name' => $user->real_name, 'mobile' => $user->mobile];
        $ret['review_list'] = $this->getReviewList();
        $ret['menu_list'] =  $this->getMenuList();
        $gs = new GroupService();
        $group_row = $gs->selectById($user->group_id);
        $group_name = '';
        if (!empty($group_row)){
            $group_name = $group_row->name;
        }
        $group_list = $gs->getGroupList()->toArray();
        if (count($group_list) > 1) {
            array_unshift($group_list, ['uid' => '', 'name' => '全部组织']);
        }
        $group_idx = 0;
        if ($this->session->has('group_all') && !$this->session->get('group_all')) {
            $group_row = $gs->selectById($this->session->get('group_id'));
            if ($group_row == false) {
                $ret['message'] = ErrorHelper::WRONG_ID;
                return $ret;
            }
            $group_uid = $group_row->uid;
            foreach ($group_list as $idx => $group)
            {
                if ($group['uid'] == $group_uid) {
                    $group_idx = $idx;
                    break;
                }
            }
        }
        $ret['date'] = DateUtil::today();
        $ret['group_name'] = $group_name;
        return $ret;
    }

    public function getReviewList(){
        $rs =  new ReviewService();
        return $rs->getCntData();
    }


    public function getMenuList()
    {
        $user = SessionData::user();
        $rs = new RoleService();
        $role = $rs->selectById($user->role_id);
        $is_admin = false;
        if ($role != null) {
            if ($role->identity == 'admin'){
                $is_admin = true;
                $this->session->set('admin', true);
            }
        }

        $menu_list_all = [
            [
                'name' => '生产管理',
                'list' =>  [
                    ['name' => '扫一扫', 'icon' => 'scan', 'url' => 'produce/view', 'identity' => 'work:produce:view','scan' => 1],
                    ['name' => '扫码报工', 'icon' => 'list-switching', 'url' => 'produce/report', 'identity' => 'work:produce:report','scan' => 1],
                    ['name' => '其他报工', 'icon' => 'records-o', 'url' => 'produce/other', 'identity' => 'work:produce:other','scan' => 0],
                    ['name' => '工时统计', 'icon' => 'completed-o', 'url' => 'produce/list', 'identity' => 'work:produce:list','scan' => 0]
                ]
            ],
            [
                'name' => '质量管理',
                'list' =>  [
                    ['name' => '生产质检', 'icon' => 'shield-o', 'url' => 'quality/produce', 'identity' => 'work:quality:produce','scan' => 1],
                    ['name' => '原材料检验', 'icon' => 'shield-o', 'url' => 'check/list', 'identity' => 'work:check:list','scan' => 0],
                    ['name' => '质检履历', 'icon' => 'list-switch', 'url' => 'quality/list', 'identity' => 'work:quality:list','scan' => 0]
                ]
            ],
            [
                'name' => '设备管理',
                'list' =>  [
                    ['name' => '设备故障管理', 'icon' => 'warn-o', 'url' => 'faultList', 'identity' => 'work:equ:fault','scan' => 0],
                    ['name' => '外协修理管理', 'icon' => 'setting-o', 'url' => 'repairList', 'identity' => 'work:equ:repair','scan' => 0],
                    ['name' => '设备点检管理', 'icon' => 'completed-o', 'url' => 'checkList', 'identity' => 'work:equ:check','scan' => 0],
                    ['name' => '设备保养管理', 'icon' => 'brush-o', 'url' => 'maintainList', 'identity' => 'work:equ:maintain','scan' => 0]
                ]
            ],
        ];
        $menu_list = [];
        foreach ($menu_list_all as $menus)
        {
            $menus_new = [
                'name' => $menus['name'],
                'list' => []
            ];
            foreach ($menus['list'] as $menu)
            {
                if ($this->acl->has($menu['identity']) || $is_admin) {
                    $menus_new['list'][] = $menu;
                }
            }
            if (count($menus_new['list']) > 0) {
                array_push($menu_list, $menus_new);
            }
        }
        return $menu_list;
    }
}