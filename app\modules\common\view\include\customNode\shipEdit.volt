<script>
    class ButtonNode extends HtmlNode {
        setHtml(rootEl) {
            const { properties } = this.props.model;
            const el = document.createElement('div');
            el.className = 'uml-wrapper';
            const html = `
                 <div style="width: 300px;height: 150px;border: 2px #0052D9 solid;border-radius: 5px;background-color: #FFF">
                    <div style="display: flex;flex-direction: row;background-color: #f2f2f2;padding: 10px;border-bottom: 1px #E2E2E2 solid;justify-content: space-between;border-radius: 5px 5px 0px 0px;">
                        <div>
                            <span style="font-size: 16px;">${properties.type_name ? properties.type_name:''}</span>
                        </div>
                        <a type="button" onclick="delDataShipCard('${properties.id}')" onmousedown="shipCardStop(arguments[0])"  style="color: red;font-size: 20px">
                            <i class="fa fa-times"></i>
                        </a>
                    </div>
                    <div style="padding-top: 5px">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">工艺名称</label>
                                    <div class="col-sm-8">
                                       <input type="text" class="form-control" placeholder="请输入名称" onblur="setDataShipDataName('${properties.id}',arguments[0])" value="${properties.name ? properties.name:''}" onfocus="handleFocus(arguments[0])" maxlength="100"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12" style="margin-top: 10px">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">构成数量</label>
                                    <div class="col-sm-8">
                                       <input type="number" class="form-control"  placeholder="请输入数量" onblur="setDataShipDataCnt('${properties.id}',arguments[0])" value="${properties.cnt ? properties.cnt:''}" maxlength="4">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            el.innerHTML = html;
            rootEl.innerHTML = '';
            rootEl.appendChild(el);
            window.shipCardStop = (ev) => {
                ev.stopPropagation();
            };
            window.delDataShipCard = (id) => {
                ship_flow_lf.deleteNode(id);
            };
            window.setDataShipDataName = (id,obj) => {
                let nodeModel = ship_flow_lf.getNodeModelById(id);
                nodeModel.setProperty('name',obj.srcElement.value);
            };
            window.setDataShipDataCnt = (id,obj) => {
                let nodeModel = ship_flow_lf.getNodeModelById(id);
                nodeModel.setProperty('cnt',obj.srcElement.value);
            };
            window.handleFocus = (ev) => {
                setTimeout(() => {
                    ev.target.select();
                }, 100);
            };
        }
    }

    class ButtonNodeModel extends HtmlNodeModel {
        setAttributes() {
            this.width = 300;
            this.height = 150;
            this.text.editable = false;
        }
        createId() {
            return getUuid2();
        }
    }

    function shipCard(type){
        return {
            type: type,
            view: ButtonNode,
            model: ButtonNodeModel
        }
    }
</script>