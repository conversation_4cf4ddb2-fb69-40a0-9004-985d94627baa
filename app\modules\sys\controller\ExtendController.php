<?php
namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Sys\Model\ExtendColumn;
use Envsan\Modules\Sys\Service\ExtendService;
/**
 * @name("拓展字段")
 */
class ExtendController extends SuperController
{

    /**
     * @name("管理")
     */
    public function listAction($type='')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $us = new ExtendService();
            $rows = $us->selectAll();
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'sys:extend:list'})
     */
    public function createAction()
    {
        if($this->request->isPost()){
            $this->setJsonResponse();
            $rs = new ExtendService();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new ExtendColumn())->toArray();
        $jrow['page_id'] = '';
        $this->view->page_list = ConstantUtil::$page_extend_column;
        $this->view->jsonForm = json_encode($jrow);
    }

    /**
     * @acl({'link':'sys:extend:list'})
     */
    function editAction($uid)
    {
        $rs = new ExtendService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
        $jrow['data'] = ConstantUtil::$form_data_template;
        $jrow['add_flag'] = 1;
        $this->view->uid = $uid;
        $this->view->jsonForm = json_encode($jrow);
        $this->view->inputTypes = ConstantUtil::$input_types;
        $this->view->defaultData = ConstantUtil::$form_data_template;
    }

    /**
     * @acl({'link':'sys:extend:list'})
     */
    public function deleteAction(){
        $s = new ExtendService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

}