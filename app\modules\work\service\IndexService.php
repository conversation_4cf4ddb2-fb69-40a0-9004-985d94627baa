<?php
namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DomainUtil;
use Envsan\Common\Util\UUID;
use Envsan\Common\Util\WeixinUtil;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Sys\Service\UserService;
use GuzzleHttp\Client;
use Phalcon\Mvc\User\Component;

class IndexService extends Component
{
    public function init(){
        $registration = $this->request->getPost('registration', 'tstring');
        if (empty($registration)) {
            $user = User::findFirst([
                'session_id = ?1 and account_status = 0 and del_flag = 0',
                'bind' => [1 => $this->session->getId()]
            ]);
        } else {
            $user = User::findFirst([
                'app_registration_id = ?1 and account_status = 0 and del_flag = 0',
                'bind' => [1 => $registration]
            ]);
        }
        $this->session->set('app_registration_id', $registration);
        if (!empty($user)) {
            $this->session->set(SessionData::$_USER_KEY, $user);
            $gs = new GroupService();
            if (!empty($user->auth_data_ids)){
                $arr = explode(',', $user->auth_data_ids);
            } else {
                $arr = $gs->selectGroupIds($user->group_id);
            }
            $this->session->set('group_ids', $arr);
            $us = new UserService();
            $this->session->set('acl', $us->buildAcl($user));
            $cs = new CommonService();
            $user = $cs->getUserData();
        }

        $ret['status'] = 'ok';
        $ret['sid'] = $this->session->getId();
        $ret['user'] = $user;
        return $ret;
    }

}