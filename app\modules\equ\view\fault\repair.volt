{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">外协费用统计</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">统计日期</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="date dtpicker form-control" name="date_begin" v-model="date_begin">
                                    <span class="input-group-addon no-border-lr">至</span>
                                    <input type="text" class="date dtpicker form-control" name="date_end" v-model="date_end">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('equ/fault/repair/' ~ stat_type ~ '/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                    <tr>
                        {% if stat_type == 1 %}
                        <th data-field="fault_month">月份</th>
                        {% elseif stat_type == 2 %}
                        <th data-field="equ_code">设备</th>
                        {% else %}
                        <th data-field="repair_company">供应商</th>
                        {% endif %}
                        <th data-field="total_money">总费用</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            date_begin: '',
            date_end: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable();

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format:'yyyy-mm-dd'
    }).on('changeDate', function() {
        app[$(this).attr('name')] = $(this).val();
    });
</script>