<?php

namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;

class DailyReportService extends BaseService
{
    public function searchAll()
    {
        $date_start = $this->request->get('date_start', 'tstring');
        $date_end = $this->request->get('date_end', 'tstring');
        $notice_code = $this->request->get('notice_code', 'tstring');
        $product_name = $this->request->get('product_name', 'tstring');
        $product_code = $this->request->get('product_code', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.day_hour,
                t.report_date,
                t.produce_list,
                t1.status,
                t1.status_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesReportDetail', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesReport', 't1.id = t.report_id', 't1')
            ->where('t.del_flag = 0 and t.owner = :owner: and t1.status = ?1', [
                'owner' => SessionData::ownerId(),
                1 => 20
            ]);

        // 添加日期搜索条件
        if (!empty($date_start)) {
            $builder->andWhere("t.report_date >= ?2", [2 => $date_start]);
        }
        if (!empty($date_end)) {
            $builder->andWhere("t.report_date <= ?3", [3 => $date_end]);
        }

        $builder->orderBy('t.report_date desc');

        $report_details = $builder->getQuery()->execute()->toArray();

        // 处理JSON数据并关联其他表
//        $result = [];
        $grouped_data = []; // 用于分组统计

        foreach ($report_details as $detail) {
            $produce_list = json_decode($detail['produce_list'], true);

            if (!empty($produce_list)) {
                // 收集所有需要关联的ID
                $notice_detail_ids = [];
                $bom_ids = [];
                $notice_codes = [];

                foreach ($produce_list as $produce_item) {
                    if (!empty($produce_item['notice_detail_id'])) {
                        $notice_detail_ids[] = $produce_item['notice_detail_id'];
                    }
                    if (!empty($produce_item['bom_id'])) {
                        $bom_ids[] = $produce_item['bom_id'];
                    }
                    if (!empty($produce_item['notice_code'])) {
                        $notice_codes[] = $produce_item['notice_code'];
                    }
                }

                // 去重
                $notice_detail_ids = array_unique($notice_detail_ids);
                $bom_ids = array_unique($bom_ids);
                $notice_codes = array_unique($notice_codes);

                // 批量查询关联数据
                $notice_details_map = [];
                $bom_map = [];

                // 查询通知单详情
                if (!empty($notice_detail_ids)) {
                    $notice_details = $this->modelsManager->createBuilder()
                        ->columns('
                            t1.id,
                            t1.notice_id,
                            t1.product_id,
                            t1.quantity,
                            t2.code as notice_code,
                            t3.code as product_model,
                            t3.name as product_name
                        ')
                        ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
                        ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't1.notice_id = t2.id', 't2')
                        ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't1.product_id = t3.id', 't3')
                        ->where('t1.del_flag = 0 and t1.id IN ({ids:array})', [
                            'ids' => $notice_detail_ids
                        ])
                        ->getQuery()
                        ->execute()
                        ->toArray();

                    foreach ($notice_details as $nd) {
                        $notice_details_map[$nd['id']] = $nd;
                    }
                }

                // 查询BOM信息
                if (!empty($bom_ids)) {
                    $bom_list = $this->modelsManager->createBuilder()
                        ->columns('
                            t1.id,
                            t1.name as bom_name,
                            t1.data_sort,
                            t1.produce_cnt,
                            t1.nid
                        ')
                        ->addFrom('Envsan\Modules\Mes\Model\MesProductBom', 't1')
                        ->where('t1.del_flag = 0 and t1.id IN ({ids:array})', [
                            'ids' => $bom_ids
                        ])
                        ->getQuery()
                        ->execute()
                        ->toArray();

                    foreach ($bom_list as $bom) {
                        $bom_map[$bom['id']] = $bom;
                    }
                }

                // 处理每个生产记录，补充关联数据并进行分组统计
                foreach ($produce_list as &$produce_item) {
                    // 补充通知单和产品信息
                    if (!empty($produce_item['notice_detail_id']) && 
                        isset($notice_details_map[$produce_item['notice_detail_id']])) {
                        $notice_detail = $notice_details_map[$produce_item['notice_detail_id']];
                        $produce_item['notice_info'] = $notice_detail;
                        
                        // 如果JSON中没有这些信息，从关联数据中补充
                        if (empty($produce_item['notice_code'])) {
                            $produce_item['notice_code'] = $notice_detail['notice_code'];
                        }
                        if (empty($produce_item['product_code'])) {
                            $produce_item['product_code'] = $notice_detail['product_code'];
                        }
                        if (empty($produce_item['product_name'])) {
                            $produce_item['product_name'] = $notice_detail['product_name'];
                        }
                    }

                    // 补充BOM信息
                    if (!empty($produce_item['bom_id']) && 
                        isset($bom_map[$produce_item['bom_id']])) {
                        $bom_info = $bom_map[$produce_item['bom_id']];
                        $produce_item['bom_info'] = $bom_info;
                    }

                    // 分组统计 - 按照notice_code + product_id
                    $product_id = '';
                    if (isset($produce_item['notice_info']['product_id'])) {
                        $product_id = $produce_item['notice_info']['product_id'];
                    } elseif (!empty($produce_item['notice_detail_id']) && 
                              isset($notice_details_map[$produce_item['notice_detail_id']])) {
                        $product_id = $notice_details_map[$produce_item['notice_detail_id']]['product_id'];
                    }
                    $group_key = $produce_item['notice_code'] . '_' . $product_id;
                    
                    if (!isset($grouped_data[$group_key])) {
                        $grouped_data[$group_key] = [
                            'notice_code' => $produce_item['notice_code'],
                            'product_code' => $produce_item['product_code'],
                            'product_name' => $produce_item['product_name'],
//                            'report_date' => $detail['report_date'],
                            'total_hour' => 0,
                            'total_cnt' => 0,
                            'total_error_cnt' => 0
                        ];
                    }

                    // 累加时间和数量
                    $grouped_data[$group_key]['total_hour'] += floatval($produce_item['hour'] ?? 0);
                    $grouped_data[$group_key]['total_cnt'] += intval($produce_item['cnt'] ?? 0);
                    $grouped_data[$group_key]['total_error_cnt'] += intval($produce_item['error_cnt'] ?? 0);
                }

                // 构建详细结果（如果需要详细数据）
//                $result[] = [
//                    'id' => $detail['id'],
//                    'day_hour' => $detail['day_hour'],
//                    'report_date' => $detail['report_date'],
//                    'status' => $detail['status'],
//                    'status_name' => $detail['status_name'],
//                    'produce_list' => $produce_list
//                ];
            }
        }

        // 转换分组数据为数组格式
        $grouped_result = array_values($grouped_data);

        // 根据搜索条件过滤数据
        if (!empty($notice_code) || !empty($product_name) || !empty($product_code)) {
            $filtered_result = [];
            foreach ($grouped_result as $item) {
                $match = true;
                
                // 生产批次过滤
                if (!empty($notice_code) && strpos($item['notice_code'], $notice_code) === false) {
                    $match = false;
                }
                
                // 产品名称过滤
                if (!empty($product_name) && strpos($item['product_name'], $product_name) === false) {
                    $match = false;
                }
                
                // 规格型号过滤
                if (!empty($product_code) && strpos($item['product_code'], $product_code) === false) {
                    $match = false;
                }
                
                if ($match) {
                    $filtered_result[] = $item;
                }
            }
            $grouped_result = $filtered_result;
        }

        // 按报告日期倒序排列
//        usort($grouped_result, function($a, $b) {
//            return strcmp($b['report_date'], $a['report_date']);
//        });

        // 返回分组统计数据（不分页）
        return $grouped_result;
    }
}