<?php
namespace Envsan\Modules\Work\Api;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Work\Service\CommonService;

class CommonController extends SuperController
{
    /**
     * @noacl
     */
    public function pdfAction($file_name){
        $file_name = str_replace('_','/',$file_name);
        $this->setPdfResponse();
        $file_url = $this->config->oss->host . $file_name . '.pdf';
        $remote_file = fopen($file_url, 'rb');
        if (!$remote_file) {
            header("HTTP/1.0 404 Not Found");
            exit;
        }
        fpassthru($remote_file);
    }

    public function uploadAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new FileService();
            $ret = new JsonData();
            $folder_name = $this->request->getPost('folder_name', 'tstring');
            $img_base64 = $this->request->getPost('img_base64', 'tstring');
            $ret->handleResult($s->uploadBase64($folder_name, $img_base64,'jpg'));
            return json_encode($ret);
        }
    }
}
