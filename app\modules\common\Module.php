<?php
namespace Envsan\Modules\Common;

use Phalcon\Config;
use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\View;
use Phalcon\Mvc\View\Engine\Php as PhpEngine;
use Phalcon\Mvc\ModuleDefinitionInterface;

class Module implements ModuleDefinitionInterface
{
    /**
     * Registers an autoloader related to the module
     *
     * @param DiInterface $di
     */
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'Envsan\Modules\Common\Controller' => __DIR__ . '/controller/',
            'Envsan\Modules\Common\Model'      => __DIR__ . '/model/',
            'Envsan\Modules\Common\Service'    => __DIR__ . '/service/',
            'Envsan\Modules\Common\Util'    => __DIR__ . '/util/',
            'Envsan\Modules\Sys\Model'       => APP_PATH . '/modules/sys/model/',
        ]);

        $loader->register();
    }

    /**
     * Registers services related to the module
     *
     * @param DiInterface $di
     */
    public function registerServices(DiInterface $di)
    {
        /**
         * Try to load local configuration
         */
        if (file_exists(__DIR__ . '/config/config.php')) {
            $override = new Config(include __DIR__ . '/config/config.php');
            $config = $di->get('config');

            if ($config instanceof Config) {
                $config->merge($override);
            } else {
                $config = $override;
            }
        }

        /**
         * Setting up the view component
         */
        $di->setShared('view', function () {
            $view = new View();
            $view->setDI($this);
            $view->setViewsDir(__DIR__ . '/view/');
            $view->setPartialsDir(__DIR__ . '/view/include/');
            $view->registerEngines([
                '.volt'  => 'voltShared',
                '.phtml' => PhpEngine::class
            ]);

            return $view;
        });


        $di->setShared('log', function() use($di){
            $config = $di->get('config');
            $logger = new \Phalcon\Logger\Adapter\File( $config->application->logDir.'common-'.date("Ymd").'.log' );
            return $logger;
        });

        if (file_exists(__DIR__ . '/config/upyun.php')) {
            $di->setShared('upyun', function () {
                return new Config(include __DIR__ . '/config/upyun.php');
            });
        }
    }
}
