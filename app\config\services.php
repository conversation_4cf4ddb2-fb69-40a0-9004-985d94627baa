<?php

use Phalcon\Events\Event;
use Phalcon\Events\Manager as EventsManager;
use Phalcon\Logger;
use Phalcon\Logger\Adapter\File as FileLogger;
use Phalcon\Mvc\Model\Metadata\Memory as MetaDataAdapter;
use Phalcon\Mvc\View\Engine\Volt as VoltEngine;

/**
 * Shared configuration service
 */
$di->setShared('config', function () {
    return include APP_PATH . "/config/config.php";
});

/**
 * Database connection is created based in the parameters defined in the configuration file
 */
$di->setShared('db', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class([
        'host' => $config->database->host,
        'username' => $config->database->username,
        'password' => $config->database->password,
        'dbname' => $config->database->dbname,
        'charset' => $config->database->charset
    ]);

    if ($config->showDbLog) {
        // 监听sql写入记录
        $logger = new FileLogger($config->application->logDir . 'db-' . date("Ymd") . '.log');
        $eventsManager = new EventsManager();

        $eventsManager->attach('db', function (Event $event, $connection) use ($logger) {
            if ($event->getType() == 'beforeQuery') {
                // 获取原始SQL和绑定参数
                $sql = $connection->getSQLStatement();
                $params = method_exists($connection, 'getSqlVariables')
                    ? $connection->getSqlVariables()
                    : [];

                // 记录原始SQL和参数（不替换占位符）
                $logContent = sprintf(
                    "SQL: %s\nParams: %s",
                    $sql,
                    json_encode($params, JSON_PRETTY_PRINT)
                );

                $logger->log($logContent, Logger::INFO);
            }
        });
        $connection->setEventsManager($eventsManager);
    }

    return $connection;
});

/**
 * If the configuration specify the use of metadata adapter use it or use memory otherwise
 */
$di->setShared('modelsMetadata', function () {
    return new MetaDataAdapter();

    $config = $this->getConfig();
    return new \Phalcon\Mvc\Model\Metadata\Redis([
        'host' => $config->redis->host,
        'port' => $config->redis->port,
        'auth' => $config->redis->password,
        'persistent' => false,
        'lifetime' => 3600,
        'statsKey' => '_PHCM_MD',
        'index' => $config->redis->metaIdx //使用第1个db
    ]);
});

/**
 * Configure the Volt service for rendering .volt templates
 */
$di->setShared('voltShared', function ($view) {
    $config = $this->getConfig();

    $volt = new VoltEngine($view, $this);
    $volt->setOptions([
        'compiledPath' => $config->application->cacheDir . 'volt/',
        'compiledSeparator' => '_'
    ]);

    return $volt;
});

$di->setShared('modelsManager', function () {
    Phalcon\Mvc\Model::setup(['exceptionOnFailedSave' => true]);
    return new \Phalcon\Mvc\Model\Manager();
});

/**
 * 自定义过滤器
 */
$di->setShared('filter', function () {
    $filter = new \Phalcon\Filter();
    $filter->add('bool', function ($value) {
        if ($value == 'false' || $value == 'off' || $value == '0' || $value == '' || $value == 'null')
            return '0';
        return '1';
    });

    $filter->add('int', function ($value) {
        return intval($value);
    });

    $filter->add('tstring', function ($value) use($filter){
        return trim($filter->sanitize($value, 'string'));
    });
    return $filter;
});
