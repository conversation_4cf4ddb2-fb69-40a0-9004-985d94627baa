{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light portlet-detail" style="margin-bottom: 0;">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-social-dribbble font-green"></i>
                <span class="caption-subject font-green bold">入库信息</span>
            </div>
        </div>
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">入库单号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">外委加工单</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">供应商</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">入库日期</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="instock_date" v-model="instock_date" readonly/>
                                </div>
                            </div>
                        </div>
                        {{ partial('view') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item, index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" class="form-control" v-model="remarks" readonly rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="zh-table-box">
                <div class="zh-table-box-content">
                    <table class="table table-bordered table-big">
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>批次号</th>
                            <th>产品名称</th>
                            <th>产品规格</th>
                            <th>外委工艺</th>
                            <th>入库计量数量</th>
                            <th>外委计量数量</th>
                            <th>未税单价</th>
                            <th>含税单价</th>
                            <th>未税金额</th>
                            <th>含税金额</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="row, index in detail_data">
                            <td v-text="index + 1"></td>
                            <td v-text="row.notice_code"></td>
                            <td v-text="row.product_name"></td>
                            <td v-text="row.product_code"></td>
                            <td v-text="row.bom_name"></td>
                            <td v-text="row.quantity"></td>
                            <td v-text="row.pricing_quantity"></td>
                            <td v-text="row.price"></td>
                            <td v-text="row.price_hs"></td>
                            <td v-text="row.total_money"></td>
                            <td v-text="row.total_money_hs"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonInstock }},
    });

    function initSize() {
        let portlet_height = $(window).height() - 35 - 27 - 8;
        $(".portlet-detail .zh-table-box-content").height(portlet_height
            - $(".portlet-detail .portlet-title").outerHeight(true)
            - $(".portlet-detail #form").outerHeight(true));
    }

    $(function() {
        initSize();
    });
</script>
<style>
    .zh-table-box table thead {
        z-index: 3;
    }
</style>