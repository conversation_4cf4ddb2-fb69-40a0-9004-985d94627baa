(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f44e8b04"],{4620:function(t,s,e){"use strict";e.r(s);var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"main"},[s("div",{staticClass:"header"}),s("div",{staticClass:"body"},[s("tools-bar"),s("div",{staticClass:"box-groups"},[s("div",{staticClass:"box-groups-content"},[s("div",{staticClass:"box-group left"},[s("div",{staticClass:"box-card cq"},[t._m(0),s("div",{staticClass:"box-card-body"},[s("div",{staticClass:"phb-box"},t._l(t.top_list,(function(e){return s("div",{staticClass:"phb-row"},[s("div",{staticClass:"phb-title",domProps:{textContent:t._s(e.name)}}),s("div",{staticClass:"phb-bar-group"},[s("div",{staticClass:"phb-bar-bg"}),s("div",{staticClass:"phb-bar",style:"width: "+e.percent+"%"})]),s("div",{staticClass:"phb-val",domProps:{textContent:t._s(e.value+"h")}})])})),0)])])]),s("div",{staticClass:"box-group center"},[s("div",{staticClass:"box-card list"},[t._m(1),s("div",{staticClass:"box-card-body"},[s("div",{staticClass:"zh-table-box"},[s("div",{staticClass:"zh-table-box-content"},[s("table",{staticClass:"table table-bordered table-big"},[t._m(2),s("tbody",[0==t.order_list.length?s("tr",[s("td",{staticStyle:{"text-align":"center"},attrs:{colspan:"8"}},[t._v("没有数据")])]):t._e(),t._l(t.order_list,(function(e){return s("tr",[s("td",{domProps:{textContent:t._s(e.code)}}),s("td",{domProps:{textContent:t._s(e.customer_name)}}),s("td",{domProps:{textContent:t._s(e.plan_begin_date)}}),s("td",{domProps:{textContent:t._s(e.plan_end_date)}}),s("td",{domProps:{textContent:t._s(e.instock_cnt)}}),s("td",{domProps:{textContent:t._s(e.error_cnt)}}),s("td",{domProps:{textContent:t._s(e.status_name)}}),s("td",[s("div",{staticClass:"progress-group"},[s("div",{staticClass:"progress-base",class:1==e.danger_flag?"danger":""},[s("div",{staticClass:"order-progress-bar",style:"width: "+e.produce_rate+"%"},[s("div",{staticClass:"progress-text",domProps:{textContent:t._s(e.produce_rate+"%")}})])])])])])}))],2)])])])])])]),s("div",{staticClass:"box-group right"},[s("div",{staticClass:"box-card lxzb"},[t._m(3),s("div",{staticClass:"box-card-body"},[s("div",{staticClass:"bar-group"},[s("div",{staticClass:"bar-body"},[s("div",{staticClass:"chart-lxzb-container"},[s("div",{staticClass:"chart-lxzb-core"},[s("div",{staticClass:"lxzb-title"},[t._v("设备总数")]),s("div",{staticClass:"lxzb-val"},[s("span",{domProps:{textContent:t._s(t.equipment_total_cnt+"台")}})])])]),s("div",{staticStyle:{width:"100%",height:"100%"},attrs:{id:"chart_lxzb"}})])])])]),s("div",{staticClass:"box-card yctj"},[t._m(4),s("div",{staticClass:"box-card-body"},[s("div",{staticClass:"yctj-header"},[s("div",{staticClass:"yctj-header-box"},[s("div",{staticClass:"yctj-title"},[t._v("总异常件数")]),s("div",{staticClass:"yctj-val"},[s("span",{domProps:{textContent:t._s(t.equipment_error_cnt)}})])])]),t._m(5)])])])])])],1),s("loading",{ref:"loading"})],1)},i=[function(){var t=this,s=t._self._c;return s("div",{staticClass:"box-card-header"},[s("div",{staticClass:"box-card-title"},[t._v("个人工时绩效排行榜")])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"box-card-header"},[s("div",{staticClass:"box-card-title"},[t._v("生产完成情况统计")])])},function(){var t=this,s=t._self._c;return s("thead",[s("tr",[s("th",{staticStyle:{width:"120px"}},[t._v("生产批次号")]),s("th",{staticStyle:{width:"200px"}},[t._v("客户名称")]),s("th",{staticStyle:{width:"100px"}},[t._v("计划开工日")]),s("th",{staticStyle:{width:"100px"}},[t._v("计划完工日")]),s("th",{staticStyle:{width:"100px"}},[t._v("入库数量")]),s("th",{staticStyle:{width:"100px"}},[t._v("不合格数量")]),s("th",{staticStyle:{width:"80px"}},[t._v("状态")]),s("th",[t._v("进度")])])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"box-card-header"},[s("div",{staticClass:"box-card-title"},[t._v("设备类型数量占比")])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"box-card-header"},[s("div",{staticClass:"box-card-title"},[t._v("设备异常情况统计")])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"yctj-body"},[s("div",{staticStyle:{width:"100%",height:"100%"},attrs:{id:"chart_yctj"}})])}],r=(e("7703"),e("c534"),e("8a38"),function(){var t=this,s=t._self._c;return s("div",{staticClass:"tools-bar"},[s("div",{staticClass:"tools-box time-box"},[s("div",{domProps:{textContent:t._s(t.current_date)}}),s("div",{staticClass:"time",domProps:{textContent:t._s(t.current_time)}})])])}),o=[],l=(e("14d9"),{name:"ToolsBar",data(){return{current_date:"",current_time:""}},created(){let t=this;this.$store.state.intervalList.push(setInterval((function(){t.getNow()}),1e3))},methods:{getNow(){let t=new Date,s=t.getFullYear(),e=("0"+(t.getMonth()+1)).substr(-2),a=("0"+t.getDate()).substr(-2);this.current_date=s+"-"+e+"-"+a;let i=("0"+t.getHours()).substr(-2),r=("0"+t.getMinutes()).substr(-2),o=("0"+t.getSeconds()).substr(-2);this.current_time=i+":"+r+":"+o}}}),c=l,d=e("2877"),n=Object(d["a"])(c,r,o,!1,null,null,null),_=n.exports;let b=null,h=null;var p={name:"page1",components:{"tools-bar":_},data(){return{top_list:[],equipment_total_cnt:0,equipment_data:[],equipment_error_cnt:0,bj_task_list:[],zp_task_list:[],order_list:[]}},mounted(){b=null,h=null,this.init()},methods:{init(){this.initSize(),this.$http.post("screen/index/page").then(t=>{this.equipment_total_cnt=t.et_total_cnt,this.equipment_data=t.et_data,t.equ_error_data&&(this.equipment_error_cnt=t.equ_error_data.total_cnt,this.createChartYctj(t.equ_error_data)),this.top_list=t.top_list,this.order_list=t.order_list,this.createChartLxzb()})},initSize(){$(".zh-table-box-content").height($(".box-card.list .box-card-body").height())},showLoading(){this.$refs.loading.showLoadingRainbow()},hideLoading(){this.$refs.loading.hideLoadingRainbow()},createChartLxzb(){b||(b=echarts.init(document.getElementById("chart_lxzb"))),b.setOption({color:["#be01ff","#36d967","#0600f1","#c0ff01","#e30cbd","#ff7301","#0ca7f7","#ff3101","#5b01ff","#17f5ed"],legend:{bottom:"5%",left:"center",textStyle:{color:"#FFFFFF"}},series:[{type:"pie",radius:["50%","70%"],center:["50%","40%"],label:{show:!1},labelLine:{show:!1},data:this.equipment_data}]})},createChartYctj(t){h||(h=echarts.init(document.getElementById("chart_yctj"))),h.setOption({grid:{top:10,right:20,left:100,bottom:30},xAxis:{type:"value",axisLabel:{show:!1},splitLine:{lineStyle:{color:"rgba(38, 196, 255, 0.2)",width:2}}},yAxis:{type:"category",inverse:!0,axisLabel:{fontSize:14,color:"#bcdbff",margin:15},axisTick:{show:!1},axisLine:{lineStyle:{color:"#acc9ec",width:2}},data:t.x_values},series:[{type:"bar",barWidth:13,itemStyle:{color:{type:"linear",x:0,y:0,x2:1,y2:0,colorStops:[{offset:0,color:"#721F1A"},{offset:.95,color:"#E01D0D"},{offset:1,color:"#FFC8C7"}],global:!1}},label:{show:!0,position:"right",fontSize:15,color:"#bcdbff"},z:2,data:t.data},{type:"bar",barWidth:9,barGap:"-90%",itemStyle:{color:"rgba(53, 155, 235, 0.2)"},z:1,data:[15,15,15,15,15,15,15,15]}]})}}},v=p,u=Object(d["a"])(v,a,i,!1,null,null,null);s["default"]=u.exports},7703:function(t,s,e){},"8a38":function(t,s,e){},c534:function(t,s,e){}}]);
//# sourceMappingURL=chunk-f44e8b04.ccba56d2.js.map