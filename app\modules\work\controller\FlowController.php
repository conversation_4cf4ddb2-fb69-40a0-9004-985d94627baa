<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Data\JsonData;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Work\Model\WorkData;
use Envsan\Modules\Work\Service\DataCommonService;
use Envsan\Modules\Work\Service\DataService;

class FlowController extends SuperController
{
    /**
     * @skipacl
     */
    public function printAction($uid = '')
    {
        $ds = new DataCommonService();
        $work_data = WorkData::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
        if ($work_data == false) {
            die('暂无审批流数据');
        }
        $ret = $ds->getFlowList($work_data);
        $this->view->data = json_encode($ret);
    }

    /**
     * @skipacl
     */
    public function printtypeAction($id = '',$type = '')
    {
        $ds = new DataCommonService();
        $work_data = WorkData::findFirst(['del_flag = 0 and form_data_type = ?1 and form_data_id = ?2','bind'=>[1=>$type,2=>$id]]);
        if ($work_data == false) {
            die('暂无审批流数据');
        }
        $ret = $ds->getFlowList($work_data);
        $this->view->data = json_encode($ret);
        $this->view->pick('flow/print');
    }

    /**
     * @skipacl
     */
    public function viewAction($type,$id){
        $work_datas = WorkData::find(['del_flag = 0 and form_data_type = ?1 and form_data_id = ?2','bind'=>[1=>$type,2=>$id]]);
        if (count($work_datas) == 0) {
            die('暂无审批流数据');
        }
        $s = new DataService();
        $list = [];
        foreach ($work_datas as $work_data){
            $w_data = $s->getWorkData($work_data);
            $list[] = $w_data->data;
        }
        $oss = new FileService();
        $base_path = $oss->getImagePath();
        $this->view->base_path = $base_path;
        $this->view->type = $type;
        $this->view->list = json_encode($list);
    }

    /**
     * @skipacl
     */
    public function updatetimeAction($id,$index){
        $work_data = WorkData::findFirst(['id = ?1','bind'=>[1=>$id]]);
        if ($work_data == false) {
            die('数据不正确');
        }
        $flow_list = json_decode($work_data->flow_list,true);
        foreach ($flow_list as $key => $item){
            if($key == $index){
                $view_date = substr($item['time'],0,10);
                $view_time = substr($item['time'],11);
            }
        }
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $s = new DataService();
            $ret->message = $s->updateTime($work_data,$index);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $this->view->view_date = $view_date;
        $this->view->view_time = $view_time;
        $this->view->r_id = $id;
        $this->view->index = $index;
    }
}