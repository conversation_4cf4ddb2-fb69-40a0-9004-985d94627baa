<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Data\SessionData;
use Envsan\Modules\Common\Service\TableService;
use Phalcon\Mvc\User\Component;

class StockService extends Component
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.uid,
                a.name,
                a.code,
                a.spec,
                a.model,
                a.deputy_unit,
                ifnull(s.name,\'\') as supplier_name,
                g.name as type_name,
                ifnull(t.quantity,0) as quantity
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 'a.supplier_id = s.id', 's')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a.type_id = g.id', 'g')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewStock', 'a.id = t.goods_id', 't')
            ->where('a.del_flag = 0 and a.owner = :owner:', ['owner'=>SessionData::ownerId()])
            ->orderBy('a.code');

        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function searchAll()
    {
        $name = $this->request->get('name', 'tstring');
        $data_type = $this->request->get('data_type', 'tstring');
        $batch_no = $this->request->get('batch_no', 'tstring');
        $date_start = $this->request->get('date_start', 'tstring');
        $date_end = $this->request->get('date_end', 'tstring');
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.id,
                a.batch_no,
                a.data_type,
                a.data_id,
                a.quantity,
                a.update_date,
                a.update_user,
                a.money,
                g.name,
                g.code,
                g.spec,
                g.model,
                g.unit
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseStockLogs', 'a')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods', 'a.goods_id = g.id', 'g')
            ->where('a.del_flag = 0 and a.owner = :owner:', ['owner'=>SessionData::ownerId()])
            ->orderBy('a.id desc');
        if (!empty($name)){
            $builder->andWhere("g.name like ?1 or g.code like ?1 or g.spec like ?1 or g.model like ?1", [1 => "%$name%"]);
        }
        if (!empty($data_type)){
            $builder->andWhere("a.data_type = ?3", [3 => $data_type]);
        }
        if (!empty($batch_no)){
            $builder->andWhere("a.batch_no like ?4", [4 => "%$batch_no%"]);
        }
        if (!empty($date_start)){
            $builder->andWhere("a.update_date >= ?5", [5 => $date_start . ' 00:00:00']);
        }
        if (!empty($date_end)){
            $builder->andWhere("a.update_date <= ?6", [6 => $date_end . ' 23:59:59']);
        }
        return $builder;
    }
}