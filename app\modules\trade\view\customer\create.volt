{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}



<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>客户名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" v-model="name" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>客户编码</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" maxlength="20" required>
                                </div>
                            </div>
                        </div>
                      <!-- <div class="col-xs-6">
                          <div class="form-group">
                              <label class="col-sm-4 control-label"><span class="required">*</span>存货档案分类</label>
                              <div class="col-sm-8">
                                  <div class="input-group">
                                      <input type="text" class="form-control" name="goods_type_text" v-model="goods_type_text" maxlength="20" required readonly>
                                      <span class="input-group-btn">
                                          <button type="button" class="btn btn-info" @click="showPopup">
                                              <i class="fa fa-window-restore"></i>选择
                                          </button>
                                      </span>
                                  </div>
                              </div>
                          </div>
                      </div> -->
                      <input type="hidden" name="goods_type_id" v-model="goods_type_id">
                        {{ partial('form') }}
                    </div>
                    <div class="form-actions right">
                        <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                        <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 添加弹出窗口的HTML结构 -->
    <div id="custom-popup" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title">双击选择产品档案</h4>
                </div>
                <div class="modal-body">
                    <div>
                        <div id="tree"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" @click="handleClose">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonCustomer }},
            goods_type_id: 0,
        },
        methods: {
            submit: function(e) {
                e.preventDefault();
                if (!$('#form').validate().form())
                    return false;

                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('trade/customer/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('trade/customer/create') }}';
                {% endif %}
                let param = JSON.parse(JSON.stringify(this.$data));
                param.ext_data = encodeURI(JSON.stringify(this.ext_data)).replace(/\+/g, '%2B');
                showSpin();
                $.post(url, param, function (rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!' + rs.message);
                    }
                }).error(function(rs) {
                    closeSpin();
                    console.log(rs);
                });
            },
            showPopup: function() {
                $('#custom-popup').modal('show');
            },
            handleClose() {
              $('#custom-popup').modal('hide');
            },
        }
    });

</script>
{{ partial('form_script') }}