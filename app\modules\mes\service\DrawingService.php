<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesDrawing;
use Envsan\Modules\Mes\Model\MesDrawingImage;
use Envsan\Modules\Mes\Model\MesDrawingVersion;
use Envsan\Modules\Mes\Model\MesOrderBom;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Util\Constant;
use Envsan\Modules\Sys\Model\SysDict;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Trade\Model\TradeOrderDetail;
use Envsan\Modules\Work\Service\DataCommonService;
use Phalcon\Mvc\User\Component;

class DrawingService extends Component
{
    public function selectAll()
    {
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.drawing_name,
                t1.version_code,
                t1.drawing_url,
                t1.status,
                t1.status_name,
                t1.remarks,
                t2.name as product_name,
                t2.code as product_code,
                t6.name as customer_name,
                t4.code as order_code,
                t5.real_name as upload_user
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesDrawing', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't1.product_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrderDetail', 't2.order_detail_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t3.order_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Sys\Model\User','t1.create_by = t5.id','t5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't4.customer_id = t6.id', 't6')
            ->where('t1.del_flag = 0 and t1.status > 10 and t1.owner = ?1 and t1.create_by = ?2', [1 => SessionData::ownerId() ,2 => $user->id])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function getVersionList($drawing_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.drawing_url,
                t1.drawing_name,
                t1.remarks,
                t1.upload_time,
                t1.upload_user,
                t1.status,
                t1.status_name,
                t1.reject_status,
                t1.reject_remarks,
                if(t2.version_id = t1.id, 1, 0) as use_status,
                t3.real_name as upload_user
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesDrawingVersion', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesDrawing', 't1.drawing_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.upload_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.drawing_id = ?1', [1 => $drawing_id])
            ->orderBy('t1.id desc');
        return $builder->getQuery()->execute();
    }

    public function selectById($id)
    {
        return MesDrawing::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return MesDrawing::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function selectVersionById($id)
    {
        return MesDrawingVersion::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectVersionByUid($uid)
    {
        return MesDrawingVersion::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function upload($row, $version = null)
    {
        $ret = [];
        $ret['status'] = 'error';
        $ret['refresh'] = 0;

        $code = $this->request->getPost('code', 'tstring');
        $drawing_name = $this->request->getPost('drawing_name', 'tstring');
        $drawing_url = $this->request->getPost('drawing_url', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        if (empty($code) || empty($drawing_name) || empty($drawing_url)) {
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }

        if (!empty($version) && $version->status != 10) {
            $ret['message'] = '版本数据状态无效';
            return $ret;
        }

        if ($this->isRepeat($code, $row->id, $version)){
            $ret['message'] = '版本号重复';
            return $ret;
        }

        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
            if (empty($version)) {
                $version = new MesDrawingVersion();
                $version->uid = UUID::make();
                $version->drawing_id = $row->id;
                $version->create_date = $now;
                $version->create_by = $user->id;
                $version->del_flag = 0;
                $version->group_id = $user->group_id;
                $version->owner = $user->owner;
            }

            if ($drawing_url != $version->drawing_url) {
                $version->drawing_images = null;
                $ret['refresh'] = 1;
            }

            $version->code = $code;
            $version->drawing_url = $drawing_url;
            $version->drawing_name = $drawing_name;
            $version->status = 20;
            $version->status_name = Constant::$drawing_version_status[$version->status];
            $version->remarks = CvtUtil::blankToNull($remarks);
            $version->upload_time = $now;
            $version->upload_by = $user->id;
            $version->update_date = $now;
            $version->update_by = $user->id;
            if (!$version->save()) {
                throw new \Exception("mes_drawing_version表更新失败");
            }

            if ($row->status != 30) {
                $row->status = 30;
                $row->status_name = Constant::$drawing_status[$row->status];
                $row->update_date = $now;
                $row->update_by = $user->id;
                if (!$row->save()) {
                    throw new \Exception("MesDrawing表更新失败");
                }
            }

            $dcm = new DataCommonService();
            $dcm->submitDesign($version->id, 3, $user->group_id);

            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $ret['message'] = $e->getMessage();
            return $ret;
        }

        $ret['status'] = 'ok';
        $ret['uid'] = $version->uid;
        return $ret;
    }

    private function isRepeat($code, $drawing_id, $version_row = null)
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Mes\Model\MesDrawingVersion')
            ->where('del_flag = 0 and drawing_id = ?1 and code = ?2', [1 => $drawing_id, 2 => $code]);

        if (!empty($version_row) && !CheckUtil::is_empty($version_row->id)) {
            $builder->andWhere("id <> ?3", [3 => $version_row->id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function passSave($id)
    {
        $version_row = $this->selectVersionById($id);
        if (empty($version_row) || $version_row->del_flag == 1) {
            throw new \Exception("版本记录已失效");
        } else if ($version_row->status != 20) {
            throw new \Exception("版本记录状态已变更");
        }

        $now = DateUtil::now();
        $user = SessionData::user();

        $version_row->status = 30;
        $version_row->status_name = Constant::$drawing_version_status[$version_row->status];
        $version_row->review_date = $now;
        $version_row->review_by = $user->id;
        $version_row->update_date = $now;
        $version_row->update_by = $user->id;
        if (!$version_row->save()) {
            throw new \Exception("【图纸变更申请-审核通过】mes_drawing_version表更新失败");
        }

        $row = $this->selectById($version_row->drawing_id);
        if (empty($row) || $row->del_flag == 1) {
            throw new \Exception("记录已失效");
        }

        $row->status = 40;
        $row->status_name = Constant::$drawing_status[$row->status];
        $row->version_id = $version_row->id;
        $row->version_code = $version_row->code;
        $row->drawing_url = $version_row->drawing_url;
        $row->drawing_name = $version_row->drawing_name;
        $row->drawing_images = $version_row->drawing_images;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()) {
            throw new \Exception("【图纸变更申请-审核通过】mes_drawing表更新失败");
        }
    }

    public function rejectSave($id, $reject_remarks)
    {
        $row = $this->selectVersionById($id);
        if (empty($row) || $row->del_flag == 1) {
            throw new \Exception("记录已失效");
        } else if ($row->status != 20) {
            throw new \Exception("记录状态已变更");
        }

        $now = DateUtil::now();
        $user = SessionData::user();

        $row->status = 10;
        $row->status_name = Constant::$drawing_version_status[$row->status];
        $row->reject_status = 1;
        $row->reject_remarks = $reject_remarks;
        $row->review_date = $now;
        $row->review_by = $user->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()) {
            throw new \Exception("【图纸变更申请-审核驳回】mes_drawing_version表更新失败");
        }
    }

    public function deleteVersion($row)
    {
        $version_id = $this->request->getPost('version_id', 'tstring');
        if (empty($version_id)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $version_row = MesDrawingVersion::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $version_id]]);
        if (empty($version_row)) {
            return '版本数据不存在';
        } else if ($version_row->status == 20) {
            return '版本审核中，不能删除';
        }

        if ($version_row->id == $row->version_id) {
            return '版本使用中，不能删除';
        }

        $version_row->del_flag = 1;
        $version_row->update_date = DateUtil::now();
        $version_row->update_by = SessionData::user()->id;
        return $version_row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function updateBom($row)
    {
        $version_uid = $this->request->getPost('uid', 'tstring');
        $bom_list = str_replace('%2B','+',urldecode($this->request->getPost('bom_list', ['string', 'trim'])));
        if (empty($bom_list) || empty($version_uid)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $version_row = $this->selectVersionByUid($version_uid);
        if (empty($version_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $bom_list = CvtUtil::emptyToArray($bom_list);
        $show_msg = false;
        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            foreach ($bom_list as $bom_row)
            {
                $pb_row = MesProductBom::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $bom_row['id']]]);
                if (empty($pb_row)) {
                    $show_msg = true;
                    throw new \Exception("工艺数据不存在");
                }
                if (count($bom_row['drawing_data']) == 0) {
                    $show_msg = true;
                    throw new \Exception("请选择".$pb_row->name."的图纸");
                }
                foreach ($bom_row['drawing_data'] as &$drawing_item){
                    $drawing_item['status'] = 1;
                }
                $pb_row->drawing_data = json_encode($bom_row['drawing_data'], JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
                $pb_row->update_date = $now;
                $pb_row->update_by = $user->id;
                if (!$pb_row->save()) {
                    throw new \Exception("【图纸管理-发布版本】mes_product_bom表更新失败");
                }
            }
            $row->uid = $version_row->uid;
            $row->status = 20;
            $row->status_name = Constant::$drawing_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【图纸管理-发布版本】mes_drawing表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            if ($show_msg) {
                return $e->getMessage();
            } else {
                Logger::error($e->getMessage());
                return ErrorHelper::UNKOWN;
            }
        }
        return '';
    }

    public function create($product_row)
    {
        $code = $this->request->getPost('code', 'tstring');
        $version_code = $this->request->getPost('version_code', 'tstring');
        $drawing_url = $this->request->getPost('drawing_url', 'tstring');
        $drawing_name = $this->request->getPost('drawing_name', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $rtn = new \stdClass();
        if (empty($version_code) || empty($drawing_url) || empty($code)) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $check_row = MesDrawing::findFirst(['del_flag = 0 and code = ?1','bind'=>[1=>$code]]);
        if (!empty($check_row)){
            $rtn->message = '图号重复';
            return $rtn;
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
            $row = new MesDrawing();
            $row->uid = UUID::make();
            $row->code = $code;
            $row->product_id = $product_row->id;
            $row->version_code = $version_code;
            $row->drawing_url = $drawing_url;
            $row->drawing_name = $drawing_name;
            $row->status = 10;
            $row->status_name = Constant::$drawing_status[$row->status];
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->create_date = $now;
            $row->create_by = $user->id;
            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->del_flag = 0;
            $row->owner = $user->owner;
            if (!$row->save()) {
                throw new \Exception("MesProduct表更新失败");
            }
            $rtn->uid = $row->uid;
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function getBomList($drawing_row)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns(' 
                t1.id,
                t1.uid,
                t1.name,
                t1.drawing_data,
                t2.name as model_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductBom', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductModel','t1.product_model_id = t2.id','t2')
            ->where('t1.del_flag = 0 and JSON_CONTAINS(t1.drawing_data,JSON_OBJECT(\'drawing_uid\', ?1)) and t1.product_id = ?2',
                [1 => $drawing_row->uid,2=>$drawing_row->product_id]);
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            $drawing_data = CvtUtil::emptyToArray($row['drawing_data']);
            foreach ($drawing_data as &$drawing_item){
                if ($drawing_item['drawing_uid'] == $drawing_row->uid){
                    $drawing_item['status'] = 0;
                }
            }
            $row['drawing_data'] = $drawing_data;
        }
        return $rows;
    }

    public function saveStatus()
    {
        $uid = $this->request->getPost('uid', 'tstring');
        $status_type = $this->request->getPost('status_type', 'tstring');
        if (empty($uid) || empty($status_type)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $row = $this->selectByUid($uid);
        if (empty($row) || $row->del_flag == 1) {
            return '图纸数据不存在';
        }

        if ($status_type != 2 && $status_type != 3) {
            return ErrorHelper::WRONG_INPUT;
        }

        if (($status_type == 2 && $row->status != 20)
            || ($status_type == 3 && $row->status != 30)) {
            return '图纸数据状态不正确';
        }

        $row->status = $status_type == 2 ? 30 : 20;
        $row->status_name = Constant::$drawing_status[$row->status];
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getSkillTypeList($product_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
               t1.skill_type_id as id,
               t1.skill_type_name as name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductProgress', 't1')
            ->where('t1.del_flag = 0 and product_id = ?1 and create_by = ?2',[1 => $product_id , 2=> SessionData::user()->id]);
        return $builder->getQuery()->execute();
    }

    public function saveBase64()
    {
        $ret = [];
        $ret['status'] = 'error';
        $uid = $this->request->getPost('uid', 'tstring');
        $num = $this->request->getPost('num', 'tstring');
        $img_base64 = $this->request->getPost('img_base64', 'tstring');
        if (empty($uid) || empty($num) || empty($img_base64)){
            $ret['message'] = ErrorHelper::WRONG_INPUT;
            return $ret;
        }
        $row = MesDrawingImage::findFirst(['del_flag = 0 and drawing_uid = ?1 and page_num = ?2','bind'=>[1=>$uid,2=>$num]]);
        if (!empty($row)) {
            $ret['status'] = 'ok';
            $ret['url'] = $row->url;
            return $ret;
        }
        $cs = new FileService();
        $img_base64 =  str_replace('data:image/jpeg;base64,', '', urldecode($img_base64));
        $filename = $cs->uploadBase64('drewingimage',$img_base64,'png');
        if (empty($filename)) {
            $ret['message'] = '图片上传失败';
            return $ret;
        }
        $row = new MesDrawingImage();
        $row->drawing_uid = $uid;
        $row->page_num = $num;
        $row->url = $filename;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if ($row->save()) {
            $ret['status'] = 'ok';
            $ret['url'] = $row->url;
            return $ret;
        }
        $ret['message'] = '图片保存失败';
        return $ret;
    }

    public function getPrintData($uid){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,t1.uid,t2.drawing_data,t3.code,t1.owner,t4.name_py,t5.code as product_code
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesOrderBom', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom', 't1.product_bom_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't2.product_id = t5.id', 't5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder', 't1.order_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't2.skill_user_id = t4.id', 't4')
            ->where('t1.del_flag = 0 and t1.uid = ?1', [1 => $uid])
            ->orderBy('t1.id desc');
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            return null;
        }
        $row = $rows[0]->toArray();
        $row['drawing_data'] = CvtUtil::emptyToArray( $row['drawing_data']);
        foreach ($row['drawing_data'] as &$drawing_item){
            $drawing_item['base64'] = '';
            $drawing_item['print_flag'] = 1;
        }
        return $row;
    }
}