/******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/App.vue?vue&type=template&id=7ba5bd90":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/App.vue?vue&type=template&id=7ba5bd90 ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    attrs: {
      "id": "app"
    }
  }, [_c('router-view')], 1);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./src/App.vue":
/*!*********************!*\
  !*** ./src/App.vue ***!
  \*********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _App_vue_vue_type_template_id_7ba5bd90__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./App.vue?vue&type=template&id=7ba5bd90 */ "./src/App.vue?vue&type=template&id=7ba5bd90");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");

var script = {}


/* normalize component */
;
var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_1__["default"])(
  script,
  _App_vue_vue_type_template_id_7ba5bd90__WEBPACK_IMPORTED_MODULE_0__.render,
  _App_vue_vue_type_template_id_7ba5bd90__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/App.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/App.vue?vue&type=template&id=7ba5bd90":
/*!***************************************************!*\
  !*** ./src/App.vue?vue&type=template&id=7ba5bd90 ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_App_vue_vue_type_template_id_7ba5bd90__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_App_vue_vue_type_template_id_7ba5bd90__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_App_vue_vue_type_template_id_7ba5bd90__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=template&id=7ba5bd90 */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/App.vue?vue&type=template&id=7ba5bd90");


/***/ }),

/***/ "./src/config.js":
/*!***********************!*\
  !*** ./src/config.js ***!
  \***********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  wss: 'ws://**************:9500/',
  host: "http://localhost:9999/"
});

/***/ }),

/***/ "./src/js/common.js":
/*!**************************!*\
  !*** ./src/js/common.js ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var element_ui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! element-ui */ "./node_modules/element-ui/lib/element-ui.common.js");
/* harmony import */ var element_ui__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(element_ui__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ __webpack_exports__["default"] = ({
  showLoading: msg => {
    if (!msg) {
      msg = '加载中';
    }
    return element_ui__WEBPACK_IMPORTED_MODULE_0__.Loading.service({
      lock: true,
      text: '',
      background: 'rgba(0, 0, 0, 0.3)'
    });
  },
  hideLoading: () => {
    // if ( this.loading != null){
    //     this.loading.close();
    // }
  },
  formatDate(date) {
    if (date == null || date == '') {
      return '';
    }
    if (!(date instanceof Date)) {
      return date;
    }
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return [year, month, day].map(this.formatNumber).join('-');
  },
  formatDateTime(date) {
    if (date == null || date == '') {
      return '';
    }
    if (!(date instanceof Date)) {
      return date;
    }
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();
    let d = [year, month, day].map(this.formatNumber).join('-');
    let t = [hour, minute, second].map(this.formatNumber).join(':');
    return d + ' ' + t;
  },
  formatMonth(date) {
    if (date == null || date == '') {
      return '';
    }
    if (!(date instanceof Date)) {
      return date;
    }
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    return [year, month].map(this.formatNumber).join('-');
  },
  formatNumber(n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  }
});

/***/ }),

/***/ "./src/js/global.js":
/*!**************************!*\
  !*** ./src/js/global.js ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  setItem(c_name, value) {
    localStorage.setItem(c_name, value);
  },
  getItem(c_name) {
    return localStorage.getItem(c_name);
  },
  getItemJson(c_name) {
    let item = localStorage.getItem(c_name);
    if (item) {
      try {
        return JSON.parse(item);
      } catch (e) {
        return null;
      }
    } else {
      return null;
    }
  }
});

/***/ }),

/***/ "./src/js/request.js":
/*!***************************!*\
  !*** ./src/js/request.js ***!
  \***************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config */ "./src/config.js");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ "./node_modules/axios/lib/axios.js");
/* harmony import */ var _common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./common */ "./src/js/common.js");
/* harmony import */ var element_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! element-ui */ "./node_modules/element-ui/lib/element-ui.common.js");
/* harmony import */ var element_ui__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(element_ui__WEBPACK_IMPORTED_MODULE_2__);





// 使用 import.meta.env 或 process.env 判断环境
const isDev =  false || "development" === 'development';
axios__WEBPACK_IMPORTED_MODULE_3__["default"].defaults.baseURL = isDev ? _config__WEBPACK_IMPORTED_MODULE_0__["default"].host + 'api/' // 开发环境直连后端
: `${window.location.origin}/api/`; // 生产环境用相对路径

// 配置axios允许携带cookie和session
axios__WEBPACK_IMPORTED_MODULE_3__["default"].defaults.withCredentials = true;

// 从cookie中获取SESSID并设置到请求头
function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
  return null;
}

// 添加请求拦截器，确保每次请求都带上最新的SID和自定义标志
axios__WEBPACK_IMPORTED_MODULE_3__["default"].interceptors.request.use(config => {
  const currentSessId = getCookie('SESSID');
  if (currentSessId) {
    config.headers.SID = currentSessId;
  }

  // 添加自定义标志头部
  config.headers['X-Custom-Flag'] = 'noapi';
  return config;
}, error => {
  return Promise.reject(error);
});
let app = null;
let loading_msg = '';
function setLoadingMsg(msg) {
  loading_msg = msg;
}
function get(url, params, hide_loading) {
  let loading = null;
  if (!hide_loading) {
    loading = _common__WEBPACK_IMPORTED_MODULE_1__["default"].showLoading(loading_msg);
  }
  setLoadingMsg('');
  if (!params) {
    params = {};
  }
  return new Promise((resolve, reject) => {
    axios__WEBPACK_IMPORTED_MODULE_3__["default"].get(url, {
      params: params
    }).then(rs => {
      if (loading != null) {
        loading.close();
      }
      resolve(rs);
    }).catch(error => {
      if (loading != null) {
        loading.close();
      }
      reject(error);
    });
  });
}
function post(url, params, hide_loading) {
  if (app == null) app = window.VueApp;
  let loading = null;
  if (!hide_loading) {
    loading = _common__WEBPACK_IMPORTED_MODULE_1__["default"].showLoading(loading_msg);
  }
  setLoadingMsg('');
  return new Promise((resolve, reject) => {
    axios__WEBPACK_IMPORTED_MODULE_3__["default"].post(url, params).then(rs => {
      if (loading != null) {
        loading.close();
      }
      if (rs.status == 200) {
        resolve(rs.data);
      } else {
        reject('网络错误！');
      }
    }).catch(error => {
      if (loading != null) {
        loading.close();
      }
      let rs = error.response;
      if (!rs) {
        reject(error);
        return;
      }
      if (rs.statusText === '401.1:') {
        element_ui__WEBPACK_IMPORTED_MODULE_2__.Notification.error({
          title: '错误',
          message: '登录超时'
        });
        app.$router.replace({
          name: 'error'
        });
      } else if (rs.statusText === '401.3:') {
        element_ui__WEBPACK_IMPORTED_MODULE_2__.Notification.error({
          title: '错误',
          message: '无权限'
        });
        app.$router.replace({
          name: 'error'
        });
      } else {
        reject(error);
      }
    });
  });
}
/* harmony default export */ __webpack_exports__["default"] = ({
  setLoadingMsg,
  get,
  post,
  get_only: (url, params) => {
    return get(url, params, 1);
  },
  post_only: (url, params) => {
    return post(url, params, 1);
  }
});

/***/ }),

/***/ "./src/main.js":
/*!*********************!*\
  !*** ./src/main.js ***!
  \*********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! vue */ "./node_modules/vue/dist/vue.runtime.esm.js");
/* harmony import */ var _App_vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./App.vue */ "./src/App.vue");
/* harmony import */ var element_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! element-ui */ "./node_modules/element-ui/lib/element-ui.common.js");
/* harmony import */ var element_ui__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(element_ui__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var element_ui_lib_theme_chalk_index_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! element-ui/lib/theme-chalk/index.css */ "./node_modules/element-ui/lib/theme-chalk/index.css");
/* harmony import */ var element_ui_lib_theme_chalk_index_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(element_ui_lib_theme_chalk_index_css__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./router */ "./src/router.js");
/* harmony import */ var _js_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./js/common */ "./src/js/common.js");
/* harmony import */ var _js_request__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./js/request */ "./src/js/request.js");
/* harmony import */ var _js_global__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./js/global */ "./src/js/global.js");
/* harmony import */ var bootstrap_dist_css_bootstrap_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.css */ "./node_modules/bootstrap/dist/css/bootstrap.css");
/* harmony import */ var bootstrap_dist_css_bootstrap_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(bootstrap_dist_css_bootstrap_css__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var font_awesome_less_font_awesome_less__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! font-awesome/less/font-awesome.less */ "./node_modules/font-awesome/less/font-awesome.less");
/* harmony import */ var font_awesome_less_font_awesome_less__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(font_awesome_less_font_awesome_less__WEBPACK_IMPORTED_MODULE_8__);










vue__WEBPACK_IMPORTED_MODULE_9__["default"].config.productionTip = false;
vue__WEBPACK_IMPORTED_MODULE_9__["default"].prototype.$http = _js_request__WEBPACK_IMPORTED_MODULE_5__["default"];
vue__WEBPACK_IMPORTED_MODULE_9__["default"].prototype.$cjs = _js_common__WEBPACK_IMPORTED_MODULE_4__["default"];
vue__WEBPACK_IMPORTED_MODULE_9__["default"].prototype.$global = _js_global__WEBPACK_IMPORTED_MODULE_6__["default"];
vue__WEBPACK_IMPORTED_MODULE_9__["default"].prototype.$hub = new vue__WEBPACK_IMPORTED_MODULE_9__["default"]();
vue__WEBPACK_IMPORTED_MODULE_9__["default"].use((element_ui__WEBPACK_IMPORTED_MODULE_1___default()));
new vue__WEBPACK_IMPORTED_MODULE_9__["default"]({
  router: _router__WEBPACK_IMPORTED_MODULE_3__.router,
  render: h => h(_App_vue__WEBPACK_IMPORTED_MODULE_0__["default"])
}).$mount("#app");

/***/ }),

/***/ "./src/router.js":
/*!***********************!*\
  !*** ./src/router.js ***!
  \***********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   router: function() { return /* binding */ router; }
/* harmony export */ });
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! vue */ "./node_modules/vue/dist/vue.runtime.esm.js");
/* harmony import */ var vue_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vue-router */ "./node_modules/vue-router/dist/vue-router.esm.js");


vue__WEBPACK_IMPORTED_MODULE_0__["default"].use(vue_router__WEBPACK_IMPORTED_MODULE_1__["default"]);
const routes = [{
  path: "*",
  redirect: "/yikecheck"
}, {
  name: "error",
  component: () => __webpack_require__.e(/*! import() */ "src_view_error_index_vue").then(__webpack_require__.bind(__webpack_require__, /*! ./view/error */ "./src/view/error/index.vue")),
  meta: {
    title: "异常"
  }
}, {
  name: "process",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_vuedraggable_dist_vuedraggable_umd_js"), __webpack_require__.e("src_view_process_plan_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/process/plan */ "./src/view/process/plan.vue")),
  meta: {
    title: "排产"
  }
}, {
  name: "plan",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("node_modules_vuedraggable_dist_vuedraggable_umd_js"), __webpack_require__.e("src_view_plan_index_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/plan/index */ "./src/view/plan/index.vue")),
  meta: {
    title: "排产"
  }
}, {
  name: "mingjingplan",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("node_modules_vuedraggable_dist_vuedraggable_umd_js"), __webpack_require__.e("node_modules_core-js_internals_get-iterator-direct_js-node_modules_core-js_internals_iterate_-c8ca10"), __webpack_require__.e("src_view_mingjing_plan_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/mingjing/plan */ "./src/view/mingjing/plan.vue")),
  meta: {
    title: "排产"
  }
}, {
  name: "mingjingplanview",
  component: () => __webpack_require__.e(/*! import() */ "src_view_mingjing_view_vue").then(__webpack_require__.bind(__webpack_require__, /*! ./view/mingjing/view */ "./src/view/mingjing/view.vue")),
  meta: {
    title: "排产"
  }
}, {
  name: "hot",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_vuedraggable_dist_vuedraggable_umd_js"), __webpack_require__.e("src_view_hot_plan_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/hot/plan */ "./src/view/hot/plan.vue")),
  meta: {
    title: "排产"
  }
}, {
  name: "castplan",
  component: () => __webpack_require__.e(/*! import() */ "src_view_cast_plan_vue").then(__webpack_require__.bind(__webpack_require__, /*! ./view/cast/plan */ "./src/view/cast/plan.vue")),
  meta: {
    title: "流程"
  }
}, {
  name: "ipc",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("src_view_ipc_index_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/ipc/index */ "./src/view/ipc/index.vue")),
  meta: {
    title: "报工"
  }
}, {
  name: "document",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("node_modules_vuedraggable_dist_vuedraggable_umd_js"), __webpack_require__.e("node_modules_core-js_internals_get-iterator-direct_js-node_modules_core-js_internals_iterate_-c8ca10"), __webpack_require__.e("src_view_document_index_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/document/index */ "./src/view/document/index.vue")),
  meta: {
    title: "生成文档"
  }
}, {
  name: "print",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("src_view_document_print_item_vue"), __webpack_require__.e("src_view_document_print_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/document/print */ "./src/view/document/print.vue")),
  meta: {
    title: "打印"
  }
}, {
  name: "printlist",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("src_view_document_print_item_vue"), __webpack_require__.e("src_view_document_list_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/document/list */ "./src/view/document/list.vue")),
  meta: {
    title: "打印"
  }
}, {
  name: "pdf",
  component: () => __webpack_require__.e(/*! import() */ "src_view_pdf_index_vue").then(__webpack_require__.bind(__webpack_require__, /*! ./view/pdf/index */ "./src/view/pdf/index.vue")),
  meta: {
    title: "打印"
  }
}, {
  name: "yike",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("node_modules_socket_io-client_build_esm_index_js"), __webpack_require__.e("src_view_yike_index_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/yike/index */ "./src/view/yike/index.vue")),
  meta: {
    title: "涂覆"
  }
}, {
  name: "yikeproduce",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_socket_io-client_build_esm_index_js"), __webpack_require__.e("src_view_yike_produce_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/yike/produce */ "./src/view/yike/produce.vue")),
  meta: {
    title: "分条"
  }
}, {
  name: "yikecheck",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_socket_io-client_build_esm_index_js"), __webpack_require__.e("src_view_yike_check_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/yike/check */ "./src/view/yike/check.vue")),
  meta: {
    title: "分条检测"
  }
}, {
  name: "mingjingchecklist",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("src_view_mingjing_check_list_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/mingjing/check_list */ "./src/view/mingjing/check_list.vue")),
  meta: {
    title: "原材料检验列表"
  }
}, {
  name: "mingjingcheckdetail",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("src_components_QualityField_vue-node_modules_qs_lib_index_js"), __webpack_require__.e("src_view_mingjing_check_detail_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/mingjing/check_detail */ "./src/view/mingjing/check_detail.vue")),
  meta: {
    title: "原材料检验详情"
  }
}, {
  name: "mingjingproducescan",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("node_modules_core-js_internals_get-iterator-direct_js-node_modules_core-js_internals_iterate_-c8ca10"), __webpack_require__.e("src_view_mingjing_produce_scan_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/mingjing/produce_scan */ "./src/view/mingjing/produce_scan.vue")),
  meta: {
    title: "产品检验扫描"
  }
}, {
  name: "mingjingproducedetail",
  component: () => Promise.all(/*! import() */[__webpack_require__.e("node_modules_core-js_modules_es_array_push_js"), __webpack_require__.e("node_modules_core-js_internals_get-iterator-direct_js-node_modules_core-js_internals_iterate_-c8ca10"), __webpack_require__.e("src_components_QualityField_vue-node_modules_qs_lib_index_js"), __webpack_require__.e("src_view_mingjing_produce_detail_vue")]).then(__webpack_require__.bind(__webpack_require__, /*! ./view/mingjing/produce_detail */ "./src/view/mingjing/produce_detail.vue")),
  meta: {
    title: "产品检验详情"
  }
}];

// add route path
routes.forEach(route => {
  route.path = route.path || "/" + (route.name || "");
});
const router = new vue_router__WEBPACK_IMPORTED_MODULE_1__["default"]({
  routes
});


/***/ }),

/***/ "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==":
/*!**********************************************************************************************************************************************!*\
  !*** data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg== ***!
  \**********************************************************************************************************************************************/
/***/ (function(module) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==";

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/amd options */
/******/ 	!function() {
/******/ 		__webpack_require__.amdO = {};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	!function() {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = function(result, chunkIds, fn, priority) {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var chunkIds = deferred[i][0];
/******/ 				var fn = deferred[i][1];
/******/ 				var priority = deferred[i][2];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	!function() {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = function(module) {
/******/ 			var getter = module && module.__esModule ?
/******/ 				function() { return module['default']; } :
/******/ 				function() { return module; };
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/ensure chunk */
/******/ 	!function() {
/******/ 		__webpack_require__.f = {};
/******/ 		// This file contains only the entry chunk.
/******/ 		// The chunk loading function for additional chunks
/******/ 		__webpack_require__.e = function(chunkId) {
/******/ 			return Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {
/******/ 				__webpack_require__.f[key](chunkId, promises);
/******/ 				return promises;
/******/ 			}, []));
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/get javascript chunk filename */
/******/ 	!function() {
/******/ 		// This function allow to reference async chunks
/******/ 		__webpack_require__.u = function(chunkId) {
/******/ 			// return url for filenames based on template
/******/ 			return "js/" + chunkId + "." + {"src_view_error_index_vue":"b5b7fa5f","node_modules_vuedraggable_dist_vuedraggable_umd_js":"abbe812f","src_view_process_plan_vue":"d8233fb9","node_modules_core-js_modules_es_array_push_js":"1a233f61","src_view_plan_index_vue":"221338b9","node_modules_core-js_internals_get-iterator-direct_js-node_modules_core-js_internals_iterate_-c8ca10":"66d3ca84","src_view_mingjing_plan_vue":"4fb35df6","src_view_mingjing_view_vue":"24d6d75a","src_view_hot_plan_vue":"f7dfee54","src_view_cast_plan_vue":"9cf8384a","src_view_ipc_index_vue":"9f12f5d4","src_view_document_index_vue":"131a4df5","src_view_document_print_item_vue":"750c0ccc","src_view_document_print_vue":"5863ae66","src_view_document_list_vue":"4dfa0ce4","src_view_pdf_index_vue":"478aec1a","node_modules_socket_io-client_build_esm_index_js":"28e78b40","src_view_yike_index_vue":"8ff9b0f7","src_view_yike_produce_vue":"8f34c9c7","src_view_yike_check_vue":"6a5890bf","src_view_mingjing_check_list_vue":"55ec849a","src_components_QualityField_vue-node_modules_qs_lib_index_js":"ab534561","src_view_mingjing_check_detail_vue":"9fa69c3c","src_view_mingjing_produce_scan_vue":"3645ebe2","src_view_mingjing_produce_detail_vue":"3bc6dc3a"}[chunkId] + ".js";
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	!function() {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/load script */
/******/ 	!function() {
/******/ 		var inProgress = {};
/******/ 		var dataWebpackPrefix = "sfp_ext:";
/******/ 		// loadScript function to load a script via script tag
/******/ 		__webpack_require__.l = function(url, done, key, chunkId) {
/******/ 			if(inProgress[url]) { inProgress[url].push(done); return; }
/******/ 			var script, needAttach;
/******/ 			if(key !== undefined) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				for(var i = 0; i < scripts.length; i++) {
/******/ 					var s = scripts[i];
/******/ 					if(s.getAttribute("src") == url || s.getAttribute("data-webpack") == dataWebpackPrefix + key) { script = s; break; }
/******/ 				}
/******/ 			}
/******/ 			if(!script) {
/******/ 				needAttach = true;
/******/ 				script = document.createElement('script');
/******/ 		
/******/ 				script.charset = 'utf-8';
/******/ 				script.timeout = 120;
/******/ 				if (__webpack_require__.nc) {
/******/ 					script.setAttribute("nonce", __webpack_require__.nc);
/******/ 				}
/******/ 				script.setAttribute("data-webpack", dataWebpackPrefix + key);
/******/ 		
/******/ 				script.src = url;
/******/ 			}
/******/ 			inProgress[url] = [done];
/******/ 			var onScriptComplete = function(prev, event) {
/******/ 				// avoid mem leaks in IE.
/******/ 				script.onerror = script.onload = null;
/******/ 				clearTimeout(timeout);
/******/ 				var doneFns = inProgress[url];
/******/ 				delete inProgress[url];
/******/ 				script.parentNode && script.parentNode.removeChild(script);
/******/ 				doneFns && doneFns.forEach(function(fn) { return fn(event); });
/******/ 				if(prev) return prev(event);
/******/ 			}
/******/ 			var timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);
/******/ 			script.onerror = onScriptComplete.bind(null, script.onerror);
/******/ 			script.onload = onScriptComplete.bind(null, script.onload);
/******/ 			needAttach && document.head.appendChild(script);
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/node module decorator */
/******/ 	!function() {
/******/ 		__webpack_require__.nmd = function(module) {
/******/ 			module.paths = [];
/******/ 			if (!module.children) module.children = [];
/******/ 			return module;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/publicPath */
/******/ 	!function() {
/******/ 		__webpack_require__.p = "";
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	!function() {
/******/ 		__webpack_require__.b = document.baseURI || self.location.href;
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			"app": 0
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.f.j = function(chunkId, promises) {
/******/ 				// JSONP chunk loading for javascript
/******/ 				var installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;
/******/ 				if(installedChunkData !== 0) { // 0 means "already installed".
/******/ 		
/******/ 					// a Promise means "currently loading".
/******/ 					if(installedChunkData) {
/******/ 						promises.push(installedChunkData[2]);
/******/ 					} else {
/******/ 						if(true) { // all chunks have JS
/******/ 							// setup Promise in chunk cache
/******/ 							var promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });
/******/ 							promises.push(installedChunkData[2] = promise);
/******/ 		
/******/ 							// start chunk loading
/******/ 							var url = __webpack_require__.p + __webpack_require__.u(chunkId);
/******/ 							// create error before stack unwound to get useful stacktrace later
/******/ 							var error = new Error();
/******/ 							var loadingEnded = function(event) {
/******/ 								if(__webpack_require__.o(installedChunks, chunkId)) {
/******/ 									installedChunkData = installedChunks[chunkId];
/******/ 									if(installedChunkData !== 0) installedChunks[chunkId] = undefined;
/******/ 									if(installedChunkData) {
/******/ 										var errorType = event && (event.type === 'load' ? 'missing' : event.type);
/******/ 										var realSrc = event && event.target && event.target.src;
/******/ 										error.message = 'Loading chunk ' + chunkId + ' failed.\n(' + errorType + ': ' + realSrc + ')';
/******/ 										error.name = 'ChunkLoadError';
/******/ 										error.type = errorType;
/******/ 										error.request = realSrc;
/******/ 										installedChunkData[1](error);
/******/ 									}
/******/ 								}
/******/ 							};
/******/ 							__webpack_require__.l(url, loadingEnded, "chunk-" + chunkId, chunkId);
/******/ 						}
/******/ 					}
/******/ 				}
/******/ 		};
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = function(parentChunkLoadingFunction, data) {
/******/ 			var chunkIds = data[0];
/******/ 			var moreModules = data[1];
/******/ 			var runtime = data[2];
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = self["webpackChunksfp_ext"] = self["webpackChunksfp_ext"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, ["chunk-vendors"], function() { return __webpack_require__("./src/main.js"); })
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	
/******/ })()
;
//# sourceMappingURL=app.2347f9b9.js.map