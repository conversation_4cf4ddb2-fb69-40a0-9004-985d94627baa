<?php

namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Service\GroupService;

/**
 * @name('组织管理')
 */
class GroupController extends SuperController
{

    /**
     * @name('列表')
     */
    public function listAction()
    {
        $gs = new GroupService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->status = JsonData::STATUS_OK;
            $ret->data = $gs->selectTree(0);
            return json_encode($ret);
        }

        $this->view->jsonTree = $gs->selectTree(0);
    }

    /**
     * @acl({'link':'sys:group:list'})
     */
    public function createAction()
    {
        $gs = new GroupService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $gs = new GroupService();
            $ret = new JsonData();
            $ret->message = $gs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $this->view->jsonTree = $gs->selectTree(0, true);
        $jrow = (new Group())->toArray();
        $jrow['pname'] = '';
        $jrow['type'] = '';
        $jrow['cname'] = '';
        $jrow['isCreated'] = true;
        $this->view->jsonGroup = json_encode($jrow);
        $this->view->code = '';
        $this->view->types = ConstantUtil::$group_types;
    }

    /**
     * @acl({'link':'sys:group:list'})
     */
    function editAction($uid = 0)
    {
        $gs = new GroupService();
        $row = $gs->selectByUid($uid);
        if ($row == null) {
            die(ErrorHelper::WRONG_ID);
        }
        else if ($row->pid != 0) {
            // 编辑的时候要选出父组织的名称
            $prow = $gs->selectById($row->pid);
            if ($prow != null)
                $row->pname = $prow->name;
        }

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $gs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $this->view->pick('group/create');
        $this->view->jsonTree = $gs->selectTree($row->id, true);
        $this->view->group = $row;
        $jrow = $row->toArray();
        $jrow['pname'] = (isset($row->pname) ? $row->pname : '');
        $jrow['isCreated'] = true;
        $jrow['type'] = CvtUtil::nullToBlank($jrow['type']);
        $this->view->jsonGroup = json_encode($jrow);
        $this->view->code = $row->code;
        $this->view->types = ConstantUtil::$group_types;
    }

    /**
     * @acl({'link':'sys:group:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $gs = new GroupService();
            $msg = $gs->deleteByUid($this->request->getPost('uid', 'string'));
            if ($msg == '')
                $ret->status = JsonData::STATUS_OK;
            else
                $ret->message = $msg;
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function switchAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $s = new GroupService();
            $ret->message = $s->switchGroup();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $gs = new GroupService();
        $this->view->jsonTree = $gs->selectTree();

        $this->view->group = $gs->selectById(SessionData::groupId());
    }

    /**
     * @skipacl
     */
    public function selAction()
    {
        $gs = new GroupService();
        $this->view->jsonTree = $gs->selectTree();
    }

    /**
     * @skipacl
     */
    public function selMultipleAction($ids)
    {
        $gs = new GroupService();
        $this->view->jsonTree = $gs->selectTree(0,true,$ids);
        $this->view->pick('group/sel-multiple');
    }

    /**
     * @noacl
     */
    public function barcodeAction($path)
    {
        $s = new GroupService();
        $s->createBarCodeImg($path);
        return "完成";
    }
}