{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">审批类型<span class="required">*</span>：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="name" v-model="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6" v-show="form_id == '' || form_id == null">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">业务类型：</label>
                            <div class="col-sm-8">
                                <select name="doc_type" class="form-control bs-select" v-model="doc_type" data-size="8" data-live-search="true">
                                    <option value="">请选择</option>
                                    {% for key, value in types %}
                                        <option value="{{ key }}">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group " v-if="doc_type == ''">
                            <label class="col-sm-4 control-label">
                                表单
                            </label>
                            <div class="col-sm-8">
                                <select name="form_id" v-model="form_id" class="form-control bs-select">
                                    <option value="">请选择</option>
                                    {% for row in formList %}
                                        <option value="{{ row.id }}">{{row.name}}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group ">
                            <label class="col-sm-4 control-label">
                                使用组织<span class="required"> * </span>
                            </label>
                            <div class="col-sm-8">
                                <select name="group_id" v-model="group_id" class="form-control bs-select" required>
                                    <option value="">请选择</option>
                                    {% for row in groupList %}
                                        <option value="{{ row.id }}">{{row.name}}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">状态</label>
                            <div class="col-sm-8">
                                <select name="status" class="bs-select form-control" v-model="status" required>
                                    <option value="1">启用</option>
                                    <option value="0">停用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">摘要内容</label>
                            <div class="col-sm-10">
                                <select name="abstrakt_keys" v-model="abstrakt_keys" class="bs-select form-control" multiple data-size="8">
                                    <option v-for="(item,index) in form_list" :value="item.key">${ item.name }</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 15px">
                        <label class="control-label" style="font-size: 16px;color: #000">设置审批节点隐藏项目</label>
                    </div>
                    <div class="col-sm-12" v-for="hidden_item,hidden_idx in hidden_list">
                        <div class="form-group">
                            <label class="col-sm-2 control-label" v-text="hidden_item.name"></label>
                            <div class="col-sm-10">
                                <select name="hidden_item_keys" v-model="hidden_item.keys" class="bs-select form-control" multiple data-size="8">
                                    <option v-for="(item,index) in form_list" :value="item.key">${ item.name }</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>

</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{
            id:'{{ id }}',
            name:'{{ name }}',
            doc_type:'{{ doc_type }}',
            status:'{{ status }}',
            group_id:'{{ group_id }}',
            form_id:'{{ form_id }}',
            flow_ids:{{ flow_ids }},
            flow_error_ids:{{ flow_error_ids }},
            flow_more_ids:{{ flow_more_ids }},
            data_status:{{ data_status }},
            status_value:'',
            form_list : {{ form_list }},
            abstrakt_keys: {{ abstrakt_keys }},
            hidden_list: {{ hidden_list }}
        },
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                var url= '{{ url('work/design/bind/')~id }}';
                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            },
            addSelect(){
                if (this.status_value == ''){
                    return;
                }
                for(let item of this.data_status){
                    if (this.status_value == item){
                        toastr.error('重复添加!');
                        return;
                    }
                }
                this.data_status.push(this.status_value);
                this.status_value = '';
            },
            delSelect(idx){
                this.data_status.splice(idx,1);
            }
        },
        watch:{
            doc_type:function (val){
                if (val == null || val == ''){
                    app.form_list = [];
                    app.$nextTick(function() {
                        $(".bs-select").selectpicker('refresh');
                    });
                    return;
                }
                showSpin();
                $.post("{{ url('work/design/getform/')  }}" + val, function (rs) {
                    closeSpin(null);
                    app.form_list = rs;
                    app.$nextTick(function() {
                        $(".bs-select").selectpicker('refresh');
                    });
                })
            }
        }
    });

    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);
</script>

<style>
    .factory-table {
        margin-bottom: 0;
        width: 100%;
        margin-top: 10px
    }

    .factory-table > thead > tr > th, .factory-table > tbody > tr > td {
        border: 1px solid #F2F2F2;
        height: 35px;
        padding-left: 5px ;
        background-color: #ffffff;
    }

    .factory-table > thead > tr > th {
        font-weight: normal;
        background-color: #3598DC;
    }
</style>
