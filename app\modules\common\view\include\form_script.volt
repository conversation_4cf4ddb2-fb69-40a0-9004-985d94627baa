<script>
    $('.dtpicker-ext').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: true,
        autoclose: true,
        format:'yyyy-mm-dd'
    }).on('changeDate', function(ev) {
        app.{{ extDataName }}[$(this).attr('name')]['value'] = $(this).val();
    });

    $('.dtpicker-month-ext').datetimepicker(
        {
            language: "zh-CN",
            startView: 3,
            minView: 3,
            todayBtn: false,
            autoclose: true,
            format: 'yyyy-mm',
            pickerPosition: 'bottom-left',
            fontAwesome: true
        }
    ).on('changeDate', function(ev) {
        app.{{ extDataName }}[$(this).attr('name')]['value'] = $(this).val();
    });

    $('.dtpicker-time-ext').datetimepicker(
        {
            language: "zh-C<PERSON>",
            startView: 1,
            minView: 1,
            maxView: 1,
            todayBtn: false,
            autoclose: true,
            format: 'hh:ii',
            pickerPosition: 'bottom-left',
            fontAwesome: true,
            showSecond : true,
            showHours : true
        }
    ).on('changeDate', function(ev) {
        app.{{ extDataName }}[$(this).attr('name')]['value'] = $(this).val()+':00';
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>