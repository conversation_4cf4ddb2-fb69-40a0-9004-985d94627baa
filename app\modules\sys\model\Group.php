<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\ModelUtil;
use Phalcon\Mvc\Model\Behavior\SoftDelete;
use Envsan\Common\Model\BaseModel;

class Group extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $pid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $short_name;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=false)
     */
    public $code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=false)
     */
    public $type;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $update_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=11, nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_group';
    }

    public static function find($params = null)
    {
        return parent::find(ModelUtil::build($params));
    }

    public static function findFirst($params = null)
    {
        return parent::findFirst(ModelUtil::build($params));
    }

    public static function findFirstDirect($params = null)
    {
        return parent::findFirst($params);
    }

    public function initialize()
    {
        $this->hasMany('id', 'Envsan\Modules\Sys\Model\User', 'group_id', array('alias' => 'User'));

        ModelUtil::softDelete($this);
    }

    public function beforeDelete()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }

    public function beforeSave()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }
}
