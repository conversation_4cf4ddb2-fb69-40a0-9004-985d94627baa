<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesViewProductBomCnt extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $bom_cnt;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_view_product_bom_cnt';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewProductBomCnt[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewProductBomCnt
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
