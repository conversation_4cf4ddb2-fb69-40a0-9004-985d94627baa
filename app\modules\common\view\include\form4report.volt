<div class="col-sm-{{ extDataCnt }}"  v-for="ext_item,ext_key in {{extDataName}}">
    <div class="form-group">
        <label class="col-sm-4 control-label">
            <span v-if="ext_item.required == 1" class="required">*</span>
            <span v-text="ext_item.title">*</span>
        </label>
        <div class="col-sm-8">
            <input v-if="ext_item.type == 1" :placeholder="ext_item.explain" type="text" class="form-control" :name="ext_key" v-model="ext_item.value" :maxlength="ext_item.max" :required="ext_item.required == 1 ? true : false">
            <div v-if="ext_item.type == 2" class="input-group">
                <input type="number" :placeholder="ext_item.explain" class="form-control" number="true" :name="ext_key" v-model="ext_item.value" :maxlength="ext_item.max" :required="ext_item.required == 1 ? true : false">
                <span class="input-group-addon" v-text="ext_item.unit"></span>
            </div>
            <select v-if="ext_item.type == 3" class="bs-select form-control" :name="ext_key" v-model="ext_item.value" :required="ext_item.required == 1 ? true : false" data-live-search="true" data-size="8">
                <option value="" v-text="'请选择'+(ext_item.explain == null ? '' : ext_item.explain)"></option>
                <option v-for="(val,idx) in ext_item.list" :value="val" v-text="val"></option>
            </select>
            <select v-if="ext_item.type == 4" class="bs-select form-control" :name="ext_key" v-model="ext_item.values" :required="ext_item.required == 1 ? true : false" data-live-search="true" data-size="8" multiple>
                <option v-for="(val,idx) in ext_item.list" :value="val" v-text="val"></option>
            </select>
            <textarea v-if="ext_item.type == 5" style="resize: none;" :placeholder="ext_item.explain" class="form-control" rows="3" :name="ext_key" v-model="ext_item.value" :maxlength="ext_item.max" :required="ext_item.required == 1 ? true : false"></textarea>
            <div v-if="ext_item.type == 6" class="input-group">
                <input type="text" class="form-control date dtpicker-ext" :placeholder="ext_item.explain" :name="ext_key" v-model="ext_item.value" :required="ext_item.required == 1 ? true : false"/>
                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
            </div>
            <div v-if="ext_item.type == 7" class="input-group">
                <input type="text" class="form-control date dtpicker-time-ext" :placeholder="ext_item.explain" :name="ext_key" v-model="ext_item.value" :required="ext_item.required == 1 ? true : false"/>
                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
            </div>
            <div v-if="ext_item.type == 8" class="input-group">
                <input type="text" class="form-control date dtpicker-month-ext" :placeholder="ext_item.explain" :name="ext_key" v-model="ext_item.value" :required="ext_item.required == 1 ? true : false"/>
                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
            </div>
        </div>
    </div>
</div>