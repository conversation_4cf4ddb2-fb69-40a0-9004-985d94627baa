<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-xs-6">
            <div class="portlet light portlet-tree" style="margin-bottom: 0;">
                <div class="portlet-body form" style="overflow-y: auto;">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">请选择上级部门
                        </label>
                        <div class="col-sm-8">
                            <div id="tree"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal form-validate" autocomplete="off">
                        <div class="form-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required"> * </span>已选择的上级部门</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="pname" v-model="pname" readonly required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required"> * </span>分类编码</label>
                                        <div class="col-sm-8">
                                            <div style="display: flex;flex-direction: row">
                                                <input style="width: 50%" type="text" class="form-control" name="p_code" v-model="p_code" readonly>
                                                <input style="width: 50%" type="text" class="form-control" name="code" v-model="code" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required"> * </span>名称</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="name" v-model="name" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required"> * </span>缩写</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="short_name" v-model="short_name" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i>取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonGoodsType }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                showSpin();
                $.post('{{ url('purchase/goodstype/create') }}', app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });

    var $tree = $('#tree');
    $(function () {
        initSize();

        $tree.jstree({
            "core": {"data": {{ jsonTree }}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            app.pname = data.node.text;
            app.pid = data.node.id;
            showSpin();
            $.post('{{ url('purchase/goodstype/takecode') }}', {id:data.node.id}, function (rs) {
                closeSpin(null);
                if(rs.status=='ok'){
                    app.p_code = rs.data.p_code;
                    app.code = rs.data.code;
                }
                else {
                    toastr.error('操作失败!'+rs.message);
                }
            })
        });
    });

    function initSize() {
        $(".portlet-tree .portlet-body").css('max-height', $(window).height() - 57);
    }
</script>