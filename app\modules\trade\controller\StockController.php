<?php
namespace Envsan\Modules\Trade\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Trade\Service\CustomerService;
use Envsan\Modules\Trade\Service\OrderDetailService;
use Envsan\Modules\Trade\Service\StockService;

/**
 * @name('库存')
 */
class StockController extends SuperController
{
    private $page_id = 11;

    /**
     * @name('查询')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new StockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @skipacl
     */
    public function historyAction($uid)
    {
        $od = new OrderDetailService();
        $row = $od->selectByUid($uid);
        if (empty($row)){
            die(ErrorHelper::WRONG_INPUT);
        }
        $s = new StockService();
        $this->view->list = json_encode($s->selectHistory($row->id));
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new CustomerService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}