<?php

namespace Envsan\Modules\Sys\Api;

use Envsan\Common\Base\ApiController;
use <PERSON>war\Captcha\CaptchaBuilder;

/**
 * @noacl
 */
class CaptchaController extends ApiController
{
    private function makePhrase()
    {
        // $charset = 'abcdefghijklmnpqrstuvwxyz123456789';
        $charset = '0123456789';
        $phrase = '';
        $chars = str_split($charset);

        for ($i = 0; $i < 4; $i++) {
            $phrase .= $chars[array_rand($chars)];
        }

        return $phrase;
    }

    public function imageAction()
    {
        $this->view->disable();
        $this->response->setContentType('Content-type: image/jpg');

        $phrase = $this->makePhrase();
        $this->session->set('captcha', $phrase);
        $builder = new CaptchaBuilder($phrase);
        $builder->setBackgroundColor(255, 255, 196);
        $builder->build(100);
        $builder->output();
    }
}