<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesPlan;
use Envsan\Modules\Mes\Model\MesProduceLogs;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Util\Constant;
use Envsan\Modules\Sys\Service\UserService;
use Phalcon\Mvc\User\Component;

class PlanService extends BaseService
{
    public function selectAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.remarks,
                t1.quantity,
                t1.status,
                t1.status_name,
                t2.code as product_code,
                t2.name as product_name,
                t2.ext_val as product_ext_val,
                t3.name as customer_name,
                t4.code,
                t4.plan_begin_date,
                t4.plan_end_date,
                t5.warning_flag,
                t5.error_rate,
                t5.finish_rate,
                t5.plan_rate
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewPlanWarning','t1.id = t5.notice_detail_id','t5')
            // 这里已经有排产的判断了
            ->where('t1.del_flag = 0 and t1.status = 10 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t5.warning_flag desc,t4.plan_begin_date,t1.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    /**
     * 展示排产的查询信息
     * 这里获得排产信息，但是有问题啊，未来的排产可以直接删除吗
     */
    public function searchAll(){
        $common = new CommonService();
        $work_date =  $common->getWorkDate();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t1.uid as notice_detail_uid,
                t1.remarks,
                t1.quantity,
                t1.status,
                t1.status_name,
                t2.code as product_code,
                t2.name as product_name,
                t2.ext_val as product_ext_val,
                t3.name as customer_name,
                t4.code,
                ifnull(t5.code,\'外委\') as equ_code,
                ifnull(t5.name,\'外委\') as equ_name,
                t.plan_date,
                t.plan_cnt,
                t.plan_hour,
                t6.name as bom_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesPlan', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id' ,'t1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItem','t.equ_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t.bom_id = t6.id','t6')
            ->where('t.del_flag = 0 and plan_date >= ?1 and t1.owner = ?2'
                , [1 =>$work_date, 2 => SessionData::ownerId()])
            ->orderBy('t.plan_date,t.equ_id,t.id');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function getInitData(){
        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)){
            return $this->error(ErrorHelper::WRONG_INPUT);
        }
        $builder = $this->modelsManager->createBuilder()
        ->columns('
            t1.id,
            t1.uid,
            t1.remarks,
            t1.quantity,
            t1.product_id,
            t2.code as product_code,
            t2.name as product_name,
            t3.name as customer_name,
            t4.code,
            t4.plan_begin_date,
            t4.plan_end_date,
            t4.id as notice_id
        ')
        ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
        ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
        ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
        ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            // 这里已经有排产的判断了
        ->where('t1.del_flag = 0 and t1.status = 10 and t1.uid = ?1', [1 =>$uid]);
        $detail_rows = $builder->getQuery()->execute();
        if (count($detail_rows) == 0){
            return $this->error(ErrorHelper::WRONG_INPUT);
        }
        $detail_row = $detail_rows[0]->toArray();
        $bom_list = $this->getPlanBomList($detail_row['id']);
        $userService = new UserService();
        $worker_list = $userService->getWorkers();
        return [
            'row' => $detail_row,
            'bom_list' => $bom_list,
            'day_list' => $this->getDayList(),
            'worker_list' => $worker_list
        ];
    }

    private function getDayList(){
        $day_list = [];
        $yesterday = DateUtil::yesterday();
        for($i = 1 ; $i <= 31 ; $i++){
            $date = date('Y-m-d', strtotime("+$i days",strtotime($yesterday)));
            $day_list[] = [
                'key' => strtotime($date),
                'date' => $date,
                'date_show' => date('m/d', strtotime($date)),
                'week' => Constant::$week_days[date('w', strtotime($date))]
            ];
        }
        return $day_list;
    }

    public function getPlanBomList($notice_detail_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.name,
                t1.data_sort,
                t1.produce_cnt,
                t1.start_date,
                t1.end_date,
                t1.plan_cnt,
                t1.quantity,
                round(t1.finish_cnt,2) as finish_cnt,
                round(t1.error_cnt,2) as error_cnt,
                if (t1.date_warning_flag = 1 , 1 , t1.count_warning_flag) as warning_flag,
                round(t1.quantity - t1.all_plan_cnt ,2) as un_plan_cnt,
                t1.use_plan_cnt,
                t2.id as entrust_id,
                t4.code,
                t3.plan_type,
                t3.name as ship_type_name,
                t3.id as ship_type_id
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesViewPlanFinishSituation', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust','t1.id = t2.product_bom_id and t2.del_flag = 0 and t2.notice_detail_id = t1.notice_detail_id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesShipType','t1.ship_type_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->where('t1.notice_detail_id = ?1', [1 =>$notice_detail_id])
            ->orderBy('t1.data_sort asc,t1.id asc');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row){
            $sort = CvtUtil::emptyToDouble($row['data_sort']);
            if ($sort < 10){
                $sort = '0'.$sort;
            }
            $row['bom_code'] = $row['code'] .$sort;
            if (!empty($row['entrust_id'])){
                $row['plan_type'] = 2;
            }
        }
        return $rows;
    }

    public function getPlanData() {
        $rtn = new \stdClass();
        $notice_detail_uid = $this->request->getPost('notice_detail_uid', 'tstring');
        $bom_uid = $this->request->getPost('bom_uid', 'tstring');
        $level = $this->request->getPost('level', 'tstring');
        $entrust_id = $this->request->getPost('entrust_id', 'tstring');
        $plan_type = $this->request->getPost('plan_type', 'tstring');
        if (empty($notice_detail_uid) || empty($bom_uid) || empty($level) || empty($plan_type)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$notice_detail_uid]]);
        if (empty($notice_detail_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $bom_row = MesProductBom::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$bom_uid]]);
        if (empty($bom_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $day_list = $this->getDayList();
        $begin_date = $day_list[0]['date'];
        $end_date = $day_list[count($day_list) - 1]['date'];
        $equ_ids = [];
        if (!empty($entrust_id)){
            $equ_rows = [[
                'equ_id' => '',
                'type' => 2,
                'ship_type_id' => $bom_row->ship_type_id,
                'name' => '委外加工',
                'code' => '',
                'status_name' => '',
                'type_name' => ''
            ]];
        } else {
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                    t1.id,
                    t1.equ_id,
                    1 as type,
                    t2.name,
                    t2.code,
                    t2.status_name,
                    t3.name as type_name
                ')
                ->addFrom('Envsan\Modules\Equ\Model\EquItemShip', 't1')
                ->leftJoin('Envsan\Modules\Equ\Model\EquItem','t1.equ_id = t2.id','t2')
                ->leftJoin('Envsan\Modules\Equ\Model\EquItemType','t2.type_id = t3.id','t3')
                ->where('t1.del_flag = 0 and t2.del_flag = 0 and t1.ship_type_id = ?1 and t1.ship_level = ?2', [1 => $bom_row->ship_type_id , 2 => $level])
                ->orderBy('t2.id asc');
            $equ_rows = $builder->getQuery()->execute()->toArray();
            $equ_ids = array_map(function($item) {return $item['equ_id'];}, $equ_rows);
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                    t1.id,
                    t1.uid,
                    t1.plan_date,
                    t1.plan_cnt,
                    t1.plan_hour,
                    t1.plan_sort,
                    t1.bom_id,
                    t1.equ_id,
                    t1.notice_detail_id,
                    t3.code,
                    t4.code as product_code,
                    t4.name as product_name,
                    t5.name,
                    t5.data_sort,
                    t6.name as customer_name
                ')
            ->addFrom('Envsan\Modules\Mes\Model\MesPlan', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t4.customer_id = t6.id','t6')
            ->where('t1.del_flag = 0 and t2.del_flag = 0 and t1.plan_date >= ?1 and t1.plan_date <= ?2',[1 => $begin_date , 2=> $end_date]);
        if (!empty($entrust_id)){
            $builder->andWhere(' t1.entrust_id is not null and t5.ship_type_id = ?3',[3=>$bom_row->ship_type_id]);
            $builder->orderBy('t1.plan_sort asc,t1.id asc');
        } else {
            $builder->inWhere('t1.equ_id',$equ_ids);
            $builder->orderBy('t1.equ_id,t1.plan_sort asc,t1.id asc');
        }
        $plan_rows = $builder->getQuery()->execute()->toArray();
        $plan_obj = [];
        foreach ($plan_rows as $plan_row){
            $sort = CvtUtil::emptyToDouble($plan_row['data_sort']);
            if ($sort < 10){
                $sort = '0'.$sort;
            }
            $plan_row['bom_code'] = $plan_row['code'] .$sort;
            $key = '_' . strtotime($plan_row['plan_date']) ;
            if (!empty($plan_row['equ_id'])){
                $key .=  '_' . $plan_row['equ_id'];
            }
            if (array_key_exists($key,$plan_obj)){
                $plan_obj[$key][] = $plan_row;
            } else {
                $plan_obj[$key] = [$plan_row];
            }
        }
        if ($plan_type == 2){
            foreach ($equ_rows as $idx => &$equ_row){
                $equ_row['day_list'] = json_decode(json_encode($day_list,JSON_UNESCAPED_UNICODE),true);
                foreach ($equ_row['day_list'] as  $day_idx => &$day_item){
                    $day_item['hour'] = 0;
                    $key = '_' . $day_item['key'];
                    if (!empty($equ_row['equ_id'])){
                        $key .=  '_' . $equ_row['equ_id'];
                    }
                    $list = [];
                    if (array_key_exists($key,$plan_obj)) {
                        $list = $plan_obj[$key];
                    }
                    $day_item['list'] = [];
                    for($i = 0;$i < 5;$i++){
                        $flag = true;
                        foreach ($list as $item){
                            if ($item['plan_sort'] == $i){
                                $item['col_key'] = '_'.$day_idx.'_'.$i.'_'.$idx;
                                $day_item['list'][] = $item;
                                $flag = false;
                                break;
                            }
                        }
                        if ($flag){
                            $day_item['list'][] = [
                                'id'=>'',
                                'col_key' => '_'.$day_idx.'_'.$i.'_'.$idx
                            ];
                        }
                    }
                }
            }
        } else {
            foreach ($equ_rows as &$equ_row){
                $equ_row['day_list'] = json_decode(json_encode($day_list,JSON_UNESCAPED_UNICODE),true);
                foreach ($equ_row['day_list'] as &$day_item){
                    $day_item['hour'] = 0;
                    $key = '_' . $day_item['key'] . '_' . $equ_row['equ_id'];
                    if (array_key_exists($key,$plan_obj)){
                        $day_item['list'] = $plan_obj[$key];
                        foreach ($day_item['list'] as $item){
                            $day_item['hour'] += CvtUtil::emptyToDouble($item['plan_hour']);
                        }
                        $day_item['hour'] = round($day_item['hour'],2);
                    } else {
                        $day_item['list'] = [];
                    }
                }
            }
        }
        $rtn->message = '';
        $rtn->data = [
            'equ_list' => $equ_rows,
            'bom_list' => $this->getPlanBomList($notice_detail_row->id)
        ];
        return $rtn;
    }

    public function savePlan(){

        return $this->executeInTransaction(function() {
            $notice_detail_uid = $this->request->getPost('notice_detail_uid', 'tstring');
            $bom_uid = $this->request->getPost('bom_uid', 'tstring');
            $equ_id = $this->request->getPost('equ_id', 'tstring');
            $plan_date = $this->request->getPost('plan_date', 'tstring');
            $plan_cnt = $this->request->getPost('plan_cnt', 'tstring');
            $plan_hour = $this->request->getPost('plan_hour', 'tstring');
            $worker_id = $this->request->getPost('worker_id', 'tstring');
            if (CheckUtil::is_empty($notice_detail_uid) || CheckUtil::is_empty($bom_uid) ||
                CheckUtil::is_empty($equ_id) || CheckUtil::is_empty($plan_date) ||
                CheckUtil::is_empty($plan_cnt) || CheckUtil::is_empty($plan_hour) || CheckUtil::is_empty($worker_id)){
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$notice_detail_uid]]);
            if (empty($notice_detail_row)){
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $bom_row = MesProductBom::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$bom_uid]]);
            if (empty($bom_row)){
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $sort = 1;
            $last_row = MesPlan::findFirst(['del_flag = 0 and equ_id = ?1 and plan_date = ?2',
                'bind'=>[1=>$equ_id ,2=>$plan_date],'order'=>'plan_sort desc']);
            if (!empty($last_row)){
                $sort += CvtUtil::emptyToInt($last_row->plan_sort);
            }
            //$user = SessionData::user();
            $now = DateUtil::now();
            $common = new CommonService();
            $row = new MesPlan();
            $row->uid = UUID::make();
            $row->plan_month = $common->getWorkMonth($plan_date);
            $row->plan_date = $plan_date;
            $row->plan_type = 1;
            $row->user_id = $worker_id;
            $row->notice_detail_id = $notice_detail_row->id;
            $row->product_id = $bom_row->product_id;
            $row->equ_id = $equ_id;
            $row->bom_id = $bom_row->id;
            $row->plan_cnt = CvtUtil::emptyToInt($plan_cnt);
            $row->plan_hour = CvtUtil::emptyToDouble($plan_hour);
            $row->plan_sort = $sort;
            $row->update_date = $now;
            $row->update_by = 1;
            $row->del_flag = 0;
            $row->owner = $bom_row->owner;
            $row->save();
        });

    }

    public function savePlan2(){
        $rtn = new \stdClass();
        $notice_detail_uid = $this->request->getPost('notice_detail_uid', 'tstring');
        $bom_uid = $this->request->getPost('bom_uid', 'tstring');
        $equ_id = $this->request->getPost('equ_id', 'tstring');
        $entrust_id = $this->request->getPost('entrust_id', 'tstring');
        $row_idx = $this->request->getPost('row_idx', 'tstring');
        $plan_list = $this->request->getPost('plan_list');
        if (empty($notice_detail_uid) || empty($bom_uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        if (count($plan_list) == 0){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$notice_detail_uid]]);
        if (empty($notice_detail_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $bom_row = MesProductBom::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$bom_uid]]);
        if (empty($bom_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            if (!empty($entrust_id)){
                $phql = 'DELETE FROM Envsan\Modules\Mes\Model\MesPlan WHERE entrust_id = ?1';
                $result = $this->modelsManager->executeQuery($phql, [1 => $entrust_id]);
                if (!$result->success()) {
                    throw new \Exception('删除失败！');
                }
            } else {
                $phql = 'DELETE FROM Envsan\Modules\Mes\Model\MesPlan WHERE notice_detail_id = ?1 and bom_id = ?2 and equ_id = ?3';
                $result = $this->modelsManager->executeQuery($phql,
                    [1 => $notice_detail_row->id , 2 => $bom_row->id, 3 => $equ_id]);
                if (!$result->success()) {
                    throw new \Exception('删除失败！');
                }
            }
            foreach ($plan_list as $plan_date){
                $common = new CommonService();
                $row = new MesPlan();
                $row->uid = UUID::make();
                $row->plan_month = $common->getWorkMonth($plan_date);
                $row->plan_date = $plan_date;
                $row->plan_type = 2;
                $row->notice_detail_id = $notice_detail_row->id;
                $row->entrust_id = CvtUtil::blankToNull($entrust_id);
                $row->product_id = $bom_row->product_id;
                $row->equ_id = CvtUtil::blankToNull($equ_id);
                $row->bom_id = $bom_row->id;
                $row->plan_cnt = CvtUtil::emptyToInt($notice_detail_row->quantity);
                $row->plan_hour = 0;
                $row->plan_sort = CvtUtil::emptyToInt($row_idx);
                $row->update_date = $now;
                $row->update_by = 1;
                $row->del_flag = 0;
                $row->owner = $bom_row->owner;
                if (!$row->save()){
                    throw new \Exception("表更新失败");
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function saveMove(){
        $rtn = new \stdClass();
        $equ_id = $this->request->getPost('equ_id', 'tstring');
        $plan_id = $this->request->getPost('plan_id', 'tstring');
        $plan_date = $this->request->getPost('plan_date', 'tstring');
        $ids = $this->request->getPost('ids');
        if (empty($equ_id) || empty($plan_date) || empty($plan_id) || empty($ids)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $plan_row = $this->selectById($plan_id);
        if (empty($plan_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            //$user = SessionData::user();
            $plan_row->equ_id = $equ_id;
            $plan_row->plan_date = $plan_date;
            $plan_row->update_date = $now;
            $plan_row->update_by = 1;
            if (!$plan_row->save()){
                throw new \Exception("表更新失败");
            }
            if (count($ids) > 0){
                $sort = 1;
                foreach ($ids as $id){
                    $row = $this->selectById($id);
                    if (!empty($row)){
                        $row->plan_sort = $sort;
                        $row->update_date = $now;
                        $row->update_by = 1;
                        if (!$row->save()){
                            throw new \Exception("表更新失败");
                        }
                        $sort++;
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function deleteById()
    {
        $rtn = new \stdClass();
        $plan_id = $this->request->getPost('plan_id', 'tstring');
        if (empty($plan_id)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $plan_row = $this->selectById($plan_id);

        if (empty($plan_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }

        $notice_detail_id = $plan_row->notice_detail_id;
        $bom_id = $plan_row->bom_id;
        $mesProduceLogs = MesProduceLogs::find(['notice_detail_id = ?1 and bom_id = ?2 and del_flag = 0', 'bind' => [1 => $notice_detail_id, 2 => $bom_id]]);
        if ($mesProduceLogs->count() > 0) {
            $rtn->message = '已经生产并且报工了，不可以删除';
            return $rtn;
        }
        $plan_row->del_flag = 1;
        $plan_row->update_date = DateUtil::now();
        if (!$plan_row->save()){
            $rtn->message = ErrorHelper::UNKOWN;
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function deleteById2()
    {
        $rtn = new \stdClass();
        $plan_id = $this->request->getPost('plan_id', 'tstring');
        if (empty($plan_id)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $plan_row = $this->selectById($plan_id);
        if (empty($plan_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        if (!empty($plan_row->entrust_id)){
            $phql = 'DELETE FROM Envsan\Modules\Mes\Model\MesPlan WHERE entrust_id = ?1';
            $result = $this->modelsManager->executeQuery($phql, [1 => $plan_row->entrust_id]);
            if (!$result->success()) {
                $rtn->message = ErrorHelper::UNKOWN;
                return $rtn;
            }
        } else {
            $phql = 'DELETE FROM Envsan\Modules\Mes\Model\MesPlan WHERE notice_detail_id = ?1 and bom_id = ?2 and equ_id = ?3';
            $result = $this->modelsManager->executeQuery($phql,
                [1 => $plan_row->notice_detail_id , 2 =>$plan_row->bom_id, 3 => $plan_row->equ_id]);
            if (!$result->success()) {
                throw new \Exception('删除失败！');
            }
        }
        $rtn->message = '';
        return $rtn;
    }

    public function selectById($id)
    {
        return MesPlan::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return MesPlan::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function getPlanListAll(){
        $rtn = new \stdClass();
        $day_list = $this->getDayList();
        $begin_date = $day_list[0]['date'];
        $end_date = $day_list[count($day_list) - 1]['date'];
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                    t2.id as equ_id,
                    1 as type,
                    \'\' as ship_type_id,
                    t2.name,
                    t2.code,
                    t2.status_name,
                    t3.name as type_name
                ')
            ->addFrom('Envsan\Modules\Equ\Model\EquItem','t2')
            ->leftJoin('Envsan\Modules\Equ\Model\EquItemType','t2.type_id = t3.id','t3')
            ->where('t2.del_flag = 0')
            ->orderBy('t2.id asc');
        $equ_rows = $builder->getQuery()->execute()->toArray();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                    t2.ship_type_id,
                    t3.name as ship_type_name
                ')
            ->addFrom('Envsan\Modules\Mes\Model\MesPlan', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesShipType','t2.ship_type_id = t3.id','t3')
            ->where('t1.del_flag = 0 and t2.del_flag = 0 and t1.entrust_id is not null and t1.plan_date >= ?1 and t1.plan_date <= ?2',[1 => $begin_date , 2=> $end_date])
            ->groupBy('t2.ship_type_id');
        $entrust_ships = $builder->getQuery()->execute();
        foreach ($entrust_ships as $entrust_ship){
            $equ_rows[] = [
                'equ_id' => '',
                'type' => 2,
                'ship_type_id' => $entrust_ship->ship_type_id,
                'name' => '委外加工',
                'code' => $entrust_ship->ship_type_name,
                'status_name' => '',
                'type_name' => ''
            ];
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                    t1.id,
                    t1.uid,
                    t1.plan_date,
                    t1.plan_cnt,
                    t1.plan_hour,
                    t1.plan_sort,
                    t1.plan_type,
                    t1.bom_id,
                    t1.equ_id,
                    t1.notice_detail_id,
                    t3.code,
                    t4.code as product_code,
                    t4.name as product_name,
                    t5.name,
                    t5.data_sort,
                    t6.name as customer_name,
                    t1.entrust_id,
                    t5.ship_type_id
                ')
            ->addFrom('Envsan\Modules\Mes\Model\MesPlan', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t4.customer_id = t6.id','t6')
            ->where('t1.del_flag = 0 and t2.del_flag = 0 and t1.plan_date >= ?1 and t1.plan_date <= ?2',[1 => $begin_date , 2=> $end_date])
            ->orderBy('t1.equ_id,t5.ship_type_id,t1.plan_sort asc,t1.id asc');
        $plan_rows = $builder->getQuery()->execute()->toArray();
        $plan_obj = [];
        $que_plan_type = [];
        foreach ($plan_rows as $plan_row){
            $sort = CvtUtil::emptyToDouble($plan_row['data_sort']);
            if ($sort < 10){
                $sort = '0'.$sort;
            }
            $plan_row['bom_code'] = $plan_row['code'] .$sort;
            $key = '_' . strtotime($plan_row['plan_date']);
            if (!empty($plan_row['equ_id'])){
                $key .=  'equ' . $plan_row['equ_id'];
                $que_plan_type['_' . $plan_row['equ_id']] = CvtUtil::emptyToInt($plan_row['plan_type']);
            } else {
                $key .=  'ent' . $plan_row['ship_type_id'];
            }
            if (array_key_exists($key,$plan_obj)){
                $plan_obj[$key][] = $plan_row;
            } else {
                $plan_obj[$key] = [$plan_row];
            }
        }
        foreach ($equ_rows as $idx => &$equ_row){
            $equ_row['day_list'] = json_decode(json_encode($day_list,JSON_UNESCAPED_UNICODE),true);
            if (empty($equ_row['equ_id'])){
                $equ_row['plan_type'] = 2;
            } else {
                if (array_key_exists('_' . $equ_row['equ_id'],$que_plan_type)){
                    $equ_row['plan_type'] = $que_plan_type['_' . $equ_row['equ_id']];
                } else {
                    $equ_row['plan_type'] = 1;
                }
            }
            if ($equ_row['plan_type'] == 2){
                foreach ($equ_row['day_list'] as  $day_idx => &$day_item){
                    $day_item['hour'] = 0;
                    if (empty($equ_row['equ_id'])){
                        $key = '_' . $day_item['key'].'ent' . $equ_row['ship_type_id'];
                    } else {
                        $key = '_' . $day_item['key'] . 'equ' . $equ_row['equ_id'];
                    }
                    $list = [];
                    if (array_key_exists($key,$plan_obj)) {
                        $list = $plan_obj[$key];
                    }
                    $day_item['list'] = [];
                    for($i = 0;$i < 5;$i++){
                        $flag = true;
                        foreach ($list as $item){
                            if ($item['plan_sort'] == $i){
                                $item['col_key'] = '_'.$day_idx.'_'.$i.'_'.$idx;
                                $day_item['list'][] = $item;
                                $flag = false;
                                break;
                            }
                        }
                        if ($flag){
                            $day_item['list'][] = [
                                'id'=>'',
                                'col_key' => '_'.$day_idx.'_'.$i.'_'.$idx
                            ];
                        }
                    }
                }
            } else {
                foreach ($equ_row['day_list'] as &$day_item){
                    $day_item['hour'] = 0;
                    $key = '_' . $day_item['key'] . 'equ' . $equ_row['equ_id'];
                    if (array_key_exists($key,$plan_obj)){
                        $day_item['list'] = $plan_obj[$key];
                        foreach ($day_item['list'] as $item){
                            $day_item['hour'] += CvtUtil::emptyToDouble($item['plan_hour']);
                        }
                        $day_item['hour'] = round($day_item['hour'],2);
                    } else {
                        $day_item['list'] = [];
                    }
                }
            }
        }
        $rtn->message = '';
        $rtn->data = [
            'day_list' => $this->getDayList(),
            'equ_list' => $equ_rows
        ];
        return $rtn;
    }
}