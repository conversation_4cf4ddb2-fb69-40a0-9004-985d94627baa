<?php
namespace Envsan\modules\quality\controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'quality']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->setJsonResponse();
//        if($this->request->isPost()) {
            $this->moduleExistThenDie('quality');

            $res = [
                [
                    'name' => '质量标准管理',
                    'identity' => 'quality:template',
                    'action' => [
                        ['name' => '质量标准管理', 'identity' => 'quality:template:list', 'comment' => ''],
                        ['name' => '创建质量标准', 'identity' => 'quality:template:create', 'comment' => ''],
                        ['name' => '创建质量标准', 'identity' => 'quality:template:edit', 'comment' => ''],
                        ['name' => '删除质量标准', 'identity' => 'quality:template:delete', 'comment' => '']
                    ]
                ]
            ];

            $ret = new JsonData();
            $this->db->begin();
            try {
                $this->makePackage('quality', '质量标准管理模块', '1.0', '提供质量标准管理功能');
                $module = $this->makeModule('quality', '质量标准管理模块');
                foreach ($res as $controller) {
                    $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                    foreach ($controller['action'] as $action) {
                        $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                    }
                }
                $this->db->commit();
            } catch (\Exception $e) {
                $this->db->rollback();
                Logger::error($e->getMessage(), $e->getTraceAsString());
                $ret->message = '发生错误';
            }
            $ret->emptyIsOk();
            return json_encode($ret);
//        }
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}