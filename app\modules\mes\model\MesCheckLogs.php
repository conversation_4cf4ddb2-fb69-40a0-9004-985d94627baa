<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesCheckLogs extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $work_month;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $work_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $notice_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $bom_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quality_template_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $quality_template_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $error_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $error_money;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $error_type;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $error_type_name;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $error_remarks;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $error_flag;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_tools;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $tools_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $production_workers;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $workers_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $check_val;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $staff_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $staff_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_time;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_check_logs';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesCheckLogs[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesCheckLogs
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
