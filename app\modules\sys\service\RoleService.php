<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\ModelUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Role;
use Envsan\Modules\Sys\Model\RoleRes;
use Envsan\Modules\Sys\Model\User;
use Phalcon\Mvc\User\Component;

class RoleService extends Component
{
    public function selectAll($find='')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                r.id,
                r.uid,
                r.name,
                r.scope,
                r.identity,
                r.home_page_id,
                r.update_date,
                g.name as group_name
            ')
            ->addFrom('Envsan\Modules\Sys\Model\Role', 'r')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 'r.group_id = g.id', 'g')
            ->where('r.del_flag = 0 ')
            ->orderBy('r.id desc');

        if(!SessionData::isSuper())
            $builder->andWhere('r.owner='.SessionData::ownerId());

        if(!empty($find))
            $builder->andWhere("r.name like ?1", [1 => "%$find%"]);

        ModelUtil::limitGroup('r.group_id', $builder);
        return $builder;
    }

    public function create()
    {
        $row = new Role();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = trim($this->request->getPost('name', 'string'));
        $check = Role::findFirst(['del_flag = 0 and name=?1', 'bind'=>[1=>$name]]);
        $home_page_id = trim($this->request->getPost('home_page_id', 'string'));
        //ADD RC-LQ-968 20240220 BY WXX START
        $auth_data_ids = $this->request->getPost('auth_data_ids', 'string');
        $auth_data_names = $this->request->getPost('auth_data_names', 'string');
        //ADD RC-LQ-968 20240220 BY WXX END
        if($check!=null && $check->id != $row->id)
            return '该名称已存在，请更换角色名称';
        $group_uid = trim($this->request->getPost('group_uid', 'string'));

        if ($name=='' || $group_uid=='')
            return ErrorHelper::WRONG_INPUT;
        if ($name == '')
            return ErrorHelper::WRONG_INPUT;

        if ($name == '工人'){
            return '该岗位为固定岗位不能编辑';
        }
        $gs = new GroupService();
        $group = $gs->selectByUid($group_uid);
        if ($group == null)
            return '组织不存在';

        $user = SessionData::user();
        $row->name = $name;
        $row->identity = '';
        $row->update_date = DateUtil::now();
        $row->update_by = $user->id;
        $row->group_id = $group->id;
        $row->home_page_id = $home_page_id;
        //UPDATE RC-LQ-968 20240220 BY WXX START
        if($this->request->getPost('scope', 'string')=='true' || $this->request->getPost('scope', 'string')=='1'){
            $row->scope = Role::SCOPE_USER;
            $row->auth_data_ids = null;
            $row->auth_data_names = null;
        } else {
            $row->scope = Role::SCOPE_ROLE;
            if (empty($auth_data_ids)){
                $row->auth_data_ids = null;
                $row->auth_data_names = null;
            } else {
                $row->auth_data_ids = $auth_data_ids;
                $row->auth_data_names = $auth_data_names;
            }
        }
        //UPDATE RC-LQ-968 20240220 BY WXX END
        if ($act == 'create') {
            $row->owner = $user->owner;
            $row->uid = UUID::make();
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectByUid($uid)
    {
        return Role::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }

    public function selectById($id)
    {
        return Role::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function deleteByUid($uid)
    {
        $row = Role::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
        if($row==null)
            return ErrorHelper::WRONG_ID;
        if ($row->name == '工人'){
            return '该岗位为固定岗位不能删除';
        }

        if(User::count('del_flag = 0 and role_id='.$row->id)>0)
            return '有用户绑定到该角色上，无法删除，请先解除该所属用户的角色！';

        if($row->identity=='admin')
            return '无法删除管理员角色';

        if($row->delete())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function assign($uid, $ids)
    {
        $ids = trim($ids, ',');
        $role = Role::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
        if($role==null)
            return ErrorHelper::WRONG_ID;

        if($role->identity=='admin')
            return '无法对管理员角色进行该操作';

        //删除和新插入的记录要事务完成
        $this->db->begin();

        $role_res = RoleRes::find('role_id='.$role->id);
        if($role_res!=null)
            $role_res->delete();

        $owner = SessionData::ownerId();
        $res = $this->modelsManager->executeQuery('select * from Envsan\Modules\Sys\Model\Res where id in('.$ids.')');
        foreach ($res as $row){
            $item = new RoleRes();
            $item->role_id = $role->id;
            $item->res_id = $row->id;
            $item->owner = $owner;
            $item->save();

        }
        $this->db->commit();
        return '';
    }

    public function selectRes($row)
    {
        $role_res = $row->getRoleRes();
        if(count($role_res)){
            $arr = array();
            foreach ($role_res as $item)
                array_push($arr, $item->res_id);

            $ids = trim(implode(',', $arr), ',');
            return $this->modelsManager->executeQuery('select * from Envsan\Modules\Sys\Model\Res where id in('.$ids.')');
        }
        return null;
    }
}