<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseWwInspection extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $inspection_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $inspection_day;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $inspection_date;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $inspection_department;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $check_type;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $department_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $department_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $receipt_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $receipt_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $receipt_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $supplier_id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $supplier_code;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $supplier_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $detail_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_wwinspection';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwInspection[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwInspection
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }

}
