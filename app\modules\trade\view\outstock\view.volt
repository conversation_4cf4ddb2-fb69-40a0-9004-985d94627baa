<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">发货单号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">发货日期</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="outstock_date" v-model="outstock_date" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">发货人</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="outstock_user" v-model="outstock_user" readonly/>
                                </div>
                            </div>
                        </div>
                        {{ partial('view') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" rows="3" readonly></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                <thead>
                                <tr>
                                    <th>存货编码</th>
                                    <th>产品名称</th>
                                    <th>产品规格</th>
                                    <th>主计量</th>
                                    <th>订单数量</th>
                                    <th>报价</th>
                                    <th>无税单价</th>
                                    <th>无税金额</th>
                                    <th>含税单价</th>
                                    <th>税额</th>
                                    <th>税率（%）</th>
                                    <th>价税合计</th>
                                    <th>库存数量</th>
                                    <th>订单号</th>
                                    <th>发货数量</th>
                                    <th>发货备注</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="row, index in detail_data">
                                    <td>
                                        <span v-text="row.inventory_code"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.product_name"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.product_code"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.inventory_unit"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.order_cnt"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.quote_price"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.unit_price"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.net_amount"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.unit_price_hs"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.tax_amount"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.tax_rate"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.total_amount"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.stock_cnt"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.order_code"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.quantity"></span>
                                    </td>
                                    <td>
                                        <span v-text="row.remarks"></span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {

        }
    });
</script>