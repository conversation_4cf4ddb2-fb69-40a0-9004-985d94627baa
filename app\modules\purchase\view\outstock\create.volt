{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-6">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">物资信息</span>
                    </div>
                    <div class="actions" style="display: flex;align-items: center">
                    </div>
                </div>
                <div class="portlet-body" style="min-height: 700px">
                    <div class="row">
                        <div class="col-sm-8" style="padding-left: 0">
                            <div class="search-page">
                                <div class="row table-search-bar">
                                    <div class="col-sm-6">
                                        <div class="input-group">
                                            <span class="input-group-addon">物料</span>
                                            <input type="text" class="form-control" name="goods" v-model="goods">
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="input-group">
                                            <span class="input-group-addon">供应商</span>
                                            <input type="text" class="form-control" name="supplier" v-model="supplier">
                                        </div>
                                    </div>
                                </div>
                                <div style="height:65vh;overflow-y: auto;overflow-x: hidden">
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 15px">
                                        <thead>
                                        <tr>
                                            <th>编码/名称</th>
                                            <th>规格型号/供应商</th>
                                            <th>库存量</th>
                                            <th>生产需求量</th>
                                            <th>以领料量</th>
                                            <th>选择</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="row, index in goods_list" v-if="row.show == 1">
                                            <td>
                                                <div v-text="row.code"></div>
                                                <div v-text="row.name"></div>
                                            </td>
                                            <td>
                                                <div v-text="row.model"></div>
                                                <div v-text="row.supplier_name"></div>
                                            </td>
                                            <td><span v-text="row.stock_cnt + '('+row.deputy_unit+')'"></span></td>
                                            <td><span v-text="row.total_need_quantity + '('+row.deputy_unit+')'"></span></td>
                                            <td><span v-text="row.total_outstock_quantity + '('+row.deputy_unit+')'"></span></td>
                                            <td>
                                                <a href="javascript:;" @click='addList(row)'>
                                                    <i class="fa fa-arrow-right"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div class="row" v-if="goods_list.length == 0" style="text-align: center;margin-top: 15px">
                                        <span>没有数据</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">领料信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="min-height: 625px">
                            <div id="form_data" class="row">
                                {% if dispatcher.getActionName() == 'edit' %}
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>领料单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}

                                {% endif %}
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>生产批次</label>
                                        <div class="col-sm-8">
                                            <select
                                              id="notice_id"
                                              class="form-control bs-select"
                                              v-model="notice_id"
                                              @change="changeNotice"
                                              data-live-search="true" 
                                            >
                                              <option value="">请选择生产批次</option>
                                              {% for row in noticeList %}
                                                  <option value="{{ row['id'] }}">{{ row['code'] }}</option>
                                              {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>产品</label>
                                        <div class="col-sm-8">
                                            <select
                                              id="product_id"
                                              class="form-control  bs-select"
                                              v-model="product_id"
                                              @change="changeProduct"
                                              data-live-search="true" 
                                            >
                                                <option value="">请选择产品</option>
                                                <option v-for="row in productList" :key="row.id" :value="row.id">
                                                    ${ row.name }
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>领料日期</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker-ext" placeholder="请输入领料日期" name="outstock_date" v-model="outstock_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>领料人</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="outstock_user" placeholder="请输入领料人"  v-model="outstock_user" required/>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                        <thead>
                                        <tr>
                                            <th>编码/名称</th>
                                            <th>规格型号</th>
                                            <th>领取数量</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="row, index in detail_data">
                                            <td>
                                                <span v-text="row.code"></span>
                                                <br>
                                                <span v-text="row.name"></span>
                                            </td>
                                            <td>
                                                <span v-text="row.model"></span>
                                            </td>
                                            <td>
                                                <div style="width: 160px">
                                                    <div class="input-group">
                                                        <input  type="number" class="form-control" :name="'quantity' + index" v-model="row.quantity" placeholder="数量" number="true"  maxlength="10">
                                                        <span class="input-group-addon" v-text="row.deputy_unit"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="javascript:;" @click='delList(index)' style="color: red;font-size: 20px">
                                                    <i class="fa fa-times"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div class="row" v-if="detail_data.length == 0" style="text-align: center;margin-top: 15px">
                                        <span>没有数据</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" @click="submit" class="btn btn-primary">提交出库</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonOutstock }},
        methods: {
            changeProduct() {
              if (this.product_id) {
                commonAjaxRequest('{{ url('purchase/outstock/bomgoods/json') }}', {
                    product_id:app.product_id,
                    notice_id:app.notice_id,
                }, function(rs) {
                    // 成功回调
                    app.supplier = '';
                    app.goods = '';
                    app.detail_data = [];
                    app.goods_list = rs.data;
                    app.$nextTick(function() {
                        $('.bs-select').selectpicker('refresh');
                    });
                });
              } else {
                // 清空物资信息明细
                app.supplier = '';
                app.goods = '';
                app.goods_list = [];
                app.detail_data = [];
                app.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
              }
            },
            changeNotice() {
              // 重置产品选择
              this.product_id = '';
              this.productList = [];
              // 清空领料信息明细
              this.detail_data = [];
              // 清空物资信息明细
              app.supplier = '';
              app.goods = '';
              app.goods_list = [];
              if (this.notice_id) {
                commonAjaxRequest('{{ url('purchase/outstock/changenotice/json') }}', {
                    notice_id:app.notice_id,
                }, function(rs) {
                    // 成功回调
                    app.productList = rs.data;
                    // 等待Vue更新DOM后刷新bootstrap-select
                    app.$nextTick(function() {
                        $('.bs-select').selectpicker('refresh');
                    });
                });
              } else {
                // 清空时也要刷新
                this.$nextTick(function() {
                    $('.bs-select').selectpicker('refresh');
                });
              }
            },
            keyInputChange(row){
                let val = '';
                for(let item of row.f_list) {
                    val += item.v || 0;
                }
                try {
                    row.quantity = eval(val);
                } catch (e){
                    row.quantity = '';
                }
                return row.quantity;
            },
            save(e){
                e.preventDefault();
                if(!this.product_id) {
                    toastr.error('请选择产品');
                    return
                }
                if( !$('#form').validate().form() )
                    return;
                this.saveData(1);
            },
            submit: function (e) {
                e.preventDefault();
                if(!this.product_id) {
                    toastr.error('请选择产品');
                    return
                }
                if( !$('#form').validate().form() )
                    return;
                this.saveData(2);
            },
            saveData(type){
                if (this.detail_data.length == 0){
                    toastr.error('请添加明细');
                    return;
                }

                for (let item of this.detail_data) {

                    if (!this.isValidNumber(item.quantity)) {
                        toastr.error(`规格型号"${item.model}"的领取数量无效，请输入大于0的数值`);
                        return;
                    }
                    
                    // if (!this.isValidNumber(item.stock_cnt)) {
                    //     toastr.error(`规格型号"${item.model}"的库存数量无效`);
                    //     return;
                    // }

                    // const quantity = Number(item.quantity);
                    // const stockCnt = Number(item.stock_cnt);
                    
                    // if (quantity > stockCnt) {
                    //     toastr.error(`规格型号"${item.model}"的领取数量(${quantity})不能超过库存数量(${stockCnt})`);
                    //     return;
                    // }
                }
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('purchase/outstock/edit/' ~ from ~ '/' ~ uid) }}';
                {% else %}
                var url= '{{ url('purchase/outstock/create/' ~ from) }}';
                {% endif %}

                commonAjaxRequest(url, {
                    type:type,
                    outstock_user:app.outstock_user,
                    outstock_date:app.outstock_date,
                    product_id:app.product_id,
                    notice_id:app.notice_id,
                    remarks:app.remarks,
                    files:encodeURI(JSON.stringify(app.files)),
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                    detail : encodeURI(JSON.stringify(app.detail_data)).replace(/\+/g,'%2B')
                }, function(rs) {
                    // 成功回调
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                });
            },
            delFile:function (index) {
                this.files.splice(index);
            },
            addList(goods){
                for(let item of this.detail_data){
                    if (item.id == goods.id){
                        toastr.error('已存在，不能重复添加');
                        return;
                    }
                }
                if (goods.formula_list != null){
                    goods.f_list = JSON.parse(goods.formula_list);
                } else {
                    goods.f_list = [];
                }
                let outstock_quantity = safeNumber(goods.total_need_quantity) - safeNumber(goods.total_outstock_quantity);
                let quantity = outstock_quantity < 0 ? 0 : outstock_quantity
                this.detail_data.push({...goods, quantity: quantity})
            },
            delList: function(idx) {
                this.detail_data.splice(idx, 1);
            },
            goodsShow() {
                for (let item of this.goods_list) {
                    item.show = 1;
                    if (this.supplier != '') {
                        if (item.supplier_name.indexOf(this.supplier) == -1){
                            item.show = 0;
                        }
                    }
                    if (this.goods != '') {
                        let name = item.code + item.name + (item.spec || '') + (item.model || '');
                        let conditions = this.goods.split(' ');
                        let show_flag = 1;
                        for (let condition of conditions) {
                            if (name.indexOf(condition) < 0) {
                                show_flag = 0;
                                break;
                            }
                        }
                        item.show = show_flag;
                    }
                }
            },
            isValidNumber(value, allowZero = false, allowNegative = false) {
                // 使用Number()确保是纯数字
                const num = Number(value);
                
                // 检查是否为有效数字
                if (isNaN(num)) {
                    return false;
                }
                
                // 检查是否为零
                if (num === 0 && !allowZero) {
                    return false;
                }
                
                // 检查是否为负数
                if (num < 0 && !allowNegative) {
                    return false;
                }
    
                return true;
            }
        },
        watch:{
            supplier: function(val) {
                this.goodsShow();
            },
            goods: function(val) {
                this.goodsShow();
            }
        }
    });

    initUpLoaderPdf('purchase_outstock');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}