<?php
namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Res;
use Envsan\Modules\Sys\Service\ResService;
use Envsan\Modules\Sys\Service\RoleService;

class ResController extends SuperController
{
    /**
     * @super
     */
    public function listAction($type = '')
    {
        $rs = new ResService();

        if ($type == 'json') {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->status = JsonData::STATUS_OK;
            $ret->data = $rs->selectTree();
            return json_encode($ret);
        }

        $this->view->jsonTree = $rs->selectTree();
    }

    /**
     * @super
     */
    public function createAction($type = '')
    {
        $rs = new ResService();

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->create($type);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        if ($type == 'controller' || $type == 'action')
            $this->view->res = $rs->selectById(intval($this->request->get('id')));
        else if ($type != 'module')
            die(ErrorHelper::WRONG_ID);

        $this->view->jsonRes = json_encode((new Res())->jsonSerialize());
        $this->view->type = $type;
    }

    /**
     * @super
     */
    function editAction($id = 0)
    {
        $rs = new ResService();
        $row = $rs->selectById(intval($id));
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $this->view->pick('res/create');
        $this->view->res = $row;
        $this->view->jsonRes = json_encode($row->jsonSerialize());
    }

    /**
     * @super
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $id = intval($this->request->getPost('id', 'int'));
            $rs = new ResService();
            if ($rs->deleteById($id))
                $ret->status = JsonData::STATUS_OK;
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function selAction($uid = '', $act = '')
    {
        $this->view->action = $act;
        $rs = new ResService();
        $this->view->jsonTree = $rs->selectTree(false);

        $ros = new RoleService();
        $row = $ros->selectByUid($uid);;
        $this->view->role = $row;

        $res = $ros->selectRes($row);
        $arr = array();

        if(!empty($res)) {
            foreach ($res as $item)
                array_push($arr, $item->id);
        }

        $this->view->nodes = json_encode($arr);
    }
}