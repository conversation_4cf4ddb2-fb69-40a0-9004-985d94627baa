<?php
namespace Envsan\Modules\{{Module}}\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\{{Module}}\Model\{{Model}};
use Phalcon\Mvc\User\Component;

class {{Model}}Service extends Component
{
    public function selectAll()
    {
        $name = $this->request->get('name', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\{{Module}}\Model\{{Model}}')
            ->where('del_flag = 0 and owner = '.SessionData::ownerId())
            ->orderBy('id desc');

        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("name like ?1", [1 => "%$name%"]);
        }
        return $builder;
    }

    public function create()
    {
        $row = new {{Model}}();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $name = $this->request->getPost('name', 'tstring');

        if (empty($name))
            return ErrorHelper::WRONG_INPUT;

        $user = SessionData::user();
        $now = DateUtil::now();

        $row->name = $name;
        $row->update_date = $now;
        $row->update_by = $user->id;

        if ($act == 'create') {
            $row->uid = UUID::make();
            $row->create_date = $now;
            $row->create_by = $user->id;
            $row->del_flag = 0;
            $row->group_id = $user->group_id;
            $row->owner = $user->owner;
        }

        if ($row->save())
            return '';
        return ErrorHelper::UNKOWN;
    }

    public function selectById($id)
    {
        return {{Model}}::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return {{Model}}::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return true;
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save();
    }
}