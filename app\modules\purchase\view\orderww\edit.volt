{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div  class="col-sm-12">
            <div class="portlet light" style="margin-bottom: 10px;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">外委加工单信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">订单号</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_code" v-model="order_code" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">订单日期</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="order_date" v-model="order_date">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">供应商</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" name="supplier_name" v-model="supplier_name" readonly>
                                        </div>
                                    </div>
                                </div>

                                {{ partial('form') }}
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right" style="padding: 0px;padding-top: 10px;">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" @click="commit" class="btn btn-warning">提交审批</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="portlet light" style="margin-bottom: 0;">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-handbag font-blue"></i>
                        <span class="caption-subject font-blue bold">外委加工单明细</span>
                    </div>
                    <div class="actions">
                        <div class="actions" style="display: flex;align-items: center">
                            <button type="button" class="btn btn-outline yellow" onclick="choose()">
                                <span>选择</span>
                            </button>
                            <button style="margin-left: 10px" type="button" class="btn btn-outline red" @click="delApply">
                                <span>删除</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>
                                        <a @click="applySelect">
                                            <span v-if="apply_sel == 0"> 全选</span>
                                            <span v-else> 取消</span>
                                        </a>
                                    </th>
                                    <th>物料编码</th>
                                    <th>产品名称</th>
                                    <th>产品规格</th>
                                    <th>委外工序</th>
                                    <th>计量单位</th>
                                    <th>外委数量</th>
                                    <th>待出库数量</th>
                                    <th>计价单位</th>
                                    <th>计价数量</th>
                                    <th>换算率</th>
                                    <th>未税单价(元)</th>
                                    <th>含税单价(元)</th>
                                    <th>税率</th>
                                    <th>税额</th>
                                    <th>未税金额</th>
                                    <th>税价合计</th>
                                    <th>生产批次</th>
                                    <th>请选择工序价格</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="ww_list.length == 0">
                                    <td colspan="20" style="text-align: center;">没有找到匹配的记录</td>
                                </tr>
                                <tr v-else v-for="row, index in ww_list">
                                    <td>
                                        <a @click="row.sel == 1 ? row.sel = 0 : row.sel = 1">
                                            <i v-if="row.sel == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                            <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                        </a>
                                    </td>
                                    <td v-text="row.goods_code"></td>
                                    <td v-text="row.product_name"></td>
                                    <td v-text="row.product_model"></td>
                                    <td v-text="row.bom_name"></td>
                                    <td v-text="row.measurement_unit"></td>
                                    <td>
                                        <div class="input-group">
                                            <input  type="number" class="form-control" :name="'quantity' + index" @input="updateAmounts(row, index)" v-model="row.quantity" placeholder="数量" number="true" min="0" max="99999999" step="0.01" required>
                                        </div>
                                    </td>
                                    <td v-text="row.wait_out_cnt"></td>
                                    <td v-text="row.price_unit"></td>
                                    <td v-text="row.pricing_quantity"></td>
                                    <td v-text="row.conversion_rate"></td>
                                    <td v-text="row.price"></td>
                                    <td v-text="row.price_hs"></td>
                                    <td>${row.tax_rate ? row.tax_rate * 100 : ''}</td>
                                    <td v-text="row.tax_money"></td>
                                    <td v-text="row.total_money"></td>
                                    <td v-text="row.total_money_hs"></td>
                                    <td v-text="row.notice_code"></td>
                                    <td>
                                        <a href="javascript:;" @click='openProcessModal(row, index)' class="btn btn-xs blue">
                                            <i class="fa fa-cog"></i> 工序价格
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 委外工序选择模态框 -->
    <div class="modal fade" id="processModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="processModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="processModalLabel">选择委外工序</h4>
            </div>
            <div class="modal-body">
                <!-- 搜索框 -->
                
                <!-- 工序列表 -->
                <div class="table-scrollable">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                    <tr>
                        <th width="40">选择</th>
                        <th>委外商编码</th>
                        <th>委外商</th>
                        <th>物料编号</th>
                        <th>物料名称</th>
                        <th>规格型号</th>
                        <th>工序名称</th>
                        <th>计量单位</th>
                        <th>计价单位</th>
                        <th>加工费单价</th>
                        <th>换算率</th>
                        <th>税率</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-if="process_list.length == 0">
                        <td colspan="11" class="text-center">没有找到匹配的记录</td>
                    </tr>
                    <tr v-for="(item, index) in process_list" :key="index" :class="{'active': selectedProcessIndex === index}">
                        <td class="text-center">
                        <input type="radio" name="processSelection" v-model="selectedProcessIndex" :value="index">
                        </td>
                        <td v-text="item.supplier_code"></td>
                        <td v-text="item.supplier_name"></td>
                        <td v-text="item.goods_code"></td>
                        <td v-text="item.goods_name"></td>
                        <td v-text="item.goods_model"></td>
                        <td v-text="item.ship_type_name"></td>
                        <td v-text="item.measurement_unit"></td>
                        <td v-text="item.price_unit"></td>
                        <td v-text="item.unit_price"></td>
                        <td v-text="item.conversion_rate"></td>
                        <td>${item.tax_rate * 100}</td>
                    </tr>
                    </tbody>
                </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn default" data-dismiss="modal">取消</button>
                <button type="button" class="btn blue" @click="confirmSelectProcess">确认选择</button>
            </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }},
            apply_sel: 0,
            process_list: [], // 工序列表
            selectedProcessIndex: -1, // 选中的工序索引，-1表示未选择
            plan_index: -1
        },
        created() {
        },
        mounted: function() {
            
        },
        methods: {
            // 验证明细数据
            validateDetailData: function(type) {
                if(!this.ww_list || this.ww_list.length === 0) {
                    toastr.error('没有添加明细数据！');
                    return false;
                }

                for (let i = 0; i < this.ww_list.length; i++) {
                    let item = this.ww_list[i];
                    if (!item.process_id && type == 2) {
                        toastr.error('请选择对应的工序价格等信息！');
                        return false;
                    }                    
                    // 验证数量必须大于0
                    if (!isDecimal(item.quantity) || item.quantity <= 0) {
                        toastr.error('【序号' + (i + 1) + '】订单数量只能是大于0的数字。');
                        return false;
                    }

                    if (this.safeNumber(item.quantity)  >  this.safeNumber(item.wait_out_cnt) ){
                        toastr.error('【序号' + (i + 1) + '】订单数量不能大于待出库数量');
                        return false;
                    }
                }
                return true;
            },
            save:function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                if (!this.validateDetailData(1)) {
                    return;
                }
                
                this.submit(1);
            },
            commit:function (e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                if (!this.validateDetailData(2)) {
                    return;
                }
                
                var dlg = top.layer.confirm('确认提交审批吗?', function() {
                    top.layer.close(dlg);
                    app.submit(2);
                });
            },
            submit: function (type) {
                var url= '{{ url('purchase/orderww/edit/' ~ uid) }}';
                commonAjaxRequest(url, {
                    type : type,
                    order_code: app.order_code,
                    order_date: app.order_date,
                    supplier_id: app.supplier_id,
                    remarks:app.remarks,
                    detail_data:encodeURI(JSON.stringify(app.ww_list)).replace(/\+/g,'%2B'),
                    files:encodeURI(JSON.stringify(app.files)),
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B')
                }, function(rs) {
                    top.window.layer_result = 'ok';
                    top.layer.close(top.layer.getFrameIndex(window.name));
                });
            },
            delFile:function (index) {
                this.files.splice(index);
            },
            // 打开工序选择模态框
            openProcessModal(row, index) {
                this.plan_index = index;
                this.process_list = []; // 清空列表
                this.searchProcess(row); // 加载工序列表
                $('#processModal').modal('show');
            },
            // 搜索工序
            searchProcess(row) {
                commonAjaxRequest('{{ url("purchase/orderww/processlist") }}', {
                    supplier_id: app.supplier_id,
                    goods_code: row.goods_code,
                    ship_type_id: row.ship_type_id
                }, function(rs) {
                    let processList = rs.data || [];
                    processList.forEach(function(item) {
                        item.selected = false;
                    });
                    app.process_list = processList;
                    app.selectedProcessIndex = -1; // 重置选中索引
                });
            },
            // 安全转换函数
            safeNumber: function(value, defaultValue = 0) {
                if (value === '' || value === null || value === undefined) {
                    return defaultValue;
                }
                const num = Number(value);
                return isNaN(num) ? defaultValue : num;
            },
            
            // 确认选择工序
            confirmSelectProcess() {
                if (this.selectedProcessIndex === -1) {
                    toastr.warning('请选择一个工序');
                    return;
                }
                let selectedProcess = this.process_list[this.selectedProcessIndex];
                
                this.addSelectedProcess(selectedProcess);
            },
            
            // 添加选中的工序到订单明细
            addSelectedProcess: function(selectedItem) {
                // 获取当前行的数据
                const currentRow = this.ww_list[this.plan_index];
                const originalTaxRate = selectedItem.tax_rate || 0; // 原始税率，如0.13
                console.log('原始税率:', originalTaxRate);
                
                // 计算基础价格和含税价格
                let basePrice, taxPrice;
                
                // 确保所有数据都是数字类型
                
                if (selectedItem.price_flag == 2) {
                    // 价格标志为2时，unit_price是未税单价
                    basePrice = Number(selectedItem.unit_price);
                    taxPrice = Number(basePrice * (1 + Number(originalTaxRate)));
                    // 格式化为2位小数
                    basePrice = parseFloat(basePrice.toFixed(2));
                    taxPrice = parseFloat(taxPrice.toFixed(2));
                } else {
                    // 价格标志不为2时，unit_price是含税单价
                    taxPrice = Number(selectedItem.unit_price);
                    basePrice = Number(taxPrice / (1 + Number(originalTaxRate)));
                    // 格式化为2位小数
                    basePrice = parseFloat(basePrice.toFixed(2));
                    taxPrice = parseFloat(taxPrice.toFixed(2));
                }
                
                // 记录最终计算结果
                console.log('最终计算结果 - 基础价格:', basePrice, '含税价格:', taxPrice);
                // 计算加工数量（考虑换算率）
                const conversionRate = parseFloat(selectedItem.conversion_rate) || 1; // 防止为0或非数字
                const wwCnt = parseFloat((parseFloat(currentRow.quantity) * conversionRate).toFixed(2)); // 保留2位小数
                
                // 计算税额和金额（保留2位小数）
                const taxMoney = parseFloat((originalTaxRate * wwCnt * basePrice).toFixed(2));
                const totalMoney = parseFloat((wwCnt * basePrice).toFixed(2));
                const totalMoneyHs = parseFloat((wwCnt * taxPrice).toFixed(2));
                
                // 只更新需要的字段，保留其他原有数据
                const updatedFields = {
                    process_id: selectedItem.id,
                    measurement_unit: selectedItem.measurement_unit || '',
                    price_unit: selectedItem.price_unit || '',
                    price_flag: selectedItem.price_flag || 1,
                    price: basePrice,
                    price_hs: taxPrice,
                    unit_price: selectedItem.unit_price,
                    conversion_rate: conversionRate,
                    // 计价数量，也是外委数量
                    pricing_quantity: wwCnt,
                    tax_rate: originalTaxRate, // 存储为百分比形式（如13而不是0.13）
                    tax_money: taxMoney,
                    total_money: totalMoney,
                    total_money_hs: totalMoneyHs,
                };
                
                // 合并原有数据和新数据
                const detailItem = Object.assign({}, currentRow, updatedFields);
                
                // 更新数组中的元素（确保响应式更新）
                Vue.set(this.ww_list, this.plan_index, detailItem);
                
                toastr.success('已添加委外工序到订单明细');
                $('#processModal').modal('hide');
            },
            delApply: function() {
                let ww_list = [], goods_obj = {};
                for (let item of this.ww_list) {
                    if (!goods_obj.hasOwnProperty(item.id)) {
                        goods_obj[item.id] = 0;
                    }

                    if (item.sel == 0) {
                        ww_list.push(item);
                        goods_obj[item.id]++;
                    }
                }
                if (ww_list.length === this.ww_list.length) {
                    toastr.error('请选择删除记录！');
                    return;
                }
                this.ww_list = ww_list;
                app.$forceUpdate();
            },
            applySelect(){
                if (this.apply_sel == 0){
                    this.apply_sel = 1;
                } else {
                    this.apply_sel = 0;
                }
                for (let item of this.ww_list){
                    item.sel = this.apply_sel;
                }
            },
            // 当数量变化时更新金额
            updateAmounts(row, index) {
                // 确保数量是有效数字
                const quantity = parseFloat(row.quantity) || 0;
                if (quantity <= 0 || !row.process_id) {
                    return;
                }
                
                // 获取基础数据
                const basePrice = parseFloat(row.price) || 0;
                const taxPrice = parseFloat(row.price_hs) || 0;
                const conversionRate = parseFloat(row.conversion_rate) || 1;
                const taxRate = parseFloat(row.tax_rate) || 0;
                
                // 计算加工数量（考虑换算率）
                const wwCnt = parseFloat((quantity * conversionRate).toFixed(2));
                
                // 计算税额和金额（保留2位小数）
                const taxMoney = parseFloat((taxRate * wwCnt * basePrice).toFixed(2));
                const totalMoney = parseFloat((wwCnt * basePrice).toFixed(2));
                const totalMoneyHs = parseFloat((wwCnt * taxPrice).toFixed(2));
                
                // 更新当前行的计算字段
                const updatedFields = {
                    pricing_quantity: wwCnt,
                    tax_money: taxMoney,
                    total_money: totalMoney,
                    total_money_hs: totalMoneyHs
                };
                
                // 合并原有数据和新数据
                const detailItem = Object.assign({}, row, updatedFields);
                
                // 更新数组中的元素（确保响应式更新）
                Vue.set(this.ww_list, index, detailItem);
            },
        }
    });

    function create() {
        top.window.layer_result='';
        top.layer.open({
            title:'新增明细',
            type: 2,
            area: ['70em', '90%'],
            content: '{{ url('trade/orderdetail/create/' ~ uid)}}',
            end : function() {
                if(top.window.layer_result == 'ok'){
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function choose() {
        ids = [];
        for (let item of app.ww_list) {
            ids.push(item.id);
        }
        top.window.layer_result2 = '';
        top.layer.open({
            title: '添加到外委加工单明细',
            type: 2,
            area: ['100%', '100%'],
            content: '{{url("purchase/orderww/addplan/0/")}}' + ids,
            end: function() {
                if (top.window.layer_result2 == 'ok') {
                    addDetailList(top.window.layer_data2);
                }
            }
        });
    }

    function addDetailList(ww_list) {
        paramLoop: for (let i = 0; i < ww_list.length; i++) {
            record = ww_list[i];
            record['quantity'] = record['wait_out_cnt'];
            for (let j = 0; j < app.ww_list.length; j++) {
                if (app.ww_list[j].id == record.id) {
                    continue paramLoop;
                }
            }
            app.ww_list.push(record);
        }
    }
    

    initUpLoaderPdf('purchase_orderww');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}