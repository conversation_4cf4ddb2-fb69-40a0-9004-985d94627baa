{{ assets.outputJs('validate') }}
<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate">
                <div class="form-body">
                    {% if res is not empty %}
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    创建于：<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" readonly value="{{ res.name}}/{{ res.identity }}">
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    标识：<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="identity" v-model="identity" placeholder="必须是英文字母,且唯一" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    命名：<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" v-model="name" placeholder="比如:人员管理" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    说明：<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <textarea class="form-control" name="comment" v-model="comment" placeholder="请输入一个简短的说明"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el:'#app',
        data:{{ jsonRes }},
        methods:{
            submit :function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if dispatcher.getActionName()=='edit' %}
                var url= '{{ url('sys/res/edit/'~res.id) }}';
                {% else %}
                var url= '{{ url('sys/res/create/'~type) }}';
                {% endif %}

                {% if res is not empty %}
                this.pid = {{ res.id }};
                {% endif %}

                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });

    {% if dispatcher.getActionName()=='create' and res is not empty%}
    app.identity = '{{ res.identity }}' + ':';
    {% endif %}
</script>
