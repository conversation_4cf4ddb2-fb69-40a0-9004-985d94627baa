<?php

namespace Envsan\Modules\Trade\Model;

use Envsan\Common\Model\BaseModel;

use Envsan\Common\Util\ModelUtil;

class TradeOrderDetail extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $new_flag;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $new_flag_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $price;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $price_hs;

    /**
     *
     * @var string
     * @Column(type="string", length=15, nullable=true)
     */
    public $total_money;

    /**
     *
     * @var string
     * @Column(type="string", length=15, nullable=true)
     */
    public $total_money_hs;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $files;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $inventory_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $inventory_unit;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $purchase_unit;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $is_batch_managed;

    /**
     *
     * @var double
     * @Column(type="double", length=3, nullable=true)
     */
    public $tax_rate;


    /**
     *
     * @var double
     * @Column(type="double", length=10, nullable=true)
     */
    public $weight;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'trade_order_detail';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return TradeOrderDetail[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return TradeOrderDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
