<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseWwProcessPrice;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\PurchaseWwProcessService;
use Envsan\Modules\Purchase\Service\SupplierService;
use Envsan\Modules\Trade\Util\Constant;

/**
 * @name('委外工序价格')
 */
class WwprocessController extends SuperController
{
    private $page_id = 49;

    /**
     * @name('管理')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new PurchaseWwProcessService();
            $builder = $s->selectAllPagination();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @skipacl
     * 物料选择页面
     */
    public function addgoodsAction($type = 'page', $ids = '')
    {
        // 获取mode参数，判断是否为单选模式
        $mode = $this->request->get('mode', 'string', '');
        $this->view->mode = $mode;
        $this->view->singleMode = ($mode === 'single');
        
        $this->view->ids = $ids;
        $gs = new GoodsTypeService();
        $this->view->jsonTree = $gs->selectTree();
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $s = new PurchaseWwProcessService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $jrow = (new PurchaseWwProcessPrice())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);


        $gs = new GoodsTypeService();
        $supplierService = new SupplierService();
        // 供应商
        $jrow['supplier_list'] = $supplierService->select4Dropdown();
        // 工序
        $jrow['ship_type_list'] = $s->getShipTypeList();
        $gs = new GoodsTypeService();
        $this->view->tree_list = $gs->selectTree();
        $this->view->jsonPurchaseWwProcess = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'purchase:wwprocess:create'})
     */
    public function editAction($uid = '')
    {
        $s = new PurchaseWwProcessService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, CvtUtil::emptyToArray($row->ext_data));
        $supplierService = new SupplierService();
        $jrow['supplier_list'] = $supplierService->select4Dropdown();
        $jrow['ship_type_list'] = $s->getShipTypeList();
        $gs = new GoodsTypeService();
        $this->view->tree_list = $gs->selectTree();
        $this->view->jsonPurchaseWwProcess = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('wwprocess/create');
    }

    /**
     * @acl({'link':'purchase:wwprocess:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwProcessService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new PurchaseWwProcessService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->jsonPurchaseWwProcess = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new PurchaseWwProcessService();
        $builder = $s->selectAllPagination();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function changesupplierAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwProcessService();
            $ret = new JsonData();
            return json_encode($ret->handleResult($s->changeSupplier()));
        }
    }

    /**
     * @skipacl
     */
    public function goodsinfoAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new PurchaseWwProcessService();
            $ret = new JsonData();
            return json_encode($ret->handleResult($s->getGoodsInfo()));
        }
    }
}