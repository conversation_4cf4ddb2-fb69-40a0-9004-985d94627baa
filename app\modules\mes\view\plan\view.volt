{% do assets.collection('css').addCss('static/global/plugins/bootstrap-colorpicker/css/colorpicker.css') %}
{% do assets.collection('js').addJs('static/global/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jquery-minicolors/jquery.minicolors.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jquery-minicolors/jquery.minicolors.js') %}
<div id="app" style="height: 100vh;width: 100%;background-color: #fff">
    <div style="display: flex;flex-direction: row;">
        <div>
            <div class="plan-header">
            </div>
            <div v-for="task_item,task_idx in task_list">
                <div class="plan-item" style="background-color: #f2f2f2;font-size: 12px;font-weight: 600;padding: 3px">
                    <div><span v-text="task_item.code"></span></div>
                    <div><span v-text="task_item.work_hour + '(H)'"></span></div>
                </div>
            </div>
        </div>
        <div style="flex: 1;overflow-x: auto;padding-bottom: 100px;">
            <div style="display: flex;flex-direction: row">
                <div v-for="day_item,day_idx in days" class="plan-header">
                    <span v-text="day_item.date_show"></span>
                </div>
            </div>
            <div style="display: flex;flex-direction: row" v-for="task_item,task_idx in task_list">
                <div :style="{backgroundColor:plan_item.color}"  class="plan-item" v-for="plan_item,plan_idx in task_item.plan_list">
                    <div  v-if="plan_item.plan_id != ''">
                        <div class="btn-group">
                            <div class="dropdown-toggle" data-toggle="dropdown" >
                                <span style="color: #FFFFFF;font-size: 12px" v-text="plan_item.notice_code"></span> <span style="color: #FFF" class="caret"></span>
                            </div>
                            <ul class="dropdown-menu pull-left" role="menu">
                                <li><a href="javascript:" @click="viewBom(plan_item.bom_uid)">工艺</a></li>
                                <li><a href="javascript:" @click="viewProduct(plan_item.product_uid)">产品</a></li>
                                <li><a href="javascript:" @click="viewNotice(plan_item.notice_uid)">生产通知</a></li>
                            </ul>
                        </div>
                        <div style="line-height: 17px;"><span style="color: #FFFFFF;font-size: 12px" v-text="'数量:' + plan_item.cnt"></span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            days : {{ days }},
            task_list : {{ task_list }}
        },
        methods: {
            viewBom(uid) {
                top.layer.open({
                    title: '查看工艺',
                    type: 2,
                    area: ['70em', '90%'],
                    content: '{{ url('mes/bom/view/') }}' + uid
                });
            },
            viewNotice(uid) {
                top.layer.open({
                    title: '查看生产通知',
                    type: 2,
                    area: ['70em', '90%'],
                    content: '{{ url('mes/notice/view/') }}' + uid
                });
            },
            viewProduct(uid) {
                top.layer.open({
                    title: '查看产品',
                    type: 2,
                    area: ['70em', '90%'],
                    content: '{{ url('mes/product/view/') }}' + uid
                });
            }
        }
    });
</script>
<style>
    .plan-header{
        width: 100px;
        height: 40px;
        line-height: 40px;
        background-color: #F2F2F2;
        border-width:  0 2px 2px 0;
        border-color: #FFF;
        border-style: solid;
        text-align: center;
        flex-shrink: 0;
    }

    .plan-item{
        width: 100px;
        height: 45px;
        background-color: #E2E2E2;
        border-width:  0 2px 2px 0;
        border-color: #FFF;
        border-style: solid;
        flex-shrink: 0;
        padding-left: 4px;
    }
    .plan-item::selection {
        background: rgba(255,255,255,0);
    }
    .dropdown-menu {
        min-width: 100px !important;
    }
</style>