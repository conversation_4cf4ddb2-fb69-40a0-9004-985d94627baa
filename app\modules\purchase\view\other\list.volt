{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content" >
    <div class="search-page">
        <div id="app" class="search-bar bordered" style="margin-bottom: 0;padding-bottom: 0">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-6 col-lg-6">
                        <h3 class="page-title">其他入库管理</h3>
                    </div>
                    <div class="col-md-6 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        {% if acl.has('purchase:other:create') %}
                            <button type="button" class="btn yellow" onclick="create()">
                                <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                            </button>
                        {% endif %}
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        {{ partial('table') }}
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            print: {{ print }}
        },
        mounted:function (){
            let btn_list = [];
            {% if acl.has('purchase:other:create') %}
            btn_list.push({type:1,name:'编辑',fn: (data) => {return data.status == 10}});
            btn_list.push({type:2,name:'删除',fn: (data) => {return data.status == 10}});
            btn_list.push({type:3,name:'取消入库',fn: (data) => {return data.status > 10}});
            {% endif %}
            btn_list.push({type:4,name:'查看详情',fn: (data) => {return data.status > 10}});
            btn_list.push({type: 5, name: '打印', fn: (data) => {return data.status > 10}});
            app_ext_table.init({{ page_id }},btn_list,'{{ url('purchase/other/list/json') }}');
        },
        methods: {
            search: function() {
                app_ext_table.search();
            },
            reset: function() {
                app_ext_table.reset();
            },
            excel:function (){
                app_ext_table.excel("{{ url('purchase/other/export') }}");
            },
            getParams: function() {
                return {}
            },
            dataView:function (type,row){
                if (type == 1){
                    edit(row.uid);
                } else if (type == 2){
                    del(row.uid);
                } else if (type == 3){
                    cancel(row.uid);
                } else if (type == 4){
                    view(row.uid);
                } else if (type == 5) {
                    print(row.uid);
                }
            }
        }
    });

    function create() {
        top.window.layer_result='';
        top.layer.open({
            title:'新增',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('purchase/other/create') }}',
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function edit(uid){
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('purchase/other/edit/') }}'+uid,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function view(uid){
        top.window.layer_result='';
        top.layer.open({
            title:'查看',
            type: 2,
            area: ['80em', '90%'],
            content: '{{ url('purchase/other/view/') }}'+uid,
            end:function(){
            }
        });
    }

    function del(uid) {
        var dlg = top.layer.confirm('确认删除吗?', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('purchase/other/delete') }}", {uid: uid}, function(rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }

    function cancel(uid) {
        var dlg = top.layer.confirm('确认取消入库吗?', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('purchase/other/cancel') }}", {uid: uid}, function(rs) {
                closeSpin(dlg);
                if (rs.status == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }

    function print(uid) {
        if (app.print.uid == '') {
            toastr.error('请配置打印模板！');
            return;
        }
        window.open("{{'/print/#/print?uid='}}" + app.print.uid + "&doc_id=" + uid);
    }
</script>