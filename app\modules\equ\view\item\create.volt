{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('js').addJs('static/global/plugins/iCheck/icheck.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/iCheck/all.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required">*</span>设备名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" v-model="name" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required"> * </span>设备编号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" maxlength="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required"> * </span>设备型号</label>
                                <div class="col-sm-8">
                                    <select name="type_id" v-model="type_id" class="bs-select form-control" data-size="8" required>
                                        <option value="">请选择</option>
                                        {% for row in type_list %}
                                            <option value="{{ row.id }}">{{ row.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required"> * </span>状态</label>
                                <div class="col-sm-8">
                                    <select name="status_name" v-model="status_name" class="bs-select form-control" required>
                                        <option value="">请选择</option>
                                        {% for key, val in status_list %}
                                            <option value="{{ val }}">{{ val }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span class="required"> * </span>工资系数</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="wages_ratio" v-model="wages_ratio" maxlength="10" required>
                                </div>
                            </div>
                        </div>
                        {{ partial('form') }}
                    </div>
                    <h3>设备工艺能力配置</h3>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><span class="required"> * </span>优先级1</label>
                                <div class="col-sm-10">
                                    <select class="bs-select form-control" name="ship_list1" v-model="ship_list1" data-live-search="true" data-size="8" required multiple>
                                        {% for row in ship_list %}
                                            <option value="{{ row['id'] }}">{{ row['label'] }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">优先级2</label>
                                <div class="col-sm-10">
                                    <select class="bs-select form-control" name="ship_list2" v-model="ship_list2" data-live-search="true" data-size="8" multiple>
                                        {% for row in ship_list %}
                                            <option value="{{ row['id'] }}">{{ row['label'] }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">优先级3</label>
                                <div class="col-sm-10">
                                    <select class="bs-select form-control" name="ship_list3" v-model="ship_list3" data-live-search="true" data-size="8" multiple>
                                        {% for row in ship_list %}
                                            <option value="{{ row['id'] }}">{{ row['label'] }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonEquItem }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                for(let ship of this.ship_list1){
                    if (this.ship_list2.some(item=>item.id == ship)){
                        toastr.error('工艺重复配置！');
                        return;
                    }
                    if (this.ship_list3.some(item=>item.id == ship)){
                        toastr.error('工艺重复配置！');
                        return;
                    }
                }
                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('equ/item/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('equ/item/create') }}';
                {% endif %}

                let param = JSON.parse(JSON.stringify(this.$data));
                param.ext_data = encodeURI(JSON.stringify(this.ext_data)).replace(/\+/g, '%2B');

                showSpin();
                $.post(url, param, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            }
        }
    });

    $(".bs-select-d").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });

    $(function() {
        let $icheck_com = $('.icheck-rdo');
        $icheck_com.iCheck({
            radioClass: 'iradio_square-blue'
        });
        $icheck_com.on('ifChanged', function(e) {
            if ($(this).is(':checked')) {
                app[$(this).attr('name')] = $(this).val();
            }
        });
    });
</script>
{{ partial('form_script') }}