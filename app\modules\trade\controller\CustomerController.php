<?php
namespace Envsan\Modules\Trade\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
//use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Trade\Model\TradeCustomer;
use Envsan\Modules\Trade\Service\CustomerService;
use Envsan\Modules\Trade\Util\Constant;

/**
 * @name("客户")
 */
class CustomerController extends SuperController
{
    private $page_id = 1;

    /**
     * @name("管理")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new CustomerService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("新增")
     */
    public function createAction()
    {
        $s = new CustomerService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $table = new TableService();
        $jrow = (new TradeCustomer())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['goods_type_text'] = null;
//        $gs = new GoodsTypeService();
//        $this->view->tree_list = $gs->selectTree(Constant::$purchase_finished_inventory_arr,2);
        $this->view->jsonCustomer = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'trade:customer:create'})
     */
    function editAction($uid)
    {
        $s = new CustomerService();
        if($this->request->isPost()){
            $row = $s->selectByUid($uid);
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        } else {
            $row = $s->selectByUid2($uid);
            if (empty($row))
                die(ErrorHelper::WRONG_ID);
            $table = new TableService();
            $ext_data =  CvtUtil::emptyToArray($row->ext_data);

            $jrow = $row->toArray();
            $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
//            $gs = new GoodsTypeService();
//            $this->view->tree_list = $gs->selectTree(Constant::$purchase_finished_inventory_arr);
            $this->view->jsonCustomer = json_encode($jrow);
            $this->view->uid = $uid;
            $this->view->extDataName = 'ext_data';
            $this->view->extDataCnt = 6;
            $this->view->pick('customer/create');
        }
    }

    /**
     * @acl({'link':'trade:customer:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new CustomerService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new CustomerService();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonCustomer = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new CustomerService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}