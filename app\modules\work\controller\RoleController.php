<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Sys\Service\UserService;
use Envsan\Modules\Work\Model\WorkRole;
use Envsan\Modules\Work\Service\CommonService;
use Envsan\Modules\Work\Service\RoleService;

/**
 * @name('权限')
 */
class RoleController extends SuperController
{
    /**
     * @name('管理')
     */
    public function listAction($type='')
    {
        if($type=='json') {
            $this->setJsonResponse();
            $rs = new RoleService();
            $find = trim($this->request->get('name', 'string'));
            $rows = $rs->selectAll($find);
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
    }

    /**
     * @name('设定')
     */
    public function userlistAction($type='')
    {
        if($type=='json') {
            $this->setJsonResponse();
            $rs = new RoleService();
            $find = trim($this->request->get('name', 'string'));
            $rows = $rs->selectUserAll($find);
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
        $cs = new CommonService();
        $this->view->groupList = $cs->getGroupList();
    }

    /**
     * @acl({'link':'work:role:userlist'})
     */
    public function userAction($uid)
    {
        $us = new UserService();
        $row = $us->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);
        $rs = new RoleService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->saveRole($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $this->view->uid = $uid;
        $this->view->name = $row->real_name;
        $this->view->role_id = CvtUtil::nullToBlank($row->work_role_id);
        $this->view->list = $rs->getRoleList();
    }

    /**
     * @acl({'link':'work:role:list'})
     */
    public function createAction()
    {
        if($this->request->isPost()){
            $this->setJsonResponse();
            $rs = new RoleService();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = (new WorkRole())->toArray();
        $this->view->jsonRole = json_encode($jrow);
    }

    /**
     * @acl({'link':'work:role:list'})
     */
    function editAction($uid='')
    {
        $rs = new RoleService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $jrow = $row->toArray();
        $this->view->jsonRole = json_encode($jrow);
        $this->view->role = $row;
        $this->view->pick('role/create');
    }

    /**
     * @acl({'link':'work:role:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new RoleService();
            $ret = new JsonData();
            $ret->message = $rs->deleteByUid($this->request->getPost('uid', 'string'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'work:role:list'})
     */
    public function assignAction($uid)
    {
        $rs = new RoleService();
        $row = $rs->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->saveAssign($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $this->view->uid = $uid;
        $this->view->list = json_encode($rs->getAssign($row->type_ids));
    }

}