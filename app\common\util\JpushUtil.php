<?php
namespace Envsan\Common\Util;

use Envsan\Common\Component\Logger;
use Phalcon\Mvc\User\Component;

class JpushUtil extends Component
{
    public function push($uid,$src,$title,$content,$reg_ids){
        $client = new \JPush\Client($this->config->jpush->key, $this->config->jpush->secret);
        $push = $client->push();
        $alert = 'Hello JPush';
        $android_notification = array(
            'alert' => $title,
            'title' => $title,
            'big_text' => $content,
            'builder_id' => 3,
            'category' => 'WORK',
            'priority' => 0,
            'style' => 1,
            'alert_type' => 1,
            'intent' =>['url'=>'intent:#Intent;action=android.intent.action.MAIN;component=com.fengtongmespro34app/com.fengtongmespro34app.MainActivity;S.uid='.$uid.';S.src='.$src.';end'],
            'badge_add_num' => 1,
            'extras' => array(
                'uid' => $uid,
                'src' => $src
            ),
        );
        $response = $push
            ->setPlatform(array('android'))
            ->addRegistrationId($reg_ids)
            ->androidNotification($alert, $android_notification)
            ->message($title,array(
                'title' => $title,
                'msg_content' => $content,
                'content_type' => 'text',
                'extras' => array(
                    'uid' => $uid,
                    'src' => $src
                ),
            ))
            ->options(array(
                'third_party_channel' => array(
                    'huawei' => array (
                        'distribution' => 'secondary_push',
                        'importance' => 'HIGH',
                        'category' => 'WORK',
                        'urgency' => 'HIGH',
                        'target_user_type' => 1,
                        'style' => 1
                    )
                )
            ))
            ->send();
    }
}