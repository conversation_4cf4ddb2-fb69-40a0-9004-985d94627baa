<?php

namespace Envsan\Modules\Equ\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class EquFault extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $fault_no;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $fault_month;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $fault_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $equ_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $equ_code;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $equ_type;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $equ_type_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $fault_level;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $begin_dt;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $begin_describe;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $begin_files;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $begin_submit_dt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $begin_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $repair_dt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $repair_user;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $repair_user_name;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $repair_describe;

    /**
     *
     * @var integer
     * @Column(type="integer", length=200, nullable=true)
     */
    public $repair_company_id;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $repair_company;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $repair_submit_dt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $repair_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $repair_money;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $repair_money_describe;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $repair_finish_dt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $repair_finish_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $end_dt;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $end_describe;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $end_files;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $end_submit_dt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $end_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'equ_fault';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquFault[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return EquFault
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
