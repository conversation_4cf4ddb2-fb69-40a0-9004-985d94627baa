(self["webpackChunkrrts_manager"] = self["webpackChunkrrts_manager"] || []).push([["src_view_work_display_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/components/base.vue?vue&type=script&lang=js":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/components/base.vue?vue&type=script&lang=js ***!
  \***********************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  name: "base",
  data() {
    return {
      route_name: '',
      base_to_view_name: ''
    };
  },
  created() {
    this.route_name = this.$router.currentRoute.name;
  },
  beforeRouteEnter(to, from, next) {
    //console.log('beforeRouteEnter',from.name,to.name);
    if (to.meta.from === undefined || to.meta.from == '') {
      to.meta.from = from.name;
    }
    next();
  },
  activated() {
    if (this.base_to_view_name == '') {
      this.onLoad ? this.onLoad() : void 0;
    }
  },
  watch: {
    '$route'(to, from) {
      if (this.$store.state.auth == false) {
        this.base_to_view_name = '';
      }
      if (from.name == this.route_name) {
        //console.log('$route',from.name,to.name);
        if (from.meta.from == to.name) {
          from.meta.from = '';
          this.base_to_view_name = '';
        } else {
          this.base_to_view_name = to.name;
        }
      } else if (to.name == this.route_name) {
        if (this.base_to_view_name != '') {
          //console.log(this.base_to_view_name,from.name);
          this.onShow ? this.onShow() : void 0;
        }
      }
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=script&lang=js":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=script&lang=js ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.push.js */ "./node_modules/core-js/modules/es.array.push.js");
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _components_base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/base */ "./src/components/base.vue");
/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config */ "./src/config.js");




/* harmony default export */ __webpack_exports__["default"] = ({
  extends: _components_base__WEBPACK_IMPORTED_MODULE_1__["default"],
  name: "work",
  data() {
    return {
      active: 0,
      uid: '',
      pos: 0,
      step: 0,
      message: '',
      type: 1,
      src: 2,
      reject_show_flag: false,
      reject_value: '',
      base_path: '',
      flow_list: [],
      anchor_list: [],
      user_list: [],
      next_id: '',
      next_list: [],
      work_list: [],
      main_id: '',
      pid: '',
      data: {}
    };
  },
  components: {},
  created() {
    this.$hub.$on('refresh', data => {
      this.refresh();
    });
  },
  methods: {
    onLoad() {
      let user = this.$store.state.user;
      this.base_path = user.imgdir;
      this.pos = 0;
      this.uid = this.$route.params.uid;
      this.step = 0;
      this.message = '';
      this.type = this.$route.params.type;
      this.src = this.$route.params.src;
      this.pass_show_flag = false;
      this.pass_value = '';
      this.reject_show_flag = false;
      this.reject_value = '';
      this.next_id = '';
      this.$http.post('work/work/view', {
        uid: this.uid,
        src: this.src
      }).then(rs => {
        if (rs.status == 'ok') {
          this.step = 2;
          this.main_id = rs.data.data.main_id;
          this.pid = rs.data.data.pid;
          this.flow_list = rs.data.flow_list;
          this.anchor_list = rs.data.anchor_list;
          this.user_list = rs.data.user_list;
          this.next_list = rs.data.next_list;
          this.work_list = rs.data.work_list;
          this.data = rs.data.data;
          for (let i = 0; i < this.work_list.length; i++) {
            if (this.work_list[i].uid == this.uid) {
              this.active = i;
              break;
            }
          }
        } else {
          this.step = 1;
          this.message = rs.message;
        }
      });
    },
    refresh() {
      this.$http.post('work/work/view', {
        uid: this.uid,
        src: this.src,
        main_id: this.main_id,
        pid: this.pid
      }).then(rs => {
        if (rs.status == 'ok') {
          this.flow_list = rs.data.flow_list;
          this.anchor_list = rs.data.anchor_list;
          this.user_list = rs.data.user_list;
          this.next_list = rs.data.next_list;
          this.work_list = rs.data.work_list;
          this.data = rs.data.data;
          for (let i = 0; i < this.work_list.length; i++) {
            if (this.work_list[i].uid == this.uid) {
              this.active = i;
              break;
            }
          }
        } else {
          this.$toast.fail(rs.message);
        }
      });
    },
    onShow() {
      this.onLoad();
      this.$refs['scroll'].scrollTop = this.pos;
    },
    previewImg(img) {
      this.$router.push({
        name: 'preview/image',
        params: {
          images: [this.base_path + img]
        }
      });
    },
    openFile(path) {
      if (/\.pdf$/.test(path)) {
        this.$router.push({
          name: 'preview/pdf',
          params: {
            path: this.base_path + path
          }
        });
      } else {
        window.open(this.base_path + path);
      }
    },
    viewWeight(item) {
      this.$router.push({
        name: 'work/weightdetail',
        params: {
          uid: item.uid
        }
      });
    },
    viewDetail(v) {
      if (v.sort == 99) {
        this.$router.push({
          name: 'work/accountsdetail',
          params: {
            id: v.id
          }
        });
      } else if (v.sort == 999) {
        this.$router.push({
          name: 'display',
          params: {
            uid: v.id,
            type: 2,
            src: 2
          }
        });
      } else if (v.sort == 1) {
        this.$router.push({
          name: 'instock/review_detail',
          params: {
            uid: v.instock_uid,
            auth: this.data.auth
          }
        });
      } else if (v.sort == 2) {
        if (v.business_type == 0) {
          this.$router.push({
            name: 'outstock/detail',
            params: {
              uid: v.uid
            }
          });
        } else if (v.business_type == 1) {
          this.$router.push({
            name: 'instock/detail',
            params: {
              uid: v.uid
            }
          });
        } else {
          this.$router.push({
            name: 'work/weightdetail',
            params: {
              uid: v.uid
            }
          });
        }
      } else if (v.sort == 3) {
        this.$router.push({
          name: 'outstock/search',
          params: {
            id: v.id,
            type: 'invoice'
          }
        });
      } else if (v.sort == 4) {
        if (v.pdf_name == '') {
          if (v.contract_type == 1) {
            this.$router.push({
              name: 'preview/page',
              params: {
                path: _config__WEBPACK_IMPORTED_MODULE_2__["default"].host + '/dist/#/contract/' + v.contract_key
              }
            });
          } else {
            this.$toast({
              message: '合同未上传！',
              position: 'top'
            });
          }
        } else {
          this.$router.push({
            name: 'preview/pdf',
            params: {
              path: this.base_path + this.data.pdf_name
            }
          });
        }
      } else if (v.sort == 7) {
        this.$router.push({
          name: 'trade/ticket',
          params: {
            id: v.id,
            type: 'invoice'
          }
        });
      } else if (v.sort == 8) {
        this.$router.push({
          name: 'pay/detail',
          params: {
            account_id: v.id,
            name: v.cardholder
          }
        });
      } else if (v.sort == 9) {
        this.$router.push({
          name: 'price/price_view',
          params: {
            price_id: v.price_id
          }
        });
      }
    },
    viewMore() {
      this.$router.push({
        name: 'work/more',
        params: {
          uid: this.data.uid
        }
      });
    },
    doBack() {
      this.$router.back();
    },
    scroll(event) {
      this.pos = event.target.scrollTop;
    },
    selectItem(idx) {
      this.next_id = this.next_list[idx].id;
    },
    stepClick(idx) {
      if (this.work_list[idx].uid == this.uid) {
        return;
      }
      this.uid = this.work_list[idx].uid;
      this.refresh();
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/components/base.vue?vue&type=template&id=6ba07e61":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/components/base.vue?vue&type=template&id=6ba07e61 ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div");
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=template&id=597fa565&scoped=true":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=template&id=597fa565&scoped=true ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_vm.type == 2 ? _c('div', {
    staticStyle: {
      "position": "absolute",
      "top": "60px",
      "right": "10px",
      "z-index": "999"
    }
  }, [_c('van-button', {
    staticStyle: {
      "width": "40px",
      "height": "40px",
      "border-radius": "25px",
      "font-size": "20px",
      "padding-top": "5px"
    },
    attrs: {
      "type": "info"
    },
    on: {
      "click": _vm.doBack
    }
  }, [_c('van-icon', {
    attrs: {
      "name": "arrow-left"
    }
  })], 1)], 1) : _vm._e(), _vm.step == 0 ? _c('div') : _vm._e(), _vm.step == 1 ? _c('div', [_c('van-empty', {
    attrs: {
      "image": "error",
      "description": _vm.message
    }
  })], 1) : _vm._e(), _vm.step == 2 ? _c('div', {
    staticClass: "main"
  }, [_vm.work_list.length > 1 ? _c('van-steps', {
    attrs: {
      "active": _vm.active
    },
    on: {
      "click-step": _vm.stepClick
    }
  }, _vm._l(_vm.work_list, function (item, i) {
    return _c('van-step', [_vm._v(_vm._s(item.name))]);
  }), 1) : _vm._e(), _c('div', {
    ref: "scroll",
    style: {
      overflow: 'auto',
      height: _vm.data.auth == 0 ? '100vh' : 'calc(100vh - 56px)',
      paddingBottom: '60px'
    },
    on: {
      "scroll": _vm.scroll
    }
  }, [_c('div', {
    staticClass: "header"
  }, [_c('div', {
    staticClass: "title",
    domProps: {
      "textContent": _vm._s(_vm.data.title)
    }
  }), _c('div', {
    staticClass: "title2",
    domProps: {
      "textContent": _vm._s(_vm.data.group)
    }
  }), _c('div', {
    staticClass: "title3"
  }, [_c('span', {
    domProps: {
      "textContent": _vm._s(_vm.data.anchor)
    }
  }), _vm._v("      "), _vm.data.handle_status == 1 ? _c('van-tag', {
    attrs: {
      "type": "success",
      "size": "large"
    }
  }, [_vm._v("通过")]) : _vm._e(), _vm.data.handle_status == 2 ? _c('van-tag', {
    attrs: {
      "type": "danger",
      "size": "large"
    }
  }, [_vm._v("拒绝")]) : _vm._e(), _vm.data.handle_status == 3 ? _c('van-tag', {
    attrs: {
      "type": "danger",
      "size": "large"
    }
  }, [_vm._v("撤销")]) : _vm._e()], 1)]), _c('div', {
    staticClass: "content",
    staticStyle: {
      "padding": "0",
      "overflow": "hidden"
    }
  }, [_c('div', [_c('van-cell-group', [_c('van-cell', {
    attrs: {
      "title": "业务单号",
      "value": _vm.data.code
    }
  }), _c('van-cell', {
    attrs: {
      "title": "业务名称",
      "value": _vm.data.type_name
    }
  }), _vm.data.review_type == 1 ? _c('van-cell', {
    attrs: {
      "title": "业务类型"
    },
    scopedSlots: _vm._u([{
      key: "extra",
      fn: function () {
        return [_c('span', {
          staticStyle: {
            "color": "red"
          }
        }, [_vm._v("撤销审批")])];
      },
      proxy: true
    }], null, false, 688513901)
  }) : _vm._e(), _c('van-cell', {
    attrs: {
      "title": "申请人",
      "value": _vm.data.create_user
    }
  }), _c('van-cell', {
    attrs: {
      "title": "申请时间",
      "value": _vm.data.create_date
    }
  }), _vm._l(_vm.data.form_list, function (item, i) {
    return [item.type == 99 ? [item.sum_list.length > 0 || item.data_list.length > 0 ? [_c('van-cell', {
      attrs: {
        "title": item.name,
        "icon": "star"
      }
    }), _vm._l(item.sum_list, function (sum, j) {
      return _c('van-cell', {
        attrs: {
          "title": sum.name,
          "value": sum.value + sum.unit
        }
      });
    }), _vm._l(item.data_list, function (d, k) {
      return _c('div', {
        staticClass: "content",
        staticStyle: {
          "padding": "0",
          "overflow": "hidden"
        }
      }, [_c('van-cell-group', {
        attrs: {
          "inset": ""
        }
      }, [_vm._l(d, function (v, l) {
        return [v.type == 100 ? _c('van-cell', {
          attrs: {
            "title": "查看详情",
            "is-link": ""
          },
          on: {
            "click": function ($event) {
              return _vm.viewDetail(v);
            }
          }
        }) : _c('van-cell', {
          attrs: {
            "title": v.name,
            "value": v.value + v.unit
          }
        })];
      })], 2)], 1);
    })] : _vm._e()] : item.type == 999 ? _c('van-cell', {
      attrs: {
        "title": "图片"
      },
      scopedSlots: _vm._u([{
        key: "extra",
        fn: function () {
          return [_c('div', {
            staticStyle: {
              "display": "flex",
              "flex-direction": "row",
              "flex-wrap": "wrap",
              "width": "79%"
            }
          }, _vm._l(item.value, function (file, i) {
            return _c('div', {
              staticStyle: {
                "width": "80px",
                "height": "80px",
                "position": "relative",
                "margin-right": "8px",
                "margin-bottom": "10px"
              },
              on: {
                "click": function ($event) {
                  return _vm.previewImg(file);
                }
              }
            }, [_c('van-image', {
              staticClass: "img-view",
              attrs: {
                "src": _vm.base_path + file + '!small',
                "width": "80px",
                "height": "80px"
              }
            })], 1);
          }), 0)];
        },
        proxy: true
      }], null, true)
    }) : item.type == 101 ? _c('van-cell', {
      attrs: {
        "title": item.name,
        "is-link": ""
      },
      on: {
        "click": function ($event) {
          return _vm.viewDetail(item);
        }
      }
    }) : _c('van-cell', {
      attrs: {
        "title": item.name,
        "value": item.value + item.unit
      }
    })];
  }), _vm.data.remarks != null ? _c('van-cell', {
    attrs: {
      "title": "说明",
      "value": _vm.data.remarks
    }
  }) : _vm._e(), _vm.data.files.length > 0 ? _c('van-cell', {
    attrs: {
      "title": "图片"
    },
    scopedSlots: _vm._u([{
      key: "extra",
      fn: function () {
        return [_c('div', {
          staticStyle: {
            "display": "flex",
            "flex-direction": "row",
            "flex-wrap": "wrap",
            "width": "79%"
          }
        }, _vm._l(_vm.data.files, function (file, i) {
          return _c('div', {
            staticStyle: {
              "width": "80px",
              "height": "80px",
              "position": "relative",
              "margin-right": "8px",
              "margin-bottom": "10px"
            },
            on: {
              "click": function ($event) {
                return _vm.previewImg(file);
              }
            }
          }, [_c('van-image', {
            staticClass: "img-view",
            attrs: {
              "src": _vm.base_path + file + '!small',
              "width": "80px",
              "height": "80px"
            }
          })], 1);
        }), 0)];
      },
      proxy: true
    }], null, false, 3396324386)
  }) : _vm._e(), _vm.data.file_list.length > 0 ? _c('van-cell', {
    attrs: {
      "title": "文件"
    },
    scopedSlots: _vm._u([{
      key: "extra",
      fn: function () {
        return [_c('div', {
          staticClass: "file-box"
        }, _vm._l(_vm.data.file_list, function (file) {
          return _c('div', {
            staticClass: "file-item van-ellipsis",
            domProps: {
              "textContent": _vm._s(file.name)
            },
            on: {
              "click": function ($event) {
                return _vm.openFile(file.path);
              }
            }
          });
        }), 0)];
      },
      proxy: true
    }], null, false, 2499635437)
  }) : _vm._e(), _vm.data.more_flag == 1 ? _c('van-cell', {
    attrs: {
      "title": "查看子流程",
      "is-link": ""
    },
    on: {
      "click": _vm.viewMore
    }
  }) : _vm._e()], 2)], 1)]), _vm.flow_list.length > 0 ? _c('div', {
    staticClass: "content"
  }, [_c('div', _vm._l(_vm.flow_list, function (item, index) {
    return _c('div', {
      staticClass: "item"
    }, [_c('div', {
      staticClass: "title-border"
    }, [item.type == 4 ? _c('div', {
      staticClass: "title",
      staticStyle: {
        "background-color": "#3296FB"
      }
    }, [_c('van-icon', {
      staticStyle: {
        "margin-top": "6px"
      },
      attrs: {
        "name": "wap-home-o",
        "size": "30"
      }
    })], 1) : item.type == 5 ? _c('div', {
      staticClass: "title",
      staticStyle: {
        "background-color": "#FF2B2B"
      }
    }, [_c('van-icon', {
      staticStyle: {
        "margin-top": "6px"
      },
      attrs: {
        "name": "stop",
        "size": "30"
      }
    })], 1) : item.type == 6 ? _c('div', {
      staticClass: "title",
      staticStyle: {
        "background-color": "#FF2B2B"
      }
    }, [_c('van-icon', {
      staticStyle: {
        "margin-top": "6px"
      },
      attrs: {
        "name": "revoke",
        "size": "30"
      }
    })], 1) : _c('div', {
      staticClass: "title",
      staticStyle: {
        "background-color": "#3296FB"
      }
    }, [_vm._v(" " + _vm._s(item.icon) + " "), item.type == 1 ? _c('div', {
      staticClass: "border",
      staticStyle: {
        "color": "#4AB37E"
      }
    }, [_c('van-icon', {
      attrs: {
        "name": "checked"
      }
    })], 1) : item.type == 3 ? _c('div', {
      staticClass: "border",
      staticStyle: {
        "color": "#4AB37E"
      }
    }, [_c('van-icon', {
      attrs: {
        "name": "volume"
      }
    })], 1) : _c('div', {
      staticClass: "border",
      staticStyle: {
        "color": "#3296FB"
      }
    }, [_c('van-icon', {
      attrs: {
        "name": "thumb-circle"
      }
    })], 1)])]), _c('div', {
      staticClass: "item-content"
    }, [_c('div', {
      staticClass: "top"
    }, [_c('div', {
      style: {
        flex: '1',
        color: item.status == 0 ? '#A2A2A2' : '#000000'
      }
    }, [_c('span', [_vm._v(_vm._s(item.name))]), _c('span', [_vm._v(" " + _vm._s(item.val))])]), _c('div', {
      staticStyle: {
        "width": "80px",
        "margin-left": "5px"
      }
    }, [_c('span', {
      staticStyle: {
        "font-size": "13px",
        "color": "#898989",
        "margin-top": "-3px"
      }
    }, [_vm._v(_vm._s(item.time))])])]), _c('div', {
      staticClass: "bottom",
      style: {
        borderLeft: _vm.flow_list.length - 1 == index ? 0 : '4px #D2D2D2 solid'
      }
    }, [item.text != '' ? _c('div', {
      staticClass: "bottom-content"
    }, [_vm._v(" " + _vm._s(item.text) + " ")]) : _vm._e(), _c('div', {
      staticStyle: {
        "display": "flex",
        "flex-direction": "row",
        "flex-wrap": "wrap",
        "margin-left": "35px",
        "margin-top": "5px"
      }
    }, _vm._l(item.files, function (file, i) {
      return _c('div', {
        staticStyle: {
          "width": "80px",
          "height": "80px",
          "position": "relative",
          "margin-right": "8px",
          "margin-bottom": "10px"
        },
        on: {
          "click": function ($event) {
            return _vm.previewImg(file);
          }
        }
      }, [_c('van-image', {
        staticClass: "img-view",
        attrs: {
          "src": _vm.base_path + file + '!small',
          "width": "80px",
          "height": "80px"
        }
      })], 1);
    }), 0), item.send != '' ? _c('div', {
      staticClass: "bottom-send"
    }, [_vm._v(" " + _vm._s(item.send) + " ")]) : _vm._e()])])]);
  }), 0), _c('div', _vm._l(_vm.anchor_list, function (item, idx) {
    return _c('div', {
      key: idx
    }, [_c('div', {
      staticStyle: {
        "display": "flex",
        "flex-direction": "row",
        "min-height": "60px"
      }
    }, [_c('div', {
      staticStyle: {
        "width": "35%",
        "display": "flex",
        "flex-direction": "row"
      }
    }, [_c('div', {
      staticStyle: {
        "height": "100%",
        "position": "relative"
      }
    }, [_c('div', {
      staticStyle: {
        "height": "100%",
        "border-right": "1px #D2D2D2 solid",
        "width": "8px"
      }
    }), idx == 0 ? _c('div', {
      staticStyle: {
        "height": "18px",
        "position": "absolute",
        "top": "0",
        "width": "12px",
        "background-color": "#FFF"
      }
    }) : _vm._e(), idx + 1 == _vm.anchor_list.length ? _c('div', {
      staticStyle: {
        "height": "calc(100% - 22px)",
        "position": "absolute",
        "top": "22px",
        "width": "12px",
        "background-color": "#FFF"
      }
    }) : _vm._e(), _c('div', {
      staticStyle: {
        "position": "absolute",
        "top": "15px",
        "width": "15px",
        "height": "15px",
        "background-color": "#aaaaaa",
        "border-radius": "15px"
      }
    })]), _c('div', {
      staticStyle: {
        "padding-top": "5px",
        "padding-left": "15px"
      }
    }, [_c('span', {
      staticStyle: {
        "font-weight": "400"
      },
      domProps: {
        "textContent": _vm._s(item.name)
      }
    }), _c('div', [item.type == 1 ? _c('span', {
      staticStyle: {
        "font-size": "12px",
        "color": "#898989"
      }
    }, [_vm._v("会签")]) : _vm._e(), item.type == 2 ? _c('span', {
      staticStyle: {
        "font-size": "12px",
        "color": "#898989"
      }
    }, [_vm._v("或签")]) : _vm._e()])])]), _c('div', {
      staticStyle: {
        "width": "65%",
        "display": "flex",
        "flex-direction": "row",
        "flex-wrap": "wrap",
        "justify-content": "flex-end",
        "padding-right": "10px",
        "padding-top": "5px"
      }
    }, _vm._l(item.list, function (user, i) {
      return _c('van-tag', {
        key: user.id,
        staticStyle: {
          "height": "20px",
          "margin": "2px"
        },
        attrs: {
          "type": item.type == 1 ? 'primary' : 'success',
          "size": "large"
        }
      }, [_vm._v(_vm._s(user.name))]);
    }), 1)]), item.nlist.length > 0 ? _c('div', {
      staticStyle: {
        "display": "flex",
        "flex-direction": "row",
        "min-height": "60px"
      }
    }, [_c('div', {
      staticStyle: {
        "width": "35%",
        "display": "flex",
        "flex-direction": "row"
      }
    }, [_c('div', {
      staticStyle: {
        "height": "100%",
        "position": "relative"
      }
    }, [idx + 1 != _vm.anchor_list.length ? _c('div', {
      staticStyle: {
        "height": "100%",
        "border-right": "1px #D2D2D2 solid",
        "width": "8px"
      }
    }) : _c('div', {
      staticStyle: {
        "height": "100%",
        "width": "8px"
      }
    })]), _vm._m(0, true)]), _c('div', {
      staticStyle: {
        "width": "65%",
        "display": "flex",
        "flex-direction": "row",
        "flex-wrap": "wrap",
        "justify-content": "flex-end",
        "padding-right": "10px",
        "padding-top": "5px"
      }
    }, _vm._l(item.nlist, function (user, i) {
      return _c('van-tag', {
        key: user.id,
        staticStyle: {
          "height": "20px",
          "margin": "2px"
        },
        attrs: {
          "type": "default",
          "size": "large"
        }
      }, [_vm._v(_vm._s(user.name))]);
    }), 1)]) : _vm._e()]);
  }), 0)]) : _vm._e()])], 1) : _vm._e()]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticStyle: {
      "padding-top": "5px",
      "padding-left": "15px"
    }
  }, [_c('span', {
    staticStyle: {
      "font-weight": "400",
      "color": "#898989"
    }
  }, [_vm._v("抄送人")])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "./node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.main[data-v-597fa565]{\n    background-color: #F2F2F2;\n    width: 100%;\n    padding-bottom: 10px;\n    height: 100vh;\n    overflow: hidden;\n}\n.header[data-v-597fa565]{\n    width: 100%;\n    min-height: 100px;\n    background-color: #FFFFFF;\n    padding: 15px;\n    box-sizing: border-box;\n}\n.header .title[data-v-597fa565]{\n    font-size: 24px;\n    letter-spacing: 1px;\n}\n.header .title2[data-v-597fa565]{\n    font-size: 14px;\n    color: #898989;\n    margin-top: 10px;\n}\n.header .title3[data-v-597fa565]{\n    font-size: 16px;\n    margin-top: 10px;\n    color: #CAA36D;\n    font-weight: bold;\n    letter-spacing: 1px;\n}\n.content[data-v-597fa565]{\n    min-height: 100px;\n    background-color: #FFFFFF;\n    margin: 15px;\n    padding: 15px;\n    border: 1px #E2E2E2 solid;\n    border-radius: 10px;\n}\n.item[data-v-597fa565]{\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-start;\n    margin-top: 4px;\n}\n.item .title[data-v-597fa565]{\n    width: 45px;\n    height: 45px;\n    line-height: 45px;\n    vertical-align: middle;\n    background-color: #3296FB;\n    border: 1px #E2E2E2 solid;\n    border-radius: 5px;\n    color: #FFFFFF;\n    text-align: center;\n    position: relative;\n    font-size: 16px;\n}\n.item .title .border[data-v-597fa565]{\n    position: absolute;\n    top: 31px;\n    left: 31px;\n    width: 18px;\n    height: 18px;\n    line-height: 18px;\n    border-radius: 9px;\n    background-color: #FFFFFF;\n    text-align: center;\n    padding-top: 1px;\n}\n.item .item-content[data-v-597fa565]{\n    width: 100%;\n    margin-left: 15px;\n    color: #888888;\n}\n.item .item-content .top[data-v-597fa565] {\n    min-height: 20px;\n    line-height: 20px;\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n    font-size: 16px;\n    margin-top: 2px;\n    vertical-align: top;\n}\n.item .item-content .bottom[data-v-597fa565] {\n    margin-left: -38px;\n    border-left: 4px #D2D2D2 solid;\n    padding-bottom: 10px;\n    min-height: 55px;\n}\n.item .bottom-content[data-v-597fa565]{\n    margin-left: 35px;\n    margin-top:5px;\n    min-height: 40px;\n    background-color: #F2F2F2;\n    border-radius: 5px;\n    padding: 10px;\n    color: #000000;\n}\n.item .bottom-send[data-v-597fa565]{\n    margin-left: 35px;\n    margin-top:5px;\n    font-size: 14px;\n}\n.item .title-border[data-v-597fa565]{\n    background-color: #FFFFFF;\n    width: 45px;\n    height: 52px;\n    z-index: 999;\n}\n.flow-title[data-v-597fa565]{\n    font-size: 20px;\n    margin-top: 10px;\n    margin-bottom: 15px;\n    font-weight: bold;\n    letter-spacing: 2px;\n}\n.footer[data-v-597fa565]{\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 56px;\n    border-top: 1px #E2E2E2 solid;\n    background-color: #FFFFFF;\n    z-index: 1000;\n    box-sizing: border-box;\n    padding-top: 5px;\n    padding-right: 15px;\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n}\n.footer .btn-left[data-v-597fa565]{\n    border-radius: 0;\n    border-top-left-radius:22px;\n    border-bottom-left-radius:22px;\n    font-size: 18px;\n    width: 90px;\n}\n.footer .btn-right[data-v-597fa565]{\n    border-radius: 0;\n    border-top-right-radius:22px;\n    border-bottom-right-radius:22px;\n    font-size: 18px;\n    width: 90px;\n}\n.footer .btn-right-left[data-v-597fa565]{\n    border-radius: 0;\n    border-top-left-radius:22px;\n    border-bottom-left-radius:22px;\n    border-top-right-radius:22px;\n    border-bottom-right-radius:22px;\n    font-size: 18px;\n    width: 90px;\n}\n.footer .btn-comment[data-v-597fa565]{\n    border:0;\n    margin-right: 10px;\n}\n.footer .btn-comment .icon[data-v-597fa565]{\n    font-size: 26px;\n    color: #929292;\n}\n.footer .btn-comment .text[data-v-597fa565]{\n    font-size: 14px;\n    color: #929292;\n    margin-top: -5px;\n}\n.input-text[data-v-597fa565] {\n    font-size: 18px;\n}\n.van-button[data-v-597fa565]{\n    width: 120px;\n    font-size: 17px;\n}\n.btn-footer[data-v-597fa565]{\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    background-color: #FFFFFF;\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    padding-bottom: 10px;\n}\n.file-box[data-v-597fa565] {\n    width: 79%;\n    display: flex;\n    flex-direction: column;\n}\n.file-item[data-v-597fa565] {\n    margin-bottom: 20px;\n    font-size: 18px;\n    font-style: italic;\n    color: #3a94ec;\n}\n.file-box > .file-item[data-v-597fa565]:last-child {\n    margin-bottom: 0;\n}\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css */ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.id, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = (__webpack_require__(/*! !../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js")["default"])
var update = add("de248ae8", content, false, {"sourceMap":false,"shadowMode":false});
// Hot Module Replacement
if(false) // removed by dead control flow
{}

/***/ }),

/***/ "./src/components/base.vue":
/*!*********************************!*\
  !*** ./src/components/base.vue ***!
  \*********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _base_vue_vue_type_template_id_6ba07e61__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.vue?vue&type=template&id=6ba07e61 */ "./src/components/base.vue?vue&type=template&id=6ba07e61");
/* harmony import */ var _base_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base.vue?vue&type=script&lang=js */ "./src/components/base.vue?vue&type=script&lang=js");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _base_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _base_vue_vue_type_template_id_6ba07e61__WEBPACK_IMPORTED_MODULE_0__.render,
  _base_vue_vue_type_template_id_6ba07e61__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/components/base.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/components/base.vue?vue&type=script&lang=js":
/*!*********************************************************!*\
  !*** ./src/components/base.vue?vue&type=script&lang=js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_base_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/components/base.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_base_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/components/base.vue?vue&type=template&id=6ba07e61":
/*!***************************************************************!*\
  !*** ./src/components/base.vue?vue&type=template&id=6ba07e61 ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_base_vue_vue_type_template_id_6ba07e61__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_base_vue_vue_type_template_id_6ba07e61__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_base_vue_vue_type_template_id_6ba07e61__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=template&id=6ba07e61 */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/components/base.vue?vue&type=template&id=6ba07e61");


/***/ }),

/***/ "./src/view/work/display.vue":
/*!***********************************!*\
  !*** ./src/view/work/display.vue ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _display_vue_vue_type_template_id_597fa565_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./display.vue?vue&type=template&id=597fa565&scoped=true */ "./src/view/work/display.vue?vue&type=template&id=597fa565&scoped=true");
/* harmony import */ var _display_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./display.vue?vue&type=script&lang=js */ "./src/view/work/display.vue?vue&type=script&lang=js");
/* harmony import */ var _display_vue_vue_type_style_index_0_id_597fa565_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css */ "./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");



;


/* normalize component */

var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _display_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _display_vue_vue_type_template_id_597fa565_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _display_vue_vue_type_template_id_597fa565_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "597fa565",
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/view/work/display.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/view/work/display.vue?vue&type=script&lang=js":
/*!***********************************************************!*\
  !*** ./src/view/work/display.vue?vue&type=script&lang=js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css":
/*!*******************************************************************************************!*\
  !*** ./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_style_index_0_id_597fa565_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css */ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=style&index=0&id=597fa565&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_style_index_0_id_597fa565_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_style_index_0_id_597fa565_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_style_index_0_id_597fa565_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_style_index_0_id_597fa565_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./src/view/work/display.vue?vue&type=template&id=597fa565&scoped=true":
/*!*****************************************************************************!*\
  !*** ./src/view/work/display.vue?vue&type=template&id=597fa565&scoped=true ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_template_id_597fa565_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_template_id_597fa565_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_display_vue_vue_type_template_id_597fa565_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./display.vue?vue&type=template&id=597fa565&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/work/display.vue?vue&type=template&id=597fa565&scoped=true");


/***/ })

}]);
//# sourceMappingURL=src_view_work_display_vue.js.map