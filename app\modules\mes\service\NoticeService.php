<?php
namespace Envsan\Modules\Mes\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesNotice;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Model\MesProductGoods;
use Envsan\Modules\Mes\Util\Constant;
use Envsan\Modules\Purchase\Model\PurchaseApplyEntrust;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Work\Service\DataCommonService;

class NoticeService extends BaseService
{
    public $page_id = 5;

    public function selectAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.plan_begin_date,
                t1.plan_end_date,
                t1.ext_val,
                t1.status,
                t1.status_name,
                t4.name as customer_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNotice', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't1.customer_id = t4.id', 't4')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function searchAll(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.remarks,
                t1.quantity,
                t1.status,
                t1.status_name,
                t1.product_id,
                t2.code as product_code,
                t2.name as product_name,
                t2.ext_val as product_ext_val,
                t3.name as customer_name,
                t4.code,
                t4.plan_begin_date,
                t4.plan_end_date,
                ifnull(t7.instock_cnt,0) as instock_cnt,
                ifnull(t5.error_cnt,0) as error_cnt,
                round((ifnull(t5.produce_cnt,0)/(t1.quantity * t6.bom_cnt)) * 100 ,2) as produce_rate
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t2.customer_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t1.notice_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewProduceBomCnt','t1.id = t5.notice_detail_id','t5')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewProductBomCnt','t1.product_id = t6.product_id','t6')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewInstockCnt','t1.id = t7.notice_detail_id','t7')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.status,t4.plan_end_date,t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectById($id)
    {
        return MesNotice::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return MesNotice::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

//    public function selectDetailByUid($uid)
//    {
//        return MesNoticeDetail::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
//    }

    public function create()
    {
        $row = new MesNotice();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($act, $row) {
            $type = $this->request->getPost('type', 'tstring');

            if ($type != 3) {

                $customer_id = $this->request->getPost('customer_id', 'tstring');
                $order_id = $this->request->getPost('order_id', 'tstring');
                $plan_begin_date = $this->request->getPost('plan_begin_date', 'tstring');
                $plan_end_date = $this->request->getPost('plan_end_date', 'tstring');
                $remarks = $this->request->getPost('remarks', 'tstring');
                $files = urldecode($this->request->getPost('files', 'tstring'));
                $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
                $detail_data = str_replace('%2B', '+', urldecode($this->request->getPost('detail_data', 'tstring')));
                if (empty($type)  || empty($plan_begin_date) || empty($plan_end_date)) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
                $files = CvtUtil::emptyToArray($files);
                $detail_data = CvtUtil::emptyToArray($detail_data);
                $ext_data = CvtUtil::emptyToArray($ext_data);
                $now = DateUtil::now();
                $user = SessionData::user();
                $row->order_id = CvtUtil::blankToNull($order_id);
                $row->customer_id = CvtUtil::blankToNull($customer_id);
                $row->plan_begin_date = $plan_begin_date;
                $row->plan_end_date = $plan_end_date;
                $row->remarks = CvtUtil::blankToNull($remarks);
                $row->files = CvtUtil::arrayToNull($files);
                $row->ext_data = CvtUtil::arrayToNull($ext_data);
                $table = new TableService();
                $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
                $row->detail_data = CvtUtil::arrayToNull($detail_data);
                if ($type == 1) {
                    $row->status = 10;
                } else {
                    $row->status = 20;
                }
                $row->status_name = Constant::$notice_status[$row->status];
                $row->update_date = $now;
                $row->update_by = $user->id;
                if ($act == 'create') {
                    $ss = new SequenceService();
                    $row->code = $ss->useSequence(9);
                    $row->uid = UUID::make();
                    $row->del_flag = 0;
                    $row->owner = $user->owner;
                }
                // save
                $row->save();
                // 提交
                if ($type == 2) {
                    $dcm = new DataCommonService();
                    $dcm->submitDesign($row->id, 4, $user->group_id);
                }

            } else {
                // 改变生产通知的数量
                $now = DateUtil::now();
                $user = SessionData::user();
                $detail_data = str_replace('%2B', '+', urldecode($this->request->getPost('detail_data', 'tstring')));
                $detail_data = CvtUtil::emptyToArray($detail_data);
                $row->detail_data = CvtUtil::arrayToNull($detail_data);
                $row->update_date = $now;
                $row->update_by = $user->id;
                $row->save();
                foreach ($detail_data as $detail_item){
                    $detail = MesNoticeDetail::findFirst(['del_flag = 0 and notice_id = ?1 and product_id = ?2', 'bind' => [1 => $row->id ,2 => $detail_item['id']]]);
                    $detail->quantity = $detail_item['quantity'];
                    $detail->remarks = $detail_item['remarks'];
                    $detail->update_date = $now;
                    $detail->update_by = $user->id;
                    $detail->save();
                    $list = $detail_item['list'];
                    foreach ($list as $item){
                        if ($item['sel'] == 1){
                            // 做成外委计划主表数据
                            $entrusts = PurchaseApplyEntrust::find(['del_flag = 0 and notice_id = ?1 and product_id = ?2 and type = 1', 'bind' => [1 => $row->id ,2 => $detail_item['id']]]);
                            foreach ($entrusts as $entrust){
                                $entrust->quantity = $detail->quantity;
                                $entrust->update_date = $now;
                                $entrust->update_by = $user->id;
                                $entrust->save();
                            }
                        }
                    }
                }
            }
        });
    }

    public function deleteByUid()
    {
        return $this->executeInTransaction(function () {
            $row = $this->selectByUid($this->request->getPost('uid', 'string'));
            if (empty($row) || $row->del_flag == 1) {
                return '';
            } else if ($row->status != 10) {
                return $this->error('生产计划已提交，不能删除');
            }

            $now = DateUtil::now();
            $user = SessionData::user();

            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->save();
        });
    }

    public function getCustomerList(){
        $builder = $this->modelsManager->createBuilder()
        ->columns('
            t1.id,
            t1.name
        ')
        ->addFrom('Envsan\Modules\Trade\Model\TradeCustomer', 't1')
        ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
        ->orderBy('t1.id desc');
        return $builder->getQuery()->execute();
    }

    public function getProductList($order_id = '', $customer_id = ''){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t3.id,
                t3.uid,
                t3.name,
                t3.code
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrder', 't1')
            ->innerJoin('Envsan\Modules\Trade\Model\TradeOrderDetail','t2.order_id = t1.id' , 't2')
            ->innerJoin('Envsan\Modules\Mes\Model\MesProduct', 't2.product_id = t3.id','t3')
            ->where('t1.del_flag = 0 and t3.del_flag = 0 and t3.status = 30');


        if (!CheckUtil::is_empty($order_id) && !CheckUtil::is_empty($customer_id)) {
            $builder->andWhere('t1.id = ?1 and t1.customer_id = ?2', [1 => $order_id, 2 => $customer_id]);
        }
        return $builder->getQuery()->execute();
    }

    public function getOrderList($customer_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrder', 't1')
            ->where('t1.del_flag = 0 and t1.status = 30 and t1.customer_id = ?1', [1 => $customer_id])
            ->orderBy('t1.id desc');
        return $builder->getQuery()->execute();
    }

    public function getBomList($product_id){
        $product_row = MesProduct::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$product_id]]);
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.name,
                t1.is_outsourcing as sel
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductBom', 't1')
            ->where('t1.del_flag = 0 and t1.product_id = ?1', [1 => $product_id])
            ->orderBy('t1.data_sort asc,t1.id asc');
        $rows = $builder->getQuery()->execute();
        return [
            'id' => $product_row->id,
            'uid' => $product_row->uid,
            'code' => $product_row->code,
            'name' => $product_row->name,
            'remarks' => '',
            'quantity' => '',
            'list' => $rows
        ];
    }

    public function passSave($id){
        $row = $this->selectById($id);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 20){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $detail_data = CvtUtil::emptyToArray($row->detail_data);
        foreach ($detail_data as $detail_item){
            $detail = new MesNoticeDetail();
            $detail->uid = 'pch' . mb_substr(UUID::make(),0,15);
            $detail->notice_id = $row->id;
            $detail->product_id = $detail_item['id'];
            $detail->quantity = $detail_item['quantity'];
            $detail->remarks = $detail_item['remarks'];
            $detail->status = 10;
            $detail->status_name = Constant::$notice_detail_status[$detail->status];
            $detail->update_date = $now;
            $detail->update_by = $user->id;
            $detail->del_flag = 0;
            $detail->owner = $user->owner;
            $detail->save();
            $list = $detail_item['list'];
            foreach ($list as $item){
                if ($item['sel'] == 1){
                    // 做成外委计划主表数据
                    $entrust = new PurchaseApplyEntrust();
                    $entrust->uid = UUID::make();
                    // 计划外委
                    $entrust->type = 1;
                    $ss = new SequenceService();
                    $entrust->code = $ss->useSequence(22);
                    $entrust->notice_id = $row->id;
                    $entrust->product_id = $detail->product_id;
                    $entrust->product_bom_id = $item['id'];
                    $entrust->product_bom_name = $item['name'];
                    $entrust->notice_detail_id = $detail->id;
                    $entrust->quantity = $detail->quantity;
                    $entrust->ordered_quantity = 0;
                    $entrust->remaining_quantity = $detail->quantity;
                    $entrust->status = 10;
                    $entrust->status_name = \Envsan\Modules\Purchase\Util\Constant::$purchase_apply_entrust_status[$entrust->status];
                    $entrust->update_date = $now;
                    $entrust->update_by = $user->id;
                    $entrust->del_flag = 0;
                    $entrust->owner = $user->owner;
                    $entrust->save();
                }
            }
        }
        $row->status = 30;
        $row->status_name = Constant::$notice_status[$row->status];
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->save();
    }

    public function rejectSave($id,$reject_remarks)
    {
        $row = $this->selectById($id);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 20){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->status = 10;
        $row->status_name = '被驳回';
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()) {
            throw new \Exception("【合同变更审批驳回】TradeOrderChange表更新失败");
        }
    }

    public function getDetailData($notice_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t2.name,
                t2.code,
                t1.quantity,
                t1.remarks
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNoticeDetail', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't1.product_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.notice_id = ?1', [1 => $notice_id])
            ->orderBy('t1.id asc');
        $details = $builder->getQuery()->execute();
        $details = $details->toArray();
        foreach ($details as &$detail){
            $detail['list'] = [];
            $entrusts = PurchaseApplyEntrust::find(['del_flag = 0 and notice_detail_id = ?1','bind'=>[1=>$detail['id']]]);
            foreach ($entrusts as $entrust){
                $detail['list'][] = [
                    'id' => $entrust->id,
                    'name' => $entrust->product_bom_name
                ];
            }
        }
        return $details;
    }

    public function getProduceData($notice_detail_id,$notice_code){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id as notice_detail_id,
                t1.bom_id,
                t1.bom_name,
                t1.data_sort,
                t1.bom_produce_cnt,
                t1.quantity,
                t1.start_date,
                t1.end_date,
                ifnull(t1.plan_cnt,0) as plan_cnt,
                round(ifnull(t1.produce_cnt,0),4) as produce_cnt,
                ifnull(t1.error_cnt,0) as error_cnt,
                ifnull(t1.error_rate,0) as error_rate,
                if(t2.id is null , 0 , 1) as entrust_flag 
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesViewProduce', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust','t1.bom_id = t2.product_bom_id and t2.del_flag = 0 and t2.notice_detail_id = t1.id','t2')
            ->where('t1.id = ?1', [1 => $notice_detail_id])
            ->orderBy('t1.data_sort asc,t1.id asc');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row){
            $sort = CvtUtil::emptyToDouble($row['data_sort']);
            if ($sort < 10){
                $sort = '0'.$sort;
            }
            $row['bom_code'] = $notice_code .$sort;
        }
        return $rows;
    }

    public function getQualityData($product_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.product_bom_id,
                t1.quality_template_name,
                t2.name as bom_name,
                t1.form_data
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductQuality', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.product_bom_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t2.del_flag = 0 and t2.product_id = ?1', [1 => $product_id])
            ->orderBy('t2.data_sort asc,t1.id asc');
        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row){
            $row['form_data'] = CvtUtil::emptyToArray($row['form_data']);
        }
        return $rows;
    }

    public function getCheckData(){
        $rtn = new \stdClass();
        $notice_detail_id = $this->request->getPost('notice_detail_id', 'tstring');
        $bom_id = $this->request->getPost('bom_id', 'tstring');
        $quality_template_id = $this->request->getPost('quality_template_id', 'tstring');
        if (empty($notice_detail_id) || empty($bom_id) || empty($quality_template_id)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $rtn->message = '';
        $rtn->data = $this->getCheckList($notice_detail_id,$bom_id,$quality_template_id);
        return $rtn;
    }

    public function getCheckList($notice_detail_id,$bom_id,$quality_template_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.work_date,
                t1.create_time,
                t1.check_data,
                t1.staff_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesCheckLogs', 't1')
            ->where('t1.del_flag = 0 and t1.notice_detail_id = ?1 and t1.bom_id = ?2 and t1.quality_template_id =?3',
                [1 => $notice_detail_id ,2 => $bom_id ,3 => $quality_template_id])
            ->orderBy('t1.work_date,t1.create_time asc,t1.id');
        $check_rows = $builder->getQuery()->execute()->toArray();
        $date_obj = [];
        foreach ($check_rows as $check_row){
            $check_data = CvtUtil::emptyToArray($check_row['check_data']);
            $key = '_' . strtotime($check_row['work_date']);
            $time = date('H:i',strtotime($check_row['create_time']));
            if (!array_key_exists($key,$date_obj)){
                $date_obj[$key] = [
                    'work_date' => $check_row['work_date'],
                    'staff_name' => $check_row['staff_name'],
                    'time_data' => json_decode(json_encode(Constant::$quality_times,JSON_UNESCAPED_UNICODE),true)
                ];
            }
            foreach ($date_obj[$key]['time_data'] as &$time_item){
                if ($time_item['begin'] < $time && $time <= $time_item['end']){
                    $time_item['check_data'] = $check_data;
                    break;
                }
            }
        }
        $date_list = [];
        foreach ($date_obj as $key => $data){
            $date_list[] = $data;
        }
        return $date_list;
    }


    /**
     * 取得订单数量，库存数量，报工数量，领料数量，生产通知数量
     * @param $product_id
     * @return \Phalcon\Mvc\Model\Query\Builder
     */
    public function selectOrderDetail($product_id)
    {
        // 产品名称
        $product_name =  $this->request->get('product_name', 'tstring');
        // 规格型号
        $product_model =  $this->request->get('product_model', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.price,
                t1.status,
                t1.cnt,
                t1.tax_rate,
                t1.inventory_unit,
                t2.sign_date,
                t2.code as order_code,
                t2.order_type_name,
                t3.id as product_id,
                t3.code as product_code,
                t3.name as product_name,
                t3.remarks,
                null as quantity,
                t4.code as goods_code,
                t4.name as goods_name,
                t4.model as goods_model,
                t5.in_cnt,
                t5.out_cnt,
                t5.cnt as stock_cnt
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrderDetail', 't1')
            ->innerJoin('Envsan\Modules\Trade\Model\TradeOrder','t1.order_id = t2.id and t2.del_flag = 0','t2')
            ->innerJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t3.id and t3.del_flag = 0','t3')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseGoods','t3.goods_id = t4.id and t4.del_flag = 0','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewStock','t1.product_id = t5.product_id','t5')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.status in (?2,?3) and t2.status = ?4 and t3.id = ?5 and t3.status = ?6',
                [1 => SessionData::ownerId(), 2 => '20', 3 => '30', 4 => '30', 5 => $product_id, 6 => '30'])
            ->orderBy('t2.sign_date asc');

        if (!empty($product_name)) {
            $builder->andWhere("t3.name = ?7", [7 => $product_name]);
        }

        if (!empty($product_model)) {
            $builder->andWhere("t3.code = ?8", [8 => $product_model]);
        }
        return $builder;
    }

    /**
     * 根据产品ID获取原材料清单
     * @param $product_id
     * @return array
     */
    public function getMaterialsByProductId($product_id)
    {
        // 通过mes_product_goods关联表查询原材料
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t2.name as material_name,
                t2.code as material_code,
                t2.model as material_model,
                t1.quantity as required_qty,
                ifnull(t3.quantity, 0) as stock_qty,
                t2.unit,
                t1.remarks
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductGoods', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods', 't1.goods_id = t2.id and t2.del_flag = 0', 't2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewStock', 't2.id = t3.goods_id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.product_id = ?2', 
                [1 => SessionData::ownerId(), 2 => $product_id])
            ->orderBy('t1.id asc');

        $materials = $builder->getQuery()->execute();
        
        if (count($materials) == 0) {
            // 如果没有找到原材料，返回空数组
            return [];
        }
        
        return $materials->toArray();
    }

    public function getProductCount($product_id)
    {
        // 先检查mes_plan表结构
        try {
            $sql_test = "DESCRIBE mes_plan";
            $table_structure = $this->db->fetchAll($sql_test, \Phalcon\Db::FETCH_ASSOC);
            Logger::error("mes_plan table structure: " . json_encode($table_structure));
        } catch (\Exception $e) {
            Logger::error("Error checking table structure: " . $e->getMessage());
        }

        // 修改查询：添加mes_notice_detail按产品分组的数量统计
        $sql = "SELECT
                    plan_stats.max_plan_cnt,
                    plan_stats.total_plan_cnt,
                    produce_stats.max_finish_cnt,
                    produce_stats.total_error_cnt,
                    notice_stats.total_notice_detail_cnt,
                    notice_stats.total_notice_quantity
                FROM (
                    -- 计划数据聚合（一次查询计算 MAX 和 SUM）
                    SELECT
                        MAX(sub.plan_cnt) as max_plan_cnt,
                        SUM(sub.plan_cnt) as total_plan_cnt
                    FROM (
                        SELECT IF((t1.plan_type = 2), MAX(t1.plan_cnt), SUM(t1.plan_cnt)) AS plan_cnt
                        FROM mes_plan t1
                        LEFT JOIN mes_notice_detail t2 ON t1.notice_detail_id = t2.id
                            AND t2.product_id = ? AND t2.del_flag = 0
                        WHERE t1.del_flag = 0
                            AND t1.product_id = ?
                            AND t1.owner = ?
                            AND t1.plan_date >= CURDATE()
                            AND t2.status = ?
                        GROUP BY t1.bom_id
                    ) sub
                ) plan_stats
                CROSS JOIN (
                    -- 生产数据聚合（一次查询计算 MAX 和 SUM）
                    SELECT
                        MAX(sub.cnt) as max_finish_cnt,
                        SUM(sub.error_cnt) as total_error_cnt
                    FROM (
                        SELECT t1.bom_id, t1.cnt, t1.error_cnt
                        FROM mes_produce_logs t1
                        LEFT JOIN mes_notice_detail t2 ON t1.notice_detail_id = t2.id
                            AND t2.product_id = ? AND t2.del_flag = 0
                        LEFT JOIN mes_product_bom t3 ON t1.bom_id = t3.id
                            AND t3.product_id = ? AND t3.del_flag = 0
                        WHERE t1.del_flag = 0
                            AND t1.owner = ?
                            AND t1.work_date < CURDATE()
                            AND t2.status = ?
                        GROUP BY t1.bom_id
                    ) sub
                ) produce_stats
                CROSS JOIN (
                    -- 生产通知详情数据聚合（按产品分组的数量统计）
                    SELECT
                        COUNT(*) as total_notice_detail_cnt,
                        SUM(IFNULL(quantity, 0)) as total_notice_quantity
                    FROM mes_notice_detail t1
                    WHERE t1.del_flag = 0
                        AND t1.owner = ?
                        AND t1.product_id = ?
                        AND t1.status = ?
                ) notice_stats";

        $result = $this->db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, [
            // 计划查询参数
            $product_id, $product_id, SessionData::ownerId(), '10',
            // 生产查询参数
            $product_id, $product_id , SessionData::ownerId(), '10',
            // 生产通知详情查询参数
            SessionData::ownerId(), $product_id, '10'
        ]);

        return $result;
    }

    public function getAllActiveNotice(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.code,
                t1.customer_id,
                t4.name as customer_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNotice', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't1.customer_id = t4.id', 't4')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.status = ?2', [1 => SessionData::ownerId(), 2 => '30'])
            ->orderBy('t1.id desc');

        return $builder->getQuery()->execute()->toArray();
    }

    public function stop()
    {
        return $this->executeInTransaction(function () {
            $row = $this->selectByUid($this->request->getPost('uid', 'string'));
            if (empty($row) || $row->del_flag == 1) {
                return $this->error('没有生产计划');
            } else if ($row->status != 30) {
                return $this->error('生产计划没有已提交，不能停产');
            }

            $now = DateUtil::now();
            $user = SessionData::user();

            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->status = 40;
            $row->status_name = Constant::$notice_status[$row->status];
            $row->save();

            // 停产需要考虑的内容是什么
            $mesNoticeDetails = MesNoticeDetail::find(['notice_id = ?1 and del_flag = 0', 'bind' => [1 => $row->id]]);
            foreach ($mesNoticeDetails as $mesNoticeDetail) {
                $mesNoticeDetail->update_date = $now;
                $mesNoticeDetail->update_by = $user->id;
                $mesNoticeDetail->status = 40;
                $mesNoticeDetail->status_name = Constant::$notice_detail_status[$mesNoticeDetail->status];
                $mesNoticeDetail->save();
            }
        });
    }

    public function restart()
    {
        return $this->executeInTransaction(function () {
            $row = $this->selectByUid($this->request->getPost('uid', 'string'));
            if (empty($row) || $row->del_flag == 1) {
                return $this->error('没有生产计划');
            } else if ($row->status != 40) {
                return $this->error('没有停产的生产计划，不用再次恢复了');
            }

            $now = DateUtil::now();
            $user = SessionData::user();

            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->status = 30;
            $row->status_name = '恢复生产';
            $row->save();

            // 停产需要考虑的内容是什么
            $mesNoticeDetails = MesNoticeDetail::find(['notice_id = ?1 and del_flag = 0', 'bind' => [1 => $row->id]]);
            foreach ($mesNoticeDetails as $mesNoticeDetail) {
                $mesNoticeDetail->update_date = $now;
                $mesNoticeDetail->update_by = $user->id;
                $mesNoticeDetail->status = 10;
                $mesNoticeDetail->status_name = Constant::$notice_detail_status[$mesNoticeDetail->status];
                $mesNoticeDetail->save();
            }
        });
    }
}