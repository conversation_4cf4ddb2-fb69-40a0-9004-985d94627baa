{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.worker.js') %}
<!-- Main content -->
<div id="app" style="background-color: #f2f2f2">
    <div class="pdf-viewer">
        <div class="toolbar">
            <button @click="prevPage" :disabled="pageNumber <= 1">上一页</button>
            <span>第 ${pageNumber} 页 / 共 ${numPages} 页</span>
            <button @click="nextPage" :disabled="pageNumber >= numPages">下一页</button>
            <button @click="fontChange(1)">放大</button>
            <button @click="fontChange(2)">缩小</button>
        </div>
        <div class="pdf-container">
            <canvas id="pdfCanvas"></canvas>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            src:'{{ pdf }}',
            pdfDoc: null,
            pageNumber: 1,
            numPages: 0,
            scale: 1,
            renderTask: null,
            pageRendering: false,
            pageNumPending: null
        },
        created() {
            this.loadPDF();
        },
        methods: {
            loadPDF() {
                pdfjsLib.getDocument(this.src).promise.then((pdfDoc_) => {
                    this.pdfDoc = pdfDoc_;
                    this.numPages = this.pdfDoc.numPages;
                    this.renderPage();
                });
            },
            fontChange(type){
                if (type == 1){
                    if (this.scale >= 3){
                        return;
                    }
                    this.scale += 0.25
                } else {
                    if (this.scale <= 1){
                        return;
                    }
                    this.scale -= 0.25
                }
                this.renderPage();
            },
            renderPage() {
                if (!this.pdfDoc) return;
                this.pageRendering = true;
                if (this.renderTask) {
                    this.renderTask.cancel();
                }
                this.pdfDoc.getPage(this.pageNumber).then((page) => {
                    const viewport = page.getViewport({ scale: this.scale });
                    const canvas = document.getElementById('pdfCanvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
                    this.renderTask = page.render({
                        canvasContext: context,
                        viewport: viewport
                    });
                    this.renderTask.promise.then(() => {
                        this.pageRendering = false;
                        // 如果有等待中的页面渲染请求
                        if (this.pageNumPending !== null) {
                            this.pageNumber = this.pageNumPending;
                            this.pageNumPending = null;
                            this.renderPage();
                        }
                    });
                });
            },
            queueRenderPage(num) {
                if (this.pageRendering) {
                    this.pageNumPending = num;
                } else {
                    this.pageNumber = num;
                    this.renderPage();
                }
            },
            prevPage() {
                if (this.pageNumber <= 1) return;
                this.queueRenderPage(this.pageNumber - 1);
            },
            nextPage() {
                if (this.pageNumber >= this.numPages) return;
                this.queueRenderPage(this.pageNumber + 1);
            }
        }
    });
</script>
<style scoped>
    .pdf-viewer {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .toolbar {
        padding: 10px;
        background: #f0f0f0;
        display: flex;
        gap: 10px;
        align-items: center;
        height: 5vh;;
    }

    .pdf-container {
        width: 100vw;
        height: 95vh;
        overflow: auto;
        border: 1px solid #ddd;
    }

    canvas {
        display: block;
        margin: 0 auto;
    }
</style>
