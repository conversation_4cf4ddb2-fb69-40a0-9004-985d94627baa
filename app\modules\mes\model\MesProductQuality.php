<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class MesProductQuality extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_bom_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quality_template_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $quality_template_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $form_data;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_product_quality';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProductQuality[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesProductQuality
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
