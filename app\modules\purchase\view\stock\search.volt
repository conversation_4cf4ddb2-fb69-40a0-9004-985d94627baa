{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">库存履历查询</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">物资</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="name" v-model.trim="name" @keyup.enter="search"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">操作类型</label>
                            <div class="col-md-9">
                                <select class="form-control bs-select" name="data_type" v-model="data_type">
                                    <option value="">全部</option>
                                    {% for key,value in stock_types %}
                                        <option value="{{ key }}">{{ value }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">业务单号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="batch_no" v-model.trim="batch_no" placeholder="请输业务单号"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">操作时间</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control date dtpicker" name="date_start" v-model="date_start" placeholder="开始时间"/>
                                    <span class="input-group-addon no-border-lr">至</span>
                                    <input type="text" class="form-control date dtpicker" name="date_end" v-model="date_end" placeholder="结束时间"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('purchase/stock/search/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="code">物资编码</th>
                    <th data-field="name">物资名称</th>
                    <th data-field="spec">规格</th>
                    <th data-field="model">型号</th>
                    <th data-field="data_type" data-formatter="dataFormatter">操作类型</th>
                    <th data-field="batch_no">业务单号</th>
                    <th data-field="quantity">入出数量</th>
                    <th data-field="unit">数量单位</th>
                    <th data-field="money">金额(元)</th>
                    <th data-field="update_date">操作时间</th>
                    <th data-field="update_user">操作人</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var stock_types = {{ stock_types | json_encode }};
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            name: '',
            data_type: '',
            batch_no: '',
            date_start:'',
            date_end:''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                p.name = this.name;
                p.data_type = this.data_type;
                p.batch_no = this.batch_no;
                p.date_start = this.date_start;
                p.date_end = this.date_end;
                return p;
            },
            reset: function() {
                this.name = '';
                this.data_type = '';
                this.date_start = '';
                this.date_end = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable();

    function dataFormatter(v) {
        return stock_types[v];
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>