<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseRequest extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;


    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $notice_id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $apply_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $purchase_type;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $purchase_type_name;


    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $notice_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $apply_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=500, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $detail_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $customer_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $create_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $create_by;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_request';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseRequest[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseRequest
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
