<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesViewProduce extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $bom_id;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $bom_name;

    /**
     *
     * @var double
     * @Column(type="double", nullable=true)
     */
    public $data_sort;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $bom_produce_cnt;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $start_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $end_date;

    /**
     *
     * @var double
     * @Column(type="double", length=32, nullable=true)
     */
    public $plan_cnt;

    /**
     *
     * @var string
     * @Column(type="string", length=21, nullable=true)
     */
    public $produce_cnt;

    /**
     *
     * @var double
     * @Column(type="double", length=32, nullable=true)
     */
    public $error_cnt;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $error_rate;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $drawing_data;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_view_produce';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewProduce[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesViewProduce
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
