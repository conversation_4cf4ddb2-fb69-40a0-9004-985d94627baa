<?php
namespace Envsan\Modules\Quality\Util;

class Constant
{
    public static $formula_input = ['1','2','3','4','5','6','7','8','9','0','.','+','-','*','/','(',')'];

    public static $template_types = [
        1 => '产品检验',
        2 => '原材料检验'
    ];

    public static $input_types = [
        1 => '文本',
        2 => '数字',
        3 => '单选列表',
        4 => '多选列表',
        5 => '多行文本',
        6 => '选择判断项',
        7 => '公式判断项',
        8 => '尺寸判断项'
    ];

    public static $data_template = [
        'id' => '',
        'type' => '',
        'name' => '',
        'title' => '',
        'unit' => '',
        'required' => '0',
        'explain' => '',
        'formula_list' => [],
        'formula_val' => '',
        'standard_val' => '',
        'standard_plus' => '',
        'standard_minus' => '',
        'input_cnt' => 1,
        'list' => [],
        'value' => '',
        'values' => [],
        'result' => 0,
        'results' => [],
    ];
}