{"version": 3, "file": "js/src_view_review_search_vue.js", "mappings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kBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/review/search.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/review/search.vue", "webpack://rrts-manager/./src/view/review/search.vue?f299", "webpack://rrts-manager/./src/view/review/search.vue?e3df", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/review/search.vue?c879", "webpack://rrts-manager/./src/view/review/search.vue?190c", "webpack://rrts-manager/./src/view/review/search.vue?2218", "webpack://rrts-manager/./src/view/review/search.vue?780a"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div>\r\n        <m-header :is_back=\"true\" name=\"审批查询\">\r\n            <div slot=\"right\" style=\"padding-right: 15px\">\r\n                <button slot=\"right\" class=\"btn-search\" @click=\"search\"><van-icon name=\"search\"/> 查询</button>\r\n            </div>\r\n        </m-header>\r\n        <div>\r\n            <van-cell-group>\r\n                <van-field\r\n                        input-align=\"right\"\r\n                        v-model=\"param.type_name\"\r\n                        name=\"审批名称\"\r\n                        label=\"审批名称\"\r\n                        :is-link=\"true\"\r\n                        placeholder=\"请输入审批名称关键字\"\r\n                />\r\n                <van-field\r\n                        input-align=\"right\"\r\n                        v-model=\"param.content\"\r\n                        name=\"审批内容\"\r\n                        label=\"审批内容\"\r\n                        :is-link=\"true\"\r\n                        placeholder=\"请输入审批内容关键字\"\r\n                />\r\n                <van-field\r\n                        input-align=\"right\"\r\n                        @click-input=\"status_show = true\"\r\n                        name=\"审批状态\"\r\n                        label=\"审批状态\"\r\n                        :is-link=\"true\"\r\n                        v-model=\"param.status_text\"\r\n                        placeholder=\"请选择审批状态\"\r\n                        readonly\r\n                />\r\n                <van-cell title=\"发起日期\">\r\n                    <template #extra>\r\n                        <div class=\"\" @click=\"startDateShow\">\r\n                            <span style=\"color: #aaaaaa\" v-if=\"!param.date_start\">选择起始日期</span>\r\n                            <div v-else style=\"display: flex\">\r\n                                <div class=\"txt-date\" v-text=\"param.date_start\"></div>\r\n                                <div class=\"btn-clear\" style=\"margin-left: 10px\">\r\n                                    <van-icon name=\"clear\" color=\"red\" @click.stop=\"clearDateStart\"/>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"padding: 0 10px\">\r\n                            <span> ~ </span>\r\n                        </div>\r\n                        <div class=\"search-col date\" @click=\"endDateShow\">\r\n                            <span style=\"color: #aaaaaa\" v-if=\"!param.date_end\">选择截止日期</span>\r\n                            <div v-else style=\"display: flex\">\r\n                                <div class=\"txt-date\" v-text=\"param.date_end\"></div>\r\n                                <div class=\"btn-clear\" style=\"margin-left: 10px\">\r\n                                    <van-icon name=\"clear\" color=\"red\" @click.stop=\"clearDateEnd\"/>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </template>\r\n                </van-cell>\r\n            </van-cell-group>\r\n        </div>\r\n        <div class=\"review\" @scroll=\"scroll\" ref=\"scroll\">\r\n            <van-list\r\n                    :immediate-check=\"false\"\r\n                    v-model=\"list_loading\"\r\n                    :finished=\"list_finished\"\r\n                    finished-text=\"没有更多了\"\r\n                    :offset=\"20\"\r\n                    @load=\"more\"\r\n            >\r\n                <div v-for=\"(item,index) in list\" :key=\"index\" class=\"review-content\">\r\n                    <div class=\"title\">\r\n                        {{item.type_name}}\r\n                        <span v-if=\"item.review_type == 1\" style=\"color:red;\" >(撤销审批)</span>\r\n                        <van-tag v-if=\"item.status == 15\" type=\"primary\" size=\"large\">审批中</van-tag>\r\n                        <van-tag v-if=\"item.handle_status == 1\" type=\"success\" size=\"large\">通过</van-tag>\r\n                        <van-tag v-if=\"item.handle_status == 2\" type=\"danger\" size=\"large\">拒绝</van-tag>\r\n                        <van-tag v-if=\"item.handle_status == 3\" type=\"danger\" size=\"large\">撤回</van-tag>\r\n                        <van-tag v-if=\"item.pressing_flag > 0\" type=\"danger\" size=\"large\">催办 +{{item.pressing_flag}}</van-tag>\r\n                        <van-tag v-if=\"item.read_flag == 0\" type=\"danger\" size=\"large\">未读</van-tag>\r\n                    </div>\r\n                    <div class=\"content\">\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                部门 :\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                {{item.group_name}}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"content\">\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                提交人 :\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                {{item.create_name}} {{item.create_date}}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div  class=\"content\" v-for=\" (abstrakt_item,abstrakt_index) in item.abstrakt_data\" :key=\"abstrakt_index\">\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                {{abstrakt_item.name}} :\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                {{abstrakt_item.value}}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div v-if=\"item.remarks != ''\" class=\"content\">\r\n                        <div class=\"item\">\r\n                            <div class=\"title2\">\r\n                                备注 :\r\n                            </div>\r\n                            <div class=\"value\">\r\n                                {{item.remarks}}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <van-cell title=\"查看详情\" title-style=\"font-size:16px\" is-link @click=\"view(item)\" />\r\n                </div>\r\n            </van-list>\r\n        </div>\r\n        <van-popup v-model=\"date_start_show\" position=\"bottom\">\r\n            <van-datetime-picker\r\n                    type=\"date\"\r\n                    v-model=\"dateValue\"\r\n                    title=\"选择起始日期\"\r\n                    :min-date=\"minDate\"\r\n                    :max-date=\"maxDate\"\r\n                    @confirm=\"onDateStartConfirm\"\r\n                    @cancel=\"onDateStartCancel\"\r\n            />\r\n        </van-popup>\r\n        <van-popup v-model=\"date_end_show\" position=\"bottom\">\r\n            <van-datetime-picker\r\n                    type=\"date\"\r\n                    v-model=\"dateValue\"\r\n                    title=\"选择截止日期\"\r\n                    :min-date=\"minDate\"\r\n                    :max-date=\"maxDate\"\r\n                    @confirm=\"onDateEndConfirm\"\r\n                    @cancel=\"onDateEndCancel\"\r\n            />\r\n        </van-popup>\r\n        <van-popup\r\n                v-model=\"status_show\"\r\n                position=\"bottom\"\r\n                style=\"height: 30%;border-top-left-radius:10px;border-top-right-radius:10px;padding: 20px\">\r\n            <van-picker\r\n                    title=\"选择状态\"\r\n                    show-toolbar\r\n                    :columns=\"status_list\"\r\n                    @cancel= \"status_show = false\"\r\n                    @confirm=\"selectStatus\"\r\n            >\r\n                <template #columns-bottom>\r\n                    <div style=\"padding-top: 10px\">\r\n                        <van-button round block type=\"default\" @click=\"selectStatus(null)\">取消选择</van-button>\r\n                    </div>\r\n                </template>\r\n            </van-picker>\r\n        </van-popup>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import base from '../../components/base';\r\n    import m_header from '../../components/header';\r\n    let search_param_default = {\r\n        type_name: '',\r\n        content: '',\r\n        status: '',\r\n        status_text: '全部',\r\n        date_start: '',\r\n        date_end: ''\r\n    };\r\n    export default {\r\n        extends: base,\r\n        name: \"reviewSearch\",\r\n        data () {\r\n            return {\r\n                offset:0,\r\n                limit:10,\r\n                pos:0,\r\n                list_loading: false,\r\n                list_finished: false,\r\n                list:[],\r\n                param: JSON.parse(JSON.stringify(search_param_default)),\r\n                date_start_show: false,\r\n                date_end_show: false,\r\n                minDate: new Date(2000, 0, 1),\r\n                maxDate: new Date(2100, 11, 31),\r\n                dateValue:new Date(2025, 11, 31),\r\n                status_show:false,\r\n                status_list:[{id:'',text:'全部'},{id:1,text:'进行中'},{id:2,text:'通过'},{id:2,text:'拒绝'}],\r\n            };\r\n        },\r\n        components: {\r\n            m_header\r\n        },\r\n        created(){\r\n\r\n        },\r\n        methods:{\r\n            onLoad(){\r\n                this.list = [];\r\n                this.param = JSON.parse(JSON.stringify(search_param_default));\r\n            },\r\n            onShow(){\r\n\r\n            },\r\n            search(){\r\n                this.offset = 0;\r\n                this.list_loading = true;\r\n                this.list_finished = false;\r\n                let me = this;\r\n                this.$http.get('work/review/search?limit='+this.limit+'&offset='+this.offset, this.param).then((rs) => {\r\n                    let data = rs.data;\r\n                    me.list_loading = false;\r\n                    me.list = data.rows;\r\n                    if (data.rows.length < this.limit){\r\n                        me.list_finished = true;\r\n                    } else {\r\n                        me.offset += data.paginator.limit;\r\n                    }\r\n                });\r\n            },\r\n            more(){\r\n                this.list_loading = true;\r\n                this.list_finished = false;\r\n                let me = this;\r\n                this.$http.get('work/review/search?limit='+this.limit+'&offset='+this.offset, this.param).then((rs) => {\r\n                    console.log(rs);\r\n                    let data = rs.data;\r\n                    me.list_loading = false;\r\n                    for(let i =0; i < data.rows.length; i++){\r\n                        me.list.push(data.rows[i]);\r\n                    }\r\n                    if (data.rows.length < this.limit){\r\n                        me.list_finished = true;\r\n                    } else {\r\n                        me.offset += data.paginator.limit;\r\n                    }\r\n                });\r\n            },\r\n            scroll(event) {\r\n                this.pos = event.target.scrollTop;\r\n            },\r\n            view(item){\r\n                this.$router.push({name: 'work',params: { uid: item.uid,type : 2,src : 1}});\r\n            },\r\n            reset() {\r\n                this.param = JSON.parse(JSON.stringify(search_param_default));\r\n            },\r\n            startDateShow(){\r\n                if (this.param.date_start == ''){\r\n                    this.dateValue = new Date(new Date().setDate((new Date().getDate()-7)));\r\n                } else {\r\n                    this.dateValue = new Date(this.param.date_start);\r\n                }\r\n                this.date_start_show = true;\r\n            },\r\n            endDateShow(){\r\n                if (this.param.date_end == ''){\r\n                    this.dateValue = new Date();\r\n                } else {\r\n                    this.dateValue = new Date(this.param.date_end);\r\n                }\r\n                this.date_end_show = true;\r\n            },\r\n            onDateStartConfirm(date) {\r\n                this.date_start_show = false;\r\n                this.param.date_start = date.Format('yyyy-MM-dd');\r\n            },\r\n            onDateEndConfirm(date) {\r\n                this.date_end_show = false;\r\n                this.param.date_end = date.Format('yyyy-MM-dd');\r\n            },\r\n            onDateStartCancel() {\r\n                this.date_start_show = false;\r\n            },\r\n            onDateEndCancel() {\r\n                this.date_end_show = false;\r\n            },\r\n            clearDateStart() {\r\n                this.param.date_start = '';\r\n            },\r\n            clearDateEnd() {\r\n                this.param.date_end = '';\r\n            },\r\n            selectStatus(data){\r\n                this.param.status = data.id;\r\n                this.param.status_text = data.text;\r\n                this.status_show = false;\r\n            }\r\n        }\r\n    }\r\n\r\n</script>\r\n<style scoped>\r\n    .btn-search {\r\n        border: 0;\r\n        background-color: transparent;\r\n        color: #FFFFFF;\r\n    }\r\n\r\n    .review{\r\n        width: 100%;\r\n        height: calc(100vh - 250px);\r\n        overflow: auto;\r\n    }\r\n\r\n    .review-content{\r\n        margin: 15px;\r\n        background-color: #FFFFFF;\r\n        border-radius: 6px;\r\n        overflow: hidden;\r\n        position: relative;\r\n    }\r\n\r\n    .review-content .title{\r\n        color: #000000;\r\n        padding: 10px 15px;\r\n        font-size: 18px;\r\n    }\r\n\r\n    .review-content .reject{\r\n        position: absolute;\r\n        top:22px;\r\n        right: 0;\r\n        width: 80px;\r\n        height: 30px;\r\n        transform:rotate(40deg)\r\n    }\r\n\r\n    .review-content .content{\r\n        border-bottom: 1px #F2F2F2 solid;\r\n        padding: 5px 0;\r\n    }\r\n\r\n    .review-content .content .item{\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-start;\r\n        padding: 1px 15px;\r\n    }\r\n    .review-content .content .item .title2{\r\n        width: 100px;\r\n        height: 25px;\r\n        line-height: 25px;\r\n        vertical-align: center;\r\n        color: #888888;\r\n    }\r\n\r\n    .review-content .content .item .value{\r\n        height: 25px;\r\n        line-height: 25px;\r\n        vertical-align: center;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('m-header',{attrs:{\"is_back\":true,\"name\":\"审批查询\"}},[_c('div',{staticStyle:{\"padding-right\":\"15px\"},attrs:{\"slot\":\"right\"},slot:\"right\"},[_c('button',{staticClass:\"btn-search\",attrs:{\"slot\":\"right\"},on:{\"click\":_vm.search},slot:\"right\"},[_c('van-icon',{attrs:{\"name\":\"search\"}}),_vm._v(\" 查询\")],1)])]),_c('div',[_c('van-cell-group',[_c('van-field',{attrs:{\"input-align\":\"right\",\"name\":\"审批名称\",\"label\":\"审批名称\",\"is-link\":true,\"placeholder\":\"请输入审批名称关键字\"},model:{value:(_vm.param.type_name),callback:function ($$v) {_vm.$set(_vm.param, \"type_name\", $$v)},expression:\"param.type_name\"}}),_c('van-field',{attrs:{\"input-align\":\"right\",\"name\":\"审批内容\",\"label\":\"审批内容\",\"is-link\":true,\"placeholder\":\"请输入审批内容关键字\"},model:{value:(_vm.param.content),callback:function ($$v) {_vm.$set(_vm.param, \"content\", $$v)},expression:\"param.content\"}}),_c('van-field',{attrs:{\"input-align\":\"right\",\"name\":\"审批状态\",\"label\":\"审批状态\",\"is-link\":true,\"placeholder\":\"请选择审批状态\",\"readonly\":\"\"},on:{\"click-input\":function($event){_vm.status_show = true}},model:{value:(_vm.param.status_text),callback:function ($$v) {_vm.$set(_vm.param, \"status_text\", $$v)},expression:\"param.status_text\"}}),_c('van-cell',{attrs:{\"title\":\"发起日期\"},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('div',{on:{\"click\":_vm.startDateShow}},[(!_vm.param.date_start)?_c('span',{staticStyle:{\"color\":\"#aaaaaa\"}},[_vm._v(\"选择起始日期\")]):_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',{staticClass:\"txt-date\",domProps:{\"textContent\":_vm._s(_vm.param.date_start)}}),_c('div',{staticClass:\"btn-clear\",staticStyle:{\"margin-left\":\"10px\"}},[_c('van-icon',{attrs:{\"name\":\"clear\",\"color\":\"red\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.clearDateStart.apply(null, arguments)}}})],1)])]),_c('div',{staticStyle:{\"padding\":\"0 10px\"}},[_c('span',[_vm._v(\" ~ \")])]),_c('div',{staticClass:\"search-col date\",on:{\"click\":_vm.endDateShow}},[(!_vm.param.date_end)?_c('span',{staticStyle:{\"color\":\"#aaaaaa\"}},[_vm._v(\"选择截止日期\")]):_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('div',{staticClass:\"txt-date\",domProps:{\"textContent\":_vm._s(_vm.param.date_end)}}),_c('div',{staticClass:\"btn-clear\",staticStyle:{\"margin-left\":\"10px\"}},[_c('van-icon',{attrs:{\"name\":\"clear\",\"color\":\"red\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.clearDateEnd.apply(null, arguments)}}})],1)])])]},proxy:true}])})],1)],1),_c('div',{ref:\"scroll\",staticClass:\"review\",on:{\"scroll\":_vm.scroll}},[_c('van-list',{attrs:{\"immediate-check\":false,\"finished\":_vm.list_finished,\"finished-text\":\"没有更多了\",\"offset\":20},on:{\"load\":_vm.more},model:{value:(_vm.list_loading),callback:function ($$v) {_vm.list_loading=$$v},expression:\"list_loading\"}},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"review-content\"},[_c('div',{staticClass:\"title\"},[_vm._v(\" \"+_vm._s(item.type_name)+\" \"),(item.review_type == 1)?_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(\"(撤销审批)\")]):_vm._e(),(item.status == 15)?_c('van-tag',{attrs:{\"type\":\"primary\",\"size\":\"large\"}},[_vm._v(\"审批中\")]):_vm._e(),(item.handle_status == 1)?_c('van-tag',{attrs:{\"type\":\"success\",\"size\":\"large\"}},[_vm._v(\"通过\")]):_vm._e(),(item.handle_status == 2)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"拒绝\")]):_vm._e(),(item.handle_status == 3)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"撤回\")]):_vm._e(),(item.pressing_flag > 0)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"催办 +\"+_vm._s(item.pressing_flag))]):_vm._e(),(item.read_flag == 0)?_c('van-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"未读\")]):_vm._e()],1),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 部门 : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(item.group_name)+\" \")])])]),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 提交人 : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(item.create_name)+\" \"+_vm._s(item.create_date)+\" \")])])]),_vm._l((item.abstrakt_data),function(abstrakt_item,abstrakt_index){return _c('div',{key:abstrakt_index,staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" \"+_vm._s(abstrakt_item.name)+\" : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(abstrakt_item.value)+\" \")])])])}),(item.remarks != '')?_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"title2\"},[_vm._v(\" 备注 : \")]),_c('div',{staticClass:\"value\"},[_vm._v(\" \"+_vm._s(item.remarks)+\" \")])])]):_vm._e(),_c('van-cell',{attrs:{\"title\":\"查看详情\",\"title-style\":\"font-size:16px\",\"is-link\":\"\"},on:{\"click\":function($event){return _vm.view(item)}}})],2)}),0)],1),_c('van-popup',{attrs:{\"position\":\"bottom\"},model:{value:(_vm.date_start_show),callback:function ($$v) {_vm.date_start_show=$$v},expression:\"date_start_show\"}},[_c('van-datetime-picker',{attrs:{\"type\":\"date\",\"title\":\"选择起始日期\",\"min-date\":_vm.minDate,\"max-date\":_vm.maxDate},on:{\"confirm\":_vm.onDateStartConfirm,\"cancel\":_vm.onDateStartCancel},model:{value:(_vm.dateValue),callback:function ($$v) {_vm.dateValue=$$v},expression:\"dateValue\"}})],1),_c('van-popup',{attrs:{\"position\":\"bottom\"},model:{value:(_vm.date_end_show),callback:function ($$v) {_vm.date_end_show=$$v},expression:\"date_end_show\"}},[_c('van-datetime-picker',{attrs:{\"type\":\"date\",\"title\":\"选择截止日期\",\"min-date\":_vm.minDate,\"max-date\":_vm.maxDate},on:{\"confirm\":_vm.onDateEndConfirm,\"cancel\":_vm.onDateEndCancel},model:{value:(_vm.dateValue),callback:function ($$v) {_vm.dateValue=$$v},expression:\"dateValue\"}})],1),_c('van-popup',{staticStyle:{\"height\":\"30%\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.status_show),callback:function ($$v) {_vm.status_show=$$v},expression:\"status_show\"}},[_c('van-picker',{attrs:{\"title\":\"选择状态\",\"show-toolbar\":\"\",\"columns\":_vm.status_list},on:{\"cancel\":function($event){_vm.status_show = false},\"confirm\":_vm.selectStatus},scopedSlots:_vm._u([{key:\"columns-bottom\",fn:function(){return [_c('div',{staticStyle:{\"padding-top\":\"10px\"}},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"default\"},on:{\"click\":function($event){return _vm.selectStatus(null)}}},[_vm._v(\"取消选择\")])],1)]},proxy:true}])})],1)],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.btn-search[data-v-6f510124] {\\n    border: 0;\\n    background-color: transparent;\\n    color: #FFFFFF;\\n}\\n.review[data-v-6f510124]{\\n    width: 100%;\\n    height: calc(100vh - 250px);\\n    overflow: auto;\\n}\\n.review-content[data-v-6f510124]{\\n    margin: 15px;\\n    background-color: #FFFFFF;\\n    border-radius: 6px;\\n    overflow: hidden;\\n    position: relative;\\n}\\n.review-content .title[data-v-6f510124]{\\n    color: #000000;\\n    padding: 10px 15px;\\n    font-size: 18px;\\n}\\n.review-content .reject[data-v-6f510124]{\\n    position: absolute;\\n    top:22px;\\n    right: 0;\\n    width: 80px;\\n    height: 30px;\\n    transform:rotate(40deg)\\n}\\n.review-content .content[data-v-6f510124]{\\n    border-bottom: 1px #F2F2F2 solid;\\n    padding: 5px 0;\\n}\\n.review-content .content .item[data-v-6f510124]{\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-start;\\n    padding: 1px 15px;\\n}\\n.review-content .content .item .title2[data-v-6f510124]{\\n    width: 100px;\\n    height: 25px;\\n    line-height: 25px;\\n    vertical-align: center;\\n    color: #888888;\\n}\\n.review-content .content .item .value[data-v-6f510124]{\\n    height: 25px;\\n    line-height: 25px;\\n    vertical-align: center;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./search.vue?vue&type=style&index=0&id=6f510124&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"3f1a32ae\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./search.vue?vue&type=style&index=0&id=6f510124&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./search.vue?vue&type=style&index=0&id=6f510124&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./search.vue?vue&type=template&id=6f510124&scoped=true\"\nimport script from \"./search.vue?vue&type=script&lang=js\"\nexport * from \"./search.vue?vue&type=script&lang=js\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=6f510124&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f510124\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6f510124')) {\n      api.createRecord('6f510124', component.options)\n    } else {\n      api.reload('6f510124', component.options)\n    }\n    module.hot.accept(\"./search.vue?vue&type=template&id=6f510124&scoped=true\", function () {\n      api.rerender('6f510124', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/review/search.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./search.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./search.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./search.vue?vue&type=style&index=0&id=6f510124&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./search.vue?vue&type=template&id=6f510124&scoped=true\""], "names": [], "sourceRoot": ""}