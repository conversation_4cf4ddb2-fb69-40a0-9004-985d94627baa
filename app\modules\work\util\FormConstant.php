<?php
namespace Envsan\Modules\Work\Util;

class FormConstant
{
    public static $form_data_list = [
        1 => [
            'code' => ['id'=>'code','type'=>'1','name'=>'订单号', 'value' => '','unit'=>''],
            'customer_name' => ['id'=>'customer_name','type'=>'1','name'=>'销售客户', 'value' => '','unit'=>''],
            'order_type_name' => ['id'=>'order_type_name','type'=>'1','name'=>'订单类型', 'value' => '','unit'=>''],
            'sign_date' => ['id'=>'sign_date','type'=>'1','name'=>'下单日期', 'value' => '','unit'=>'']
        ],
        2 => [
            'code' => ['id'=>'code','type'=>'1','name'=>'产品编号', 'value' => '','unit'=>''],
            'name' => ['id'=>'customer_name','type'=>'1','name'=>'规格型号', 'value' => '','unit'=>''],
            'customer_name' => ['id'=>'customer_name','type'=>'1','name'=>'销售客户', 'value' => '','unit'=>''],
            'uid' => ['id'=>'uid','type'=>'80','name'=>'产品详情', 'value' => '','unit'=>'']
        ],
        3 => [
            'product_code' => ['id' => 'product_code', 'type' => '1', 'name' => '产品编号', 'value' => '', 'unit' => ''],
            'product_name' => ['id' => 'product_name', 'type' => '1', 'name' => '产品名称', 'value' => '', 'unit' => ''],
            'drawing_code' => ['id' => 'drawing_code', 'type' => '1', 'name' => '图纸编号', 'value' => '', 'unit' => ''],
            'drawing_remarks' => ['id' => 'drawing_remarks', 'type' => '1', 'name' => '图纸说明', 'value' => '', 'unit' => ''],
            'version_code' => ['id' => 'version_code', 'type' => '1', 'name' => '新版本号', 'value' => '', 'unit' => ''],
            'version_remarks' => ['id' => 'version_remarks', 'type' => '1', 'name' => '版本说明', 'value' => '', 'unit' => ''],
            'drawing_files' => ['id' => 'drawing_files', 'type' => '77', 'name' => '新图纸', 'value' => '', 'unit' => ''],
            'old_version_code' => ['id' => 'old_version_code', 'type' => '1', 'name' => '旧版本号', 'value' => '', 'unit' => ''],
            'old_drawing_files' => ['id' => 'old_drawing_files', 'type' => '77', 'name' => '旧图纸', 'value' => '', 'unit' => ''],
        ],
        4 => [
            'code' => ['id' => 'code', 'type' => '1', 'name' => '生产批次号', 'value' => '', 'unit' => ''],
            'customer_name' => ['id' => 'customer_name', 'type' => '1', 'name' => '客户名', 'value' => '', 'unit' => ''],
            'plan_begin_date' => ['id' => 'plan_begin_date', 'type' => '1', 'name' => '计划开始日', 'value' => '', 'unit' => ''],
            'plan_end_date' => ['id' => 'plan_end_date', 'type' => '1', 'name' => '计划完成日', 'value' => '', 'unit' => '']
        ],
        5 => [
            'order_code' => ['id' => 'order_code', 'type' => '1', 'name' => '订单编号', 'value' => '', 'unit' => ''],
            'order_date' => ['id' => 'order_date', 'type' => '1', 'name' => '订单日期', 'value' => '', 'unit' => ''],
            'supplier_name' => ['id' => 'supplier_name', 'type' => '1', 'name' => '供应商', 'value' => '', 'unit' => ''],
            'total_money' => ['id' => 'total_money', 'type' => '1', 'name' => '未税总金额', 'value' => '', 'unit' => ''],
            'total_money_hs' => ['id' => 'total_money_hs', 'type' => '1', 'name' => '含税总金额', 'value' => '', 'unit' => ''],
            'remarks' => ['id' => 'remarks', 'type' => '1', 'name' => '备注', 'value' => '', 'unit' => ''],
        ],
        6 => [
            'order_code' => ['id' => 'order_code', 'type' => '1', 'name' => '订单编号', 'value' => '', 'unit' => ''],
            'order_date' => ['id' => 'order_date', 'type' => '1', 'name' => '订单日期', 'value' => '', 'unit' => ''],
            'supplier_name' => ['id' => 'supplier_name', 'type' => '1', 'name' => '供应商', 'value' => '', 'unit' => ''],
            'total_quantity' => ['id' => 'total_quantity', 'type' => '1', 'name' => '总数量', 'value' => '', 'unit' => ''],
            'total_money_hs' => ['id' => 'total_money_hs', 'type' => '1', 'name' => '含税总金额', 'value' => '', 'unit' => ''],
            'total_money' => ['id' => 'total_money', 'type' => '1', 'name' => '未税总金额', 'value' => '', 'unit' => ''],
            'remarks' => ['id' => 'remarks', 'type' => '1', 'name' => '备注', 'value' => '', 'unit' => ''],
        ],
    ];

    public static $detail_data_list = [
        1 => [
            'new_flag_name' => ['id'=>'new_flag_name','type'=>'1','name'=>'是否新品', 'value' => '','unit'=>''],
            'name' => ['id'=>'name','type'=>'1','name'=>'产品名称', 'value' => '','unit'=>''],
            'code' => ['id'=>'code','type'=>'1','name'=>'产品规格', 'value' => '','unit'=>''],
            'cnt' => ['id'=>'cnt','type'=>'1','name'=>'订单数量', 'value' => '','unit'=>'个'],
            'price' => ['id'=>'price','type'=>'1','name'=>'销售单价', 'value' => '','unit'=>'元/个'],
            'remarks' => ['id'=>'remarks','type'=>'1','name'=>'备注', 'value' => '','unit'=>'']
        ],
        4 =>[
            'code' => ['id'=>'code','type'=>'1','name'=>'产品编号', 'value' => '','unit'=>''],
            'name' => ['id'=>'name','type'=>'1','name'=>'规格型号', 'value' => '','unit'=>''],
            'ww_ship' => ['id'=>'ww_ship','type'=>'1','name'=>'委外工艺', 'value' => '','unit'=>''],
            'quantity' => ['id'=>'quantity','type'=>'1','name'=>'生产数量', 'value' => '','unit'=>'个'],
            'remarks' => ['id'=>'remarks','type'=>'1','name'=>'备注', 'value' => '','unit'=>''],
            'uid' => ['id'=>'uid','type'=>'80','name'=>'产品详情', 'value' => '','unit'=>'']
        ],
        5 => [
            'product_name' => ['id' => 'product_name', 'type' => '1', 'name' => '产品名称', 'value' => '', 'unit' => ''],
            'product_model' => ['id' => 'product_model', 'type' => '1', 'name' => '规格型号', 'value' => '', 'unit' => ''],
            'quantity' => ['id' => 'quantity', 'type' => '1', 'name' => '外委数量', 'value' => '', 'unit' => ''],
            'measurement_unit' => ['id' => 'measurement_unit', 'type' => '1', 'name' => '外委单位', 'value' => '', 'unit' => ''],
            'pricing_quantity' => ['id' => 'pricing_quantity', 'type' => '1', 'name' => '计价数量', 'value' => '', 'unit' => ''],
            'price_unit' => ['id' => 'price_unit', 'type' => '1', 'name' => '计价单位', 'value' => '', 'unit' => ''],
            'price' => ['id' => 'price', 'type' => '1', 'name' => '未税单价', 'value' => '', 'unit' => ''],
            'total_money' => ['id' => 'total_money', 'type' => '1', 'name' => '未税总价', 'value' => '', 'unit' => ''],
            'price_hs' => ['id' => 'price_hs', 'type' => '1', 'name' => '含税单价', 'value' => '', 'unit' => ''],
            'total_money_hs' => ['id' => 'total_money_hs', 'type' => '1', 'name' => '含税总价', 'value' => '', 'unit' => ''],
        ],
        6 => [
            'code' => ['id' => 'code', 'type' => '1', 'name' => '编码', 'value' => '', 'unit' => ''],
            'name' => ['id' => 'name', 'type' => '1', 'name' => '名称', 'value' => '', 'unit' => ''],
            'model' => ['id' => 'model', 'type' => '1', 'name' => '规格型号', 'value' => '', 'unit' => ''],
            'quantity' => ['id' => 'quantity', 'type' => '1', 'name' => '数量', 'value' => '', 'unit' => ''],
            'price' => ['id' => 'price', 'type' => '1', 'name' => '未税单价', 'value' => '', 'unit' => ''],
            'total_money' => ['id' => 'total_money', 'type' => '1', 'name' => '未税总价', 'value' => '', 'unit' => ''],
            'price_hs' => ['id' => 'price_hs', 'type' => '1', 'name' => '含税单价', 'value' => '', 'unit' => ''],
            'total_money_hs' => ['id' => 'total_money_hs', 'type' => '1', 'name' => '含税总价', 'value' => '', 'unit' => ''],
        ],
    ];
}