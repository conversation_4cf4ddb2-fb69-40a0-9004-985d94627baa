{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">模块一览</h3>
    <div class="search-page">
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-url="{{ url('sys/index/package/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="name">名称</th>
                    <th data-field="comment">说明</th>
                    <th data-field="version">版本</th>
                    <th data-field="update_date">更新日期</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    $table.bootstrapTable();
</script>