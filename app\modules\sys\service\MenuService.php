<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Phalcon\Mvc\User\Component;

class MenuService extends Component
{
    public function buileMenu($id)
    {
        $menu = require($this->config->application->menuDir . $id . '.php');

        $html = '';
        $html .= '<li class="heading"><h3>'.$menu['header'].'</h3></li>';

        $open = '';
        $super = SessionData::isSuper();
        foreach ($menu['items'] as $item)
        {
            $html_sub = '';
            if ($super || !isset($item['identity']) || (empty($item['super']) && $this->acl->has($item['identity']))) {
                $html_sub .= '<li class="nav-item">';

                if (isset($item['items'])) {
                    $html_sub .= '<a href="javascript:void(0);" class="nav-link nav-toggle">';
                } else {
                    if (isset($item['target'])) {
                        $target = ' target="'.$item['target'].'"';
                    } else {
                        $target = '';
                    }

                    $html_sub .= '<a href="'.$this->url->get($item['url']).'" class="nav-link"'.$target.'>';
                }

                if (isset($item['icon'])) {
                    $html_sub .= '<i class="'.$item['icon'].'"></i>';
                } else {
                    $html_sub .= '<i class="icon-direction"></i>';
                }
                $html_sub .= '<span class="title">'.$item['name'].'</span>';

                if (isset($item['items'])) {
                    $html_sub .= '<span class="arrow'.$open.'"></span>';
                }

                $html_sub .= '</a>';

                if (isset($item['items'])) {
                    $html_child = $this->produceMenu($item['items']);
                    if (empty($html_child)) {
                        continue;
                    }

                    $html_sub .= '<ul class="sub-menu">';
                    $html_sub .= $html_child;
                    $html_sub .= '</ul>';
                }

                $html_sub .= '</li>';
            }

            $html .= $html_sub;
        }

        return $html;
    }

    private function produceMenu($items)
    {
        $super = SessionData::isSuper();
        $html = '';
        foreach ($items as $item) {
            if (isset($item['items'])) {

                $html_child = $this->produceMenu($item['items']);
                if (empty($html_child)) {
                    continue;
                }

                $html .= '<li class="nav-item">';
                $html .= '<a href="javascript:void(0);" class="nav-link nav-toggle">';
                $html .= '<span class="title">'.$item['name'].'</span>';
                $html .= '<span class="arrow"></span>';
                $html .= '</a>';
                $html .= '<ul class="sub-menu">';
                $html .= $html_child;
                $html .= '</ul>';

            } else {
                if ($super || (empty($item['super']) && $this->acl->has($item['identity']))) {
                    $html .= '<li class="nav-item">';
                    $html .= '<a href="'.$this->url->get($item['url']).'" class="nav-link">';
                    $html .= '<span class="title">'.$item['name'].'</span>';
                    $html .= '</a>';
                    $html .= '</li>';
                }
            }
        }
        return $html;
    }

    public function buileMenu2()
    {
        $menus = [];
        $mydir = $this->config->application->menuDir;
        $files = scandir($mydir);
        foreach ($files as $file){
            if ($file != '..' && $file != '.') {
                $a = require($this->config->application->menuDir . $file);
                array_push($menus, $a);
            }
        }

        $menus_list = array();
        foreach ($menus as $menu) {
            $menu_obj = new \stdClass();
            $menu_obj->id = isset($menu['id']) ? $menu['id'] : '';
            $menu_obj->icon = isset($menu['icon']) ? $menu['icon'] : '';
            $menu_obj->header = $menu['header'];

            $has_auth = $this->hasAuth($menu['items']);

            if ($has_auth) {
                array_push($menus_list, $menu_obj);
            }
        }
        return $menus_list;
    }

    private function hasAuth($items)
    {
        $super = SessionData::isSuper();
        $has_auth = false;
        foreach ($items as $item)
        {
            if($super){
                if (!empty($item['super'])){
                    $has_auth = true;
                    break;
                }
            } else {
                if ((!isset($item['identity']) && !isset($item['items']))
                    || (empty($item['super']) && $this->acl->has($item['identity']))) {
                    $has_auth = true;
                    break;
                }

                if (isset($item['items']) && $this->hasAuth($item['items'])) {
                    $has_auth = true;
                    break;
                }
            }
        }
        return $has_auth;
    }

    public function refRes(){
//        $mydir = $this->config->application->menuDir;
//        $files = scandir($mydir);
//        foreach ($files as $file){
//            if ($file != '..' && $file != '.') {
//                $menu = require($this->config->application->menuDir . $file);
//                $now = DateUtil::now();
//                $m_identity = $menu['module'];
//                $res = new ResTemp();
//                $res->uid = UUID::make();
//                $res->pid = 0;
//                $res->type = 'module';
//                $res->name = $menu['header'];
//                $res->identity = $m_identity;
//                $res->comment = '';
//                $res->update_date = $now;
//                $res->update_by = 0;
//                $res->del_flag = 0;
//                $res->save();
//                foreach ($menu['items'] as $item)
//                {
//                    $res_con = new ResTemp();
//                    $res_con->uid = UUID::make();
//                    $res_con->pid = $res->id;
//                    $res_con->type = 'controller';
//                    $res_con->name = $item['name'];
//                    $res_con->identity = $m_identity.':'.$item['controller'];
//                    $res_con->comment = '';
//                    $res_con->update_date = $now;
//                    $res_con->update_by = 0;
//                    $res_con->del_flag = 0;
//                    $res_con->save();
//                    if (array_key_exists('items',$item)){
//                        foreach ($item['items'] as $sub)
//                        {
//                            $old = ResOld::findFirst(['del_flag = 0 and type = ?1 and identity = ?2','bind'=>[1=>'action',2=>$sub['identity']]]);
//                            if (empty($old)){
//                                $res_action = new ResTemp();
//                                $res_action->uid = UUID::make();
//                                $res_action->pid = $res_con->id;
//                                $res_action->type = 'action';
//                                $res_action->name = $sub['name'];
//                                $res_action->identity = $sub['identity'];
//                                $res_action->comment = '';
//                                $res_action->update_date = $now;
//                                $res_action->update_by = 0;
//                                $res_action->del_flag = 0;
//                                $res_action->save();
//                            } else {
//                                $res_action = new ResTemp();
//                                $res_action->id = $old->id;
//                                $res_action->uid = $old->uid;
//                                $res_action->pid = $res_con->id;
//                                $res_action->type = 'action';
//                                $res_action->name = $sub['name'];
//                                $res_action->identity = $sub['identity'];
//                                $res_action->comment = '';
//                                $res_action->update_date = $now;
//                                $res_action->update_by = 0;
//                                $res_action->del_flag = 0;
//                                $res_action->save();
//                                $old->del_flag = 1;
//                                $old->save();
//                            }
//                        }
//                    } else {
//                        $old = ResOld::findFirst(['del_flag = 0 and type = ?1 and identity = ?2','bind'=>[1=>'action',2=> $item['identity']]]);
//                        if (empty($old)){
//                            $res_action = new ResTemp();
//                            $res_action->uid = UUID::make();
//                            $res_action->pid = $res_con->id;
//                            $res_action->type = 'action';
//                            $res_action->name = $item['name'];
//                            $res_action->identity = $item['identity'];
//                            $res_action->comment = '';
//                            $res_action->update_date = $now;
//                            $res_action->update_by = 0;
//                            $res_action->del_flag = 0;
//                            $res_action->save();
//                        } else {
//                            $res_action = new ResTemp();
//                            $res_action->id = $old->id;
//                            $res_action->uid = $old->uid;
//                            $res_action->pid = $res_con->id;
//                            $res_action->type = 'action';
//                            $res_action->name = $item['name'];
//                            $res_action->identity = $item['identity'];
//                            $res_action->comment = '';
//                            $res_action->update_date = $now;
//                            $res_action->update_by = 0;
//                            $res_action->del_flag = 0;
//                            $res_action->save();
//                            $old->del_flag = 1;
//                            $old->save();
//                        }
//                    }
//                }
//            }
//        }
    }

}
