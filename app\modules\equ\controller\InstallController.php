<?php
namespace Envsan\Modules\equ\Controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'equ']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->moduleExistThenDie('equ');
        $res = [
            [
                'name' => '点检履历查询',
                'identity' => 'equ:check',
                'action' => [
                    ['name' => '点检履历查询', 'identity' => 'equ:check:list', 'comment' => '']
                ]
            ],
            [
                'name' => '模板管理',
                'identity' => 'equ:checkform',
                'action' => [
                    ['name' => '模板管理', 'identity' => 'equ:checkform:list', 'comment' => ''],
                ]
            ],
            [
                'name' => '设备故障管理',
                'identity' => 'equ:fault',
                'action' => [
                    ['name' => '修理费用统计', 'identity' => 'equ:fault:repair', 'comment' => ''],
                    ['name' => '设备故障查询', 'identity' => 'equ:fault:search', 'comment' => '']
                ]
            ],
            [
                'name' => '设备管理',
                'identity' => 'equ:item',
                'action' => [
                    ['name' => '设备查询', 'identity' => 'equ:item:list', 'comment' => ''],
                    ['name' => '设备管理', 'identity' => 'equ:item:create', 'comment' => ''],
                    ['name' => '设备状态看板', 'identity' => 'equ:item:panel', 'comment' => '']
                ]
            ],
            [
                'name' => '设备类型管理',
                'identity' => 'equ:itemtype',
                'action' => [
                    ['name' => '设备类型查询', 'identity' => 'equ:itemtype:list', 'comment' => ''],
                    ['name' => '设备类型管理', 'identity' => 'equ:itemtype:create', 'comment' => '']
                ]
            ],
            [
                'name' => '保养计划管理',
                'identity' => 'equ:maintain',
                'action' => [
                    ['name' => '保养计划管理', 'identity' => 'equ:maintain:list', 'comment' => '']
                ]
            ],
            [
                'name' => '外协供应商管理',
                'identity' => 'equ:outsidecompany',
                'action' => [
                    ['name' => '外协供应商查询', 'identity' => 'equ:outsidecompany:list', 'comment' => ''],
                    ['name' => '外协供应商管理', 'identity' => 'equ:outsidecompany:create', 'comment' => '']
                ]
            ],
        ];


        $ret = new JsonData();
        $this->db->begin();
        try {
            $this->makePackage('equ', '设备模块', '1.0', '提供设备基础功能');
            $module = $this->makeModule('equ', '设备模块');
            foreach ($res as $controller) {
                $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                foreach ($controller['action'] as $action) {
                    $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage(), $e->getTraceAsString());
            $ret->message = '发生错误';
        }
        $ret->emptyIsOk();
        die(json_encode($ret,JSON_UNESCAPED_UNICODE));
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}