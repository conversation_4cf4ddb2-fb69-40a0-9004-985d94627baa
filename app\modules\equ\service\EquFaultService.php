<?php
namespace Envsan\Modules\Equ\Service;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Equ\Model\EquFault;
use Envsan\Modules\Equ\Util\Constant;
use Phalcon\Mvc\User\Component;
use Upyun\Upyun;

class EquFaultService extends Component
{
    public function searchAll()
    {
        $equ_code = $this->request->get('equ_code', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Equ\Model\EquFault')
            ->where('del_flag = 0 and owner = '.SessionData::ownerId())
            ->orderBy('id desc');

        if (!CheckUtil::is_empty($equ_code)) {
            $builder->andWhere("equ_code like ?1", [1 => "%$equ_code%"]);
        }
        return $builder;
    }

    public function setSearchDetail($rows)
    {
        foreach ($rows as &$row)
        {
            $row['fault_level_name'] = Constant::$fault_level_arr[$row['fault_level']];
            $row['status_name'] = Constant::$fault_status_arr[$row['status']];
        }
        return $rows;
    }

    public function selectById($id)
    {
        return EquFault::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return EquFault::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function getDetailData($uid)
    {
        $row = $this->selectByUid($uid);
        if (empty($row)) {
            return false;
        }

        $cs = new CommonService();
        $base_path = $cs->getImagePath();

        $begin_files = CvtUtil::emptyToArray($row->begin_files);
        foreach ($begin_files as &$begin_file)
        {
            $begin_file = $base_path . $begin_file;
        }

        $end_files = CvtUtil::emptyToArray($row->end_files);
        foreach ($end_files as &$end_file)
        {
            $end_file = $base_path . $end_file;
        }

        $jrow = $row->toArray();
        $jrow['fault_level_name'] = Constant::$fault_level_arr[$row->fault_level];
        $jrow['begin_dt'] = substr($jrow['begin_dt'], 0, 16);
        $jrow['repair_dt'] = empty($jrow['repair_dt']) ? '' : substr($jrow['repair_dt'], 0, 16);
        $jrow['end_dt'] = empty($jrow['end_dt']) ? '' : substr($jrow['end_dt'], 0, 16);
        $jrow['begin_files'] = $begin_files;
        $jrow['end_files'] = $end_files;
        return $jrow;
    }

    public function getMoneySum($stat_type)
    {
        $date_begin = $this->request->get('date_begin', 'tstring');
        $date_end = $this->request->get('date_end', 'tstring');

        if ($stat_type == 1) {
            $columns = 'fault_month,';
            $group = 'fault_month';
        } else if ($stat_type == 2) {
            $columns = 'equ_code,';
            $group = 'equ_code';
        } else {
            $columns = 'repair_company,';
            $group = 'repair_company';
        }
        $columns .= 'ROUND(IFNULL(sum(repair_money), 0), 2) as total_money';

        $builder = $this->modelsManager->createBuilder()
            ->columns($columns)
            ->addFrom('Envsan\Modules\Equ\Model\EquFault')
            ->where('del_flag = 0 and owner = '.SessionData::ownerId())
            ->groupBy($group);

        if (!empty($date_begin)) {
            $builder->andWhere('begin_dt >= ?1', [1 => $date_begin.' 00:00:00']);
        }
        if (!empty($date_end)) {
            $builder->andWhere('begin_dt <= ?2', [2 => $date_end.' 23:59:59']);
        }
        if ($stat_type == 3) {
            $builder->andWhere('repair_company is not null');
        }
        return $builder;
    }

    public function updateImage()
    {
        $rows = EquFault::find(['del_flag = 0 and begin_files is not null']);
        foreach ($rows as $row)
        {
            $begin_files = CvtUtil::emptyToArray($row->begin_files);

            foreach ($begin_files as $idx => $begin_file)
            {
                if (strpos($begin_file, '1') === 0) {
                    $begin_files[$idx] = '/'.$begin_file;
                }
            }

            $row->begin_files = json_encode($begin_files, JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
            $row->save();
        }
    }

    public function saveImage()
    {
        $config = new \Upyun\Config($this->config->upyun->bucket, $this->config->upyun->user, $this->config->upyun->password);
        $upyun = new UpYun($config);

        $list = $this->getList();
        foreach ($list as $idx => $item)
        {
            $rows = EquFault::find(['del_flag = 0 and begin_dt = ?1', 'bind' => [1 => $item['time']]]);
            if (count($rows) == 0) {
                Logger::error('未找到记录 idx:'.$idx.' time:'.$item['time']);
                continue;
            } else if (count($rows) > 1) {
                Logger::error('找到多条记录 idx:'.$idx.' time:'.$item['time']);
                continue;
            }

            $row = $rows[0];
            $begin_files = CvtUtil::emptyToArray($row->begin_files);

            $file_content = file_get_contents($item['img']);
            $filename = '/1/image/equ/fault/2024/12/30/'.time().'_'.$idx.'.jpg';

            try {
                $upyun->write($filename, $file_content);
            } catch (\Exception $e) {
                Logger::error('上传失败 idx:'.$idx.' time:'.$item['time']);
                Logger::error($e->getMessage(), $e->getTraceAsString());
                continue;
            }

            array_push($begin_files, $filename);
            $row->begin_files = json_encode($begin_files, JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
            if (!$row->save()) {
                Logger::error('保存失败 idx:'.$idx.' time:'.$item['time']);
            }

            Logger::debug('保存成功 idx:'.$idx);
        }
    }

    private function getList()
    {
        return [
            ['time' => '2024-06-25 17:07:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1721652926.997777.jpg'],
            ['time' => '2024-07-23 03:04:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721700263.220875.jpg'],
            ['time' => '2024-07-23 18:04:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721729045.959461.jpg'],
            ['time' => '2024-07-23 18:04:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721729053.841534.jpg'],
            ['time' => '2024-07-23 02:51:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721775084.947462.jpg'],
            ['time' => '2024-07-26 00:28:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721953682.922282.jpg'],
            ['time' => '2024-07-26 16:30:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721953803.737972.jpg'],
            ['time' => '2024-07-26 16:30:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721953808.662832.jpg'],
            ['time' => '2024-07-26 16:30:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721953816.216229.jpg'],
            ['time' => '2024-07-26 16:30:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721953822.56495.jpg'],
            ['time' => '2024-07-26 00:56:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1721955407.224349.jpg'],
            ['time' => '2024-07-26 00:56:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1721961344.215348.jpg'],
            ['time' => '2024-07-27 02:58:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722052683.516039.jpg'],
            ['time' => '2024-07-27 02:58:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722147942.220914.jpg'],
            ['time' => '2024-07-27 02:58:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722147949.039091.jpg'],
            ['time' => '2024-07-27 02:58:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722147956.111634.jpg'],
            ['time' => '2024-07-29 08:53:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722214405.118354.jpg'],
            ['time' => '2024-07-04 19:10:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722259262.669827.jpg'],
            ['time' => '2024-07-30 08:34:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722299657.288711.jpg'],
            ['time' => '2024-07-30 00:12:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722329375.135397.jpg'],
            ['time' => '2024-07-31 10:02:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722391346.423488.jpg'],
            ['time' => '2024-07-15 12:23:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722494951.135046.jpg'],
            ['time' => '2024-08-02 08:47:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722559675.801369.jpg'],
            ['time' => '2024-08-02 09:39:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722562782.987405.jpg'],
            ['time' => '2024-07-25 11:27:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722569247.75229.jpg'],
            ['time' => '2024-07-25 11:27:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722569514.962153.jpg'],
            ['time' => '2024-08-03 00:25:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722615937.185449.jpg'],
            ['time' => '2024-08-03 06:49:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722638967.721904.jpg'],
            ['time' => '2024-08-03 06:49:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722638976.508398.jpg'],
            ['time' => '2024-08-02 08:47:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722738824.709504.jpg'],
            ['time' => '2024-08-02 08:47:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722738824.964986.jpg'],
            ['time' => '2024-08-04 03:05:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722769557.47178.jpg'],
            ['time' => '2024-08-04 03:05:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722769567.344025.jpg'],
            ['time' => '2024-08-05 15:01:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722841308.648829.jpg'],
            ['time' => '2024-08-05 15:01:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722911068.197347.jpg'],
            ['time' => '2024-08-05 15:01:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1722911068.886318.jpg'],
            ['time' => '2024-08-07 07:55:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1722988515.869205.jpeg'],
            ['time' => '2024-08-07 21:52:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723038725.970273.jpg'],
            ['time' => '2024-08-08 15:39:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723102772.134258.jpg'],
            ['time' => '2024-08-09 04:40:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723149609.113024.jpg'],
            ['time' => '2024-08-09 13:47:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723182453.898569.jpeg'],
            ['time' => '2024-08-09 20:20:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723206039.038099.jpg'],
            ['time' => '2024-08-10 05:11:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723237867.359595.jpeg'],
            ['time' => '2024-08-10 06:51:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723243860.203674.jpg'],
            ['time' => '2024-08-11 12:43:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723373030.362056.jpg'],
            ['time' => '2024-08-11 12:43:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723373036.627201.jpg'],
            ['time' => '2024-08-11 12:43:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723373045.341565.jpg'],
            ['time' => '2024-08-12 10:44:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723430673.516725.jpg'],
            ['time' => '2024-08-13 10:34:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723516477.634433.jpeg'],
            ['time' => '2024-08-12 10:48:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723534896.897792.jpg'],
            ['time' => '2024-08-12 19:51:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723535076.472823.jpg'],
            ['time' => '2024-08-14 13:45:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723614335.012248.jpg'],
            ['time' => '2024-08-13 09:24:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723727301.345966.jpg'],
            ['time' => '2024-08-13 09:24:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723727307.390309.jpg'],
            ['time' => '2024-08-13 09:24:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723727314.267401.jpg'],
            ['time' => '2024-08-14 21:20:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723728049.906854.jpg'],
            ['time' => '2024-08-14 21:20:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723728056.477914.jpg'],
            ['time' => '2024-07-24 07:57:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723729447.80183.jpg'],
            ['time' => '2024-08-16 08:31:00', 'img' => 'http://image.dnpc-dalian.com//fault_media/1723768287.714813.jpg'],
            ['time' => '2024-08-12 22:04:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723846396.225364.jpg'],
            ['time' => '2024-08-11 12:43:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723971088.219511.jpg'],
            ['time' => '2024-08-02 09:39:00', 'img' => 'http://image.dnpc-dalian.com//resolution_media/1723971245.228.jpg'],
        ];
    }
}