<?php
namespace Envsan\Modules\Trade\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Trade\Model\TradeOutstock;
use Envsan\Modules\Trade\Service\CommonService;
use Envsan\Modules\Trade\Service\OutstockService;
use Envsan\Modules\Trade\Util\Constant;

/**
 * @name("发货管理")
 */
class OutstockController extends SuperController
{
    private $page_id = 12;
    private $search_page_id = 13;
    // 汇总信息id
    private $summary_page_id = 53;

    /**
     * @name("列表")
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    /**
     * @name("查询")
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @name('发货汇总')
     */
    public function summaryAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->summary();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->summary_page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->summary_page_id;
    }

    /**
     * @name("创建")
     */
    public function createAction()
    {
        $s = new OutstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->create());
            return json_encode($ret);
        }
        $table = new TableService();
        $jrow = (new TradeOutstock())->toArray();
        $jrow['outstock_date'] = DateUtil::today();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['detail_data'] = [];
        $jrow['files'] = [];
        $jrow['customer_id'] = '';
        $common = new CommonService();
        $this->view->customerList = $common->getCustomerList();
        $this->view->jsonData = json_encode($jrow);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'trade:outstock:create'})
     */
    function editAction($uid)
    {
        $s = new OutstockService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->handleResult($s->update($row));
            return json_encode($ret);
        }

        $table = new TableService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);
        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, $ext_data);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $common = new CommonService();
        $this->view->customerList = $common->getCustomerList();
        $this->view->jsonData = json_encode($jrow);
        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('outstock/create');
    }

    /**
     * @acl({'link':'trade:outstock:create'})
     */
    public function orderAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->selectOrderAll();
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'trade:outstock:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new OutstockService();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }


    /**
     * @acl({'link':'trade:outstock:create'})
     */
    public function cancelAction(){
        $s = new OutstockService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->cancel();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new OutstockService();
        $row = $s->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $user = User::findFirst(['id = ?1','bind'=>[1=>$jrow['create_by']]]);
        $jrow['outstock_user'] = '';
        if (!empty($user)){
            $jrow['outstock_user'] = $user->real_name;
        }
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonData = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new OutstockService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new OutstockService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }

    /**
     * 订单可以发货的信息
     * @acl({'link':'trade:outstock:create'})
     */
    public function detailAction($type = '', $customer_id = null, $ids = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OutstockService();
            $builder = $s->selectOrderShipmentDetails($customer_id, $ids);
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->customer_id = $customer_id;
        $this->view->ids = $ids;
    }

    /**
     * 导出汇总信息
     * @skipacl
     */
    public function exportSummaryAction()
    {
        $s = new OutstockService();
        $builder = $s->summary();
        $table = new TableService();
        $table->exportExcel($this->summary_page_id, $builder);
    }

}