<?php
namespace Envsan\Modules\Trade\Service;

use Envsan\Common\Data\SessionData;
use Phalcon\Mvc\User\Component;

class CommonService extends Component
{
    public function getCustomerList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.id,a.code, a.name')
            ->addFrom('Envsan\Modules\Trade\Model\TradeCustomer', 'a')
            ->where('a.del_flag = 0 and a.owner = ?1',[1 => SessionData::ownerId()]);
        return $builder->getQuery()->execute();
    }
}