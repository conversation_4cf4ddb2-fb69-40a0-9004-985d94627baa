<?php

namespace Envsan\Modules\Purchase\Model;

use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseWwReceiptDetail extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $receipt_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $product_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $bom_name;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $product_code;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_deputy_unit;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $pricing_quantity;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $conversion_rate;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $price_hs;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $price;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $total_amount;

    /**
     *
     * @var double
     * @Column(type="double", length=8, nullable=true)
     */
    public $tax_rate;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $tax_amount;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $total_amount_hs;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $check_status;

    /**
     *
     * @var integer
     * @Column(type="integer", length=1, nullable=true)
     */
    public $check_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $apply_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $apply_code;

    /**
     *
     * @var integer
     * @Column(type="integer", length=1, nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $owner;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_wwreceipt_detail';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwReceiptDetail[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwReceiptDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }

}
