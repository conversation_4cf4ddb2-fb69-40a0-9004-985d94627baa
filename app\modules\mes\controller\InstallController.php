<?php
namespace Envsan\Modules\Mes\Controller;

use Envsan\Common\Base\InstallerBase;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Sys\Model\Package;

/**
 * @super
 */
class InstallController extends InstallerBase
{
    public function indexAction()
    {
        $row = Package::findFirst(['short_name=?1', 'bind'=>[1=>'mes']]);
        $this->view->package = $row;
    }

    public function execAction()
    {
        $this->moduleExistThenDie('mes');

        $res = [
            [
                'name' => '排产管理',
                'identity' => 'mes:plan',
                'action' => [
                    ['name' => '排产管理', 'identity' => 'mes:plan:list', 'comment' => ''],
                    ['name' => '排产查询', 'identity' => 'mes:plan:search', 'comment' => '']
                ]
            ],
            [
                'name' => '质量管理',
                'identity' => 'mes:check',
                'action' => [
                    ['name' => '质验数据查询', 'identity' => 'mes:check:list', 'comment' => '']
                ]
            ],
            [
                'name' => '工艺管理',
                'identity' => 'mes:ship',
                'action' => [
                    ['name' => '工艺标准字段管理', 'identity' => 'mes:ship:fieldlist', 'comment' => ''],
                    ['name' => '工艺类型管理', 'identity' => 'mes:ship:list', 'comment' => ''],
                ]
            ],
            [
                'name' => '产品管理',
                'identity' => 'mes:product',
                'action' => [
                    ['name' => '产品管理', 'identity' => 'mes:product:list', 'comment' => ''],
                    ['name' => '产品查询', 'identity' => 'mes:product:search', 'comment' => ''],
                    ['name' => '产品编辑', 'identity' => 'mes:product:create', 'comment' => '']
                ]
            ],
            [
                'name' => '图纸管理',
                'identity' => 'mes:drawing',
                'action' => [
                    ['name' => '图纸管理', 'identity' => 'mes:drawing:list', 'comment' => '']
                ]
            ],
            [
                'name' => '生产通知管理',
                'identity' => 'mes:notice',
                'action' => [
                    ['name' => '生产通知管理', 'identity' => 'mes:notice:list', 'comment' => ''],
                    ['name' => '生产通知查询', 'identity' => 'mes:notice:search', 'comment' => ''],
                    ['name' => '添加生产通知', 'identity' => 'mes:notice:create', 'comment' => ''],
                ]
            ],
            [
                'name' => '生产管理',
                'identity' => 'mes:produce',
                'action' => [
                    ['name' => '生产履历查询', 'identity' => 'mes:produce:list', 'comment' => ''],
                    ['name' => '其他工作查询', 'identity' => 'mes:produce:other', 'comment' => ''],
                ]
            ],
            [
                'name' => '生产日报',
                'identity' => 'mes:report',
                'action' => [
                    ['name' => '生产日报管理', 'identity' => 'mes:report:list', 'comment' => ''],
                    ['name' => '工资统计', 'identity' => 'mes:report:stat', 'comment' => ''],
                ]
            ]
        ];

        $ret = new JsonData();
        $this->db->begin();
        try {
            $this->makePackage('mes', '生产模块', '1.0', '提供生产模块功能');
            $module = $this->makeModule('mes', '生产模块');
            foreach ($res as $controller) {
                $conn = $this->makeController($module, $controller['identity'], $controller['name']);
                foreach ($controller['action'] as $action) {
                    $this->makeAct($conn, $action['identity'], $action['name'], $action['comment']);
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage(), $e->getTraceAsString());
            $ret->message = '发生错误';
        }
        $ret->emptyIsOk();
        die(json_encode($ret,JSON_UNESCAPED_UNICODE));
    }

    public function updateAction()
    {

    }

    public function clearAction()
    {

    }
}