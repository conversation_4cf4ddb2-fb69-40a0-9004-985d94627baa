<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseArrivalRejection;
use Envsan\Modules\Purchase\Model\PurchaseDefectHandling;
use Envsan\Modules\Sys\Model\SysDict;
use Phalcon\Mvc\User\Component;

class PurchaseDefectHandlingService extends BaseService
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.inspection_id,
                t1.inspection_code,
                t1.handling_date,
                t1.supplier_id,
                t1.supplier_name,
                t1.goods_id,
                t1.goods_model,
                t1.goods_name,
                t1.goods_unit,
                t1.arrival_quantity,
                t1.rejected_qty,
                t1.available_qty,
                t1.defect_reasons,
                t1.handling_method,
                t1.handling_method_name,
                t1.status,
                t1.status_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseDefectHandling', 't1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseDefectHandling();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {

        return $this->executeInTransaction(function () use ($act, $row){
            // 检验单id
            $inspection_id = $this->request->getPost('inspection_id', 'tstring');
            // 检验单号
            $inspection_code = $this->request->getPost('inspection_code', 'tstring');
            $handling_date = $this->request->getPost('handling_date', 'tstring');
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');
            $supplier_name = $this->request->getPost('supplier_name', 'tstring');
            $goods_id = $this->request->getPost('goods_id', 'tstring');
            $goods_model = $this->request->getPost('goods_model', 'tstring');
            $goods_name = $this->request->getPost('goods_name', 'tstring');
            $goods_unit = $this->request->getPost('goods_unit', 'tstring');
            // 到货数量
            $arrival_quantity = $this->request->getPost('arrival_quantity', 'tstring');
            // 不可入库数量
            $rejected_qty = $this->request->getPost('rejected_qty', 'tstring');
            // 可入库数量
            $available_qty = $this->request->getPost('available_qty', 'tstring');

            // 不良品处理方式
            $handling_method = $this->request->getPost('handling_method', 'tstring');
            $handling_method_name = $this->request->getPost('handling_method_name', 'tstring');
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
            // 不良品原因 - 先URL解码，再HTML实体解码
            $defect_reasons = $this->request->getPost('defect_reasons_dis', 'tstring');

            $remarks = $this->request->getPost('remarks', 'tstring');

            if (empty($inspection_code) || empty($handling_date))
                return ErrorHelper::WRONG_INPUT;

            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);
            // defect_reasons保持为JSON字符串，不转换为数组
            // 不良品处理单号
            $defect_code = 'LLBLP' . date('ym');
            // 拿到当前最大号
            // 返回的是下一个可用号，比如 '0005'
            $max_code = $this->generateSequenceNumber('Envsan\Modules\Purchase\Model\PurchaseDefectHandling', $defect_code);
            $row->code = $defect_code . $max_code;
            // 检验详细id
            $row->inspection_id = CvtUtil::blankToNull($inspection_id);
            // 检验详细单号
            $row->inspection_code = $inspection_code;
            $row->handling_date = CvtUtil::blankToNull($handling_date);
            $row->supplier_id = CvtUtil::blankToNull($supplier_id);
            $row->supplier_name = CvtUtil::blankToNull($supplier_name);
            $row->goods_id = CvtUtil::blankToNull($goods_id);
            $row->goods_model =$goods_model;
            $row->goods_name =$goods_name;
            $row->goods_unit =$goods_unit;
            // 到货数量
            $row->arrival_quantity = CvtUtil::blankToNull($arrival_quantity);
            // 不可入库数量
            $row->rejected_qty = CvtUtil::blankToNull($rejected_qty);
            // 可入库数量
            $row->available_qty = CvtUtil::blankToNull($available_qty);
            // 不良品原因 - 确保是JSON字符串格式
            if (empty($defect_reasons)) {
                $row->defect_reasons = null;
            } else{
                $row->defect_reasons = json_encode($defect_reasons,JSON_UNESCAPED_UNICODE);
            }
            // 不良品处理方式
            $row->handling_method = $handling_method;
            $row->handling_method_name = $handling_method_name;

            $row-> remarks = CvtUtil::blankToNull($remarks);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;

            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            $row->status = 20;
            $row->status_name = '待审核';
            $row->save();
        });
    }

    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseDefectHandling')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return PurchaseDefectHandling::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseDefectHandling::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getDefectHandingData($inspection_code = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                 t1.id as inspection_id,
                 t1.uid,
                 t1.goods_id,
                 t1.goods_code,
                 t1.goods_name,
                 t1.goods_model,
                 t1.goods_unit,
                 round(t1.quantity,4) as quantity,
                 t1.status,
                 t1.status_name,
                 t1.check_code,
                 t1.check_status,
                 t1.check_images,
                 IF(t1.check_result_flag = 0, "OK", IF(t1.check_result_flag = 1, "NG", "未知")) as check_result,
                 t1.check_val,
                 t1.check_data,
                 t1.check_user_id,
                 t1.check_user_name,
                 t1.check_time,
                 t1.check_remarks,
                 t1.quality_template_id,
                 t2.inspection_code,
                 t2.receipt_code,
                 t2.department_name,
                 t2.supplier_id,
                 t2.supplier_name,
                 t3.quantity as receipt_quantity,
                 t3.purchase_quantity             
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInspectionDetail','t1')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInspection','t1.inspection_id = t2.id','t2')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseReceiptDetail','t1.receipt_detail_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseDefectHandling','t4.inspection_id = t1.id and t4.del_flag = 0','t4')
            ->where('t1.del_flag = 0 and t1.check_status = 20 and t1.check_result_flag = 1 and t1.status = 10 and t4.id is null')
            ->orderBy('t2.inspection_day desc , t1.id');
        if (!empty($inspection_code)) {
            $builder->andWhere('t1.check_code = ?1', [1 => $inspection_code]);
        }

        return $builder->getQuery()->execute();
    }


    public function getDictList($dict_type)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('a.uid, a.id, a.code, a.name, a.dict_type')
            ->addFrom('Envsan\Modules\Sys\Model\SysDict', 'a')
            ->where('a.del_flag = 0 and a.dict_type = ?1', [1 => $dict_type])
            ->orderBy('a.id');
        return $builder->getQuery()->execute();
    }

    /**
     * 处理不良品原因显示 - 将ID数组转换为中文名称
     * @param array $data 数据数组
     * @return array 处理后的数据数组
     */
    public function processDefectReasonsDisplay($data)
    {
        // 获取不良品原因字典
        $dictData = $this->getDictList('purchase:defect:reason');
        $dictMap = [];
        foreach ($dictData as $dict) {
            $dictMap[$dict->id] = $dict->name;
        }
        
        // 处理每条记录的不良品原因显示
        foreach ($data as &$item) {
            if (!empty($item['defect_reasons'])) {
                $reasons = json_decode($item['defect_reasons'], true);
                $reasonNames = [];
                if (is_array($reasons)) {
                    foreach ($reasons as $reasonId) {
                        if (isset($dictMap[$reasonId])) {
                            $reasonNames[] = $dictMap[$reasonId];
                        }
                    }
                }
                $item['defect_reasons_display'] = implode(', ', $reasonNames);
            } else {
                $item['defect_reasons_display'] = '';
            }
        }
        
        return $data;
    }

    /**
     * 审核通过
     * @return array
     */
    public function approval()
    {
        return $this->executeInTransaction(function () {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $record = $this->selectByUid($uid);

            if (empty($record)) {
                return $this->error('单据不存在');
            }

            $now = DateUtil::now();
            $user = SessionData::user();
            $record->comment = $comment;
            $record->approved_by = $user->id;
            $record->update_date = $now;
            $record->update_by = $user->id;
            $record->status = 30;
            $record->status_name = '完成';
            $record->save();

            // 调试输出
            Logger::info('handling_method值: [' . $record->handling_method . ']');
            Logger::info('handling_method长度: ' . strlen($record->handling_method));
            Logger::info('比较结果: ' . ($record->handling_method == 'all_reject' ? 'true' : 'false'));
            
            if (trim($record->handling_method) === 'all_reject') {
                $pars = new PurchaseArrivalRejectionService();
                // 不良品处理单号
                $reject_code = 'CGDH' . date('ym');
                // 拿到当前最大号
                // 返回的是下一个可用号，比如 '0005'
                $max_code = $this->generateSequenceNumber('Envsan\Modules\Purchase\Model\PurchaseArrivalRejection', $reject_code);


                // 获取不良品原因字典
                $dictData = $this->getDictList('purchase:defect:reason');
                $dictMap = [];
                foreach ($dictData as $dict) {
                    $dictMap[$dict->id] = $dict->name;
                }
                $reasons = json_decode($record->defect_reasons, true);
                $reasonNames = [];
                if (is_array($reasons)) {
                    foreach ($reasons as $reasonId) {
                        if (isset($dictMap[$reasonId])) {
                            $reasonNames[] = $dictMap[$reasonId];
                        }
                    }
                }
                $rejection = new PurchaseArrivalRejection();
                $rejection->uid = UUID::make();
                $rejection->code = $reject_code . $max_code;
                $rejection->defect_handling_id = $record->id;
                $rejection->defect_handling_code = $record->code;
                $rejection->rejection_date = date('Y-m-d');
                $rejection->supplier_id = $record->supplier_id;
                $rejection->supplier_name = $record->supplier_name;
                $rejection->goods_id = $record->goods_id;
                $rejection->goods_name = $record->goods_name;
                $rejection->goods_model = $record->goods_model;
                $rejection->goods_unit = $record->goods_unit;
                $rejection->rejection_quantity = $record->rejected_qty;
                $rejection->rejection_reasons = $record->defect_reasons;
                $rejection->rejection_reasons_dis = implode(', ', $reasonNames);
                $rejection->status = 30;
                $rejection->status_name = '完成';
                $rejection->approval_comment = $comment;
                $rejection->approval_user_id = $user->id;
                $rejection->approval_user_name = $user->real_name;
                $rejection->update_date = $now;
                $rejection->update_by = $user->id;
                $rejection->del_flag = 0;
                $rejection->group_id = $user->group_id;
                $rejection->owner = $user->owner;
                $rejection->save();
            }

        });
    }

    /**
     * 审核驳回
     * @return array
     */
    public function reject()
    {
        return $this->executeInTransaction(function() {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $record = $this->selectByUid($uid);
            if (empty($record)) {
                return $this->error('单据不存在');
            }

            $now = DateUtil::now();
            $user = SessionData::user();
            $record->comment = $comment;
            $record->approved_by = $user->id;
            $record->update_date = $now;
            $record->update_by = $user->id;
            $record->status = 10;
            $record->status_name = '以驳回';
            $record->save();
        });
    }

}