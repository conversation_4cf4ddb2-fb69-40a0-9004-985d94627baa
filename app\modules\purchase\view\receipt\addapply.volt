{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">物料编码</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="goods_code" v-model="goods_code"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">物料名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="goods_name" v-model="goods_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">规格型号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="goods_model" v-model="goods_model"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">采购订单号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="order_code" v-model="order_code"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="row row-summary">
            <div class="col-md-12" style="text-align: right">
                <button type="button" class="btn blue btn-outline btn-circle" style="margin-right: 15px;" onclick="add()">
                    <i class="fa fa-plus"></i>&nbsp;<span>添加</span>
                </button>
            </div>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('purchase/receipt/addapply/json/' ~ order_id ~ '/' ~ ids) }}"
                   data-page-size="100"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-checkbox="true"></th>
                    <th data-field="goods_code">物料编码</th>
                    <th data-field="goods_name">物料名称</th>
                    <th data-field="goods_model">规格型号</th>
                    <th data-field="quantity">订单数量</th>
                    <th data-field="int_cnt">入库数量</th>
                    <th data-field="goods_deputy_unit">库存单位</th>
                    <th data-field="purchase_quantity">采购计量数量</th>
                    <th data-field="int_purchase_cnt">采购计量入库数量</th>
                    <th data-field="goods_unit">采购单位</th>
                    <th data-field="unit_conversion_rate">换算率</th>
                    <th data-field="price">未税单价</th>
                    <th data-field="price_hs">含税单价</th>
                    <th data-field="total_money">金额</th>
                    <th data-field="tax_rate" data-formatter="taxRateFormatter">税率</th>
                    <th data-field="tax_money">税额</th>
                    <th data-field="total_money_hs">价税合计</th>
                    <th data-field="order_code">采购订单号</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            goods_code: '',
            goods_name: '',
            goods_model: '',
            order_code: '',
            from_type: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });
    $table.bootstrapTable();
    function codeFormatter(v, row, index) {
        return row.apply_code ?? v;
    }

    function dateFormatter(v, row, index) {
        return row.apply_date ?? v;
    }

    function add() {
        var datas = $table.bootstrapTable('getSelections');
        if (datas.length == 0) {
            alertWarning('请选择添加明细');
            return;
        }

        top.window.layer_data2 = datas;
        top.window.layer_result2 = 'ok';
        top.layer.close(top.layer.getFrameIndex(window.name));
    }

    function taxRateFormatter(value, row, index) {
        if (value == null || value === '') {
            return '';
        }
        return (parseFloat(value) * 100).toFixed(2) + '%';
    }

    function formatQuantity(value, row, index) {
        if (value == null || value === '') {
            return '';
        }
        return value + ' ' + row.goods_deputy_unit;
    }

    function formatPurchaseQuantity(value, row, index) {
        if (value == null || value === '') {
            return '';
        }
        return value + ' ' + row.goods_unit;
    }

    

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>