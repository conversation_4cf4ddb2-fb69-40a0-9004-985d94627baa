{"version": 3, "file": "js/src_view_fault_detail_vue.js", "mappings": ";;;;;;;;;;;AAeA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC3HA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://rrts-manager/src/components/datetime.vue", "webpack://rrts-manager/src/view/fault/detail.vue", "webpack://rrts-manager/./src/components/datetime.vue", "webpack://rrts-manager/./src/view/fault/detail.vue", "webpack://rrts-manager/./src/components/datetime.vue?b741", "webpack://rrts-manager/./src/components/datetime.vue?1b08", "webpack://rrts-manager/./src/js/date.js", "webpack://rrts-manager/./src/view/fault/detail.vue?fb1c", "webpack://rrts-manager/./src/view/fault/detail.vue?597b", "webpack://rrts-manager/./src/view/fault/detail.vue?66f3"], "sourcesContent": ["<template>\r\n    <van-popup :value=\"value\" position=\"bottom\" @click-overlay=\"onDateCancel\">\r\n        <van-datetime-picker\r\n                v-model=\"date_selected\"\r\n                type=\"datetime\"\r\n                title=\"选择时间\"\r\n                :min-date=\"minDate\"\r\n                :max-date=\"maxDate\"\r\n                @confirm=\"onDateConfirm\"\r\n                @cancel=\"onDateCancel\"\r\n        />\r\n    </van-popup>\r\n</template>\r\n\r\n<script>\r\n    import DateUtil from '../js/date';\r\n\r\n    export default {\r\n        name: \"m-datetime\",\r\n\r\n        props: {\r\n            value: {\r\n                type: Boolean,\r\n                default: false\r\n            },\r\n\r\n            init_date_selected: {\r\n                type: String,\r\n                default: ''\r\n            },\r\n        },\r\n\r\n        mounted() {\r\n            this.setDate(this.init_date_selected);\r\n        },\r\n\r\n        data() {\r\n            return {\r\n                date_show: false,\r\n                date_selected: new Date(),\r\n                minDate: new Date(2024, 0, 1, 0, 0),\r\n                maxDate: new Date(2074, 11, 31, 23, 59)\r\n            }\r\n        },\r\n\r\n        watch: {\r\n            value: function(val) {\r\n                this.date_show = val;\r\n            },\r\n\r\n            date_show: function(val) {\r\n                this.$emit('input', val);\r\n            },\r\n\r\n            init_date_selected: function(val) {\r\n                this.setDate(val);\r\n            }\r\n        },\r\n\r\n        methods: {\r\n            onDateConfirm(date) {\r\n                this.date_show = false;\r\n                this.$emit('date-confirm', DateUtil.formatDateTime(date));\r\n            },\r\n            onDateCancel() {\r\n                this.date_show = false;\r\n            },\r\n            setDate(val) {\r\n                if (!val) {\r\n                    this.date_selected = new Date();\r\n                } else {\r\n                    let arr = val.split(' ');\r\n                    let dates = arr[0].split('-');\r\n                    let year = dates[0];\r\n                    let month = Number(dates[1]) - 1;\r\n                    let day = Number(dates[2]);\r\n\r\n                    let times = arr[1].split(':');\r\n                    let hour = times[0];\r\n                    let minute = times[1];\r\n                    this.date_selected = new Date(year, month, day, hour, minute);\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n", "<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"查看故障单\" is_back=\"1\"></m-header>\r\n        <m-body>\r\n            <div v-if=\"loading\" style=\"height: 100%;display: flex;align-items: center;justify-content: center;\">\r\n                <van-loading size=\"36px\" text-size=\"16px\" vertical>加载中...</van-loading>\r\n            </div>\r\n            <div v-else style=\"height: 100%;display: flex;flex-direction: column;\">\r\n                <van-form style=\"flex: 1;overflow-y: auto;\">\r\n                    <van-field\r\n                        label=\"设备类型\"\r\n                        type=\"text\"\r\n                        v-model=\"equ_type_name\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"设备\"\r\n                        type=\"text\"\r\n                        v-model=\"equ_code\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"影响级别\"\r\n                        type=\"text\"\r\n                        v-model=\"fault_level_name\"\r\n                        placeholder=\"请选择影响级别\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"发生时间\"\r\n                        type=\"text\"\r\n                        v-model=\"begin_dt\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"故障现象\"\r\n                        type=\"textarea\"\r\n                        v-model=\"begin_describe\"\r\n                        readonly\r\n                    />\r\n\r\n                    <van-field name=\"uploader\" label=\"故障现象照片\">\r\n                        <template #input>\r\n                            <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;\">\r\n                                <div v-for=\"(file,i) in begin_files\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                    <van-image :src=\"file\" width=\"80px\" height=\"80px\" @click=\"getImg(begin_files, i)\" class=\"img-view\"></van-image>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </van-field>\r\n                </van-form>\r\n            </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import MDateTime from '../../components/datetime';\r\n    import { Dialog, ImagePreview } from 'vant';\r\n\r\n    export default {\r\n        name: \"faultDetail\",\r\n        components: {\r\n            'm-datetime': MDateTime\r\n        },\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                uid: '',\r\n                equ_type_name: '',\r\n                equ_code: '',\r\n                fault_level_name: '',\r\n                begin_dt: '',\r\n                begin_describe: '',\r\n                begin_files: [],\r\n                base_path: '',\r\n            }\r\n        },\r\n        created() {\r\n            this.uid = this.$route.params.uid || '';\r\n\r\n            let user = this.$store.state.user;\r\n            this.base_path = user.imgdir;\r\n\r\n            this.init();\r\n        },\r\n        methods: {\r\n            init() {\r\n                this.loading = true;\r\n                this.$http.post_only('work/fault/detail', {uid: this.uid}).then((rs) => {\r\n                    this.loading = false;\r\n                    if (rs.status == 'ok') {\r\n                        let data = rs.data;\r\n                        this.equ_type_name = data.equ_type_name;\r\n                        this.equ_code = data.equ_code;\r\n                        this.fault_level_name = data.fault_level_name;\r\n                        this.begin_dt = data.begin_dt;\r\n                        this.begin_describe = data.begin_describe;\r\n                        this.begin_files = data.begin_files;\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '提示',\r\n                            message: rs.message,\r\n                            confirmButtonText: '返回上一页'\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            getImg(images, index) {\r\n                ImagePreview({\r\n                    images: this.begin_files,\r\n                    showIndex: true,\r\n                    loop: false,\r\n                    startPosition: index\r\n                });\r\n            },\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('van-popup',{attrs:{\"value\":_vm.value,\"position\":\"bottom\"},on:{\"click-overlay\":_vm.onDateCancel}},[_c('van-datetime-picker',{attrs:{\"type\":\"datetime\",\"title\":\"选择时间\",\"min-date\":_vm.minDate,\"max-date\":_vm.maxDate},on:{\"confirm\":_vm.onDateConfirm,\"cancel\":_vm.onDateCancel},model:{value:(_vm.date_selected),callback:function ($$v) {_vm.date_selected=$$v},expression:\"date_selected\"}})],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"查看故障单\",\"is_back\":\"1\"}}),_c('m-body',[(_vm.loading)?_c('div',{staticStyle:{\"height\":\"100%\",\"display\":\"flex\",\"align-items\":\"center\",\"justify-content\":\"center\"}},[_c('van-loading',{attrs:{\"size\":\"36px\",\"text-size\":\"16px\",\"vertical\":\"\"}},[_vm._v(\"加载中...\")])],1):_c('div',{staticStyle:{\"height\":\"100%\",\"display\":\"flex\",\"flex-direction\":\"column\"}},[_c('van-form',{staticStyle:{\"flex\":\"1\",\"overflow-y\":\"auto\"}},[_c('van-field',{attrs:{\"label\":\"设备类型\",\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.equ_type_name),callback:function ($$v) {_vm.equ_type_name=$$v},expression:\"equ_type_name\"}}),_c('van-field',{attrs:{\"label\":\"设备\",\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.equ_code),callback:function ($$v) {_vm.equ_code=$$v},expression:\"equ_code\"}}),_c('van-field',{attrs:{\"label\":\"影响级别\",\"type\":\"text\",\"placeholder\":\"请选择影响级别\",\"readonly\":\"\"},model:{value:(_vm.fault_level_name),callback:function ($$v) {_vm.fault_level_name=$$v},expression:\"fault_level_name\"}}),_c('van-field',{attrs:{\"label\":\"发生时间\",\"type\":\"text\",\"readonly\":\"\"},model:{value:(_vm.begin_dt),callback:function ($$v) {_vm.begin_dt=$$v},expression:\"begin_dt\"}}),_c('van-field',{attrs:{\"label\":\"故障现象\",\"type\":\"textarea\",\"readonly\":\"\"},model:{value:(_vm.begin_describe),callback:function ($$v) {_vm.begin_describe=$$v},expression:\"begin_describe\"}}),_c('van-field',{attrs:{\"name\":\"uploader\",\"label\":\"故障现象照片\"},scopedSlots:_vm._u([{key:\"input\",fn:function(){return [_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.begin_files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"}},[_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":file,\"width\":\"80px\",\"height\":\"80px\"},on:{\"click\":function($event){return _vm.getImg(_vm.begin_files, i)}}})],1)}),0)]},proxy:true}])})],1)],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./datetime.vue?vue&type=template&id=3e86520b&scoped=true\"\nimport script from \"./datetime.vue?vue&type=script&lang=js\"\nexport * from \"./datetime.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e86520b\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('3e86520b')) {\n      api.createRecord('3e86520b', component.options)\n    } else {\n      api.reload('3e86520b', component.options)\n    }\n    module.hot.accept(\"./datetime.vue?vue&type=template&id=3e86520b&scoped=true\", function () {\n      api.rerender('3e86520b', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/datetime.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./datetime.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./datetime.vue?vue&type=script&lang=js\"", "export default {\r\n    format: (date) => {\r\n        if (!date) {\r\n            return date;\r\n        }\r\n\r\n        let month = date.getMonth() + 1;\r\n        if (month < 10) {\r\n            month = \"0\" + month;\r\n        }\r\n        let day = date.getDate();\r\n        if (day < 10) {\r\n            day = \"0\" + day;\r\n        }\r\n        return date.getFullYear() + \"-\" + month + \"-\" + day;\r\n    },\r\n    formatDateTime: (date) => {\r\n        if (!date) {\r\n            return date;\r\n        }\r\n\r\n        let month = date.getMonth() + 1;\r\n        if (month < 10) {\r\n            month = \"0\" + month;\r\n        }\r\n        let day = date.getDate();\r\n        if (day < 10) {\r\n            day = \"0\" + day;\r\n        }\r\n        let hour = ('0' + date.getHours()).substr(-2);\r\n        let minute = ('0' + date.getMinutes()).substr(-2);\r\n        return date.getFullYear() + \"-\" + month + \"-\" + day + ' ' + hour + ':' + minute;\r\n    },\r\n}\r\n", "import { render, staticRenderFns } from \"./detail.vue?vue&type=template&id=6450fd02&scoped=true\"\nimport script from \"./detail.vue?vue&type=script&lang=js\"\nexport * from \"./detail.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6450fd02\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6450fd02')) {\n      api.createRecord('6450fd02', component.options)\n    } else {\n      api.reload('6450fd02', component.options)\n    }\n    module.hot.accept(\"./detail.vue?vue&type=template&id=6450fd02&scoped=true\", function () {\n      api.rerender('6450fd02', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/fault/detail.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./detail.vue?vue&type=template&id=6450fd02&scoped=true\""], "names": [], "sourceRoot": ""}