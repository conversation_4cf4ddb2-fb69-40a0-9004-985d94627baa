{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">项目号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="order_code" v-model="order_code"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">物料名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="goods_name" v-model="goods_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">供应商</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="supplier_name" v-model="supplier_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">产品名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="product_name" v-model="product_name"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">来源</label>
                            <div class="col-md-9">
                                <select class="bs-select form-control" name="from_type" v-model="from_type">
                                    <option value="">全部</option>
                                    <option value="1">项目发起</option>
                                    <option value="2">库房发起</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="row row-summary">
            <div class="col-md-12" style="text-align: right">
                <button type="button" class="btn blue btn-outline btn-circle" style="margin-right: 15px;" onclick="add()">
                    <i class="fa fa-plus"></i>&nbsp;<span>添加</span>
                </button>
            </div>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('purchase/order/addapply/json/' ~ request_id ~ '/' ~ ids) }}"
                   data-page-size="100"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-checkbox="true"></th>
                    <th data-field="goods_name">物料名称</th>
                    <th data-field="goods_model">规格型号</th>
                    <th data-field="purchase_quantity" data-formatter="formatPurchaseQuantity">采购数量</th>
                    <th data-field="inventory_quantity" data-formatter="formatInventoryQuantity">库存计量数量</th>
                    <th data-field="from_type">来源</th>
                    <th data-field="order_code" data-formatter="codeFormatter">项目号/申请单号</th>
                    <th data-field="deliver_date" data-formatter="dateFormatter">交付/申请日期</th>
                    <th data-field="supplier_name">供应商</th>
                    <th data-field="remarks">备注</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            order_code: '',
            goods_name: '',
            supplier_name: '',
            product_name: '',
            from_type: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },

        }
    });
    $table.bootstrapTable();
    function codeFormatter(v, row, index) {
        return row.apply_code ?? v;
    }

    function dateFormatter(v, row, index) {
        return row.apply_date ?? v;
    }

    function add() {
        var datas = $table.bootstrapTable('getSelections');
        if (datas.length == 0) {
            alertWarning('请选择添加明细');
            return;
        }

        top.window.layer_data2 = datas;
        top.window.layer_result2 = 'ok';
        top.layer.close(top.layer.getFrameIndex(window.name));
    }

    // 格式化采购数量显示：数量（单位）
    function formatPurchaseQuantity(value, row, index) {
        if (row.purchase_quantity && row.purchase_unit) {
            return row.purchase_quantity + '（' + row.purchase_unit + '）';
        }
        return value || '';
    }

    // 格式化库存数量显示：数量（单位）
    function formatInventoryQuantity(value, row, index) {
        if (row.inventory_quantity && row.inventory_unit) {
            return row.inventory_quantity + '（' + row.inventory_unit + '）';
        }
        return value || '';
    }

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>