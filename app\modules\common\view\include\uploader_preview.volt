<script>
    function initUpLoaderReadOnly(id, title, img_path, img_name, big_img) {
        var uploader_class = '';
        if (big_img != null && big_img) {
            uploader_class = ' uploader-one-custom';
        }

        var html = '<div id="uploader" class="uploader-one' + uploader_class + '">';
        html += '<div id="' + id + '" class="img-body">';
        html += '<div class="uploader-wrap">';
        html += '<div class="uploader-table">';
        html += '<div class="uploader-table-cell">';
        html += '<a id="btn_see" class="btn btn-default btn-circle" data-id="'+id+'" href="javascript:void(0);" data-url="" data-title="' + title + '" title="查看图片" onclick="previewImg(this)">';
        html += '<i class="fa fa-search"></i>';
        html += '</a>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '<div class="uploader-title">' + title + '</div>';
        $("#" + id + "_area").append(html);

        showImage(id, img_path + img_name, big_img);
    }

    function showImage(id, path, big_img) {
        var $img_body = $("#" + id);
        $img_body.find(".file-item").remove();

        var small_img = '';
        var img_style = '';
        if (big_img == null || big_img == false) {
            small_img = '!small';
        } else {
            img_style = ' style="height: ' + ($img_body.height() - 10) + 'px"';
            if (id == 'vl_url' || id == 'dl_url' || id == "img_idcard" || id == "img_idcard_sub" || id == "img_bank"){
                small_img = '!com';
            }
        }

        var $div_img = $(
            '<div class="file-item thumbnail">' +
            '<img src="' + path + small_img + '"' + img_style + '>' +
            '</div>'
        );

        $img_body.find(".uploader-default").remove();
        $img_body.find(".uploader-wrap").before($div_img);
        $img_body.find("#btn_see").attr("data-url", path);
    }
</script>