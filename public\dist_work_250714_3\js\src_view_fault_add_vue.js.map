{"version": 3, "file": "js/src_view_fault_add_vue.js", "mappings": ";;;;;;;;;;;AAeA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACkEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC9YA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;AEAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://rrts-manager/src/components/datetime.vue", "webpack://rrts-manager/src/view/fault/add.vue", "webpack://rrts-manager/./src/components/datetime.vue", "webpack://rrts-manager/./src/view/fault/add.vue", "webpack://rrts-manager/./src/components/datetime.vue?b741", "webpack://rrts-manager/./src/components/datetime.vue?1b08", "webpack://rrts-manager/./src/components/datetime.vue?f29f", "webpack://rrts-manager/./src/js/date.js", "webpack://rrts-manager/./src/view/fault/add.vue?9a24", "webpack://rrts-manager/./src/view/fault/add.vue?9d50", "webpack://rrts-manager/./src/view/fault/add.vue?1b1a"], "sourcesContent": ["<template>\r\n    <van-popup :value=\"value\" position=\"bottom\" @click-overlay=\"onDateCancel\">\r\n        <van-datetime-picker\r\n                v-model=\"date_selected\"\r\n                type=\"datetime\"\r\n                title=\"选择时间\"\r\n                :min-date=\"minDate\"\r\n                :max-date=\"maxDate\"\r\n                @confirm=\"onDateConfirm\"\r\n                @cancel=\"onDateCancel\"\r\n        />\r\n    </van-popup>\r\n</template>\r\n\r\n<script>\r\n    import DateUtil from '../js/date';\r\n\r\n    export default {\r\n        name: \"m-datetime\",\r\n\r\n        props: {\r\n            value: {\r\n                type: Boolean,\r\n                default: false\r\n            },\r\n\r\n            init_date_selected: {\r\n                type: String,\r\n                default: ''\r\n            },\r\n        },\r\n\r\n        mounted() {\r\n            this.setDate(this.init_date_selected);\r\n        },\r\n\r\n        data() {\r\n            return {\r\n                date_show: false,\r\n                date_selected: new Date(),\r\n                minDate: new Date(2024, 0, 1, 0, 0),\r\n                maxDate: new Date(2074, 11, 31, 23, 59)\r\n            }\r\n        },\r\n\r\n        watch: {\r\n            value: function(val) {\r\n                this.date_show = val;\r\n            },\r\n\r\n            date_show: function(val) {\r\n                this.$emit('input', val);\r\n            },\r\n\r\n            init_date_selected: function(val) {\r\n                this.setDate(val);\r\n            }\r\n        },\r\n\r\n        methods: {\r\n            onDateConfirm(date) {\r\n                this.date_show = false;\r\n                this.$emit('date-confirm', DateUtil.formatDateTime(date));\r\n            },\r\n            onDateCancel() {\r\n                this.date_show = false;\r\n            },\r\n            setDate(val) {\r\n                if (!val) {\r\n                    this.date_selected = new Date();\r\n                } else {\r\n                    let arr = val.split(' ');\r\n                    let dates = arr[0].split('-');\r\n                    let year = dates[0];\r\n                    let month = Number(dates[1]) - 1;\r\n                    let day = Number(dates[2]);\r\n\r\n                    let times = arr[1].split(':');\r\n                    let hour = times[0];\r\n                    let minute = times[1];\r\n                    this.date_selected = new Date(year, month, day, hour, minute);\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n", "<template>\r\n    <div class=\"main\">\r\n        <m-header :name=\"title\" is_back=\"1\"></m-header>\r\n        <m-body>\r\n            <div v-if=\"loading\" style=\"height: 100%;display: flex;align-items: center;justify-content: center;\">\r\n                <van-loading size=\"36px\" text-size=\"16px\" vertical>加载中...</van-loading>\r\n            </div>\r\n            <div v-else style=\"height: 100%;display: flex;flex-direction: column;\">\r\n                <van-form style=\"flex: 1;overflow-y: auto;\">\r\n                    <van-field\r\n                        label=\"设备类型\"\r\n                        type=\"text\"\r\n                        v-model=\"equ_type_name\"\r\n                        placeholder=\"请选择设备类型\"\r\n                        @click-input=\"equ_type_show = true\"\r\n                        input-align=\"right\"\r\n                        is-link\r\n                        readonly\r\n                        required\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"设备\"\r\n                        type=\"text\"\r\n                        v-model=\"equ_code\"\r\n                        placeholder=\"请选择设备\"\r\n                        @click-input=\"equ_show = true\"\r\n                        input-align=\"right\"\r\n                        is-link\r\n                        readonly\r\n                        required\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"影响级别\"\r\n                        type=\"text\"\r\n                        v-model=\"fault_level_name\"\r\n                        placeholder=\"请选择影响级别\"\r\n                        @click-input=\"fault_level_show = true\"\r\n                        input-align=\"right\"\r\n                        is-link\r\n                        readonly\r\n                        required\r\n                    />\r\n\r\n                    <van-field\r\n                        label=\"发生时间\"\r\n                        type=\"text\"\r\n                        v-model=\"begin_dt\"\r\n                        placeholder=\"请选择发生时间\"\r\n                        @click-input=\"begin_dt_show = true\"\r\n                        input-align=\"right\"\r\n                        is-link\r\n                        readonly\r\n                        required\r\n                    />\r\n\r\n                    <van-field\r\n                        v-model=\"begin_describe\"\r\n                        rows=\"2\"\r\n                        autosize\r\n                        label=\"故障现象\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入故障现象\"\r\n                        show-word-limit\r\n                        required\r\n                    />\r\n\r\n                    <van-field name=\"uploader\" label=\"故障现象照片\" required>\r\n                        <template #input>\r\n                            <input\r\n                                ref=\"fileInput\"\r\n                                type=\"file\"\r\n                                multiple\r\n                                accept=\"image/*\"\r\n                                hidden\r\n                                @change=\"handleFileChange\"\r\n                            >\r\n                            <div style=\"display: flex;flex-direction: row;flex-wrap: wrap;\">\r\n                                <div v-for=\"(file,i) in begin_files\" style=\"width: 80px;height: 80px;position: relative;margin-right: 8px;margin-bottom: 10px\">\r\n                                    <div @click=\"delPhoto(i)\" style=\"position: absolute;top: -5px;right: -5px;width: 20px;height: 20px;background-color: red;border-radius: 20px;z-index: 99;text-align: center;display: flex;flex-direction: column;justify-content: center\">\r\n                                        <van-icon name=\"cross\" size=\"16\" color=\"#FFFFFF\"/>\r\n                                    </div>\r\n                                    <van-image :src=\"file\" width=\"80px\" height=\"80px\" @click=\"getImg(begin_files, i)\" class=\"img-view\"></van-image>\r\n                                </div>\r\n                                <div v-if=\"begin_files.length < 5\" @click=\"takePhoto\" style=\"width: 80px;height: 80px;background-color: #f2f2f2;text-align: center;display: flex;flex-direction: column; justify-content: center;\">\r\n                                    <van-icon name=\"photograph\" color=\"#bbbbbb\" size=\"25\"/>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </van-field>\r\n                </van-form>\r\n                <div>\r\n                    <van-button type=\"warning\" icon=\"success\" block size=\"large\" @click=\"doSubmit\">提交</van-button>\r\n                </div>\r\n            </div>\r\n        </m-body>\r\n\r\n        <van-popup\r\n            v-model=\"equ_type_show\"\r\n            position=\"bottom\"\r\n            style=\"height: 400px;border-top-left-radius: 10px;border-top-right-radius: 10px;padding: 20px\">\r\n            <van-picker\r\n                title=\"选择设备类型\"\r\n                show-toolbar\r\n                :columns=\"equ_type_list\"\r\n                :default-index=\"equ_type\"\r\n                @cancel=\"equ_type_show = false\"\r\n                @confirm=\"equTypeChange\"\r\n            >\r\n            </van-picker>\r\n        </van-popup>\r\n\r\n        <van-popup\r\n            v-model=\"equ_show\"\r\n            position=\"bottom\"\r\n            style=\"height: 400px;border-top-left-radius: 10px;border-top-right-radius: 10px;padding: 20px\">\r\n            <van-picker\r\n                title=\"选择设备\"\r\n                show-toolbar\r\n                :columns=\"equ_list\"\r\n                :default-index=\"equ_index\"\r\n                value-key=\"code\"\r\n                @cancel=\"equ_show = false\"\r\n                @confirm=\"equChange\"\r\n            >\r\n            </van-picker>\r\n        </van-popup>\r\n\r\n        <van-popup\r\n            v-model=\"fault_level_show\"\r\n            position=\"bottom\"\r\n            style=\"height: 400px;border-top-left-radius: 10px;border-top-right-radius: 10px;padding: 20px\">\r\n            <van-picker\r\n                title=\"选择影响级别\"\r\n                show-toolbar\r\n                :columns=\"fault_level_list\"\r\n                :default-index=\"fault_level\"\r\n                @cancel=\"fault_level_show = false\"\r\n                @confirm=\"faultLevelChange\"\r\n            >\r\n            </van-picker>\r\n        </van-popup>\r\n\r\n        <m-datetime v-model=\"begin_dt_show\" :init_date_selected=\"begin_dt\" @date-confirm=\"onBeginDtConfirm\"/>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import MDateTime from '../../components/datetime';\r\n    import { Dialog, ImagePreview } from 'vant';\r\n\r\n    export default {\r\n        name: \"faultAdd\",\r\n        components: {\r\n            'm-datetime': MDateTime\r\n        },\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                uid: '',\r\n                title: '',\r\n                equ_type: '',\r\n                equ_type_name: '',\r\n                equ_type_show: false,\r\n                equ_type_list: [],\r\n                equ_type_index: 0,\r\n                equ_id: '',\r\n                equ_code: '',\r\n                equ_show: false,\r\n                equ_list: [],\r\n                equ_index: 0,\r\n                fault_level: '',\r\n                fault_level_name: '',\r\n                fault_level_show: false,\r\n                fault_level_list: [],\r\n                begin_dt: '',\r\n                begin_dt_show: false,\r\n                begin_describe: '',\r\n                begin_files: [],\r\n                base_path: '',\r\n            }\r\n        },\r\n        created() {\r\n            this.uid = this.$route.params.uid || '';\r\n            if (this.uid) {\r\n                this.title = '编辑故障单';\r\n            } else {\r\n                this.title = '新增故障单';\r\n            }\r\n\r\n            let user = this.$store.state.user;\r\n            this.base_path = user.imgdir;\r\n\r\n            this.init();\r\n        },\r\n        methods: {\r\n            init() {\r\n                this.loading = true;\r\n                this.$http.post_only('work/fault/add', {uid: this.uid}).then((rs) => {\r\n                    this.loading = false;\r\n                    if (rs.status == 'ok') {\r\n                        this.equ_type_list = rs.equ_type_list;\r\n                        this.fault_level_list = rs.fault_level_list;\r\n                        if (this.uid) {\r\n                            this.equ_list = rs.equ_list;\r\n                            this.equ_index = rs.equ_index;\r\n\r\n                            let data = rs.data;\r\n                            this.equ_type = data.equ_type;\r\n                            this.equ_type_name = data.equ_type_name;\r\n                            this.equ_id = data.equ_id;\r\n                            this.equ_code = data.equ_code;\r\n                            this.fault_level = data.fault_level;\r\n                            this.fault_level_name = data.fault_level_name;\r\n                            this.begin_dt = data.begin_dt;\r\n                            this.begin_describe = data.begin_describe;\r\n                            this.begin_files = data.begin_files;\r\n                        }\r\n                    } else {\r\n                        Dialog.alert({\r\n                            title: '提示',\r\n                            message: rs.message,\r\n                            confirmButtonText: '返回上一页'\r\n                        }).then(() => {\r\n                            this.$router.back();\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            equTypeChange(name, index) {\r\n                this.equ_type = index;\r\n                this.equ_type_name = name;\r\n                this.equ_id = '';\r\n                this.equ_code = '';\r\n                this.equ_list = [];\r\n                this.equ_type_show = false;\r\n\r\n                this.$http.post('work/fault/equlist', {equ_type: this.equ_type}).then((rs) => {\r\n                    this.equ_list = rs;\r\n                });\r\n            },\r\n            equChange(v) {\r\n                this.equ_id = v.id;\r\n                this.equ_code = v.code;\r\n                this.equ_show = false;\r\n            },\r\n            faultLevelChange(name, index) {\r\n                this.fault_level = index;\r\n                this.fault_level_name = name;\r\n                this.fault_level_show = false;\r\n            },\r\n            onBeginDtConfirm(date) {\r\n                this.begin_dt = date;\r\n            },\r\n            doSubmit() {\r\n                if (this.equ_type === '' || this.equ_type === null) {\r\n                    this.$toast.fail('请选择故障设备类型');\r\n                    return;\r\n                }\r\n\r\n                if (!this.equ_id) {\r\n                    this.$toast.fail('请选择故障设备');\r\n                    return;\r\n                }\r\n\r\n                if (this.fault_level === '' || this.fault_level === null) {\r\n                    this.$toast.fail('请选择故障影响级别');\r\n                    return;\r\n                }\r\n\r\n                if (!this.begin_dt) {\r\n                    this.$toast.fail('请选择故障发生时间');\r\n                    return;\r\n                }\r\n\r\n                if (!this.begin_describe) {\r\n                    this.$toast.fail('请录入故障现象');\r\n                    return;\r\n                }\r\n\r\n                if (this.begin_files.length == 0) {\r\n                    this.$toast.fail('请上传故障照片');\r\n                    return;\r\n                }\r\n\r\n                Dialog.confirm({\r\n                    title: '提交',\r\n                    message: '确定提交吗？',\r\n                }).then(() => {\r\n                    this.$cjs.showLoading('照片上传中');\r\n                    this.upload(this.begin_files, [], 0, (upload_rs) => {\r\n                        this.$cjs.hideLoading();\r\n                        if (upload_rs.status == 'ok') {\r\n                            this.$cjs.showLoading('数据提交中');\r\n                            this.$http.post('work/fault/submit', {\r\n                                uid: this.uid,\r\n                                equ_type: this.equ_type,\r\n                                equ_id: this.equ_id,\r\n                                fault_level: this.fault_level,\r\n                                begin_dt: this.begin_dt,\r\n                                begin_describe: this.begin_describe,\r\n                                begin_files: encodeURI(JSON.stringify(upload_rs.list))\r\n                            }).then((rs) => {\r\n                                if (rs.status === 'ok') {\r\n                                    this.$toast.success('提交成功！');\r\n                                    this.$router.go(-1);\r\n                                } else {\r\n                                    this.$toast.fail(rs.message);\r\n                                }\r\n                            }).catch((e) => {\r\n                                this.$toast.fail('提交失败');\r\n                            });\r\n                        } else {\r\n                            this.$toast.fail('文件上传失败！');\r\n                        }\r\n                    });\r\n                }).catch(() => {});\r\n            },\r\n            takePhoto() {\r\n                this.$refs.fileInput.click();\r\n            },\r\n            async handleFileChange(e){\r\n                const selectedFiles = Array.from(e.target.files);\r\n                if (!selectedFiles) return;\r\n                // 逐个处理文件\r\n                for (const file of selectedFiles) {\r\n                    // 验证文件类型\r\n                    if (!file.type.startsWith('image/')) {\r\n                        this.errorMessage = '仅支持图片格式'\r\n                        continue\r\n                    }\r\n                    // 验证文件大小\r\n                    if (file.size > 10 * 1024 * 1024) {\r\n                        this.errorMessage = `文件大小不能超过10MB`\r\n                        continue\r\n                    }\r\n                    const preview = await this.readFileAsDataURL(file)\r\n                    this.begin_files.push(preview)\r\n                }\r\n            },\r\n            readFileAsDataURL(file) {\r\n                return new Promise((resolve, reject) => {\r\n                    const reader = new FileReader()\r\n                    reader.onload = () => resolve(reader.result)\r\n                    reader.onerror = reject\r\n                    reader.readAsDataURL(file)\r\n                })\r\n            },\r\n            delPhoto(i) {\r\n                this.begin_files.splice(i,1);\r\n            },\r\n            getImg(images, index) {\r\n                ImagePreview({\r\n                    images: this.begin_files,\r\n                    showIndex: true,\r\n                    loop: false,\r\n                    startPosition: index\r\n                });\r\n            },\r\n            upload(flies, new_flies, i, cb) {\r\n                if (flies.length == i) {\r\n                    cb({\r\n                        status: 'ok',\r\n                        list: new_flies\r\n                    });\r\n                    return;\r\n                }\r\n                if (flies[i].indexOf('data:') >= 0) {\r\n                    this.fileUpload(flies[i],(data) => {\r\n                        if (data.status == 'ok') {\r\n                            new_flies.push(data.path);\r\n                            i++;\r\n                            this.upload(flies, new_flies, i, cb);\r\n                        } else {\r\n                            cb(data);\r\n                        }\r\n                    });\r\n                } else {\r\n                    new_flies.push(flies[i].replace(this.base_path, ''));\r\n                    i++;\r\n                    this.upload(flies, new_flies, i, cb);\r\n                }\r\n            },\r\n            fileUpload(base64Data, cb) {\r\n                let user = this.$store.state.user;\r\n                this.$http.fileUpload(user,'equ/fault', base64Data).then((rs) => {\r\n                    cb({\r\n                        status: 'ok',\r\n                        path: rs\r\n                    });\r\n                }).catch((e) => {\r\n                    console.error(e);\r\n                    cb({status: 'error'});\r\n                });\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('van-popup',{attrs:{\"value\":_vm.value,\"position\":\"bottom\"},on:{\"click-overlay\":_vm.onDateCancel}},[_c('van-datetime-picker',{attrs:{\"type\":\"datetime\",\"title\":\"选择时间\",\"min-date\":_vm.minDate,\"max-date\":_vm.maxDate},on:{\"confirm\":_vm.onDateConfirm,\"cancel\":_vm.onDateCancel},model:{value:(_vm.date_selected),callback:function ($$v) {_vm.date_selected=$$v},expression:\"date_selected\"}})],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":_vm.title,\"is_back\":\"1\"}}),_c('m-body',[(_vm.loading)?_c('div',{staticStyle:{\"height\":\"100%\",\"display\":\"flex\",\"align-items\":\"center\",\"justify-content\":\"center\"}},[_c('van-loading',{attrs:{\"size\":\"36px\",\"text-size\":\"16px\",\"vertical\":\"\"}},[_vm._v(\"加载中...\")])],1):_c('div',{staticStyle:{\"height\":\"100%\",\"display\":\"flex\",\"flex-direction\":\"column\"}},[_c('van-form',{staticStyle:{\"flex\":\"1\",\"overflow-y\":\"auto\"}},[_c('van-field',{attrs:{\"label\":\"设备类型\",\"type\":\"text\",\"placeholder\":\"请选择设备类型\",\"input-align\":\"right\",\"is-link\":\"\",\"readonly\":\"\",\"required\":\"\"},on:{\"click-input\":function($event){_vm.equ_type_show = true}},model:{value:(_vm.equ_type_name),callback:function ($$v) {_vm.equ_type_name=$$v},expression:\"equ_type_name\"}}),_c('van-field',{attrs:{\"label\":\"设备\",\"type\":\"text\",\"placeholder\":\"请选择设备\",\"input-align\":\"right\",\"is-link\":\"\",\"readonly\":\"\",\"required\":\"\"},on:{\"click-input\":function($event){_vm.equ_show = true}},model:{value:(_vm.equ_code),callback:function ($$v) {_vm.equ_code=$$v},expression:\"equ_code\"}}),_c('van-field',{attrs:{\"label\":\"影响级别\",\"type\":\"text\",\"placeholder\":\"请选择影响级别\",\"input-align\":\"right\",\"is-link\":\"\",\"readonly\":\"\",\"required\":\"\"},on:{\"click-input\":function($event){_vm.fault_level_show = true}},model:{value:(_vm.fault_level_name),callback:function ($$v) {_vm.fault_level_name=$$v},expression:\"fault_level_name\"}}),_c('van-field',{attrs:{\"label\":\"发生时间\",\"type\":\"text\",\"placeholder\":\"请选择发生时间\",\"input-align\":\"right\",\"is-link\":\"\",\"readonly\":\"\",\"required\":\"\"},on:{\"click-input\":function($event){_vm.begin_dt_show = true}},model:{value:(_vm.begin_dt),callback:function ($$v) {_vm.begin_dt=$$v},expression:\"begin_dt\"}}),_c('van-field',{attrs:{\"rows\":\"2\",\"autosize\":\"\",\"label\":\"故障现象\",\"type\":\"textarea\",\"maxlength\":\"200\",\"placeholder\":\"请输入故障现象\",\"show-word-limit\":\"\",\"required\":\"\"},model:{value:(_vm.begin_describe),callback:function ($$v) {_vm.begin_describe=$$v},expression:\"begin_describe\"}}),_c('van-field',{attrs:{\"name\":\"uploader\",\"label\":\"故障现象照片\",\"required\":\"\"},scopedSlots:_vm._u([{key:\"input\",fn:function(){return [_c('input',{ref:\"fileInput\",attrs:{\"type\":\"file\",\"multiple\":\"\",\"accept\":\"image/*\",\"hidden\":\"\"},on:{\"change\":_vm.handleFileChange}}),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"flex-wrap\":\"wrap\"}},[_vm._l((_vm.begin_files),function(file,i){return _c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"position\":\"relative\",\"margin-right\":\"8px\",\"margin-bottom\":\"10px\"}},[_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"-5px\",\"right\":\"-5px\",\"width\":\"20px\",\"height\":\"20px\",\"background-color\":\"red\",\"border-radius\":\"20px\",\"z-index\":\"99\",\"text-align\":\"center\",\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\"},on:{\"click\":function($event){return _vm.delPhoto(i)}}},[_c('van-icon',{attrs:{\"name\":\"cross\",\"size\":\"16\",\"color\":\"#FFFFFF\"}})],1),_c('van-image',{staticClass:\"img-view\",attrs:{\"src\":file,\"width\":\"80px\",\"height\":\"80px\"},on:{\"click\":function($event){return _vm.getImg(_vm.begin_files, i)}}})],1)}),(_vm.begin_files.length < 5)?_c('div',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"background-color\":\"#f2f2f2\",\"text-align\":\"center\",\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\"},on:{\"click\":_vm.takePhoto}},[_c('van-icon',{attrs:{\"name\":\"photograph\",\"color\":\"#bbbbbb\",\"size\":\"25\"}})],1):_vm._e()],2)]},proxy:true}])})],1),_c('div',[_c('van-button',{attrs:{\"type\":\"warning\",\"icon\":\"success\",\"block\":\"\",\"size\":\"large\"},on:{\"click\":_vm.doSubmit}},[_vm._v(\"提交\")])],1)],1)]),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.equ_type_show),callback:function ($$v) {_vm.equ_type_show=$$v},expression:\"equ_type_show\"}},[_c('van-picker',{attrs:{\"title\":\"选择设备类型\",\"show-toolbar\":\"\",\"columns\":_vm.equ_type_list,\"default-index\":_vm.equ_type},on:{\"cancel\":function($event){_vm.equ_type_show = false},\"confirm\":_vm.equTypeChange}})],1),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.equ_show),callback:function ($$v) {_vm.equ_show=$$v},expression:\"equ_show\"}},[_c('van-picker',{attrs:{\"title\":\"选择设备\",\"show-toolbar\":\"\",\"columns\":_vm.equ_list,\"default-index\":_vm.equ_index,\"value-key\":\"code\"},on:{\"cancel\":function($event){_vm.equ_show = false},\"confirm\":_vm.equChange}})],1),_c('van-popup',{staticStyle:{\"height\":\"400px\",\"border-top-left-radius\":\"10px\",\"border-top-right-radius\":\"10px\",\"padding\":\"20px\"},attrs:{\"position\":\"bottom\"},model:{value:(_vm.fault_level_show),callback:function ($$v) {_vm.fault_level_show=$$v},expression:\"fault_level_show\"}},[_c('van-picker',{attrs:{\"title\":\"选择影响级别\",\"show-toolbar\":\"\",\"columns\":_vm.fault_level_list,\"default-index\":_vm.fault_level},on:{\"cancel\":function($event){_vm.fault_level_show = false},\"confirm\":_vm.faultLevelChange}})],1),_c('m-datetime',{attrs:{\"init_date_selected\":_vm.begin_dt},on:{\"date-confirm\":_vm.onBeginDtConfirm},model:{value:(_vm.begin_dt_show),callback:function ($$v) {_vm.begin_dt_show=$$v},expression:\"begin_dt_show\"}})],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./datetime.vue?vue&type=template&id=3e86520b&scoped=true\"\nimport script from \"./datetime.vue?vue&type=script&lang=js\"\nexport * from \"./datetime.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e86520b\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('3e86520b')) {\n      api.createRecord('3e86520b', component.options)\n    } else {\n      api.reload('3e86520b', component.options)\n    }\n    module.hot.accept(\"./datetime.vue?vue&type=template&id=3e86520b&scoped=true\", function () {\n      api.rerender('3e86520b', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/datetime.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./datetime.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./datetime.vue?vue&type=script&lang=js\"", "export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./datetime.vue?vue&type=template&id=3e86520b&scoped=true\"", "export default {\r\n    format: (date) => {\r\n        if (!date) {\r\n            return date;\r\n        }\r\n\r\n        let month = date.getMonth() + 1;\r\n        if (month < 10) {\r\n            month = \"0\" + month;\r\n        }\r\n        let day = date.getDate();\r\n        if (day < 10) {\r\n            day = \"0\" + day;\r\n        }\r\n        return date.getFullYear() + \"-\" + month + \"-\" + day;\r\n    },\r\n    formatDateTime: (date) => {\r\n        if (!date) {\r\n            return date;\r\n        }\r\n\r\n        let month = date.getMonth() + 1;\r\n        if (month < 10) {\r\n            month = \"0\" + month;\r\n        }\r\n        let day = date.getDate();\r\n        if (day < 10) {\r\n            day = \"0\" + day;\r\n        }\r\n        let hour = ('0' + date.getHours()).substr(-2);\r\n        let minute = ('0' + date.getMinutes()).substr(-2);\r\n        return date.getFullYear() + \"-\" + month + \"-\" + day + ' ' + hour + ':' + minute;\r\n    },\r\n}\r\n", "import { render, staticRenderFns } from \"./add.vue?vue&type=template&id=6c18f363&scoped=true\"\nimport script from \"./add.vue?vue&type=script&lang=js\"\nexport * from \"./add.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6c18f363\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6c18f363')) {\n      api.createRecord('6c18f363', component.options)\n    } else {\n      api.reload('6c18f363', component.options)\n    }\n    module.hot.accept(\"./add.vue?vue&type=template&id=6c18f363&scoped=true\", function () {\n      api.rerender('6c18f363', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/fault/add.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./add.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./add.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./add.vue?vue&type=template&id=6c18f363&scoped=true\""], "names": [], "sourceRoot": ""}