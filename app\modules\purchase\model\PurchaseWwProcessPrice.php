<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
class PurchaseWwProcessPrice extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $supplier_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $supplier_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $supplier_name;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $price_flag;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $good_inventory_code;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_model;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $ship_type_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $ship_type_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $ship_explain;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $measurement_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $price_unit;

    /**
     *
     * @var double
     * @Column(type="double", length=12, nullable=true)
     */
    public $unit_price;

    /**
     *
     * @var double
     * @Column(type="double", length=10, nullable=true)
     */
    public $conversion_rate;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $currency;

    /**
     *
     * @var double
     * @Column(type="double", length=5, nullable=true)
     */
    public $tax_rate;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $effective_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $expiry_date;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", length=1, nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_ww_process_price';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwProcessPrice[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseWwProcessPrice
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
