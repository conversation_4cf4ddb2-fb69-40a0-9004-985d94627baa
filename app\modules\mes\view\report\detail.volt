{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">生产日期</span>
                        <span v-text="report_date"></span>
                    </div>
                    <div class="actions">
                    </div>
                </div>
                <div class="portlet-body" style="height: 85.5vh;overflow-y: auto">
                    <div style="margin-bottom: 15px">
                        <div style="width: 100%;background-color: #F2F2F2;display: flex;flex-direction: row;padding: 8px;border: 1px solid #E2E2E2">
                            <div>
                                <span v-text="staff_name" style="font-weight: bold"></span>
                            </div>
                            <div style="display: flex;color:#0080FF;padding-left: 10px;font-weight: bold">
                                <div style="margin-right: 10px">
                                    时薪：<span v-text="cost + ' 元/H'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    日工：<span v-text="day_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    计件：<span v-text="jj_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    其他工作：<span v-text="other_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px">
                                    补助：<span v-text="day_bz_money + ' 元'"></span>
                                </div>
                                <div style="margin-right: 10px;color: red">
                                    总工资：<span v-text="sum_money + ' 元'"></span>
                                </div>
                            </div>
                        </div>
                        <div style="border: 1px solid #E2E2E2">
                            <div v-if="produce_list.length > 0">
                                <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                    <thead>
                                    <tr>
                                        <th>班次</th>
                                        <th>批次号</th>
                                        <th>产品名称</th>
                                        <th>工艺名称</th>
                                        <th>基准</th>
                                        <th>生产性</th>
                                        <th>工资方式</th>
                                        <th>合格数量</th>
                                        <th>加工费件/元</th>
                                        <th>工时(H)</th>
                                        <th>工资系数</th>
                                        <th>不良数</th>
                                        <th>不良原因</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="d_item, d_index in produce_list">
                                        <td>
                                            <span v-if="d_item.shift_type == 1">白班</span>
                                            <span v-else>夜班</span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.notice_code'></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.product_code'></span>
                                            <br>
                                            <span v-text='d_item.product_name'></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.bom_code'></span>
                                            <br>
                                            <span v-text='d_item.bom_name'></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.scxjz != ''" v-text="d_item.scxjz + '(件/H)'"></span>
                                            <br>
                                            <span v-if="d_item.jzz != ''" v-text="d_item.jzz + '(H)'"></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.scx != ''" v-text="d_item.scx + '%'"></span>
                                        </td>
                                        <td>
                                            <span v-if="d_item.work_type == 1">日工</span>
                                            <span v-else>计件</span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.cnt"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.one_cost"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.hour"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.wages_ratio"></span>
                                        </td>
                                        <td>
                                            <span v-text="d_item.error_cnt"></span>
                                        </td>
                                        <td>
                                            <span v-text='d_item.error_type'></span>
                                            <br>
                                            <span v-text='d_item.error_remarks'></span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div v-if="other_list.length > 0">
                                <div style="display: flex;width: 100%;padding: 5px;flex-wrap: wrap">
                                    <div style="width: 50%;padding-right: 5px" v-for="o_item, o_index in other_list">
                                        <div style="width: 100%;display: flex;background-color: #F2F2F2;padding: 5px;border: 1px solid #e2e2e2;border-radius: 5px">
                                            <div style="width: 15%;line-height: 36px">
                                                <span v-text="o_item.produce_type"></span>
                                            </div>
                                            <div style="width: 20%;line-height: 36px">
                                                <span v-text="o_item.remarks"></span>
                                            </div>
                                            <div style="width: 15%;line-height: 36px">
                                                <span v-if="o_item.shift_type == 1">白班</span>
                                                <span v-else>夜班</span>
                                            </div>
                                            <div style="width: 25%;line-height: 36px">
                                                <span>工时(H)：</span>
                                                <span v-text="o_item.hour"></span>
                                            </div>
                                            <div style="width: 25%;line-height: 36px">
                                                <span>工资系数：</span>
                                                <span v-text="o_item.wages_ratio"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        methods: {

        }
    });
</script>