<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Util\Constant as MesConstant;
use Envsan\Modules\Purchase\Model\PurchaseApply;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseOrder;
use Envsan\Modules\Purchase\Model\PurchaseOrderDetail;
use Envsan\Modules\Purchase\Model\PurchaseRequest;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Work\Service\DataCommonService;
use Envsan\Modules\Work\Service\WorkService;

class OrderService extends BaseService
{
    public function selectAll($order_type = 1)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.order_code,
                t1.order_date,
                t1.remarks,
                t1.status,
                t1.status_name,
                t1.total_weight,
                t1.total_money,
                t1.total_money_hs,
                substring(t1.finish_date, 1, 10) as finish_date,
                t2.name as supplier_name,
                t1.ext_val
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->andWhere('t1.order_type = ?2', [2 => $order_type])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectWaitAll($request_id = '', $ids = '')
    {
        $order_code = $this->request->get('order_code', 'tstring');
        $goods_name = $this->request->get('goods_name', 'tstring');
        $supplier_name = $this->request->get('supplier_name', 'tstring');
        $product_name = $this->request->get('product_name', 'tstring');
        $from_type = $this->request->get('from_type', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.goods_id,
                t2.uid as goods_uid,
                t1.goods_code,
                t1.goods_name,
                t1.goods_spec,
                t1.goods_model,
                t2.price_hs,
                t2.price,
                t2.tax_rate ,
                t2.check_flag,
                round(t1.purchase_quantity, 4) as purchase_quantity,
                round(t1.quantity, 4) as inventory_quantity,
                t1.supplier_name,
                t1.remarks,
                t2.unit as purchase_unit ,
                t2.deputy_unit as inventory_unit,
                t2.unit_conversion_rate ,
                r.apply_code,
                r.apply_date,
                r.create_date,
                u.real_name,
                0 as sel
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApply', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods', 't1.goods_id = t2.id', 't2')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseRequest', 't1.request_id = r.id', 'r')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 'r.notice_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 'r.create_by = u.id', 'u')
            ->where('t1.del_flag = 0 and t1.status = 10 and (t3.id is null or t3.status = :status:)', ['status' => 30])
            ->orderBy('t1.id asc');
        if (!empty($ids)) {
            $builder->notInWhere('t1.id', explode(',', $ids));
        }
        if (!CheckUtil::is_empty($order_code)) {
            $builder->andWhere("r.apply_code like ?1", [1 => "%$order_code%"]);
        }
        if (!CheckUtil::is_empty($request_id)) {
            $builder->andWhere("r.id = :request_id:", ['request_id' => $request_id]);
        }
        if (!CheckUtil::is_empty($supplier_name)) {
            $builder->andWhere("t1.supplier_name like ?3", [3 => "%$supplier_name%"]);
        }
        if (!CheckUtil::is_empty($product_name)) {
            $builder->andWhere("t2.name like ?4", [4 => "%$product_name%"]);
        }
        if ($from_type == 1) {
            $builder->andWhere('r.id is null');
        } else if ($from_type == 2) {
            $builder->andWhere('r.id is not null');
        }
        if (!CheckUtil::is_empty($goods_name)) {
            $cols = ['t1.goods_code', 't1.goods_name', 't1.goods_spec', 't1.goods_model'];
            $cs = new CommonService();
            $builder = $cs->getGoodsBuilder($goods_name, $cols, $builder);
        }
        return $builder;
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.order_code,
                t1.order_date,
                substring(t1.finish_date, 1, 10) as finish_date,
                t1.remarks,
                t1.status,
                t1.status_name,
                t1.total_weight as order_total_weight,
                t1.total_money as order_total_money,
                t1.total_money_hs as order_total_money_hs,
                t1.ext_val,
                t2.name as supplier_name,
                t3.goods_code,
                t3.goods_name,
                t3.goods_spec,
                t3.goods_model,
                t3.goods_unit,
                t3.quantity,
                t3.weight,
                t3.total_weight,
                t3.price,
                t3.price_hs,
                t3.total_money,
                t3.tax_rate * 100 as tax_rate,
                t3.total_money_hs,
                t4.quantity as instock_quantity
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrderDetail', 't1.id = t3.order_id', 't3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewOrderInstock', 't3.order_id = t4.order_id and t3.goods_id = t4.goods_id', 't4')
            ->where('t1.del_flag = 0 and t1.order_type = 1 and t3.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function selectById($id)
    {
        return PurchaseOrder::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseOrder::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function create($order_type = 1)
    {
        $row = new PurchaseOrder();
        $row->order_type = $order_type;
        return $this->build('create', $row);
    }

    public function update($row, $order_type = 1)
    {
        $row->order_type = $order_type;
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        return $this->executeInTransaction(function() use ($act, $row) {
            $order_date = $this->request->getPost('order_date', 'tstring');
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');

//            $request_id = $this->request->getPost('request_id', 'tstring');
            $remarks = $this->request->getPost('remarks', 'tstring');
            $files = urldecode($this->request->getPost('files', ['string', 'trim']));
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
            if (empty($order_date) || CheckUtil::is_empty ($supplier_id) ) {
                return $this->error(ErrorHelper::WRONG_INPUT) ;
            }
            $files = CvtUtil::emptyToArray($files);
            $ext_data = CvtUtil::emptyToArray($ext_data);
            $now = DateUtil::now();
            $user = SessionData::user();
            $row->order_date = $order_date;
//            $row->request_id = CvtUtil::blankToNull($request_id);
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->files = CvtUtil::arrayToNull($files);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $ss = new SequenceService();
                $seq_type = $row->order_type == 1 ? 12 : 4;
                $row->order_code = $ss->useSequence($seq_type);
                $row->supplier_id = $supplier_id;
                $row->status = 10;
                $row->status_name = Constant::$purchase_order_status[$row->status];
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            // 订单保存
            $row->save();
        });
    }

    public function detail($row)
    {
        return $this->executeInTransaction(function () use ($row) {
            $type = $this->request->getPost('type', 'tstring');
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');
            $request_id = $this->request->getPost('request_id', 'tstring');
            $order_date = $this->request->getPost('order_date', 'tstring');
            $detail_list = $this->request->getPost('detail_list');
            $apply_ids = $this->request->getPost('apply_ids');
            if (empty($type) || empty($order_date) || empty($detail_list)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }

            if ($type == 2) {
                if (empty($supplier_id)) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
            }

            $total_money = 0;
            $total_money_hs = 0;
            $total_quantity = 0;
            foreach ($detail_list as &$item) {
                if (!CheckUtil::isDecimal($item['price'])) {
                    return $this->error('无效的无税单价');
                }
                if (!CheckUtil::isDecimal($item['price_hs'])) {
                    return $this->error('无效的含税单价');
                }
                if (!CheckUtil::isDecimal($item['purchase_quantity'])) {
                    return $this->error('无效的采购数量');
                }
                $price  = CvtUtil::emptyToDouble($item['price']);
                $price_hs   = CvtUtil::emptyToDouble($item['price_hs']);
                $purchase_quantity  = CvtUtil::emptyToDouble($item['purchase_quantity']);

                // 单个商品采购金额（含税）
                $money_hs = round($price_hs * $purchase_quantity, 4);
                // 单个商品采购金额（未税）
                $money = round($price * $purchase_quantity, 4);
                // 总的采购数量
                $total_quantity += round($purchase_quantity, 4);
                // 总的采购金额（未税）
                $total_money += $money;
                // 总的采购金额（含税）
                $total_money_hs += $money_hs;
            }

            $now = DateUtil::now();
            $user = SessionData::user();

            $row->order_date = $order_date;
            $row->request_id = CvtUtil::blankToNull($request_id);
            $row->supplier_id = CvtUtil::blankToNull($supplier_id);
            $row->status = $type == 2 ? 20 : 10;
            $row->status_name = Constant::$purchase_order_status[$row->status];
            $row->total_money = round($total_money, 2);
            $row->total_money_hs = round($total_money_hs, 2);
            $row->total_quantity = $total_quantity;
            $row->detail_data = CvtUtil::arrayToNull($detail_list);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (empty($row->id)) {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->order_type = 1;
                $row->order_code = $ss->useSequence(12);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            $row->save();

            $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseApply';
            $phql .= ' SET status = 10, status_name = ?4, purchase_order_id = null, update_date = ?2, update_by = ?3';
            $phql .= ' WHERE del_flag = 0 and purchase_order_id = ?1';
            if (count($apply_ids) > 0) {
                $phql .= ' and id not in (' . implode(',', $apply_ids) . ')';
            }
            $result = $this->modelsManager->executeQuery($phql, [
                1 => $row->id,
                2 => $now,
                3 => $user->id,
                4 => Constant::$purchase_apply_status[10]
            ]);
            if (!$result->success()) {
                return $this->error($result->getMessage());
            }

            if (count($apply_ids) > 0) {
                foreach ($apply_ids as $apply_id) {
                    $apply_row = PurchaseApply::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $apply_id]]);
                    if (empty($apply_row)) {
                        return $this->error('采购申请已失效');
                    } else if (!empty($apply_row->purchase_order_id)) {
                        if ($apply_row->purchase_order_id != $row->id) {
                            return $this->error('采购申请已绑定其他采购订单');
                        }
                    } else {
                        $apply_row->purchase_order_id = $row->id;
                        $apply_row->status = 20;
                        $apply_row->status_name = Constant::$purchase_apply_status[$apply_row->status];
                        $apply_row->update_date = $now;
                        $apply_row->update_by = $user->id;
                        $apply_row->save();
                    }
                }
            }

            if ($type == 2) {
                $dcm = new DataCommonService();
                $dcm->submitDesign($row->id, 6, $user->group_id);
            }
        });
    }

    public function passSave($id)
    {
        $row = $this->selectById($id);
        if (empty($row) || $row->del_flag == 1) {
            throw new \Exception("记录已失效");
        } else if ($row->status != 20) {
            throw new \Exception("记录状态已变更");
        }
        $total_weight = 0;
        $total_money_hs = 0;
        $now = DateUtil::now();
        $user = SessionData::user();
        $details = CvtUtil::emptyToArray($row->detail_data);
        // 采购订单明细
        foreach ($details as $item)
        {
            $tax_rate = 0;
            $weight = 0;
            $goods_row = PurchaseGoods::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $item['goods_id']]]);
            if (!empty($goods_row)) {
                $goods_row->price_hs = $item['price_hs'];
                $goods_row->price = $item['price'];
                $goods_row->update_date = $now;
                $goods_row->update_by = $user->id;
                $goods_row->save();
                $tax_rate = CvtUtil::emptyToDouble($goods_row->tax_rate);
                $weight = CvtUtil::emptyToDouble($goods_row->weight);
            }

            $detail_row = new PurchaseOrderDetail();
            $detail_row->uid = UUID::make();
            $detail_row->order_id = $row->id;
            $detail_row->goods_id = $item['goods_id'];
            $detail_row->goods_code = $item['code'];
            $detail_row->goods_name = $item['name'];
            $detail_row->goods_model = $item['model'];
            // 采购单位
            $detail_row->goods_unit = $item['purchase_unit'];
            // 是否质检
            $detail_row->check_flag = $item['check_flag'];
            // 库存单位
            $detail_row->goods_deputy_unit = $item['inventory_unit'];
            // 库存数量
            $detail_row->quantity = $item['inventory_quantity'];
            // 采购数量
            $detail_row->purchase_quantity = $item['purchase_quantity'];
            $detail_row->weight = CvtUtil::zeroToNull($weight);
            $detail_row->total_weight = CvtUtil::zeroToNull(round(CvtUtil::emptyToDouble($detail_row->quantity) * $weight, 3));
            $detail_row->price = $item['price'];
            $detail_row->price_hs = $item['price_hs'];
            $detail_row->total_money = $item['total_money'];
            $detail_row->total_money_hs = $item['total_money_hs'];
            $detail_row->tax_rate = CvtUtil::zeroToNull($tax_rate);
            $detail_row->update_date = $now;
            $detail_row->update_by = $user->id;
            $detail_row->del_flag = 0;
            $detail_row->owner = $user->owner;
            $detail_row->save();

            $total_weight += CvtUtil::emptyToDouble($detail_row->total_weight);
            $total_money_hs += CvtUtil::emptyToDouble($detail_row->total_money_hs);
        }

        $row->status = 30;
        $row->status_name = Constant::$purchase_order_status[$row->status];
        $row->total_weight = CvtUtil::zeroToNull(round($total_weight, 3));
        $row->total_money_hs = CvtUtil::zeroToNull(round($total_money_hs, 3));
        $row->review_date = $now;
        $row->review_by = $user->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->save();

        // 采购申请审核通过
        $apply_rows = PurchaseApply::find(['del_flag = 0 and purchase_order_id = ?1', 'bind' => [1 => $row->id]]);
        foreach ($apply_rows as $apply_row)
        {
            $apply_row->status = 30;
            $apply_row->status_name = Constant::$purchase_apply_status[$apply_row->status];
            $apply_row->update_date = $now;
            $apply_row->update_by = $user->id;
            $apply_row->save();

            $wait_apply_cnt = PurchaseApply::count([
                'del_flag = 0 and status < 30 and request_id = ?1', 'bind' => [1 => $apply_row->request_id]
            ]);
            if ($wait_apply_cnt == 0) {
                $request_row = PurchaseRequest::findFirst([
                    'del_flag = 0 and id = ?1', 'bind' => [1 => $apply_row->request_id]
                ]);
                if (!empty($request_row)) {
                    $request_row->status = 30;
                    $request_row->status_name = Constant::$purchase_request_status[$request_row->status];
                    $request_row->update_date = $now;
                    $request_row->update_by = $user->id;
                    $request_row->save();

                    $product_row = MesProduct::findFirst([
                        'del_flag = 0 and id = ?1', 'bind' => [1 => $request_row->product_id]
                    ]);
                    if (!empty($product_row)) {
                        $product_row->goods_status = 30;
                        // TODO 待确认
//                        $product_row->goods_status_name = MesConstant::$product_goods_status[$product_row->goods_status];
                        $product_row->goods_status_name = MesConstant::$product_status[$product_row->goods_status];
                        $product_row->update_date = $now;
                        $product_row->update_by = $user->id;
                        $product_row->save();
                    }
                }
            }
        }
    }

    public function rejectSave($id, $reject_remarks)
    {
        $row = $this->selectById($id);
        if (empty($row) || $row->del_flag == 1) {
            throw new \Exception("记录已失效");
        } else if ($row->status != 20) {
            throw new \Exception("记录状态已变更");
        }

        $now = DateUtil::now();
        $user = SessionData::user();

        // 采购申请也恢复状态
//        $apply_row = PurchaseApply::find(['del_flag = 0 and purchase_order_id = ?1', 'bind' => [1 => $row->id]]);

        $row->status = 10;
        $row->status_name = Constant::$purchase_order_status[$row->status];
        $row->reject_status = 1;
        $row->reject_remarks = $reject_remarks;
        $row->review_date = $now;
        $row->review_by = $user->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        // 【采购订单申请-审核驳回】mes_check_error表更新失败
        $row->save();
    }

    public function delete()
    {
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)) {
            return $this->error(ErrorHelper::WRONG_INPUT);
        }
        $row = $this->selectByUid($uid);
        if (empty($row)) {
            return $this->error(ErrorHelper::WRONG_INPUT);
        } else if ($row->status != 10) {
            return $this->error('状态不正确');
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        $row->save();
    }

    public function cancelReview()
    {
        $row = $this->selectByUid($this->request->getPost('uid', 'tstring'));
        if (empty($row) || $row->del_flag == 1) {
            return ErrorHelper::ROW_NOTEXIST;
        } else if ($row->status != PurchaseOrder::STATUS_REVIEW) {
            return ErrorHelper::WRONG_STATUS;
        }
        $ws = new WorkService();
        return $ws->cancel(6, $row->id);
    }

    public function finish()
    {
        $uid = $this->request->getPost('uid', 'string');
        if (empty($uid)){
            return $this->error(ErrorHelper::WRONG_INPUT);
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return $this->error(ErrorHelper::WRONG_INPUT);
        }
        if ($row->status != 30){
            return $this->error('状态不正确');
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->status = 40;
        $row->status_name = Constant::$purchase_order_status[$row->status];
        $row->finish_date = $now;
        $row->finish_by = $user->id;
        $row->update_date = $now;
        $row->update_by = $user->id;
        $row->save();
    }

    public function getDetailList($id)
    {
       return PurchaseOrderDetail::find(['del_flag = 0 and order_id = ?1','bind'=>[1=>$id]]);
    }

    public function addApply()
    {
        $order_uid = $this->request->get('uid', 'tstring');
        $detail_ids = $this->request->get('detail_ids', 'tstring');
        $a_row = $this->selectByUid($order_uid);
        if (empty($a_row)) {
            return ErrorHelper::UNKOWN;
        }
        if ($a_row->status != 10) {
            return ErrorHelper::WRONG_ID;
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            $phql = 'UPDATE Envsan\Modules\Purchase\Model\PurchaseApply';
            $phql .= ' SET purchase_order_id = ?1, status = 20, update_date = ?3, update_by = ?4';
            $phql .= ' WHERE del_flag = 0 AND status = 10 AND purchase_order_id IS NULL AND id IN ('.trim($detail_ids, ',').')';
            $result = $this->modelsManager->executeQuery($phql, [
                1 => $a_row->id,
                3 => $now,
                4 => $user->id
            ]);
            if (!$result->success()) {
                throw new \Exception('【结算单-添加明细】trade_accounts_detail表更新失败！');
            }
            $details = $this->saveOrderDetail($a_row->id);
            $a_row->detail_data = CvtUtil::arrayToNull($details);
            $a_row->update_date = $now;
            $a_row->update_by = $user->id;
            if (!$a_row->save()) {
                throw new \Exception('PurchaseOrder表更新失败！');
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function saveOrderDetail($order_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
               t2.id,
               t2.uid,
               t2.code,
               t2.name,
               t2.spec,
               t2.unit,
               t2.model,
               t2.price,
               round(sum(t1.quantity), 4) as quantity
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApply', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoods','t1.goods_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.status = 20 and t1.purchase_order_id = ?1', [1 => $order_id])
            ->groupBy('t1.goods_id');
        return $builder->getQuery()->execute();
    }

    public function delApply()
    {
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $ids = $this->request->getPost('ids');
        if (count($ids) == 0 || empty($uid)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $a_row = $this->selectByUid($uid);
        if (empty($a_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($a_row->status != 10){
            return ErrorHelper::WRONG_INPUT;
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            foreach ($ids as $id){
                $apply_row = PurchaseApply::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$id]]);
                if (!empty($apply_row)){
                    $apply_row->status = 10;
                    $apply_row->purchase_order_id = null;
                    $apply_row->update_date = $now;
                    $apply_row->update_by = $user->id;
                    if (!$apply_row->save()) {
                        throw new \Exception('PurchaseOrder表更新失败！');
                    }
                }
            }
            $details = $this->saveOrderDetail($a_row->id);
            $a_row->detail_data = CvtUtil::arrayToNull($details);
            $a_row->update_date = $now;
            $a_row->update_by = $user->id;
            if (!$a_row->save()) {
                throw new \Exception('PurchaseOrder表更新失败！');
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function getApplyList($purchase_order_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.goods_id,
                t1.goods_code,
                t1.goods_name,
                t1.goods_spec,
                t1.goods_model,
                t1.goods_unit,
                round(t1.quantity,4) as quantity,
                t1.supplier_name,
                t1.remarks,
                t2.code as product_code,
                t2.name as product_name,
                t4.order_date,
                t4.order_code as order_code,
                t5.name as bom_name,
                0 as sel
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApply', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrderDetail', 't1.order_detail_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder','t3.order_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t1.product_bom_id = t5.id','t5')
            ->where('t1.del_flag = 0 and t1.status = 20 and t1.purchase_order_id = ?1', [1 => $purchase_order_id])
            ->orderBy('t4.order_date asc,t1.id asc');
        $rows = $builder->getQuery()->execute();
        $detail = $this->selectById($purchase_order_id);
        $detail_list = CvtUtil::emptyToArray($detail->detail_data);
        return [
            'apply' => $rows,
            'detail' => $detail_list
        ];
    }
}