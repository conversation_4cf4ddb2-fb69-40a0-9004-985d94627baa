<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInspectionDetail;
use Envsan\Modules\Purchase\Model\PurchaseWwInspection;
use Envsan\Modules\Purchase\Model\PurchaseWwInspectionDetail;
use Envsan\Modules\Purchase\Model\PurchaseWwReceipt;
use Envsan\Modules\Purchase\Model\PurchaseWwReceiptDetail;
use Envsan\Modules\Purchase\Util\Constant;

class PurchaseWwReceiptService extends BaseService
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwReceipt', 't1')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseWwReceipt();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        return $this->executeInTransaction(function() use ($row, $act) {

            // save:1,commit:
            $type = $this->request->getPost('type', 'tstring');
            // 业务类型
            // $business_type = $this->request->getPost('business_type', 'tstring');
            // 业务类型
            // $business_type_name = $this->request->getPost('business_type_name', 'tstring');
            // 单据号前缀
            $re_code = $this->request->getPost('re_code', 'tstring');
            // 单据号采番
            $code = $this->request->getPost('code', 'tstring');
            // 到货日期
            $receipt_date = $this->request->getPost('receipt_date', 'tstring');
            // 采购订单ID
            $order_id = $this->request->getPost('order_id', 'tstring');
            // 采购订单code
            $order_code = $this->request->getPost('order_code', 'tstring');
            // 采购订单ID
            $supplier_id = $this->request->getPost('supplier_id', 'tstring');
            // 采购订单code
            $supplier_code = $this->request->getPost('supplier_code', 'tstring');
            // 采购订单ID
            $supplier_name = $this->request->getPost('supplier_name', 'tstring');
            // 部门id
            $department_id = $this->request->getPost('department_id', 'tstring');
            // 部门名字
            $department_name = $this->request->getPost('department_name', 'tstring');
            // 币种
            $currency = $this->request->getPost('currency', 'tstring');
            // 汇率
            $exchange_rate = $this->request->getPost('exchange_rate', 'tstring');
            // 采购单明细
            $detail_data = $this->request->getPost('detail_data');
            // 备注
            $remarks = $this->request->getPost('remarks', 'tstring');

            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));

            // 判断必须输入项目
            if (empty($code) || empty($receipt_date) ||
                empty($detail_data) || empty($currency) || empty($exchange_rate) || CheckUtil::is_empty($order_id)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }

            if (!CheckUtil::isDecimalCommon($exchange_rate, 6)) {
                return $this->error('无效的汇率');
            }

            foreach ($detail_data as $detail)
            {
                if (!CheckUtil::isDecimal($detail['quantity'])) {
                    return $this->error('无效的数量');
                }
            }
            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);

            // $row->business_type = $business_type;
            // $row->business_type_name = $business_type_name;
            $row->receipt_code = $re_code . $code;
            $row->receipt_date = $receipt_date;
            $row->order_id = CvtUtil::blankToNull($order_id);
            $row->order_code = $order_code;
            $row->supplier_id = CvtUtil::blankToNull($supplier_id);
            $row->supplier_code = $supplier_code;
            $row->supplier_name = $supplier_name;
            $row->department_id = $department_id;
            $row->department_name = $department_name;
            $row->currency = $currency;
            $row->exchange_rate = $exchange_rate;
            $row->detail_data = CvtUtil::arrayToNull($detail_data);
            $row->remarks = $remarks;
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;

            // 新增,并且是save的场合
            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->status = 10;
                $row->status_name = Constant::$purchase_receipt_status[$row->status];
                $row->owner = $user->owner;
            }
            // 提交审批
            if ($type == 2) {
                $row->status = 20;
                $row->status_name = Constant::$purchase_receipt_status[$row->status];
            }
            $row->save();

            if ($type == 2) {
                foreach ($detail_data as $detail) {
                    $PurchaseWwReceiptDetail = new PurchaseWwReceiptDetail();
                    // 基础信息
                    $PurchaseWwReceiptDetail->uid = UUID::make();
                    $PurchaseWwReceiptDetail->receipt_id = $row->id;
                    // 物料信息
                    $PurchaseWwReceiptDetail->goods_id = $detail['goods_id'] ?? null;
                    $PurchaseWwReceiptDetail->product_name = $detail['product_name'] ?? null;
                    $PurchaseWwReceiptDetail->product_code = $detail['product_code'] ?? null;
                    $PurchaseWwReceiptDetail->goods_unit = $detail['goods_unit'] ?? null;
                    $PurchaseWwReceiptDetail->goods_deputy_unit = $detail['goods_deputy_unit'] ?? null;
                    $PurchaseWwReceiptDetail->bom_name = $detail['bom_name'] ?? null;
                    // 数量和价格信息
                    $PurchaseWwReceiptDetail->quantity = CvtUtil::blankToNull($detail['quantity']);
                    $PurchaseWwReceiptDetail->pricing_quantity = CvtUtil::blankToNull($detail['pricing_quantity']);
                    $PurchaseWwReceiptDetail->conversion_rate = CvtUtil::blankToNull($detail['conversion_rate']);
                    $PurchaseWwReceiptDetail->price = CvtUtil::blankToNull($detail['price']);
                    $PurchaseWwReceiptDetail->price_hs = CvtUtil::blankToNull($detail['price_hs']);
                    $PurchaseWwReceiptDetail->total_amount = CvtUtil::blankToNull($detail['total_money']);
                    $PurchaseWwReceiptDetail->tax_rate = CvtUtil::blankToNull($detail['tax_rate']);
                    $PurchaseWwReceiptDetail->tax_amount = CvtUtil::blankToNull($detail['tax_money']);
                    $PurchaseWwReceiptDetail->total_amount_hs = CvtUtil::blankToNull($detail['total_money_hs']);
                    // 检验信息
                    $PurchaseWwReceiptDetail->check_status = $detail['check_status'] ?? null;
                    $PurchaseWwReceiptDetail->check_flag = $detail['check_flag'] ?? 0;
                    // 来源信息
                    $PurchaseWwReceiptDetail->order_detail_id = $detail['order_detail_id'] ?? null;
                    $PurchaseWwReceiptDetail->apply_id = $detail['id'] ?? null;
                    $PurchaseWwReceiptDetail->apply_code = $detail['entrust_code'] ?? null;
                    // 标准字段
                    $PurchaseWwReceiptDetail->del_flag = 0;
                    $PurchaseWwReceiptDetail->group_id = $user->group_id;
                    $PurchaseWwReceiptDetail->owner = $user->owner;
                    $PurchaseWwReceiptDetail->update_date = $now;
                    $PurchaseWwReceiptDetail->update_by = $user->id;
                    // 状态信息
                    $PurchaseWwReceiptDetail->status = 10; // 初始状态
                    $PurchaseWwReceiptDetail->status_name = Constant::$purchase_receipt_status[10];
                    // 保存明细
                    $PurchaseWwReceiptDetail->save();
                }
            }
        });
    }


    /**
     * 审核通过
     * @return array
     */
    public function approval()
    {
        return $this->executeInTransaction(function () {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $PurchaseWwReceipt = $this->selectByUid($uid);

            if (empty($PurchaseWwReceipt)) {
                return $this->error('单据不存在');
            }

            $now = DateUtil::now();
            $user = SessionData::user();
            $PurchaseWwReceipt->comment = $comment;
            $PurchaseWwReceipt->approved_by = $user->id;
            $PurchaseWwReceipt->update_date = $now;
            $PurchaseWwReceipt->update_by = $user->id;
            $PurchaseWwReceipt->status = 30;
            $PurchaseWwReceipt->status_name = Constant::$purchase_receipt_status[$PurchaseWwReceipt->status];
            $PurchaseWwReceipt->save();

            // 获取不需要报检的明细
            $receiptDetailNoCheck = PurchaseWwReceiptDetail::find(['receipt_id = ?1 and del_flag = 0', 'bind' => [1 => $PurchaseWwReceipt->id]]);
            foreach ($receiptDetailNoCheck as $i => $detail) {
                $detail->update_date = $now;
                $detail->update_by = $user->id;
                $detail->status = 30;
                $detail->status_name = Constant::$purchase_receipt_status[$detail->status];
                $detail->save();
            }

            // 获取需要报检的明细
            $receiptDetailCheck = PurchaseWwReceiptDetail::find(['receipt_id = ?1 and del_flag = 0 and check_flag = 1', 'bind' => [1 => $PurchaseWwReceipt->id]]);
            // 外委检验单号
            $inspection_type_code = 'WWBT' . date('ym');
            // 拿到当前最大号
            // 返回的是下一个可用号，比如 '0005'
            $inspection_max_code = $this->getInspectionCode($inspection_type_code);
            $inspection_code = $inspection_type_code . $inspection_max_code;

            // 外委检验单号
            $check_type_code = 'WWTY' . date('ym');
            // 拿到当前最大号
            // 返回的是下一个可用号，比如 '0005'
            $check_max_code = $this->getCheckCode($check_type_code);
            $max_num = intval($check_max_code);

            if ($receiptDetailCheck->count() > 0) {
                // 插入报检单
                $inspection_id = $this->insertOrUpdateInspection($inspection_code, $now, $PurchaseWwReceipt, $user);
                foreach ($receiptDetailCheck as $i => $detail) {
                    // 插入报检单明细
                    $this->insertOrUpdateInspectionDetail($detail, $now, $user, $inspection_id, $max_num, $check_type_code);
                    $max_num++;
                }
            }
        });
    }

    /**
     * 审核驳回
     * @return array
     */
    public function reject()
    {
        return $this->executeInTransaction(function() {
            // uid
            $uid = $this->request->getPost('uid', 'tstring');
            // 审单通过的评论
            $comment = $this->request->getPost('comment', 'tstring');
            $PurchaseWwReceipt = $this->selectByUid($uid);
            if (empty($PurchaseWwReceipt)) {
                return $this->error('单据不存在');
            }

            $now = DateUtil::now();
            $user = SessionData::user();
            $PurchaseWwReceipt->comment = $comment;
            $PurchaseWwReceipt->approved_by = $user->id;
            $PurchaseWwReceipt->update_date = $now;
            $PurchaseWwReceipt->update_by = $user->id;
            $PurchaseWwReceipt->status = 10;
            $PurchaseWwReceipt->status_name = '以驳回';
            $PurchaseWwReceipt->save();

            $receiptDetail = PurchaseWwReceiptDetail::find(['receipt_id = ?1 and del_flag = 0', 'bind' => [1 => $PurchaseWwReceipt->id]]);

            foreach ($receiptDetail as $i => $detail) {
                $detail->delete();
            }
        });
    }

    /**
     * 更新或者插入一条报检单
     * @param $max_num
     * @param $i
     * @param $type_code
     * @param $now
     * @param $PurchaseWwReceipt
     * @return int
     */
    private function insertOrUpdateInspection($inspection_code, $now, $PurchaseWwReceipt, $user): int
    {
        $purchaseInspection = new PurchaseWwInspection();

        $purchaseInspection->uid = UUID::make();
        // 编码
        $purchaseInspection->inspection_code = $inspection_code;
        // 报检日期
        $purchaseInspection->inspection_day = DateUtil::today();
        // 报检时间
        $purchaseInspection->inspection_date = $now;
        // 报检部门
        $purchaseInspection->inspection_department = $PurchaseWwReceipt->department_name;
        // TODO
        $purchaseInspection->check_type = '采购检验';
        $purchaseInspection->department_id = $PurchaseWwReceipt->department_id;
        $purchaseInspection->department_name = $PurchaseWwReceipt->department_name;
        // 到货信息
        $purchaseInspection->receipt_id = $PurchaseWwReceipt->id;
        $purchaseInspection->receipt_code = $PurchaseWwReceipt->receipt_code;
        $purchaseInspection->receipt_date = $PurchaseWwReceipt->receipt_date;
        // 供应商信息
        $purchaseInspection->supplier_id = $PurchaseWwReceipt->supplier_id;
        $purchaseInspection->supplier_code = $PurchaseWwReceipt->supplier_code;
        $purchaseInspection->supplier_name = $PurchaseWwReceipt->supplier_name;
        // 状态信息
        $purchaseInspection->status = 30;
        $purchaseInspection->status_name = Constant::$purchase_inspection_status[$purchaseInspection->status];
        // 标准字段
        $purchaseInspection->del_flag = 0;
        $purchaseInspection->group_id = $user->group_id;
        $purchaseInspection->owner = $user->owner;
        $purchaseInspection->update_date = $now;
        $purchaseInspection->update_by = $user->id;
        // 保存
        $purchaseInspection->save();
        return $purchaseInspection->id;
    }

    /**
     * 更新或者插入一条报检单明细
     * @param $max_num
     * @param $i
     * @param $type_code
     * @param $now
     * @param $PurchaseWwReceipt
     * @return void
     */
    private function insertOrUpdateInspectionDetail($detail, $now, $user, $inspection_id, $max_num ,$check_max_code)
    {
        $inspectionDetail = new PurchaseWwInspectionDetail();
        // 检验单号
        $check_code = $check_max_code .str_pad($max_num, 4, '0', STR_PAD_LEFT);

        $inspectionDetail->uid = UUID::make();
        // 到货单ID
        $inspectionDetail->inspection_id = $inspection_id;
        // 到货单明细ID
        $inspectionDetail->receipt_detail_id = $detail->id;
        // 物料信息
        $inspectionDetail->goods_id = $detail->goods_id;
        $inspectionDetail->goods_code = $detail->goods_code;
        $inspectionDetail->goods_name = $detail->goods_name;
        $inspectionDetail->goods_model = $detail->goods_model;
        $inspectionDetail->goods_unit = $detail->goods_unit;
        $inspectionDetail->purchase_quantity = $detail->pricing_quantity;
        $inspectionDetail->goods_deputy_unit = $detail->goods_deputy_unit;
        $inspectionDetail->quantity = $detail->quantity;

        // 来料检验内容初始化
        $inspectionDetail->check_status = 10;
        $inspectionDetail->check_images = null;
        $inspectionDetail->check_code = $check_code;

        // 状态信息
        $inspectionDetail->status = 10;
        $inspectionDetail->status_name = Constant::$purchase_inspection_status[$inspectionDetail->status];
        // 标准字段
        $inspectionDetail->del_flag = 0;
        $inspectionDetail->group_id = $user->group_id;
        $inspectionDetail->owner = $user->owner;
        $inspectionDetail->update_date = $now;
        $inspectionDetail->update_by = $user->id;
        // 保存
        $inspectionDetail->save();
    }

    public function getOrders()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.order_code,
                t2.id as supplier_id,
                t2.code as supplier_code,
                t2.name as supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            // 先处理订单状态是外委的，order_type = 2
            ->where('t1.del_flag = 0 and t1.order_type = 2 and t1.owner = ?1 and t1.status < 40', [1 => SessionData::ownerId()])
            ->orderBy('t1.status asc, t1.order_code desc');
        return $builder->getQuery()->execute()->toArray();

    }

    /**
     * 判断是否重复
     * @param $name
     * @param $id
     * @return bool
     */
    private function isRepeat($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwReceipt')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return PurchaseWwReceipt::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseWwReceipt::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    /**
     * 取得当前供应商所有没完成的采购单的采购单明细
     * @param $supplier_id
     * @param $ids
     */
    public function selectOrderDetail($order_id, $ids)
    {

        if (empty($order_id)) {
            return [];
        }

        // 产品名字
        $product_name = $this->request->get('product_name', 'tstring');
        // 工序名称
        $bom_name = $this->request->get('bom_name', 'tstring');
        // 规格型号
        $product_model = $this->request->get('product_model', 'tstring');

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t.id,
                t.uid,
                t.code as entrust_code,
                0 as sel,
                t.quantity as plan_cnt,
                t2.code as product_code,
                t2.name as product_name,
                t2.goods_id,
                t3.name as customer_name,
                t4.code,
                t5.name as bom_name,
                t10.price,
                t10.price_hs,
                t10.purchase_quantity as pricing_cnt, 
                t10.conversion_rate,
                t10.goods_deputy_unit,
                t10.goods_unit,
                t10.tax_rate,
                t10.id as order_detail_id,
                t7.order_code,
                ifnull(t8.quantity,0) as out_cnt,
                ifnull(t9.quantity,0) as in_cnt,
                1 as show_flag,
                t10.check_flag,
                IF(t10.check_flag = 1, \'是\', \'否\') as check_status
            ')
            // 一个外委计划只能被一个外委加工单的详细关联
            // 一个订单是可以有多次出库的
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust', 't')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail', 't.notice_detail_id = t1.id', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 't1.product_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't2.customer_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice', 't1.notice_id = t4.id', 't4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom', 't.product_bom_id = t5.id', 't5')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustOrderOut', 't.id = t8.entrust_id', 't8')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseViewEntrustOrderIn', 't8.entrust_id = t9.entrust_id and t8.order_id = t9.order_id', 't9')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't.purchase_order_id = t7.id and t7.id = ?1', 't7')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrderDetail', 't7.id = t10.order_id and t.id = t10.apply_id', 't10')
            ->where('t1.del_flag = 0 and t8.order_id = ?1', [1 => $order_id])
            ->orderBy('t1.id asc');

        if (!empty($ids)) {
            $builder->notInWhere('t.id', explode(',', $ids));
        }

        if (!empty($product_name)) {
            $builder->andWhere("t2.name like  ?3", [3 => '%' . $product_name . '%']);
        }

        if (!empty($product_model)) {
            $builder->andWhere("t2.code like ?4", [4 => '%' . $product_model . '%']);
        }

        if (!empty($bom_name)) {
            $builder->andWhere("t5.name like ?5", [5 => '%' . $bom_name . '%']);
        }
        return $builder;
    }

    /**
     * 最大的到货编号
     * @param $type_code
     * @return string
     */
    public function getReceiptCode($type_code): string
    {
        $receipt_row = PurchaseWwReceipt::findFirst([
            'receipt_code like ?1 and length(receipt_code) = ?2',
            'bind' => [1 => "$type_code%", 2 => strlen($type_code) + 4],
            'order' => 'receipt_code desc'
        ]);
        if (empty($receipt_row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($receipt_row->receipt_code, strlen($type_code))) + 1;
        }
        return str_pad($cnt, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 最大的报检单编号
     * @param $type_code
     * @return string
     */
    public function getInspectionCode($type_code): string
    {
        $inspection_row = PurchaseWwInspection::findFirst([
            'inspection_code like ?1 and length(inspection_code) = ?2',
            'bind' => [1 => "$type_code%", 2 => strlen($type_code) + 4],
            'order' => 'inspection_code desc'
        ]);
        if (empty($inspection_row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($inspection_row->inspection_code, strlen($type_code))) + 1;
        }
        return str_pad($cnt, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 最大的报检单编号
     * @param $type_code
     * @return string
     */
    public function getCheckCode($type_code): string
    {
        $check_row = PurchaseInspectionDetail::findFirst([
            'check_code like ?1 and length(check_code) = ?2',
            'bind' => [1 => "$type_code%", 2 => strlen($type_code) + 4],
            'order' => 'check_code desc'
        ]);
        if (empty($check_row)) {
            $cnt = 1;
        } else {
            $cnt = intval(substr($check_row->check_code, strlen($type_code))) + 1;
        }
        return str_pad($cnt, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 到货单中取得所有可以入库的详细
     * @param $id
     * @return mixed
     */
    public function getAllReceipts()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.receipt_code,
                t1.supplier_name
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseWwReceipt', 't1')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseWwReceiptDetail', 't1.id = t2.receipt_id and t2.del_flag = 0 and t2.status != 40', 't2')
            ->innerJoin('Envsan\Modules\Purchase\Model\PurchaseInspectionDetail', 't3.receipt_detail_id = t2.id and t3.del_flag = 0', 't3')
            ->where('t1.del_flag = 0 and t1.status = 30 and  t1.owner = ?1 and t3.check_result_flag != 1', [1 => SessionData::user()->owner])
            ->groupBy('t1.id, t1.receipt_code, t1.supplier_name');
        return $builder->getQuery()->execute();
    }
}