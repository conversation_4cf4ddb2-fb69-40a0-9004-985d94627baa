(self["webpackChunksfp_ext"] = self["webpackChunksfp_ext"] || []).push([["src_view_mingjing_produce_scan_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=script&lang=js":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=script&lang=js ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.push.js */ "./node_modules/core-js/modules/es.array.push.js");
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.iterator.constructor.js */ "./node_modules/core-js/modules/es.iterator.constructor.js");
/* harmony import */ var core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_iterator_find_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.iterator.find.js */ "./node_modules/core-js/modules/es.iterator.find.js");
/* harmony import */ var core_js_modules_es_iterator_find_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_iterator_find_js__WEBPACK_IMPORTED_MODULE_2__);



/* harmony default export */ __webpack_exports__["default"] = ({
  name: "ProduceScan",
  data() {
    return {
      selectedBatch: '',
      selectedProduct: '',
      batchOptions: [],
      productOptions: [],
      batchLoading: false,
      recentRecords: []
    };
  },
  mounted() {
    this.loadRecentRecords();
    // 初始加载一些批次数据
    this.queryBatches('');
  },
  methods: {
    // 查询批次列表（支持远程搜索）
    queryBatches(query) {
      this.batchLoading = true;
      this.$http.post('mes/produce/batches', {
        keyword: query || '',
        limit: 20
      }).then(rs => {
        this.batchLoading = false;
        if (rs.status === 'ok') {
          this.batchOptions = rs.data;
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.batchLoading = false;
        this.$message.error('获取批次列表失败');
      });
    },
    // 批次选择改变时，加载对应的产品
    onBatchChange(batchId) {
      this.selectedProduct = '';
      this.productOptions = [];
      if (!batchId) return;
      this.$http.post('mes/produce/products', {
        batch_id: batchId
      }).then(rs => {
        if (rs.status === 'ok') {
          this.productOptions = rs.data;
          if (rs.data.length === 0) {
            this.$message.warning('该批次下没有可检验的产品');
          }
        } else {
          this.$message.error(rs.message);
        }
      }).catch(() => {
        this.$message.error('获取产品列表失败');
      });
    },
    // 提交检验
    handleSubmit() {
      if (!this.selectedBatch || !this.selectedProduct) {
        this.$message.warning('请选择批次和产品');
        return;
      }

      // 保存到最近记录
      this.saveToRecentRecords();

      // 跳转到检验详情页面
      this.redirectToDetail(this.selectedBatch, this.selectedProduct);
    },
    // 重置表单
    resetForm() {
      this.selectedBatch = '';
      this.selectedProduct = '';
      this.productOptions = [];
    },
    // 跳转到检验详情页面
    redirectToDetail(batchId, productId) {
      console.log('redirectToDetail called with:', batchId, productId);
      try {
        this.$router.push({
          name: 'mingjingproducedetail',
          params: {
            batch_id: batchId,
            product_id: productId
          }
        });
        console.log('Router push completed');
      } catch (error) {
        console.error('Router push failed:', error);
        this.$message.error('页面跳转失败：' + error.message);
      }
    },
    // 保存到最近记录
    saveToRecentRecords() {
      const batchInfo = this.batchOptions.find(b => b.id === this.selectedBatch);
      const productInfo = this.productOptions.find(p => p.id === this.selectedProduct);
      if (!batchInfo || !productInfo) return;
      const now = new Date();
      const timeStr = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0') + '-' + String(now.getDate()).padStart(2, '0') + ' ' + String(now.getHours()).padStart(2, '0') + ':' + String(now.getMinutes()).padStart(2, '0');

      // 检查是否已存在相同记录
      const existingIndex = this.recentRecords.findIndex(item => item.batch_id === this.selectedBatch && item.product_id === this.selectedProduct);
      if (existingIndex > -1) {
        this.recentRecords.splice(existingIndex, 1);
      }

      // 添加到最前面
      this.recentRecords.unshift({
        batch_id: this.selectedBatch,
        product_id: this.selectedProduct,
        batch_code: batchInfo.code,
        product_name: productInfo.name,
        time: timeStr
      });

      // 只保留最近10条记录
      if (this.recentRecords.length > 10) {
        this.recentRecords = this.recentRecords.slice(0, 10);
      }

      // 保存到localStorage
      localStorage.setItem('produce_inspection_history', JSON.stringify(this.recentRecords));
    },
    // 加载最近记录
    loadRecentRecords() {
      try {
        const saved = localStorage.getItem('produce_inspection_history');
        if (saved) {
          this.recentRecords = JSON.parse(saved);
        }
      } catch (e) {
        console.log('加载检验历史失败:', e);
      }
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=template&id=528c98ed&scoped=true":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=template&id=528c98ed&scoped=true ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "container"
  }, [_vm._m(0), _c('div', {
    staticClass: "select-container"
  }, [_c('div', {
    staticClass: "select-box"
  }, [_c('i', {
    staticClass: "el-icon-s-check",
    staticStyle: {
      "font-size": "150px",
      "color": "#409EFF"
    }
  }), _vm._m(1), _c('div', {
    staticClass: "select-form"
  }, [_c('el-row', {
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "生产批次",
      "label-width": "100px"
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "placeholder": "请选择生产批次",
      "filterable": "",
      "remote": "",
      "remote-method": _vm.queryBatches,
      "loading": _vm.batchLoading
    },
    on: {
      "change": _vm.onBatchChange
    },
    model: {
      value: _vm.selectedBatch,
      callback: function ($$v) {
        _vm.selectedBatch = $$v;
      },
      expression: "selectedBatch"
    }
  }, _vm._l(_vm.batchOptions, function (batch) {
    return _c('el-option', {
      key: batch.id,
      attrs: {
        "label": batch.code + ' - ' + batch.customer_name,
        "value": batch.id
      }
    }, [_c('span', {
      staticStyle: {
        "float": "left"
      }
    }, [_vm._v(_vm._s(batch.code))]), _c('span', {
      staticStyle: {
        "float": "right",
        "color": "#8492a6",
        "font-size": "13px"
      }
    }, [_vm._v(_vm._s(batch.customer_name))])]);
  }), 1)], 1)], 1), _c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "产品名称",
      "label-width": "100px"
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "placeholder": "请先选择批次",
      "filterable": "",
      "disabled": !_vm.selectedBatch || _vm.productOptions.length === 0
    },
    model: {
      value: _vm.selectedProduct,
      callback: function ($$v) {
        _vm.selectedProduct = $$v;
      },
      expression: "selectedProduct"
    }
  }, _vm._l(_vm.productOptions, function (product) {
    return _c('el-option', {
      key: product.id,
      attrs: {
        "label": product.name + ' - ' + product.code,
        "value": product.id
      }
    }, [_c('span', {
      staticStyle: {
        "float": "left"
      }
    }, [_vm._v(_vm._s(product.name))]), _c('span', {
      staticStyle: {
        "float": "right",
        "color": "#8492a6",
        "font-size": "13px"
      }
    }, [_vm._v(_vm._s(product.code))])]);
  }), 1)], 1)], 1)], 1), _c('div', {
    staticClass: "button-group"
  }, [_c('el-button', {
    attrs: {
      "type": "primary",
      "size": "large",
      "disabled": !_vm.selectedBatch || !_vm.selectedProduct
    },
    on: {
      "click": _vm.handleSubmit
    }
  }, [_vm._v(" 开始检验 ")]), _c('el-button', {
    attrs: {
      "size": "large"
    },
    on: {
      "click": _vm.resetForm
    }
  }, [_vm._v("重置")])], 1)], 1)])]), _vm.recentRecords.length > 0 ? _c('div', {
    staticClass: "recent-records"
  }, [_c('h3', [_vm._v("最近检验记录")]), _c('el-table', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "data": _vm.recentRecords
    }
  }, [_c('el-table-column', {
    attrs: {
      "prop": "batch_code",
      "label": "批次号",
      "width": "150"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "product_name",
      "label": "产品名称",
      "width": "200"
    }
  }), _c('el-table-column', {
    attrs: {
      "prop": "time",
      "label": "检验时间",
      "width": "180"
    }
  }), _c('el-table-column', {
    attrs: {
      "label": "操作"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function (scope) {
        return [_c('el-button', {
          attrs: {
            "type": "text"
          },
          on: {
            "click": function ($event) {
              return _vm.redirectToDetail(scope.row.batch_id, scope.row.product_id);
            }
          }
        }, [_vm._v(" 重新检验 ")])];
      }
    }], null, false, 3936277189)
  })], 1)], 1) : _vm._e()]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "header"
  }, [_c('h1', [_vm._v("产品检验")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "select-title"
  }, [_c('span', {
    staticStyle: {
      "font-size": "40px",
      "color": "#303133"
    }
  }, [_vm._v("请选择批次和产品")])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/core-js/modules/es.iterator.find.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.iterator.find.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

"use strict";

var $ = __webpack_require__(/*! ../internals/export */ "./node_modules/core-js/internals/export.js");
var call = __webpack_require__(/*! ../internals/function-call */ "./node_modules/core-js/internals/function-call.js");
var iterate = __webpack_require__(/*! ../internals/iterate */ "./node_modules/core-js/internals/iterate.js");
var aCallable = __webpack_require__(/*! ../internals/a-callable */ "./node_modules/core-js/internals/a-callable.js");
var anObject = __webpack_require__(/*! ../internals/an-object */ "./node_modules/core-js/internals/an-object.js");
var getIteratorDirect = __webpack_require__(/*! ../internals/get-iterator-direct */ "./node_modules/core-js/internals/get-iterator-direct.js");
var iteratorClose = __webpack_require__(/*! ../internals/iterator-close */ "./node_modules/core-js/internals/iterator-close.js");
var iteratorHelperWithoutClosingOnEarlyError = __webpack_require__(/*! ../internals/iterator-helper-without-closing-on-early-error */ "./node_modules/core-js/internals/iterator-helper-without-closing-on-early-error.js");

var findWithoutClosingOnEarlyError = iteratorHelperWithoutClosingOnEarlyError('find', TypeError);

// `Iterator.prototype.find` method
// https://tc39.es/ecma262/#sec-iterator.prototype.find
$({ target: 'Iterator', proto: true, real: true, forced: findWithoutClosingOnEarlyError }, {
  find: function find(predicate) {
    anObject(this);
    try {
      aCallable(predicate);
    } catch (error) {
      iteratorClose(this, 'throw', error);
    }

    if (findWithoutClosingOnEarlyError) return call(findWithoutClosingOnEarlyError, this, predicate);

    var record = getIteratorDirect(this);
    var counter = 0;
    return iterate(record, function (value, stop) {
      if (predicate(value, counter++)) return stop(value);
    }, { IS_RECORD: true, INTERRUPTED: true }).result;
  }
});


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "./node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.container[data-v-528c98ed] {\n    padding: 20px;\n    background-color: #f5f7fa;\n    min-height: 100vh;\n}\n.header[data-v-528c98ed] {\n    text-align: center;\n    margin-bottom: 50px;\n}\n.header h1[data-v-528c98ed] {\n    color: #303133;\n    font-size: 32px;\n    margin: 0;\n}\n.select-container[data-v-528c98ed] {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-pack: center;\n        -ms-flex-pack: center;\n            justify-content: center;\n    margin-bottom: 50px;\n}\n.select-box[data-v-528c98ed] {\n    width: 800px;\n    background: white;\n    border-radius: 10px;\n    border: 2px solid #DCDFE6;\n    padding: 50px;\n    text-align: center;\n    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n.select-title[data-v-528c98ed] {\n    margin: 30px 0;\n}\n.select-form[data-v-528c98ed] {\n    text-align: left;\n    margin-top: 40px;\n}\n.button-group[data-v-528c98ed] {\n    text-align: center;\n    margin-top: 40px;\n}\n.button-group .el-button[data-v-528c98ed] {\n    margin: 0 10px;\n    padding: 12px 30px;\n}\n.recent-records[data-v-528c98ed] {\n    max-width: 1000px;\n    margin: 0 auto;\n    background: white;\n    padding: 20px;\n    border-radius: 8px;\n    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n.recent-records h3[data-v-528c98ed] {\n    margin-top: 0;\n    color: #303133;\n}\n\n/* 自定义下拉框样式 */\n.el-select-dropdown__item[data-v-528c98ed] {\n    padding: 8px 20px;\n}\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css */ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.id, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = (__webpack_require__(/*! !../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js")["default"])
var update = add("2fd549e0", content, false, {"sourceMap":false,"shadowMode":false});
// Hot Module Replacement
if(false) // removed by dead control flow
{}

/***/ }),

/***/ "./src/view/mingjing/produce_scan.vue":
/*!********************************************!*\
  !*** ./src/view/mingjing/produce_scan.vue ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _produce_scan_vue_vue_type_template_id_528c98ed_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./produce_scan.vue?vue&type=template&id=528c98ed&scoped=true */ "./src/view/mingjing/produce_scan.vue?vue&type=template&id=528c98ed&scoped=true");
/* harmony import */ var _produce_scan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./produce_scan.vue?vue&type=script&lang=js */ "./src/view/mingjing/produce_scan.vue?vue&type=script&lang=js");
/* harmony import */ var _produce_scan_vue_vue_type_style_index_0_id_528c98ed_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css */ "./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");



;


/* normalize component */

var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _produce_scan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _produce_scan_vue_vue_type_template_id_528c98ed_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _produce_scan_vue_vue_type_template_id_528c98ed_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "528c98ed",
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/view/mingjing/produce_scan.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/view/mingjing/produce_scan.vue?vue&type=script&lang=js":
/*!********************************************************************!*\
  !*** ./src/view/mingjing/produce_scan.vue?vue&type=script&lang=js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_scan.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css":
/*!****************************************************************************************************!*\
  !*** ./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css ***!
  \****************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_style_index_0_id_528c98ed_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css */ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=style&index=0&id=528c98ed&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_style_index_0_id_528c98ed_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_style_index_0_id_528c98ed_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_style_index_0_id_528c98ed_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_style_index_0_id_528c98ed_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./src/view/mingjing/produce_scan.vue?vue&type=template&id=528c98ed&scoped=true":
/*!**************************************************************************************!*\
  !*** ./src/view/mingjing/produce_scan.vue?vue&type=template&id=528c98ed&scoped=true ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_template_id_528c98ed_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_template_id_528c98ed_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_scan_vue_vue_type_template_id_528c98ed_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_scan.vue?vue&type=template&id=528c98ed&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_scan.vue?vue&type=template&id=528c98ed&scoped=true");


/***/ })

}]);
//# sourceMappingURL=src_view_mingjing_produce_scan_vue.3645ebe2.js.map