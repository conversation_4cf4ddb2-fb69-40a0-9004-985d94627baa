<script>
    $.validator.addMethod("sysNumber",function( value,element ){
        return this.optional(element) || /^\d+(\.\d{1,3})?$/.test(value);
    }, "请输入最多三位小数的正数");

    $.validator.addMethod("sysMoney",function( value,element ){
        return this.optional(element) || /^\d+(\.\d{1,2})?$/.test(value);
    }, "请输入最多两位小数的正数");

    $.validator.addMethod( "loginName", function( value, element ) {
        return this.optional( element ) || /^[a-zA-Z0-9_]{1,20}$/.test(value);
    }, "格式错误，仅支持字母、数字、“_”");

    $.validator.addMethod( "password", function( value, element ) {
        return this.optional( element ) || /^[a-zA-Z0-9_]{1,20}$/.test(value);
    }, "格式错误，仅支持字母、数字、“_”");

    $.validator.addMethod( "code", function( value, element ) {
        return this.optional( element ) || /^[a-zA-Z0-9]*$/.test(value);
    }, "格式错误，仅支持字母、数字");

    $.validator.addMethod( "email", function( value, element ) {
        return this.optional( element ) || /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(value);
    }, "请输入正确的邮箱格式");

    $.validator.addMethod( "mobile", function( value, element ) {
        return this.optional( element ) || /^[0-9]*$/.test(value);
    }, "格式错误，仅支持数字");
</script>