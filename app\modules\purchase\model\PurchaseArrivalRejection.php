<?php

namespace Envsan\Modules\Purchase\Model;

use Envsan\Common\Model\BaseModel;

class PurchaseArrivalRejection extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $defect_handling_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $defect_handling_code;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $rejection_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $supplier_id;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $supplier_name;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $goods_model;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var double
     * @Column(type="double", length=15, nullable=true)
     */
    public $rejection_quantity;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $rejection_reasons;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $rejection_reasons_dis;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", length=500, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $approval_user_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $approval_user_name;

    /**
     *
     * @var string
     * @Column(type="string", length=500, nullable=true)
     */
    public $approval_comment;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", length=1, nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $group_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_arrival_rejection';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseArrivalRejection[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseArrivalRejection
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
