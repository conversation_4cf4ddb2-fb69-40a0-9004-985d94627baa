<?php

namespace Envsan\Modules\Mes\Model;


use Envsan\Common\Model\BaseModel;
class MesReport extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=true)
     */
    public $uid;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $report_month;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $report_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $user_cnt;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $user_cost;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $report_data;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $review_user_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $review_time;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_data;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $ext_val;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mes_report';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesReport[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return MesReport
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
