{% do assets.collection('js').addJs('static/global/plugins/qrcode/qrcode.min.js') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">用户一览</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">姓名</label>
                            <div class="col-md-9">
                                <input name="real_name" type="text" class="form-control" v-model="real_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">工号</label>
                            <div class="col-md-9">
                                <input name="empno" type="text" class="form-control" v-model="empno"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">登录名</label>
                            <div class="col-md-9">
                                <input name="login_name" type="text" class="form-control" v-model="login_name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">手机号</label>
                            <div class="col-md-9">
                                <input name="mobile" type="text" class="form-control" v-model="mobile"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">部门</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="group" v-model="group_name" required readonly>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default btn-flat" @click="selectGroup"><i class="fa fa-search"></i></button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-offset-4 col-md-4 col-lg-offset-6 col-lg-3" style="text-align: right;">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('sys/user/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                    <tr>
                        <th data-field="uid" data-visible="false">UID</th>
                        <th data-field="real_name">姓名</th>
                        <th data-field="account_status" data-formatter="statusFormatter">账号状态</th>
                        <th data-field="empno">工号</th>
                        <th data-field="login_name">登录名</th>
                        <th data-field="role_name">岗位</th>
                        <th data-field="group_name">部门</th>
                        <th data-field="type" data-formatter="typeFormatter">权限类型</th>
                        <th data-formatter="actionFormatter">操作</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
<div id="act" style="display: none;">
    <div class="btn-group">
        {#<div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                角色 <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
                <li><a href="javascript:" onclick="role('bind', '@id@')">绑定角色</a></li>
                <li><a href="javascript:" onclick="role('unbind', '@id@')">解除角色</a></li>
            </ul>
        </div>#}
        <div class="btn-group">
            <button type="button" class="btn blue dropdown-toggle" data-toggle="dropdown">
                操作 <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
                <li><a href="javascript:" onclick="edit('@id@')"><i class="fa fa-edit"></i> 编辑</a></li>
                <li><a href="javascript:" onclick="del('@id@')"><i class="fa fa-times"></i> 删除</a></li>
                <li><a data-toggle="modal"  onclick="viewQrCode('@id@','@name@')" href="#view_qr_code">查看二维码</a></li>
            </ul>
        </div>
    </div>
</div>

<div id="view_qr_code" class="modal fade" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="width: 300px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 id="view_qr_title" class="modal-title"></h4>
            </div>
            <div class="modal-body" style="height: 260px;overflow: auto;display: flex;justify-content: center">
                <div id="qrcode" style="width:200px; height:200px; margin-top:15px;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn dark btn-outline">关闭</button>
            </div>
        </div>
    </div>
</div>
<script>
    var qrcode = new QRCode(document.getElementById("qrcode"), {
        width : 200,
        height : 200
    });

    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            real_name: '',
            empno: '',
            login_name: '',
            mobile: '',
            group_name: ''
        },
        methods: {
            search: function () {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function (p) {
                p.real_name = this.real_name;
                p.empno = this.empno;
                p.login_name = this.login_name;
                p.mobile = this.mobile;
                p.group_name = this.group_name;
                return p;
            },
            reset: function () {
                this.real_name = '';
                this.empno = '';
                this.login_name = '';
                this.mobile = '';
                this.group_name = '';
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            selectGroup: function() {
                top.window.group_layer_result = null;
                top.layer.open({
                    title: '选择组织',
                    type: 2,
                    area: ['35em', '60%'],
                    content: '{{ url('sys/group/sel') }}',
                    end: function() {
                        if (top.window.group_layer_result != null) {
                            var rs = top.window.group_layer_result;
                            app.group_name = rs.name;
                        }
                    }
                });
            }
        }
    });

    $table.bootstrapTable();
    var actHtml = $('#act').html();

    function actionFormatter(v, row, idx) {
        return actHtml.replace(/@id@/g, row.uid).replace(/@name@/g, row.real_name);
    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新增用户',
            type: 2,
            area: makeArea('70em', '50em'),
            content: '{{ url('sys/user/create') }}',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(uid) {
        top.window.layer_result='';
        top.layer.open({
            title:'编辑',
            type: 2,
            area: makeArea('70em', '50em'),
            content: '{{ url('sys/user/edit/') }}' + uid,
            end:function(){
                if(top.window.layer_result=='ok'){
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function del(uid){
        var dlg = top.layer.confirm('确认删除吗?', function(){
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('sys/user/delete') }}", {uid:uid}, function (rs) {
                closeSpin(null);
                if(rs.status=='ok'){
                    toastr.success('操作成功！');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }

    function role(act, uid) {
        if(act=='bind') {
            window.layer_result = '';
            layer.open({
                title: '分配角色',
                type: 2,
                area: makeArea('30em', '20em'),
                content: '{{ url('sys/user/role/bind/') }}' + uid,
                end: function () {
                    if (window.layer_result == 'ok') {
                        toastr.success('操作完毕');
                        $table.bootstrapTable('refresh');
                    }
                }
            });
        }
        else if(act=='unbind'){
            var dlg = layer.confirm('确认解绑用户角色吗?', function(){
                showSpin();
                $.post("{{ url('sys/user/role/unbind/') }}"+uid, function (rs) {
                    closeSpin(dlg);
                    if(rs.status=='ok'){
                        toastr.success('操作成功!');
                        $table.bootstrapTable('refresh');
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            });
        }
    }

    function viewQrCode(uid,name){
        qrcode.makeCode(uid);
        document.getElementById("view_qr_title").innerHTML = name;
    }

    function statusFormatter(val) {
        if (val ==0){
            return '<span class="label label-success">启用</span>';
        } else {
            return '<span class="label label-danger">禁用</span>';
        }
    }

    function typeFormatter(v){
        if (v ==0){
            return '<span class="label label-success">组织数据权限</span>';
        } else {
            return '<span class="label label-warning">个人数据权限</span>';
        }
    }
</script>
