<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\MaintainService;

class MaintainController extends SuperController
{
    public function listAction()
    {
        if ($this->request->isPost()) {
            $s = new MaintainService();
            return json_encode($s->selectAll());
        }
    }

    public function addAction()
    {
        if ($this->request->isPost()) {
            $s = new MaintainService();
            return json_encode($s->getAddIndexData());
        }
    }

    public function submitAction()
    {
        if ($this->request->isPost()) {
            $s = new MaintainService();
            $ret = new JsonData();
            $ret->message = $s->submit();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $s = new MaintainService();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }
}