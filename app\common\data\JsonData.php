<?php
namespace Envsan\Common\Data;

use Envsan\Common\Component\ErrorHelper;

class JsonData
{
    const STATUS_OK='ok';
    const STATUS_ERROR='error';
    const EMPTY_DATA = '[]';

    public $status;
    public $message;
    public $data;

    public function __construct(){
        $this->status = JsonData::STATUS_ERROR;
        $this->message = '';
        $this->data = null;
    }

    public function emptyIsOk()
    {
        if($this->message=='')
            $this->status = JsonData::STATUS_OK;
        return $this;
    }
    
    /**
     * 处理服务层返回的标准结果
     * @param array $result 服务层返回的标准格式结果
     * @return $this 支持链式调用
     */
    /**
     * 处理各种类型的结果，转换为标准的JsonData格式
     * @param mixed $result 可以是数组、对象、字符串、布尔值等
     * @return $this
     */
    public function handleResult($result, $stringAsData = false)
    {
        // 1. 处理null值
        if ($result === null) {
            $this->status = 'ok';
            return $this;
        }

        // 2. 处理标准格式的数组或对象（包含success键）
        if ((is_array($result) && isset($result['success'])) ||
            (is_object($result) && isset($result->success))) {

            $success = is_array($result) ? $result['success'] : $result->success;
            $this->status = $success ? 'ok' : 'error';

            if (is_array($result) && isset($result['message'])) {
                $this->message = $result['message'];
            } elseif (is_object($result) && isset($result->message)) {
                $this->message = $result->message;
            }

            if (is_array($result) && isset($result['data'])) {
                $this->data = $result['data'];
            } elseif (is_object($result) && isset($result->data)) {
                $this->data = $result->data;
            }

            return $this;
        }

        // 3. 处理Phalcon结果集
        if ($result instanceof \Phalcon\Mvc\Model\Resultset) {
            $this->status = 'ok';
            $this->data = $result->toArray();
            return $this;
        }

        // 4. 处理Phalcon模型
        if ($result instanceof \Phalcon\Mvc\Model) {
            $this->status = 'ok';
            $this->data = $result->toArray();
            return $this;
        }

        // 5. 处理字符串 - 这里是关键改变
        if (is_string($result)) {
            if ($stringAsData) {
                // 字符串作为数据处理
                $this->status = 'ok';
                $this->data = $result;
            } else {
                // 字符串作为消息处理
                $this->status = empty($result) ? 'ok' : 'error';
                $this->message = $result;
            }
            return $this;
        }

        // 6. 处理布尔值
        if (is_bool($result)) {
            $this->status = $result ? 'ok' : 'error';
            return $this;
        }

        // 7. 处理其他情况（数组、对象等）- 作为数据返回
        $this->status = 'ok';
        $this->data = $result;

        return $this;
    }
}