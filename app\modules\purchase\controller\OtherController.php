<?php
namespace Envsan\Modules\Purchase\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Printing\Service\TemplateService;
use Envsan\Modules\Purchase\Model\PurchaseOther;
use Envsan\Modules\Purchase\Service\CommonService;
use Envsan\Modules\Purchase\Service\GoodsTypeService;
use Envsan\Modules\Purchase\Service\OtherService;

/**
 * @name('其他入库')
 */
class OtherController extends SuperController
{
    private $page_id = 27;
    private $search_page_id = 32;


    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OtherService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
        $ts = new TemplateService();
        $this->view->print = json_encode($ts->getPrintTempList($this->page_id));
    }


    /**
     * @name('查询')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new OtherService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->search_page_id,$page->rows,$builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->search_page_id;
    }

    /**
     * @name('新增')
     */
    public function createAction()
    {
        $rs = new OtherService();
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $common = new CommonService();
        $fs = new FileService();
        $gs = new GoodsTypeService();

        $jrow = (new PurchaseOther())->toArray();
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['other_date'] = DateUtil::today();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $jrow['detail_data'] = [];
        $jrow['files'] = [];
        $jrow['goods_list'] = [];
        $jrow['goods'] = '';
        $jrow['supplier'] = '';
        $jrow['type_uid'] = '';
        $jrow['supplier_list'] = $common->getSupplierList();
        $this->view->jsonOther = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonTree = $gs->selectTree();
    }

    /**
     * @acl({'link':'purchase:other:create'})
     */
    function editAction($uid)
    {
        $rs = new OtherService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        if($row->status > 10)
            die('已提交入库');
        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $rs->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();
        $common = new CommonService();
        $fs = new FileService();
        $gs = new GoodsTypeService();
        $ext_data =  CvtUtil::emptyToArray($row->ext_data);

        $jrow = $row->toArray();
        $jrow['base_path'] = $fs->getImagePath();
        $jrow['ext_data'] = $table->margeFormData($this->page_id,$ext_data);
        $jrow['detail_data'] = CvtUtil::emptyToArray($jrow['detail_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['goods_list'] = [];
        $jrow['goods'] = '';
        $jrow['supplier'] = '';
        $jrow['type_uid'] = '';
        $jrow['supplier_list'] = $common->getSupplierList();
        $this->view->jsonOther = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonTree = $gs->selectTree();
        $this->view->pick('other/create');
    }

    /**
     * @acl({'link':'purchase:other:create'})
     */
    public function deleteAction(){
        $s = new OtherService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->delete();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'purchase:other:create'})
     */
    public function cancelAction(){
        $s = new OtherService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->cancel();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $rs = new OtherService();
        $row = $rs->selectByUid($uid);
        if($row == null)
            die(ErrorHelper::WRONG_ID);
        $jrow = $row->toArray();
        $oss_util = new FileService();
        $jrow['base_path'] = $oss_util->getImagePath();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $jrow['files'] = CvtUtil::emptyToArray($jrow['files']);
        $jrow['detail_data'] = $rs->getDetailList($jrow['id']);
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->jsonOther = json_encode($jrow);
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new OtherService();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id,$builder);
    }

    /**
     * @skipacl
     */
    public function export2Action()
    {
        $s = new OtherService();
        $builder = $s->searchAll();
        $table = new TableService();
        $table->exportExcel($this->search_page_id,$builder);
    }
}