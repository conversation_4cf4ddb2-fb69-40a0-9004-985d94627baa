<?php
namespace Envsan\Modules\Equ\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Equ\Model\EquFault;
use Envsan\Modules\Equ\Service\CommonService;
use Envsan\Modules\Equ\Service\EquFaultService;

/**
 * @name('设备故障')
 */
class FaultController extends SuperController
{

    /**
     * @name('检索')
     */
    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new EquFaultService();
            $builder = $s->searchAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setSearchDetail($page->rows->toArray());
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'equ:fault:search'})
     */
    public function detailAction($uid)
    {
        $s = new EquFaultService();
        $row = $s->getDetailData($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $this->view->data = json_encode($row);

        $cs = new CommonService();
        $this->view->base_path = $cs->getImagePath();
    }


    /**
     * @name('外协费用')
     */
    public function repairAction($stat_type = 1, $type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new EquFaultService();
            $builder = $s->getMoneySum($stat_type);
            $page = $this->getPagination($builder);
            return json_encode($page);
        }
        $this->view->stat_type = $stat_type;
    }

    public function testAction()
    {
        $s = new EquFaultService();
        $s->updateImage();
        return json_encode('ok');
    }
}