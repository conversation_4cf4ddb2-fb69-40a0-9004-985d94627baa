<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Group;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Work\Model\WorkData;
use Envsan\Modules\Work\Model\WorkDataRead;
use Envsan\Modules\Work\Model\WorkDataReview;
use Envsan\Modules\Work\Model\WorkDesign;
use Envsan\Modules\Work\Model\WorkForm;
use Envsan\Modules\Work\Model\WorkRole;
use Envsan\Modules\Work\Util\Constant;
use Phalcon\Mvc\User\Component;

class WorkService extends BaseService
{
    public function getTypeList(){
        $user = SessionData::user();
        if (empty($user->work_role_id)){
            return [];
        }
        $role_row = WorkRole::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$user->work_role_id]]);
        if(empty($role_row) || empty($role_row->type_ids)){
            return [];
        }
        $type_ids = json_decode($role_row->type_ids,true);
        $builder = $this->modelsManager->createBuilder()
            ->columns('id, name as text')
            ->addFrom('Envsan\Modules\Work\Model\WorkDesign')
            ->where('del_flag = 0 and status = 1')
            ->inWhere('id',$type_ids)
            ->orderBy('id');
        return $builder->getQuery()->execute();
    }

    public function getGoodsData(){
        $rtn = new \stdClass();
        $rtn->message = '';
        $cc_id = $this->request->getPost('cc_id', ['string', 'trim']);
        $ck_id = $this->request->getPost('ck_id', ['string', 'trim']);
        $cs = new CommonService();
        $cc_list = $cs->selectCCList();
        $ck_list = [];
        if (!empty($cc_id)){
            $ck_list = $cs->selectCKList($cc_id);
        }
        $goods_list = [];
        if (!empty($ck_id)){
            $goods_list = $cs->getGoodsListChildren($ck_id,1);
        }
        $rtn->message = '';
        $rtn->data = [
            'cc_list' => $cc_list,
            'ck_list' => $ck_list,
            'goods_list' => $goods_list
        ];
        return $rtn;
    }

    public function getGroupList($group_id=''){
        $builder = $this->modelsManager->createBuilder()
            ->columns('id,name as text')
            ->addFrom('Envsan\Modules\Sys\Model\Group')
            ->where('del_flag = 0 ')
            ->orderBy('id');
        if (!empty($group_id)){
            $builder->andWhere("id = ?1", [1 => $group_id]);
        }
        return $builder->getQuery()->execute();
    }

    public function getFlow(){
        $rtn = new \stdClass();
        $rtn->message = '';
        $id = $this->request->getPost('id', ['string', 'trim']);
        $main_uid = $this->request->getPost('main_uid', ['string', 'trim']);
        if (empty($id)){
            $rtn->message =  ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $user = SessionData::user();
        $design = WorkDesign::findFirst(['del_flag = 0 and status = 1 and id = ?1','bind'=>[1 => $id]]);
        if (empty($design)){
            $rtn->message =  '未配置业务流程';
            return $rtn;
        }
        $work_row = null;
        if (!empty($main_uid)){
            $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$main_uid]]);
            if (!empty($work_row)){
                if ($work_row->pid != $work_row->id){
                    $work_row = WorkData::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$work_row->pid]]);
                }
            }
        }
        $flow_data = json_decode($design->flow_data,true);
        $flow_list = json_decode($design->flow_list,true);
        foreach ($flow_list as &$item){
            $item['list'] = [];
            $item['nlist'] = [];
            $item['type_name'] = Constant::$review_type[$item['type']];
            foreach ($flow_data as $u){
                if ($item['id'] == $u['anchor']){
                    if ($u['notify'] == 0){
                        array_push( $item['list'],['id' => $u['id'], 'name' => $u['name']]);
                    } else {
                        array_push( $item['nlist'],['id' => $u['id'], 'name' => $u['name']]);
                    }
                }
            }
        }
        $data = new \stdClass();
        $data->id = $design->id;
        $data->group_id = $design->group_id;
        $data->list = $flow_list;
        $data->form_data = [];
        if (!empty($design->form_id)){
            $form_row = WorkForm::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$design->form_id]]);
            if (!empty($form_row)){
                if (!empty($form_row->form_data)){
                    $data->form_data = json_decode($form_row->form_data,true);
                }
            }
        }
        $rtn->data = $data;
        return $rtn;
    }

    public function create(){
        $uid = $this->request->getPost('uid', 'tstring');
        $p_uid = $this->request->getPost('p_uid', 'tstring');
        $main_uid = $this->request->getPost('main_uid', 'tstring');
        $type_id = $this->request->getPost('type_id', 'tstring');
        $data_list = urldecode($this->request->getPost('data_list', 'tstring'));
        $remarks = $this->request->getPost('remarks', 'tstring');
        $files = urldecode($this->request->getPost('files', ['string', 'trim']));
        if (empty($type_id)){
            return ErrorHelper::WRONG_INPUT;
        }
        $design = WorkDesign::findFirst(['del_flag = 0 and id =?1','bind'=>[1=>$type_id]]);
        if (empty($design)){
            return ErrorHelper::WRONG_INPUT;
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row = null;
        $main_id = '';
        $create_flag = true;
        if (!empty($uid)){
            $row = WorkData::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
            if (empty($row)){
                return ErrorHelper::WRONG_INPUT;
            }
            if (!empty($row->valid_date)){
                if ($row->valid_date <= $now){
                    return '该流程已到期请删除后重新发起！';
                }
            }
            $create_flag = false;
            $main_id  = $row->main_id;
        }
        $work_row = null;
        if (!empty($main_uid)){
            if (empty($p_uid)){
                return ErrorHelper::WRONG_INPUT;
            }
            $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$main_uid]]);
            if (empty($work_row)){
                return ErrorHelper::WRONG_INPUT;
            }
            if (!empty($work_row->valid_date)){
                if ($p_uid == $main_uid){
                    if ($work_row->valid_date <= $now){
                        return '该流程已到期不能发起！';
                    }
                }
            }
            $main_id = $work_row->main_id;
        }
        $flow_data = json_decode($design->flow_data,true);
        $flow_list = json_decode($design->flow_list,true);
        if (count($flow_list) == 0){
            return ErrorHelper::WRONG_INPUT;
        }
        $data_list = CvtUtil::emptyToArray($data_list);
        $return_flag = 0;
        $user_ids = '|' . $user->id . '|';
        foreach ($flow_data as $u){
            if ($u['id'] != ''){
                if (strpos($user_ids, '|' . $u['id'] . '|') ===  false){
                    $user_ids .= $u['id'] . '|';
                }
            }
        }
        foreach ($flow_list as &$item){
            if (!array_key_exists('stock', $item)) {
                $item['stock'] = 0;
            }
            $item['list'] = [];
            $item['nlist'] = [];
            foreach ($flow_data as $u){
                if ($item['id'] == $u['anchor']){
                    if ($u['notify'] == 0){
                        array_push( $item['list'],[
                            'id' => $u['id'],
                            'name' => $u['name'],
                            'status' => 0
                        ]);
                    } else {
                        array_push( $item['nlist'],[
                            'id' => $u['id'],
                            'name' => $u['name']
                        ]);
                    }
                }
            }
        }
        $first_anchor = $flow_list[0];
        $status = 15;
        $push_users = array();
        $anchor_users = '|';
        foreach ($first_anchor['list'] as $anchor){
            if ($anchor['id'] != ''){
                $anchor_users .= $anchor['id'] . '|';
                array_push($push_users,$anchor['id']);
            }
        }
        $this->db->begin();
        try {
            $row_flow_list = [];
            $flow_item = json_decode(json_encode(Constant::$data_template),true);
            $flow_item['id'] = $user->id;
            $flow_item['name'] = $user->real_name;
            $flow_item['val'] = '(发起申请)';
            $flow_item['time'] = $now;
            if (!empty($row)){
                $row_flow_list = CvtUtil::emptyToArray($row->flow_list);
            } else {
                $row = new WorkData();
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->code =  $ss->useSequence(0, $user->owner);
                $row->type_id = $design->id;
                $row->type_name = $design->name;
            }
            array_push($row_flow_list,$flow_item);
            $row->handle_status = 0;
            if (empty($design->flow_more_ids)){
                $row->more_flag = 0;
            } else {
                $row->more_flag = 1;
            }
            $row->form_id = $design->form_id;
            $row->valid_date = null;
            if (count($data_list) > 0){
                $row->form_data = json_encode($data_list,JSON_UNESCAPED_UNICODE);
            } else {
                $row->form_data = null;
            }
            $row->files = $files;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->anchor_data = json_encode($first_anchor,JSON_UNESCAPED_UNICODE);
            $row->anchor_users = $anchor_users;
            $row->flow_list = json_encode($row_flow_list,JSON_UNESCAPED_UNICODE);
            $row->flow_data = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
            $row->flow_users = $user_ids;
            $row->status = $status;
            $row->return_flag = $return_flag;
            $row->read_flag = 0;
            $row->create_date = $now;
            $row->create_by = $user->id;
            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->del_flag = 0;
            $row->create_group_id = $user->group_id;
            $row->group_id = $design->group_id;
            $row->owner = $design->owner;
            if (!$row->save()){
                throw new \Exception( 'WorkData表更新失败');
            }
            if ($create_flag){
                if (empty($work_row)){
                    $row->main_id = $row->id;
                    $row->pid = $row->id;
                    if (!$row->save()){
                        throw new \Exception( 'WorkData表更新失败');
                    }
                } else {
                    if ($main_uid != $p_uid){
                        $work_row = WorkData::findFirst(['del_flag = 0 and status = 20 and uid = ?1','bind'=>[1=>$p_uid]]);
                    }
                    if (!empty($work_row)){
                        if (empty($work_row->more_flag)){
                            $work_row->read_flag = 1;
                            $work_row->update_date = $now;
                            $work_row->update_by = $user->id;
                            if (!$work_row->save()){
                                throw new \Exception( 'WorkDataMain表更新失败');
                            }
                        }
                        if ($work_row->more_flag == 1){
                            $row->pid = $work_row->id;
                            $row->main_id = $row->id;
                            $row->valid_date = $work_row->valid_date;
                        } else {
                            $row->pid = $work_row->pid;
                            $row->main_id = $work_row->main_id;
                        }
                    }
                    if (!$row->save()){
                        throw new \Exception( 'WorkData表更新失败');
                    }
                }
            }
            if (count($push_users) > 0){
                $type_name = $row->type_name;
                if (!empty($row->plate)) {
                    $type_name .= '('.$row->plate.')';
                }
                $dcs = new DataCommonService();
                //$dcs->pushMessage($push_users,$row->uid,$type_name,2,$user->real_name,CvtUtil::nullToBlank($remarks));
                $dcs->pushAppMessage($push_users,$row->uid,2,$type_name,$row->code,$row->create_by,CvtUtil::nullToBlank($remarks));
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function selectAll(){
        $params = [
            'active' => $this->request->getPost('active', ['string', 'trim']),
            'type_name' => $this->request->getPost('type_name', ['string', 'trim']),
            'code' => $this->request->getPost('code', ['string', 'trim']),
            'status' => $this->request->getPost('status', ['string', 'trim']),
            'date_start' => $this->request->getPost('date_start', ['string', 'trim']),
            'date_end' => $this->request->getPost('date_end', ['string', 'trim']),
        ];
        return $this->selectAllCommon($params);
    }

    public function getAll()
    {
        $params = [
            'active' => $this->request->get('active', ['string', 'trim']),
            'type_name' => $this->request->get('type_name', ['string', 'trim']),
            'code' => $this->request->get('code', ['string', 'trim']),
            'status' => $this->request->get('status', ['string', 'trim']),
            'date_start' => $this->request->get('date_start', ['string', 'trim']),
            'date_end' => $this->request->get('date_end', ['string', 'trim']),
        ];
        return $this->selectAllCommon($params);
    }

    private function selectAllCommon($params)
    {
        $active = $params['active'];
        $type_name = $params['type_name'];
        $code = $params['code'];
        $handle_status = $params['status'];
        $date_start = $params['date_start'];
        $date_end = $params['date_end'];

        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.uid,
                t1.code,
                t1.type_name,
                t1.review_type,
                t1.anchor_data,
                t1.remarks,
                t1.create_date,
                t1.status,
                t1.handle_status,
                t1.read_flag,
                t2.name as group_name,
                t3.real_name as create_name
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.create_group_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.create_by = ?1 and t1.owner = ?2',
                [1=>$user->id,2=>SessionData::ownerId()])
            ->orderBy('t1.create_date desc');
        if (empty($active)){
            $builder->andWhere('t1.read_flag = 0');
        } else if ($active == '99') {
            $builder->andWhere('t1.id = t1.main_id and t1.id = t1.pid');
            if (!empty($date_start)) {
                $builder->andWhere("t1.create_date >= ?4", [4 => $date_start.' 00:00:00']);
            }
            if (!empty($date_end)) {
                $builder->andWhere("t1.create_date <= ?5", [5 => $date_end.' 23:59:59']);
            }
        } else {
            $builder->andWhere('t1.id = t1.main_id and t1.id = t1.pid and t1.read_flag = 1');
            if (!empty($date_start)) {
                $builder->andWhere("t1.create_date >= ?4", [4 => $date_start.' 00:00:00']);
            }
            if (!empty($date_end)) {
                $builder->andWhere("t1.create_date <= ?5", [5 => $date_end.' 23:59:59']);
            }
        }
        if (!CheckUtil::is_empty($type_name)) {
            $builder->andWhere("t1.type_name like ?3", [3 => "%$type_name%"]);
        }
        if (!CheckUtil::is_empty($code)) {
            $builder->andWhere("t1.code like ?6", [6 => "%$code%"]);
        }
        if (!empty($handle_status)) {
            if ($handle_status == 1){
                $builder->andWhere("t1.handle_status > 0");
            } else {
                $builder->andWhere("t1.handle_status = 0");
            }
        }
        return $builder;
    }

    public function setDetail($rows)
    {
        foreach ($rows as &$row)
        {
            $anchor_data = CvtUtil::emptyToArray($row['anchor_data']);
            $row['anchor_name'] = '';
            if ($row['status'] < 70) {
                if (array_key_exists('name', $anchor_data)) {
                    $row['anchor_name'] = $anchor_data['name'];
                }
            } else {
                $row['anchor_name'] = '已完成';
            }
            $row['anchor_data'] = null;

            $row['know_time'] = date('m/d H:i', strtotime($row['create_date']));
        }
        return $rows;
    }

    public function getReviewAll(){
        $params = [
            'active' => $this->request->get('active', ['string', 'trim']),
            'type_name' => $this->request->get('type_name', ['string', 'trim']),
            'code' => $this->request->get('code', ['string', 'trim']),
            'date_start' => $this->request->get('date_start', ['string', 'trim']),
            'date_end' => $this->request->get('date_end', ['string', 'trim']),
        ];
        return $this->selectReviewAllCommon($params);
    }

    public function selectReviewAll(){
        $params = [
            'active' => $this->request->getPost('active', ['string', 'trim']),
            'type_name' => $this->request->getPost('type_name', ['string', 'trim']),
            'code' => $this->request->getPost('code', ['string', 'trim']),
            'date_start' => $this->request->getPost('date_start', ['string', 'trim']),
            'date_end' => $this->request->getPost('date_end', ['string', 'trim']),
        ];
        return $this->selectReviewAllCommon($params);
    }

    private function selectReviewAllCommon($params)
    {
        $active = $params['active'];
        $type_name = $params['type_name'];
        $code = $params['code'];
        $date_start = $params['date_start'];
        $date_end = $params['date_end'];

        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.uid,
                t1.code,
                t1.type_name,
                t1.review_type,
                t1.remarks,
                t1.create_date,
                t2.name as group_name,
                t3.real_name as create_name,
                t1.status,
                t1.anchor_users,
                t1.flow_users
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.create_group_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and t1.status > 10', [1=>SessionData::ownerId()])
            ->orderBy('t1.update_date desc');
        if (empty($active)) {
            $builder->andWhere(' t1.anchor_users like ?2 ', [2 => "%|$user->id|%"]);
        } else if ($active == '99') {
            $builder->andWhere('t1.anchor_users like ?2 or t1.flow_users like ?2', [2 => "%|$user->id|%"]);
        } else {
            if($active == 1){
                $builder->andWhere('t1.status <20 and t1.flow_users like ?2 and t1.anchor_users not like ?7',[2=>"%|$user->id|%",7=>"%|$user->id|%"]);
            }else{
                $builder->andWhere('t1.status =20 and t1.flow_users like ?2',[2=>"%|$user->id|%"]);
            }
        }
        if (!CheckUtil::is_empty($type_name)) {
            $builder->andWhere("t1.type_name like ?3", [3 => "%$type_name%"]);
        }
        if (!empty($date_start)) {
            $builder->andWhere("t1.create_date >= ?4", [4 => $date_start.' 00:00:00']);
        }
        if (!empty($date_end)) {
            $builder->andWhere("t1.create_date <= ?5", [5 => $date_end.' 23:59:59']);
        }
        if (!CheckUtil::is_empty($code)) {
            $builder->andWhere("t1.code like ?6", [6 => "%$code%"]);
        }
        return $builder;
    }

    public function setReviewDetail($rows)
    {
        $user = SessionData::user();
        foreach ($rows as &$row)
        {
            if (strpos($row['anchor_users'], '|'.$user->id.'|') !== false) {
                $row['read_flag'] = 0;
            } else {
                $row['read_flag'] = 1;
            }
        }
        return $rows;
    }

    public function selectReadAll(){
        $params = [
            'active' => $this->request->getPost('active', ['string', 'trim']),
            'type_name' => $this->request->getPost('type_name', ['string', 'trim']),
            'code' => $this->request->getPost('code', ['string', 'trim']),
            'status' => $this->request->getPost('status', ['string', 'trim']),
            'date_start' => $this->request->getPost('date_start', ['string', 'trim']),
            'date_end' => $this->request->getPost('date_end', ['string', 'trim']),
            //***************add BUG-LQ-947 20240202 zhuhao start*****************
            'create_name' => '',
            'group_id' => ''
            //***************add BUG-LQ-947 20240202 zhuhao end*****************
        ];
        return $this->selectRealAllCommon($params);
    }

    public function getReadAll(){
        $params = [
            'active' => $this->request->get('active', ['string', 'trim']),
            'type_name' => $this->request->get('type_name', ['string', 'trim']),
            'create_name' => $this->request->get('create_name', ['string', 'trim']),
            'group_id' => $this->request->get('group_id', ['string', 'trim']),
            'code' => $this->request->get('code', ['string', 'trim']),
            'status' => $this->request->get('status', ['string', 'trim']),
            'date_start' => $this->request->get('date_start', ['string', 'trim']),
            'date_end' => $this->request->get('date_end', ['string', 'trim']),
        ];
        return $this->selectRealAllCommon($params);
    }

    private function selectRealAllCommon($params)
    {
        $active = $params['active'];
        $type_name = $params['type_name'];
        $code = $params['code'];
        $handle_status = $params['status'];
        $date_start = $params['date_start'];
        $date_end = $params['date_end'];
        $create_name = $params['create_name'];
        $group_id = $params['group_id'];

        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.uid,
                t1.code,
                t1.type_name,
                t1.review_type,
                t1.remarks,
                t1.anchor_data,
                t1.create_date,
                t2.name as group_name,
                t3.real_name as create_name,
                t1.status,
                t1.handle_status,
                r.read_flag
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->leftJoin('Envsan\Modules\Work\Model\WorkDataRead', 't1.id = r.data_id', 'r')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.create_group_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = ?1 and r.user_id = ?2 and t1.status > 10', [1=>SessionData::ownerId(),2=>$user->id])
            ->orderBy('t1.update_date desc');
        if (empty($active)) {
            $builder->andWhere(' r.read_flag = 0');
        } else if ($active == '1') {
            $builder->andWhere(' r.read_flag = 1');
        }
        if (!CheckUtil::is_empty($type_name)) {
            $builder->andWhere("t1.type_name like ?3", [3 => "%$type_name%"]);
        }
        if (!empty($date_start)) {
            $builder->andWhere("t1.create_date >= ?4", [4 => $date_start.' 00:00:00']);
        }
        if (!empty($date_end)) {
            $builder->andWhere("t1.create_date <= ?5", [5 => $date_end.' 23:59:59']);
        }
        if (!CheckUtil::is_empty($code)) {
            $builder->andWhere("t1.code like ?6", [6 => "%$code%"]);
        }
        if (!CheckUtil::is_empty($handle_status)) {
            $builder->andWhere("t1.handle_status = ?9", [9 => $handle_status]);
        }
        if (!CheckUtil::is_empty($create_name)) {
            $builder->andWhere("t3.real_name like ?10", [10 => "%$create_name%"]);
        }
        if (!CheckUtil::is_empty($group_id)) {
            $builder->andWhere("t2.id = ?11", [11 => $group_id]);
        }
        return $builder;
    }

    public function selectMore($work_row){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.uid,
                t1.code,
                t1.type_name,
                t1.remarks,
                t1.anchor_data,
                t1.create_date,
                t2.name as group_name,
                t3.real_name as create_name,
                t1.status,
                t1.handle_status
            ')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->leftJoin('Envsan\Modules\Sys\Model\Group', 't1.create_group_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't1.create_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.status > 10 and t1.id = t1.main_id and t1.id <> ?1 and t1.pid = ?1 ', [1=>$work_row->id])
            ->orderBy('t1.create_date desc');
        return $builder;
    }

    public function getCount($builder){
        $builder->columns('count(t1.id) as cnt');
        $rows = $builder->getQuery()->execute();
        return $rows[0]->cnt;
    }

    public function getWorkData(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $type = $this->request->getPost('src', ['string', 'trim']);
        $main_id = $this->request->getPost('main_id', ['string', 'trim']);
        $pid = $this->request->getPost('pid', ['string', 'trim']);

        $rtn = new \stdClass();
        if (empty($uid) || empty($type)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $owner = SessionData::owner();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $rtn = $this->getFlowData($work_row,$type);
        $common = new CommonService();
        if (empty($main_id)){
            $rtn->data->work_list = $common->getWorkList($work_row->main_id,$work_row->pid);
        } else {
            $rtn->data->work_list = $common->getWorkList($main_id,$pid);
        }
        return $rtn;
    }

    public function getEditData(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $rtn = new \stdClass();
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
        if (empty($work_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        if ($work_row->status != 10){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $user = SessionData::user();
        if ($work_row->create_by != $user->id){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $data = new \stdClass();
        $data->uid = $work_row->uid;
        $data->type_id = $work_row->type_id;
        $data->type_name = $work_row->type_name;
        $data->files = CvtUtil::emptyToArray($work_row->files);
        $data->file_list = CvtUtil::emptyToArray($work_row->file_list);
        $data->flow_list =  CvtUtil::emptyToArray($work_row->flow_list);
        $data->flow_data = json_decode($work_row->flow_data,true);
        foreach ($data->flow_list as &$p){
            $p['icon'] = $this->getIconName($p['name']);
            $p['time'] = date('m-d H:i', strtotime($p['time']));
            $send = '';
            if (isset($p['send'])){
                foreach ($p['send'] as $item){
                    $send .= '@'.$item['name'].' ';
                }
            }
            $p['send'] = $send;
        }
        $data->group_id = $work_row->group_id;
        $gs = new GroupService();
        $group_row = $gs->selectById($work_row->group_id);
        $data->group_name = '';
        if (!empty($group_row)){
            $data->group_name = $group_row->name;
        }
        $data->form_data = [];
        if (!empty($work_row->form_id)){
            $form_row = WorkForm::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$work_row->form_id]]);
            if (!empty($form_row)){
                if (!empty($form_row->form_data)){
                    $data->form_data = json_decode($form_row->form_data,true);
                    $value_data = CvtUtil::emptyToArray($work_row->form_data);
                    $cs = new CommonService();
                    $cs->setFromData($data->form_data);
                    $cs->margeFormData($data->form_data,$value_data);
                }
            }
        }
        $rtn->data = $data;
        $rtn->message = '';
        return $rtn;
    }

    public function getFlowData($work_row,$type){
        $rtn = new \stdClass();
        $user = SessionData::user();
        $flow_data = json_decode($work_row->flow_data,true);
        $pass_list = json_decode($work_row->flow_list,true);
        $data = $this->getWorkRow($work_row);
        $user_data = [];
        foreach ($flow_data as $f){
            foreach ($f['list']  as $l){
                $user_data[$l['id']] = $l['name'];
            }
        }
        $user_list = [];
        foreach ($user_data as $key => $value)
        {
            array_push($user_list,[
                'id' => $key,
                'name' => $value,
                'check' => false
            ]);
        }
        $data->auth = 0; //0:浏览 1:评论 2:审批 3:已知晓 4：取消
        $data->status = $work_row->status;
        $data->handle_status = $work_row->handle_status;
        $data->review_type = $work_row->review_type;
        if ($work_row->id == $work_row->main_id){
            $data->main_uid = $work_row->uid;
        } else {
            $main_work_row = WorkData::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$work_row->main_id]]);
            $data->main_uid = $main_work_row->uid;
        }
        $anchor_list = [];
        $hidden_list = [];
        $data->pass_type = '';
        $work_hidden_data = CvtUtil::emptyToArray($work_row->hidden_data);
        if ($work_row->status == 10){
            $data->auth = 0;
        } else if ($work_row->status < 20){
            $data->auth = 1;
            $anchor_data = json_decode($work_row->anchor_data,true);
            $data->anchor = $anchor_data['name'];
            $data->pass_type = $anchor_data['type'];
            foreach ($anchor_data['list'] as $u){
                if ($u['status'] == 0){
                    if ($u['id'] == $user->id){
                        $data->auth = 2;
                        if (array_key_exists($anchor_data['id'],$work_hidden_data))   {
                            $hidden_list = $work_hidden_data[$anchor_data['id']];
                        }
                        break;
                    }
                }
            }
            $push_flag = false;
            foreach ($flow_data as $f){
                if ($f['id'] == $anchor_data['id']){
                    $push_flag = true;
                }
                if ($push_flag){
                    array_push($anchor_list,$f);
                }
            }
        } else {
            $data->anchor = '已完成';
        }
        if ($data->auth != 2){
            $read_row = WorkDataRead::findFirst(['del_flag = 0 and data_id = ?1 and user_id = ?2','bind'=>[1=>$work_row->id,2=>$user->id]]);
            if (!empty($read_row)){
                if ($read_row->read_flag == 0){
                    $data->auth = 3;
                }
                $hidden_list = CvtUtil::emptyToArray($read_row->hidden_keys);
            } else {
                $review_row = WorkDataReview::findFirst(['del_flag = 0 and data_id = ?1 and user_id = ?2','bind'=>[1=>$work_row->id,2=>$user->id],'order'=>'id desc']);
                if (!empty($review_row)){
                    $hidden_list = CvtUtil::emptyToArray($review_row->hidden_keys);
                }
            }
        }
        if ($work_row->create_by == $user->id){
           if ($data->auth != 2 && $work_row->status < 20){
               $data->auth = 4;
           }
        }
        foreach ($pass_list as &$p){
            $p['icon'] = $this->getIconName($p['name']);
            $p['time'] = date('m-d H:i', strtotime($p['time']));
            $send = '';
            if (isset($p['send'])){
                foreach ($p['send'] as $item){
                    $send .= '@'.$item['name'].' ';
                }
            }
            $p['send'] = $send;
        }
        $data->form_list = [];
        if (!empty($work_row->form_data)){
            $form = json_decode($work_row->form_data,true);
            foreach ($form as &$from_item){
                $from_item['show'] = 1;
                if (is_array($hidden_list) && in_array($from_item['id'].'_1',$hidden_list)){
                    $from_item['show'] = 0;
                }
                if (CvtUtil::emptyToInt($from_item['type']) == 99){
                    $show_cnt = 0;
                    foreach ($from_item['data_list'] as &$data_row){
                        $show_cnt = 0;
                        foreach ($data_row as &$data_item){
                            $data_item['show'] = 1;
                            if (is_array($hidden_list) && in_array($data_item['id'].'_2',$hidden_list)){
                                $data_item['show'] = 0;
                            } else {
                                $show_cnt ++;
                            }
                        }
                    }
                    $from_item['show_cnt'] = $show_cnt;
                }
            }
            $data->form_list = $form;
        }
        $rtn_data =  new \stdClass();
        $rtn_data->next_list = [];
        if ($data->auth == 6){
            $design = WorkDesign::findFirst(['del_flag = 0 and status = 1 and id = ?1','bind'=>[1 => $work_row->type_id]]);
            if (!empty($design)){
                if ($work_row->more_flag == 1){
                    $data_cnt = WorkData::count(['del_flag = 0 and pid = ?1','bind'=>[1=>$work_row->id]]);
                    if ($data_cnt > 1){
                        if (!empty($design->flow_ids)){
                            $ids = json_decode($design->flow_more_ids,true);
                            $rtn_data->next_list = $this->getNextList($ids);
                        }
                    } else {
                        $data->more_flag = 0;
                    }
                }
                if (count($rtn_data->next_list) == 0){
                    if ($work_row->handle_status == 1){
                        if (!empty($design->flow_ids)){
                            $ids = json_decode($design->flow_ids,true);
                            $rtn_data->next_list = $this->getNextList($ids);
                        }
                    } else {
                        if (!empty($design->flow_error_ids)){
                            $ids = json_decode($design->flow_error_ids,true);
                            $rtn_data->next_list = $this->getNextList($ids);
                        }
                    }
                }
            }
        }
        $rtn_data->data = $data;
        $rtn_data->flow_list = $pass_list;
        $rtn_data->anchor_list = $anchor_list;
        $rtn_data->user_list = $user_list;
        $rtn->message = '';
        $rtn->data = $rtn_data;
        return $rtn;
    }

    public function getNextList($ids){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.name,
                t1.flow_type
            ')
             ->addFrom('Envsan\Modules\Work\Model\WorkDesign','t1')
             ->where('t1.del_flag=0 and t1.status = 1 and t1.owner = ?1',[1=>SessionData::ownerId()])
             ->inWhere('t1.id', $ids)
             ->orderBy('t1.id desc');
        return $builder->getQuery()->execute();
    }

    public function getWorkRow($work_row){
        $rtn = new \stdClass();
        $rtn->uid = $work_row->uid;
        $rtn->main_id = $work_row->main_id;
        $rtn->pid = $work_row->pid;
        $rtn->title = $work_row->type_name;
        $rtn->group = '';
        $group_row = Group::findFirst('id='.$work_row->create_group_id);
        if (!empty($group_row)){
            $rtn->group = $group_row->name;
        }
        $user_row = User::findFirst('id='.$work_row->create_by);
        $rtn->create_user = '';
        $rtn->create_date = $work_row->create_date;
        if (!empty($user_row)){
            $rtn->create_user = $user_row->real_name;
        }
        $rtn->code = $work_row->code;
        $rtn->form_data_id = $work_row->form_data_id;
        $rtn->form_data_type = $work_row->form_data_type;
        $rtn->type_name = $work_row->type_name;
        $rtn->more_flag = $work_row->more_flag;
        $rtn->review_type = $work_row->review_type;
        $rtn->files = CvtUtil::emptyToArray($work_row->files);
        $rtn->remarks = $work_row->remarks;
        return $rtn;
    }

    public function getIconName($name){
        $len = mb_strlen($name);
        if ($len <= 2 ){
            return $name;
        } else {
            return mb_substr($name,$len-2,2);
        }
    }

    public function delete(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $owner = SessionData::owner();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($work_row->status != 10){
            return ErrorHelper::WRONG_INPUT;
        }
        $user = SessionData::user();
        if ($work_row->create_by != $user->id){
            return ErrorHelper::WRONG_INPUT;
        }
        $this->db->begin();
        try {
            $work_row->update_date = DateUtil::now();
            $work_row->update_by = $user->id;
            $work_row->del_flag = 1;
            if (!$work_row->save()){
                throw new \Exception( 'WorkData表更新失败');
            }
            if ($work_row->id != $work_row->main_id){
                $work_up_row = WorkData::findFirst(['del_flag = 0 and read_flag = 1 and main_id = ?1','bind'=>[1=>$work_row->main_id],'order'=>'id desc']);
                if (!empty($work_up_row)){
                    $work_up_row->read_flag = 0;
                    $work_up_row->update_date = DateUtil::now();
                    $work_up_row->update_by = $user->id;
                    if (!$work_up_row->save()){
                        throw new \Exception( 'WorkData表更新失败');
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function knowSave(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $owner = SessionData::owner();
        $user = SessionData::user();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if (!($work_row->status == 20 || $work_row->status == 10)){
            return '该业务流程未结束';
        }
        if ($work_row->create_by != $user->id){
            return '没有取消业务权限';
        }
        $now = DateUtil::now();
        $work_row->read_flag = 1;
        $work_row->update_date = $now;
        $work_row->update_by = $user->id;
        if (!$work_row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function commentSave(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $value = $this->request->getPost('value', ['string', 'trim']);
        $list = urldecode($this->request->getPost('list', ['string', 'trim']));
        $files = urldecode($this->request->getPost('files', ['string', 'trim']));
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $owner = SessionData::owner();
        $user = SessionData::user();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($work_row->status >= 20){
            return '该业务流程已结束';
        }
        $anchor_data = json_decode($work_row->anchor_data,true);
        $hidden_data = CvtUtil::emptyToArray($work_row->hidden_data);
        $now = DateUtil::now();
        $list = json_decode($list,true);
        $files = json_decode($files,true);
        $row_flow_list = json_decode($work_row->flow_list,true);
        $flow_item = json_decode(json_encode(Constant::$data_template),true);
        $flow_item['id'] = $user->id;
        $flow_item['type'] = 2;
        $flow_item['name'] = $user->real_name;
        $flow_item['val'] = '(添加了评论)';
        $flow_item['text'] = CvtUtil::nullToBlank($value);
        $flow_item['time'] = $now;
        $flow_item['files'] = $files;
        $flow_item['send'] = $list;
        array_push($row_flow_list,$flow_item);
        $work_row->flow_list = json_encode($row_flow_list,JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
        $work_row->update_date = DateUtil::now();
        $work_row->update_by = $user->id;
        if (!$work_row->save()){
            return ErrorHelper::UNKOWN;
        }
        if (count($list) > 0){
            $push_users = [];
            foreach ($list as $item){
                array_push($push_users,$item['id']);
                $read_row = new WorkDataRead();
                $read_row->data_id = $work_row->id;
                $read_row->anchor_id = $anchor_data['id'];
                $read_row->user_id = $item['id'];
                $read_row->read_flag = 0;
                if (array_key_exists($anchor_data['id'],$hidden_data)) {
                    $read_row->hidden_keys = json_encode($hidden_data[$anchor_data['id']],JSON_UNESCAPED_UNICODE);
                }
                $read_row->update_date = $now;
                $read_row->update_by = $user->id;
                $read_row->del_flag = 0;
                $read_row->owner = $user->owner;
                if (!$read_row->save()){
                    throw new \Exception( 'WorkDataRead,抄送表更新失败');
                }
            }
            $dcs = new DataCommonService();
            //UPDATE RC-LQ-1032  20240305 BY PYS ----START
            //$dcs->pushMessage($push_users,$work_row->uid,$work_row->type_name,2,$user->real_name,CvtUtil::nullToBlank($value),$now,$work_row->code);
            $dcs->pushAppMessage($push_users,$work_row->uid,2,$work_row->type_name,$work_row->code,$work_row->create_by,CvtUtil::nullToBlank($value));
//            $dcs->pushMessage($push_users,$work_row->uid,$work_row->type_name,2,$row_flow_list[0]['name'],CvtUtil::nullToBlank($value),$work_row->create_date,$work_row->code);
            //UPDATE RC-LQ-1032  20240305 BY PYS ----END
        }
        return '';
    }

    public function readSave(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $owner = SessionData::owner();
        $user = SessionData::user();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $read_rows = WorkDataRead::find(['del_flag = 0 and data_id = ?1 and user_id = ?2 and read_flag = 0','bind'=>[1=>$work_row->id,2=>$user->id]]);
        if (count($read_rows) == 0){
            return ErrorHelper::WRONG_INPUT;
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            foreach ($read_rows as $read_row){
                $read_row->read_flag = 1;
                $read_row->update_date = $now;
                $read_row->update_by = $user->id;
                if (!$read_row->save()){
                    throw new \Exception( 'WorkDataRead表更新失败');
                }
            }
            $row_flow_list = json_decode($work_row->flow_list,true);
            $flow_item = json_decode(json_encode(Constant::$data_template),true);
            $flow_item['id'] = $user->id;
            $flow_item['type'] = 2;
            $flow_item['name'] = $user->real_name;
            $flow_item['val'] = '(已知晓)';
            $flow_item['text'] = '';
            $flow_item['time'] = $now;
            $flow_item['files'] = [];
            $flow_item['send'] = [];
            array_push($row_flow_list,$flow_item);
            $work_row->flow_list = json_encode($row_flow_list,JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
            $work_row->update_date = DateUtil::now();
            $work_row->update_by = $user->id;
            if (!$work_row->save()){
                throw new \Exception( 'WorkData表更新失败');
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function pass(){

        Logger::error('pass');
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $p_type = $this->request->getPost('p_type', ['string', 'trim']);
        $value = $this->request->getPost('value', ['string', 'trim']);
        $files = urldecode($this->request->getPost('files', ['string', 'trim']));
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }


        $files = json_decode($files,true);
        $owner = SessionData::owner();
        $user = SessionData::user();
        $now = DateUtil::now();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($work_row->status >= 20){
            return '该审批流已结束';
        }
        $flow_data = json_decode($work_row->flow_data,true);
        $flow_list = json_decode($work_row->flow_list,true);
        $anchor_data = json_decode($work_row->anchor_data,true);
        $auth_flag = true;
        foreach ($anchor_data['list'] as &$u){
            if ($u['status'] == 0 && $u['id'] == $user->id){
                $auth_flag = false;
                $u['status'] = 1;
                break;
            }
        }
        if ($auth_flag){
            return '已完成审批或无审批权限';
        }
        $this->db->begin();
        try {
            $next_flag = true;
            $status = $work_row->status;
            if ($anchor_data['type'] == 1){
                foreach ($anchor_data['list'] as $u1){
                    if ($u1['status'] == 0){
                        $next_flag = false;
                        break;
                    }
                }
            }
            $flow_item = json_decode(json_encode(Constant::$data_template),true);
            $flow_item['id'] = $user->id;
            $flow_item['type'] = 1;
            $flow_item['status'] = 1;
            $flow_item['name'] = $user->real_name;
            $flow_item['val'] = '(通过)';
            $flow_item['text'] = CvtUtil::nullToBlank($value);
            $flow_item['time'] = $now;
            $flow_item['files'] = $files;
            array_push($flow_list,$flow_item);
            $review_row = new WorkDataReview();
            $review_row->data_id = $work_row->id;
            $review_row->anchor_id = $anchor_data['id'];
            $review_row->anchor_name =  $anchor_data['name'];
            $review_row->user_id = $user->id;
            $review_row->user_name = $user->real_name;
            $review_row->status = 1;
            $hidden_data = CvtUtil::emptyToArray($work_row->hidden_data);
            if(array_key_exists($anchor_data['id'],$hidden_data)){
                $review_row->hidden_keys = json_encode($hidden_data[$anchor_data['id']],JSON_UNESCAPED_UNICODE);
            }
            $review_row->text =  CvtUtil::nullToBlank($value);
            $review_row->files = json_encode($files,JSON_UNESCAPED_UNICODE);
            $review_row->update_date = DateUtil::now();
            $review_row->update_by = $user->id;
            $review_row->del_flag = 0;
            $review_row->owner =  $user->owner;
            if (!$review_row->save()){
                throw new \Exception( '更新失败');
            }
            $push_read_users = array();
            $push_users = array();
            if ($next_flag){
                foreach ($anchor_data['nlist'] as $n){
                    $read_row = new WorkDataRead();
                    $read_row->anchor_id = $anchor_data['id'];
                    $read_row->data_id = $work_row->id;
                    $read_row->user_id = $n['id'];
                    $read_row->hidden_keys = $review_row->hidden_keys;
                    $read_row->read_flag = 0;
                    $read_row->update_date = $now;
                    $read_row->update_by = $user->id;
                    $read_row->del_flag = 0;
                    $read_row->owner = $user->owner;
                    if (!$read_row->save()){
                        throw new \Exception( 'WorkDataRead,抄送表更新失败');
                    }
                    array_push($push_read_users,$n['id']);
                }
                $pass_type = $anchor_data['type'];
                $target = $anchor_data['target'];
                if (empty($target) || ($pass_type == 3 && $p_type == 1)){
                    //完成
                    $status = 20;
                    $anchor_data = null;
                } else {
                    $anchor_data = null;
                    foreach ($flow_data as $i_data){
                        if ($i_data['id'] == $target){
                            $anchor_data = $i_data;
                            break;
                        }
                    }
                    if (empty($anchor_data)){
                        throw new \Exception( '业务数据错误!');
                    }
                }
            }
            $anchor_users = '|';
            if (!empty($anchor_data)){
                foreach ($anchor_data['list'] as $anchor){
                    if ($anchor['id'] != '' && $anchor['status'] == 0){
                        $anchor_users .= $anchor['id'] . '|';
                        array_push($push_users,$anchor['id']);
                    }
                }
            }
            $work_row->status = $status;
            if ($status >= 20){
                $work_row->handle_status = 1;
                if (!empty($work_row->form_data_type)){
                    $work_row->read_flag = 1;
                }
                $work_row->pressing_flag = 0;
            }
            $work_row->flow_list = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
            if (empty($anchor_data)){
                $work_row->anchor_data = null;
            } else {
                $work_row->anchor_data = json_encode($anchor_data,JSON_UNESCAPED_UNICODE);
            }
            $work_row->anchor_users = $anchor_users;
            $work_row->update_date = DateUtil::now();
            $work_row->update_by = $user->id;
            if (!$work_row->save()){
                throw new \Exception( '更新失败');
            }

            Logger::error('DataCommonService');
            $dcs = new DataCommonService();
            Logger::error($work_row->status, $work_row->handle_status);
            if ($work_row->status >= 20 && $work_row->handle_status = 1){
                $dcs->businessPass($work_row);
                //UPDATE RC-LQ-1032  20240305 BY PYS ----START
//                $dcs->pushMessage([$work_row->create_by],$work_row->uid,$work_row->type_name,1,$user->real_name,'完成流程',$now,$work_row->code);
                //$dcs->pushMessage([$work_row->create_by],$work_row->uid,$work_row->type_name,1,$flow_list[0]['name'],'完成流程',$work_row->create_date,$work_row->code);
               // $dcs->pushAppMessage([$work_row->create_by],$work_row->uid,1,$work_row->type_name,$work_row->code,$work_row->create_by,'完成流程');
                //UPDATE RC-LQ-1032  20240305 BY PYS ----END
                //ADD BUG-LQ-922  20240201 BY WXX ----START
                if (count($push_read_users) > 0){
                   // $dcs->pushMessage($push_read_users,$work_row->uid,$work_row->type_name,3, $flow_list[0]['name'],CvtUtil::nullToBlank($value),$work_row->create_date,$work_row->code);
                    $dcs->pushAppMessage($push_read_users,$work_row->uid,3,$work_row->type_name,$work_row->code,$work_row->create_by,CvtUtil::nullToBlank($value));
                }
                //ADD BUG-LQ-922  20240201 BY WXX ----END
            } else {
                if ($next_flag){
                    if (count($push_read_users) > 0){
                        //$dcs->pushMessage($push_read_users,$work_row->uid,$work_row->type_name,3, $flow_list[0]['name'],CvtUtil::nullToBlank($value),$work_row->create_date,$work_row->code);
                        $dcs->pushAppMessage($push_read_users,$work_row->uid,3, $work_row->type_name,$work_row->code,$work_row->create_by,CvtUtil::nullToBlank($value));
                    }
                    if (count($push_users) > 0){
                        //$dcs->pushMessage($push_users,$work_row->uid,$work_row->type_name,2, $flow_list[0]['name'],CvtUtil::nullToBlank($value),$work_row->create_date,$work_row->code);
                        $dcs->pushAppMessage($push_read_users,$work_row->uid,2, $work_row->type_name,$work_row->code,$work_row->create_by,CvtUtil::nullToBlank($value));
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function reject(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $files = urldecode($this->request->getPost('files', ['string', 'trim']));
        $value = $this->request->getPost('value', ['string', 'trim']);
        if (empty($uid) || empty($value)){
            return ErrorHelper::WRONG_INPUT;
        }
        $files = json_decode($files,true);
        $owner = SessionData::owner();
        $user = SessionData::user();
        $now = DateUtil::now();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($work_row->status >= 20){
            return '该审批流已结束';
        }
        $flow_data = json_decode($work_row->flow_data,true);
        $flow_list = json_decode($work_row->flow_list,true);
        $anchor_data = json_decode($work_row->anchor_data,true);
        $auth_flag = true;
        foreach ($anchor_data['list'] as &$u){
            if ($u['status'] == 0 && $u['id'] == $user->id){
                $auth_flag = false;
            }
        }
        if ($auth_flag){
            return '已完成审批或无审批权限';
        }
        $this->db->begin();
        try {
            if (!empty($work_row->form_id)){
                $work_row->status = 10;
            } else {
                $work_row->status = 20;
            }
            $work_row->pressing_flag = 0;
            $work_row->anchor_users = null;
            $work_row->handle_status = 2;
            $flow_item = json_decode(json_encode(Constant::$data_template),true);
            $flow_item['id'] = $user->id;
            $flow_item['type'] = 5;
            $flow_item['status'] = 2;
            $flow_item['name'] = $user->real_name;
            $flow_item['val'] = '(拒绝)';
            $flow_item['text'] = CvtUtil::nullToBlank($value);
            $flow_item['files'] = $files;
            $flow_item['time'] = $now;
            array_push($flow_list,$flow_item);
            $review_row = new WorkDataReview();
            $review_row->data_id = $work_row->id;
            $review_row->anchor_id = $anchor_data['id'];
            $review_row->anchor_name =  $anchor_data['name'];
            $review_row->user_id = $user->id;
            $review_row->user_name = $user->real_name;
            $review_row->status = 2;
            $hidden_data = CvtUtil::emptyToArray($work_row->hidden_data);
            if(array_key_exists($anchor_data['id'],$hidden_data)){
                $review_row->hidden_keys = json_encode($hidden_data[$anchor_data['id']],JSON_UNESCAPED_UNICODE);
            }
            $review_row->text =  CvtUtil::nullToBlank($value);
            $review_row->files = json_encode($files,JSON_UNESCAPED_UNICODE);
            $review_row->update_date = DateUtil::now();
            $review_row->update_by = $user->id;
            $review_row->del_flag = 0;
            $review_row->owner =  $user->owner;
            if (!$review_row->save()){
                throw new \Exception( '更新失败');
            }
            $work_row->flow_list = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
            $work_row->update_date = DateUtil::now();
            $work_row->update_by = $user->id;
            if (!$work_row->save()){
                throw new \Exception( 'WorkData表更新失败');
            }
            $dcs = new DataCommonService();
            $dcs->businessReject($work_row,$value);
            //UPDATE RC-LQ-1032  20240305 BY PYS ----START
            //$dcs->pushMessage([$work_row->create_by],$work_row->uid,$work_row->type_name,1,$user->real_name,'业务被拒绝',$now,$work_row->code);
            $dcs->pushAppMessage([$work_row->create_by],$work_row->uid,1,$work_row->type_name,$work_row->code,$user->id,'业务被拒绝');
//            $dcs->pushMessage([$work_row->create_by],$work_row->uid,$work_row->type_name,1,$flow_list[0]['name'],'业务被拒绝',$work_row->create_date,$work_row->code);
            //UPDATE RC-LQ-1032  20240305 BY PYS ----END
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function cancel($form_data_type, $form_data_id)
    {
        return $this->executeInTransaction(function () use ($form_data_type, $form_data_id) {
            $remarks = $this->request->getPost('remarks', ['string', 'trim']);
            if (empty($form_data_id) || empty($form_data_type)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $owner = SessionData::owner();
            $user = SessionData::user();

            $work_row = WorkData::findFirst(['del_flag = 0 and status = 15 and form_data_id = ?1 and form_data_type = ?2 and owner = ?3', 'bind' => [1 => $form_data_id, 2 => $form_data_type, 3 => $owner->id]]);
            if (empty($work_row)) {
                return $this->error('该审批流已结束');
            }
            if ($work_row->create_by != $user->id) {
                return $this->error('没有取消业务权限');
            }
            $this->saveCancel($work_row, $remarks);
        });

    }

    private function saveCancel($work_row, $remarks)
    {
        $user = SessionData::user();
        $now = DateUtil::now();
        $flow_list = json_decode($work_row->flow_list, true);
        if (!empty($work_row->form_id)) {
            $work_row->status = 10;
            $work_row->handle_status = 2;
        } else {
            $work_row->status = 20;
            $work_row->handle_status = 3;
        }
        $work_row->pressing_flag = 0;
        $flow_item = json_decode(json_encode(Constant::$data_template), true);
        $flow_item['id'] = $user->id;
        $flow_item['type'] = 5;
        $flow_item['status'] = 2;
        $flow_item['name'] = $user->real_name;
        $flow_item['val'] = '(撤回)';
        $flow_item['time'] = $now;
        $flow_item['text'] = $remarks;
        $flow_item['files'] = [];
        $flow_item['send'] = [];
        array_push($flow_list, $flow_item);
        $work_row->anchor_users = null;
        $work_row->flow_list = json_encode($flow_list, JSON_UNESCAPED_UNICODE);
        $work_row->update_date = DateUtil::now();
        $work_row->update_by = $user->id;
        $work_row->save();
        $dcs = new DataCommonService();
        $dcs->businessReject($work_row, $remarks);
    }

    public function cancelSave(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $remarks = $this->request->getPost('remarks', ['string', 'trim']);
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $owner = SessionData::owner();
        $user = SessionData::user();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($work_row->status >= 20){
            return '该业务流程已结束';
        }
        if ($work_row->create_by != $user->id){
            return '没有取消业务权限';
        }
        $this->db->begin();
        try {
            $this->saveCancel($work_row,$remarks);
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function pressingSave(){
        $uid = $this->request->getPost('uid', ['string', 'trim']);
        $remarks = $this->request->getPost('remarks', ['string', 'trim']);
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $owner = SessionData::owner();
        $user = SessionData::user();
        $now = DateUtil::now();
        $work_row = WorkData::findFirst(['del_flag = 0 and uid = ?1 and owner = ?2','bind'=>[1=>$uid,2=>$owner->id]]);
        if (empty($work_row)){
            return ErrorHelper::WRONG_INPUT;
        }
        if ($work_row->status >= 20){
            return '该业务流程已结束';
        }
        if ($work_row->create_by != $user->id){
            return '没有取消业务权限';
        }
        $this->db->begin();
        try {
            $flow_list = json_decode($work_row->flow_list,true);
            $work_row->pressing_flag = CvtUtil::emptyToInt($work_row->pressing_flag) + 1;
            $flow_item = json_decode(json_encode(Constant::$data_template),true);
            $flow_item['id'] = $user->id;
            $flow_item['type'] = 2;
            $flow_item['name'] = $user->real_name;
            $flow_item['val'] = '(催办)';
            $flow_item['time'] = $now;
            $flow_item['text'] = $remarks;
            $flow_item['files'] = [];
            $flow_item['send'] = [];
            array_push($flow_list,$flow_item);
            $work_row->flow_list = json_encode($flow_list,JSON_UNESCAPED_UNICODE);
            $work_row->update_date = DateUtil::now();
            $work_row->update_by = $user->id;
            if (!$work_row->save()){
                throw new \Exception( 'WorkData表更新失败');
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function getUserList(){
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                id , real_name as name,1 as is_show
            ')
            ->addFrom('Envsan\Modules\Sys\Model\User')
            ->where('del_flag = 0 and id <> ?1 and ifnull(role_id,0) > 1 and account_status = 0 and owner = ?2', [1 =>$user->id,2=>$user->owner]);
        return $builder->getQuery()->execute();
    }

    public function checkNextReviewData(){
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('t1.uid')
            ->addFrom('Envsan\Modules\Work\Model\WorkData','t1')
            ->where('t1.del_flag = 0 and t1.owner = ?1',[1 => SessionData::ownerId()])
            ->andWhere('t1.status = 15 and t1.anchor_users like ?2 ', [2 => "%|$user->id|%"])
            ->orderBy('t1.pressing_flag desc,t1.status asc,t1.create_date asc')
            ->limit(1);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            return null;
        }
        return $rows[0]->uid;
    }
    public function checkNextReadData(){
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('t2.uid')
            ->addFrom('Envsan\Modules\Work\Model\WorkDataRead','t1')
            ->leftJoin('Envsan\Modules\Work\Model\WorkData','t1.data_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.read_flag = 0 and t1.owner = ?1',[1 => SessionData::ownerId()])
            ->andWhere('t1.user_id = ?2 ', [2 => $user->id])
            ->orderBy('t1.id asc')
            ->limit(1);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            return null;
        }
        return $rows[0]->uid;
    }
}