<?php
namespace Envsan\Modules\Trade\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\PinyinUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesOrderBom;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Model\MesProductProgress;
use Envsan\Modules\Mes\Model\MesProductType;
use Envsan\Modules\Mes\Util\Constant as MesConstant;
use Envsan\Modules\Purchase\Model\PurchaseApply;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseGoodsType;
use Envsan\Modules\Purchase\Util\Constant as PurchaseConstant;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Trade\Model\TradeCustomer;
use Envsan\Modules\Trade\Model\TradeOrder;
use Envsan\Modules\Trade\Model\TradeOrderDetail;
use Envsan\Modules\Trade\Util\Constant;
use Envsan\Modules\Work\Service\DataCommonService;
use Envsan\Modules\Work\Service\WorkService;
use Phalcon\Mvc\User\Component;

class OrderService extends BaseService
{
    public function selectAll($type = '')
    {
        $user = SessionData::user();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.order_type_name,
                t1.code,
                t1.sign_date,
                t1.ext_val,
                t1.remarks,
                t1.manager_name,
                t2.name as customer_name,
                t2.ext_val as customer_ext_val
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrder', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t1.customer_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        if ($type == 'list') {
            $builder->andWhere('t1.create_by = ?2',[2 => $user->id]);
        } else if ($type == 'assign') {
            $builder->andWhere('t1.status = 25');
        }
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new TradeOrder();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($act, $row){
            $type = $this->request->getPost('type', 'tstring');
            $customer_id = $this->request->getPost('customer_id', 'tstring');
            $order_type = $this->request->getPost('order_type', 'tstring');
            $sign_date = $this->request->getPost('sign_date', 'tstring');
            $remarks = $this->request->getPost('remarks', 'tstring');
            $files = urldecode($this->request->getPost('files', ['string', 'trim']));
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
            if (empty($type) || CheckUtil::is_empty($customer_id) || empty($order_type) || empty($sign_date)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $order_account = 0;
            if ($type == 2) {
                if (empty($row->id)) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
                if ($row->status > 10) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
                $detail_rows = $this->selectDetailsById($row->id);
                if (count($detail_rows) == 0) {
                    return $this->error('请添加订单明细');
                }
                foreach ($detail_rows as $detail_row) {
                    $order_account += round(CvtUtil::emptyToDouble($detail_row['cnt']) * CvtUtil::emptyToDouble($detail_row['price']), 2);
                }
            }

            $files = CvtUtil::emptyToArray($files);
            $ext_data = CvtUtil::emptyToArray($ext_data);

            $now = DateUtil::now();
            $user = SessionData::user();

            $order_type_item = Constant::$order_types[$order_type];
            $row->order_type = $order_type;
            $row->order_type_name = $order_type_item['name'];
            $row->customer_id = $customer_id;
            $row->sign_date = $sign_date;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->files = CvtUtil::arrayToNull($files);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $ss = new SequenceService();
                $row->code = $ss->useSequence(1);
                $row->customer_id = $customer_id;
                $row->status = 10;
                $row->status_name = Constant::$order_status[$row->status];
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            if ($type == 2) {
                $row->order_amount = $order_account;
                $row->status = 20;
                $row->status_name = Constant::$order_status[$row->status];
            }

            if (preg_match('/^[A-Za-z]+/', $row->code, $matches)) {
                $row->code = str_replace($matches[0], $order_type_item['header'], $row->code);
            }
            $row->save();
            if ($type == 2) {
                $dcm = new DataCommonService();
                $dcm->submitDesign($row->id, 1, $user->group_id, $remarks);
            }
            return $row->uid;
        });

    }

    public function selectById($id)
    {
        return TradeOrder::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function selectByUid($uid)
    {
        return TradeOrder::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = TradeOrder::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
        if (empty($row)) {
            return ErrorHelper::WRONG_INPUT;
        }
        if ($row->status > 10){
            return '已经提交不能删除';
        }
        $detail_rows = $this->selectDetailsById($row->id);
        if (count($detail_rows) > 0){
            return '请先删除订单明细';
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【删除客户】trade_Order表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function cancelReview()
    {
        $row = $this->selectByUid($this->request->getPost('uid', 'tstring'));
        if (empty($row) || $row->del_flag == 1) {
            return ErrorHelper::ROW_NOTEXIST;
        } else if ($row->status != TradeOrder::STATUS_REVIEW) {
            return ErrorHelper::WRONG_STATUS;
        }

        $ws = new WorkService();
        return $ws->cancel(1, $row->id);
    }

    public function selectDetailsById($order_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.name,
                t1.code,
                t1.new_flag_name,
                t1.cnt,
                t1.price,
                t1.ext_data,
                t1.files,
                t1.remarks
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrderDetail', 't1')
            ->where('t1.del_flag = 0 and t1.order_id = ?1', [1 => $order_id])
            ->orderBy('t1.id asc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @throws \Exception
     */
    public function passSave($id)
    {
        $row = TradeOrder::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 20){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->status = 30;
        $row->status_name = Constant::$order_status[$row->status];
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()) {
            throw new \Exception("TradeOrder表更新失败");
        }
        $ss = new SequenceService();
        $detail_rows = TradeOrderDetail::find(['del_flag = 0 and order_id = ?1','bind'=>[1=>$row->id]]);
        foreach ($detail_rows as $detail_row){
            $detail_row->status = 20;
            if (empty($detail_row->product_id)){
                $product = new MesProduct();
                $product->uid = UUID::make();
                $product->code = $detail_row->code;
                $product->name = $detail_row->name;
                $product->customer_id = $row->customer_id;
                $product->order_detail_id = $detail_row->id;
                // 存货代码
                $product->inventory_code = $detail_row->inventory_code;
                // 库存单位
                $product->inventory_unit = $detail_row->inventory_unit;
                // 外委单位
                $product->purchase_unit = $detail_row->purchase_unit;
                // 是否批次管理
                $product->is_batch_managed = 0;
                // 税率
                $product->tax_rate = $detail_row->tax_rate;
                // 单重
                $product->weight = $detail_row->weight;
                $product->status = 10;
                $product->status_name = MesConstant::$product_status[$product->status];
                $product->update_date = $now;
                $product->update_by = $user->id;
                $product->del_flag = 0;
                $product->owner = $user->owner;
                if (!$product->save()) {
                    throw new \Exception("MesProduct表更新失败");
                }
                $detail_row->product_id = $product->id;
            } else {
                $product_row = MesProduct::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$detail_row->product_id]]);
                if (empty($product_row)){
                    throw new \Exception("MesProduct表更新失败");
                }
                $detail_row->status = 30;
            }
            $detail_row->status_name = Constant::$order_detail_status[$detail_row->status];
            $detail_row->update_date = $now;
            $detail_row->update_by = $user->id;
            if (!$detail_row->save()) {
                throw new \Exception("TradeOrderDetail表更新失败");
            }
        }
    }

    public function rejectSave($id,$reject_remarks)
    {
        $row = $this->selectById($id);
        if (empty($row)){
            throw new \Exception("输入不正确，数据已失效");
        }
        if ($row->status != 20){
            throw new \Exception("数据状态已变更");
        }
        $now = DateUtil::now();
        $user = SessionData::user();
        $row->status = 10;
        $row->status_name ='被驳回';
        $row->update_date = $now;
        $row->update_by = $user->id;
        if (!$row->save()) {
            throw new \Exception("【合同变更审批驳回】TradeOrderChange表更新失败");
        }
    }
}