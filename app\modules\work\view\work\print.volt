<div id="app" style="padding: 0;display: flex;flex-direction: row">
    <div style="width: 75%;background-color: #f2f2f2;min-height: 100vh;display: flex;flex-direction: row;justify-content: center">
        <div class="container-print-page">
            <div class="container1-page" :style="{width:width+'px'}">
                <div style="font-size: 20px;font-weight: bold;margin-bottom: 20px;text-align: center">
                    <span v-text="data.type_name"></span> (审批)
                </div>
                <div style="border-width: 1px 0 0 1px;border-style: solid;border-color:#898989;">
                    <div v-for="row_no in Math.ceil(data.form_list.length / item_cnt)"
                         style="display: flex;flex-direction: row;">
                         <div :style="{
                            display: 'flex',
                            width:(100/item_cnt).toFixed(4) + '%',
                            padding:'10px',
                            borderWidth: '0 1px 1px 0',
                            borderStyle: 'solid',
                            borderColor: '#898989'}"
                            v-for="cell_no in item_cnt">
                            <template v-if="Math.round((row_no-1)*item_cnt + (cell_no -1)) < data.form_list.length">
                                <div>${data.form_list[Math.round((row_no-1)*item_cnt + (cell_no -1))]['name']}</div>：
                                <div>
                                    <span>${data.form_list[Math.round((row_no-1)*item_cnt + (cell_no -1))]['value']} ${data.form_list[Math.round((row_no-1)*item_cnt + (cell_no -1))]['unit']}</span>
                                </div>
                            </template>
                         </div>
                    </div>
                </div>
                <div style="margin-top: 15px">
                    <table v-if="data.detail_list.length > 0 && table_dict == 'row'" style="width: 100%;border-spacing:0">
                        <thead>
                            <tr>
                                <th style="border-color:#898989;border-style: solid;border-width: 1px 1px 1px 1px;text-align: center;padding:5px" v-for="(header, header_idx) in data.detail_list[0]" v-text="header.name"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(row, row_idx) in data.detail_list">
                                <td style="border-color:#898989;border-style: solid;border-width: 0 1px 1px 1px;text-align: center;padding:5px"
                                    v-for="(col, col_idx) in row">
                                    <div>
                                        <span v-text="col.value"></span>
                                        <span v-if="col.unit" v-text="col.unit" style="margin-left: 5px;"></span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div v-if="data.detail_list.length > 0 && table_dict == 'column'" style="border-width: 0 1px 1px 0;border-style: solid;border-color:#898989">
                        <div v-for="(row, row_idx) in data.detail_list" style="padding: 10px;border-width:1px 0 0 1px;border-style: solid;border-color:#898989">
                            <div v-for="(col, col_idx) in row" style="display: flex;text-align: left;padding: 3px 0;">
                                <div style="width: 25%;text-align: right;padding-right: 15px">
                                    <span v-text="col.name"></span>：
                                </div>
                                <div style="width: 75%">
                                    <span v-text="col.value"></span>
                                    <span v-if="col.unit" v-text="col.unit" style="margin-left: 5px;"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 15px;width: 100%;border-width:1px 0 0 1px;border-style: solid;border-color:#898989">
                    <div v-for="(pass_item,pass_idx) in data.pass_list" style="display: flex;">
                        <div style="width: 20%;border-width: 0 1px 1px 0;border-style: solid;border-color:#898989;padding: 10px 0;text-align: center">
                            <div>
                                <span v-text="pass_item.anchor_name"></span>:
                            </div>
                            <div style="margin-top: 5px">
                                <span>审批意见</span>
                            </div>
                        </div>
                        <div style="width: 80%;border-width: 0 1px 1px 0;border-style: solid;border-color:#898989;">
                            <div style="display: flex;width: 100%">
                                <div style="width: 60%;padding: 10px;text-align: left">
                                    <span v-if="pass_item.text != null && pass_item.text != ''" v-text="pass_item.text"></span>
                                    <span v-else >同意！</span>
                                </div>
                                <div style="width: 40%">
                                    <div style="text-align: right;padding: 15px">
                                        <div v-if="pass_item.sign != null">
                                            <img :src="pass_item.sign" style="width: 150px"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="display: flex;padding: 10px">
                                <div style="width: 50%">
                                    审批人：<span v-text="pass_item.user_name"></span>
                                </div>
                                <div style="width: 50%">
                                    审批时间：<span v-text="pass_item.update_date"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="width: 25%;background-color: #FFF;min-height: 100vh;">
        <div style="height:100vh;border-right: 1px #E2E2E2 solid;">
            <div class="portlet-body form">
                <form id="form" class="form-horizontal">
                    <div class="form-body" style="height: 87vh;overflow-y: auto;">
                        <div id="form_data">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">打印宽度</label>
                                        <div class="col-sm-8">
                                            <input type="number" class="form-control" name="width" v-model="width" maxlength="50"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">每行项目数</label>
                                        <div class="col-sm-8">
                                            <input type="number" class="form-control" name="item_cnt" v-model="item_cnt" maxlength="100"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">明细表格</label>
                                        <div class="col-sm-8">
                                            <select name="gender" class="bs-select form-control" name="table_dict" v-model="table_dict">
                                                <option value="row">横向</option>
                                                <option value="column">纵向</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12" style="text-align: right">
                                    <button type="button" class="btn blue" @click="printPreview"><i class="fa fa-print"></i> 打印</button>
                                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            width : 780,
            item_cnt : 2,
            table_dict : 'row',
            data : {{ jsonData }}
        },
        methods: {
            printPreview(){
                const iframe  = document.createElement("iframe");
                const f  = document.body.appendChild(iframe);
                iframe.id = "myIframe";
                iframe.setAttribute(
                    "style",
                    "position:absolute;width:0;height:0;top:-10px;left:-10px;"
                );
                const w = f.contentWindow || f.contentDocument;
                // eslint-disable-next-line prefer-const
                const doc = f.contentDocument || f.contentWindow.document;
                doc.open();
                doc.write($('.container-print-page').html());
                doc.close();
                w.print();
            }
        }
    });
</script>

<style scoped>
    .container-print-page{
        margin-top: 10px;
        background-color: #fff;
        text-align: center;
    }
    .container1-page{
        background-color: #fff;
        min-height: 300px;
        margin: 20px;
    }
</style>
