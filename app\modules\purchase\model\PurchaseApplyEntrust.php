<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseApplyEntrust extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $type;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_bom_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
//    public $bom_goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $product_bom_name;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $deputy_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
//    public $ww_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
//    public $bom_goods_name;


    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $code;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $notice_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $notice_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
//    public $product_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
    public $ordered_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
    public $remaining_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
//    public $bom_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
//    public $pricing_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $purchase_order_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_apply_entrust';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseApplyEntrust[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseApplyEntrust
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
