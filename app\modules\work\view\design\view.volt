{% do assets.collection('css').addCss('static/global/plugins/jsplumb/css/jsplumbtoolkit-defaults.css') %}
{% do assets.collection('css').addCss('static/global/plugins/jsplumb/css/main.css') %}
{% do assets.collection('js').addJs('static/global/plugins/jsplumb/js/jsplumb.js') %}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-body">
            <div class="flow-main">
                {% for index, cols in flow_list %}
                <div class="flow-col">
                    {% for item in cols %}
                        {% if item.type == 'point' %}
                            <div id="item_{{ item.id }}" class="flow-item">
                                <div class="btn-group">
                                    <button type="button" class="btn {% if index == 0 %}blue{% else %}dark{% endif %} btn-outline btn-circle btn-lg dropdown-toggle" data-toggle="dropdown">
                                        {{ item.name }} <span class="fa fa-caret-down"></span>
                                    </button>
                                    <ul class="dropdown-menu pull-right" role="menu">
                                        <li><a href="javascript:" onclick="edit('{{ item.id }}')"><i class="fa fa-fw fa-edit"></i> 编辑流程</a></li>
                                        {% if item.form_uid %}
                                            <li><a href="javascript:" onclick="editForm('{{ item.form_uid }}')"><i class="fa fa-fw fa-table"></i> 编辑表单</a></li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        {% else %}
                            <div class="flow-label">
                                <div id="item_{{ item.id }}" class="flow-item">
                                    <span class="label label-{% if item.val == 0 %}danger{% else %}success{% endif %}">{{ item.name }}</span>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
    jsPlumb.ready(function () {
        let common = {
            endpoint: 'Blank',
            connector: ['Flowchart'],
            anchor: ['Left', 'Right'],
            paintStyle: { stroke: '#2f353b', strokeWidth: 1 },
            overlays: [ ['Arrow', { width: 8, length: 8, location: 1 }] ]
        }

        {% for connect in connect_list %}
        jsPlumb.connect({
            source: 'item_{{ connect.src }}',
            target: 'item_{{ connect.tar }}'
        }, common);
        {% endfor %}
    });

    function edit(id) {
        top.layer.open({
            title:'编辑',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('work/design/create/') }}' + id.split("_")[0],
            end: function() {
                location.reload();
            }
        });
    }

    function editForm(uid) {
        top.layer.open({
            title: '编辑',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('work/form/edit/') }}' + uid,
        });
    }

    $(function() {
        $(".flow-main").height($(".page-content").height() - 35);
    })
</script>
<style>
    .flow-main {
        display: flex;
        justify-content: center;
    }

    .flow-col {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        margin-right: 80px;
    }

    .flow-item {
        margin: 30px 0;
    }

    .flow-label {
        height: 46px;
        margin: 30px 0;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .flow-item .label {
        font-size: 125%;
    }
</style>