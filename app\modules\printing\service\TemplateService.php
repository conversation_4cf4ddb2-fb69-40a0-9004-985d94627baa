<?php
namespace Envsan\Modules\Printing\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Printing\Model\PrintingTemplate;
use Envsan\Modules\Printing\Util\Constant;
use Phalcon\Mvc\User\Component;

class TemplateService extends Component
{
    public function selectAll(){
        $name = $this->request->get('name', 'tstring');
        $builder = $this->modelsManager->createBuilder()
        ->columns('
            t1.id,
            t1.uid,
            t1.page_name,
            t1.template_name,
            t1.status,
            t1.default_flag
        ')
        ->addFrom('Envsan\Modules\Printing\Model\PrintingTemplate', 't1')
        ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
        ->orderBy('t1.id desc');
        if (!CheckUtil::is_empty($name)) {
            $builder->andWhere("t1.template_name like ?2", [2 => "%$name%"]);
        }
        return $builder;
    }

    public function getInitData(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $jrow = $row->toArray();
        if (!empty($jrow['form_data'])){
            $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
        }
        $jrow['bind_data'] = Constant::$print_data_list[$row->page_id]['page_data'];
        $rtn->message = '';
        $rtn->data = $jrow;
        return $rtn;
    }

    public function saveData(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        $form_data = str_replace('%2B','+',urldecode($this->request->getPost('form_data', 'tstring')));
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $form_data = CvtUtil::emptyToArray($form_data);
        $row->form_data = CvtUtil::arrayToNull($form_data);
        if (!$row->save()){
            $rtn->message = ErrorHelper::UNKOWN;
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function create()
    {
        $row = new PrintingTemplate();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        $template_name= $this->request->getPost('template_name', 'tstring');
        $page_id = trim($this->request->getPost('page_id', 'string'));
        $default_flag = trim($this->request->getPost('default_flag', 'string'));
        $status = trim($this->request->getPost('status', 'string'));
        if (empty($page_id))
            return ErrorHelper::WRONG_INPUT;

        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
            $row->default_flag = $default_flag;
            $row->status = $status;
            $row->page_id = $page_id;
            $row->page_name =  Constant::$print_data_list[$page_id]['page_name'];
            $row->template_name = $template_name;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("PrintingTemplate表更新失败");
            }
            if ($default_flag == 1){
                $page_rows = PrintingTemplate::find(['del_flag = 0 and id <> ?1 and page_id = ?2', 'bind' => [1 => $row->id,2=> $row->page_id]]);
                foreach ($page_rows as $page_row){
                    $page_row->default_flag = 0;
                    $page_row->update_date = $now;
                    $page_row->update_by = $user->id;
                    if (!$page_row->save()) {
                        throw new \Exception("PrintingTemplate表更新失败");
                    }
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function delete(){
        $uid = $this->request->getPost('uid');
        if (empty($uid)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row = $this->selectByUid($uid);
        if (empty($row)){
            return ErrorHelper::WRONG_INPUT;
        }
        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        if (!$row->save()){
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function selectById($id)
    {
        return PrintingTemplate::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PrintingTemplate::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function getPageList(){
        $page_list = [];
        foreach (Constant::$print_data_list as $idx => $item){
            $page_list[$idx] = $item['page_name'];
        }
        return $page_list;
    }

    public function getPrintTempList($page_id){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
            t1.uid,
            t1.template_name,
            t1.default_flag
        ')
        ->addFrom('Envsan\Modules\Printing\Model\PrintingTemplate', 't1')
        ->where('t1.del_flag = 0 and t1.status = 1 and t1.page_id = ?1', [1 => $page_id])
        ->orderBy('t1.id desc');
        $rows = $builder->getQuery()->execute()->toArray();
        $data = [
            'uid' => '',
            'name' => '',
            'list' => $rows
        ];
        foreach ($rows as $row){
            $data['uid'] = $row['uid'];
            $data['name'] = $row['name'];
            if ($data['default_flag'] == 1){
                $data['uid'] = $row['uid'];
                $data['name'] = $row['name'];
                break;
            }
        }
        return $data;
    }
}