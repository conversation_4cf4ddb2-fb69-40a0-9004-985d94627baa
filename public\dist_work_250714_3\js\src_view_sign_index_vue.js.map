{"version": 3, "file": "js/src_view_sign_index_vue.js", "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AC7BA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AChMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC3BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACnEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://rrts-manager/src/components/base.vue", "webpack://rrts-manager/src/view/sign/index.vue", "webpack://rrts-manager/./src/components/base.vue", "webpack://rrts-manager/./src/view/sign/index.vue", "webpack://rrts-manager/./node_modules/core-js/internals/a-possible-prototype.js", "webpack://rrts-manager/./node_modules/core-js/internals/an-instance.js", "webpack://rrts-manager/./node_modules/core-js/internals/array-buffer-basic-detection.js", "webpack://rrts-manager/./node_modules/core-js/internals/array-buffer-view-core.js", "webpack://rrts-manager/./node_modules/core-js/internals/array-with.js", "webpack://rrts-manager/./node_modules/core-js/internals/classof.js", "webpack://rrts-manager/./node_modules/core-js/internals/correct-prototype-getter.js", "webpack://rrts-manager/./node_modules/core-js/internals/define-built-in-accessor.js", "webpack://rrts-manager/./node_modules/core-js/internals/dom-exception-constants.js", "webpack://rrts-manager/./node_modules/core-js/internals/error-stack-clear.js", "webpack://rrts-manager/./node_modules/core-js/internals/function-uncurry-this-accessor.js", "webpack://rrts-manager/./node_modules/core-js/internals/inherit-if-required.js", "webpack://rrts-manager/./node_modules/core-js/internals/is-big-int-array.js", "webpack://rrts-manager/./node_modules/core-js/internals/is-possible-prototype.js", "webpack://rrts-manager/./node_modules/core-js/internals/normalize-string-argument.js", "webpack://rrts-manager/./node_modules/core-js/internals/object-get-prototype-of.js", "webpack://rrts-manager/./node_modules/core-js/internals/object-set-prototype-of.js", "webpack://rrts-manager/./node_modules/core-js/internals/to-big-int.js", "webpack://rrts-manager/./node_modules/core-js/internals/to-string-tag-support.js", "webpack://rrts-manager/./node_modules/core-js/internals/to-string.js", "webpack://rrts-manager/./node_modules/core-js/modules/es.typed-array.with.js", "webpack://rrts-manager/./node_modules/core-js/modules/web.dom-exception.stack.js", "webpack://rrts-manager/./src/view/sign/index.vue?65fd", "webpack://rrts-manager/./src/view/sign/index.vue?dea2", "webpack://rrts-manager/./src/components/base.vue?43cf", "webpack://rrts-manager/./src/components/base.vue?a524", "webpack://rrts-manager/./src/view/sign/index.vue?e788", "webpack://rrts-manager/./src/view/sign/index.vue?345f", "webpack://rrts-manager/./src/view/sign/index.vue?30ca", "webpack://rrts-manager/./src/view/sign/index.vue?1658"], "sourcesContent": ["<template></template>\r\n<script>\r\n    export default {\r\n        name: \"base\",\r\n        data () {\r\n            return {\r\n                route_name:'',\r\n                base_to_view_name:''\r\n            };\r\n        },\r\n        created() {\r\n            this.route_name = this.$router.currentRoute.name;\r\n        },\r\n        beforeRouteEnter(to, from, next) {\r\n            //console.log('beforeRouteEnter',from.name,to.name);\r\n            if (to.meta.from === undefined || to.meta.from == ''){\r\n                to.meta.from = from.name;\r\n            }\r\n            next();\r\n        },\r\n        activated() {\r\n            if (this.base_to_view_name == ''){\r\n                this.onLoad ? this.onLoad() : void(0);\r\n            }\r\n        },\r\n        watch:{\r\n            '$route'(to, from){\r\n                if (this.$store.state.auth == false){\r\n                    this.base_to_view_name = '';\r\n                }\r\n                if (from.name == this.route_name){\r\n                    //console.log('$route',from.name,to.name);\r\n                    if (from.meta.from == to.name){\r\n                        from.meta.from = '';\r\n                        this.base_to_view_name = '';\r\n                    } else {\r\n                        this.base_to_view_name = to.name;\r\n                    }\r\n                } else if (to.name == this.route_name) {\r\n                    if (this.base_to_view_name != ''){\r\n                        //console.log(this.base_to_view_name,from.name);\r\n                        this.onShow ? this.onShow() : void(0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</script>", "<template>\r\n    <div class=\"main\">\r\n        <m-header name=\"签字\" is_back=\"1\"></m-header>\r\n        <m-body padding=\"false\">\r\n           <div style=\"background-color: #f2f2f2;display: flex;flex-direction: row\">\r\n               <div style=\"width: 80%;height: 93vh;border:1px solid #D2D2D2;background-color: #fff;\">\r\n                   <div id=\"signCanvas\"></div>\r\n               </div>\r\n               <div style=\"width: 20%;display: flex;flex-direction: column;justify-content: center;\">\r\n                   <van-button round plain style=\"width: 80px;transform: rotate(-90deg);margin-bottom: 180px\" type=\"danger\" @click=\"reset\">重写</van-button>\r\n                   <van-button round plain style=\"width: 80px;transform: rotate(-90deg);\" type=\"info\" @click=\"submit\">提交</van-button>\r\n               </div>\r\n           </div>\r\n        </m-body>\r\n    </div>\r\n</template>\r\n<script>\r\n    import base from '../../components/base';\r\n    import Config from \"../../config\";\r\n    import { Dialog } from 'vant';\r\n\r\n    export default {\r\n        name: \"sign\",\r\n        extends: base,\r\n        data() {\r\n            return {\r\n                canvas:null,\r\n                sign_show:false,\r\n                money: 0,\r\n                company_ids: [],\r\n                uid:'',\r\n                url:'',\r\n                path:'',\r\n                title:'',\r\n                type:''\r\n            }\r\n        },\r\n        methods: {\r\n            onLoad() {\r\n                this.initCanvas();\r\n            },\r\n            onShow() {\r\n\r\n            },\r\n            initCanvas(){\r\n                if (this.canvas == null){\r\n                    this.canvas = $(\"#signCanvas\").jSignature({\r\n                        lineWidth:5,\r\n                        color:'#000000',\r\n                        width:'100%' ,\r\n                        'height':'92vh'\r\n                    });\r\n                }\r\n            },\r\n            reset(){\r\n                if (this.canvas != null){\r\n                    this.canvas.jSignature('reset');\r\n                }\r\n            },\r\n            submit(){\r\n                if (this.canvas == null){\r\n                    return;\r\n                }\r\n                let data = this.canvas.jSignature('getData');\r\n                console.log(data);\r\n                console.log(data.length);\r\n                if (data.length <= 14000){\r\n                    this.$toast.fail('请签字！');\r\n                    return;\r\n                }\r\n                this.rotateBase64Image(data,90).then((base_data) => {\r\n                    Dialog.confirm({\r\n                        title: '提交',\r\n                        message: '确定要提交吗？',\r\n                    })\r\n                    .then(() => {\r\n                        let url = 'work/user/sign';\r\n                        this.$http.post(url,{\r\n                            data : base_data\r\n                        }).then((rs) => {\r\n                            if (rs.status == 'ok'){\r\n                                this.$router.back();\r\n                            } else {\r\n                                this.$toast.fail(rs.message);\r\n                            }\r\n                        }).catch((e) => {\r\n                            this.$toast.fail('保存失败！');\r\n                        });\r\n                    })\r\n                    .catch(() => {});\r\n                }).catch((e) => {\r\n                    this.$toast.fail('保存失败！');\r\n                });\r\n\r\n\r\n\r\n            },\r\n            rotateBase64Image(base64, angle){\r\n                return new Promise((resolve, reject) => {\r\n                    try {\r\n                        // 将base64字符串转换为Uint8Array\r\n                        const binaryString = window.atob(base64.split(',')[1]);\r\n                        const len = binaryString.length;\r\n                        const bytes = new Uint8Array(len);\r\n                        for (let i = 0; i < len; i++) {\r\n                            bytes[i] = binaryString.charCodeAt(i);\r\n                        }\r\n                        // 创建Blob对象\r\n                        const blob = new Blob([bytes], {type: 'image/png'});\r\n                        // 创建Image对象并设置其src为Blob URL\r\n                        const img = new Image();\r\n                        const urlCreator = window.URL || window.webkitURL;\r\n                        const objectUrl = urlCreator.createObjectURL(blob);\r\n                        img.src = objectUrl;\r\n\r\n                        img.onload = function() {\r\n                            // 创建Canvas元素并设置其大小与图像相同\r\n                            const canvas = document.createElement('canvas');\r\n                            const ctx = canvas.getContext('2d');\r\n                            canvas.width = img.height;\r\n                            canvas.height = img.width;\r\n\r\n                            // 应用旋转变换并绘制图像\r\n                            ctx.translate(canvas.width / 2, canvas.height / 2); // 将原点移至中心\r\n                            ctx.rotate(angle * Math.PI / 180); // 旋转角度转换为弧度\r\n                            ctx.drawImage(img, -img.width / 2, -img.height / 2); // 绘制图像到中心点\r\n\r\n                            // 将Canvas内容转换为新的Base64字符串\r\n                            canvas.toBlob(function(blob) {\r\n                                const reader = new FileReader();\r\n                                reader.onerror = reject;\r\n                                reader.onload = () => {\r\n                                    resolve(reader.result);\r\n                                };\r\n                                reader.readAsDataURL(blob); // 转换为Base64\r\n                            }, 'image/png');\r\n                        };\r\n                        img.onerror = function() {\r\n                            reject('Error loading image.');\r\n                        };\r\n                    } catch (error) {\r\n                        reject(error);\r\n                    }\r\n                });\r\n            }\r\n        }\r\n    }\r\n</script>\r\n<style scoped>\r\n    iframe{\r\n        border: 0 !important;\r\n    }\r\n    .footer{\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 60px;\r\n        border-top: 1px #E2E2E2 solid;\r\n        background-color: #FFFFFF;\r\n        z-index: 1000;\r\n        box-sizing: border-box;\r\n        padding-top: 8px;\r\n        padding-right: 15px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: flex-end;\r\n    }\r\n    .sign-canvas{\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 390px;\r\n        z-index: 9999;\r\n    }\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c(\"div\")\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"main\"},[_c('m-header',{attrs:{\"name\":\"签字\",\"is_back\":\"1\"}}),_c('m-body',{attrs:{\"padding\":\"false\"}},[_c('div',{staticStyle:{\"background-color\":\"#f2f2f2\",\"display\":\"flex\",\"flex-direction\":\"row\"}},[_c('div',{staticStyle:{\"width\":\"80%\",\"height\":\"93vh\",\"border\":\"1px solid #D2D2D2\",\"background-color\":\"#fff\"}},[_c('div',{attrs:{\"id\":\"signCanvas\"}})]),_c('div',{staticStyle:{\"width\":\"20%\",\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\"}},[_c('van-button',{staticStyle:{\"width\":\"80px\",\"transform\":\"rotate(-90deg)\",\"margin-bottom\":\"180px\"},attrs:{\"round\":\"\",\"plain\":\"\",\"type\":\"danger\"},on:{\"click\":_vm.reset}},[_vm._v(\"重写\")]),_c('van-button',{staticStyle:{\"width\":\"80px\",\"transform\":\"rotate(-90deg)\"},attrs:{\"round\":\"\",\"plain\":\"\",\"type\":\"info\"},on:{\"click\":_vm.submit}},[_vm._v(\"提交\")])],1)])])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "'use strict';\nvar isPossiblePrototype = require('../internals/is-possible-prototype');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument) {\n  if (isPossiblePrototype(argument)) return argument;\n  throw new $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};\n", "'use strict';\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (it, Prototype) {\n  if (isPrototypeOf(Prototype, it)) return it;\n  throw new $TypeError('Incorrect invocation');\n};\n", "'use strict';\n// eslint-disable-next-line es/no-typed-arrays -- safe\nmodule.exports = typeof ArrayBuffer != 'undefined' && typeof DataView != 'undefined';\n", "'use strict';\nvar NATIVE_ARRAY_BUFFER = require('../internals/array-buffer-basic-detection');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar hasOwn = require('../internals/has-own-property');\nvar classof = require('../internals/classof');\nvar tryToString = require('../internals/try-to-string');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineBuiltInAccessor = require('../internals/define-built-in-accessor');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar uid = require('../internals/uid');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar Int8Array = globalThis.Int8Array;\nvar Int8ArrayPrototype = Int8Array && Int8Array.prototype;\nvar Uint8ClampedArray = globalThis.Uint8ClampedArray;\nvar Uint8ClampedArrayPrototype = Uint8ClampedArray && Uint8ClampedArray.prototype;\nvar TypedArray = Int8Array && getPrototypeOf(Int8Array);\nvar TypedArrayPrototype = Int8ArrayPrototype && getPrototypeOf(Int8ArrayPrototype);\nvar ObjectPrototype = Object.prototype;\nvar TypeError = globalThis.TypeError;\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar TYPED_ARRAY_TAG = uid('TYPED_ARRAY_TAG');\nvar TYPED_ARRAY_CONSTRUCTOR = 'TypedArrayConstructor';\n// Fixing native typed arrays in Opera Presto crashes the browser, see #595\nvar NATIVE_ARRAY_BUFFER_VIEWS = NATIVE_ARRAY_BUFFER && !!setPrototypeOf && classof(globalThis.opera) !== 'Opera';\nvar TYPED_ARRAY_TAG_REQUIRED = false;\nvar NAME, Constructor, Prototype;\n\nvar TypedArrayConstructorsList = {\n  Int8Array: 1,\n  Uint8Array: 1,\n  Uint8ClampedArray: 1,\n  Int16Array: 2,\n  Uint16Array: 2,\n  Int32Array: 4,\n  Uint32Array: 4,\n  Float32Array: 4,\n  Float64Array: 8\n};\n\nvar BigIntArrayConstructorsList = {\n  BigInt64Array: 8,\n  BigUint64Array: 8\n};\n\nvar isView = function isView(it) {\n  if (!isObject(it)) return false;\n  var klass = classof(it);\n  return klass === 'DataView'\n    || hasOwn(TypedArrayConstructorsList, klass)\n    || hasOwn(BigIntArrayConstructorsList, klass);\n};\n\nvar getTypedArrayConstructor = function (it) {\n  var proto = getPrototypeOf(it);\n  if (!isObject(proto)) return;\n  var state = getInternalState(proto);\n  return (state && hasOwn(state, TYPED_ARRAY_CONSTRUCTOR)) ? state[TYPED_ARRAY_CONSTRUCTOR] : getTypedArrayConstructor(proto);\n};\n\nvar isTypedArray = function (it) {\n  if (!isObject(it)) return false;\n  var klass = classof(it);\n  return hasOwn(TypedArrayConstructorsList, klass)\n    || hasOwn(BigIntArrayConstructorsList, klass);\n};\n\nvar aTypedArray = function (it) {\n  if (isTypedArray(it)) return it;\n  throw new TypeError('Target is not a typed array');\n};\n\nvar aTypedArrayConstructor = function (C) {\n  if (isCallable(C) && (!setPrototypeOf || isPrototypeOf(TypedArray, C))) return C;\n  throw new TypeError(tryToString(C) + ' is not a typed array constructor');\n};\n\nvar exportTypedArrayMethod = function (KEY, property, forced, options) {\n  if (!DESCRIPTORS) return;\n  if (forced) for (var ARRAY in TypedArrayConstructorsList) {\n    var TypedArrayConstructor = globalThis[ARRAY];\n    if (TypedArrayConstructor && hasOwn(TypedArrayConstructor.prototype, KEY)) try {\n      delete TypedArrayConstructor.prototype[KEY];\n    } catch (error) {\n      // old WebKit bug - some methods are non-configurable\n      try {\n        TypedArrayConstructor.prototype[KEY] = property;\n      } catch (error2) { /* empty */ }\n    }\n  }\n  if (!TypedArrayPrototype[KEY] || forced) {\n    defineBuiltIn(TypedArrayPrototype, KEY, forced ? property\n      : NATIVE_ARRAY_BUFFER_VIEWS && Int8ArrayPrototype[KEY] || property, options);\n  }\n};\n\nvar exportTypedArrayStaticMethod = function (KEY, property, forced) {\n  var ARRAY, TypedArrayConstructor;\n  if (!DESCRIPTORS) return;\n  if (setPrototypeOf) {\n    if (forced) for (ARRAY in TypedArrayConstructorsList) {\n      TypedArrayConstructor = globalThis[ARRAY];\n      if (TypedArrayConstructor && hasOwn(TypedArrayConstructor, KEY)) try {\n        delete TypedArrayConstructor[KEY];\n      } catch (error) { /* empty */ }\n    }\n    if (!TypedArray[KEY] || forced) {\n      // V8 ~ Chrome 49-50 `%TypedArray%` methods are non-writable non-configurable\n      try {\n        return defineBuiltIn(TypedArray, KEY, forced ? property : NATIVE_ARRAY_BUFFER_VIEWS && TypedArray[KEY] || property);\n      } catch (error) { /* empty */ }\n    } else return;\n  }\n  for (ARRAY in TypedArrayConstructorsList) {\n    TypedArrayConstructor = globalThis[ARRAY];\n    if (TypedArrayConstructor && (!TypedArrayConstructor[KEY] || forced)) {\n      defineBuiltIn(TypedArrayConstructor, KEY, property);\n    }\n  }\n};\n\nfor (NAME in TypedArrayConstructorsList) {\n  Constructor = globalThis[NAME];\n  Prototype = Constructor && Constructor.prototype;\n  if (Prototype) enforceInternalState(Prototype)[TYPED_ARRAY_CONSTRUCTOR] = Constructor;\n  else NATIVE_ARRAY_BUFFER_VIEWS = false;\n}\n\nfor (NAME in BigIntArrayConstructorsList) {\n  Constructor = globalThis[NAME];\n  Prototype = Constructor && Constructor.prototype;\n  if (Prototype) enforceInternalState(Prototype)[TYPED_ARRAY_CONSTRUCTOR] = Constructor;\n}\n\n// WebKit bug - typed arrays constructors prototype is Object.prototype\nif (!NATIVE_ARRAY_BUFFER_VIEWS || !isCallable(TypedArray) || TypedArray === Function.prototype) {\n  // eslint-disable-next-line no-shadow -- safe\n  TypedArray = function TypedArray() {\n    throw new TypeError('Incorrect invocation');\n  };\n  if (NATIVE_ARRAY_BUFFER_VIEWS) for (NAME in TypedArrayConstructorsList) {\n    if (globalThis[NAME]) setPrototypeOf(globalThis[NAME], TypedArray);\n  }\n}\n\nif (!NATIVE_ARRAY_BUFFER_VIEWS || !TypedArrayPrototype || TypedArrayPrototype === ObjectPrototype) {\n  TypedArrayPrototype = TypedArray.prototype;\n  if (NATIVE_ARRAY_BUFFER_VIEWS) for (NAME in TypedArrayConstructorsList) {\n    if (globalThis[NAME]) setPrototypeOf(globalThis[NAME].prototype, TypedArrayPrototype);\n  }\n}\n\n// WebKit bug - one more object in Uint8ClampedArray prototype chain\nif (NATIVE_ARRAY_BUFFER_VIEWS && getPrototypeOf(Uint8ClampedArrayPrototype) !== TypedArrayPrototype) {\n  setPrototypeOf(Uint8ClampedArrayPrototype, TypedArrayPrototype);\n}\n\nif (DESCRIPTORS && !hasOwn(TypedArrayPrototype, TO_STRING_TAG)) {\n  TYPED_ARRAY_TAG_REQUIRED = true;\n  defineBuiltInAccessor(TypedArrayPrototype, TO_STRING_TAG, {\n    configurable: true,\n    get: function () {\n      return isObject(this) ? this[TYPED_ARRAY_TAG] : undefined;\n    }\n  });\n  for (NAME in TypedArrayConstructorsList) if (globalThis[NAME]) {\n    createNonEnumerableProperty(globalThis[NAME], TYPED_ARRAY_TAG, NAME);\n  }\n}\n\nmodule.exports = {\n  NATIVE_ARRAY_BUFFER_VIEWS: NATIVE_ARRAY_BUFFER_VIEWS,\n  TYPED_ARRAY_TAG: TYPED_ARRAY_TAG_REQUIRED && TYPED_ARRAY_TAG,\n  aTypedArray: aTypedArray,\n  aTypedArrayConstructor: aTypedArrayConstructor,\n  exportTypedArrayMethod: exportTypedArrayMethod,\n  exportTypedArrayStaticMethod: exportTypedArrayStaticMethod,\n  getTypedArrayConstructor: getTypedArrayConstructor,\n  isView: isView,\n  isTypedArray: isTypedArray,\n  TypedArray: TypedArray,\n  TypedArrayPrototype: TypedArrayPrototype\n};\n", "'use strict';\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar $RangeError = RangeError;\n\n// https://tc39.es/proposal-change-array-by-copy/#sec-array.prototype.with\n// https://tc39.es/proposal-change-array-by-copy/#sec-%typedarray%.prototype.with\nmodule.exports = function (O, C, index, value) {\n  var len = lengthOfArrayLike(O);\n  var relativeIndex = toIntegerOrInfinity(index);\n  var actualIndex = relativeIndex < 0 ? len + relativeIndex : relativeIndex;\n  if (actualIndex >= len || actualIndex < 0) throw new $RangeError('Incorrect index');\n  var A = new C(len);\n  var k = 0;\n  for (; k < len; k++) A[k] = k === actualIndex ? value : O[k];\n  return A;\n};\n", "'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) === 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) === 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  // eslint-disable-next-line es/no-object-getprototypeof -- required for testing\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n", "'use strict';\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineProperty = require('../internals/object-define-property');\n\nmodule.exports = function (target, name, descriptor) {\n  if (descriptor.get) makeBuiltIn(descriptor.get, name, { getter: true });\n  if (descriptor.set) makeBuiltIn(descriptor.set, name, { setter: true });\n  return defineProperty.f(target, name, descriptor);\n};\n", "'use strict';\nmodule.exports = {\n  IndexSizeError: { s: 'INDEX_SIZE_ERR', c: 1, m: 1 },\n  DOMStringSizeError: { s: 'DOMSTRING_SIZE_ERR', c: 2, m: 0 },\n  HierarchyRequestError: { s: 'HIERARCHY_REQUEST_ERR', c: 3, m: 1 },\n  WrongDocumentError: { s: 'WRONG_DOCUMENT_ERR', c: 4, m: 1 },\n  InvalidCharacterError: { s: 'INVALID_CHARACTER_ERR', c: 5, m: 1 },\n  NoDataAllowedError: { s: 'NO_DATA_ALLOWED_ERR', c: 6, m: 0 },\n  NoModificationAllowedError: { s: 'NO_MODIFICATION_ALLOWED_ERR', c: 7, m: 1 },\n  NotFoundError: { s: 'NOT_FOUND_ERR', c: 8, m: 1 },\n  NotSupportedError: { s: 'NOT_SUPPORTED_ERR', c: 9, m: 1 },\n  InUseAttributeError: { s: 'INUSE_ATTRIBUTE_ERR', c: 10, m: 1 },\n  InvalidStateError: { s: 'INVALID_STATE_ERR', c: 11, m: 1 },\n  SyntaxError: { s: 'SYNTAX_ERR', c: 12, m: 1 },\n  InvalidModificationError: { s: 'INVALID_MODIFICATION_ERR', c: 13, m: 1 },\n  NamespaceError: { s: 'NAMESPACE_ERR', c: 14, m: 1 },\n  InvalidAccessError: { s: 'INVALID_ACCESS_ERR', c: 15, m: 1 },\n  ValidationError: { s: 'VALIDATION_ERR', c: 16, m: 0 },\n  TypeMismatchError: { s: 'TYPE_MISMATCH_ERR', c: 17, m: 1 },\n  SecurityError: { s: 'SECURITY_ERR', c: 18, m: 1 },\n  NetworkError: { s: 'NETWORK_ERR', c: 19, m: 1 },\n  AbortError: { s: 'ABORT_ERR', c: 20, m: 1 },\n  URLMismatchError: { s: 'URL_MISMATCH_ERR', c: 21, m: 1 },\n  QuotaExceededError: { s: 'QUOTA_EXCEEDED_ERR', c: 22, m: 1 },\n  TimeoutError: { s: 'TIMEOUT_ERR', c: 23, m: 1 },\n  InvalidNodeTypeError: { s: 'INVALID_NODE_TYPE_ERR', c: 24, m: 1 },\n  DataCloneError: { s: 'DATA_CLONE_ERR', c: 25, m: 1 }\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar $Error = Error;\nvar replace = uncurryThis(''.replace);\n\nvar TEST = (function (arg) { return String(new $Error(arg).stack); })('zxcasd');\n// eslint-disable-next-line redos/no-vulnerable, sonarjs/slow-regex -- safe\nvar V8_OR_CHAKRA_STACK_ENTRY = /\\n\\s*at [^:]*:[^\\n]*/;\nvar IS_V8_OR_CHAKRA_STACK = V8_OR_CHAKRA_STACK_ENTRY.test(TEST);\n\nmodule.exports = function (stack, dropEntries) {\n  if (IS_V8_OR_CHAKRA_STACK && typeof stack == 'string' && !$Error.prepareStackTrace) {\n    while (dropEntries--) stack = replace(stack, V8_OR_CHAKRA_STACK_ENTRY, '');\n  } return stack;\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) { /* empty */ }\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    isCallable(NewTarget = dummy.constructor) &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n", "'use strict';\nvar classof = require('../internals/classof');\n\nmodule.exports = function (it) {\n  var klass = classof(it);\n  return klass === 'BigInt64Array' || klass === 'BigUint64Array';\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nmodule.exports = function (argument) {\n  return isObject(argument) || argument === null;\n};\n", "'use strict';\nvar toString = require('../internals/to-string');\n\nmodule.exports = function (argument, $default) {\n  return argument === undefined ? arguments.length < 2 ? '' : $default : toString(argument);\n};\n", "'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar isCallable = require('../internals/is-callable');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar $Object = Object;\nvar ObjectPrototype = $Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\n// eslint-disable-next-line es/no-object-getprototypeof -- safe\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? $Object.getPrototypeOf : function (O) {\n  var object = toObject(O);\n  if (hasOwn(object, IE_PROTO)) return object[IE_PROTO];\n  var constructor = object.constructor;\n  if (isCallable(constructor) && object instanceof constructor) {\n    return constructor.prototype;\n  } return object instanceof $Object ? ObjectPrototype : null;\n};\n", "'use strict';\n/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar isObject = require('../internals/is-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    requireObjectCoercible(O);\n    aPossiblePrototype(proto);\n    if (!isObject(O)) return O;\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "'use strict';\nvar toPrimitive = require('../internals/to-primitive');\n\nvar $TypeError = TypeError;\n\n// `ToBigInt` abstract operation\n// https://tc39.es/ecma262/#sec-tobigint\nmodule.exports = function (argument) {\n  var prim = toPrimitive(argument, 'number');\n  if (typeof prim == 'number') throw new $TypeError(\"Can't convert number to bigint\");\n  // eslint-disable-next-line es/no-bigint -- safe\n  return BigInt(prim);\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "'use strict';\nvar classof = require('../internals/classof');\n\nvar $String = String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw new TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};\n", "'use strict';\nvar arrayWith = require('../internals/array-with');\nvar ArrayBufferViewCore = require('../internals/array-buffer-view-core');\nvar isBigIntArray = require('../internals/is-big-int-array');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toBigInt = require('../internals/to-big-int');\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar getTypedArrayConstructor = ArrayBufferViewCore.getTypedArrayConstructor;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\nvar PROPER_ORDER = function () {\n  try {\n    // eslint-disable-next-line no-throw-literal, es/no-typed-arrays, es/no-array-prototype-with -- required for testing\n    new Int8Array(1)['with'](2, { valueOf: function () { throw 8; } });\n  } catch (error) {\n    // some early implementations, like WebKit, does not follow the final semantic\n    // https://github.com/tc39/proposal-change-array-by-copy/pull/86\n    return error === 8;\n  }\n}();\n\n// Bug in WebKit. It should truncate a negative fractional index to zero, but instead throws an error\nvar THROW_ON_NEGATIVE_FRACTIONAL_INDEX = PROPER_ORDER && function () {\n  try {\n    // eslint-disable-next-line es/no-typed-arrays, es/no-array-prototype-with -- required for testing\n    new Int8Array(1)['with'](-0.5, 1);\n  } catch (error) {\n    return true;\n  }\n}();\n\n// `%TypedArray%.prototype.with` method\n// https://tc39.es/ecma262/#sec-%typedarray%.prototype.with\nexportTypedArrayMethod('with', { 'with': function (index, value) {\n  var O = aTypedArray(this);\n  var relativeIndex = toIntegerOrInfinity(index);\n  var actualValue = isBigIntArray(O) ? toBigInt(value) : +value;\n  return arrayWith(O, getTypedArrayConstructor(O), relativeIndex, actualValue);\n} }['with'], !PROPER_ORDER || THROW_ON_NEGATIVE_FRACTIONAL_INDEX);\n", "'use strict';\nvar $ = require('../internals/export');\nvar globalThis = require('../internals/global-this');\nvar getBuiltIn = require('../internals/get-built-in');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar anInstance = require('../internals/an-instance');\nvar inheritIfRequired = require('../internals/inherit-if-required');\nvar normalizeStringArgument = require('../internals/normalize-string-argument');\nvar DOMExceptionConstants = require('../internals/dom-exception-constants');\nvar clearErrorStack = require('../internals/error-stack-clear');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IS_PURE = require('../internals/is-pure');\n\nvar DOM_EXCEPTION = 'DOMException';\nvar Error = getBuiltIn('Error');\nvar NativeDOMException = getBuiltIn(DOM_EXCEPTION);\n\nvar $DOMException = function DOMException() {\n  anInstance(this, DOMExceptionPrototype);\n  var argumentsLength = arguments.length;\n  var message = normalizeStringArgument(argumentsLength < 1 ? undefined : arguments[0]);\n  var name = normalizeStringArgument(argumentsLength < 2 ? undefined : arguments[1], 'Error');\n  var that = new NativeDOMException(message, name);\n  var error = new Error(message);\n  error.name = DOM_EXCEPTION;\n  defineProperty(that, 'stack', createPropertyDescriptor(1, clearErrorStack(error.stack, 1)));\n  inheritIfRequired(that, this, $DOMException);\n  return that;\n};\n\nvar DOMExceptionPrototype = $DOMException.prototype = NativeDOMException.prototype;\n\nvar ERROR_HAS_STACK = 'stack' in new Error(DOM_EXCEPTION);\nvar DOM_EXCEPTION_HAS_STACK = 'stack' in new NativeDOMException(1, 2);\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar descriptor = NativeDOMException && DESCRIPTORS && Object.getOwnPropertyDescriptor(globalThis, DOM_EXCEPTION);\n\n// Bun ~ 0.1.1 DOMException have incorrect descriptor and we can't redefine it\n// https://github.com/Jarred-Sumner/bun/issues/399\nvar BUGGY_DESCRIPTOR = !!descriptor && !(descriptor.writable && descriptor.configurable);\n\nvar FORCED_CONSTRUCTOR = ERROR_HAS_STACK && !BUGGY_DESCRIPTOR && !DOM_EXCEPTION_HAS_STACK;\n\n// `DOMException` constructor patch for `.stack` where it's required\n// https://webidl.spec.whatwg.org/#es-DOMException-specialness\n$({ global: true, constructor: true, forced: IS_PURE || FORCED_CONSTRUCTOR }, { // TODO: fix export logic\n  DOMException: FORCED_CONSTRUCTOR ? $DOMException : NativeDOMException\n});\n\nvar PolyfilledDOMException = getBuiltIn(DOM_EXCEPTION);\nvar PolyfilledDOMExceptionPrototype = PolyfilledDOMException.prototype;\n\nif (PolyfilledDOMExceptionPrototype.constructor !== PolyfilledDOMException) {\n  if (!IS_PURE) {\n    defineProperty(PolyfilledDOMExceptionPrototype, 'constructor', createPropertyDescriptor(1, PolyfilledDOMException));\n  }\n\n  for (var key in DOMExceptionConstants) if (hasOwn(DOMExceptionConstants, key)) {\n    var constant = DOMExceptionConstants[key];\n    var constantName = constant.s;\n    if (!hasOwn(PolyfilledDOMException, constantName)) {\n      defineProperty(PolyfilledDOMException, constantName, createPropertyDescriptor(6, constant.c));\n    }\n  }\n}\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\niframe[data-v-6581b83e]{\\n    border: 0 !important;\\n}\\n.footer[data-v-6581b83e]{\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 60px;\\n    border-top: 1px #E2E2E2 solid;\\n    background-color: #FFFFFF;\\n    z-index: 1000;\\n    box-sizing: border-box;\\n    padding-top: 8px;\\n    padding-right: 15px;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: flex-end;\\n}\\n.sign-canvas[data-v-6581b83e]{\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 390px;\\n    z-index: 9999;\\n}\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=6581b83e&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"4ac5cade\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=6581b83e&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=6581b83e&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./base.vue?vue&type=template&id=6ba07e61\"\nimport script from \"./base.vue?vue&type=script&lang=js\"\nexport * from \"./base.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6ba07e61')) {\n      api.createRecord('6ba07e61', component.options)\n    } else {\n      api.reload('6ba07e61', component.options)\n    }\n    module.hot.accept(\"./base.vue?vue&type=template&id=6ba07e61\", function () {\n      api.rerender('6ba07e61', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/base.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./base.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=6581b83e&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6581b83e&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6581b83e\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\mes_mingjing_manage\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6581b83e')) {\n      api.createRecord('6581b83e', component.options)\n    } else {\n      api.reload('6581b83e', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=6581b83e&scoped=true\", function () {\n      api.rerender('6581b83e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/sign/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=6581b83e&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=6581b83e&scoped=true\""], "names": [], "sourceRoot": ""}