<?php
namespace Envsan\Modules\Common\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Common\Service\TableService;

/**
 * @skipacl
 */
class TableController extends SuperController
{
    public function fieldAction($page_id,$type = '')
    {
        $pcs = new TableService();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $pcs->saveUserColumnData($page_id);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        if ($type == 'json') {
            $this->setJsonResponse();
            $ret = $pcs->getFieldData($page_id);
            if (count($ret) == 0) {
                die('未配置该页面展示信息');
            }
            return json_encode($ret);
        }
        $this->view->page_id = $page_id;
    }

    public function savecdtAction($page_id)
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $pcs = new TableService();
            $ret = new JsonData();
            $ret->message = $pcs->saveUserConditionData($page_id);
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function conditionAction($page_id){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $pcs = new TableService();
            $ret = $pcs->getConditionData($page_id);
            return json_encode($ret);
        }
    }

}