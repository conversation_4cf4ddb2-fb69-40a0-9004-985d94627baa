{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered" style="margin-bottom: 0;padding-bottom: 0">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-6 col-lg-6">
                        <h3 class="page-title">外委出库管理</h3>
                    </div>
                    <div class="col-md-6 col-lg-6 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        {% if acl.has('purchase:wwoutstock:create') %}
                            <button type="button" class="btn yellow" onclick="create()">
                                <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                            </button>
                        {% endif %}
                        <button type="button" class="btn red" @click="excel">
                            <i class="fa fa-file-excel-o"></i>&nbsp;<span>导出excel</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        {{ partial('table') }}
    </div>
</div>
{{ partial('common_utils') }}
<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            name: ''
        },
        mounted: function() {
            let btn_list = [];
            {% if acl.has('purchase:wwoutstock:create') %}
            btn_list.push({ type: 1, name: '编辑', fn: (data) => {return data.status == 10} });
            btn_list.push({ type: 2, name: '删除', fn: (data) => {return data.status == 10} });
            // btn_list.push({ type: 3, name: '取消出库', fn: (data) => {return data.status > 15}});
            btn_list.push({ type: 5, name: '审批', fn: (data) => {return data.status == 15} });
            {% endif %}
            btn_list.push({ type: 4, name: '查看详情' });
            app_ext_table.init({{ page_id }}, btn_list, '{{ url('purchase/wwoutstock/list/json') }}');
        },
        methods: {
            search: function() {
                app_ext_table.search();
            },
            reset: function() {
                app_ext_table.reset();
            },
            excel: function() {
                app_ext_table.excel("{{ url('purchase/wwoutstock/export') }}");
            },
            getParams: function() {
                return {}
            },
            dataView: function(type, row) {
                if (type == 1) {
                    edit(row.uid);
                } else if (type == 2) {
                    del(row.uid);
                } else if (type == 3) {
                    cancel(row.uid);
                } else if (type == 4) {
                    view(row.uid);
                } else if(type == 5) {
                    approval(row.uid, 'approval')
                }
            }
        }
    });

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新增',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('purchase/wwoutstock/create') }}',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function edit(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '编辑',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('purchase/wwoutstock/edit/') }}' + uid,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }

    function view(uid) {
        top.layer.open({
            title: '查看',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('purchase/wwoutstock/view/') }}' + uid,
        });
    }

    function del(uid) {
        let dlg = top.layer.confirm('确认删除吗?', function() {
            top.layer.close(dlg);
            var url = '{{ url('purchase/wwoutstock/delete') }}';
            commonAjaxRequest(url,  {uid: uid}, function (rs) {
                toastr.success('操作完毕');
                app.search();
            })
        });
    }

    function cancel(uid) {
        var dlg = top.layer.confirm('确认取消出库吗?', function() {
            top.layer.close(dlg);
            var url = '{{ url('purchase/wwoutstock/cancel') }}';
            commonAjaxRequest(url,  {uid: uid}, function (rs) {
                toastr.success('操作完毕');
                app.search();
            })
        });
    }

    function approval(uid, type) {
        top.window.layer_result = '';
        top.layer.open({
            title: '采购到货审批',
            type: 2,
            area: ['100%', '100%'],
            content: '{{ url('purchase/wwoutstock/view/') }}' + uid + '/' + type,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    app.search();
                }
            }
        });
    }
</script>