{"version": 3, "file": "js/src_view_mingjing_check_detail_vue.9fa69c3c.js", "mappings": ";;;;;;;;;;;;;;;;AAwJA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACpbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;;ACvCA", "sources": ["webpack://sfp_ext/src/view/mingjing/check_detail.vue", "webpack://sfp_ext/./src/view/mingjing/check_detail.vue", "webpack://sfp_ext/./src/view/mingjing/check_detail.vue?c64a", "webpack://sfp_ext/./src/view/mingjing/check_detail.vue?6a20", "webpack://sfp_ext/./src/view/mingjing/check_detail.vue?9b1c", "webpack://sfp_ext/./src/view/mingjing/check_detail.vue?05c1", "webpack://sfp_ext/./src/view/mingjing/check_detail.vue?85ba", "webpack://sfp_ext/./src/view/mingjing/check_detail.vue?3410"], "sourcesContent": ["<template>\r\n    <div style=\"padding: 20px;\">\r\n        <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n                <span style=\"font-size: 24px; font-weight: bold;\">生产质检</span>\r\n                <el-button style=\"float: right;\" @click=\"$router.go(-1)\">返回</el-button>\r\n            </div>\r\n            \r\n            <div v-loading=\"loading\" element-loading-text=\"加载中...\">\r\n                <div v-if=\"!loading\">\r\n                    <!-- 基本信息 -->\r\n                    <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n                        <div slot=\"header\">\r\n                            <span style=\"font-weight: bold;\">基本信息</span>\r\n                        </div>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"8\">\r\n                                <div class=\"info-item\">\r\n                                    <span class=\"label\">入库单号:</span>\r\n                                    <span class=\"value\">{{ data.code }}</span>\r\n                                </div>\r\n                            </el-col>\r\n                            <el-col :span=\"8\">\r\n                                <div class=\"info-item\">\r\n                                    <span class=\"label\">入库日:</span>\r\n                                    <span class=\"value\">{{ data.instock_date }}</span>\r\n                                </div>\r\n                            </el-col>\r\n                            <el-col :span=\"8\">\r\n                                <div class=\"info-item\">\r\n                                    <span class=\"label\">物资编码:</span>\r\n                                    <span class=\"value\">{{ data.goods_code }}</span>\r\n                                </div>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n                            <el-col :span=\"8\">\r\n                                <div class=\"info-item\">\r\n                                    <span class=\"label\">物资名称:</span>\r\n                                    <span class=\"value\">{{ data.goods_name }}</span>\r\n                                </div>\r\n                            </el-col>\r\n                            <el-col :span=\"8\">\r\n                                <div class=\"info-item\">\r\n                                    <span class=\"label\">物资型号:</span>\r\n                                    <span class=\"value\">{{ data.goods_model }}</span>\r\n                                </div>\r\n                            </el-col>\r\n                            <el-col :span=\"8\">\r\n                                <div class=\"info-item\">\r\n                                    <span class=\"label\">入库数量:</span>\r\n                                    <span class=\"value\">{{ data.quantity }}({{ data.goods_deputy_unit }})</span>\r\n                                </div>\r\n                            </el-col>\r\n                        </el-row>\r\n                    </el-card>\r\n\r\n                    <!-- 质检项目 -->\r\n                    <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n                        <div slot=\"header\">\r\n                            <span style=\"font-weight: bold;\">质检项目</span>\r\n                        </div>\r\n                        <el-form label-width=\"120px\">\r\n                            <quality-field \r\n                                v-for=\"(item, idx) in check_data\" \r\n                                :key=\"idx\"\r\n                                :data=\"item\" \r\n                                @change=\"checkResult\"\r\n                            />\r\n                        </el-form>\r\n                    </el-card>\r\n\r\n                    <!-- 质检结果和说明 -->\r\n                    <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n                        <div slot=\"header\">\r\n                            <span style=\"font-weight: bold;\">质检结果</span>\r\n                        </div>\r\n                        <el-form label-width=\"120px\">\r\n                            <el-form-item label=\"质检结果:\">\r\n                                <el-tag v-if=\"data.check_result_flag == 0\" type=\"success\" size=\"large\">OK</el-tag>\r\n                                <el-tag v-if=\"data.check_result_flag == 1\" type=\"danger\" size=\"large\">NG</el-tag>\r\n                            </el-form-item>\r\n                            \r\n                            <el-form-item label=\"质检说明:\">\r\n                                <el-input \r\n                                    v-model=\"data.check_remarks\"\r\n                                    type=\"textarea\"\r\n                                    :rows=\"3\"\r\n                                    placeholder=\"请输入质检说明\"\r\n                                />\r\n                            </el-form-item>\r\n                            \r\n                            <el-form-item label=\"照片上传:\">\r\n                                <div style=\"display: flex; flex-wrap: wrap; gap: 10px; align-items: center;\">\r\n                                    <div \r\n                                        v-for=\"(file, i) in files\" \r\n                                        :key=\"i\"\r\n                                        style=\"position: relative; display: inline-block;\"\r\n                                    >\r\n                                        <el-image \r\n                                            :src=\"file\" \r\n                                            style=\"width: 100px; height: 100px; border-radius: 6px;\"\r\n                                            fit=\"cover\"\r\n                                            :preview-src-list=\"files\"\r\n                                        />\r\n                                        <el-button \r\n                                            type=\"danger\" \r\n                                            icon=\"el-icon-close\" \r\n                                            size=\"mini\" \r\n                                            circle\r\n                                            @click=\"delPhoto(i)\"\r\n                                            style=\"position: absolute; top: -8px; right: -8px;\"\r\n                                        />\r\n                                    </div>\r\n                                    \r\n                                    <div \r\n                                        v-if=\"files.length < 5\"\r\n                                        @click=\"takePhoto\"\r\n                                        style=\"width: 100px; height: 100px; border: 2px dashed #d9d9d9; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: border-color 0.3s;\"\r\n                                        :style=\"{'border-color': uploadHover ? '#409EFF' : '#d9d9d9'}\"\r\n                                        @mouseenter=\"uploadHover = true\"\r\n                                        @mouseleave=\"uploadHover = false\"\r\n                                    >\r\n                                        <i class=\"el-icon-plus\" style=\"font-size: 28px; color: #8c939d;\"></i>\r\n                                    </div>\r\n                                </div>\r\n                                <input\r\n                                    ref=\"fileInput\"\r\n                                    type=\"file\"\r\n                                    multiple\r\n                                    accept=\"image/*\"\r\n                                    style=\"display: none;\"\r\n                                    @change=\"handleFileChange\"\r\n                                />\r\n                            </el-form-item>\r\n                        </el-form>\r\n                    </el-card>\r\n\r\n                    <!-- 提交按钮 -->\r\n                    <div style=\"text-align: center; margin-top: 30px;\">\r\n                        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\" :loading=\"submitting\">\r\n                            <i class=\"el-icon-check\"></i>\r\n                            提交质检结果\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-card>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport QualityField from '../../components/QualityField.vue';\r\nimport qs from 'qs';\r\n\r\nexport default {\r\n    name: \"checkDetail\",\r\n    components: {\r\n        QualityField\r\n    },\r\n    data() {\r\n        return {\r\n            uid: '',\r\n            base_path: '',\r\n            loading: true,\r\n            submitting: false,\r\n            uploadHover: false,\r\n            data: {},\r\n            check_data: [],\r\n            files: [],\r\n            error_result: 0\r\n        }\r\n    },\r\n    mounted() {\r\n        this.uid = this.$route.params.uid;\r\n        this.init();\r\n    },\r\n    methods: {\r\n        init() {\r\n             // 使用qs.stringify格式发送数据\r\n             this.$http.post('/work/check/init', qs.stringify({\r\n                 uid: this.uid\r\n             }), {\r\n                 headers: {\r\n                     'Content-Type': 'application/x-www-form-urlencoded'\r\n                 }\r\n             }).then((rs) => {\r\n                 if (rs.status == 'ok') {\r\n                     this.loading = false;\r\n                     this.data = rs.data.data;\r\n                     this.files = [];\r\n                     this.check_data = rs.data.check_data;\r\n                 } else {\r\n                     this.$message.error(rs.message);\r\n                 }\r\n             }).catch(() => {\r\n                 this.$message.error('网络异常');\r\n             });\r\n         },\r\n        \r\n        checkResult(cb) {\r\n            this.data.check_result_flag = 0;\r\n            for(let check_item of this.check_data) {\r\n                check_item.result = 0;\r\n                if (check_item.type == 6) {\r\n                    for(let i = 0; i < check_item.values.length; i++) {\r\n                        check_item.results[i] = 0;\r\n                        if (check_item.values[i] == 1) {\r\n                            if (check_item.result == 0) {\r\n                                check_item.result = 1;\r\n                            }\r\n                            check_item.results[i] = 1;\r\n                        }\r\n                    }\r\n                } else if (check_item.type == 7) {\r\n                    check_item.result = 0;\r\n                    for(let i = 0; i < check_item.values.length; i++) {\r\n                        check_item.results[i] = 0;\r\n                        if (check_item.values[i] != '') {\r\n                            // 验证输入是否为有效数字\r\n                            const inputValue = parseFloat(check_item.values[i]);\r\n                            if (isNaN(inputValue)) {\r\n                                // 输入的不是有效数字，标记为错误\r\n                                check_item.results[i] = 1;\r\n                                if (check_item.result == 0) {\r\n                                    check_item.result = 1;\r\n                                }\r\n                                continue;\r\n                            }\r\n                            \r\n                            try {\r\n                                let num = Number(check_item.values[i]);\r\n                                num = Number(num.toFixed(4));\r\n                                if (isNaN(num) || !(num >= parseFloat(check_item.standard_minus) && num <= parseFloat(check_item.standard_plus))) {\r\n                                    check_item.results[i] = 1;\r\n                                }\r\n                            } catch (e) {\r\n                                check_item.results[i] = 1;\r\n                            }\r\n                            if (check_item.result == 0) {\r\n                                check_item.result = check_item.results[i];\r\n                            }\r\n                        }\r\n                    }\r\n                } else if (check_item.type == 8) {\r\n                    check_item.result = 0;\r\n                    let min = parseFloat(check_item.standard_val) - parseFloat(check_item.standard_minus);\r\n                    let max = parseFloat(check_item.standard_val) + parseFloat(check_item.standard_plus);\r\n                    for(let i = 0; i < check_item.values.length; i++) {\r\n                        check_item.results[i] = 0;\r\n                        if (check_item.values[i] != '') {\r\n                            let res = parseFloat(check_item.values[i]);\r\n                            // 验证输入是否为有效数字\r\n                            if (isNaN(res) || !(res >= min && res <= max)) {\r\n                                check_item.results[i] = 1;\r\n                            }\r\n                            if (check_item.result == 0) {\r\n                                check_item.result = check_item.results[i];\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (this.data.check_result_flag == 0) {\r\n                    this.data.check_result_flag = check_item.result;\r\n                }\r\n            }\r\n            cb();\r\n        },\r\n        \r\n        onSubmit() {\r\n            // 验证必填项\r\n            for(let item of this.check_data) {\r\n                if (item.required == 1) {\r\n                    if (item.type == 1 || item.type == 3 || item.type == 5) {\r\n                        if (item.value === '' || item.value === null || item.value === undefined) {\r\n                            this.$message.error('请选择' + item.title);\r\n                            return;\r\n                        }\r\n                    } else if (item.type == 4) {\r\n                        if (item.values.length == 0) {\r\n                            this.$message.error('请选择' + item.title);\r\n                            return;\r\n                        }\r\n                    } else if (item.type == 2 || item.type == 6 || item.type == 7 || item.type == 8) {\r\n                        for(let value of item.values) {\r\n                            if (value === '' || value === null || value === undefined) {\r\n                                this.$message.error('请输入' + item.title);\r\n                                return;\r\n                            }\r\n                            // 对于数值类型，验证输入是否为有效数字\r\n                            if (item.type == 2 || item.type == 7 || item.type == 8) {\r\n                                const numValue = parseFloat(value);\r\n                                if (isNaN(numValue)) {\r\n                                    this.$message.error(item.title + ' 必须输入有效的数字');\r\n                                    return;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            \r\n            this.submitting = true;\r\n            this.upload(this.files, [], 0, (upload_rs) => {\r\n                if (upload_rs.status == 'ok') {\r\n                     // 使用qs.stringify格式发送数据\r\n                     const postData = qs.stringify({\r\n                         uid: this.uid,\r\n                         quality_template_id: this.data.quality_template_id,\r\n                         check_result_flag: this.data.check_result_flag,\r\n                         check_remarks: this.data.check_remarks,\r\n                         check_data: encodeURI(JSON.stringify(this.check_data)),\r\n                         files: upload_rs.list // qs会自动处理数组\r\n                     });\r\n                     \r\n                     this.$http.post('work/check/save', postData, {\r\n                         headers: {\r\n                             'Content-Type': 'application/x-www-form-urlencoded'\r\n                         }\r\n                     }).then((rs) => {\r\n                         this.submitting = false;\r\n                         if (rs.status == 'ok') {\r\n                             this.$message.success('提交成功');\r\n                             // 关闭 layer 弹窗\r\n                             if (window.top && window.top.layer) {\r\n                                 window.top.layer.closeAll();\r\n                             } else {\r\n                                 // 如果不是在 layer 中打开，则使用路由返回\r\n                                 if (this.$route.params.cb) {\r\n                                     this.$route.params.cb();\r\n                                 }\r\n                                 this.$router.go(-1);\r\n                             }\r\n                         } else {\r\n                             this.$message.error(rs.message);\r\n                         }\r\n                     }).catch(() => {\r\n                         this.submitting = false;\r\n                         this.$message.error('网络异常');\r\n                     });\r\n                } else {\r\n                     this.submitting = false;\r\n                     this.$message.error('文件上传失败！');\r\n                }\r\n             });\r\n        },\r\n        \r\n        takePhoto() {\r\n            this.$refs.fileInput.click();\r\n        },\r\n        \r\n        async handleFileChange(e) {\r\n            const selectedFiles = Array.from(e.target.files);\r\n            if (!selectedFiles) return;\r\n            \r\n            for (const file of selectedFiles) {\r\n                if (!file.type.startsWith('image/')) {\r\n                    this.$message.error('仅支持图片格式');\r\n                    continue;\r\n                }\r\n                if (file.size > 10 * 1024 * 1024) {\r\n                    this.$message.error('文件大小不能超过10MB');\r\n                    continue;\r\n                }\r\n                const preview = await this.readFileAsDataURL(file);\r\n                this.files.push(preview);\r\n            }\r\n        },\r\n        \r\n        readFileAsDataURL(file) {\r\n            return new Promise((resolve, reject) => {\r\n                const reader = new FileReader();\r\n                reader.onload = () => resolve(reader.result);\r\n                reader.onerror = reject;\r\n                reader.readAsDataURL(file);\r\n            });\r\n        },\r\n        \r\n        delPhoto(i) {\r\n            this.files.splice(i, 1);\r\n        },\r\n        \r\n        upload(files, new_files, i, cb) {\r\n             if (files.length == i) {\r\n                 cb({\r\n                     status: 'ok',\r\n                     list: new_files\r\n                 });\r\n                 return;\r\n             }\r\n             \r\n             // 添加延迟确保文件名唯一性\r\n             setTimeout(() => {\r\n                 this.fileUpload(files[i], (data) => {\r\n                     if (data.status == 'ok') {\r\n                         new_files.push(data.path);\r\n                         i++;\r\n                         this.upload(files, new_files, i, cb);\r\n                     } else {\r\n                         cb(data);\r\n                     }\r\n                 });\r\n             }, i * 100); // 每个文件间隔100ms上传\r\n         },\r\n        \r\n        fileUpload(base64Data, cb) {\r\n            // 使用FormData格式发送上传数据\r\n            const formData = new FormData();\r\n            formData.append('img_base64', base64Data);\r\n            formData.append('folder_name', 'review'); // 使用review作为文件夹名\r\n            \r\n            // 添加时间戳和随机数确保文件名唯一性\r\n            const timestamp = Date.now();\r\n            const random = Math.floor(Math.random() * 10000);\r\n            formData.append('file_suffix', `_${timestamp}_${random}`);\r\n            \r\n            this.$http.post('work/common/upload', formData).then((rs) => {\r\n                if (rs.status == 'ok') {\r\n                    cb({\r\n                        status: 'ok',\r\n                        path: rs.data\r\n                    });\r\n                } else {\r\n                    cb({\r\n                        status: 'error',\r\n                        message: rs.message\r\n                    });\r\n                }\r\n            }).catch((error) => {\r\n                cb({\r\n                    status: 'error',\r\n                    message: '上传失败'\r\n                });\r\n            });\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.info-item .label {\r\n    color: #909399;\r\n    margin-right: 10px;\r\n    min-width: 80px;\r\n}\r\n\r\n.info-item .value {\r\n    color: #303133;\r\n    font-weight: 500;\r\n}\r\n\r\n.box-card {\r\n    margin-bottom: 20px;\r\n}\r\n</style> ", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding\":\"20px\"}},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-size\":\"24px\",\"font-weight\":\"bold\"}},[_vm._v(\"生产质检\")]),_c('el-button',{staticStyle:{\"float\":\"right\"},on:{\"click\":function($event){return _vm.$router.go(-1)}}},[_vm._v(\"返回\")])],1),_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"element-loading-text\":\"加载中...\"}},[(!_vm.loading)?_c('div',[_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\"}},[_vm._v(\"基本信息\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"入库单号:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.code))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"入库日:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.instock_date))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"物资编码:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.goods_code))])])])],1),_c('el-row',{staticStyle:{\"margin-top\":\"15px\"},attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"物资名称:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.goods_name))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"物资型号:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.goods_model))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"入库数量:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.data.quantity)+\"(\"+_vm._s(_vm.data.goods_deputy_unit)+\")\")])])])],1)],1),_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\"}},[_vm._v(\"质检项目\")])]),_c('el-form',{attrs:{\"label-width\":\"120px\"}},_vm._l((_vm.check_data),function(item,idx){return _c('quality-field',{key:idx,attrs:{\"data\":item},on:{\"change\":_vm.checkResult}})}),1)],1),_c('el-card',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"shadow\":\"never\"}},[_c('div',{attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-weight\":\"bold\"}},[_vm._v(\"质检结果\")])]),_c('el-form',{attrs:{\"label-width\":\"120px\"}},[_c('el-form-item',{attrs:{\"label\":\"质检结果:\"}},[(_vm.data.check_result_flag == 0)?_c('el-tag',{attrs:{\"type\":\"success\",\"size\":\"large\"}},[_vm._v(\"OK\")]):_vm._e(),(_vm.data.check_result_flag == 1)?_c('el-tag',{attrs:{\"type\":\"danger\",\"size\":\"large\"}},[_vm._v(\"NG\")]):_vm._e()],1),_c('el-form-item',{attrs:{\"label\":\"质检说明:\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入质检说明\"},model:{value:(_vm.data.check_remarks),callback:function ($$v) {_vm.$set(_vm.data, \"check_remarks\", $$v)},expression:\"data.check_remarks\"}})],1),_c('el-form-item',{attrs:{\"label\":\"照片上传:\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-wrap\":\"wrap\",\"gap\":\"10px\",\"align-items\":\"center\"}},[_vm._l((_vm.files),function(file,i){return _c('div',{key:i,staticStyle:{\"position\":\"relative\",\"display\":\"inline-block\"}},[_c('el-image',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\",\"border-radius\":\"6px\"},attrs:{\"src\":file,\"fit\":\"cover\",\"preview-src-list\":_vm.files}}),_c('el-button',{staticStyle:{\"position\":\"absolute\",\"top\":\"-8px\",\"right\":\"-8px\"},attrs:{\"type\":\"danger\",\"icon\":\"el-icon-close\",\"size\":\"mini\",\"circle\":\"\"},on:{\"click\":function($event){return _vm.delPhoto(i)}}})],1)}),(_vm.files.length < 5)?_c('div',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\",\"border\":\"2px dashed #d9d9d9\",\"border-radius\":\"6px\",\"display\":\"flex\",\"align-items\":\"center\",\"justify-content\":\"center\",\"cursor\":\"pointer\",\"transition\":\"border-color 0.3s\"},style:({'border-color': _vm.uploadHover ? '#409EFF' : '#d9d9d9'}),on:{\"click\":_vm.takePhoto,\"mouseenter\":function($event){_vm.uploadHover = true},\"mouseleave\":function($event){_vm.uploadHover = false}}},[_c('i',{staticClass:\"el-icon-plus\",staticStyle:{\"font-size\":\"28px\",\"color\":\"#8c939d\"}})]):_vm._e()],2),_c('input',{ref:\"fileInput\",staticStyle:{\"display\":\"none\"},attrs:{\"type\":\"file\",\"multiple\":\"\",\"accept\":\"image/*\"},on:{\"change\":_vm.handleFileChange}})])],1)],1),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"30px\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"loading\":_vm.submitting},on:{\"click\":_vm.onSubmit}},[_c('i',{staticClass:\"el-icon-check\"}),_vm._v(\" 提交质检结果 \")])],1)],1):_vm._e()])])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\n.info-item[data-v-ea5b084e] {\\r\\n    display: -webkit-box;\\r\\n    display: -ms-flexbox;\\r\\n    display: flex;\\r\\n    -webkit-box-align: center;\\r\\n        -ms-flex-align: center;\\r\\n            align-items: center;\\r\\n    margin-bottom: 10px;\\n}\\n.info-item .label[data-v-ea5b084e] {\\r\\n    color: #909399;\\r\\n    margin-right: 10px;\\r\\n    min-width: 80px;\\n}\\n.info-item .value[data-v-ea5b084e] {\\r\\n    color: #303133;\\r\\n    font-weight: 500;\\n}\\n.box-card[data-v-ea5b084e] {\\r\\n    margin-bottom: 20px;\\n}\\r\\n\", \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./check_detail.vue?vue&type=style&index=0&id=ea5b084e&scoped=true&lang=css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"1bd5cbac\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./check_detail.vue?vue&type=style&index=0&id=ea5b084e&scoped=true&lang=css\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./check_detail.vue?vue&type=style&index=0&id=ea5b084e&scoped=true&lang=css\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./check_detail.vue?vue&type=template&id=ea5b084e&scoped=true\"\nimport script from \"./check_detail.vue?vue&type=script&lang=js\"\nexport * from \"./check_detail.vue?vue&type=script&lang=js\"\nimport style0 from \"./check_detail.vue?vue&type=style&index=0&id=ea5b084e&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ea5b084e\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('ea5b084e')) {\n      api.createRecord('ea5b084e', component.options)\n    } else {\n      api.reload('ea5b084e', component.options)\n    }\n    module.hot.accept(\"./check_detail.vue?vue&type=template&id=ea5b084e&scoped=true\", function () {\n      api.rerender('ea5b084e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/mingjing/check_detail.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./check_detail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./check_detail.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./check_detail.vue?vue&type=style&index=0&id=ea5b084e&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./check_detail.vue?vue&type=template&id=ea5b084e&scoped=true\""], "names": [], "sourceRoot": ""}