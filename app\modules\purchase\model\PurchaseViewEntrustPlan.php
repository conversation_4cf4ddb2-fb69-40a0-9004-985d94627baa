<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
class PurchaseViewEntrustPlan extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $entrust_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $start_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $end_date;


    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $plan_cnt;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_view_entrust_plan';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewEntrustPlan[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewEntrustPlan
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
