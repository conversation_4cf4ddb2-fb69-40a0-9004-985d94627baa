<?php

namespace Envsan\Modules\Common\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Util\ConstantUtil;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Common\Service\LogsService;
use Envsan\Modules\Sys\Model\DataLogs;

/**
 * @skipacl
 */
class LogController extends SuperController
{
    public function viewAction($page_id,$uid)
    {
        $row = DataLogs::findFirst(['del_flag = 0 and page_id = ?1 and uid = ?2'
            ,'bind'=>[1 => $page_id,2 => $uid]]);
        if (empty($row)){
            die(ErrorHelper::WRONG_ID);
        }
        $ls = new LogsService();
        $this->view->logs = json_encode($ls->getDataLogs($row));
        $this->view->page_name = ConstantUtil::$page_extend_column[$page_id];
        $oss_util = new FileService();
        $this->view->base_path = $oss_util->getImagePath();
    }
}