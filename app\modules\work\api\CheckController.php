<?php

namespace Envsan\Modules\Work\Api;

use Envsan\Common\Data\JsonData;
use Envsan\Modules\Work\Service\CheckService;

/**
 * @name('原材料质检')
 */
class CheckController extends SuperController
{


    /**
     * @name('详细')
     */
    public function initAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new CheckService();
            $ret = new JsonData();
            $rtn = $s->getData();
            $ret->message = $rtn->message;
            if (empty($ret->message)) {
                $ret->data = $rtn->data;
            }
            $ret->emptyIsOk();
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * @name('列表')
     */
    public function listAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new CheckService();
            $ret = new JsonData();
            $ret->handleResult($s->getList());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * @name('提交')
     */
    public function saveAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new CheckService();
            $ret = new JsonData();
            $ret->handleResult($s->saveData());
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }
    }
}
