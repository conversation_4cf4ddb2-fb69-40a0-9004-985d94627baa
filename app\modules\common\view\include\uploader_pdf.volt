<div id="btn_select_pdf" style="display: none;">选择文件</div>
<script>
    var uploader = null;
    function initUpLoaderPdf(folder_name) {
        uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/main/lib/uploader/uploader.swf') }}',
            server: '{{ url('common/file/uploadfile/') }}' + folder_name,
            pick: '#btn_select_pdf',
            accept: {
                title: 'Pdf',
                extensions: 'pdf',
                mimeTypes: 'application/pdf'
            },
            fileSingleSizeLimit: 100 * 1024 * 1024,
            fileNumLimit: 1,
            duplicate: true
        });

        uploader.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，只支持pdf后缀的文件');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，文件大小不能超过100M');
            }
        });

        uploader.on('beforeFileQueued', function(file) {
            uploader.reset();
        });

        uploader.on('fileQueued', function(file) {
            fileQueued(file);
        });

        uploader.on('uploadSuccess', function(file, rs) {
            uploadSuccess(rs);
        });

        uploader.on('uploadError', function(file) {
            closeSpin();
            toastr.error('操作失败！');
        });
    }

    function uploadPdf() {
        $("#btn_select_pdf").find("label").click();
    }
</script>