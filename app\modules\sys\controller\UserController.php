<?php
namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Data\SessionData;
use Envsan\Modules\Sys\Model\User;
use Envsan\Modules\Sys\Service\GroupService;
use Envsan\Modules\Sys\Service\PositionService;
use Envsan\Modules\Sys\Service\RoleService;
use Envsan\Modules\Sys\Service\SessionService;
use Envsan\Modules\Sys\Service\UserService;


/**
 * @name("用户")
 */
class UserController extends SuperController
{
    /**
     * @name("列表")
     */
    public function listAction($type='')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $us = new UserService();
            $rows = $us->selectDetailAll();
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'sys:user:list'})
     */
    public function onlineAction($type='')
    {
        if($type=='json') {
            $this->setJsonResponse();
            $us = new UserService();
            $rows = $us->selectActiveUsers();
            $page = $this->getPagination($rows);
            return json_encode($page);
        }
    }

    /**
     * @acl({'link':'sys:user:list'})
     */
    public function forceOffAction()
    {
        if($this->request->isPost() ) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->status=JsonData::STATUS_OK;
            $cnt = 0;

            $ids = $this->request->getPost('ids', 'string');
            $us = new UserService();
            $rows = $us->selectActiveUsersByIds($ids);

            $ss = new SessionService();
            foreach ($rows as $row) {
                $cnt += $ss->offline($row->session_id);
                if($row->session_id!=$this->session->getId())
                    $row->delete();
            }

            $ret->message = $cnt;
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'sys:user:list'})
     */
    public function createAction()
    {
        if($this->request->isPost()){
            $this->setJsonResponse();
            $us = new UserService();
            $ret = new JsonData();
            $ret->message = $us->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $rs = new RoleService();
        $this->view->roles = $rs->selectAll()->getQuery()->execute();
        $jrow = (new User())->toArray();
        $jrow['group_name'] = '';
        $jrow['group_uid'] = '';
        $jrow['account_status'] = '0';
        $jrow['type'] = '0';
        $jrow['gender'] = '';
        $jrow['role_id'] = '';
        $jrow['position_id'] = '';
        $this->view->jsonUser = json_encode($jrow);
    }

    /**
     * @acl({'link':'sys:user:list'})
     */
    public function editAction($uid=0)
    {
        $us = new UserService();
        $row = $us->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($this->request->isPost()){
            $this->setJsonResponse();
            $us = new UserService();
            $ret = new JsonData();
            $ret->message = $us->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $rs = new RoleService();
        $this->view->roles = $rs->selectAll()->getQuery()->execute();

        $jrow = $row->toArray();
        $group = $row->getGroup();
        $jrow['group_name'] = ($group==null?'':$group->name);
        $jrow['group_uid'] = ($group==null?'':$group->uid);
        $this->view->jsonUser = json_encode($jrow);

        $this->view->user = $row;
        $this->view->pick('user/create');
    }

    /**
     * @acl({'link':'sys:user:list'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $us = new UserService();
            $ret->message = $us->deleteByUid($this->request->getPost('uid', 'string'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @acl({'link':'sys:user:list'})
     */
    public function viewAction($uid='')
    {
        $us = new UserService();
        $row = $us->selectByUid($uid);
        if ($row == null)
            die(ErrorHelper::WRONG_ID);

    }

    /**
     * @acl({'link':'sys:user:list'})
     */
    public function roleAction($act='', $uid='')
    {
        $us = new UserService();
        $row = $us->selectByUid($uid);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        if($act=='unbind'){
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $us->unbindRole($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        if($this->request->isPost()){
            $this->setJsonResponse();
            $ret = new JsonData();
            if( $us->bindRole($row, trim($this->request->getPost('ruid', 'string'))) )
                $ret->status = JsonData::STATUS_OK;
            return json_encode($ret);
        }

        $rs = new RoleService();
        $this->view->roles = $rs->selectAll()->getQuery()->execute();
        $this->view->user = $row;
        $this->view->role = $rs->selectById($row->role_id);
    }

    /**
     * @skipacl
     */
    public function accountAction()
    {
        $us = new UserService();
        $user = $us->selectDetailOne(SessionData::user()->id);
        if($user==null)
            die('用户账户不正常');

        if($this->request->isPost()){
            $this->setJsonResponse();
            $row = $us->selectById($user[0]->id);
            $ret = new JsonData();
            $ret->message = $us->saveDetail($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $this->view->jsonUser = json_encode($user[0]);
        $this->view->user = $user[0];
    }

    /**
     * @skipacl
     */
    public function changepwdAction()
    {
        if($this->request->isPost()) {
            $this->setJsonResponse();
            $us = new UserService();
            $ret = new JsonData();
            $ret->message = $us->changepwd();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    public function selMultipleAction($ids)
    {
        $gs = new GroupService();
        $this->view->jsonTree = $gs->selectTree(0,true,$ids);
        $this->view->pick('user/sel-multiple');
    }
}