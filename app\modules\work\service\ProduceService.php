<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\CommonService;
use Envsan\Modules\Equ\Model\EquItem;
use Envsan\Modules\Equ\Model\EquItemType;
use Envsan\Modules\Mes\Model\MesNotice;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesOtherLogs;
use Envsan\Modules\Mes\Model\MesPlan;
use Envsan\Modules\Mes\Model\MesProduceLogs;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Model\MesStockLogs;
use Phalcon\Mvc\User\Component;

class ProduceService extends BaseService
{
    public function getReportList(){
        $rtn = new \stdClass();
        $report_date = $this->request->getPost('report_date', 'tstring');
        $common = new \Envsan\Modules\Mes\Service\CommonService();
        $today = $common->getWorkDate();
        if (empty($report_date)){
            $report_date = $today;
        } else {
            if($report_date > $today){
                $rtn->message = '不能选择未来日期';
                return $rtn;
            }
        }
        $builder = $this->modelsManager->createBuilder()
        ->columns('
                t1.id,
                t1.uid,
                t1.bom_id,
                t1.shift_type,
                t1.notice_detail_id,
                t1.shift_type,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name,
                t5.name as bom_name,
                round(t1.cnt,4) as cnt,
                round(t1.hour,4) as hour,
                ifnull(t1.error_cnt,0) as error_cnt,
                t1.error_type,
                t1.error_remarks
            ')
        ->addFrom('Envsan\Modules\Mes\Model\MesProduceLogs', 't1')
        ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
        ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
        ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
        ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
        ->where('t1.del_flag = 0 and t1.work_date = ?1 and t1.staff_id = ?2',[1 => $report_date, 2 => SessionData::user()->id])
        ->orderBy('t1.id');
        $report_list = $builder->getQuery()->execute()->toArray();

        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.shift_type,
                round(t1.hour,4) as hour,
                t1.work_type,
                t1.produce_type,
                t1.remarks
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesOtherLogs', 't1')
            ->where('t1.del_flag = 0 and t1.work_date = ?1 and t1.staff_id = ?2',[1 => $report_date, 2 => SessionData::user()->id])
            ->orderBy('t1.id');
        $other_list = $builder->getQuery()->execute()->toArray();
        $rtn->message = '';
        $rtn->data = [
            'edit_flag' => $report_date == $today ? 1 : 0,
            'report_date' => $report_date,
            'report_list' => $report_list,
            'other_list' => $other_list
        ];
        return $rtn;
    }

    public function getData(){
        $rtn = new \stdClass();
        $code = $this->request->getPost('code', 'tstring');
        if (empty($code)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        if (mb_strlen($code) != 18){
            $rtn->message = '二维码不正确';
            return $rtn;
        }
        $yw_type = mb_substr($code,0,3);
        $notice_detail_row = null;
        $equ_row = null;
        if ($yw_type == 'pch'){
            $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$code]]);
            if (empty($notice_detail_row)){
                $rtn->message = '二维码不存在';
                return $rtn;
            }
            if ($notice_detail_row->status > 10){
                $rtn->message = '批次已生产完成，不能报工';
                return $rtn;
            }
        } else if ($yw_type == 'equ'){
            $equ_row = EquItem::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$code]]);
            if(empty($equ_row)){
                $rtn->message = '设备不存在';
                return $rtn;
            }
        } else {
            $rtn->message = '二维码不正确';
            return $rtn;
        }
        $notice_data = null;
        $plan_data = null;
        if (!empty($notice_detail_row)){
            $product_row = MesProduct::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$notice_detail_row->product_id]]);
            if (empty($product_row)){
                $rtn->message = '产品不存在';
                return $rtn;
            }
            $notice_row = MesNotice::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$notice_detail_row->notice_id]]);
            if (empty($notice_row)){
                $rtn->message = '生产通知不存在';
                return $rtn;
            }
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                    t1.id,
                    t1.name
                ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductBom', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust','t1.id = t2.product_bom_id and t2.del_flag = 0 and t2.notice_detail_id =' . $notice_detail_row->id ,'t2')
            ->where('t1.del_flag = 0 and t2.id is null and t1.product_id = ?1', [1 => $notice_detail_row->product_id])
            ->orderBy('t1.data_sort asc,t1.id asc');
            $bom_list = $builder->getQuery()->execute();
            $notice_data = [
                'notice_detail_id' => $notice_detail_row->id,
                'notice_code' => $notice_row->code,
                'product_code' => $product_row->code,
                'product_name' => $product_row->name,
                'bom_id' => '',
                'bom_name' => '',
                'bom_list' => $bom_list
            ];
        } else if (!empty($equ_row)){
            $que_type_row = EquItemType::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$equ_row->type_id]]);
            if (empty($que_type_row)){
                $rtn->message = '设备类型不存在';
                return $rtn;
            }
            $common = new \Envsan\Modules\Mes\Service\CommonService();
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                    t1.id,
                    t1.uid,
                    t1.bom_id,
                    t1.notice_detail_id,
                    t3.code as notice_code,
                    t4.code as product_code,
                    t4.name as product_name,
                    t5.name as bom_name,
                    CONCAT(t3.code, \'/\' ,t4.name ,  \'/\' ,t5.name) as name
                ')
                ->addFrom('Envsan\Modules\Mes\Model\MesPlan', 't1')
                ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
                ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
                ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
                ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
                ->where('t1.del_flag = 0 and t2.del_flag = 0 and t1.plan_date = ?1 and t1.equ_id = ?2',[1 => $common->getWorkDate() , 2 => $equ_row->id])
                ->orderBy('t1.plan_sort,t1.id');
            $plan_list = $builder->getQuery()->execute();
            $plan_data = [
                'que_id' => $equ_row->id,
                'que_code' => $equ_row->code,
                'que_name' => $equ_row->name,
                'que_type_name' => $que_type_row->name,
                'plan_id' => '',
                'notice_detail_id' => '',
                'notice_code' => '',
                'product_code' => '',
                'product_name' => '',
                'bom_id' => '',
                'bom_name' => '',
                'plan_list' => $plan_list
            ];
        } else {
            $rtn->message = '二维码不正确';
            return $rtn;
        }
        $cts = new CommonService();
        $rtn->message = '';
        $rtn->data = [
            'notice_data' => $notice_data,
            'plan_data' => $plan_data,
            'error_types' => $cts->getDictList('mes:check:errors')
        ];
        return $rtn;
    }

    public function getOtherData(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        $cts = new CommonService();
        $rtn->data = [
            'work_type' => '',
            'produce_hour' => '',
            'remarks' => '',
            'work_types' => $cts->getDictList('mes:produce:types')
        ];
        if (!empty($uid)){
            $row = MesOtherLogs::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
            if (empty($row)){
                $rtn->message = ErrorHelper::WRONG_INPUT;
                return $rtn;
            }
            $rtn->data['work_type'] = $row->produce_type;
            $rtn->data['produce_hour'] = $row->hour;
            $rtn->data['remarks'] = $row->remarks;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function saveProduce(){

        return $this->executeInTransaction(function (){
            $notice_detail_id = $this->request->getPost('notice_detail_id', 'tstring');
            $bom_id = $this->request->getPost('bom_id', 'tstring');
            // 合格数量
            $produce_cnt = $this->request->getPost('produce_cnt', 'tstring');
            // 工时
            $produce_hour = $this->request->getPost('produce_hour', 'tstring');
            // 不合格数量
            $error_cnt = $this->request->getPost('error_cnt', 'tstring');
            $error_type = $this->request->getPost('error_type', 'tstring');
            $error_remarks = $this->request->getPost('error_remarks', 'tstring');
            if (empty($notice_detail_id) || empty($bom_id) || empty($produce_cnt) || empty($produce_hour)){
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$notice_detail_id]]);
            if (empty($notice_detail_row)){
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            if ($notice_detail_row->status > 10){
                return $this->error('批次已生产完成，不能报工');
            }
            // 取得已经生产的数量
            $sql = "SELECT notice_detail_id, bom_id, SUM(cnt) as total_cnt 
                    FROM mes_produce_logs 
                    WHERE del_flag = 0 AND notice_detail_id = ? and bom_id = ?
                    GROUP BY notice_detail_id, bom_id";
            $result = $this->db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, [$notice_detail_id, $bom_id]);

            if (!empty($result)){
                $total_cnt = $result[0]['total_cnt'];
                if (CvtUtil::emptyToInt($total_cnt) + CvtUtil::emptyToInt($produce_cnt) > CvtUtil::emptyToInt($notice_detail_row->quantity)){
                    return $this->error('报工数量不能大于生产计划的剩余数量');
                }
            } else {
                if (CvtUtil::emptyToInt($produce_cnt) > CvtUtil::emptyToInt($notice_detail_row->quantity)){
                    return $this->error('报工数量不能大于生产计划数量');
                }
            }

            // TODO 排产数量的验证应该有日期的逻辑
//            $sql_plan = "SELECT notice_detail_id, bom_id , SUM(plan_cnt) as total_plan_cnt
//                    FROM mes_plan
//                    WHERE del_flag = 0 AND notice_detail_id = ? and bom_id = ? and plan_type = 1
//                    GROUP BY notice_detail_id, bom_id";
//            $result_plan = $this->db->fetchAll($sql_plan, \Phalcon\Db::FETCH_ASSOC, [$notice_detail_id, $bom_id]);
//
//            if (!empty($result)){
//                $total_plan_cnt = $result_plan[0]['total_plan_cnt'];
//                if (CvtUtil::emptyToInt($total_cnt) + CvtUtil::emptyToInt($produce_cnt) > CvtUtil::emptyToInt($total_plan_cnt)){
//                    return $this->error('报工数量不能大于排产数量');
//                }
//            }

            $bom_row = MesProductBom::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$bom_id]]);
            if (empty($bom_row)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            $common = new \Envsan\Modules\Mes\Service\CommonService();
            $work_date = $common->getWorkDate();
            $plan_row = MesPlan::findFirst(['del_flag = 0 and notice_detail_id = ?1 and bom_id = ?2 and plan_date = ?3','bind'=>[
                1 =>  $notice_detail_row->id , 2=> $bom_row->id , 3 =>$work_date
            ]]);
            $equ_id = null;
            if (!empty($plan_row)){
                $equ_id = $plan_row->equ_id;
            }

            $error_cost = $this->getCost($notice_detail_row->product_id,$bom_row->data_sort);
            $user = SessionData::user();
            $now = DateUtil::now();
            $log = new MesProduceLogs();
            $log->uid = UUID::make();
            $log->work_date = $work_date;
            $log->work_month = $common->getWorkMonth($work_date);
            $log->notice_detail_id = $notice_detail_row->id;
            $log->bom_id = $bom_row->id;
            $log->cnt = CvtUtil::emptyToInt($produce_cnt);
            $log->error_cnt = CvtUtil::blankToNull($error_cnt);
            $log->error_type = CvtUtil::blankToNull($error_type);
            $log->error_remarks = CvtUtil::blankToNull($error_remarks);
            $log->equ_id = $equ_id;
            $log->error_money = $error_cost;
            $log->cost = $bom_row->produce_cost;
            $log->one_cost = $bom_row->one_cost;
            $log->work_type = $bom_row->work_type;
            $log->hour = CvtUtil::emptyToDouble($produce_hour);
            $log->shift_type = $common->getShiftType();
            $log->staff_id = $user->id;
            $log->staff_name = $user->real_name;
            $log->staff_cost = $user->cost;
            $log->create_time = $now;
            $log->update_date = $now;
            $log->update_by = $user->id;
            $log->del_flag = 0;
            $log->owner = $user->owner;
            $log->save();
            if (empty($bom_row->nid)) {
                $stock_log = new MesStockLogs();
                $stock_log->uid = UUID::make();
                $stock_log->produce_logs_id = $log->id;
                $stock_log->notice_detail_id = $log->notice_detail_id;
                $stock_log->product_id = $notice_detail_row->product_id;
                $stock_log->work_month = $log->work_month;
                $stock_log->work_date = $log->work_date;
                $stock_log->cnt = $log->cnt;
                $stock_log->staff_name = $user->real_name;
                $stock_log->create_time = $now;
                $stock_log->update_date = $now;
                $stock_log->update_by = $user->id;
                $stock_log->del_flag = 0;
                $stock_log->owner = $user->owner;
                $stock_log->save();
            }
        });
    }

    public function saveOther(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        $produce_hour = $this->request->getPost('produce_hour', 'tstring');
        $work_type = $this->request->getPost('work_type', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        if (empty($work_type) || empty($produce_hour)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $log = null;
        $user = SessionData::user();
        $now = DateUtil::now();
        if (!empty($uid)){
            $log = MesOtherLogs::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
            if (empty($log)){
                $rtn->message = ErrorHelper::WRONG_INPUT;
                return $rtn;
            }
            $common = new \Envsan\Modules\Mes\Service\CommonService();
            $today = $common->getWorkDate();
            if ($log->staff_id != $user->id){
                $rtn->message = '没有权限';
                return $rtn;
            }
            if ($today != $log->work_date){
                $rtn->message = '只能编辑当日报工';
                return $rtn;
            }
        }
        $this->db->begin();
        try {
            $common = new \Envsan\Modules\Mes\Service\CommonService();
            $work_date = $common->getWorkDate();
            if (empty($log)){
                $log = new MesOtherLogs();
                $log->uid = UUID::make();
                $log->work_date = $work_date;
                $log->work_month = $common->getWorkMonth($work_date);
                $log->work_type = 1;
                $log->shift_type = $common->getShiftType();
                $log->staff_id = $user->id;
                $log->staff_name = $user->real_name;
                $log->staff_cost = $user->cost;
                $log->create_time = $now;
                $log->del_flag = 0;
                $log->owner = $user->owner;
            }
            $log->produce_type = $work_type;
            $log->hour = CvtUtil::emptyToDouble($produce_hour);
            $log->remarks = CvtUtil::blankToNull($remarks);
            $log->update_date = $now;
            $log->update_by = $user->id;
            if (!$log->save()){
                throw new \Exception("表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function getCost($product_id,$data_sort){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                    sum(ifnull(t1.produce_cost,0)) as cost
                ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProductBom', 't1')
            ->where('t1.del_flag = 0 and t1.product_id = ?1 and data_sort < ?2',[1 => $product_id ,2 => $data_sort]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            return 0;
        } else {
            return CvtUtil::emptyToDouble($rows[0]->cost);
        }
    }

    public function deleteOther(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = MesOtherLogs::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
        if (empty($row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $user = SessionData::user();
        $now = DateUtil::now();
        $common = new \Envsan\Modules\Mes\Service\CommonService();
        $today = $common->getWorkDate();
        if ($row->staff_id != $user->id){
            $rtn->message = '没有权限';
            return $rtn;
        }
        if ($today != $row->work_date){
            $rtn->message = '只能删除当日报工';
            return $rtn;
        }
        $this->db->begin();
        try {
            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()){
                throw new \Exception("表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function deleteReport(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = MesProduceLogs::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
        if (empty($row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $user = SessionData::user();
        $now = DateUtil::now();
        $common = new \Envsan\Modules\Mes\Service\CommonService();
        $today = $common->getWorkDate();
        if ($row->staff_id != $user->id){
            $rtn->message = '没有权限';
            return $rtn;
        }
        if ($today != $row->work_date){
            $rtn->message = '只能删除当日报工';
            return $rtn;
        }
        $this->db->begin();
        try {
            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()){
                throw new \Exception("表更新失败");
            }
            $stock_log = MesStockLogs::findFirst(['del_flag = 0 and produce_logs_id = ?1','bind'=>[1=>$row->id]]);
            if (!empty($stock_log)){
                $stock_log->del_flag = 1;
                $stock_log->update_date = $now;
                $stock_log->update_by = $user->id;
                if (!$stock_log->save()){
                    throw new \Exception("表更新失败");
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function editReport(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.bom_id,
                t1.shift_type,
                t1.notice_detail_id,
                t1.shift_type,
                t3.code as notice_code,
                t4.code as product_code,
                t4.name as product_name,
                t5.name as bom_name,
                round(t1.cnt,4) as produce_cnt,
                round(t1.hour,4) as produce_hour,
                if(t1.error_cnt = 0 , null , t1.error_cnt) as error_cnt,
                t1.error_type,
                t1.error_remarks
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduceLogs', 't1')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
            ->where('t1.del_flag = 0 and t1.uid = ?1',[1 => $uid])
            ->orderBy('t1.id');
        $rows = $builder->getQuery()->execute()->toArray();
        if (count($rows) == 0){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $cts = new CommonService();
        $rtn->message = '';
        $rtn->data = [
            'data' => $rows[0],
            'error_types' => $cts->getDictList('mes:check:errors')
        ];
        return $rtn;
    }

    public function editReportSave(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        $produce_cnt = $this->request->getPost('produce_cnt', 'tstring');
        $produce_hour = $this->request->getPost('produce_hour', 'tstring');
        $error_cnt = $this->request->getPost('error_cnt', 'tstring');
        $error_type = $this->request->getPost('error_type', 'tstring');
        $error_remarks = $this->request->getPost('error_remarks', 'tstring');
        if (empty($uid) || empty($produce_cnt) || empty($produce_hour)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = MesProduceLogs::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
        if (empty($row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $user = SessionData::user();
        $now = DateUtil::now();
        $common = new \Envsan\Modules\Mes\Service\CommonService();
        $today = $common->getWorkDate();
        if ($row->staff_id != $user->id){
            $rtn->message = '没有权限';
            return $rtn;
        }
        if ($today != $row->work_date){
            $rtn->message = '只能删除当日报工';
            return $rtn;
        }
        $this->db->begin();
        try {
            $row->cnt = CvtUtil::emptyToInt($produce_cnt);
            $row->error_cnt = CvtUtil::blankToNull($error_cnt);
            $row->error_type = CvtUtil::blankToNull($error_type);
            $row->error_remarks = CvtUtil::blankToNull($error_remarks);
            $row->hour = CvtUtil::emptyToDouble($produce_hour);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()){
                throw new \Exception("表更新失败");
            }
            $stock_log = MesStockLogs::findFirst(['del_flag = 0 and produce_logs_id = ?1','bind'=>[1=>$row->id]]);
            if (!empty($stock_log)){
                $stock_log->cnt = $row->cnt;
                $stock_log->update_date = $now;
                $stock_log->update_by = $user->id;
                if (!$stock_log->save()){
                    throw new \Exception("表更新失败");
                }
            }
            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            $rtn->message = $e->getMessage();
            return $rtn;
        }
        $rtn->message = '';
        return $rtn;
    }

    public function getViewData(){
        $rtn = new \stdClass();
        $code = $this->request->getPost('code', 'tstring');
        if (empty($code)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        if (mb_strlen($code) != 18){
            $rtn->message = '二维码不正确';
            return $rtn;
        }
        $yw_type = mb_substr($code,0,3);
        $notice_detail_row = null;
        $equ_row = null;
        if ($yw_type == 'pch'){
            $notice_detail_row = MesNoticeDetail::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$code]]);
            if (empty($notice_detail_row)){
                $rtn->message = '二维码不存在';
                return $rtn;
            }
            if ($notice_detail_row->status > 10){
                $rtn->message = '批次已生产完成，不能报工';
                return $rtn;
            }
        } else if ($yw_type == 'equ'){
            $equ_row = EquItem::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$code]]);
            if(empty($equ_row)){
                $rtn->message = '设备不存在';
                return $rtn;
            }
        } else {
            $rtn->message = '二维码不正确';
            return $rtn;
        }
        $plan_list = [];
        $bom_list = [];
        $notice_data = [];
        if (!empty($notice_detail_row)){
            $product_row = MesProduct::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$notice_detail_row->product_id]]);
            if (empty($product_row)){
                $rtn->message = '产品不存在';
                return $rtn;
            }
            $notice_row = MesNotice::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$notice_detail_row->notice_id]]);
            if (empty($notice_row)){
                $rtn->message = '生产通知不存在';
                return $rtn;
            }
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                    t1.id as notice_detail_id,
                    t1.bom_id,
                    t1.bom_name,
                    t1.data_sort,
                    t1.bom_produce_cnt,
                    t1.quantity,
                    t1.start_date,
                    t1.end_date,
                    t1.drawing_data,
                    ifnull(t1.plan_cnt,0) as plan_cnt,
                    round(ifnull(t1.produce_cnt,0),4) as produce_cnt,
                    ifnull(t1.error_cnt,0) as error_cnt,
                    ifnull(t1.error_rate,0) as error_rate,
                    if(t2.id is null , 0 , 1) as entrust_flag 
                ')
                ->addFrom('Envsan\Modules\Mes\Model\MesViewProduce', 't1')
                ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseApplyEntrust','t1.bom_id = t2.product_bom_id and t2.del_flag = 0 and t2.notice_detail_id = t1.id','t2')
                ->where('t1.id = ?1', [1 => $notice_detail_row->id])
                ->orderBy('t1.data_sort asc,t1.id asc');
            $bom_list = $builder->getQuery()->execute()->toArray();
            foreach ($bom_list as &$row){
                $sort = CvtUtil::emptyToDouble($row['data_sort']);
                if ($sort < 10){
                    $sort = '0'.$sort;
                }
                $row['start_date'] = empty($row['start_date']) ? '' : date('m-d', strtotime($row['start_date']));
                $row['end_date'] = empty($row['end_date']) ? '' : date('m-d', strtotime($row['end_date']));
                $row['bom_code'] = $notice_row->code .$sort;
                $row['drawing_data'] = CvtUtil::emptyToArray( $row['drawing_data']);
            }
            $notice_data = [
                'notice_detail_id' => $notice_detail_row->id,
                'notice_code' => $notice_row->code,
                'product_code' => $product_row->code,
                'product_name' => $product_row->name,
                'quantity' => $notice_detail_row->quantity,
                'plan_begin_date' =>  $notice_row->plan_begin_date,
                'plan_end_date' =>  $notice_row->plan_end_date
            ];
        } else if (!empty($equ_row)){
            $common = new \Envsan\Modules\Mes\Service\CommonService();
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                    t1.id,
                    t1.uid,
                    t1.bom_id,
                    t1.notice_detail_id,
                    t3.code as notice_code,
                    t4.code as product_code,
                    t4.name as product_name,
                    t5.name as bom_name,
                    t1.plan_cnt,
                    t1.plan_hour,
                    t5.drawing_data
                ')
                ->addFrom('Envsan\Modules\Mes\Model\MesPlan', 't1')
                ->leftJoin('Envsan\Modules\Mes\Model\MesNoticeDetail','t1.notice_detail_id = t2.id','t2')
                ->leftJoin('Envsan\Modules\Mes\Model\MesNotice','t2.notice_id = t3.id','t3')
                ->leftJoin('Envsan\Modules\Mes\Model\MesProduct','t2.product_id = t4.id','t4')
                ->leftJoin('Envsan\Modules\Mes\Model\MesProductBom','t1.bom_id = t5.id','t5')
                ->where('t1.del_flag = 0 and t2.del_flag = 0 and t1.plan_date = ?1 and t1.equ_id = ?2',[1 => $common->getWorkDate() , 2 => $equ_row->id])
                ->orderBy('t1.plan_sort,t1.id');
            $plan_list = $builder->getQuery()->execute()->toArray();
            foreach ($plan_list as &$plan_item){
                $plan_item['drawing_data'] = CvtUtil::emptyToArray($plan_item['drawing_data']);
            }
        } else {
            $rtn->message = '二维码不正确';
            return $rtn;
        }
        $rtn->message = '';
        $rtn->data = [
            'notice_data' => $notice_data,
            'plan_list' => $plan_list,
            'bom_list' => $bom_list
        ];
        return $rtn;
    }
}