{"version": 3, "file": "js/src_view_yike_produce_vue.8f34c9c7.js", "mappings": ";;;;;;;;;;;;;AAkPA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACvfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAkBA;AACA;;;;;;;;;;;;ACtCA", "sources": ["webpack://sfp_ext/src/view/yike/produce.vue", "webpack://sfp_ext/./src/view/yike/produce.vue", "webpack://sfp_ext/./src/view/yike/produce.vue?4fdc", "webpack://sfp_ext/./src/view/yike/produce.vue?385c", "webpack://sfp_ext/./src/view/yike/produce.vue?20ef"], "sourcesContent": ["<template>\r\n    <div style=\"padding: 15px;background-color:#F2FEFF;height: 100vh\">\r\n        <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\" style=\"display: flex;flex-direction: row;\">\r\n                <div style=\"width: 20%\">\r\n                    <span style=\"font-size: 30px;color: #409eff;font-weight: bold\" v-text=\"equ_name\"></span>\r\n                </div>\r\n                <div style=\"width: 30%\">\r\n                    <div  v-if=\"user_name != ''\" style=\"font-size: 20px;\">\r\n                        操作人： <span style=\"font-size: 30px;color: #409eff;font-weight: bold\" v-text=\"user_name\"></span>\r\n                    </div>\r\n                </div>\r\n                <div style=\"width: 50%;text-align: right\">\r\n                    <el-button v-if=\"equ_uid != ''\" style=\"font-size: 20px;margin-right: 15px\" type=\"primary\" plain @click=\"tokenShow\">设置</el-button>\r\n                    <el-button v-if=\"user_uid != '' \" style=\"font-size: 20px;margin-right: 15px\" type=\"warning\" plain @click=\"refreshData\">刷新</el-button>\r\n                    <el-button v-if=\"user_uid != ''\" style=\"font-size: 20px\" type=\"danger\" plain @click=\"logout\">退出登录</el-button>\r\n                </div>\r\n            </div>\r\n            <div style=\"height: 85vh;overflow: auto;\">\r\n                <div v-if=\"step_type == 1\">\r\n                    <div style=\"display: flex;flex-direction: row;justify-content: center;padding-top: 100px\">\r\n                        <el-input style=\"width: 300px\" v-model=\"token_key\" placeholder=\"请输入TOKEN\"></el-input>\r\n                        <el-button type=\"primary\" @click=\"setToken\">设置</el-button>\r\n                    </div>\r\n                    <div style=\"text-align: center;margin-top: 60px\">\r\n                        <el-button type=\"info\" plain style=\"width: 200px;height: 120px\" @click=\"init\">\r\n                            <div><span style=\"font-size: 30px\">关闭</span></div>\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"step_type == 2\" style=\"padding-top: 50px\">\r\n                    <div style=\"width: 500px;height: 350px;margin: auto;border: 2px solid #B2B2B2;padding: 30px;border-radius: 10px;text-align: center\">\r\n                        <i class=\"el-icon-s-custom\" style=\"font-size: 150px\"></i>\r\n                        <div>\r\n                            <span style=\"font-size: 50px\">请扫描工卡</span>\r\n                        </div>\r\n                        <div style=\"display: flex;flex-direction: row;justify-content: center;margin-top: 10px\">\r\n                            <input id=\"user_key\" type=\"text\" style=\"ime-mode: disabled;width: 200px;\" @keypress=\"handleKeypress\" class=\"form-control\" placeholder=\"请扫描工卡\" v-model=\"user_key\" autoComplete=\"off\"/>\r\n                            <el-button type=\"primary\" plain size=\"small\" @click=\"setUserFocus\">扫码</el-button>\r\n                        </div>\r\n                        <div style=\"margin-top: 10px\">\r\n                            <span style=\"font-size: 16px;color: red\">如果扫码没反应，请点击上方（扫码）按钮后再扫码</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"step_type == 3\">\r\n                    <div style=\"width: 100%;height: 62vh;padding: 15px 0;overflow: auto;\">\r\n                        <template>\r\n                            <el-table\r\n                                    :data=\"produce_list\"\r\n                                    style=\"width: 100%;font-size: 16px;\">\r\n                                <el-table-column\r\n                                        label=\"母卷号\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <span v-text=\"scope.row.base_code\" style=\"font-size: 30px;font-weight: bold\"></span>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"goods_spec\"\r\n                                        label=\"母卷规格\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"order_code\"\r\n                                        label=\"订单号\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"name\"\r\n                                        label=\"产品名称\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"spec\"\r\n                                        label=\"产品规格\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        prop=\"cnt\"\r\n                                        label=\"生产数量(卷)\">\r\n                                </el-table-column>\r\n                                <el-table-column\r\n                                        label=\"操作\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-button type=\"primary\" plain style=\"width: 150px;height: 60px\" @click=\"beginProduce(scope.row.uid)\">\r\n                                            <div><span style=\"font-size: 24px\">开始生产</span></div>\r\n                                        </el-button>\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                        </template>\r\n                        <div v-if=\"produce_list.length == 0\" style=\"text-align: center;padding-top: 20px\">\r\n                            <span style=\"font-size: 20px;\">暂时没有生产任务</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"step_type == 4\" >\r\n                    <div>\r\n                        <el-descriptions class=\"margin-top\"  :column=\"3\" border style=\"font-size: 20px\">\r\n                            <el-descriptions-item>\r\n                                <template slot=\"label\">\r\n                                    母卷号\r\n                                </template>\r\n                                <span  style=\"font-weight: bold;font-size: 30px\" v-text=\"produce_data.base_code\"></span>\r\n                            </el-descriptions-item>\r\n                            <el-descriptions-item>\r\n                                <template slot=\"label\">\r\n                                    母卷规格\r\n                                </template>\r\n                                <span  style=\"font-weight: bold;font-size: 30px\" v-text=\"produce_data.goods_spec\"></span>\r\n                            </el-descriptions-item>\r\n                            <el-descriptions-item>\r\n                                <template slot=\"label\">\r\n                                    当前进度\r\n                                </template>\r\n                                <span style=\"font-weight: bold;font-size: 30px\" v-text=\" '第' + detail_data.line_no + '刀'\"></span>\r\n                            </el-descriptions-item>\r\n                        </el-descriptions>\r\n                    </div>\r\n                    <div style=\"padding: 15px;text-align: center;font-weight: bold;font-size: 40px;\">\r\n                        <span v-text=\"detail_data.status_name\"></span>\r\n                    </div>\r\n                    <div style=\"width: 100%;display: flex;flex-wrap: wrap ;\">\r\n                        <div v-for=\"(product_item,product_idx) in detail_data.product_data\" :key=\"product_idx\"\r\n                             :style=\"{\r\n                                width: '160px',\r\n                                height: '90px',\r\n                                margin: '10px',\r\n                                border: '1px solid #D2D2D2',\r\n                                textAlign: 'center',\r\n                                backgroundColor: product_item.error_flag == 1 ? '#FFB9B9' : '#F5F5F5'\r\n                                }\"\r\n                             @click=\"errorShow(product_idx)\"\r\n                        >\r\n                            <div style=\"display: flex;flex-direction: column;justify-content: center;height: 100%\">\r\n                                <div><span style=\"font-size: 40px;font-weight: bold\" v-text=\"product_item.code_no\"></span></div>\r\n                                <div><span style=\"font-size: 20px;\" v-text=\"product_item.error_type\"></span></div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div style=\"text-align: center;margin-top: 60px\">\r\n                        <el-button v-if=\"detail_data.status == 10\" @click=\"begin_ft_show = true\" type=\"primary\" plain style=\"width: 240px;height: 80px\" >\r\n                            <div><span style=\"font-size: 30px\">分条开始</span></div>\r\n                        </el-button>\r\n                        <el-button v-if=\"detail_data.status == 20\" @click=\"finish_ft_show = true\" type=\"danger\" plain style=\"width: 240px;height: 80px\" >\r\n                            <div><span style=\"font-size: 30px\">分条完成</span></div>\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-card>\r\n        <el-dialog\r\n                title=\"确认\"\r\n                :visible.sync=\"begin_produce_show\"\r\n                width=\"30%\">\r\n            <span style=\"font-size: 20px\">\r\n                确定开始生产吗？\r\n            </span>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"begin_produce_show = false\">\r\n                    <span style=\"font-size: 20px\"> 取 消</span>\r\n                </el-button>\r\n                <el-button type=\"primary\" @click=\"beginProduceSave\">\r\n                    <span style=\"font-size: 20px\"> 确 定</span>\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\" 确定开始分条吗？\"\r\n                :visible.sync=\"begin_ft_show\"\r\n                width=\"50%\">\r\n            <div>\r\n                <div style=\"text-align: center\">\r\n                    <span style=\"font-size: 40px\">请输入损耗长度</span>\r\n                </div>\r\n                <div  style=\"text-align: center;margin-top: 15px\">\r\n                    <el-input readonly type=\"text\" v-model=\"detail_data.loss_cnt\" style=\"width:300px;font-size: 30px;color: #0080FF;font-weight: bold \">\r\n                        <template slot=\"append\">M</template>\r\n                    </el-input>\r\n                </div>\r\n                <div style=\"padding: 20px\">\r\n                    <div style=\"display: flex;flex-direction: row;border: 1px solid #f2f2f2;background-color: #fafafa;width: 100%;flex-wrap: wrap;padding: 5px\">\r\n                        <div v-for=\"(key,key_idx) in key_list\" :key=\"key_idx\" style=\"padding: 5px\">\r\n                            <el-button style=\"font-size: 30px\" type=\"info\" plain @click=\"numKeypress(key)\">{{key}}</el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n               <el-button @click=\"begin_ft_show = false\">\r\n                    <span style=\"font-size: 20px\"> 取 消</span>\r\n                </el-button>\r\n                <el-button type=\"primary\" @click=\"beginFtSave\">\r\n                    <span style=\"font-size: 20px\"> 确 定</span>\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"确定\"\r\n                :visible.sync=\"finish_ft_show\"\r\n                width=\"30%\">\r\n             <span style=\"font-size: 20px\">\r\n                确定完成分条吗？\r\n            </span>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n               <el-button @click=\"finish_ft_show = false\">\r\n                    <span style=\"font-size: 20px\"> 取 消</span>\r\n                </el-button>\r\n                <el-button type=\"primary\" @click=\"finishFtSave\">\r\n                    <span style=\"font-size: 20px\"> 确 定</span>\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\" 请选择不合格类型\"\r\n                :visible.sync=\"error_show\"\r\n                width=\"40%\">\r\n            <div style=\"font-size: 40px;font-weight: bold;text-align: center\">\r\n                <apan v-text=\"error_data.error_code\"></apan>\r\n            </div>\r\n            <div>\r\n                <div style=\"margin-top: 20px\">\r\n                    <el-radio-group v-model=\"error_data.error_type\">\r\n                        <el-radio-button label=\"\">\r\n                            <div style=\"width: 100px;height: 40px;line-height: 40px;font-size: 20px\">合格</div>\r\n                        </el-radio-button>\r\n                    </el-radio-group>\r\n                    <el-radio-group v-model=\"error_data.error_type\" fill=\"red\">\r\n                        <el-radio-button v-for=\"(error_item,error_idx) in error_types\" :key=\"error_idx\" :label=\"error_item.name\">\r\n                            <div style=\"width: 100px;height: 40px;line-height: 40px;font-size: 20px\">{{error_item.name}}</div>\r\n                        </el-radio-button>\r\n                    </el-radio-group>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n               <el-button @click=\"error_show = false\">\r\n                    <span style=\"font-size: 20px\"> 取 消</span>\r\n                </el-button>\r\n                <el-button type=\"primary\" @click=\"errorSave\">\r\n                    <span style=\"font-size: 20px\"> 确 定</span>\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\n    var socket = null;\r\n    import Config from \"../../config\";\r\n    import Global from '../../js/global';\r\n    import io from \"socket.io-client\";\r\n    export default {\r\n        name: \"yikeproudce\",\r\n        components: {\r\n\r\n        },\r\n        data() {\r\n            return {\r\n                key_list:['1','2','3','4','5','6','7','8','9','0','·','删除'],\r\n                step_type : 0,\r\n                equ_uid : '',\r\n                equ_name : '',\r\n                token_key:'',\r\n                user_key:'',\r\n                batch_key:'',\r\n                user_uid:'',\r\n                user_name : '',\r\n                begin_produce_show:false,\r\n                begin_ft_show:false,\r\n                finish_ft_show:false,\r\n                produce_uid:'',\r\n                produce_data:{},\r\n                produce_list:[],\r\n                detail_data:{},\r\n                error_show:false,\r\n                error_type:'',\r\n                error_data:{\r\n                    error_type:'',\r\n                    error_code:'',\r\n                    uid:''\r\n                },\r\n                error_types:[],\r\n            };\r\n        },\r\n        created() {\r\n            this.init();\r\n        },\r\n        methods: {\r\n            connectSocket(port_code,data_type){\r\n                if (socket){\r\n                    socket.disconnect();\r\n                }\r\n                socket = io(Config.wss);\r\n                let data_list = [];\r\n                socket.on('connect', () => {\r\n                    socket.emit('register', {port : port_code});\r\n                    socket.on('real',  (data) => {\r\n\r\n                    });\r\n                });\r\n            },\r\n            setUserFocus(){\r\n                setTimeout(() => {\r\n                    this.user_key = '';\r\n                    $('#user_key').focus();\r\n                },100);\r\n            },\r\n            setBatchFocus(){\r\n                setTimeout(() => {\r\n                    this.batch_key = '';\r\n                    $('#batch_key').focus();\r\n                },100);\r\n            },\r\n            tokenShow(){\r\n                this.step_type = 1;\r\n                this.token_key = '';\r\n            },\r\n            setToken(){\r\n                if (this.token_key == ''){\r\n                    this.$message.error('请输入TOKEN');\r\n                    return;\r\n                }\r\n                if (this.token_key.length != 32){\r\n                    this.$message.error('TOKEN长度不正确');\r\n                    return;\r\n                }\r\n                this.$http.post('mes/ipc/token', {uid:this.token_key}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        Global.setItem('equ_uid', rs.data.uid);\r\n                        Global.setItem('equ_name', rs.data.name);\r\n                        this.$message.success('设置成功');\r\n                        this.token_key = '';\r\n                        this.init();\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            init(){\r\n                this.equ_uid = Global.getItem('equ_uid');\r\n                this.equ_name = Global.getItem('equ_name');\r\n                if (this.equ_uid == '' || this.equ_uid == null){\r\n                    this.step_type = 1;\r\n                }else{\r\n                    this.step_type = 2;\r\n                    this.setUserFocus();\r\n                }\r\n            },\r\n            handleKeypress(e){\r\n                if (e.code == 'Enter'){\r\n                    if (this.step_type == 2){\r\n                        if (this.user_key.length != 32){\r\n                            this.user_key = '';\r\n                            this.$message.error('二维码格式不正确');\r\n                            return;\r\n                        }\r\n                        this.getUserData(this.user_key);\r\n                        this.user_key = '';\r\n                    }\r\n                }\r\n            },\r\n            getUserData(user_key){\r\n                this.$http.post('mes/ipc/user', {uid:user_key,equ_uid:this.equ_uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.user_uid = rs.data.user_uid;\r\n                        this.user_name = rs.data.user_name;\r\n                        this.error_types = rs.data.error_types;\r\n                        this.setData(rs.data.data);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch((e) => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            setData(data){\r\n                this.step_type = data.step;\r\n                this.produce_data = data.produce_data;\r\n                this.produce_list = data.produce_list;\r\n                this.detail_data = data.detail_data;\r\n            },\r\n            numKeypress(key){\r\n                if (key == '删除'){\r\n                    this.detail_data.loss_cnt = '';\r\n                } else {\r\n                    if (key == '·'){\r\n                        if (!(this.detail_data.loss_cnt == '' || this.detail_data.loss_cnt.indexOf('.') > -1)){\r\n                            this.detail_data.loss_cnt += '.';\r\n                        }\r\n                    } else {\r\n                        this.detail_data.loss_cnt += key + '';\r\n                    }\r\n                }\r\n            },\r\n            beginProduce(uid){\r\n                this.produce_uid = uid;\r\n                this.begin_produce_show = true;\r\n            },\r\n            beginProduceSave(){\r\n                if (this.produce_uid == ''){\r\n                    this.$message.error('请选择生产母卷');\r\n                    return;\r\n                }\r\n                this.$http.post('mes/ipc/begin',\r\n                    {\r\n                        user_uid:this.user_uid,\r\n                        equ_uid:this.equ_uid,\r\n                        uid:this.produce_uid\r\n                    }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.begin_produce_show = false;\r\n                        this.setData(rs.data);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            beginFtSave(){\r\n                if (this.detail_data.loss_cnt == ''){\r\n                    this.$message.error('请输入损耗长度');\r\n                    return;\r\n                }\r\n                this.$http.post('mes/ipc/start',\r\n                    {\r\n                        user_uid : this.user_uid,\r\n                        equ_uid : this.equ_uid,\r\n                        uid : this.detail_data.uid,\r\n                        loss_cnt : this.detail_data.loss_cnt\r\n                    }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.begin_ft_show = false;\r\n                        this.setData(rs.data);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            finishFtSave(){\r\n                this.$http.post('mes/ipc/finish',\r\n                    {\r\n                        user_uid : this.user_uid,\r\n                        equ_uid : this.equ_uid,\r\n                        uid : this.detail_data.uid\r\n                    }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.finish_ft_show = false;\r\n                        this.setData(rs.data);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            refreshData(){\r\n                this.$http.post('mes/ipc/refresh', {equ_uid:this.equ_uid}).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.setData(rs.data);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            },\r\n            logout(){\r\n                this.step_type = 2;\r\n                this.user_uid = '';\r\n                this.user_name = '';\r\n                this.setUserFocus();\r\n            },\r\n            errorShow(idx){\r\n                if (this.detail_data.status == 10){\r\n                    return;\r\n                }\r\n                this.error_data.error_code = this.detail_data.product_data[idx].code_no;\r\n                this.error_data.error_type = this.detail_data.product_data[idx].error_type == null ? '' : this.detail_data.product_data[idx].error_type;\r\n                this.error_data.uid = this.detail_data.product_data[idx].uid;\r\n                this.error_show = true;\r\n            },\r\n            errorSave(){\r\n                if (this.error_data.uid == ''){\r\n                    return;\r\n                }\r\n                this.$http.post('mes/ipc/save',\r\n                    {\r\n                        user_uid : this.user_uid,\r\n                        equ_uid : this.equ_uid,\r\n                        uid : this.error_data.uid,\r\n                        error_type : this.error_data.error_type\r\n                    }).then((rs) => {\r\n                    if (rs.status == 'ok') {\r\n                        this.error_show = false;\r\n                        this.setData(rs.data);\r\n                    } else {\r\n                        this.$message.error(rs.message);\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('未知错误');\r\n                });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style scoped>\r\n\r\n</style>\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding\":\"15px\",\"background-color\":\"#F2FEFF\",\"height\":\"100vh\"}},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\"},attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('div',{staticStyle:{\"width\":\"20%\"}},[_c('span',{staticStyle:{\"font-size\":\"30px\",\"color\":\"#409eff\",\"font-weight\":\"bold\"},domProps:{\"textContent\":_vm._s(_vm.equ_name)}})]),_c('div',{staticStyle:{\"width\":\"30%\"}},[(_vm.user_name != '')?_c('div',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 操作人： \"),_c('span',{staticStyle:{\"font-size\":\"30px\",\"color\":\"#409eff\",\"font-weight\":\"bold\"},domProps:{\"textContent\":_vm._s(_vm.user_name)}})]):_vm._e()]),_c('div',{staticStyle:{\"width\":\"50%\",\"text-align\":\"right\"}},[(_vm.equ_uid != '')?_c('el-button',{staticStyle:{\"font-size\":\"20px\",\"margin-right\":\"15px\"},attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":_vm.tokenShow}},[_vm._v(\"设置\")]):_vm._e(),(_vm.user_uid != '' )?_c('el-button',{staticStyle:{\"font-size\":\"20px\",\"margin-right\":\"15px\"},attrs:{\"type\":\"warning\",\"plain\":\"\"},on:{\"click\":_vm.refreshData}},[_vm._v(\"刷新\")]):_vm._e(),(_vm.user_uid != '')?_c('el-button',{staticStyle:{\"font-size\":\"20px\"},attrs:{\"type\":\"danger\",\"plain\":\"\"},on:{\"click\":_vm.logout}},[_vm._v(\"退出登录\")]):_vm._e()],1)]),_c('div',{staticStyle:{\"height\":\"85vh\",\"overflow\":\"auto\"}},[(_vm.step_type == 1)?_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\",\"padding-top\":\"100px\"}},[_c('el-input',{staticStyle:{\"width\":\"300px\"},attrs:{\"placeholder\":\"请输入TOKEN\"},model:{value:(_vm.token_key),callback:function ($$v) {_vm.token_key=$$v},expression:\"token_key\"}}),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.setToken}},[_vm._v(\"设置\")])],1),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"60px\"}},[_c('el-button',{staticStyle:{\"width\":\"200px\",\"height\":\"120px\"},attrs:{\"type\":\"info\",\"plain\":\"\"},on:{\"click\":_vm.init}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"关闭\")])])])],1)]):_vm._e(),(_vm.step_type == 2)?_c('div',{staticStyle:{\"padding-top\":\"50px\"}},[_c('div',{staticStyle:{\"width\":\"500px\",\"height\":\"350px\",\"margin\":\"auto\",\"border\":\"2px solid #B2B2B2\",\"padding\":\"30px\",\"border-radius\":\"10px\",\"text-align\":\"center\"}},[_c('i',{staticClass:\"el-icon-s-custom\",staticStyle:{\"font-size\":\"150px\"}}),_c('div',[_c('span',{staticStyle:{\"font-size\":\"50px\"}},[_vm._v(\"请扫描工卡\")])]),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"justify-content\":\"center\",\"margin-top\":\"10px\"}},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.user_key),expression:\"user_key\"}],staticClass:\"form-control\",staticStyle:{\"ime-mode\":\"disabled\",\"width\":\"200px\"},attrs:{\"id\":\"user_key\",\"type\":\"text\",\"placeholder\":\"请扫描工卡\",\"autoComplete\":\"off\"},domProps:{\"value\":(_vm.user_key)},on:{\"keypress\":_vm.handleKeypress,\"input\":function($event){if($event.target.composing)return;_vm.user_key=$event.target.value}}}),_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\",\"size\":\"small\"},on:{\"click\":_vm.setUserFocus}},[_vm._v(\"扫码\")])],1),_c('div',{staticStyle:{\"margin-top\":\"10px\"}},[_c('span',{staticStyle:{\"font-size\":\"16px\",\"color\":\"red\"}},[_vm._v(\"如果扫码没反应，请点击上方（扫码）按钮后再扫码\")])])])]):_vm._e(),(_vm.step_type == 3)?_c('div',[_c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"62vh\",\"padding\":\"15px 0\",\"overflow\":\"auto\"}},[[_c('el-table',{staticStyle:{\"width\":\"100%\",\"font-size\":\"16px\"},attrs:{\"data\":_vm.produce_list}},[_c('el-table-column',{attrs:{\"label\":\"母卷号\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticStyle:{\"font-size\":\"30px\",\"font-weight\":\"bold\"},domProps:{\"textContent\":_vm._s(scope.row.base_code)}})]}}],null,false,3051034155)}),_c('el-table-column',{attrs:{\"prop\":\"goods_spec\",\"label\":\"母卷规格\"}}),_c('el-table-column',{attrs:{\"prop\":\"order_code\",\"label\":\"订单号\"}}),_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"产品名称\"}}),_c('el-table-column',{attrs:{\"prop\":\"spec\",\"label\":\"产品规格\"}}),_c('el-table-column',{attrs:{\"prop\":\"cnt\",\"label\":\"生产数量(卷)\"}}),_c('el-table-column',{attrs:{\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{staticStyle:{\"width\":\"150px\",\"height\":\"60px\"},attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.beginProduce(scope.row.uid)}}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"24px\"}},[_vm._v(\"开始生产\")])])])]}}],null,false,1170310829)})],1)],(_vm.produce_list.length == 0)?_c('div',{staticStyle:{\"text-align\":\"center\",\"padding-top\":\"20px\"}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\"暂时没有生产任务\")])]):_vm._e()],2)]):_vm._e(),(_vm.step_type == 4)?_c('div',[_c('div',[_c('el-descriptions',{staticClass:\"margin-top\",staticStyle:{\"font-size\":\"20px\"},attrs:{\"column\":3,\"border\":\"\"}},[_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 母卷号 \")]),_c('span',{staticStyle:{\"font-weight\":\"bold\",\"font-size\":\"30px\"},domProps:{\"textContent\":_vm._s(_vm.produce_data.base_code)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 母卷规格 \")]),_c('span',{staticStyle:{\"font-weight\":\"bold\",\"font-size\":\"30px\"},domProps:{\"textContent\":_vm._s(_vm.produce_data.goods_spec)}})],2),_c('el-descriptions-item',[_c('template',{slot:\"label\"},[_vm._v(\" 当前进度 \")]),_c('span',{staticStyle:{\"font-weight\":\"bold\",\"font-size\":\"30px\"},domProps:{\"textContent\":_vm._s( '第' + _vm.detail_data.line_no + '刀')}})],2)],1)],1),_c('div',{staticStyle:{\"padding\":\"15px\",\"text-align\":\"center\",\"font-weight\":\"bold\",\"font-size\":\"40px\"}},[_c('span',{domProps:{\"textContent\":_vm._s(_vm.detail_data.status_name)}})]),_c('div',{staticStyle:{\"width\":\"100%\",\"display\":\"flex\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.detail_data.product_data),function(product_item,product_idx){return _c('div',{key:product_idx,style:({\n                            width: '160px',\n                            height: '90px',\n                            margin: '10px',\n                            border: '1px solid #D2D2D2',\n                            textAlign: 'center',\n                            backgroundColor: product_item.error_flag == 1 ? '#FFB9B9' : '#F5F5F5'\n                            }),on:{\"click\":function($event){return _vm.errorShow(product_idx)}}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"column\",\"justify-content\":\"center\",\"height\":\"100%\"}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"40px\",\"font-weight\":\"bold\"},domProps:{\"textContent\":_vm._s(product_item.code_no)}})]),_c('div',[_c('span',{staticStyle:{\"font-size\":\"20px\"},domProps:{\"textContent\":_vm._s(product_item.error_type)}})])])])}),0),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"60px\"}},[(_vm.detail_data.status == 10)?_c('el-button',{staticStyle:{\"width\":\"240px\",\"height\":\"80px\"},attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":function($event){_vm.begin_ft_show = true}}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"分条开始\")])])]):_vm._e(),(_vm.detail_data.status == 20)?_c('el-button',{staticStyle:{\"width\":\"240px\",\"height\":\"80px\"},attrs:{\"type\":\"danger\",\"plain\":\"\"},on:{\"click\":function($event){_vm.finish_ft_show = true}}},[_c('div',[_c('span',{staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"分条完成\")])])]):_vm._e()],1)]):_vm._e()])]),_c('el-dialog',{attrs:{\"title\":\"确认\",\"visible\":_vm.begin_produce_show,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.begin_produce_show=$event}}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 确定开始生产吗？ \")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.begin_produce_show = false}}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 取 消\")])]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.beginProduceSave}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 确 定\")])])],1)]),_c('el-dialog',{attrs:{\"title\":\" 确定开始分条吗？\",\"visible\":_vm.begin_ft_show,\"width\":\"50%\"},on:{\"update:visible\":function($event){_vm.begin_ft_show=$event}}},[_c('div',[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('span',{staticStyle:{\"font-size\":\"40px\"}},[_vm._v(\"请输入损耗长度\")])]),_c('div',{staticStyle:{\"text-align\":\"center\",\"margin-top\":\"15px\"}},[_c('el-input',{staticStyle:{\"width\":\"300px\",\"font-size\":\"30px\",\"color\":\"#0080FF\",\"font-weight\":\"bold\"},attrs:{\"readonly\":\"\",\"type\":\"text\"},model:{value:(_vm.detail_data.loss_cnt),callback:function ($$v) {_vm.$set(_vm.detail_data, \"loss_cnt\", $$v)},expression:\"detail_data.loss_cnt\"}},[_c('template',{slot:\"append\"},[_vm._v(\"M\")])],2)],1),_c('div',{staticStyle:{\"padding\":\"20px\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"row\",\"border\":\"1px solid #f2f2f2\",\"background-color\":\"#fafafa\",\"width\":\"100%\",\"flex-wrap\":\"wrap\",\"padding\":\"5px\"}},_vm._l((_vm.key_list),function(key,key_idx){return _c('div',{key:key_idx,staticStyle:{\"padding\":\"5px\"}},[_c('el-button',{staticStyle:{\"font-size\":\"30px\"},attrs:{\"type\":\"info\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.numKeypress(key)}}},[_vm._v(_vm._s(key))])],1)}),0)])]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.begin_ft_show = false}}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 取 消\")])]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.beginFtSave}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 确 定\")])])],1)]),_c('el-dialog',{attrs:{\"title\":\"确定\",\"visible\":_vm.finish_ft_show,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.finish_ft_show=$event}}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 确定完成分条吗？ \")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.finish_ft_show = false}}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 取 消\")])]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.finishFtSave}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 确 定\")])])],1)]),_c('el-dialog',{attrs:{\"title\":\" 请选择不合格类型\",\"visible\":_vm.error_show,\"width\":\"40%\"},on:{\"update:visible\":function($event){_vm.error_show=$event}}},[_c('div',{staticStyle:{\"font-size\":\"40px\",\"font-weight\":\"bold\",\"text-align\":\"center\"}},[_c('apan',{domProps:{\"textContent\":_vm._s(_vm.error_data.error_code)}})],1),_c('div',[_c('div',{staticStyle:{\"margin-top\":\"20px\"}},[_c('el-radio-group',{model:{value:(_vm.error_data.error_type),callback:function ($$v) {_vm.$set(_vm.error_data, \"error_type\", $$v)},expression:\"error_data.error_type\"}},[_c('el-radio-button',{attrs:{\"label\":\"\"}},[_c('div',{staticStyle:{\"width\":\"100px\",\"height\":\"40px\",\"line-height\":\"40px\",\"font-size\":\"20px\"}},[_vm._v(\"合格\")])])],1),_c('el-radio-group',{attrs:{\"fill\":\"red\"},model:{value:(_vm.error_data.error_type),callback:function ($$v) {_vm.$set(_vm.error_data, \"error_type\", $$v)},expression:\"error_data.error_type\"}},_vm._l((_vm.error_types),function(error_item,error_idx){return _c('el-radio-button',{key:error_idx,attrs:{\"label\":error_item.name}},[_c('div',{staticStyle:{\"width\":\"100px\",\"height\":\"40px\",\"line-height\":\"40px\",\"font-size\":\"20px\"}},[_vm._v(_vm._s(error_item.name))])])}),1)],1)]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.error_show = false}}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 取 消\")])]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.errorSave}},[_c('span',{staticStyle:{\"font-size\":\"20px\"}},[_vm._v(\" 确 定\")])])],1)])],1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./produce.vue?vue&type=template&id=7d71ae5c&scoped=true\"\nimport script from \"./produce.vue?vue&type=script&lang=js\"\nexport * from \"./produce.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7d71ae5c\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\work_project\\\\sfp_ext\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('7d71ae5c')) {\n      api.createRecord('7d71ae5c', component.options)\n    } else {\n      api.reload('7d71ae5c', component.options)\n    }\n    module.hot.accept(\"./produce.vue?vue&type=template&id=7d71ae5c&scoped=true\", function () {\n      api.rerender('7d71ae5c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/view/yike/produce.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=script&lang=js\"", "export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce.vue?vue&type=template&id=7d71ae5c&scoped=true\""], "names": [], "sourceRoot": ""}