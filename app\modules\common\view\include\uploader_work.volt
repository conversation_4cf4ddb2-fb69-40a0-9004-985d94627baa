<div id="btn_select" style="display: none;"></div>
<div id="btn_select_pdf" style="display: none;">选择文件</div>
<script>
    var uploader = null;
    function initUpLoader(url) {
        uploader = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/lib/uploader/uploader.swf') }}',
            server: url,
            pick: '#btn_select',
            accept: {
                title: 'Images',
                extensions: 'jpg,jpeg,png',
                mimeTypes: 'image/jpg,image/jpeg,image/png'
            },
            fileSingleSizeLimit: 5 * 1024 * 1024,
            compress: false,
            resize: false,
            duplicate: true
        });

        uploader.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，图片格式只支持jpg, jpeg, png');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，图片大小不能超过5M');
            }
        });

        uploader.on('filesQueued', function(files) {
            filesQueued(files);
        });

        uploader.on('uploadSuccess', function(file, rs) {
            uploadSuccess(rs);
        });

        uploader.on('uploadError', function(file, rs) {
            toastr.error('操作失败！');
        });

        uploader.on('uploadFinished', function() {
            uploadFinished();
        });
    }

    function uploadImg(formData) {
        if (formData) {
            uploader.options.formData = formData;
        }
        $("#btn_select").find("label").click();
    }

    var uploaderPdf = null;
    function initUpLoaderPdf(url) {
        uploaderPdf = WebUploader.create({
            auto: false,
            swf: '{{ static_url('static/main/lib/uploader/uploader.swf') }}',
            server: url,
            pick: '#btn_select_pdf',
            accept: {
                title: 'Pdf',
                extensions: 'pdf',
                mimeTypes: 'application/pdf'
            },
            fileSingleSizeLimit: 100 * 1024 * 1024,
            fileNumLimit: 1,
            duplicate: true
        });

        uploaderPdf.on('error', function(type) {
            if (type == "Q_TYPE_DENIED") {
                toastr.error('上传失败，只支持pdf后缀的文件');
            } else if (type == "F_EXCEED_SIZE") {
                toastr.error('上传失败，文件大小不能超过100M');
            }
        });

        uploaderPdf.on('beforeFileQueued', function(file) {
            uploaderPdf.reset();
        });

        uploaderPdf.on('fileQueued', function(file) {
            pdfFileQueued(file);
        });

        uploaderPdf.on('uploadSuccess', function(file, rs) {
            pdfUploadSuccess(rs);
        });

        uploaderPdf.on('uploadError', function(file) {
            toastr.error('操作失败');
        });

        uploaderPdf.on('uploadFinished', function() {
            pdfUploadFinished();
        });
    }

    function uploadPdf() {
        $("#btn_select_pdf").find("label").click();
    }
</script>