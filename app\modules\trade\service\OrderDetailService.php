<?php
namespace Envsan\Modules\Trade\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Mes\Model\MesProduct;
use Envsan\Modules\Mes\Model\MesProductType;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Sys\Service\SequenceService;
use Envsan\Modules\Trade\Model\TradeOrder;
use Envsan\Modules\Trade\Model\TradeOrderDetail;
use Envsan\Modules\Trade\Util\Constant;
use Phalcon\Mvc\User\Component;

class OrderDetailService extends BaseService
{
    public function selectAll($uid)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.new_flag_name,
                t1.name,
                t1.code,
                t1.cnt,
                t1.price,
                t1.price_hs,
                round(t1.tax_rate,2) * 100 as tax_rate,
                round(t1.weight,3) as weight,
                t1.ext_val,
                t1.remarks,
                t2.manager_name,
                t3.real_name as order_user_name
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrderDetail', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeOrder','t1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Sys\Model\User', 't2.create_by = t3.id', 't3')
            ->where('t1.del_flag = 0 and t2.uid = ?1', [1 => $uid])
            ->orderBy('t1.id asc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create($order_id)
    {
        $row = new TradeOrderDetail();
        $row->order_id = $order_id;
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    public function build($act, $row)
    {
        return $this->executeInTransaction(function () use ($row, $act) {
            $product_id = $this->request->getPost('product_id', 'tstring');
            $new_flag = $this->request->getPost('new_flag', 'tstring');
            $name = $this->request->getPost('name', 'tstring');
            $code = $this->request->getPost('code', 'tstring');

            $cnt = $this->request->getPost('cnt', 'tstring');
            $price = $this->request->getPost('price', 'tstring');
            $price_hs = $this->request->getPost('price_hs', 'tstring');
            // 存货代码
            $inventory_code = $this->request->getPost('inventory_code', 'tstring');
            // 库存单位
            $inventory_unit = $this->request->getPost('inventory_unit', 'tstring');
            // 销售单位
            $purchase_unit = $this->request->getPost('purchase_unit', 'tstring');
            // 是否批次管理
            // $is_batch_managed = $this->request->getPost('is_batch_managed', 'tstring');
            // 税率
            $tax_rate = $this->request->getPost('tax_rate', 'tstring');
            // 单重
            $weight = $this->request->getPost('weight', 'tstring');
            $remarks = $this->request->getPost('remarks', 'tstring');

            $files = urldecode($this->request->getPost('files', ['string', 'trim']));
            $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', ['string', 'trim'])));
            if (CheckUtil::is_empty($cnt) || CheckUtil::is_empty($price) || CheckUtil::is_empty($price_hs)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            if (!empty($tax_rate) && !CheckUtil::isIntegerBetween0And100($tax_rate)) {
                return $this->error('无效的税率');
            } else {
                $tax_rate = CvtUtil::percentToDecimal($tax_rate);
            }
            // 选择存在的产品
            if ($new_flag != '1') {
                if (CheckUtil::is_empty($product_id)) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
                $product_row = MesProduct::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $product_id]]);
                if (empty($product_row)) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
                $name = $product_row->name;
                // 存货代码
                $inventory_code = $product_row->inventory_code;
                // 库存单位
                $inventory_unit = $product_row->inventory_unit;
                // 销售单位
                $purchase_unit = $product_row->purchase_unit;
                // 是否批次管理
                // $is_batch_managed = $product_row->is_batch_managed;
                // 单重
                $weight = $product_row->weight;
                // 规格型号
                $code = $product_row->code;
                // 新品的编辑和添加
            } else {
                // start 20250604 //
                if (!empty($weight) && !CheckUtil::isDecimal($weight)) {
                    return $this->error('无效的单重');
                }
                // end 20250604 //
                if (empty($name) || empty($code)) {
                    return $this->error(ErrorHelper::WRONG_INPUT);
                }
                $check_row = MesProduct::findFirst(['del_flag = 0 and code = ?1', 'bind' => [1 => $code]]);
                if (!empty($check_row)) {
                    return $this->error('产品规格重复');
                }
                $product_id = null;
            }
            $files = CvtUtil::emptyToArray($files);
            $ext_data = CvtUtil::emptyToArray($ext_data);
            $now = DateUtil::now();
            $user = SessionData::user();

            $row->new_flag = empty($new_flag) ? 0 : 1;
            $row->new_flag_name = empty($new_flag) ? null : '新品';
            $row->product_id = $product_id;
            $row->name = $name;
            $row->code = $code;
            $row->cnt = $cnt;
            // 含税单价
            $row->price_hs = CvtUtil::emptyToDouble($price_hs);
            $row->price = CvtUtil::emptyToDouble($price);
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $table = new TableService();
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->files = CvtUtil::arrayToNull($files);
            $row->update_date = $now;
            $row->update_by = $user->id;
            // start 20250604 //
            // 存货代码
            $row->inventory_code = $inventory_code;
            // 库存单位
            $row->inventory_unit = $inventory_unit;
            // 销售单位
            $row->purchase_unit = $purchase_unit;
            // 是否批次管理
            $row->is_batch_managed = 0;
            // 税率
            $row->tax_rate = CvtUtil::emptyToDouble($tax_rate);
            // 单重
            $row->weight = CvtUtil::blankToNull($weight);
            // end 20250604 //
            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->status = 10;
                $row->status_name = Constant::$order_detail_status[$row->status];
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            $row->save();
        });
    }

    public function selectById($id)
    {
        return TradeOrderDetail::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function selectByUid($uid)
    {
        return TradeOrderDetail::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }

    public function selectByUid2($uid)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.order_id,
                t1.product_id,
                t1.new_flag,
                t1.new_flag_name,
                t1.name,
                t1.code,
                t1.cnt,
                t1.price,
                t1.status,
                t1.status_name,
                t1.ext_data,
                t1.ext_val,
                t1.files,
                t1.remarks,
                t1.update_date,
                t1.update_by,
                t1.del_flag,
                t1.owner,
                t1.inventory_code,
                t1.inventory_unit,
                t1.purchase_unit,
                t1.is_batch_managed,
                round(t1.tax_rate,2) * 100 as tax_rate,
                round(t1.weight,3) as weight
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrderDetail', 't1')
            ->where('t1.del_flag = 0 and t1.uid=?1', [1 => $uid]);
        return $builder->getQuery()->execute()->getFirst();
    }

    public function deleteByUid($uid)
    {
        $row = TradeOrderDetail::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
        if (empty($row)) {
            return ErrorHelper::WRONG_INPUT;
        }
        $order_row = TradeOrder::findFirst(['del_flag = 0 and id = ?1', 'bind' => [1 => $row->order_id]]);
        if (empty($order_row)) {
            return ErrorHelper::WRONG_INPUT;
        }
        if ($order_row->status > 10){
            return ErrorHelper::WRONG_INPUT;
        }
        $this->db->begin();
        try {
            $now = DateUtil::now();
            $user = SessionData::user();
            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【删除客户】trade_Order表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    public function selectProductList($customer_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduct', 't1')
            ->where('t1.del_flag = 0 and t1.status = 30 and t1.customer_id = ?1', [1 => $customer_id])
            ->orderBy('t1.id asc');
        return $builder->getQuery()->execute();
    }

    /**
     * 取得产品库存数量
     * @return array|string
     */
    public function getStockInfo() {
        $product_id = $this->request->getQuery('productId', 'tstring');
        if (empty($product_id)){
            return ErrorHelper::WRONG_INPUT;
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                IFNULL(FLOOR(t2.cnt), 0) as stock_cnt,
                IFNULL(t1.tax_rate, 0) * 100 as tax_rate
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduct', 't1')
            // MesViewStock:这个视图分组求产品的库存量
            ->leftJoin('Envsan\Modules\Mes\Model\MesViewStock', 't1.id = t2.product_id','t2')
            ->where('t1.del_flag = 0 and t1.id = ?1', [1 => $product_id])
            ->orderBy('t1.id asc');
        $result = $builder->getQuery()->execute()->getFirst();

        return $result ? $result->toArray() : [];

    }
}