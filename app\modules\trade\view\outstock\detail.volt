{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                     <div class="col-md-4 col-lg-2">
                         <div class="form-group">
                            <label class="col-md-4 control-label">存货编码：</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="inventory_code" v-model="inventory_code"/>
                             </div>
                         </div>
                     </div>
                     <div class="col-md-4 col-lg-2">
                         <div class="form-group">
                            <label class="col-md-4 control-label">产品名称：</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="product_name" v-model="product_name"/>
                             </div>
                         </div>
                     </div>
                     <div class="col-md-4 col-lg-2">
                         <div class="form-group">
                            <label class="col-md-4 control-label">订单号：</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="order_code" v-model="order_code"/>
                             </div>
                         </div>
                     </div>
                     <div class="col-md-4 col-lg-2">
                         <div class="form-group">
                            <label class="col-md-4 control-label">规格型号：</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="product_model" v-model="product_model"/>
                             </div>
                         </div>
                     </div>
                </div>
                <div class="row">
                    <div class="col-md-12 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="row row-summary">
            <div class="col-md-12" style="text-align: right">
                <button type="button" class="btn blue btn-outline btn-circle" style="margin-right: 15px;" onclick="add()">
                    <i class="fa fa-plus"></i>&nbsp;<span>添加</span>
                </button>
            </div>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-url="{{ url('trade/outstock/detail/json/' ~ customer_id ~ '/' ~ ids) }}"
                   data-page-size="10"
                   data-side-pagination="server"
                   data-query-params="app.params">
                <thead class="bg-blue">
                <tr>
                    <th data-checkbox="true"></th>
                    <th data-field="inventory_code">存货编码</th>
                    <th data-field="product_name">产品名称</th>
                    <th data-field="product_code">产品规格</th>
                    <th data-field="inventory_unit">主计量</th>
                    <th data-field="order_cnt">订单数量</th>
                    <th data-field="pending_cnt" data-formatter="pendingCntFormatter">待发数量</th>
                    <th data-field="stock_cnt" data-formatter="orderCntFormatter">库存数量</th>
                    <th data-field="quote_price">报价</th>
                    <th data-field="unit_price">无税单价</th>
                    <th data-field="net_amount">无税金额</th>
                    <th data-field="unit_price_hs">含税单价</th>
                    <th data-field="tax_amount">税额</th>
                    <th data-field="tax_rate">税率（%）</th>
                    <th data-field="total_amount">价税合计</th>
                    
                    <th data-field="order_code">订单号</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            inventory_code: '',
            product_name: '',
            order_code: '',
            product_model: '',
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });
    
    $table.bootstrapTable();
    function codeFormatter(v, row, index) {
        return row.apply_code ?? v;
    }

    function dateFormatter(v, row, index) {
        return row.apply_date ?? v;
    }

    function add() {
        var datas = $table.bootstrapTable('getSelections');
        if (datas.length == 0) {
            alertWarning('请选择添加明细');
            return;
        }

        top.window.layer_data2 = datas;
        top.window.layer_result2 = 'ok';
        top.layer.close(top.layer.getFrameIndex(window.name));
    }

    function orderCntFormatter(value, row, index) {
        var orderCnt = parseFloat(row.order_cnt) || 0;
        var outstock_cnt = parseFloat(row.outstock_cnt) || 0;
        
        var stockCnt = parseFloat(value) || 0;
        
        if (stockCnt < (orderCnt - outstock_cnt)) {
            return '<span style="color: red; font-weight: bold;">' + value + '</span>';
        }
        return value;
    }

    function pendingCntFormatter(value, row, index) {
        if (value <= 0) {
            return '<span class="badge badge-success">' + value + '</span>';
        } else {
            return '<span class="badge badge-warning">' + value + '</span>';
        }
    }

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>