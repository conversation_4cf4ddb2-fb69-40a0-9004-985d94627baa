{% do assets.collection('js').addJs('static/global/plugins/iCheck/icheck.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/iCheck/all.css') %}
{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-xs-6">
                        <div class="left-header">
                            <h4><b>请选择显示内容</b></h4>
                            <div class="input-group input-icon">
                                <i class="fa fa-search"></i>
                                <input type="text" class="form-control" name="search_key" v-model="search_key" placeholder="请输入筛选内容">
                                <span class="input-group-btn">
                                    <button type="button" class="btn red" @click="clearSearchKey"><i class="fa fa-times"></i></button>
                                </span>
                            </div>
                        </div>
                        <div class="tabbable-line boxless tabbable-reversed" style="border-bottom: 1px #36C6D3 SOLID;">
                            <ul class="nav nav-tabs">
                                <li :class="col.show == 1 ? 'active' : ''"  v-for="(col, col_idx) in field_list">
                                    <a style="font-size: 14px !important;min-width: 60px;text-align: center" @click="tabClick(col_idx)"  data-toggle="tab" :aria-expanded="col.show == 1 ? true : false" v-text="col.name"></a>
                                </li>
                            </ul>
                        </div>
                        <div class="left-body" style="overflow-y: auto">
                            <table v-for="(col, col_key) in field_list" v-if="col.show == 1" class="factory-table" style="width: 100%;">
                                <tbody>
                                <tr v-for="(field, idx) in col.list" v-if="field.checked == 0 && field.is_hide == 0">
                                    <td>
                                        ${field.name}
                                    </td>
                                    <td width="60px" style="text-align: center;">
                                        <a href="javascript:;" @click='addField(col_key,idx)'>
                                            <i class="fa fa-arrow-right"></i>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="right-header">
                            <h4><b>已选择显示内容</b></h4>
                            <div class="to-right">
                                <button type="button" class="btn blue btn-outline" style="margin-right: 20px;" @click="fieldSort(1)"><i class="fa fa-long-arrow-up fa-fw"></i>&nbsp;向上</button>
                                <button type="button" class="btn blue btn-outline" @click="fieldSort(2)"><i class="fa fa-long-arrow-down fa-fw"></i>&nbsp;向下</button>
                            </div>
                        </div>
                        <div class="right-body" style="overflow-y: auto ">
                            <table class="factory-table" style="width: 100%;">
                                <thead>
                                <tr style="position: sticky;top: 0;z-index: 2;">
                                    <th>选择</th>
                                    <th>序号</th>
                                    <th>名称</th>
                                    <th>合计行</th>
                                    <th>删除</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="(field_item, idx) in select_field_list">
                                    <td width="60px">
                                        <input type="radio" :id="'rdo_' + idx" name="rdo_sort" :value="idx" class="icheck icheck-rdo" >
                                    </td>
                                    <td width="40px">
                                        ${idx+1}
                                    </td>
                                    <td>
                                        ${field_item.name}
                                    </td>
                                    <td>
                                        <a v-if="field_item.type == 2" @click="selectSum(idx)">
                                            <i v-if="field_item.is_sum == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                            <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                        </a>
                                    </td>
                                    <td style="text-align: center;">
                                        <a href="javascript:;" @click='delField(idx)' style="color: red;font-size: 20px">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
            <div class="field-footer to-right">
                <button type="button" class="btn blue" @click="submit"><i class="fa fa-check"></i> 提交</button>
            </div>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            search_key: '',
            field_list: [],
            select_field_list: [],
            select_idx: -1
        },
        methods: {
            search() {
                showSpin();
                $.get('{{ url('common/table/field/'~page_id~'/json') }}', function(rs) {
                    closeSpin();
                    app.field_list = rs.field_list;
                    app.select_field_list = rs.select_field_list;
                    app.$nextTick(function() {
                        initIcheck();
                        initSize();
                    });
                });
            },
            tabClick(col_key){
                for (let key in this.field_list){
                    if (key == col_key){
                        this.field_list[key].show = 1;
                    } else {
                        this.field_list[key].show = 0;
                    }
                }
            },
            fieldSort: function(sort) {
                if (this.select_idx == -1) {
                    alertWarning('请选择要调序的行');
                    return;
                }

                let idx = this.select_idx;
                if (sort == 1) {
                    //up
                    if (idx == 0) {
                        return;
                    }
                    let field = this.select_field_list[idx];
                    this.select_field_list.splice(idx, 1);
                    this.select_field_list.splice(idx - 1, 0, field);
                    this.select_idx--;
                } else {
                    //down
                    if (idx == this.select_field_list.length - 1) {
                        return;
                    }
                    let field = this.select_field_list[idx];
                    this.select_field_list.splice(idx, 1);
                    this.select_field_list.splice(idx + 1, 0, field);
                    this.select_idx++;
                }

                $("#rdo_" + this.select_idx).iCheck('check');
            },
            addField: function(col_key,idx) {
                this.select_field_list.push(this.field_list[col_key]['list'][idx]);
                this.field_list[col_key]['list'][idx].checked = 1;

                this.$nextTick(function() {
                    initIcheck(this.select_field_list.length - 1);
                });
            },
            delField: function(idx) {
                for (let key in this.field_list){
                    for (let i = 0; i < this.field_list[key]['list'].length; i++) {
                        if (this.field_list[key]['list'][i].id == this.select_field_list[idx].id) {
                            this.field_list[key]['list'][i].checked = 0;
                            break;
                        }
                    }
                }
                if (idx == this.select_idx) {
                    $("#rdo_" + this.select_idx).iCheck('uncheck');
                }
                this.select_field_list.splice(idx, 1);
                this.$nextTick(function() {
                    initSize();
                });
            },
            selectSum:function (idx){
                if (this.select_field_list[idx].is_sum == 1){
                    this.select_field_list[idx].is_sum = 0;
                } else {
                    this.select_field_list[idx].is_sum = 1;
                }
            },
            submit: function() {
                showSpin();
                $.post('{{ url('common/table/field/'~page_id) }}', { fields: encodeURI(JSON.stringify(this.select_field_list))}, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
            clearSearchKey() {
                this.search_key = '';
            }
        },
        watch: {
            search_key(val) {
                let field_item;
                for (let key in this.field_list) {
                    for (let i = 0; i < this.field_list[key]['list'].length; i++) {
                        field_item = this.field_list[key]['list'][i];
                        if (!val || field_item.name.indexOf(val) >= 0) {
                            field_item.is_hide = 0;
                        } else {
                            field_item.is_hide = 1;
                        }
                    }
                }
            }
        }
    });

    function initIcheck(idx) {
        let $icheck_rdo;
        if (!idx) {
            $icheck_rdo = $('.icheck-rdo');
        } else {
            $icheck_rdo = $('#rdo_' + idx);
        }

        $icheck_rdo.iCheck({
            radioClass: 'iradio_square-orange'
        });
        $icheck_rdo.on('ifChanged', function() {
            if ($(this).is(':checked')) {
                app.select_idx = Number($(this).val());
            }
        });
    }

    function initSize() {
        let h = $(window).height() - 35 - 27 - $(".field-footer").outerHeight(true) - 20;

        $(".left-body").css('height', h - $(".left-header").outerHeight(true) - $(".tabbable-line").outerHeight(true));
        $(".right-body").css('height', h - $(".right-header").outerHeight(true));
    }

    $(function() {
        app.search();

        initSize();
    });
</script>
<style>
    .factory-table > thead > tr > th, .factory-table > tbody > tr > td {
        border: 1px solid #F2F2F2;
        height: 35px;
        padding-left: 5px ;
    }

    .factory-table > thead > tr > th {
        font-weight: normal;
        background-color: #3598DC;
        color: #FFFFFF;
        border-top: 0;
    }

    .left-header, .right-header {
        margin-bottom: 20px;
    }

    .left-body, .right-body {
        overflow-y: auto;
    }

    .field-footer {
        margin-top: 20px;
    }
</style>