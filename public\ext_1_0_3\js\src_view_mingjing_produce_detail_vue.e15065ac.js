(self["webpackChunksfp_ext"] = self["webpackChunksfp_ext"] || []).push([["src_view_mingjing_produce_detail_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=script&lang=js":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=script&lang=js ***!
  \************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_regenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regenerator.js */ "./node_modules/@babel/runtime/helpers/esm/regenerator.js");
/* harmony import */ var D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js */ "./node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js");
/* harmony import */ var D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "./node_modules/core-js/modules/es.array.concat.js");
/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.find.js */ "./node_modules/core-js/modules/es.array.find.js");
/* harmony import */ var core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.from.js */ "./node_modules/core-js/modules/es.array.from.js");
/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.push.js */ "./node_modules/core-js/modules/es.array.push.js");
/* harmony import */ var core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.splice.js */ "./node_modules/core-js/modules/es.array.splice.js");
/* harmony import */ var core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "./node_modules/core-js/modules/es.function.name.js");
/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.iterator.constructor.js */ "./node_modules/core-js/modules/es.iterator.constructor.js");
/* harmony import */ var core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var core_js_modules_es_iterator_find_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.iterator.find.js */ "./node_modules/core-js/modules/es.iterator.find.js");
/* harmony import */ var core_js_modules_es_iterator_find_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_iterator_find_js__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.json.stringify.js */ "./node_modules/core-js/modules/es.json.stringify.js");
/* harmony import */ var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ "./node_modules/core-js/modules/es.number.constructor.js");
/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var core_js_modules_es_number_to_fixed_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.number.to-fixed.js */ "./node_modules/core-js/modules/es.number.to-fixed.js");
/* harmony import */ var core_js_modules_es_number_to_fixed_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_to_fixed_js__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ "./node_modules/core-js/modules/es.object.keys.js");
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "./node_modules/core-js/modules/es.object.to-string.js");
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ "./node_modules/core-js/modules/es.string.iterator.js");
/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_17__);
/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ "./node_modules/core-js/modules/es.string.starts-with.js");
/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_18__);
/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "./node_modules/core-js/modules/web.dom-collections.iterator.js");
/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_19__);
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! qs */ "./node_modules/qs/lib/index.js");
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(qs__WEBPACK_IMPORTED_MODULE_21__);
/* harmony import */ var _components_QualityField_vue__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../../components/QualityField.vue */ "./src/components/QualityField.vue");






















/* harmony default export */ __webpack_exports__["default"] = ({
  name: "ProduceDetail",
  components: {
    QualityField: _components_QualityField_vue__WEBPACK_IMPORTED_MODULE_20__["default"]
  },
  data: function data() {
    return {
      loading: false,
      submitting: false,
      uploadHover: false,
      data: {},
      check_data: [],
      drawing_data: [],
      quality_list: [],
      error_result: 0,
      error_cnt: 0,
      error_types: [],
      error_type: '',
      error_remarks: '',
      selectedQualityId: '',
      worker_list: [],
      production_workers: [],
      tool_list: [],
      check_tools: [],
      files: [],
      imagePreviewVisible: false,
      previewImages: [],
      // 新增的选择相关数据
      batchOptions: [],
      batchLoading: false,
      selectedBatch: '',
      productOptions: [],
      selectedProduct: ''
    };
  },
  mounted: function mounted() {
    // this.init();  // 恢复原来的 init 方法
    // 初始加载所有批次数据用于前端筛选
    this.loadAllBatches();

    // 如果路由传递了参数，直接设置选中的批次和产品
    // const { batch_id, product_id } = this.$route.params;
    // if (batch_id && product_id) {
    //     this.selectedBatch = batch_id;
    //     this.selectedProduct = product_id;
    //     this.onBatchChange(batch_id);
    //     this.onProductChange(product_id);
    // }
  },
  methods: {
    init: function init() {
      var _this = this;
      // 恢复原来的 init 逻辑，加载基础数据
      this.$http.post('/work/quality/init', qs__WEBPACK_IMPORTED_MODULE_21___default().stringify({}), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then(function (rs) {
        if (rs.status === 'ok') {
          _this.error_types = rs.data.error_types || [];
          _this.worker_list = rs.data.worker_list || [];
          _this.tool_list = rs.data.tool_list || [];
          _this.quality_list = rs.data.quality_list || [];
        } else {
          _this.$message.error(rs.message);
        }
      }).catch(function () {
        _this.$message.error('获取基础数据失败');
      });
    },
    initWithCode: function initWithCode(code) {
      var _this2 = this;
      // 以前的 init 方法的完整逻辑，传递 code 参数
      this.$http.post('/work/quality/init', qs__WEBPACK_IMPORTED_MODULE_21___default().stringify({
        code: code
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then(function (rs) {
        if (rs.status === 'ok') {
          // 更新所有数据
          if (rs.data.data) {
            // 合并基本信息，保留已设置的值
            _this2.data = (0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_3__["default"])((0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_3__["default"])({}, _this2.data), rs.data.data);
          }
          if (rs.data.error_types) {
            _this2.error_types = rs.data.error_types;
          }
          if (rs.data.worker_list) {
            _this2.worker_list = rs.data.worker_list;
          }
          if (rs.data.tool_list) {
            _this2.tool_list = rs.data.tool_list;
          }
          if (rs.data.quality_list) {
            _this2.quality_list = rs.data.quality_list;
          }
          if (rs.data.check_data) {
            _this2.check_data = rs.data.check_data;
          }
          if (rs.data.drawing_data) {
            _this2.drawing_data = rs.data.drawing_data;
          }

          // 如果有默认的质检工艺，自动选中
          if (_this2.quality_list.length > 0 && !_this2.selectedQualityId) {
            _this2.selectedQualityId = _this2.quality_list[0].id;
            _this2.onQualityChange(_this2.selectedQualityId);
          }
        } else {
          _this2.$message.error(rs.message);
        }
      }).catch(function () {
        _this2.$message.error('获取质检数据失败');
      });
    },
    onQualityChange: function onQualityChange(qualityId) {
      var selectedQuality = this.quality_list.find(function (item) {
        return item.id === qualityId;
      });
      if (!selectedQuality) return;
      if (this.data.bom_id == selectedQuality.product_bom_id && this.data.quality_template_id == selectedQuality.id) {
        return;
      }
      this.data.bom_id = selectedQuality.product_bom_id;
      this.data.bom_name = selectedQuality.bom_name;
      this.data.quality_template_id = selectedQuality.id;
      this.data.quality_template_name = selectedQuality.quality_template_name;
      this.check_data = JSON.parse(JSON.stringify(selectedQuality.form_data));
      this.drawing_data = selectedQuality.drawing_data || [];
      this.error_result = 0;
    },
    previewImg: function previewImg() {
      this.previewImages = [];
      for (var i = 0; i < this.drawing_data.length; i++) {
        this.previewImages.push(this.drawing_data[i].url);
      }
      this.imagePreviewVisible = true;
    },
    checkResult: function checkResult(callback) {
      this.error_result = 0;
      var _iterator = (0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_2__["default"])(this.check_data),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var check_item = _step.value;
          check_item.result = 0;
          if (check_item.type == 6) {
            for (var i = 0; i < check_item.values.length; i++) {
              check_item.results[i] = 0;
              if (check_item.values[i] == 1) {
                if (check_item.result == 0) {
                  check_item.result = 1;
                }
                check_item.results[i] = 1;
              }
            }
          } else if (check_item.type == 7) {
            check_item.result = 0;
            for (var _i = 0; _i < check_item.values.length; _i++) {
              check_item.results[_i] = 0;
              if (check_item.values[_i] != '') {
                // 验证输入是否为有效数字
                var inputValue = parseFloat(check_item.values[_i]);
                if (isNaN(inputValue)) {
                  // 输入的不是有效数字，标记为错误
                  check_item.results[_i] = 1;
                  if (check_item.result == 0) {
                    check_item.result = 1;
                  }
                  continue;
                }
                var val = '';
                var _iterator2 = (0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_2__["default"])(check_item.formula_list),
                  _step2;
                try {
                  for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
                    var item = _step2.value;
                    if (item.t == 3) {
                      val += check_item.values[_i];
                    } else {
                      val += item.v;
                    }
                  }
                } catch (err) {
                  _iterator2.e(err);
                } finally {
                  _iterator2.f();
                }
                try {
                  var res = eval(val);
                  res = Number(res.toFixed(4));
                  if (isNaN(res) || !(res >= parseFloat(check_item.standard_minus) && res <= parseFloat(check_item.standard_plus))) {
                    check_item.results[_i] = 1;
                  }
                } catch (e) {
                  check_item.results[_i] = 1;
                }
                if (check_item.result == 0) {
                  check_item.result = check_item.results[_i];
                }
              }
            }
          } else if (check_item.type == 8) {
            check_item.result = 0;
            var min = parseFloat(check_item.standard_val) - parseFloat(check_item.standard_minus);
            var max = parseFloat(check_item.standard_val) + parseFloat(check_item.standard_plus);
            for (var _i2 = 0; _i2 < check_item.values.length; _i2++) {
              check_item.results[_i2] = 0;
              if (check_item.values[_i2] != '') {
                var _res = parseFloat(check_item.values[_i2]);
                // 验证输入是否为有效数字
                if (isNaN(_res) || !(_res >= min && _res <= max)) {
                  check_item.results[_i2] = 1;
                }
                if (check_item.result == 0) {
                  check_item.result = check_item.results[_i2];
                }
              }
            }
          }
          if (this.error_result == 0) {
            this.error_result = check_item.result;
          }
        }

        // 如果检验结果为OK，清空不合格信息
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      if (this.error_result == 0) {
        this.error_cnt = 0;
        this.error_type = '';
        this.error_remarks = '';
      }
      if (callback) callback();
    },
    onSubmit: function onSubmit() {
      var _this3 = this;
      // 表单验证
      var _iterator3 = (0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_2__["default"])(this.check_data),
        _step3;
      try {
        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
          var item = _step3.value;
          if (item.required == 1) {
            if (item.type == 1 || item.type == 3 || item.type == 5) {
              if (item.value == '') {
                this.$message.error('请选择' + item.title);
                return;
              }
              if (item.values.length == 0) {
                this.$message.error('请选择' + item.title);
                return;
              }
            } else if (item.type == 4) {
              if (item.values.length == 0) {
                this.$message.error('请选择' + item.title);
                return;
              }
            } else if (item.type == 2 || item.type == 7 || item.type == 8) {
              var _iterator4 = (0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_createForOfIteratorHelper_js__WEBPACK_IMPORTED_MODULE_2__["default"])(item.values),
                _step4;
              try {
                for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
                  var value = _step4.value;
                  if (value == '') {
                    this.$message.error('请输入' + item.title);
                    return;
                  }
                  // 验证输入是否为有效数字
                  var numValue = parseFloat(value);
                  if (isNaN(numValue)) {
                    this.$message.error(item.title + ' 必须输入有效的数字');
                    return;
                  }
                }
              } catch (err) {
                _iterator4.e(err);
              } finally {
                _iterator4.f();
              }
            }
          }
        }

        // 验证生产工人和测量工具必填
      } catch (err) {
        _iterator3.e(err);
      } finally {
        _iterator3.f();
      }
      if (!this.production_workers || this.production_workers.length === 0) {
        this.$message.error('请选择生产工人');
        return;
      }
      if (!this.check_tools || this.check_tools.length === 0) {
        this.$message.error('请选择测量工具');
        return;
      }
      if (this.error_result == 1) {
        if (this.error_cnt === '' || this.error_cnt === 0) {
          this.$message.error('请输入不合格数量');
          return;
        }
        if (this.error_type == '') {
          this.$message.error('请选择不合格类型');
          return;
        }
      }

      // 准备数据，数组需要特殊处理
      var postData = qs__WEBPACK_IMPORTED_MODULE_21___default().stringify({
        notice_detail_id: this.data.id,
        bom_id: this.data.bom_id,
        quality_template_id: this.data.quality_template_id,
        error_result: this.error_result,
        error_cnt: this.error_cnt,
        error_type: this.error_type,
        error_remarks: this.error_remarks,
        check_data: encodeURI(JSON.stringify(this.check_data)),
        production_workers: this.production_workers,
        // qs会自动处理数组
        check_tools: this.check_tools,
        // qs会自动处理数组
        files: [] // 暂时传空数组，如果有文件上传需要单独处理
      });
      this.submitting = true;
      this.$http.post('work/quality/save', postData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then(function (rs) {
        _this3.submitting = false;
        if (rs.status == 'ok') {
          _this3.$message.success('提交成功');
          // 关闭 layer 弹窗
          if (window.top && window.top.layer) {
            window.top.layer.closeAll();
          } else {
            // 如果不是在 layer 中打开，则使用路由返回
            _this3.$router.back();
          }
        } else {
          _this3.$message.error(rs.message);
        }
      }).catch(function () {
        _this3.submitting = false;
        _this3.$message.error('网络异常');
      });

      // this.submitting = true;
      // this.upload(this.files, [], 0, (upload_rs) => {
      //     if (upload_rs.status == 'ok') {
      //         let data = {
      //             notice_detail_id: this.data.id,
      //             bom_id: this.data.bom_id,
      //             quality_template_id: this.data.quality_template_id,
      //             error_result: this.error_result,
      //             error_cnt: this.error_cnt,
      //             error_type: this.error_type, // 如果是数组直接传数组
      //             error_remarks: this.error_remarks,
      //             check_data: encodeURI(JSON.stringify(this.check_data)), // 直接传对象/数组，不用 encodeURI/JSON.stringify
      //             production_workers: this.production_workers, // 直接传数组
      //             check_tools: this.check_tools, // 直接传数组
      //             files: upload_rs.list // 直接传图片路径数组
      //         };

      //         this.$http.post('work/quality/save', data).then((rs) => {
      //             this.submitting = false;
      //             if (rs.status == 'ok') {
      //                 this.$message.success('提交成功');
      //                 this.$router.back();
      //             } else {
      //                 this.$message.error(rs.message);
      //             }
      //         }).catch(() => {
      //             this.submitting = false;
      //             this.$message.error('网络异常');
      //         });
      //     } else {
      //         this.submitting = false;
      //         this.$message.error('文件上传失败！');
      //     }
      // });
    },
    takePhoto: function takePhoto() {
      this.$refs.fileInput.click();
    },
    handleFileChange: function handleFileChange(e) {
      var _this4 = this;
      return (0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__["default"])(/*#__PURE__*/(0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_regenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])().m(function _callee() {
        var selectedFiles, _i3, _selectedFiles, file, preview;
        return (0,D_work_project_sfp_ext_node_modules_babel_runtime_helpers_esm_regenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])().w(function (_context) {
          while (1) switch (_context.n) {
            case 0:
              selectedFiles = Array.from(e.target.files);
              if (selectedFiles) {
                _context.n = 1;
                break;
              }
              return _context.a(2);
            case 1:
              _i3 = 0, _selectedFiles = selectedFiles;
            case 2:
              if (!(_i3 < _selectedFiles.length)) {
                _context.n = 7;
                break;
              }
              file = _selectedFiles[_i3];
              if (file.type.startsWith('image/')) {
                _context.n = 3;
                break;
              }
              _this4.$message.error('仅支持图片格式');
              return _context.a(3, 6);
            case 3:
              if (!(file.size > 10 * 1024 * 1024)) {
                _context.n = 4;
                break;
              }
              _this4.$message.error('文件大小不能超过10MB');
              return _context.a(3, 6);
            case 4:
              _context.n = 5;
              return _this4.readFileAsDataURL(file);
            case 5:
              preview = _context.v;
              _this4.files.push(preview);
            case 6:
              _i3++;
              _context.n = 2;
              break;
            case 7:
              return _context.a(2);
          }
        }, _callee);
      }))();
    },
    readFileAsDataURL: function readFileAsDataURL(file) {
      return new Promise(function (resolve, reject) {
        var reader = new FileReader();
        reader.onload = function () {
          return resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    },
    delPhoto: function delPhoto(i) {
      this.files.splice(i, 1);
    },
    upload: function upload(files, new_files, i, cb) {
      var _this5 = this;
      if (files.length == i) {
        cb({
          status: 'ok',
          list: new_files
        });
        return;
      }

      // 添加延迟确保文件名唯一性
      setTimeout(function () {
        _this5.fileUpload(files[i], function (data) {
          if (data.status == 'ok') {
            new_files.push(data.path);
            i++;
            _this5.upload(files, new_files, i, cb);
          } else {
            cb(data);
          }
        });
      }, i * 100); // 每个文件间隔100ms上传
    },
    fileUpload: function fileUpload(base64Data, cb) {
      // 使用FormData格式发送上传数据
      var formData = new FormData();
      formData.append('img_base64', base64Data);
      formData.append('folder_name', 'produce'); // 使用produce作为文件夹名

      // 添加时间戳和随机数确保文件名唯一性
      var timestamp = Date.now();
      var random = Math.floor(Math.random() * 10000);
      formData.append('file_suffix', "_".concat(timestamp, "_").concat(random));
      this.$http.post('work/common/upload', formData).then(function (rs) {
        if (rs.status == 'ok') {
          cb({
            status: 'ok',
            path: rs.data
          });
        } else {
          cb({
            status: 'error',
            message: rs.message
          });
        }
      }).catch(function (error) {
        cb({
          status: 'error',
          message: '上传失败'
        });
      });
    },
    loadAllBatches: function loadAllBatches() {
      var _this6 = this;
      this.batchLoading = true;
      this.$http.post('work/quality/batch', qs__WEBPACK_IMPORTED_MODULE_21___default().stringify({
        keyword: '',
        limit: 1000 // 加载更多数据用于前端筛选
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then(function (rs) {
        _this6.batchLoading = false;
        if (rs.status === 'ok') {
          _this6.batchOptions = rs.data;
        } else {
          _this6.$message.error(rs.message);
          _this6.batchOptions = [];
        }
      }).catch(function () {
        _this6.batchLoading = false;
        _this6.$message.error('获取批次列表失败');
        _this6.batchOptions = [];
      });
    },
    onBatchChange: function onBatchChange(batchId) {
      var _this7 = this;
      this.selectedProduct = '';
      this.productOptions = [];

      // 批次改变时，清空所有质检相关数据
      this.data = {};
      this.check_data = [];
      this.drawing_data = [];
      this.quality_list = [];
      this.error_result = 0;
      this.error_cnt = 0;
      this.error_type = '';
      this.error_remarks = '';
      this.production_workers = [];
      this.check_tools = [];
      this.selectedQualityId = '';
      this.files = []; // 清空照片

      if (!batchId) return;
      this.$http.post('work/quality/products', qs__WEBPACK_IMPORTED_MODULE_21___default().stringify({
        batch_id: batchId
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then(function (rs) {
        if (rs.status === 'ok') {
          _this7.productOptions = rs.data;
          if (rs.data.length === 0) {
            _this7.$message.warning('该批次下没有可检验的产品');
          }
        } else {
          _this7.$message.error(rs.message);
        }
      }).catch(function () {
        _this7.$message.error('获取产品列表失败');
      });
    },
    onProductChange: function onProductChange(productId) {
      var _this8 = this;
      if (productId) {
        var product = this.productOptions.find(function (p) {
          return p.id === productId;
        });
        var batch = this.batchOptions.find(function (b) {
          return b.id === _this8.selectedBatch;
        });
        if (product && batch) {
          // 先清空旧的数据，避免数据混淆
          this.check_data = [];
          this.drawing_data = [];
          this.quality_list = [];
          this.error_result = 0;
          this.error_cnt = 0;
          this.error_type = '';
          this.error_remarks = '';
          this.production_workers = [];
          this.check_tools = [];
          this.selectedQualityId = '';
          this.files = []; // 清空照片

          // 设置基本信息
          this.data = {
            id: productId,
            notice_code: batch.code,
            product_name: product.name,
            product_code: product.code,
            bom_id: product.bom_id || '',
            bom_name: product.bom_name || '',
            quality_template_id: product.quality_template_id || '',
            quality_template_name: product.quality_template_name || ''
          };

          // 使用原来的 init 逻辑，传递 notice_code
          this.initWithCode(product.uid);
        }
      } else {
        this.resetProductData();
      }
    },
    resetProductData: function resetProductData() {
      this.data = {};
      this.check_data = [];
      this.drawing_data = [];
      this.quality_list = [];
      this.error_result = 0;
      this.error_cnt = 0;
      this.error_type = '';
      this.error_remarks = '';
      this.production_workers = [];
      this.check_tools = [];
      this.selectedQualityId = '';
      this.files = []; // 清空照片
    },
    resetForm: function resetForm() {
      this.selectedBatch = '';
      this.selectedProduct = '';
      this.productOptions = [];
      this.data = {};
      this.check_data = [];
      this.drawing_data = [];
      this.quality_list = [];
      this.error_result = 0;
      this.error_cnt = 0;
      this.error_type = '';
      this.error_remarks = '';
      this.production_workers = [];
      this.check_tools = [];
      this.selectedQualityId = '';
      this.files = [];
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=template&id=240baac1&scoped=true":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=template&id=240baac1&scoped=true ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "./node_modules/core-js/modules/es.function.name.js");
/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__);

var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "container"
  }, [_c('div', {
    directives: [{
      name: "loading",
      rawName: "v-loading",
      value: _vm.loading,
      expression: "loading"
    }],
    attrs: {
      "element-loading-text": "加载中..."
    }
  }, [_c('el-card', {
    staticStyle: {
      "margin-bottom": "20px"
    },
    attrs: {
      "shadow": "never"
    }
  }, [_c('div', {
    staticClass: "clearfix",
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('span', {
    staticStyle: {
      "font-weight": "bold",
      "font-size": "18px"
    }
  }, [_vm._v("产品检验")])]), _c('el-form', {
    attrs: {
      "label-width": "100px"
    }
  }, [_c('el-row', {
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "生产批次",
      "required": ""
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "placeholder": "请选择生产批次",
      "filterable": ""
    },
    on: {
      "change": _vm.onBatchChange
    },
    model: {
      value: _vm.selectedBatch,
      callback: function callback($$v) {
        _vm.selectedBatch = $$v;
      },
      expression: "selectedBatch"
    }
  }, _vm._l(_vm.batchOptions, function (batch) {
    return _c('el-option', {
      key: batch.id,
      attrs: {
        "label": batch.code,
        "value": batch.id
      }
    }, [_c('span', {
      staticStyle: {
        "float": "left"
      }
    }, [_vm._v(_vm._s(batch.code))])]);
  }), 1)], 1)], 1), _c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "产品名称",
      "required": ""
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "placeholder": "请选择产品",
      "filterable": "",
      "disabled": !_vm.selectedBatch || _vm.productOptions.length === 0
    },
    on: {
      "change": _vm.onProductChange
    },
    model: {
      value: _vm.selectedProduct,
      callback: function callback($$v) {
        _vm.selectedProduct = $$v;
      },
      expression: "selectedProduct"
    }
  }, _vm._l(_vm.productOptions, function (product) {
    return _c('el-option', {
      key: product.id,
      attrs: {
        "label": product.name + ' - ' + product.code,
        "value": product.id
      }
    }, [_c('span', {
      staticStyle: {
        "float": "left"
      }
    }, [_vm._v(_vm._s(product.name))]), _c('span', {
      staticStyle: {
        "float": "right",
        "color": "#8492a6",
        "font-size": "13px"
      }
    }, [_vm._v(_vm._s(product.code))])]);
  }), 1)], 1)], 1)], 1)], 1)], 1), _vm.selectedBatch && _vm.selectedProduct && !_vm.loading ? _c('div', [_c('el-card', {
    staticStyle: {
      "margin-bottom": "20px"
    },
    attrs: {
      "shadow": "never"
    }
  }, [_c('div', {
    staticClass: "clearfix",
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('span', {
    staticStyle: {
      "font-weight": "bold",
      "font-size": "18px"
    }
  }, [_vm._v("产品信息")])]), _c('el-row', {
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 6
    }
  }, [_c('div', {
    staticClass: "info-item"
  }, [_c('span', {
    staticClass: "label"
  }, [_vm._v("生产批次号:")]), _c('span', {
    staticClass: "value"
  }, [_vm._v(_vm._s(_vm.data.notice_code))])])]), _c('el-col', {
    attrs: {
      "span": 6
    }
  }, [_c('div', {
    staticClass: "info-item"
  }, [_c('span', {
    staticClass: "label"
  }, [_vm._v("产品名称:")]), _c('span', {
    staticClass: "value"
  }, [_vm._v(_vm._s(_vm.data.product_name))])])]), _c('el-col', {
    attrs: {
      "span": 6
    }
  }, [_c('div', {
    staticClass: "info-item"
  }, [_c('span', {
    staticClass: "label"
  }, [_vm._v("规格型号:")]), _c('span', {
    staticClass: "value"
  }, [_vm._v(_vm._s(_vm.data.product_code))])])]), _c('el-col', {
    attrs: {
      "span": 6
    }
  }, [_c('div', {
    staticClass: "info-item"
  }, [_c('span', {
    staticClass: "label"
  }, [_vm._v("质检工艺:")]), _c('span', {
    staticClass: "value"
  }, [_vm._v(_vm._s(_vm.data.bom_name))])])])], 1), _c('el-row', {
    staticStyle: {
      "margin-top": "15px"
    },
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('div', {
    staticClass: "info-item"
  }, [_c('span', {
    staticClass: "label"
  }, [_vm._v("质检项目:")]), _c('span', {
    staticClass: "value"
  }, [_vm._v(_vm._s(_vm.data.quality_template_name))])])])], 1)], 1), _c('el-card', {
    staticStyle: {
      "margin-bottom": "20px"
    },
    attrs: {
      "shadow": "never"
    }
  }, [_c('div', {
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('span', {
    staticStyle: {
      "font-weight": "bold"
    }
  }, [_vm._v("质检设置")])]), _c('el-form', {
    attrs: {
      "label-width": "100px"
    }
  }, [_c('el-row', {
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "质检工艺",
      "required": ""
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "placeholder": "选择质检工艺"
    },
    on: {
      "change": _vm.onQualityChange
    },
    model: {
      value: _vm.selectedQualityId,
      callback: function callback($$v) {
        _vm.selectedQualityId = $$v;
      },
      expression: "selectedQualityId"
    }
  }, _vm._l(_vm.quality_list, function (item) {
    return _c('el-option', {
      key: item.id,
      attrs: {
        "label": item.quality_template_name,
        "value": item.id
      }
    }, [_c('span', [_vm._v(_vm._s(item.bom_name) + " - " + _vm._s(item.quality_template_name))])]);
  }), 1)], 1)], 1)], 1), _c('el-row', {
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "生产工人",
      "required": ""
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "multiple": "",
      "placeholder": "选择生产工人"
    },
    model: {
      value: _vm.production_workers,
      callback: function callback($$v) {
        _vm.production_workers = $$v;
      },
      expression: "production_workers"
    }
  }, _vm._l(_vm.worker_list, function (worker) {
    return _c('el-option', {
      key: worker.id,
      attrs: {
        "label": worker.name,
        "value": worker.id
      }
    });
  }), 1)], 1)], 1), _c('el-col', {
    attrs: {
      "span": 12
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "测量工具",
      "required": ""
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "multiple": "",
      "placeholder": "选择测量工具"
    },
    model: {
      value: _vm.check_tools,
      callback: function callback($$v) {
        _vm.check_tools = $$v;
      },
      expression: "check_tools"
    }
  }, _vm._l(_vm.tool_list, function (tool) {
    return _c('el-option', {
      key: tool.id,
      attrs: {
        "label": tool.name,
        "value": tool.id
      }
    });
  }), 1)], 1)], 1)], 1), _vm.drawing_data.length > 0 ? _c('el-row', [_c('el-col', {
    attrs: {
      "span": 24
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "图纸查看"
    }
  }, [_c('el-button', {
    attrs: {
      "type": "text",
      "icon": "el-icon-view"
    },
    on: {
      "click": _vm.previewImg
    }
  }, [_vm._v(" 查看图纸 (" + _vm._s(_vm.drawing_data.length) + "张) ")])], 1)], 1)], 1) : _vm._e()], 1)], 1), _vm.check_data.length > 0 ? _c('el-card', {
    staticStyle: {
      "margin-bottom": "20px"
    },
    attrs: {
      "shadow": "never"
    }
  }, [_c('div', {
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('span', {
    staticStyle: {
      "font-weight": "bold"
    }
  }, [_vm._v("质检项目")])]), _c('el-form', {
    attrs: {
      "label-width": "120px"
    }
  }, _vm._l(_vm.check_data, function (item, idx) {
    return _c('quality-field', {
      key: idx,
      attrs: {
        "data": item
      },
      on: {
        "change": _vm.checkResult
      }
    });
  }), 1)], 1) : _vm._e(), _vm.data.bom_id != '' ? _c('el-card', {
    staticStyle: {
      "margin-bottom": "20px"
    },
    attrs: {
      "shadow": "never"
    }
  }, [_c('div', {
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('span', {
    staticStyle: {
      "font-weight": "bold"
    }
  }, [_vm._v("质检结果")])]), _c('el-row', {
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 8
    }
  }, [_c('div', {
    staticClass: "info-item"
  }, [_c('span', {
    staticClass: "label"
  }, [_vm._v("质检结果:")]), _vm.error_result == 0 ? _c('el-tag', {
    attrs: {
      "type": "success",
      "size": "medium"
    }
  }, [_vm._v("OK")]) : _c('el-tag', {
    attrs: {
      "type": "danger",
      "size": "medium"
    }
  }, [_vm._v("NG")])], 1)])], 1)], 1) : _vm._e(), _vm.error_result == 1 && _vm.data.bom_id != '' ? _c('el-card', {
    staticStyle: {
      "margin-bottom": "20px"
    },
    attrs: {
      "shadow": "never"
    }
  }, [_c('div', {
    attrs: {
      "slot": "header"
    },
    slot: "header"
  }, [_c('span', {
    staticStyle: {
      "font-weight": "bold",
      "color": "#F56C6C"
    }
  }, [_vm._v("不合格信息")])]), _c('el-form', {
    attrs: {
      "label-width": "120px"
    }
  }, [_c('el-row', {
    attrs: {
      "gutter": 20
    }
  }, [_c('el-col', {
    attrs: {
      "span": 8
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "不合格数量",
      "required": ""
    }
  }, [_c('el-input-number', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "min": 0,
      "placeholder": "请输入不合格数量"
    },
    model: {
      value: _vm.error_cnt,
      callback: function callback($$v) {
        _vm.error_cnt = $$v;
      },
      expression: "error_cnt"
    }
  }), _c('span', {
    staticStyle: {
      "margin-left": "10px",
      "color": "#909399"
    }
  }, [_vm._v("件")])], 1)], 1), _c('el-col', {
    attrs: {
      "span": 8
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "不合格类型",
      "required": ""
    }
  }, [_c('el-select', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "placeholder": "选择不合格类型"
    },
    model: {
      value: _vm.error_type,
      callback: function callback($$v) {
        _vm.error_type = $$v;
      },
      expression: "error_type"
    }
  }, _vm._l(_vm.error_types, function (type) {
    return _c('el-option', {
      key: type.name,
      attrs: {
        "label": type.name,
        "value": type.name
      }
    });
  }), 1)], 1)], 1)], 1), _c('el-row', [_c('el-col', {
    attrs: {
      "span": 16
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "不合格原因"
    }
  }, [_c('el-input', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "type": "textarea",
      "rows": 3,
      "placeholder": "请输入不合格原因"
    },
    model: {
      value: _vm.error_remarks,
      callback: function callback($$v) {
        _vm.error_remarks = $$v;
      },
      expression: "error_remarks"
    }
  })], 1)], 1)], 1)], 1)], 1) : _vm._e(), _c('div', {
    staticClass: "footer-actions"
  }, [_c('el-button', {
    attrs: {
      "size": "large"
    },
    on: {
      "click": _vm.resetForm
    }
  }, [_vm._v("重新选择")]), _c('el-button', {
    attrs: {
      "type": "primary",
      "size": "large",
      "loading": _vm.submitting
    },
    on: {
      "click": _vm.onSubmit
    }
  }, [_vm._v(" 提交检验结果 ")])], 1)], 1) : _vm._e(), !_vm.selectedBatch || !_vm.selectedProduct ? _c('div', {
    staticStyle: {
      "text-align": "center",
      "padding": "80px 20px"
    }
  }, [_c('i', {
    staticClass: "el-icon-s-check",
    staticStyle: {
      "font-size": "100px",
      "color": "#C0C4CC"
    }
  }), _c('div', {
    staticStyle: {
      "font-size": "18px",
      "color": "#909399",
      "margin-top": "20px"
    }
  }, [_vm._v(" 请先选择生产批次和产品 ")])]) : _vm._e()], 1), _c('el-dialog', {
    attrs: {
      "title": "图纸预览",
      "visible": _vm.imagePreviewVisible,
      "width": "80%"
    },
    on: {
      "update:visible": function updateVisible($event) {
        _vm.imagePreviewVisible = $event;
      }
    }
  }, [_c('div', {
    staticStyle: {
      "text-align": "center"
    }
  }, _vm._l(_vm.previewImages, function (img, index) {
    return _c('img', {
      key: index,
      staticStyle: {
        "max-width": "100%",
        "margin-bottom": "20px",
        "border": "1px solid #ddd"
      },
      attrs: {
        "src": img
      }
    });
  }), 0)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/core-js/modules/es.array.find.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.find.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

"use strict";

var $ = __webpack_require__(/*! ../internals/export */ "./node_modules/core-js/internals/export.js");
var $find = (__webpack_require__(/*! ../internals/array-iteration */ "./node_modules/core-js/internals/array-iteration.js").find);
var addToUnscopables = __webpack_require__(/*! ../internals/add-to-unscopables */ "./node_modules/core-js/internals/add-to-unscopables.js");

var FIND = 'find';
var SKIPS_HOLES = true;

// Shouldn't skip holes
// eslint-disable-next-line es/no-array-prototype-find -- testing
if (FIND in []) Array(1)[FIND](function () { SKIPS_HOLES = false; });

// `Array.prototype.find` method
// https://tc39.es/ecma262/#sec-array.prototype.find
$({ target: 'Array', proto: true, forced: SKIPS_HOLES }, {
  find: function find(callbackfn /* , that = undefined */) {
    return $find(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);
  }
});

// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables
addToUnscopables(FIND);


/***/ }),

/***/ "./node_modules/core-js/modules/es.iterator.find.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.iterator.find.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

"use strict";

var $ = __webpack_require__(/*! ../internals/export */ "./node_modules/core-js/internals/export.js");
var call = __webpack_require__(/*! ../internals/function-call */ "./node_modules/core-js/internals/function-call.js");
var iterate = __webpack_require__(/*! ../internals/iterate */ "./node_modules/core-js/internals/iterate.js");
var aCallable = __webpack_require__(/*! ../internals/a-callable */ "./node_modules/core-js/internals/a-callable.js");
var anObject = __webpack_require__(/*! ../internals/an-object */ "./node_modules/core-js/internals/an-object.js");
var getIteratorDirect = __webpack_require__(/*! ../internals/get-iterator-direct */ "./node_modules/core-js/internals/get-iterator-direct.js");
var iteratorClose = __webpack_require__(/*! ../internals/iterator-close */ "./node_modules/core-js/internals/iterator-close.js");
var iteratorHelperWithoutClosingOnEarlyError = __webpack_require__(/*! ../internals/iterator-helper-without-closing-on-early-error */ "./node_modules/core-js/internals/iterator-helper-without-closing-on-early-error.js");

var findWithoutClosingOnEarlyError = iteratorHelperWithoutClosingOnEarlyError('find', TypeError);

// `Iterator.prototype.find` method
// https://tc39.es/ecma262/#sec-iterator.prototype.find
$({ target: 'Iterator', proto: true, real: true, forced: findWithoutClosingOnEarlyError }, {
  find: function find(predicate) {
    anObject(this);
    try {
      aCallable(predicate);
    } catch (error) {
      iteratorClose(this, 'throw', error);
    }

    if (findWithoutClosingOnEarlyError) return call(findWithoutClosingOnEarlyError, this, predicate);

    var record = getIteratorDirect(this);
    var counter = 0;
    return iterate(record, function (value, stop) {
      if (predicate(value, counter++)) return stop(value);
    }, { IS_RECORD: true, INTERRUPTED: true }).result;
  }
});


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "./node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.container[data-v-240baac1] {\r\n    padding: 20px;\r\n    background-color: #f5f7fa;\r\n    min-height: 100vh;\n}\n.clearfix[data-v-240baac1]:before,\r\n.clearfix[data-v-240baac1]:after {\r\n    display: table;\r\n    content: \"\";\n}\n.clearfix[data-v-240baac1]:after {\r\n    clear: both;\n}\n.info-item[data-v-240baac1] {\r\n    margin-bottom: 10px;\n}\n.label[data-v-240baac1] {\r\n    font-weight: bold;\r\n    color: #606266;\r\n    margin-right: 10px;\n}\n.value[data-v-240baac1] {\r\n    color: #303133;\n}\n.footer-actions[data-v-240baac1] {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    background: white;\r\n    border-radius: 8px;\r\n    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n.footer-actions .el-button[data-v-240baac1] {\r\n    margin: 0 10px;\r\n    padding: 12px 30px;\n}\r\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css */ "./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.id, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = (__webpack_require__(/*! !../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js")["default"])
var update = add("f1ed9268", content, false, {"sourceMap":false,"shadowMode":false});
// Hot Module Replacement
if(false) // removed by dead control flow
{}

/***/ }),

/***/ "./src/view/mingjing/produce_detail.vue":
/*!**********************************************!*\
  !*** ./src/view/mingjing/produce_detail.vue ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _produce_detail_vue_vue_type_template_id_240baac1_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./produce_detail.vue?vue&type=template&id=240baac1&scoped=true */ "./src/view/mingjing/produce_detail.vue?vue&type=template&id=240baac1&scoped=true");
/* harmony import */ var _produce_detail_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./produce_detail.vue?vue&type=script&lang=js */ "./src/view/mingjing/produce_detail.vue?vue&type=script&lang=js");
/* harmony import */ var _produce_detail_vue_vue_type_style_index_0_id_240baac1_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css */ "./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js */ "./node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js");



;


/* normalize component */

var component = (0,_node_modules_vue_vue_loader_v15_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _produce_detail_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _produce_detail_vue_vue_type_template_id_240baac1_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _produce_detail_vue_vue_type_template_id_240baac1_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "240baac1",
  null
  
)

/* hot reload */
if (false) // removed by dead control flow
{ var api; }
component.options.__file = "src/view/mingjing/produce_detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/view/mingjing/produce_detail.vue?vue&type=script&lang=js":
/*!**********************************************************************!*\
  !*** ./src/view/mingjing/produce_detail.vue?vue&type=script&lang=js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css":
/*!******************************************************************************************************!*\
  !*** ./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css ***!
  \******************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_style_index_0_id_240baac1_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css */ "./node_modules/vue-style-loader/index.js??clonedRuleSet-12.use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12.use[1]!./node_modules/@vue/vue-loader-v15/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12.use[2]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=style&index=0&id=240baac1&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_style_index_0_id_240baac1_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_style_index_0_id_240baac1_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_style_index_0_id_240baac1_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _node_modules_vue_style_loader_index_js_clonedRuleSet_12_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_use_1_node_modules_vue_vue_loader_v15_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_use_2_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_style_index_0_id_240baac1_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./src/view/mingjing/produce_detail.vue?vue&type=template&id=240baac1&scoped=true":
/*!****************************************************************************************!*\
  !*** ./src/view/mingjing/produce_detail.vue?vue&type=template&id=240baac1&scoped=true ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_template_id_240baac1_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_template_id_240baac1_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_use_0_node_modules_vue_vue_loader_v15_lib_loaders_templateLoader_js_ruleSet_1_rules_3_node_modules_vue_vue_loader_v15_lib_index_js_vue_loader_options_produce_detail_vue_vue_type_template_id_240baac1_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!../../../node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./produce_detail.vue?vue&type=template&id=240baac1&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/vue-loader-v15/lib/loaders/templateLoader.js??ruleSet[1].rules[3]!./node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./src/view/mingjing/produce_detail.vue?vue&type=template&id=240baac1&scoped=true");


/***/ })

}]);
//# sourceMappingURL=src_view_mingjing_produce_detail_vue.e15065ac.js.map