{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">一览</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">名称</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="name" v-model="name"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                        <button type="button" class="btn yellow" onclick="create()">
                            <i class="fa fa-plus"></i>&nbsp;<span>新增</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('{{module}}/{{model}}/list/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                    <tr>
                        {{field}}
                        <th data-formatter="actionFormatter">操作</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="list-table">
        <button type="button" onclick="edit('@uid@')" class="btn blue">编辑</button>
        <button type="button" onclick="del('@uid@')" class="btn red">删除</button>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            name: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable();
    function actionFormatter(v, row, idx) {
        return $('#act').html().replace(/@uid@/g, row.uid);
    }

    function create() {
        top.window.layer_result = '';
        top.layer.open({
            title: '新增',
            type: 2,
            resize: false,
            area: ['40em', '80%'],
            content: '{{ url('{{module}}/{{model}}/create') }}',
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function edit(uid) {
        top.window.layer_result = '';
        top.layer.open({
            title: '编辑',
            type: 2,
            resize: false,
            area: ['40em', '80%'],
            content: '{{ url('{{module}}/{{model}}/edit/') }}' + uid,
            end: function() {
                if (top.window.layer_result == 'ok') {
                    toastr.success('操作完毕');
                    $table.bootstrapTable('refresh');
                }
            }
        });
    }

    function del(uid) {
        let dlg = top.layer.confirm('确认删除吗？', function() {
            top.layer.close(dlg);
            showSpin();
            $.post("{{ url('{{module}}/{{model}}/delete') }}", {uid: uid}, function (rs) {
                closeSpin();
                if (rs.status == 'ok') {
                    toastr.success('操作成功！');
                    $table.bootstrapTable('refresh');
                }
                else {
                    toastr.error('操作失败！' + rs.message);
                }
            })
        });
    }
</script>