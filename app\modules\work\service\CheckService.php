<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Service\BaseService;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Mes\Model\MesCheckLogs;
use Envsan\Modules\Mes\Model\MesNoticeDetail;
use Envsan\Modules\Mes\Model\MesProductBom;
use Envsan\Modules\Mes\Model\MesProductQuality;
use Envsan\Modules\Purchase\Model\PurchaseGoods;
use Envsan\Modules\Purchase\Model\PurchaseInspection;
use Envsan\Modules\Purchase\Model\PurchaseInspectionDetail;
use Envsan\Modules\Purchase\Model\PurchaseInstock;
use Envsan\Modules\Purchase\Model\PurchaseInstockDetail;
use Envsan\Modules\Purchase\Model\PurchaseReceiptDetail;
use Phalcon\Mvc\User\Component;

class CheckService extends BaseService
{
    public function getList(){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                 t1.id,
                 t1.uid,
                 t1.goods_code,
                 t1.goods_name,
                 t1.goods_model,
                 t1.goods_unit,
                 t1.goods_deputy_unit,
                 round(t1.quantity,4) as quantity,
                 t2.inspection_code as code,
                 t2.inspection_day
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInspectionDetail','t1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInspection','t1.inspection_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.check_status = 10')
            ->orderBy('t2.inspection_day , t1.id');
        $rows = $builder->getQuery()->execute();
        return $this->success($rows);

    }

    public function getData(){
        $rtn = new \stdClass();
        $uid = $this->request->getPost('uid', 'tstring');
        if (empty($uid)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = PurchaseInspectionDetail::findFirst(['del_flag = 0 and uid = ?1','bind'=>[1=>$uid]]);
        if (empty($row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $inspection_row = PurchaseInspection::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$row->inspection_id]]);
        if (empty($inspection_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        if ($row->check_status > 10){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $goods_row = PurchaseGoods::findFirst(['del_flag = 0 and id = ?1','bind'=>[1=>$row->goods_id]]);
        if (empty($goods_row)){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $check_data = CvtUtil::emptyToArray($goods_row->quality_check_data);
        $jrow = $row->toArray();
        $jrow['quality_template_id'] = $goods_row->quality_template_id;
        $jrow['code'] = $inspection_row->inspection_code;
        $jrow['instock_date'] = $inspection_row->inspection_day;
        $rtn->message = '';
        $rtn->data = [
            'data' => $jrow,
            'check_data' => $check_data
        ];
        return $rtn;
    }

    public function saveData()
    {

        return $this->executeInTransaction(function () {
            $uid = $this->request->getPost('uid', 'tstring');
            $quality_template_id = $this->request->getPost('quality_template_id', 'tstring');
            $check_result_flag = $this->request->getPost('check_result_flag', 'tstring');
            $check_remarks = $this->request->getPost('check_remarks', 'tstring');
            $check_data = urldecode($this->request->getPost('check_data', 'tstring'));
            $files = urldecode($this->request->getPost('files', ['string', 'trim']));
            if (empty($uid) || empty($quality_template_id)) {
                return $this->error(ErrorHelper::WRONG_INPUT . '1');
            }
            $row = PurchaseInspectionDetail::findFirst(['del_flag = 0 and uid = ?1', 'bind' => [1 => $uid]]);
            if (empty($row)) {
                return $this->error(ErrorHelper::WRONG_INPUT);
            }
            if ($row->check_status > 10) {
                return $this->error('已经检验完成');
            }
            $user = SessionData::user();

            $now = DateUtil::now();
            $common = new \Envsan\Modules\Mes\Service\CommonService();
            $check_data = CvtUtil::emptyToArray($check_data);
            $row->check_status = 20;
            $row->check_images = $files;
            $row->check_val = CvtUtil::arrayToNull($common->getCheckDataValue($check_data));
            $row->check_data = CvtUtil::arrayToNull($check_data);
            $row->check_result_flag = CvtUtil::emptyToInt($check_result_flag);
            $row->check_user_id = $user->id;
            $row->check_user_name = $user->real_name;
            $row->check_time = $now;
            $row->check_remarks = CvtUtil::blankToNull($check_remarks);
            $row->quality_template_id = CvtUtil::blankToNull($quality_template_id);
            $row->update_date = $now;
            $row->update_by = $user->id;
            $row->del_flag = 0;
            $row->owner = $user->owner;
            $row->save();
        });
    }
}