{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">设备故障查询</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">设备编号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="equ_code" v-model="equ_code"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('equ/fault/search/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                    <tr>
                        <th data-field="fault_no">故障单号</th>
                        <th data-field="equ_code">设备编号</th>
                        <th data-field="fault_level_name">影响级别</th>
                        <th data-field="begin_dt">发生时间</th>
                        <th data-field="repair_dt">计划解除时间</th>
                        <th data-field="repair_user_name">责任人</th>
                        <th data-field="repair_company">外协供应商</th>
                        <th data-field="repair_money">外协修理费(元)</th>
                        <th data-field="end_dt">解除时间</th>
                        <th data-field="status_name">状态</th>
                        <th data-formatter="actionFormatter">操作</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div id="act" style="display: none;">
    <div class="list-table">
        <button type="button" onclick="detail('@uid@')" class="btn green">查看详情</button>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            equ_code: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });

    $table.bootstrapTable();
    function actionFormatter(v, row, idx) {
        return $('#act').html().replace(/@uid@/g, row.uid);
    }

    function detail(uid) {
        top.layer.open({
            title: '查看详情',
            type: 2,
            resize: false,
            area: ['100%', '100%'],
            content: '{{ url('equ/fault/detail/') }}' + uid,
        });
    }
</script>