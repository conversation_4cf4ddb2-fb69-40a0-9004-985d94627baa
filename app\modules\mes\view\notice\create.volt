{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('js').addJs('static/global/plugins/moment/moment.min.js') %}
{% do assets.collection('css').addCss('static/global/css/zh-fixed-table.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">基本信息</span>
                    </div>
                </div>
                <div class="portlet-body form" >
                    <form id="form" class="form-horizontal">
                        <div class="form-body"  style="height: 73vh;overflow-y: auto;overflow-x: hidden">
                            <div id="form_data" class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">客户</label>
                                        <div class="col-sm-8">
                                            <select class="bs-select form-control" name="customer_id" v-model="customer_id" data-live-search="true" data-size="8" :disabled="event_type === 'change'">
                                                <option value="">请选择</option>
                                                <option v-for="row in customerList" :value="row['id']">
                                                    ${ row['name'] }
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">销售订单</label>
                                        <div class="col-sm-8">
                                            <select class="bs-select form-control" name="order_id" v-model="order_id" data-live-search="true" data-size="8" :disabled="event_type === 'change'">
                                                <option value="">请选择</option>
                                                <option v-for="row in order_list" :value="row['id']">
                                                    ${ row['code'] }
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>计划开工日</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" placeholder="请输入计划开工日" name="plan_begin_date" v-model="plan_begin_date" required :disabled="event_type === 'change'"/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label"><span class="required">*</span>计划完工日</label>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" placeholder="请输入计划完工日" name="plan_end_date" v-model="plan_end_date" required :disabled="event_type === 'change'"/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">附件</label>
                                        <div class="col-sm-8" style="display: flex;flex-direction: column">
                                            <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                                <div>
                                                    <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                        <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                    </a>
                                                </div>
                                                <div>
                                                    <a  v-if="event_type != 'change'" style="color: red;margin-left: 10px" href="javascript:;" title="删除" @click="delFile(index)" >
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                            <div v-if="event_type != 'change'" class="btn btn-outline blue btn-sm" style="width: 60px;margin-right: 10px" onclick="uploadPdf()" :disabled="event_type === 'change'"><i class="fa fa-upload"></i> 上传</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">备注</label>
                                        <div class="col-sm-8">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3" :disabled="event_type === 'change'"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button v-if="event_type == 'change'" type="button" class="btn green" @click="change"><i class="fa fa-save"></i> 变更数量</button>
                            <button v-if="event_type != 'change'" type="button" class="btn green" @click="save"><i class="fa fa-save"></i> 保存</button>
                            <button v-if="event_type != 'change'" type="button" class="btn yellow" @click="commit"><i class="fa fa-check"></i> 提交</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light" style="margin-bottom: 0">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-list font-yellow"></i>
                        <span class="caption-subject font-yellow bold">产品明细</span>
                    </div>
                    <div v-if="event_type != 'change'" class="actions" style="display: flex;align-items: center">
                        <div class="input-group" style="width: 200px">
                            <select class="bs-select form-control" name="product_id" v-model="product_id" data-live-search="true" data-size="8" required>
                                <option value="">请选择</option>
                                <option v-for="(product_item,product_idx) in product_list" v-text="product_item.code + '/' + product_item.name" :value="product_item.id"></option>
                            </select>
                        </div>
                        <button type="button" class="btn blue btn-outline" @click="addProduct" style="margin-left: 20px;padding: 6px 12px;font-size: 14px;"><i class="fa fa-plus"></i>&nbsp;添加</button>
                    </div>
                </div>
                <div class="portlet-body" style="height: 80vh;overflow-y: auto">
                    <div class="col-sm-12">
                        <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                            <thead>
                            <tr>
                                <th>产品名称</th>
                                <th>规格型号</th>
                                <th>委托加工</th>
                                <th>生产数量</th>
                                <th>生产备注</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="row, index in detail_data">
                                <td>
                                    <span v-text="row.name"></span>
                                </td>
                                <td>
                                    <span v-text="row.code"></span>
                                </td>
                                <td>
                                    <div style="display: flex">
                                        <div v-for="(item,idx) in row.list" style="padding: 5px">
                                            <a v-if="event_type != 'change'" @click="item.sel == 0 ? item.sel = 1 : item.sel = 0">
                                                <i v-if="item.sel == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                                <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                                <span v-text="item.name" style="color: #000"></span>
                                            </a>
                                            <a v-else>
                                                <i v-if="item.sel == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                                <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                                <span v-text="item.name" style="color: #000"></span>
                                            </a>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="input-group">
                                        <input  type="number" class="form-control" :name="'quantity' + index" v-model="row.quantity" placeholder="数量" number="true"  maxlength="10" required>
                                        <span class="input-group-addon">个</span>
                                    </div>
                                </td>
                                <td>
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="row.remarks" maxlength="200" rows="3"></textarea>
                                </td>
                                <td>
                                    <a href="javascript:;" @click="viewDetail(row.id)" style="color: blue;font-size: 20px;margin-right: 10px" title="查看详情">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    <a v-if="event_type != 'change'" href="javascript:;" @click='detail_data.splice(index,1)' style="color: red;font-size: 20px">
                                        <i class="fa fa-times"></i>
                                    </a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="row" v-if="detail_data.length == 0" style="text-align: center;margin-top: 15px">
                            <span>没有数据</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{ partial('check') }}
{{ partial('uploader_file') }}
{{ partial('common_utils') }}
<script>
    var event_type = '{{ event_type }}';
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonNotice }},
            event_type: event_type
        },
        methods: {
            change(e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                for (let i = 0; i < this.detail_data.length; i++) {
                    let row = this.detail_data[i];
                    if (!isDecimal(row.quantity) || safeNumber(row.quantity) <= 0) {
                        toastr.error('【序号' + (i + 1) + '】出库数量只能是大于0的数字。');
                        return;
                    }
                }
                this.summit(3);
            },
            save(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                this.summit(1);
            },
            commit(e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                if (app.detail_data.length == 0){
                    toastr.error('请添加生产产品');
                    return;
                }

                for (let i = 0; i < this.detail_data.length; i++) {
                    let row = this.detail_data[i];
                    if (!isDecimal(row.quantity) || safeNumber(row.quantity) <= 0) {
                        toastr.error('【序号' + (i + 1) + '】出库数量只能是大于0的数字。');
                        return;
                    }
                }
                var dlg = top.layer.confirm('确认提交吗?', () => {
                    top.layer.close(dlg);
                    this.summit(2);
                });
            },
            summit(type){
                showSpin();
                {% if dispatcher.getActionName() == 'edit' %}
                var url= '{{ url('mes/notice/edit/' ~ event_type ~ '/' ~ uid) }}';
                {% else %}
                var url= '{{ url('mes/notice/create') }}';
                {% endif %}

                commonAjaxRequest(url,
                    {
                        type : type,
                        customer_id : app.customer_id,
                        order_id : app.order_id,
                        plan_begin_date : app.plan_begin_date,
                        plan_end_date : app.plan_end_date,
                        remarks : app.remarks,
                        files:encodeURI(JSON.stringify(app.files)),
                        ext_data : encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B'),
                        detail_data : encodeURI(JSON.stringify(app.detail_data)).replace(/\+/g,'%2B')
                    },
                    function(rs) {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                );
            },
            addProduct(){
                if (this.product_id == ''){
                    toastr.error('请选择添加产品');
                    return;
                }
                if (this.detail_data.some(item=>item.id == this.product_id)){
                    toastr.error('不能重复添加');
                    return;
                }

                commonAjaxRequest('{{ url('mes/notice/getbom') }}',
                    {
                        product_id: app.product_id
                    },
                    function(rs) {
                        app.detail_data.push(rs.data);
                        app.product_id = '';
                        app.$nextTick(function() {
                            $('.bs-select').selectpicker('refresh');
                        });
                    }
                );
            },
            delFile: function(index) {
                this.files.splice(index);
            },
            viewDetail(product_id) {
                top.window.layer_result2 = '';
                top.layer.open({
                    title:'新增',
                    type: 2,
                    area: ['100%', '100%'],
                    content: '{{ url('mes/notice/orderdetail/0/') }}' + '/' + product_id,
                    end: function() {
                        if (top.window.layer_result2 == 'ok') {
                            toastr.success('操作完毕');
                        }
                    }
                });
            },

        },
        watch: {
            customer_id: function(val) {
                app.order_id = '';
                app.product_id = '';
                app.product_list = [];
                app.detail_data = []

                commonAjaxRequest('{{ url('mes/notice/order') }}',
                    {
                        customer_id: val,
                    },
                    function (rs) {
                        app.order_list = rs.data.orderList;
                        app.product_list = rs.data.productList || [];
                        app.detail_data = [];
                        app.$nextTick(function() {
                            $('.bs-select').selectpicker('refresh');
                        });
                    }
                );
            },
            order_id: function(val) {
                commonAjaxRequest('{{ url('mes/notice/product') }}',
                    {
                        order_id: val,
                        customer_id: app.customer_id
                    },
                    function (rs) {
                        app.product_id = '';
                        app.product_list = rs.data;
                        app.detail_data = [];
                        app.$nextTick(function() {
                            $('.bs-select').selectpicker('refresh');
                        });
                    }
                );
            }
        }
    });

    initUpLoaderPdf('notice');
    function fileQueued() {
        showSpin();
        uploader.upload();
    }

    function uploadSuccess(rs) {
        closeSpin(null);
        if (rs.status == 'ok') {
            toastr.success("上传成功！");
            app.files.push({
                key:getUuid(),
                url_name : rs.file_name,
                url:rs.file_url,
            });
        } else {
            toastr.error("上传失败！" + rs.message);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}
<style>
    .tb-left {
        position: sticky;
        left: 0;
        z-index: 1;
    }

    .left-1 {
        border-left: 0 !important;
    }
    .left-2 {
        left: 220px;
    }
    .left-3 {
        left: 380px;
    }
    .left-4 {
        left: 480px;
    }
    .left-5 {
        left: 580px;
    }

    thead .tb-left {
        background-color: #3598DC !important;
    }

    .zh-table-box th, .zh-table-box td {
        text-align: left;
    }

    .zh-table-box table tbody .tb-left {
        border: 0 !important;
        padding: 0 !important;
    }

    .zh-table-box table tbody td {
        background-color: #ffffff;
    }

    .zh-table-box table tbody .tb-left > div {
        padding: 8px;
        border-right: 1px solid #e7ecf1;
        border-bottom: 1px solid #e7ecf1;
        line-height: 36px;
        height: 54px;
    }

    .zh-table-box table tbody .left-1 > div {
        border-left: 1px solid #e7ecf1;
    }

    .btn-del {
        width: 18px;
        height: 18px;
        font-size: 13px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 100%;
        margin-left: 5px;
        background-color: #e7505a;
        color: #FFFFFF;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .btn-del:hover {
        background-color: #e12330;
    }

    .btn-del:active {
        background-color: #c51b26;
    }
</style>