<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal">
                <div class="form-body">
                    <div id="form_data" class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">领料单号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">领料日期</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="outstock_date" v-model="outstock_date" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">领料人</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="outstock_user" v-model="outstock_user" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-if="order_bom_id != null">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">项目号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="order_code"  v-model="order_code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-if="order_bom_id != null">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">客户</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="customer_name"  v-model="customer_name" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-if="order_bom_id != null">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">产品编号</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="product_code" v-model="product_code" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-if="order_bom_id != null">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">产品名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="product_name" v-model="product_name" readonly/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6" v-if="order_bom_id != null">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">工艺名称</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="bom_name" v-model="bom_name" readonly/>
                                </div>
                            </div>
                        </div>
                        {{ partial('view') }}
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">附件</label>
                                <div class="col-sm-8" style="display: flex;flex-direction: column">
                                    <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="files.length > 0" v-for="item,index in files">
                                        <div>
                                            <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注</label>
                                <div class="col-sm-8">
                                    <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" rows="3" readonly></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <table class="table table-striped table-condensed" style="margin-bottom: 0;margin-top: 7px">
                                <thead>
                                <tr>
                                    <th>编码</th>
                                    <th>名称</th>
                                    <th>规格型号</th>
                                    <th>输入内容</th>
                                    <th>数量</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="row, index in detail_data">
                                    <td><span v-text="row.goods_code"></span></td>
                                    <td><span v-text="row.goods_name"></span></td>
                                    <td><span v-text="row.goods_model"></span></td>
                                    <td><span v-text="row.input_val"></span></td>
                                    <td><span v-text="row.quantity + '('+row.goods_unit+')'"></span></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonOutstock }},
        methods: {

        }
    });
</script>