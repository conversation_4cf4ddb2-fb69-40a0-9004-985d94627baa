<div id="btn_upyun" style="display: none;">选择文件</div>
<script>
    function upyunUpload() {
        $("#btn_upyun").find("label").click();
    }

    var upyunUploader = null;
    function initUpyunUpLoader(folder_name, file_type) {
        let uploaderOpt = {
            auto: false,
            swf: '{{ static_url('static/main/lib/uploader/uploader.swf') }}',
            server: '{{ upyun.host ~ '/' ~ upyun.bucket }}',
            dnd: '#dnd_' + folder_name,
            pick: '#btn_upyun',
            resize: false,
            fileSingleSizeLimit: 100 * 1024 * 1024,
            fileNumLimit: 1,
            duplicate: true
        };

        if (file_type === 'pdf') {
            uploaderOpt.accept = {
                title: 'Pdf',
                extensions: 'pdf',
                mimeTypes: 'application/pdf'
            };
        } else if (file_type === 'excel') {
            uploaderOpt.accept = {
                title: 'Excel',
                extensions: 'xlsx',
                mimeTypes: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };
        } else if (file_type === 'image') {
            uploaderOpt.accept = {
                title: 'Images',
                extensions: 'jpg,jpeg,png',
                mimeTypes: 'image/jpg,image/jpeg,image/png'
            };
        }

        upyunUploader = WebUploader.create(uploaderOpt);
        upyunUploader.on('beforeFileQueued', function(file) {
            let ext = file.name.split('.').pop().toLowerCase();
            if (file_type === 'excel') {
                if (ext !== 'xls' && ext !== 'xlsx') {
                    toastr.error('请上传Excel文件');
                    return false;
                }
            } else if (file_type === 'image') {
                if (ext !== 'jpg' && ext !== 'jpeg' && ext !== 'png') {
                    toastr.error('请上传jpg、jpeg、png格式的图片');
                    return false;
                }
            } else if (file_type === 'pdf') {
                if (ext!== 'pdf') {
                    toastr.error('请上传pdf格式的文件');
                    return false;
                }
            } else {
                toastr.error('文件格式无效');
                return false;
            }
            upyunUploader.reset();
        });

        upyunUploader.on('fileQueued', function(file) {
            let ext = file.name.split('.').pop().toLowerCase();
            let now = new Date();
            let date = now.toGMTString();
            let year = now.getFullYear();
            let month = ('0' + (now.getMonth() + 1)).slice(-2);
            let day = ('0' + now.getDate()).slice(-2);
            let remote_path = '{{ upyun.baseDir }}{{ owner.id }}/' + folder_name + '/' + year + '/' + month + '/' + day + '/';
            let file_name = remote_path + now.getTime() + '.' + ext;
            let contentType = '';
            if (ext === '.jpg') {
                contentType = 'image/jpeg';
            } else if (ext === '.pdf') {
                contentType = 'application/pdf';
            } else if (ext === '.xls') {
                contentType = 'application/vnd.ms-excel';
            } else {
                contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            }
            let opts = {
                'save-key': file_name,
                'bucket': '{{ upyun.bucket }}',
                'expiration': Math.round(now.getTime() / 1000) + 3600,
                'date': date,
                'content-type': contentType
            };
            let policy = btoa(JSON.stringify(opts));
            let data = [ 'POST', '/{{ upyun.bucket }}', date, policy ].join('&');
            showSpin();
            $.post('{{ url('common/file/sign') }}', {data: data}, function(rs) {
                upyunUploader.options.formData = {
                    authorization: 'UPYUN {{ upyun.user }}:' + rs.data,
                    policy: policy
                };
                upyunUploader.upload();
            });
        });

        upyunUploader.on('uploadSuccess', function(file, rs) {
            try {
                if (rs.message == 'ok') {
                    upyunUploadSuccess({
                        file_name: file.name,
                        file_url: rs.url
                    });
                } else {
                    closeSpin();
                    toastr.error('上传失败！' + rs.message);
                }
            } catch (e) {
                console.log(e);
                closeSpin();
                toastr.error('数据解析失败！');
            }
        });

        upyunUploader.on('uploadError', function(file, reason) {
            closeSpin();
            toastr.error('上传失败！');
        });
    }
</script>