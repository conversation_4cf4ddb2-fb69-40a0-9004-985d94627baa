<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Data\SessionData;
use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class User extends BaseModel
{
    const ACCOUNT_NORMAL=0;
    const ACCOUNT_LOCKED=1;

    const TYPE_SUPER = 255;

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $group_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $role_id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
    public $work_role_id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $empno;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $login_name;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $password;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $real_name;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $name_py;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $gender;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $cost;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $wages_item;

    /**
     *
     * @var string
     * @Column(type="string", length=30, nullable=true)
     */
    public $email;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $mobile;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $address;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $type;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $sign;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $auth_data_ids;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $auth_data_names;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $use_status;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $account_status;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $session_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $open_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $managewx_open_id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=false)
     */
    public $app_registration_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $team_id;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $extend_data;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $create_date;

    /**
     *
     * @var string
     * @Column(type="string", nullable=false)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_user';
    }

    public static function find($params= null)
    {
        return parent::find(ModelUtil::build($params));
    }

    public static function findDirect($params = null)
    {
        return parent::find($params);
    }

    public static function findFirst($params = null)
    {
        return parent::findFirst(ModelUtil::build($params));
    }

    public static function findFirstDirect($params = null)
    {
        return parent::findFirst($params);
    }

    public static function findFirstManage($params = null)
    {
        return parent::findFirst($params);
    }

    public function initialize()
    {
        $this->belongsTo('role_id', 'Envsan\Modules\Sys\Model\Role', 'id', array('alias' => 'Role'));
        $this->belongsTo('group_id', 'Envsan\Modules\Sys\Model\Group', 'id', array('alias' => 'Group'));

        ModelUtil::softDelete($this);
    }

    public function beforeDelete()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }

    public function beforeSave()
    {
        ModelUtil::checkOwner($this->owner);
        return true;
    }
}
