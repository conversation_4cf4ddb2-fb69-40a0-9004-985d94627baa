<?php

namespace Envsan\Modules\Sys\Controller;

use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\DomainUtil;
use Envsan\Modules\Sys\Model\Owner;
use Envsan\Modules\Sys\Service\LoginService;
use Envsan\Modules\Sys\Service\OwnerService;
use Envsan\Modules\Sys\Service\UserService;
use Phalcon\Mvc\View;

/**
 * @noacl
 */
class LoginController extends SuperController
{
    public function indexAction()
    {
        if ($this->session->has('user')){
            return $this->redirect('sys');
        }

        if ($this->config->developMode) {
            $owner = Owner::findFirst($this->config->developOwner);
        }
        else{
            $site = $this->request->get('site');
            if (empty($site))
                $owner = DomainUtil::checkOwner(false);
            else {
                $os = new OwnerService();
                $owner = $os->selectByDomain($site);
            }
        }

        if ($owner == null)
            return $this->redirect('sys/error/error404');
        else if ($owner->domain == 'admin')  // 超级用户时设置owner的Id为0
            $owner->id = 0;

        if ($owner->status == Owner::STATUS_DISABLED) // 冻结的账号处理
            return $this->redirect('sys/error/error403');

        $now = DateUtil::now();
        if ($owner->end_date < $now){
            return $this->redirect('sys/error/error402');
        }

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            // if ($this->security->checkToken('csrf')) {
            $ls = new LoginService();
            $user = $ls->login($owner);
            if ($user != null && $user->account_status == 0) {
                $ret->emptyIsOk();
                $us = new UserService();
                $us->createActiveUser($user);
            }

            // 登录失败时要更新token key
            if ($ret->status != JsonData::STATUS_OK){
                if ($user != null && $user->account_status == 1) {
                    $ret->message = '该账户已经被禁用,请联系管理员!';
                }else{
                    $ret->message = '您的用户名和密码无法登录系统!';
                }
                $ret->csrf = $this->security->getToken();
            }
// }
            // else {
            //     Logger::error('CSRF Faild!');
            // }
            return json_encode($ret, JSON_UNESCAPED_UNICODE);
        }

        $this->view->owner = $owner;
        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);
    }

    public function logoutAction()
    {
        $this->view->disable();

        $ls = new LoginService();
        $ls->logout();

        return $this->redirect('sys/login');
    }
}