<?php

namespace Envsan\Modules\Sys\Controller;

/**
 * @noacl
 */
class ErrorController extends SuperController
{
    public function error404Action()
    {
        $this->response->setStatusCode(404, 'Not Found');
    }

    public function error401Action()
    {
        $this->response->setStatusCode(401, 'Access Not Allowed');
    }

    public function error402Action()
    {
        $this->response->setStatusCode(402, 'Forbidden');
    }

    public function error403Action()
    {
        $this->response->setStatusCode(403, 'Forbidden');
    }
}