<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
class PurchaseViewEntrustOrderIn extends BaseModel
{

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=false)
     */
    public $entrust_id;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="string", length=11, nullable=true)
     */
    public $price;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_view_entrust_order_in';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewEntrustOrderIn[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseViewEntrustOrderIn
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

}
