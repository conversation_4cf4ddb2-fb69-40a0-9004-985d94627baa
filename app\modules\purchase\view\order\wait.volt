{% do assets.collection('css').addCss('static/pages/css/search.css') %}

<div class="page-content">
    <h3 class="page-title">待采购查询</h3>
    <div class="search-page">
        <div id="app" class="search-bar bordered">
            <form class="form-horizontal" autocomplete="off">
                <div class="row">
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">物料</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="goods_name" v-model="goods_name" @keyup.enter="search"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-3">
                        <div class="form-group">
                            <label class="col-md-3 control-label">供应商</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" name="supplier_name" v-model="supplier_name"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-9 to-right">
                        <button type="button" class="btn green" @click="search">
                            <i class="fa fa-search"></i>&nbsp;<span>查找</span>
                        </button>
                        <button type="button" class="btn blue" @click="reset">
                            <i class="fa fa-repeat"></i>&nbsp;<span>重置</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="search-table">
            <table id="table"
                   class="table table-striped table-condensed"
                   data-mobile-responsive="true"
                   data-pagination="true"
                   data-query-params="app.params"
                   data-url="{{ url('purchase/order/wait/json') }}"
                   data-page-size="10"
                   data-side-pagination="server">
                <thead class="bg-blue">
                <tr>
                    <th data-field="apply_code">采购申请单</th>
                    <th data-field="goods_name">物料名称</th>
                    <th data-field="goods_model">规格型号</th>
                    <th data-field="purchase_quantity" data-formatter="formatPurchaseQuantity">采购数量</th>
                    <th data-field="inventory_quantity" data-formatter="formatInventoryQuantity">库存需求数量</th>
                    <th data-field="supplier_name">供应商</th>
                    <th data-field="remarks">备注</th>
                    <th data-field="real_name">申请人</th>
                    <th data-field="create_date">申请时间</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<script>
    var $table = $('#table');
    var app = new Vue({
        el: '#app',
        data: {
            order_code: '',
            goods_name: '',
            supplier_name: '',
            product_name: '',
            from_type: ''
        },
        methods: {
            search: function() {
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            },
            params: function(p) {
                for (let col in this.$data) {
                    p[col] = this.$data[col];
                }
                return p;
            },
            reset: function() {
                for (let col in this.$data) {
                    this.$data[col] = '';
                }
                $(".bs-select").selectpicker('val', '');
                $table.bootstrapTable('refreshOptions', {pageNumber: 1});
            }
        }
    });
    $table.bootstrapTable();
    function codeFormatter(v, row, index) {
        return row.apply_code ?? v;
    }

    function dateFormatter(v, row, index) {
        return row.apply_date ?? v;
    }

    // 格式化采购数量显示：数量（单位）
    function formatPurchaseQuantity(value, row, index) {
        if (row.purchase_quantity && row.purchase_unit) {
            return row.purchase_quantity + '（' + row.purchase_unit + '）';
        }
        return value || '';
    }

    // 格式化库存数量显示：数量（单位）
    function formatInventoryQuantity(value, row, index) {
        if (row.inventory_quantity && row.inventory_unit) {
            return row.inventory_quantity + '（' + row.inventory_unit + '）';
        }
        return value || '';
    }

    $(".bs-select").selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>