<?php
namespace Envsan\Modules\Trade\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseGoodsType;
use Envsan\Modules\Trade\Model\TradeCustomer;
use Phalcon\Mvc\User\Component;

class CustomerService extends Component
{
    public function selectAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.name,
                t1.ext_val
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeCustomer', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 't1.goods_type_code = p1.code', 'p1')
            ->where('t1.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.code');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new TradeCustomer();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        // 顾客名称
        $name = $this->request->getPost('name', 'tstring');
        // 顾客码
        $code = $this->request->getPost('code', 'tstring');
        // 存货档案code + 名称
//        $goods_type_text = $this->request->getPost('goods_type_text', 'tstring');
        // 存货档案分类ID，作为存货档案的PID
//        $pid = $this->request->getPost('goods_type_id', 'tstring');
        $ext_data = str_replace('%2B', '+', urldecode($this->request->getPost('ext_data', 'tstring')));
        if (empty($code) || empty($name)) {
            return ErrorHelper::WRONG_INPUT;
        }

        if ($this->isRepeatName($name, $row->id)) {
            return '客户名称已存在';
        }

        if ($this->isRepeatCode($code, $row->id)) {
            return '客户编码已存在';
        }
        $this->db->begin();
        try {

            // 插入顾客的存货档案数据
            // 判断客户的存货档案是否已经做成
//            $goods_type_code = $this->judgeGoodsTypeName($pid, $name);
//            // 如果客户没有存货档案，则在父类下创建，code取得最大值加1
//            if (empty($goods_type_code)) {
//                $goods_type_code = $this->insertGoodsType($goods_type_text, $name, $pid);
//            }

            $table = new TableService();
            $now = DateUtil::now();
            $user = SessionData::user();
            $ext_data = CvtUtil::emptyToArray($ext_data);
            // 存货档案code
//            $row->goods_type_code = $goods_type_code;
            $row->code = $code;
            $row->name = $name;
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data), JSON_UNESCAPED_UNICODE);
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $row->uid = UUID::make();
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("【客户管理】trade_customer表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }

    /**
     * 判断客户名称是否重复
     * @param $pid
     * @param $name
     * @return string|null
     */
//    private function judgeGoodsTypeName($pid, $name)
//    {
//        $check = PurchaseGoodsType::findFirst(['pid = ?1 and name = ?2 and del_flag = 0 and owner = ?3', 'bind' => [1 => $pid, 2 => $name , 3=> SessionData::ownerId()]]);
//        if ($check != null) {
//            return $check->code;
//        }
//        return null;
//    }

    /**
     * 取得存货档案父类下最大的code，并+1返回
     * @param $pid
     * @param $goods_type_text
     * @return string
     */
//    private function getGoodsTypeChildNo($pid, $goods_type_text): string
//    {
//        // 存货档案code
//        $goods_type_code = explode(' ', trim($goods_type_text))[0]; // 获取第一个值，如 "0012"
//        $code = '01';
//        $builder = $this->modelsManager->createBuilder()
//            ->columns('max(a.code) as code')
//            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 'a')
//            ->where('a.del_flag = 0 and a.pid = ?1',[1 => $pid])
//            ->orderBy('a.id');
//        $rows = $builder->getQuery()->execute();
//        if (count($rows) > 0){
//            $code = CvtUtil::emptyToInt(substr($rows[0]->code, -2)) + 1;
//        }
//        return $goods_type_code . str_pad($code, 2, '0', STR_PAD_LEFT);
//    }

    /**
     * @throws \Exception
     */
//    private function insertGoodsType($goods_type_text, $goods_type_name, $pid)
//    {
//        $goods_type_code = $this->getGoodsTypeChildNo($pid, $goods_type_text);
//        $now = DateUtil::now();
//        $user = SessionData::user();
//        $purchaseGoodsType = new PurchaseGoodsType();
//        $purchaseGoodsType->uid = UUID::make();
//        $purchaseGoodsType->pid = $pid;
//        $purchaseGoodsType->code = $goods_type_code;
//        $purchaseGoodsType->name = $goods_type_name;
//        $purchaseGoodsType->short_name = $goods_type_name;
//        $purchaseGoodsType->type_level = 3;
//        $purchaseGoodsType->part_flag = 0;
//        $purchaseGoodsType->update_date = $now;
//        $purchaseGoodsType->update_by = $user->id;
//        $purchaseGoodsType->owner = $user->owner;
//        $purchaseGoodsType->del_flag = 0;
//        if (!$purchaseGoodsType->save()) {
//            throw new \Exception("插入PurchaseGoodsType表更新失败");
//        }
//        return $goods_type_code;
//    }

    private function isRepeatName($name, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Trade\Model\TradeCustomer')
            ->where('del_flag = 0 and name = ?1 and owner = ?2', [1 => $name, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    private function isRepeatCode($code, $id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->addFrom('Envsan\Modules\Trade\Model\TradeCustomer')
            ->where('del_flag = 0 and code = ?1 and owner = ?2', [1 => $code, 2 => SessionData::user()->owner]);

        if (!CheckUtil::is_empty($id)) {
            $builder->andWhere("id <> ?3", [3 => $id]);
        }

        $rows = $builder->getQuery()->execute();
        return count($rows) > 0;
    }

    public function selectById($id)
    {
        return TradeCustomer::findFirst(['id=?1', 'bind'=>[1=>$id]]);
    }

    public function selectByUid2($uid)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.name,
                t1.code,
                t1.ext_data,
                t1.ext_val,
                t1.market_user_id,
                t1.market_user_name,
                t1.create_date,
                t1.create_by,
                t1.update_date,
                t1.update_by,
                t1.del_flag,
                t1.group_id,
                t1.owner'
            )
            ->addFrom('Envsan\Modules\Trade\Model\TradeCustomer',  't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseGoodsType', 't1.goods_type_code = p1.code', 'p1')
            ->where('t1.del_flag = 0 and t1.uid=?1', [1 => $uid]);

        return $builder->getQuery()->execute()->getFirst();
    }


    public function selectByUid($uid)
    {
        return TradeCustomer::findFirst(['uid=?1', 'bind'=>[1=>$uid]]);
    }

    public function deleteByUid($uid)
    {
        $this->db->begin();
        try {
            $row = TradeCustomer::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
            if ($row == false) {
                return '';
            }

            $now = DateUtil::now();
            $user = SessionData::user();

            $row->del_flag = 1;
            $row->update_date = $now;
            $row->update_by = $user->id;
            if (!$row->save()) {
                throw new \Exception("【删除客户】trade_customer表更新失败");
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }
}