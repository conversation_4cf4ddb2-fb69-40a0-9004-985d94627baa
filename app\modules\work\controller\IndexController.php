<?php

namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Component\Logger;
use GuzzleHttp\Client;

/**
 * @noacl
 */
class IndexController extends SuperController
{
    public function indexAction($register_id='')
    {
        $version = $this->getVersion();
        $this->redirect("/dist_work_250714_3/?v=$version#/index?registration=".$register_id);
    }

    private function getVersion()
    {
        $versionFile = '/dist_work_250714_3/version.json';
        $version = time(); // 默认用时间戳

        if (file_exists($versionFile)) {
            $versionData = json_decode(file_get_contents($versionFile), true);
            $version = $versionData['hash']; // 使用构建hash：8491e955e542ee2e
        }

        return $version;
    }
}