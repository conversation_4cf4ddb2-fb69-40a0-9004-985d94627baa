{% do assets.collection('css').addCss('static/global/plugins/@logicflow/core/dist/style/index.css') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/core/dist/logic-flow.js') %}
{% do assets.collection('css').addCss('static/global/plugins/@logicflow/extension/lib/style/index.css') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/Menu.js') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/SelectionSelect.js') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/DndPanel.js') %}
{% do assets.collection('js').addJs('static/global/plugins/@logicflow/extension/lib/Group.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{% do assets.collection('css').addCss('static/global/plugins/uploader/webuploader.css') %}
{% do assets.collection('js').addJs('static/global/plugins/uploader/webuploader.js') %}
{% do assets.collection('css').addCss('static/global/plugins/lightbox2/css/lightbox.css') %}
{% do assets.collection('js').addJs('static/global/plugins/lightbox2/js/lightbox.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.worker.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vue.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/Sortable.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/vuedraggable/vuedraggable.umd.min.js') %}

{{ assets.outputJs('validate') }}
<!-- Main content -->
<div style="height: 100vh;width: 100%;background-color: #fff">
    <div style="display: flex;flex-direction: row;">
        <div id="app" style="width: 25vw">
            <div style="height:100vh;border-right: 1px #E2E2E2 solid;">
                <div class="portlet-body form">
                    <div class="tabbable-line boxless tabbable-reversed" style="border-bottom: 1px #36C6D3 SOLID;margin-top: 0;background-color: #f2f2f2;margin-bottom: 0">
                        <ul class="nav nav-tabs">
                            <li :class="tab_id == 1 ? 'active' : ''">
                                <a @click="tabClick(1)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="tab_id == 1 ? true : false">产品信息</a>
                            </li>
                            <li :class="tab_id == 2 ? 'active' : ''">
                                <a @click="tabClick(2)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="tab_id == 2 ? true : false">订单信息</a>
                            </li>
                            <li :class="tab_id == 3 ? 'active' : ''">
                                <a @click="tabClick(3)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="tab_id == 3 ? true : false">图纸管理</a>
                            </li>
                        </ul>
                    </div>
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 87vh;overflow-y: auto;">
                            <div id="form_data">
                                <div class="row" v-if="tab_id == 1">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>产品编号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="code" v-model="code" required maxlength="50" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>产品名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="name" v-model="name" required maxlength="100" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    {{ partial('view') }}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">备注</label>
                                            <div class="col-sm-8">
                                                <textarea style="resize: none;" placeholder="请输入备注" class="form-control" v-model="remarks" maxlength="200" rows="3" readonly></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" v-if="tab_id == 2">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">订单号</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="order_code" v-model="order_code" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">客户</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="customer_name" v-model="customer_name" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">产品名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="order_name" v-model="order_name" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    {{ partial('view2') }}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">备注</label>
                                            <div class="col-sm-8">
                                                <textarea style="resize: none;" class="form-control" v-model="order_remarks" readonly maxlength="200" rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">附件</label>
                                            <div class="col-sm-8" style="display: flex;flex-direction: column">
                                                <div style="display: flex;flex-direction:row;justify-content: flex-start;min-width: 180px;margin-bottom: 10px;margin-top: 5px;max-width: 390px"  v-if="order_files.length > 0" v-for="item,index in order_files">
                                                    <div>
                                                        <a style="display: flex;max-width: 250px;" :href="base_path + item.url" target="_blank">
                                                            <div  v-text="item.url_name" style="max-width: 300px;overflow: hidden;text-overflow: ellipsis"></div>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" v-if="tab_id == 3" >
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                        <thead>
                                        <tr>
                                            <th>图号</th>
                                            <th>类型/版本</th>
                                            <th>备注</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <template v-for="drawing_item,drawing_index in drawing_list">
                                            <tr>
                                                <td>
                                                    <span v-text="drawing_item.code"></span><br>
                                                    <span v-text="drawing_item.drawing_name"></span>
                                                </td>
                                                <td>
                                                    <span v-text="drawing_item.skill_type_name"></span><br>
                                                    <span v-text="drawing_item.version_code"></span>
                                                </td>
                                                <td>
                                                    <span v-text="drawing_item.remarks"></span>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-primary btn-outline" @click="viewPdf(drawing_item.drawing_url)">
                                                            <i class="fa fa-fw fa-eye"></i>&nbsp;查看
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
            <div v-show="bom_show == 1" style="height:100vh;width: 25vw;border-left: 1px #E2E2E2 solid;position: absolute;top:0;right: 0;background-color: #fff;z-index: 9999">
                <div class="portlet-body form">
                    <div class="tabbable-line boxless tabbable-reversed" style="border-bottom: 1px #36C6D3 SOLID;margin-top: 0;background-color: #f2f2f2;margin-bottom: 0;position: relative">
                        <ul class="nav nav-tabs">
                            <li :class="bom_tab_id == 1 ? 'active' : ''">
                                <a @click="bomTabClick(1)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="bom_tab_id == 1 ? true : false">工艺信息</a>
                            </li>
                            <li :class="bom_tab_id == 2 ? 'active' : ''">
                                <a @click="bomTabClick(2)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="bom_tab_id == 2 ? true : false">检验标准</a>
                            </li>
                            <li :class="bom_tab_id == 3 ? 'active' : ''">
                                <a @click="bomTabClick(3)" style="min-width: 100px;text-align: center"
                                   data-toggle="tab" :aria-expanded="bom_tab_id == 3 ? true : false">原料/配件</a>
                            </li>
                        </ul>
                        <div style="position: absolute;top:10px;right: 15px">
                            <a @click="closeBom"><i style="font-size: 20px" class="fa fa-times"></i></a>
                        </div>
                    </div>
                    <form id="form_bom" class="form-horizontal">
                        <div class="form-body" style="height: 87vh;overflow-y: auto;">
                            <div id="form_data_bom">
                                <div class="row" v-show="bom_tab_id == 1">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">工艺类型</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="ship_type_name" v-model="bom_data.ship_type_name" readonly/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>工艺名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" placeholder="请输入工艺名称" readonly class="form-control" name="bom_data_name" v-model="bom_data.name" required maxlength="100"/>
                                            </div>
                                        </div>
                                    </div>
{#                                    <div class="col-sm-12">#}
{#                                        <div class="form-group">#}
{#                                            <label class="col-sm-4 control-label"><span class="required">*</span>构成数量</label>#}
{#                                            <div class="col-sm-8">#}
{#                                                <input type="number" placeholder="请输入构成数量" readonly class="form-control" number="true" name="bom_data_cnt" v-model="bom_data.cnt" maxlength="4" required>#}
{#                                            </div>#}
{#                                        </div>#}
{#                                    </div>#}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>生产性基准</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="number" placeholder="请输入生产性基准" class="form-control" number="true" name="bom_produce_cnt" readonly v-model="bom_data.produce_cnt" maxlength="4" required>
                                                    <span class="input-group-addon">件/H</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>加工成本</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="number" placeholder="请输入加工成本" readonly class="form-control" number="true" name="bom_produce_cost" v-model="bom_data.produce_cost" maxlength="8" required>
                                                    <span class="input-group-addon">元/件</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>工资类型</label>
                                            <div class="col-sm-8">
                                                <input type="text" placeholder="请输入工艺名称" readonly class="form-control" name="bom_work_type_name" v-model="bom_data.work_type_name" required maxlength="100"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label"><span class="required">*</span>计件工资</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="number" placeholder="请输入计件工资" class="form-control" number="true" name="bom_one_cost" v-model="bom_data.one_cost" maxlength="8" readonly required>
                                                    <span class="input-group-addon">元/件</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">需要外委</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" number="true" name="bom_is_outsourcing_name" v-model="bom_data.is_outsourcing_name" readonly>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {{ partial('view3') }}
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">备注</label>
                                            <div class="col-sm-8">
                                                <textarea style="resize: none;" placeholder="请输入备注" class="form-control" name="bom_data_remarks" v-model="bom_data.remarks" maxlength="200" rows="3" readonly></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12" style="text-align: center">
                                        <draggable tag="ul" :list="bom_data.drawing_data" group="bind_data" class="list-group" handle=".handle">
                                            <li
                                                    class="list-group-item"
                                                    v-for="drawing_item,drawing_index in bom_data.drawing_data"
                                                    :key="drawing_index"
                                            >
                                                <i class="fa fa-align-justify handle" style="margin-right: 10px"></i>
                                                <a v-if="drawing_item.url != ''" class="lightbox-a"
                                                   :href="base_path + drawing_item.url" data-lightbox="goods-pic" :data-title="drawing_item.url_name">
                                                    <img style="width: 20vw" :src="base_path + drawing_item.url" class="lightbox-image" />
                                                </a>
                                                <a v-else class="lightbox-a" data-lightbox="goods-pic" :data-title="drawing_item.url_name">
                                                    <img style="width: 20vw" :src="drawing_item.base64" class="lightbox-image" />
                                                </a>
                                                <div>
                                                    <span v-text="drawing_item.url_name"></span>
                                                </div>
                                            </li>
                                        </draggable>
                                    </div>
                                </div>
                                <div class="row" v-show="bom_tab_id == 2">
                                    <div style="padding: 10px">
                                        <div v-for="(check,check_idx) in bom_data.check_data" class="row" style="border: 1px solid #E2E2E2;margin:0 0 15px 0;">
                                            <div style="background-color: #f2f2f2;padding: 5px;height: 40px">
                                                <div class="col-sm-6" style="font-size: 18px;color: #2b80e8">
                                                    <span v-text="check.name"></span>
                                                </div>
                                            </div>
                                            <template v-for="(check_item,check_item_idx) in check.form_data">
                                                <div class="col-sm-12" style="display: flex;border-top:  1px solid #E2E2E2;">
                                                    <div style="width: 35%;padding: 5px;">
                                                        <span v-text="check_item.title"></span><br/>
                                                        (<span v-text="check_item.explain"></span>)
                                                    </div>
                                                    <div style="width: 65%;padding: 5px;">
                                                        <div v-if="check_item.type == 3 || check_item.type == 4 || check_item.type == 6">
                                                            <span v-for="(item,idx) in check_item.list" v-text=" check_item.list.length == idx+1 ? item :  item + ','"></span>
                                                        </div>
                                                        <div v-if="check_item.type == 7">
                                                            <span v-text="check_item.formula_val"></span>
                                                            <div class="input-group"  v-for="(item,idx) in check_item.formula_list" v-if="item.t == 2">
                                                                <input type="number" placeholder="请输入" class="form-control" number="true" :name="'formula_' + idx" v-model="item.v" maxlength="10" required readonly>
                                                                <span class="input-group-addon" v-text="item.l"></span>
                                                            </div>
                                                        </div>
                                                        <div v-if="check_item.type == 8">
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" readonly number="true" :name="'standard_val' + check_item_idx" v-model="check_item.standard_val" maxlength="10" required>
                                                                <span class="input-group-addon">标准值</span>
                                                            </div>
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" readonly number="true" :name="'standard_plus' + check_item_idx" v-model="check_item.standard_plus" maxlength="10" required>
                                                                <span class="input-group-addon">工差(+)</span>
                                                            </div>
                                                            <div class="input-group">
                                                                <input type="number" placeholder="请输入" class="form-control" readonly number="true" :name="'standard_minus' + check_item_idx" v-model="check_item.standard_minus" maxlength="10" required>
                                                                <span class="input-group-addon">工差(-)</span>
                                                            </div>
                                                        </div>
                                                        <div v-if="check_item.type == 1 || check_item.type == 2 || check_item.type == 5">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" v-show="bom_tab_id == 3">
                                    <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>编码/名称</th>
                                            <th>规格/型号</th>
                                            <th>数量</th>
                                            <th>供应商</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <template  v-for="bom_row, bom_idx in bom_data.goods_data">
                                            <tr>
                                                <td v-text="bom_idx + 1"></td>
                                                <td>
                                                    <span v-text="bom_row.code"></span><br>
                                                    <span v-text="bom_row.name"></span>
                                                </td>
                                                <td>
                                                    <span v-text="bom_row.model"></span>
                                                </td>
                                                <td>
                                                    <span v-text="bom_row.quantity"></span>(<span v-text="bom_row.deputy_unit"></span>)
                                                </td>
                                                <td>
                                                    <span v-text="bom_row.supplier_name"></span><br>
                                                    <span v-text="bom_row.remarks"></span>
                                                </td>
                                            </tr>
                                            <tr v-if="bom_row.f_list.length > 0">
                                                <td></td>
                                                <td colspan="4">
                                                    <div style="display: flex">
                                                        <div v-for="formula_item,formula_idx in bom_row.f_list" v-if="formula_item.t == 2" style="margin-right: 15px">
                                                            <span v-text="formula_item.l"></span>: <span v-text="formula_item.v"></span>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" class="btn default" @click="closeBom"><i class="fa fa-times"></i> 关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div style="width: 75vw;position: relative;">
            <div class="logic-flow-view">
                <div style="position: absolute;top: 5px;left: 5px;z-index: 999">
                    <button type="button" class="btn blue btn-outline" onclick="lfCenter()"><i class="fa fa-location-arrow"></i>&nbsp;定位</button>
                </div>
                <!-- 画布 -->
                <div id="LF-Turbo"></div>
            </div>
        </div>
    </div>
</div>
{{ partial('customNode/shipViewDetail') }}
<script>
    var pdfDoc = null;
    var ship_types = {{ shipTypes | json_encode }};
    var ship_flow_lf = null;
    var sel_tab_id = 1
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        components: {
            draggable: window.vuedraggable
        },
        methods: {
            selectDrawing(drawing_index){
                let drawing_item = app.drawing_list[drawing_index];
                if (app.select_drawing_id == drawing_item.id){
                    return;
                }
                app.select_drawing_id = drawing_item.id;
                showSpin();
                app.drawing_images = [];
                pdfjsLib.getDocument(app.base_path + drawing_item.drawing_url).promise.then((pdfDoc_) => {
                    pdfDoc = pdfDoc_;
                    let page_cnt =  pdfDoc.numPages;
                    app.renderPage(1,page_cnt,drawing_item.drawing_name,drawing_item.uid,drawing_item.drawing_url);
                })
            },
            renderPage(num,page_cnt,name,uid,drawing_url) {
                if (num > page_cnt){
                    closeSpin();
                    return;
                }
                pdfDoc.getPage(num).then((page)=> {
                    const viewport = page.getViewport({ scale: 2 });
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
                    ctx.imageSmoothingEnabled = true;
                    const renderContext = {
                        canvasContext: ctx,
                        viewport: viewport
                    };
                    const renderTask = page.render(renderContext);
                    renderTask.promise.then(()=> {
                        let base64 =  canvas.toDataURL('image/jpeg',0.95);
                        app.drawing_images.push({
                            drawing_uid : uid,
                            page_num : num,
                            base64 : base64,
                            url_name :num + '_'+ name,
                            url : '',
                            drawing_url:drawing_url,
                            status : 0
                        });
                        num++;
                        app.renderPage(num,page_cnt,name,uid,drawing_url);
                    });
                });
            },
            closeBom(){
                this.bom_show = 0;
                this.select_card_id = '';
                this.select_drawing_id = '';
            },
            viewPdf(drawing_path){
                top.layer.open({
                    title:'查看',
                    type: 2,
                    area: ['100%', '100%'],
                    content: '{{ url('common/file/pdf?path=')}}' + drawing_path,
                    end:function(){}
                });
            },
            tabClick(id) {
                this.tab_id = id;
            },
            bomTabClick(id) {
                this.bom_tab_id = id;
            },
            drawingSelectAll() {
                if (app.bom_show == 1 && app.bom_tab_id == 1) {
                    app.bom_data.drawing_data = JSON.parse(JSON.stringify(app.drawing_images));
                }
            },
            drawingDeleteAll() {
                app.bom_data.drawing_data = [];
            },
            getShipData(data){
                showSpin();
                $.post('{{ url('mes/product/getbom/'~uid) }}', data, function(rs) {
                    closeSpin();
                    if (rs.status == 'ok') {
                        app.bom_show = 1;
                        app.bom_data = rs.data;
                        app.bom_ext_data = rs.data.ext_data;
                        app.$nextTick(function() {
                            $('.bs-select').selectpicker('refresh');
                        });
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            },
        }
    });

    function lfCenter() {
        if (ship_flow_lf != null) {
            ship_flow_lf.translateCenter();
        }
    }

    $(function() {
        LogicFlow.use(DndPanel);
        LogicFlow.use(SelectionSelect);
        ship_flow_lf = new LogicFlow({
            grid: true,
            nodeTextEdit:false,
            edgeTextEdit:false,
            container:document.querySelector("#LF-Turbo")
        });
       // ship_flow_lf.extension.dndPanel.setPatternItems(ship_types);
        for (let ship_type of ship_types){
            ship_flow_lf.register(shipCard(ship_type.type));
        }
        ship_flow_lf.setTheme({
            baseEdge: {
                stroke: "#0052D9",
                strokeWidth: 2,
            },
        });

        ship_flow_lf.render({{ flowData }});

        ship_flow_lf.on("custom:ship-card-click", (data) => {
            if (app.select_card_id == data.id) {
                return;
            }
            app.select_card_id = data.id;
            app.getShipData(data);
        });
        $('.page-container-bg-solid').attr('style','overflow:hidden');
        $('.page-sidebar-closed-hide-logo').attr('style','overflow:hidden');
    });

</script>
{{ partial('form_script') }}
<style>
    #LF-Turbo {
        width: 100%;
        height: 98vh;
    }

    .logic-flow-view {
        margin: 10px;
        position: relative;
    }

    .lf-dndpanel {
        top: 50px;
    }
</style>