<?php
namespace Envsan\Modules\Equ\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Equ\Service\EquCheckFormService;
use Envsan\Modules\Equ\Service\EquCheckService;
use Envsan\Modules\Equ\Service\EquItemService;
use Envsan\Modules\Equ\Util\Constant;

/**
 * @name('检查模版')
 */
class CheckController extends SuperController
{

    /**
     * @name('列表')
     */
    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new EquCheckService();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $page->rows = $s->setDetail($page->rows->toArray());
            return json_encode($page);
        }

        $this->view->types = Constant::$check_form_type;

        $es = new EquItemService();
        $this->view->equ_list = $es->getList();

        $fs = new EquCheckFormService();
        $this->view->form_list = $fs->getList(1);
    }

    /**
     * @acl({'link':'equ:check:list'})
     */
    function viewAction($id='')
    {
        $rs = new EquCheckService();
        $row = $rs->selectById($id);
        if($row==null)
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['form_data'] = CvtUtil::emptyToArray($jrow['form_data']);
        $this->view->r_id = $id;
        $this->view->jsonLog = json_encode($jrow);
    }

    /**
     * @acl({'link':'equ:check:list'})
     */
    public function exportAction()
    {
        $s = new EquCheckService();
        $s->exportExcel();
    }
}