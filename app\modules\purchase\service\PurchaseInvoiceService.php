<?php
namespace Envsan\Modules\Purchase\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\Purchase\Model\PurchaseInstock;
use Envsan\Modules\Purchase\Model\PurchaseInstockDetail;
use Envsan\Modules\Purchase\Model\PurchaseInvoice;
use Envsan\Modules\Purchase\Model\PurchaseInvoiceDetail;
use Envsan\Modules\Purchase\Util\Constant;
use Envsan\Modules\Sys\Service\SequenceService;
use Phalcon\Mvc\User\Component;
use function AlibabaCloud\Client\json;

class PurchaseInvoiceService extends Component
{
    public function selectAll()
    {
        $column_arr = [
            't1.id',
            't1.uid',
            't1.invoice_no',
            't1.invoice_date',
            't1.invoice_money',
            't1.status',
            't1.status_name',
            't1.remarks',
            't2.order_code',
            't3.name as supplier_name',
        ];
        $builder = $this->modelsManager->createBuilder()
            ->columns(implode(',', $column_arr))
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInvoice', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.owner = '.SessionData::ownerId())
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }

    public function create()
    {
        $row = new PurchaseInvoice();
        return $this->build('create', $row);
    }

    public function update($row)
    {
        return $this->build('update', $row);
    }

    private function build($act, $row)
    {
        $type = $this->request->getPost('type', 'tstring');
        $invoice_date = $this->request->getPost('invoice_date', 'tstring');
        $order_id = $this->request->getPost('order_id', 'tstring');
        $remarks = $this->request->getPost('remarks', 'tstring');
        $files = urldecode($this->request->getPost('files', 'tstring'));
        $ext_data = str_replace('%2B','+',urldecode($this->request->getPost('ext_data', 'tstring')));
        $detail_ids = $this->request->getPost('detail_ids', 'tstring');
        if (empty($type) || empty($invoice_date) || empty($order_id) || empty($detail_ids)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $table = new TableService();
        $files = CvtUtil::emptyToArray($files);
        $ext_data = CvtUtil::emptyToArray($ext_data);
        $now = DateUtil::now();
        $user = SessionData::user();
        $this->db->begin();
        try {
            $invoice_money = PurchaseInstockDetail::sum([
                'column' => 'total_money',
                'conditions' => 'del_flag = 0 and id IN ('.$detail_ids.')',
            ]);

            $row->order_id = $order_id;
            $row->invoice_date = $invoice_date;
            $row->invoice_money = $invoice_money;
            $row->remarks = CvtUtil::blankToNull($remarks);
            $row->files = CvtUtil::arrayToNull($files);
            $row->ext_data = CvtUtil::arrayToNull($ext_data);
            $row->ext_val = json_encode($table->getFormDataValue($ext_data),JSON_UNESCAPED_UNICODE);
            if ($type == 1) {
                $row->status = 10;
            } else {
                $row->status = 20;
            }
            $row->status_name = Constant::$purchase_invoice_status[$row->status];
            $row->update_date = $now;
            $row->update_by = $user->id;
            if ($act == 'create') {
                $ss = new SequenceService();
                $row->uid = UUID::make();
                $row->invoice_no = $ss->useSequence(19);
                $row->create_date = $now;
                $row->create_by = $user->id;
                $row->del_flag = 0;
                $row->group_id = $user->group_id;
                $row->owner = $user->owner;
            }
            if (!$row->save()) {
                throw new \Exception("purchase_invoice表更新失败");
            }

            $detail_rows = PurchaseInvoiceDetail::find(['del_flag = 0 and invoice_id = ?1', 'bind' => [1 => $row->id]]);
            $detail_ids = explode(',', $detail_ids);
            $old_ids = [];
            foreach ($detail_rows as $detail_row)
            {
                $old_ids[] = $detail_row->instock_detail_id;
                if (!in_array($detail_row->instock_detail_id, $detail_ids)) {
                    $detail_row->del_flag = 1;
                    $detail_row->update_date = $now;
                    $detail_row->update_by = $user->id;
                    if (!$detail_row->save()) {
                        throw new \Exception("purchase_invoice_detail表更新失败");
                    }
                }
            }

            $new_ids = array_diff($detail_ids, $old_ids);
            foreach ($new_ids as $instock_detail_id)
            {
                $detail_row = new PurchaseInvoiceDetail();
                $detail_row->invoice_id = $row->id;
                $detail_row->instock_detail_id = $instock_detail_id;
                $detail_row->create_date = $now;
                $detail_row->create_by = $user->id;
                $detail_row->update_date = $now;
                $detail_row->update_by = $user->id;
                $detail_row->del_flag = 0;
                $detail_row->group_id = $user->group_id;
                $detail_row->owner = $user->owner;
                if (!$detail_row->save()) {
                    throw new \Exception("purchase_invoice_detail表更新失败");
                }
            }

            $this->db->commit();
        } catch (\Exception $e){
            $this->db->rollback();
            Logger::error($e->getMessage());
            return $e->getMessage();
        }
        return '';
    }

    public function selectById($id)
    {
        return PurchaseInvoice::findFirst(['id = ?1', 'bind' => [1 => $id]]);
    }

    public function selectByUid($uid)
    {
        return PurchaseInvoice::findFirst(['uid = ?1', 'bind' => [1 => $uid]]);
    }

    public function deleteByUid($uid)
    {
        $row = $this->selectByUid($uid);
        if ($row == false || $row->del_flag == 1) {
            return '';
        }

        $row->del_flag = 1;
        $row->update_date = DateUtil::now();
        $row->update_by = SessionData::user()->id;
        return $row->save() ? '' : ErrorHelper::UNKOWN;
    }

    public function getInstockDetailList($order_id, $invoice_id = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id as instock_detail_id,
                t1.goods_code,
                t1.goods_name,
                t1.goods_spec,
                t1.goods_model,
                t1.goods_unit,
                t1.price,
                t1.quantity,
                t1.total_money,
                t2.code as instock_code,
                t2.instock_date,
                1 as show
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInstock', 't1.instock_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t2.status >= 20 and t3.id is null and t2.order_id = ?1', [1 => $order_id])
            ->orderBy('t1.id');

        if (empty($invoice_id)) {
            $builder->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInvoiceDetail',
                't1.id = t3.instock_detail_id and t3.del_flag = 0', 't3');
        } else {
            $builder->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInvoiceDetail',
                't1.id = t3.instock_detail_id and t3.del_flag = 0 and t3.invoice_id <> '.$invoice_id, 't3');
        }

        $rows = $builder->getQuery()->execute()->toArray();
        foreach ($rows as &$row)
        {
            $row['price'] = CvtUtil::emptyToDouble($row['price']);
            $row['quantity'] = CvtUtil::emptyToDouble($row['quantity']);
            $row['total_money'] = CvtUtil::emptyToDouble($row['total_money']);
        }
        return $rows;
    }

    public function getInvoiceDetailList($invoice_id)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.instock_detail_id,
                t2.goods_code,
                t2.goods_name,
                t2.goods_spec,
                t2.goods_model,
                t2.goods_unit,
                t2.price,
                t2.quantity,
                t2.total_money,
                t3.code as instock_code,
                t3.instock_date
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInvoiceDetail', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 't1.instock_detail_id = t2.id', 't2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInstock', 't2.instock_id = t3.id', 't3')
            ->where('t1.del_flag = 0 and t1.invoice_id = ?1', [1 => $invoice_id])
            ->orderBy('t1.id');
        return $builder->getQuery()->execute();
    }

    public function searchAll()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.status,
                t1.status_name,
                t1.invoice_no,
                t1.invoice_date,
                t1.remarks,
                t1.ext_val,
                t2.order_code,
                t3.name as supplier_name,
                t99.goods_code,
                t99.goods_name,
                t99.goods_spec,
                t99.goods_model,
                t99.goods_unit,
                t99.price,
                t99.quantity,
                t99.total_money
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseInvoice', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1.order_id = t2.id','t2')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't2.supplier_id = t3.id', 't3')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInvoiceDetail', 't1.id = t4.invoice_id', 't4')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseInstockDetail', 't4.instock_detail_id = t99.id', 't99')
            ->where('t1.del_flag = 0 and t4.del_flag = 0 and t99.del_flag = 0 and t1.owner = ?1', [1 => SessionData::ownerId()])
            ->orderBy('t1.id desc');
        $table = new TableService();
        $table->conditionExtend($builder);
        return $builder;
    }
}