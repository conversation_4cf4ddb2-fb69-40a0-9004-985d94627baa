{% do assets.collection('js').addJs('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/dtpicker/locales/bootstrap-datetimepicker.zh-CN.js') %}
{% do assets.collection('css').addCss('static/global/plugins/dtpicker/bootstrap-datetimepicker.min.css') %}
{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <div class="row">
        <div class="col-sm-3">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">到货信息</span>
                    </div>
                </div>
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 72vh;overflow-x: hidden;overflow-y: auto">
                            <div id="form_data" class="row">
                                <!-- <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label"><span class="required">*</span>业务类型</label>
                                        <div class="col-sm-9">
                                            <select
                                            id="business_type"
                                            class="form-control"
                                            v-model="business_type"
                                            required
                                            >
                                            <option value="">请选择业务类型</option>
                                            <option v-for="row in business_types" :value="row.code">
                                                ${ row.name }
                                            </option>
                                            </select>
                                        </div>
                                    </div>
                                </div> -->
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label"><span class="required"> * </span>单据号</label>
                                        <div class="col-sm-9">
                                            <div style="display: flex;flex-direction: row">
                                                <input style="width: 50%" type="text" class="form-control" name="re_code" v-model="re_code" readonly>
                                                <input style="width: 50%" type="text" class="form-control" name="code" v-model="code" required :readonly="isEditMode">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label"><span class="required">*</span>到货日期</label>
                                        <div class="col-sm-9">
                                            <div class="input-group">
                                                <input type="text" class="form-control date dtpicker" placeholder="请输入到货日期" name="receipt_date" v-model="receipt_date" required/>
                                                <span class="input-group-addon date-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label"><span class="required">*</span>外委加工单</label>
                                        <div class="col-sm-9">
                                            <select
                                            id="order_id"
                                            class="form-control"
                                            v-model="order_id"
                                            required
                                            >
                                            <option value="">请选择外委加工单</option>
                                            <option v-for="row in order_list" :value="row.id">
                                                ${ row.order_code  } /  ${ row.supplier_name  }
                                            </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
        
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            <span class="required"> * </span>部门
                                        </label>
                                        <div class="col-sm-9">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="department_name" v-model="department_name" required readonly>
                                                <span class="input-group-btn">
                                                    <button type="button" class="btn btn-default btn-flat" @click="selectGroup"><i class="fa fa-search"></i></button>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label"><span class="required">*</span>币种</label>
                                        <div class="col-sm-9">
                                            <select
                                            id="currency"
                                            class="form-control"
                                            v-model="currency"
                                            required
                                            >
                                            <option value="">请选择币种</option>
                                            <option value="人民币">人民币</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label"><span class="required">*</span>汇率</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" name="exchange_rate" v-model="exchange_rate" maxlength="50" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">备注</label>
                                        <div class="col-sm-9">
                                            <textarea style="resize: none;" placeholder="备注" class="form-control" v-model="remarks" maxlength="200" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                {{ partial('form') }}
                            </div>
                        </div>
                        <div class="form-actions right">
                            <button type="button" @click="save" class="btn btn-primary">保存</button>
                            <button type="button" @click="commit" class="btn btn-warning">提交审批</button>
                            <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-social-dribbble font-green"></i>
                        <span class="caption-subject font-green bold uppercase">到货明细</span>
                    </div>
                    <div class="actions">
                        <div class="actions" style="display: flex;align-items: center">
                            <button type="button" class="btn btn-outline yellow" onclick="choose()">
                                <span>选择</span>
                            </button>
                            <button style="margin-left: 10px" type="button" class="btn btn-outline red" @click="delApply">
                                <span>删除</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="zh-table-box">
                        <div class="zh-table-box-content">
                            <table class="table table-bordered table-big">
                                <thead>
                                <tr>
                                    <th>
                                        <a @click="applySelect">
                                            <span v-if="apply_sel == 0"> 全选</span>
                                            <span v-else> 取消</span>
                                        </a>
                                    </th>
                                    <th>产品名称</th>
                                    <th>规格型号</th>
                                    <th>委外工序</th>
                                    <th>入库数量</th>
                                    <th>计价数量</th>
                                    <th>换算率</th>
                                    <th>税率(%)</th>
                                    <th>未税单价</th>
                                    <th>含税单价</th>
                                    <th>未税金额</th>
                                    <th>税额</th>
                                    <th>含税金额</th>
                                    <th>检验</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-if="apply_list.length == 0">
                                    <td colspan="14" style="text-align: center;">没有数据</td>
                                </tr>
                                <tr v-else v-for="apply, apply_idx in apply_list">
                                    <td>
                                        <a @click="apply.sel == 1 ? apply.sel = 0 : apply.sel = 1">
                                            <i v-if="apply.sel == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                            <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                        </a>
                                    </td>
                                    <td v-text="apply.product_name"></td>
                                    <td v-text="apply.product_code"></td>
                                    <td v-text="apply.bom_name"></td>
                                    <td>
                                        <div class="input-group">
                                            <input type="number" 
                                                class="form-control" 
                                                v-model="apply.quantity" 
                                                step="1" 
                                                min="0"
                                                @input="onQuantityChange(apply)"
                                                @keyup="calculateAmount(apply)"
                                            >
                                            <span class="input-group-addon" v-text="apply.goods_deputy_unit"></span>
                                        </div>
                                    </td>
                                    <td v-text="(apply.pricing_quantity || 0 ) + '(' + apply.goods_unit + ')'"></td>
                                    <td v-text="apply.conversion_rate"></td>
                                    <td v-text="apply.tax_rate * 100"></td>
                                    <td v-text="apply.price"></td>
                                    <td v-text="apply.price_hs"></td>
                                    <td v-text="safeNumber(apply.total_money).toFixed(2)"></td>
                                    <td v-text="safeNumber(apply.tax_money).toFixed(2)"></td>
                                    <td v-text="safeNumber(apply.total_money_hs).toFixed(2)"></td>
                                    <td v-text="apply.check_status"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{ partial('check') }}
{{ partial('common_utils') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {
            ...{{ jsonData }},
            apply_sel: 0,
            isEditMode: {% if dispatcher.getActionName() == 'edit' %}true{% else %}false{% endif %},
        },
        created() {
            // if (!this.business_type) {
            //     this.business_type = ''
            // }
            if (!this.order_id) {
                this.order_id = ''
            }     
        },
        watch: {
            // 供应商变化时自动填充信息
            order_id: {
                handler(order_id) {
                    this.apply_list = []
                    if (order_id) {
                        const order = this.order_list.find(item => item.id == order_id);
                        if (order) {
                            this.order_code = order.order_code;
                            this.supplier_id = order.supplier_id;
                            this.supplier_code = order.supplier_code;
                            this.supplier_name = order.supplier_name;
                        } else {
                            this.order_code = '';
                            this.supplier_id = '';
                            this.supplier_code = '';
                            this.supplier_name = '';
                        }
                    } else {
                        this.order_code = '';
                        this.supplier_id = '';
                        this.supplier_code = '';
                        this.supplier_name = '';
                    }
                }
            },
            // 业务类型变更
            // business_type: {
            //     immediate: true,
            //     handler(code) {
            //         if (code) {
            //             const business_type = this.business_types.find(item => item.code == code);
            //             if (business_type) {
            //                 this.business_type_name = business_type.name;
            //             }
            //         } else {
            //             this.business_type_name = '';
            //         }
            //     }
            // },
        },
        methods: {
            submit: function (type) {
                {% if dispatcher.getActionName() == 'edit' %}
                var url = '{{ url('purchase/wwreceipt/edit/' ~ uid) }}';
                {% else %}
                var url = '{{ url('purchase/wwreceipt/create') }}';
                {% endif %}

                commonAjaxRequest(url, {
                    type : type,
                    // business_type:app.business_type,
                    order_id: app.order_id,
                    order_code : app.order_code,
                    supplier_id : app.supplier_id,
                    supplier_code : app.supplier_code,
                    supplier_name : app.supplier_name,
                    // business_type_name:app.business_type_name,
                    re_code:app.re_code,
                    code:app.code,
                    receipt_date:app.receipt_date,
                    department_id:app.department_id,
                    department_name:app.department_name,
                    currency:app.currency,
                    exchange_rate:app.exchange_rate,
                    detail_data:app.apply_list,
                    remarks:app.remarks,
                    ext_data:encodeURI(JSON.stringify(app.ext_data)).replace(/\+/g,'%2B')
                }, function(rs) {
                    top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                });
                
            },
            save:function (e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                if (!this.handleValidate()) {
                    return;
                }
            
                this.submit(1);
            },
            commit:function (e){
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                if (!this.handleValidate()) {
                    return;
                }
                var dlg = top.layer.confirm('确认提交审批吗?', function() {
                    top.layer.close(dlg);
                    app.submit(2);
                });
            },
            applySelect(){
                if (this.apply_sel == 0){
                    this.apply_sel = 1;
                } else {
                    this.apply_sel = 0;
                }
                for (let item of this.apply_list){
                    item.sel = this.apply_sel;
                }
            },
            selectGroup : function () {
                top.window.group_layer_result=null;
                top.layer.open({
                    title:'选择组织',
                    type: 2,
                    area: ['35em', '60%'],
                    content: '{{ url('sys/group/sel') }}',
                    end:function(){
                        if(top.window.group_layer_result!=null){
                            var rs = top.window.group_layer_result;
                            app.department_id = rs.id;
                            app.department_name = rs.name;
                        }
                    }
                });
            },
            delApply: function() {
                let apply_list = [], goods_obj = {};
                for (let item of this.apply_list) {
                    if (!goods_obj.hasOwnProperty(item.id)) {
                        goods_obj[item.id] = 0;
                    }

                    if (item.sel == 0) {
                        apply_list.push(item);
                        goods_obj[item.id]++;
                    }
                }
                if (apply_list.length === this.apply_list.length) {
                    toastr.error('请选择删除记录！');
                    return;
                }
                this.apply_list = apply_list;
                app.$forceUpdate();
                
            },
            // 计算库存为单位的数量
            onQuantityChange(item) {
                if (item.quantity !== '' && item.quantity !== null) {
                    const quantityValue = this.safeNumber(item.quantity);
                    const rateValue = this.safeNumber(item.conversion_rate, 1);
                    item.pricing_quantity = (quantityValue * rateValue).toFixed(2);
                }
            },
            // 计算金额相关字段
            calculateAmount: function(item) {
                
                let quantity = this.safeNumber(item.pricing_quantity);
                let price = this.safeNumber(item.price);
                let price_hs = this.safeNumber(item.price_hs);
                let tax_rate = this.safeNumber(item.tax_rate);
                
                // 计算金额 = 数量 × 未税单价
                let total_money = quantity * price;
                
                // 计算税额 = 金额 × 税率 / (1 + 税率)
                let tax_money = total_money * tax_rate;
                
                // 价税合计 = 金额
                let total_money_hs = quantity * price_hs;;
                
                // 更新到对象中（保留2位小数）
                this.$set(item, 'total_money', total_money.toFixed(2));
                this.$set(item, 'tax_money', tax_money.toFixed(2));
                this.$set(item, 'total_money_hs', total_money_hs.toFixed(2));
            },
            handleValidate() {
                
                // 检查到货明细列表
                if (!this.apply_list || this.apply_list.length === 0) {
                    toastr.error('到货明细不能为空，请添加明细项');
                    return false;
                }
                
                // 检查明细项中的数量
                for (let i = 0; i < this.apply_list.length; i++) {
                    let item = this.apply_list[i];
                    if (!isDecimal(item.quantity) || item.quantity <= 0) {
                        toastr.error('【序号' + (i + 1) + '】入库数量只能是大于0的数字。');
                        return false;
                    }
                    if (parseInt(item.quantity)  >  Math.round(item.out_cnt - item.in_cnt) ){
                        toastr.error('【序号' + (i + 1) + '】入库数量不能大于出库数量');
                        return false;
                    }
                }
               
                return true;
            },
            // 辅助方法：验证小数格式
            isValidDecimal(value, integerDigits, decimalDigits) {
                // 正则表达式验证小数格式
                const pattern = new RegExp(`^\\d{1,${integerDigits}}(\\.\\d{1,${decimalDigits}})?$`);
                return pattern.test(value);
            },
            // 安全转换函数
            safeNumber: function(value, defaultValue = 0) {
                if (value === '' || value === null || value === undefined) {
                    return defaultValue;
                }
                const num = Number(value);
                return isNaN(num) ? defaultValue : num;
            },
        }
    });

    function choose() {
        if (app.order_id == '') {
            toastr.error('必须选择订单后才能选择明细');
            return;
        }
        ids = [];
        for (let item of app.apply_list) {
            ids.push(item.id);
        }
        top.window.layer_result2 = '';
        top.layer.open({
            title: '添加采购到货明细',
            type: 2,
            area: ['100%', '100%'],
            content: '{{url("purchase/wwreceipt/addapply/0/")}}' + app.order_id + '/' + ids,
            end: function() {
                if (top.window.layer_result2 == 'ok') {
                    addDetailList(top.window.layer_data2);
                }
            }
        });
    }

    function addDetailList(apply_list) {
        paramLoop: for (let i = 0; i < apply_list.length; i++) {
            
            apply_row = apply_list[i];

            let quantity = Math.round(app.safeNumber(apply_row.out_cnt) - app.safeNumber(apply_row.in_cnt));
            let conversion_rate = app.safeNumber(apply_row.conversion_rate, 1);
            let pricing_quantity = quantity * conversion_rate;

            apply_row['quantity'] = quantity;
            apply_row['pricing_quantity'] = pricing_quantity;
            for (let j = 0; j < app.apply_list.length; j++) {
                if (app.apply_list[j].id == apply_row.id) {
                    continue paramLoop;
                }
            }
            app.apply_list.push(apply_row);
            app.calculateAmount(apply_row);
        }
    }

    $('.dtpicker').datetimepicker({
        language: "zh-CN",
        startView: 2,
        minView: 2,
        todayBtn: false,
        autoclose: true,
        format: 'yyyy-mm-dd'
    }).on('changeDate', function (ev) {
        app[$(this).attr('name')] = $(this).val();
    });
</script>
{{ partial('form_script') }}