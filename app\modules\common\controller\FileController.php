<?php

namespace Envsan\Modules\Common\Controller;

use Envsan\Common\Component\Logger;
use Envsan\Modules\Common\Service\FileService;

/**
 * @skipacl
 */
class FileController extends SuperController
{
    public function uploadAction($folder_name = '')
    {
        $this->setJsonResponse();
        $s = new FileService();
        return json_encode($s->uploadImage($folder_name));
    }

    public function uploadfileAction($folder_name = '')
    {
        $this->setJsonResponse();
        $s = new FileService();
        return json_encode($s->uploadFile($folder_name));
    }

    public function signAction()
    {
        $this->setJsonResponse();
        $data = $this->request->getPost('data');
        $password = md5($this->config->upyun->password);
        $signature = base64_encode(hash_hmac("sha1", $data, $password, true));
        return json_encode(['data' => $signature]);
    }

    public function pdfAction(){
        $path = $this->request->get('path', 'tstring');
        $oss_util = new FileService();
        $this->view->pdf = $oss_util->getImagePath() . $path;
    }
}