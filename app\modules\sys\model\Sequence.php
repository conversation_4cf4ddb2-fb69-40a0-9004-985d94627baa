<?php

namespace Envsan\Modules\Sys\Model;

use Envsan\Common\Model\BaseModel;

use Envsan\Common\Util\ModelUtil;

class Sequence extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", length=4, nullable=false)
     */
    public $id;

    /**
     *
     * @var integer
     * @Column(type="integer", length=4, nullable=false)
     */
    public $type;

    /**
     *
     * @var string
     * @Column(type="string", length=2, nullable=true)
     */
    public $header;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $body;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $seq_fmt;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=false)
     */
    public $num_length;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     */
    public $remarks;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'sys_sequence';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return Sequence[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return Sequence
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
