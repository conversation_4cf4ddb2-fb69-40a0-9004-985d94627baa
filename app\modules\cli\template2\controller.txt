<?php
namespace Envsan\Modules\{{Module}}\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Data\JsonData;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Common\Service\TableService;
use Envsan\Modules\{{Module}}\Model\{{Model}};
use Envsan\Modules\{{Module}}\Service\{{Model}}Service;

class {{ModelWithoutModule}}Controller extends SuperController
{
    private $page_id = 1;

    public function listAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $s = new {{Model}}Service();
            $builder = $s->selectAll();
            $page = $this->getPagination($builder);
            $table = new TableService();
            $page->data = $table->getTableData($this->page_id, $page->rows, $builder);
            return json_encode($page);
        }
        $this->view->page_id = $this->page_id;
    }

    public function createAction()
    {
        $s = new {{Model}}Service();
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->create();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = (new {{Model}}())->toArray();
        $jrow['ext_data'] = $table->getFormData($this->page_id);
        $this->view->json{{Model}} = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @acl({'link':'{{module}}:{{model}}:create'})
     */
    public function editAction($uid = '')
    {
        $s = new {{Model}}Service();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $ret = new JsonData();
            $ret->message = $s->update($row);
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $table = new TableService();

        $jrow = $row->toArray();
        $jrow['ext_data'] = $table->margeFormData($this->page_id, CvtUtil::emptyToArray($row->ext_data));
        $this->view->json{{Model}} = json_encode($jrow);

        $this->view->uid = $uid;
        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
        $this->view->pick('{{model}}/create');
    }

    /**
     * @acl({'link':'{{module}}:{{model}}:create'})
     */
    public function deleteAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new {{Model}}Service();
            $ret = new JsonData();
            $ret->message = $s->deleteByUid($this->request->getPost('uid', 'tstring'));
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    /**
     * @skipacl
     */
    function viewAction($uid)
    {
        $s = new {{Model}}Service();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        $jrow = $row->toArray();
        $jrow['ext_data'] = CvtUtil::emptyToArray($jrow['ext_data']);
        $this->view->json{{Model}} = json_encode($jrow);

        $this->view->extDataName = 'ext_data';
        $this->view->extDataCnt = 6;
    }

    /**
     * @skipacl
     */
    public function exportAction()
    {
        $s = new {{Model}}Service();
        $builder = $s->selectAll();
        $table = new TableService();
        $table->exportExcel($this->page_id, $builder);
    }
}