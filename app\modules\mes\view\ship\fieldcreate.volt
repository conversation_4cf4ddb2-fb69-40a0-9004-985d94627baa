{{ assets.outputJs('validate') }}

<div id="app" class="page-content">
    <div class="portlet light" style="margin-bottom: 0;">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-social-dribbble font-green"></i>
                <span class="caption-subject font-green bold">字段管理</span>
            </div>
        </div>
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body">
                    <div class="form-group">
                        <label>字段名称<span class="required"> * </span></label>
                        <div>
                            <input type="text" class="form-control" name="name" v-model="name" placeholder="请输入名称" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>输入类型<span class="required"> * </span></label>
                        <div>
                            <select class="bs-select form-control" name="type" v-model="type" required>
                                <option value="">请选择</option>
                                {% for key,val in input_types %}
                                    <option value="{{ key }}">{{ val }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-group" v-if="type == 2">
                        <label>数字单位</label>
                        <div>
                            <input type="text" class="form-control" name="unit" v-model="unit" placeholder="请输入单位" >
                        </div>
                    </div>
                    <div class="form-group" v-if="type == 3 || type == 4">
                        <label>选项<span class="required"> * </span></label>
                        <div>
                            <div style="display: flex">
                                <input type="text" maxlength="20" v-model="select_value" class="form-control" placeholder="请输入选项"/>
                                <button type="button"  class="btn btn-outline yellow" @click="addSelect"><i class="fa fa-plus"></i> 添加</button>
                            </div>
                            <div>
                                <table class="factory-table">
                                    <tbody>
                                        <tr v-for="item,idx in list">
                                        <td v-text="item"></td>
                                        <td>
                                            <a href="javascript:;" @click="delSelect(idx)" style="color: red;font-size: 20px">
                                                <i class="fa fa-times"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>字段说明</label>
                        <div>
                            <textarea  placeholder="请输入字段说明" class="form-control" name="explain" v-model="explain" maxlength="200" rows="3" style="resize: none; "></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{ partial('check') }}
<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonField }},
        methods: {
            submit: function(e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;
                if (app.type == 3 || app.type == 4 || app.type == 5){
                    if (app.list.length == 0){
                        toastr.error('请输入选项');
                        return;
                    }
                }
                {% if dispatcher.getActionName() == 'fieldedit' %}
                var url = '{{ url('mes/ship/fieldedit/' ~ field_id) }}';
                {% else %}
                var url = '{{ url('mes/ship/fieldcreate') }}';
                {% endif %}
                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                })
            },
            addSelect(){
                if (this.select_value == ''){
                    return;
                }
                for(let item of this.list){
                    if (this.select_value == item){
                        toastr.error('选项值重复!');
                        return;
                    }
                }
                this.list.push(this.select_value);
                this.select_value = '';
            },
            delSelect(idx){
                this.list.splice(idx,1);
            },
        }
    });

    $('.bs-select').selectpicker({
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    });
</script>
<style>
    .factory-table {
        margin-bottom: 0;
        width: 100%;
        margin-top: 10px
    }

    .factory-table > thead > tr > th, .factory-table > tbody > tr > td {
        border: 1px solid #F2F2F2;
        height: 35px;
        padding-left: 5px ;
        background-color: #ffffff;
    }

    .factory-table > thead > tr > th {
        font-weight: normal;
        background-color: #3598DC;
    }

</style>