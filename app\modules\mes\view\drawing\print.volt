{% do assets.collection('js').addJs('static/global/plugins/qrcode/qrcode.min.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.js') %}
{% do assets.collection('js').addJs('static/global/plugins/pdfjs-dist/pdf.worker.js') %}
<!-- Main content -->
<div id="app">
    <div style="display: flex">
        <div style="width:80%;background-color: #f2f2f2;height: 100vh;overflow: auto;text-align: center">
            <div class="container-print-page">
                <div v-for="drawing_item in drawing_data" style="margin: 10px">
                    <img v-if="drawing_item.base64 != '' && drawing_item.print_flag == 1" :style="{width: width + 'px'}" :src="drawing_item.base64"/>
                </div>
            </div>
        </div>
        <div style="width:20%;background-color: #FFF;height: 100vh;overflow: auto;">
            <div style="height:100vh;border-right: 1px #E2E2E2 solid;">
                <div class="portlet-body form">
                    <form id="form" class="form-horizontal">
                        <div class="form-body" style="height: 87vh;overflow-y: auto;">
                            <div id="form_data">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">打印宽度</label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <input type="number" class="form-control" name="width" v-model="width" maxlength="5"/>
                                                    <span class="input-group-addon">PX</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-sm-4 control-label">打印页</label>
                                            <div class="col-sm-8">
                                                <div class="btn-group">
                                                    <button type="button" class="btn blue btn-outline btn-sm" @click="selPrint(1)">全选</button>
                                                    <button type="button" class="btn blue btn-outline btn-sm"  @click="selPrint(0)">全未选</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                            <tbody>
                                            <template v-for="d_item,d_index in drawing_data">
                                                <tr>
                                                    <td>
                                                        <a @click="d_item.print_flag == 1 ? d_item.print_flag = 0 : d_item.print_flag = 1">
                                                            <i v-if="d_item.print_flag == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                                            <i v-else class="fa fa-check-square" style="color: #0080FF;font-size: 20px;"></i>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <span v-text="'第' + (d_index+1) + '页'"></span>
                                                    </td>
                                                    <td>
                                                        <span v-text="d_item.url_name"></span>
                                                    </td>
                                                </tr>
                                            </template>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-sm-12" style="text-align: right;margin-top: 15px">
                                        <button type="button" class="btn blue" @click="printPreview"><i class="fa fa-print"></i> 打印</button>
                                        <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 关闭</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div id="qrcode" style="display: none"></div>
</div>

<script>
    var pdfDocData = {};
    var app = new Vue({
        el: '#app',
        data: {{ jsonData }},
        created(){
            setTimeout(()=>{
                this.createDoc(0,()=>{
                    this.renderPage(0);
                });
            },1000);
        },
        methods: {
            createDoc(idx,cb){
                if (idx >= this.drawing_data.length){
                    cb();
                    return;
                }
                if (pdfDocData['_' + this.drawing_data[idx].drawing_uid]){
                    idx++;
                    this.createDoc(idx,cb);
                } else {
                    pdfjsLib.getDocument(this.base_path + this.drawing_data[idx].drawing_url).promise.then((pdfDoc_) => {
                        pdfDocData['_' + this.drawing_data[idx].drawing_uid] = pdfDoc_;
                        idx++;
                        this.createDoc(idx,cb);
                    });
                }
            },
            renderPage(idx){
                if (idx >= this.drawing_data.length){
                    closeSpin(null);
                    return;
                }
                let pdfDoc = pdfDocData['_' + this.drawing_data[idx].drawing_uid];
                pdfDoc.getPage(parseInt(this.drawing_data[idx].page_num)).then((page)=> {
                    let viewport = page.getViewport({ scale: 2});
                    let rotation_type = 0;
                    console.log(viewport.height,viewport.width);
                    if (viewport.height < viewport.width){
                        viewport = page.getViewport({ scale: 2 ,rotation:90});
                        rotation_type = 1;
                        if (viewport.height < viewport.width){
                            viewport = page.getViewport({ scale: 2 ,rotation:360});
                        }
                    }
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    ctx.imageSmoothingEnabled = true; // 默认开启
                    ctx.imageSmoothingQuality = 'high';
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;
                    canvas.style.width = '100%';
                    const renderContext = {
                        canvasContext: ctx,
                        viewport: viewport
                    };
                    let rate = 0;
                    let width = 0;
                    if (rotation_type == 0){
                        rate = 0.1048;
                        width = canvas.width;
                    } else {
                        rate = 0.0746;
                        width = canvas.height;
                    }
                    let bj_rate = (rate/4).toFixed(4);
                    let code_w =  ((width*rate)).toFixed(4);
                    let bj =  ((width*bj_rate)).toFixed(4);
                    const renderTask = page.render(renderContext);
                    renderTask.promise.then(()=> {
                        var img = $('#qrcode').find('img');
                        img = img[0];
                        if (rotation_type == 0){
                            ctx.drawImage(img, width - code_w - bj , bj, code_w, code_w);
                            ctx.restore();
                            ctx.font = bj + 'px "Times New Roman", Georgia, serif';
                            ctx.fillText((idx+1) + '/' + this.drawing_data.length,  canvas.width - code_w - (canvas.width * 0.15), bj*2 );
                            ctx.fillText(this.product_code, canvas.width - code_w -  (canvas.width * 0.35), bj*2);
                            ctx.fillText(this.code, canvas.width - code_w - (canvas.width * 0.55), bj*2 );
                        } else {
                            ctx.drawImage(img, canvas.width - code_w - bj ,canvas.height - code_w - bj, code_w, code_w);
                            ctx.font = bj + 'px "Times New Roman", Georgia, serif';
                            ctx.translate(canvas.width - bj*2, canvas.height - code_w - bj - (canvas.height * 0.50)); // 移动到目标位置
                            ctx.rotate(Math.PI / 2); // 旋转画布
                            ctx.fillText(this.code, 0,0);
                            ctx.fillText(this.product_code, canvas.height * 0.2,0);
                            ctx.fillText((idx+1) + '/' + this.drawing_data.length, canvas.height * 0.4,0); // 在旋转后的坐标系中绘制
                        }
                        ctx.restore();
                        let base64 =  canvas.toDataURL('image/jpeg', 1);
                        this.drawing_data[idx].base64 = base64;
                        idx++;
                        this.renderPage(idx);
                    });
                });
            },
            printPreview(){
                const iframe  = document.createElement("iframe");
                const f  = document.body.appendChild(iframe);
                iframe.id = "myIframe";
                iframe.setAttribute(
                    "style",
                    "position:absolute;width:0;height:0;top:0px;left:0px;"
                );
                const w = f.contentWindow || f.contentDocument;
                // eslint-disable-next-line prefer-const
                const doc = f.contentDocument || f.contentWindow.document;
                doc.open();
                doc.write($('.container-print-page').html());
                doc.close();
                w.print();
            },
            selPrint(flag){
                for(let item of this.drawing_data){
                    item.print_flag = flag;
                }
            },
            pageChange(){
                this.width = this.page_width;
            }
        }
    });
    var qrcode = new QRCode(document.getElementById("qrcode"), {
        width : 256,
        height : 256,

    });
    qrcode.makeCode('{{ uid }}');

    $(function (){

        showSpin();
    })
</script>