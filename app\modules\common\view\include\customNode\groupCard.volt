<script>
    class GroupData extends GroupNode.view {}

    class GroupDataModel extends GroupNode.model {
        initNodeData(data) {
            super.initNodeData(data);
            const noTarget = {
                message: "不允许连接分组",
                validate: (sourceNode, targetNode, sourceAnchor, targetAnchor) => {
                    return false;
                }
            };
            this.targetRules.push(noTarget);
            this.isRestrict = true;
            this.resizable = false;
            this.foldable = true;
            this.width = 500;
            this.height = 600;
            this.foldedWidth = 200;
            this.foldedHeight = 40;
            this.text = {
                value:  this.text.value ? this.text.value : '',
                x: data.x - (this.width/2) + 30,
                y: data.y - (this.height/2) + 20,
                editable: false
            };
        }
        getNodeStyle() {
            const style = super.getNodeStyle();
            style.stroke = "#989891";
            style.strokeWidth = 1;
            style.strokeDasharray = "3 3";
            if (this.isSelected) {
                style.stroke = "rgb(124, 15, 255)";
            }
            if (this.isFolded) {
                style.fill = "#F2F2F2";
            }
            return style;
        }
        getTextStyle() {
            const style = super.getTextStyle();
            style.fontSize = 16;
            style.fill = '#007CF9';
            style.fontWeight = 'bold';
            style.textAnchor = 'left';
            return style;
        }
        updateTextPosition() {
            this.text.x = this.x - (this.width/2) + 30;
            this.text.y = this.y - (this.height/2) + 20;
        }
        getAnchorStyle() {
            const style = super.getAnchorStyle();
            style.stroke = "transparent";
            style.fill = "none";
            style.hover.stroke = "transparent";
            return style;
        }

        createId() {
            return getUuid2();
        }
    }

    function groupCard(type){
        return {
            type: type,
            view: GroupData,
            model: GroupDataModel
        }
    }
</script>