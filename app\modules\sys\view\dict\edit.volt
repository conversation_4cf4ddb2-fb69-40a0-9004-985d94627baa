{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" autocomplete="off">
                <div class="form-body" style="padding: 0">
                    <div class="search-table">
                        <div style="margin-bottom: 10px;display: flex;justify-content: space-between;align-items: center;">
                            <div>
                                <button type="button" class="btn blue btn-outline" style="margin-right: 20px;" @click="fieldSort(1)"><i class="fa fa-long-arrow-up fa-fw"></i>&nbsp;向上</button>
                                <button type="button" class="btn blue btn-outline" @click="fieldSort(2)"><i class="fa fa-long-arrow-down fa-fw"></i>&nbsp;向下</button>
                            </div>
                            <div>
                                <button type="button" class="btn blue btn-outline" @click="addDetail"><i class="fa fa-plus"></i>&nbsp;添加项目</button>
                            </div>
                        </div>
                        <table class="table table-bordered">
                            <thead class="bg-blue">
                                <tr>
                                    <th style="width: 60px;">排序</th>
                                    <th style="width: 60px;">序号</th>
                                    <th>默认值</th>
                                    <th>编号</th>
                                    <th>名称</th>
                                    <th>数值</th>
                                    <th>说明</th>
                                    <th style="width: 60px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="row, idx in detail_list" :style="{backgroundColor:idx == select_idx ? '#e2e2e2' : '#FFFFFF'}">
                                    <td>
                                        <a @click="selectOn(idx)">选中</a>
                                    </td>
                                    <td v-text="idx + 1"></td>
                                    <td>
                                        <a @click="selectDefault(idx)">
                                            <i v-if="row.default_flag == 0" class="fa fa-check-square" style="color: #D2D2D2;font-size: 20px;"></i>
                                            <i v-else class="fa fa-check-square" style="color: #00D500;font-size: 20px;"></i>
                                        </a>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control" placeholder="请输入编号" :name="'code_' + idx" v-model="row.code">
                                    </td>
                                    <td>
                                        <input type="text" class="form-control" placeholder="请输入名称" :name="'name_' + idx" v-model="row.name" maxlength="50">
                                    </td>
                                    <td>
                                        <input type="number" class="form-control" placeholder="请输入数值" :name="'num_' + idx" v-model="row.num_val">
                                    </td>
                                    <td>
                                        <input type="text" class="form-control"  placeholder="请输入说明" :name="'remarks_' + idx" v-model="row.remarks" maxlength="50">
                                    </td>
                                    <td>
                                        <button v-if="row.lock_flag == 0" type="button" class="btn red" @click="delDetail(idx)"><i class="fa fa-times"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i> 取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonDict }},
        methods: {
            selectOn:function (idx){
                if (idx == this.select_idx){
                    this.select_idx = -1;
                } else {
                    this.select_idx = idx;
                }
            },
            selectDefault:function (idx){
               let default_flag =  this.detail_list[idx].default_flag;
                if (default_flag == 1){
                    this.detail_list[idx].default_flag = 0;
                } else {
                    for (let item of this.detail_list){
                        item.default_flag = 0;
                    }
                    this.detail_list[idx].default_flag = 1;
                }
            },
            fieldSort: function(sort) {
                if (this.select_idx == -1) {
                    alertWarning('请选择要调序的行');
                    return;
                }

                let idx = this.select_idx;
                if (sort == 1) {
                    //up
                    if (idx == 0) {
                        return;
                    }
                    let field = this.detail_list[idx];
                    this.detail_list.splice(idx, 1);
                    this.detail_list.splice(idx - 1, 0, field);
                    this.select_idx--;
                } else {
                    //down
                    if (idx == this.detail_list.length - 1) {
                        return;
                    }
                    let field = this.detail_list[idx];
                    this.detail_list.splice(idx, 1);
                    this.detail_list.splice(idx + 1, 0, field);
                    this.select_idx++;
                }
            },
            addDetail: function() {
                this.detail_list.push({
                    uid:'',
                    name: '',
                    code: '',
                    default_flag:0,
                    num_val:'',
                    remarks:'',
                    lock_flag: 0
                });
            },
            delDetail: function(idx) {
                this.select_idx = -1;
                this.detail_list.splice(idx, 1);
            },
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                var url = '{{ url('sys/dict/edit/' ~ dict_type) }}';

                let names = [];
                let codes = [];
                let code;
                let detail;
                for (let i = 0; i < this.detail_list.length; i++) {
                    detail = this.detail_list[i];
                    if (!detail.name) {
                        alertWarning('请输入项目名称');
                        return;
                    }

                    if (!detail.code) {
                        alertWarning('请输入项目编码');
                        return;
                    }

                    if (names.indexOf(detail.name) >= 0) {
                        alertWarning('项目名称不能重复');
                        return;
                    }
                    code = detail.code.toLowerCase();
                    if (codes.indexOf(code) >= 0) {
                        alertWarning('项目编码不能重复');
                        return;
                    }
                    names.push(detail.name);
                    codes.push(code);
                }
                showSpin();
                $.post(url, {detail_list:encodeURI(JSON.stringify(this.detail_list)).replace(/\+/g,'%2B')}, function (rs) {
                    closeSpin(null);
                    if (rs.status == 'ok') {
                        top.window.layer_result = 'ok';
                        top.layer.close(top.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败！' + rs.message);
                    }
                });
            }
        }
    });

</script>
<style>
    .input-icon i:hover {
        cursor: pointer;
        transition: all 0.3s;
        color: #3598dc;
    }
</style>