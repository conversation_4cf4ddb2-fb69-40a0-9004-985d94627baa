<?php

namespace Envsan\Modules\Purchase\Model;


use Envsan\Common\Model\BaseModel;
use Envsan\Common\Util\ModelUtil;

class PurchaseApply extends BaseModel
{

    /**
     *
     * @var integer
     * @Primary
     * @Identity
     * @Column(type="integer", nullable=false)
     */
    public $id;

    /**
     *
     * @var string
     * @Column(type="string", length=32, nullable=false)
     */
    public $uid;

    /**
     *
     * @var integer
     * @Column(type="integer", length=11, nullable=true)
     */
    public $request_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $product_bom_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $order_detail_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $goods_id;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_uid;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_code;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $goods_name;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $goods_spec;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $goods_model;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_unit;

    /**
     *
     * @var string
     * @Column(type="string", length=50, nullable=true)
     */
    public $goods_deputy_unit;

    /**
     *
     * @var string
     * @Column(type="double", length=11, nullable=true)
     */
    public $quantity;

    /**
     *
     * @var string
     * @Column(type="double", length=11, nullable=true)
     */
    public $purchase_quantity;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $purchase_order_id;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $status;

    /**
     *
     * @var string
     * @Column(type="string", length=20, nullable=true)
     */
    public $status_name;

    /**
     *
     * @var string
     * @Column(type="string", length=100, nullable=true)
     */
    public $supplier_name;

    /**
     *
     * @var string
     * @Column(type="string", length=200, nullable=true)
     */
    public $remarks;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     */
    public $update_date;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $update_by;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $del_flag;

    /**
     *
     * @var integer
     * @Column(type="integer", nullable=true)
     */
    public $owner;

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'purchase_apply';
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseApply[]
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters
     * @return PurchaseApply
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }

    public function initialize()
    {
        ModelUtil::softDelete($this);
    }
}
