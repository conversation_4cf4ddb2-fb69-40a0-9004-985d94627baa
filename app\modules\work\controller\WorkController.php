<?php
namespace Envsan\Modules\Work\Controller;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\JsonData;
use Envsan\Modules\Common\Service\FileService;
use Envsan\Modules\Work\Service\DataService;
use Envsan\Modules\Work\Service\ReviewService;
use Envsan\Modules\Work\Service\WorkService;

/**
 * @skipacl
 */
class WorkController extends SuperController
{
    public function listAction($type = '', $data_type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $as = new ReviewService();
            $builder = $as->selectAll();
            $page = $this->getPagination($builder);
            $page->rows = $as->setDetail($page->rows);
            $page->cnt_data = $as->getCntData();
            return json_encode($page);
        }
        $this->view->type = $data_type;
    }

    public function searchAction($type = '')
    {
        if ($type == 'json') {
            $this->setJsonResponse();
            $as = new ReviewService();
            $builder = $as->searchAll();
            $page = $this->getPagination($builder);
            $page->rows = $as->setDetail($page->rows);
            return json_encode($page);
        }
    }

    public function viewAction($uid, $type)
    {
        $s = new DataService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);
        $w = new WorkService();
        $rtn = $w->getFlowData($row,$type);
        $oss = new FileService();
        $base_path = $oss->getImagePath();
        $this->view->base_path = $base_path;
        $this->view->jsonData = json_encode($rtn->data);
        $this->view->type = $type;
    }

    public function refreshAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $s = new DataService();
            return json_encode($s->getViewData());
        }
    }

    public function commentAction($uid)
    {
        $s = new DataService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->commentSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $oss = new FileService();
        $ws = new WorkService();

        $this->view->uid = $uid;
        $this->view->base_path = $oss->getImagePath();
        $this->view->user_list = $ws->getUserList();
    }

    public function uploadimgAction($folder_name)
    {
        $this->setJsonResponse();
        $s = new DataService();
        return json_encode($s->uploadImage($folder_name));
    }

    public function uploadpdfAction()
    {
        $this->setJsonResponse();
        $s = new FileService();
        return json_encode($s->uploadImage());
    }

    public function readAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->readSave();
            if (empty($ret->message)){
                $ret->uid = $rs->checkNextReadData();
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function knowAction()
    {
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->knowSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function rejectAction($uid)
    {
        $s = new DataService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);

        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->reject();
            if (empty($ret->message)){
                $ret->uid = $rs->checkNextReviewData();
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }

        $oss = new FileService();

        $this->view->uid = $uid;
        $this->view->base_path = $oss->getImagePath();
    }

    public function passAction($uid)
    {
        $s = new DataService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->pass();
            if (empty($ret->message)){
                $ret->uid = $rs->checkNextReviewData();
            }
            $ret->emptyIsOk();
            return json_encode($ret);
        }
        $anchor_data = json_decode($row->anchor_data,true);
        $this->view->pass_type = $anchor_data['type'];
        $oss = new FileService();
        $this->view->uid = $uid;
        $this->view->base_path = $oss->getImagePath();
    }

    public function cancelAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->cancelSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function pressingAction(){
        if ($this->request->isPost()) {
            $this->setJsonResponse();
            $rs = new WorkService();
            $ret = new JsonData();
            $ret->message = $rs->pressingSave();
            $ret->emptyIsOk();
            return json_encode($ret);
        }
    }

    public function printAction($uid){
        $s = new DataService();
        $row = $s->selectByUid($uid);
        if (empty($row))
            die(ErrorHelper::WRONG_ID);
        $w = new ReviewService();
        $rtn = $w->getPrintData($row);
        $this->view->jsonData = json_encode($rtn->data);
    }
}