<?php
namespace Envsan\Modules\Sys\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Component\Logger;
use Envsan\Common\Data\SessionData;
use Envsan\Common\Util\CheckUtil;
use Envsan\Common\Util\CvtUtil;
use Envsan\Common\Util\DateUtil;
use Envsan\Common\Util\UUID;
use Envsan\Modules\Sys\Model\Dict;
use Envsan\Modules\Sys\Model\SysDict;
use Envsan\Modules\Sys\Model\SysDictDetail;
use Phalcon\Mvc\User\Component;

class DictService extends Component
{
    public function selectDetailAll($dict_type){
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                uid,
                code,
                name,
                num_val,
                sort,
                remarks,
                default_flag,
                lock_flag
            ')
            ->addFrom('Envsan\Modules\Sys\Model\SysDict')
            ->where('del_flag = 0 and dict_type = ?1 and owner = ?2', [1 => $dict_type , 2 => SessionData::ownerId()])
            ->orderBy('sort');
        return $builder->getQuery()->execute();
    }

    public function update($dict_type)
    {
        $detail_list = str_replace('%2B','+',urldecode($this->request->getPost('detail_list', ['string', 'trim'])));
        if (empty($detail_list)) {
            return ErrorHelper::WRONG_INPUT;
        }

        $detail_list = CvtUtil::emptyToArray($detail_list);
        if(count($detail_list) == 0){
            return ErrorHelper::WRONG_INPUT;
        }
        $dict_rows = SysDict::find(['del_flag = 0 and dict_type = ?1 and owner = ?2','bind'=>[1=>$dict_type,2=>SessionData::ownerId()]]);
        $this->db->begin();
        try {
            $user = SessionData::user();
            $now = DateUtil::now();
            $sort = 1;
            $dict_obj = [];
            foreach ($dict_rows as $dict_row){
                $dict_obj['_'.$dict_row->uid] = $dict_row;
            }
            foreach ($detail_list as $item){
                if (!empty($item['uid']) && array_key_exists('_'.$item['uid'],$dict_obj)){
                    $row = $dict_obj['_'.$item['uid']];
                    $row->code = $item['code'];
                    $row->name = $item['name'];
                    $row->sort = $sort;
                    $row->default_flag = $item['default_flag'];
                    $row->num_val = CvtUtil::blankToNull($item['num_val']);
                    $row->remarks = CvtUtil::blankToNull($item['remarks']);
                    $row->update_date = $now;
                    $row->update_by = $user->id;
                    if (!$row->save()){
                        throw new \Exception(ErrorHelper::UNKOWN);
                    }
                    unset($dict_obj['_'.$item['uid']]);
                } else {
                    $row = new SysDict();
                    $row->uid = UUID::make();
                    $row->dict_type = $dict_type;
                    $row->code = $item['code'];
                    $row->name = $item['name'];
                    $row->sort = $sort;
                    $row->default_flag = $item['default_flag'];
                    $row->num_val = CvtUtil::blankToNull($item['num_val']);
                    $row->remarks = CvtUtil::blankToNull($item['remarks']);
                    $row->update_date = $now;
                    $row->update_by = $user->id;
                    $row->del_flag = 0;
                    $row->owner = $user->owner;
                    if (!$row->save()){
                        throw new \Exception(ErrorHelper::UNKOWN);
                    }
                }
                $sort++;
            }
            foreach ($dict_obj as $key => $row){
                $row->update_date = $now;
                $row->update_by = $user->id;
                $row->del_flag = 1;
                if (!$row->save()){
                    throw new \Exception(ErrorHelper::UNKOWN);
                }
            }
            $this->db->commit();
        } catch (\Exception $e) {
            $this->db->rollback();
            Logger::error($e->getMessage());
            return ErrorHelper::UNKOWN;
        }
        return '';
    }
}