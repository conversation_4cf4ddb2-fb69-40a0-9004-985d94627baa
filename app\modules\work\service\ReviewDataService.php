<?php

namespace Envsan\Modules\Work\Service;

use Envsan\Common\Component\ErrorHelper;
use Envsan\Common\Util\CvtUtil;
use Envsan\Modules\Trade\Service\OrderService;
use Phalcon\Mvc\User\Component;

class ReviewDataService extends Component
{
    public function orderData($id){
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.code,
                t1.sign_date,
                t1.order_type_name,
                t1.remarks,
                t2.name as customer_name,
                t1.ext_data,
                t1.files
            ')
            ->addFrom('Envsan\Modules\Trade\Model\TradeOrder', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t1.customer_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.id = ?1', [1 => $id]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        $ps = new OrderService();
        $row['details'] = $ps->selectDetailsById($row['id']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function productData($id){
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.uid,
                t1.code,
                t1.name,
                t1.remarks,
                t2.name as customer_name,
                t1.ext_data
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesProduct', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer','t1.customer_id = t2.id','t2')
            ->where('t1.del_flag = 0 and t1.id = ?1', [1 => $id]);
        $rows = $builder->getQuery()->execute();
        if (count($rows) == 0){
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0]->toArray();
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function drawingData($id)
    {
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                a.code as version_code,
                a.drawing_url,
                a.drawing_name,
                a.remarks as version_remarks,
                d.code as drawing_code,
                d.skill_type_name,
                d.version_code as old_version_code,
                d.drawing_url as old_drawing_url,
                d.drawing_name as old_drawing_name,
                d.remarks as drawing_remarks,
                p.code as product_code,
                p.name as product_name
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesDrawingVersion', 'a')
            ->leftJoin('Envsan\Modules\Mes\Model\MesDrawing', 'a.drawing_id = d.id', 'd')
            ->leftJoin('Envsan\Modules\Mes\Model\MesProduct', 'd.product_id = p.id', 'p')
            ->where('a.del_flag = 0 and a.id = ?1', [1 => $id]);
        $rows = $builder->getQuery()->execute()->toArray();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }

        $row = $rows[0];
        $row['drawing_files'] = [];
        if (!empty($row['drawing_url'])) {
            $row['drawing_files'][] = [
                'name' => $row['drawing_name'],
                'url' => $row['drawing_url']
            ];
        }
        $row['old_drawing_files'] = [];
        if (!empty($row['old_drawing_url'])) {
            $row['old_drawing_files'][] = [
                'name' => $row['old_drawing_name'],
                'url' => $row['old_drawing_url']
            ];
        }

        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function noticeData($id){
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.code,
                t1.plan_begin_date,
                t1.plan_end_date,
                t1.remarks,
                t2.name as customer_name,
                t1.ext_data,
                t1.detail_data,
                t1.files
            ')
            ->addFrom('Envsan\Modules\Mes\Model\MesNotice', 't1')
            ->leftJoin('Envsan\Modules\Trade\Model\TradeCustomer', 't1.customer_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.id = ?1', [1 => $id]);
        $rows = $builder->getQuery()->execute()->toArray();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }
        $row = $rows[0];
        $details = CvtUtil::emptyToArray($row['detail_data']);
        foreach ($details as &$detail){
            $detail['ww_ship'] = '';
            foreach ($detail['list'] as $item){
                if ($item['sel'] == 1){
                    $detail['ww_ship'] .= $item['name']  . ',';
                }
            }
            $detail['ww_ship'] = rtrim($detail['ww_ship'],',');
        }
        $row['details'] = $details;
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function purchaseData($id)
    {
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.order_code,
                t1.order_date,
                t1.remarks,
                t1.total_quantity,
                t1.total_money,
                t1.total_money_hs,
                t1.detail_data,
                t2.name as supplier_name,
                t1.ext_data,
                t1.files
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.id = ?1', [1 => $id]);
        $rows = $builder->getQuery()->execute()->toArray();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }

        $row = $rows[0];
        $row['details'] = CvtUtil::emptyToArray($row['detail_data']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

    public function purchaseWwData($id)
    {
        $rtn = new \stdClass();
        $builder = $this->modelsManager->createBuilder()
            ->columns('
                t1.id,
                t1.order_code,
                t1.order_date,
                t1.remarks,
                t1.total_money,
                t2.name as supplier_name,
                t1.detail_data,
                t1.ext_data,
                t1.files
            ')
            ->addFrom('Envsan\Modules\Purchase\Model\PurchaseOrder', 't1')
            ->leftJoin('Envsan\Modules\Purchase\Model\PurchaseSupplier', 't1.supplier_id = t2.id', 't2')
            ->where('t1.del_flag = 0 and t1.id = ?1', [1 => $id]);
        $rows = $builder->getQuery()->execute()->toArray();
        if (count($rows) == 0) {
            $rtn->message = ErrorHelper::WRONG_INPUT;
            return $rtn;
        }

        $row = $rows[0];
        $row['details'] = CvtUtil::emptyToArray($row['detail_data']);
        $rtn->message = '';
        $rtn->data = $row;
        return $rtn;
    }

}