<!--jsTree -->
{% do assets.collection('js').addJs('static/global/plugins/jsTree/jstree.min.js') %}
{% do assets.collection('css').addCss('static/global/plugins/jsTree/style.min.css') %}
{{ assets.outputJs('validate') }}

<!-- Main content -->
<div id="app" class="page-content">
    <div class="portlet light">
        <div class="portlet-body form">
            <form id="form" class="form-horizontal form-validate" autocomplete="off">
                <div class="form-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">请选择上级部门
                                </label>
                                <div class="col-sm-8">
                                    <div id="tree"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    已选择的上级部门<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="pname" v-model="pname" required readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    名称<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" v-model="name" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    缩写<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="short_name" v-model="short_name" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    编号<span class="required"> * </span>
                                </label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="code" v-model="code" >
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">
                                    类型
                                </label>
                                <div class="col-sm-8">
                                    <select class="form-control bs-select" name="type" v-model="type">
                                        <option value="">请选择</option>
                                        {% for type in types %}
                                            <option value="{{ type }}">{{ type }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="button" class="btn green" @click="submit"><i class="fa fa-check"></i> 提交</button>
                    <button type="button" class="btn default" onclick="closePop()"><i class="fa fa-times"></i>取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {{ jsonGroup }},
        methods: {
            submit: function (e) {
                e.preventDefault();
                if( !$('#form').validate().form() )
                    return;

                {% if group is not empty %}
                    var url= '{{ url('sys/group/edit/'~group.uid) }}';
                {% else %}
                    var url= '{{ url('sys/group/create') }}';
                {% endif %}
                showSpin();
                $.post(url, app.$data, function (rs) {
                    closeSpin(null);
                    if(rs.status=='ok'){
                        parent.window.layer_result = 'ok';
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                    else {
                        toastr.error('操作失败!'+rs.message);
                    }
                })
            }
        }
    });

    var $tree = $('#tree');
    $(document).ready(function () {
        $tree.jstree({
            "core": {"data": {{ jsonTree }}}
        });

        $tree.on("select_node.jstree", function (e, data) {
            app.pname = data.node.text;
            app.pid = data.node.id;
        });
    });

    var bsSelectOption = {
        iconBase: 'fa',
        tickIcon: 'fa-check',
        noneSelectedText: '请选择'
    };
    $('.bs-select').selectpicker(bsSelectOption);

</script>
