<?php

namespace Envsan\Common\Util;

class HtmlUtil
{
    public static function getSearchHtml()
    {
        return '<div style="display: flex;align-items: center;justify-content: center;padding: 20px;">'
            .'<div><span id="c_title">@title@</span>：</div>'
            .'<div @condition_style@ style="margin: 0 15px;">'
            .'<select class="bs-select-s form-control" name="condition_type">'
            .'<option value="=" selected>等于</option>'
            .'<option value="<">小于</option>'
            .'<option value="<=">小于等于</option>'
            .'<option value=">">大于</option>'
            .'<option value=">=">大于等于</option>'
            .'<option value="like">包含</option>'
            .'</select>'
            .'</div>'
            .'<div @condition_style@>'
            .'<input type="text" id="c_input" class="form-control" name="condition_val" value="">'
            .'</div>'
            .'<div @date_style@>'
            .'<input type="text" id="c_input_date_begin" class="date dtpicker form-control" name="date_begin" value="" style="width: 150px;">'
            .'<span class="input-group-addon" style="width: 40px;line-height: 20px;height: 34px">至</span>'
            .'<input type="text" id="c_input_date_end" class="date dtpicker form-control" name="date_end" value="" style="width: 150px;">'
            .'</div>'
            .'<input type="hidden" id="hidden_input_type" value="@input_type@">'
            .'</div>'
            .'<div style="text-align: center;margin-top: 30px;">'
            .'<button type="button" class="btn green" onclick="addCondition()" style="margin-right: 20px;width: 120px;">确认</button>'
            .'<button type="button" class="btn default" onclick="closeCondition()" style="width: 120px;">取消</button>';
    }
}